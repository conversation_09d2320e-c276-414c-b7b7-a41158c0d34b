// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 存储服务实现 - MVVM-S架构
/// 基于SwiftData的数据持久化服务，提供特效设置的存储和管理
actor StorageService: StorageServiceProtocol {
    
    // MARK: - 属性
    private let modelContainer: ModelContainer
    private let modelContext: ModelContext
    
    // MARK: - 初始化
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        self.modelContext = ModelContext(modelContainer)
        print("🗄️ [StorageService] 初始化完成 - 使用SwiftData")
    }
    
    // MARK: - 协议实现
    
    func saveEffectsSettings(_ settings: EffectsModel) async throws {
        do {
            // 检查是否已存在设置
            let descriptor = FetchDescriptor<EffectsModel>(
                predicate: #Predicate { $0.id == "effect_settings" }
            )
            let existingSettings = try modelContext.fetch(descriptor)
            
            if let existing = existingSettings.first {
                // 更新现有设置
                existing.isTimeEnabled = settings.isTimeEnabled
                existing.selectedTimeStyle = settings.selectedTimeStyle
                existing.selectedTimeColor = settings.selectedTimeColor
                existing.selectedTimePosition = settings.selectedTimePosition
                existing.isGrainEnabled = settings.isGrainEnabled
                existing.grainIntensity = settings.grainIntensity
                existing.selectedGrainPreset = settings.selectedGrainPreset
                existing.isScratchEnabled = settings.isScratchEnabled
                existing.scratchIntensity = settings.scratchIntensity
                existing.selectedScratchPreset = settings.selectedScratchPreset
                existing.isLeakEnabled = settings.isLeakEnabled
                existing.leakIntensity = settings.leakIntensity
                existing.leakRandomValue = settings.leakRandomValue
                existing.selectedLeakPreset = settings.selectedLeakPreset
                existing.updateTimestamp()
                
                print("🗄️ [StorageService] 更新现有特效设置")
            } else {
                // 插入新设置
                modelContext.insert(settings)
                print("🗄️ [StorageService] 插入新特效设置")
            }
            
            try modelContext.save()
            print("✅ [StorageService] 特效设置保存成功")
            
        } catch {
            print("❌ [StorageService] 保存特效设置失败: \(error)")
            throw StorageError.saveFailed(error.localizedDescription)
        }
    }
    
    func loadEffectsSettings() async throws -> EffectsModel {
        do {
            let descriptor = FetchDescriptor<EffectsModel>(
                predicate: #Predicate { $0.id == "effect_settings" }
            )
            let existingSettings = try modelContext.fetch(descriptor)
            
            if let settings = existingSettings.first {
                print("✅ [StorageService] 加载现有特效设置")
                return settings
            } else {
                // 创建默认设置
                let defaultSettings = EffectsModel()
                modelContext.insert(defaultSettings)
                try modelContext.save()
                print("✅ [StorageService] 创建并返回默认特效设置")
                return defaultSettings
            }
            
        } catch {
            print("❌ [StorageService] 加载特效设置失败: \(error)")
            throw StorageError.loadFailed(error.localizedDescription)
        }
    }
    
    func deleteEffectsSettings() async throws {
        do {
            let descriptor = FetchDescriptor<EffectsModel>(
                predicate: #Predicate { $0.id == "effect_settings" }
            )
            let existingSettings = try modelContext.fetch(descriptor)
            
            for settings in existingSettings {
                modelContext.delete(settings)
            }
            
            try modelContext.save()
            print("✅ [StorageService] 删除特效设置成功")
            
        } catch {
            print("❌ [StorageService] 删除特效设置失败: \(error)")
            throw StorageError.deleteFailed(error.localizedDescription)
        }
    }
    
    func effectsSettingsExists() async -> Bool {
        do {
            let descriptor = FetchDescriptor<EffectsModel>(
                predicate: #Predicate { $0.id == "effect_settings" }
            )
            let existingSettings = try modelContext.fetch(descriptor)
            return !existingSettings.isEmpty
        } catch {
            print("❌ [StorageService] 检查设置存在性失败: \(error)")
            return false
        }
    }
    
    func getLastUpdateTime() async -> Date? {
        do {
            let descriptor = FetchDescriptor<EffectsModel>(
                predicate: #Predicate { $0.id == "effect_settings" }
            )
            let existingSettings = try modelContext.fetch(descriptor)
            return existingSettings.first?.updatedAt
        } catch {
            print("❌ [StorageService] 获取更新时间失败: \(error)")
            return nil
        }
    }
}

// MARK: - 存储错误类型
enum StorageError: LocalizedError, Equatable {
    case saveFailed(String)
    case loadFailed(String)
    case deleteFailed(String)
    case contextNotAvailable
    
    var errorDescription: String? {
        switch self {
        case .saveFailed(let reason):
            return "保存失败: \(reason)"
        case .loadFailed(let reason):
            return "加载失败: \(reason)"
        case .deleteFailed(let reason):
            return "删除失败: \(reason)"
        case .contextNotAvailable:
            return "数据上下文不可用"
        }
    }
}