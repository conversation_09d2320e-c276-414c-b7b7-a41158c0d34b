import Foundation

/// 用于统一管理 UserDefaults 操作的服务
class UserDefaultsService {
    // 单例模式
    static let shared = UserDefaultsService()
    
    // UserDefaults 实例，便于测试时可以注入 mock
    private let defaults: UserDefaults
    
    // 私有初始化方法
    private init(defaults: UserDefaults = .standard) {
        self.defaults = defaults
    }
    
    // MARK: - 常量键值
    
    // 刻度盘相关
    private enum DialKeys {
        static let lastZoomDialRotationAngle = "LastZoomDialRotationAngle"
        static let lastExposureDialRotationAngle = "LastExposureDialRotationAngle"
        static let lastTemperatureDialRotationAngle = "LastTemperatureDialRotationAngle"
        static let lastTintDialRotationAngle = "LastTintDialRotationAngle" 
        static let lastISODialRotationAngle = "LastISODialRotationAngle"
        static let lastShutterDialRotationAngle = "LastShutterDialRotationAngle"
        static let lastFocusDialRotationAngle = "LastFocusDialRotationAngle"
        static let isFromButtonSelection = "IsFromButtonSelection"
    }
    
    // 裁剪相关
    private enum CropKeys {
        static let lastCropScaleOffset = "LastCropScaleOffset"
    }
    
    // 订阅相关
    private enum SubscriptionKeys {
        static let isProUser = "isProUser"
    }
    
    // 设备配置相关
    private enum DeviceConfigKeys {
        static let supportsRAW = "supportsRAW"
        static let supportsProRAW = "supportsProRAW"
        static let supportsNightMode = "supportsNightMode"
        static let supportsPortraitMode = "supportsPortraitMode"
        static let supportsCinematicMode = "supportsCinematicMode"
        static let supportsProResVideo = "supportsProResVideo"
        static let deviceUIConfiguration = "deviceUIConfiguration"
        static let deviceConfiguration = "deviceConfiguration"
    }
    
    // MARK: - 刻度盘相关方法
    
    /// 保存刻度盘旋转角度
    /// - Parameters:
    ///   - rotationAngle: 旋转角度
    ///   - dialType: 刻度盘类型 (对应不同的存储键)
    func saveDialRotationAngle(_ rotationAngle: Double, forDialType dialType: String) {
        let key: String
        switch dialType {
        case "zoom": 
            key = DialKeys.lastZoomDialRotationAngle
        case "exposure": 
            key = DialKeys.lastExposureDialRotationAngle
        case "temperature": 
            key = DialKeys.lastTemperatureDialRotationAngle
        case "tint": 
            key = DialKeys.lastTintDialRotationAngle
        case "iso": 
            key = DialKeys.lastISODialRotationAngle
        case "shutter": 
            key = DialKeys.lastShutterDialRotationAngle
        case "focus": 
            key = DialKeys.lastFocusDialRotationAngle
        default:
            key = dialType // 直接使用传入的键
        }
        
        defaults.set(rotationAngle, forKey: key)
    }
    
    /// 获取保存的刻度盘旋转角度
    /// - Parameter storageKey: 存储键
    /// - Returns: 保存的旋转角度，默认为0.0
    func getDialRotationAngle(forKey storageKey: String) -> Double {
        return defaults.double(forKey: storageKey)
    }
    
    /// 设置按钮选择标志
    /// - Parameter isFromButton: 是否来自按钮选择
    func setIsFromButtonSelection(_ isFromButton: Bool) {
        defaults.set(isFromButton, forKey: DialKeys.isFromButtonSelection)
    }
    
    /// 获取按钮选择标志
    /// - Returns: 是否来自按钮选择
    func isFromButtonSelection() -> Bool {
        return defaults.bool(forKey: DialKeys.isFromButtonSelection)
    }
    
    /// 清除所有刻度盘角度设置
    func clearAllDialRotationAngles() {
        defaults.removeObject(forKey: DialKeys.lastZoomDialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastExposureDialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastTemperatureDialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastTintDialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastISODialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastShutterDialRotationAngle)
        defaults.removeObject(forKey: DialKeys.lastFocusDialRotationAngle)
    }
    
    // MARK: - 裁剪相关方法
    
    /// 保存裁剪刻度偏移量
    /// - Parameter offset: 偏移量
    func saveCropScaleOffset(_ offset: CGFloat) {
        defaults.set(offset, forKey: CropKeys.lastCropScaleOffset)
    }
    
    /// 获取保存的裁剪刻度偏移量
    /// - Returns: 保存的偏移量，默认为0.0
    func getCropScaleOffset() -> CGFloat {
        return defaults.object(forKey: CropKeys.lastCropScaleOffset) as? CGFloat ?? 0.0
    }
    
    // MARK: - 订阅相关方法
    
    /// 设置Pro用户状态
    /// - Parameter isPro: 是否为Pro用户
    func setProUserStatus(_ isPro: Bool) {
        defaults.set(isPro, forKey: SubscriptionKeys.isProUser)
    }
    
    /// 获取Pro用户状态
    /// - Returns: 是否为Pro用户
    func isProUser() -> Bool {
        return defaults.bool(forKey: SubscriptionKeys.isProUser)
    }
    
    // MARK: - 设备配置相关方法
    
    /// 保存设备功能支持状态
    /// - Parameters:
    ///   - supportsRAW: 是否支持RAW
    ///   - supportsProRAW: 是否支持ProRAW
    ///   - supportsNightMode: 是否支持夜间模式
    ///   - supportsPortraitMode: 是否支持人像模式
    ///   - supportsCinematicMode: 是否支持电影模式
    ///   - supportsProResVideo: 是否支持ProRes视频
    func saveDeviceFeatures(
        supportsRAW: Bool,
        supportsProRAW: Bool,
        supportsNightMode: Bool,
        supportsPortraitMode: Bool,
        supportsCinematicMode: Bool,
        supportsProResVideo: Bool
    ) {
        defaults.set(supportsRAW, forKey: DeviceConfigKeys.supportsRAW)
        defaults.set(supportsProRAW, forKey: DeviceConfigKeys.supportsProRAW)
        defaults.set(supportsNightMode, forKey: DeviceConfigKeys.supportsNightMode)
        defaults.set(supportsPortraitMode, forKey: DeviceConfigKeys.supportsPortraitMode)
        defaults.set(supportsCinematicMode, forKey: DeviceConfigKeys.supportsCinematicMode)
        defaults.set(supportsProResVideo, forKey: DeviceConfigKeys.supportsProResVideo)
    }
    
    /// 保存设备UI配置
    /// - Parameter config: UI配置字典
    func saveDeviceUIConfiguration(_ config: [String: Any]) {
        defaults.set(config, forKey: DeviceConfigKeys.deviceUIConfiguration)
    }
    
    /// 保存设备配置
    /// - Parameter config: 设备配置字典
    func saveDeviceConfiguration(_ config: [String: Any]) {
        defaults.set(config, forKey: DeviceConfigKeys.deviceConfiguration)
    }
    
    /// 获取设备配置
    /// - Returns: 设备配置字典
    func getDeviceConfiguration() -> [String: Any]? {
        return defaults.dictionary(forKey: DeviceConfigKeys.deviceConfiguration)
    }
    
    /// 获取设备UI配置
    /// - Returns: 设备UI配置字典
    func getDeviceUIConfiguration() -> [String: Any]? {
        return defaults.dictionary(forKey: DeviceConfigKeys.deviceUIConfiguration)
    }
    
    /// 清除设备配置
    func clearDeviceConfiguration() {
        defaults.removeObject(forKey: DeviceConfigKeys.deviceConfiguration)
        defaults.removeObject(forKey: DeviceConfigKeys.deviceUIConfiguration)
    }
    
    // MARK: - 通用方法
    
    /// 保存值
    /// - Parameters:
    ///   - value: 要保存的值
    ///   - key: 存储键
    func save<T>(_ value: T, forKey key: String) {
        defaults.set(value, forKey: key)
    }
    
    /// 获取字符串值
    /// - Parameter key: 存储键
    /// - Returns: 保存的字符串值，如果没有则返回nil
    func getString(forKey key: String) -> String? {
        return defaults.string(forKey: key)
    }
    
    /// 获取布尔值
    /// - Parameter key: 存储键
    /// - Returns: 保存的布尔值，如果没有则返回false
    func getBool(forKey key: String) -> Bool {
        return defaults.bool(forKey: key)
    }
    
    /// 获取整数值
    /// - Parameter key: 存储键
    /// - Returns: 保存的整数值，如果没有则返回0
    func getInt(forKey key: String) -> Int {
        return defaults.integer(forKey: key)
    }
    
    /// 获取浮点值
    /// - Parameter key: 存储键
    /// - Returns: 保存的浮点值，如果没有则返回0.0
    func getDouble(forKey key: String) -> Double {
        return defaults.double(forKey: key)
    }
    
    /// 获取对象
    /// - Parameter key: 存储键
    /// - Returns: 保存的对象，如果没有则返回nil
    func getObject(forKey key: String) -> Any? {
        return defaults.object(forKey: key)
    }
    
    /// 删除值
    /// - Parameter key: 要删除的存储键
    func removeValue(forKey key: String) {
        defaults.removeObject(forKey: key)
    }
} 