// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import CoreGraphics
import simd

/// 完整的HSL服务实现 - MVVM-S架构
/// 提供高精度的HSL颜色调整功能
actor HSLServiceImpl: HSLServiceProtocol {
    
    // MARK: - HSL状态管理
    
    /// HSL参数存储（8个颜色范围）
    private var hslParameters: HSLParametersStorage
    
    /// 当前选中的颜色范围索引
    private var currentColorRangeIndex: Int = 0
    
    /// HSL全局启用状态
    private var isHSLEnabled: Bool = true
    
    /// HSL全局强度
    private var globalIntensity: Float = 1.0
    
    /// 颜色范围柔和度
    private var colorRangeSoftness: Float = 0.5
    
    /// 颜色范围精度
    private var colorRangePrecision: Float = 1.0
    
    // MARK: - 颜色范围定义
    
    /// 8个标准颜色范围
    private let colorRanges: [HSLColorRange] = [
        HSLColorRange(name: "红色", hueCenter: 0, hueRange: 30),      // 0
        HSLColorRange(name: "橙色", hueCenter: 30, hueRange: 30),     // 1
        HSLColorRange(name: "黄色", hueCenter: 60, hueRange: 30),     // 2
        HSLColorRange(name: "绿色", hueCenter: 120, hueRange: 60),    // 3
        HSLColorRange(name: "青色", hueCenter: 180, hueRange: 30),    // 4
        HSLColorRange(name: "蓝色", hueCenter: 240, hueRange: 60),    // 5
        HSLColorRange(name: "紫色", hueCenter: 300, hueRange: 30),    // 6
        HSLColorRange(name: "洋红", hueCenter: 330, hueRange: 30)     // 7
    ]
    
    // MARK: - 性能优化
    
    /// HSL查找表缓存
    private var hslLUTCache: [String: [Float]] = [:]
    private var lutCacheValid: Bool = false
    
    /// 性能统计
    private var updateCount: Int = 0
    private var totalUpdateTime: TimeInterval = 0
    
    // MARK: - 初始化
    
    init() {
        // 初始化HSL参数存储
        self.hslParameters = HSLParametersStorage()
        
        print("🎨 [HSLServiceImpl] HSL服务初始化完成")
        print("   - 支持颜色范围: \(colorRanges.count)个")
        print("   - 默认选中: \(colorRanges[currentColorRangeIndex].name)")
    }
    
    // MARK: - HSL参数更新
    
    func updateHSLHue(_ hue: Float, for colorIndex: Int?) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let targetIndex = colorIndex ?? currentColorRangeIndex
        
        // 验证参数范围
        let clampedHue = max(-180.0, min(180.0, hue))
        
        // 更新HSL参数
        hslParameters.setHue(clampedHue, for: targetIndex)
        
        // 标记缓存无效
        invalidateLUTCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [HSLServiceImpl] 更新HSL色相")
        print("   - 颜色范围: \(colorRanges[targetIndex].name)")
        print("   - 色相值: \(clampedHue)°")
        print("   - 处理时间: \(String(format: "%.2f", duration * 1000))ms")
    }
    
    func updateHSLSaturation(_ saturation: Float, for colorIndex: Int?) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let targetIndex = colorIndex ?? currentColorRangeIndex
        
        // 验证参数范围
        let clampedSaturation = max(-100.0, min(100.0, saturation))
        
        // 更新HSL参数
        hslParameters.setSaturation(clampedSaturation, for: targetIndex)
        
        // 标记缓存无效
        invalidateLUTCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [HSLServiceImpl] 更新HSL饱和度")
        print("   - 颜色范围: \(colorRanges[targetIndex].name)")
        print("   - 饱和度值: \(clampedSaturation)%")
    }
    
    func updateHSLLuminance(_ luminance: Float, for colorIndex: Int?) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let targetIndex = colorIndex ?? currentColorRangeIndex
        
        // 验证参数范围
        let clampedLuminance = max(-100.0, min(100.0, luminance))
        
        // 更新HSL参数
        hslParameters.setLuminance(clampedLuminance, for: targetIndex)
        
        // 标记缓存无效
        invalidateLUTCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [HSLServiceImpl] 更新HSL明度")
        print("   - 颜色范围: \(colorRanges[targetIndex].name)")
        print("   - 明度值: \(clampedLuminance)%")
    }
    
    // MARK: - HSL颜色范围管理
    
    func switchHSLColorRange(to index: Int) async throws {
        guard index >= 0 && index < colorRanges.count else {
            throw HSLError.invalidColorIndex("无效的颜色索引: \(index)")
        }
        
        currentColorRangeIndex = index
        
        print("🎨 [HSLServiceImpl] 切换颜色范围")
        print("   - 新选中: \(colorRanges[index].name)")
        print("   - 索引: \(index)")
    }
    
    func getCurrentColorRangeIndex() async -> Int {
        return currentColorRangeIndex
    }
    
    func getColorRangeName(for index: Int) async -> String {
        guard index >= 0 && index < colorRanges.count else {
            return "未知颜色"
        }
        return colorRanges[index].name
    }
    
    // MARK: - HSL参数查询
    
    func getCurrentHSLParameters() async -> (hue: Float, saturation: Float, luminance: Float) {
        let params = hslParameters.getParameters(for: currentColorRangeIndex)
        return (params.hue, params.saturation, params.luminance)
    }
    
    func getAllHSLParameters() async -> (hueValues: [Float], saturationValues: [Float], luminanceValues: [Float]) {
        var hueValues: [Float] = []
        var saturationValues: [Float] = []
        var luminanceValues: [Float] = []
        
        for i in 0..<colorRanges.count {
            let params = hslParameters.getParameters(for: i)
            hueValues.append(params.hue)
            saturationValues.append(params.saturation)
            luminanceValues.append(params.luminance)
        }
        
        return (hueValues, saturationValues, luminanceValues)
    }
    
    func getHSLParameters(for colorIndex: Int) async -> (hue: Float, saturation: Float, luminance: Float) {
        guard colorIndex >= 0 && colorIndex < colorRanges.count else {
            return (0, 0, 0)
        }
        
        let params = hslParameters.getParameters(for: colorIndex)
        return (params.hue, params.saturation, params.luminance)
    }
    
    // MARK: - HSL重置操作
    
    func resetCurrentHSLColor() async throws {
        hslParameters.resetParameters(for: currentColorRangeIndex)
        invalidateLUTCache()
        
        print("🎨 [HSLServiceImpl] 重置当前颜色: \(colorRanges[currentColorRangeIndex].name)")
    }
    
    func resetAllHSLColors() async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        for i in 0..<colorRanges.count {
            hslParameters.resetParameters(for: i)
        }
        
        invalidateLUTCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [HSLServiceImpl] 重置所有HSL颜色完成")
    }
    
    func resetHSLColor(at colorIndex: Int) async throws {
        guard colorIndex >= 0 && colorIndex < colorRanges.count else {
            throw HSLError.invalidColorIndex("无效的颜色索引: \(colorIndex)")
        }
        
        hslParameters.resetParameters(for: colorIndex)
        invalidateLUTCache()
        
        print("🎨 [HSLServiceImpl] 重置颜色: \(colorRanges[colorIndex].name)")
    }
    
    // MARK: - HSL状态管理
    
    func hasActiveHSLAdjustments() async -> Bool {
        guard isHSLEnabled else { return false }
        
        for i in 0..<colorRanges.count {
            if hslParameters.hasAdjustments(for: i) {
                return true
            }
        }
        
        return false
    }
    
    func hasAdjustments(for colorIndex: Int) async -> Bool {
        guard colorIndex >= 0 && colorIndex < colorRanges.count else {
            return false
        }
        
        return hslParameters.hasAdjustments(for: colorIndex)
    }
    
    func isHSLEnabled() async -> Bool {
        return isHSLEnabled
    }
    
    func setHSLEnabled(_ enabled: Bool) async throws {
        isHSLEnabled = enabled
        
        if !enabled {
            invalidateLUTCache()
        }
        
        print("🎨 [HSLServiceImpl] 设置HSL启用状态: \(enabled)")
    }
    
    // MARK: - HSL高级设置
    
    func updateHSLGlobalIntensity(_ intensity: Float) async throws {
        globalIntensity = max(0.0, min(1.0, intensity))
        invalidateLUTCache()
        
        print("🎨 [HSLServiceImpl] 更新全局强度: \(globalIntensity)")
    }
    
    func getHSLGlobalIntensity() async -> Float {
        return globalIntensity
    }
    
    func updateHSLColorRangeSoftness(_ softness: Float) async throws {
        colorRangeSoftness = max(0.0, min(1.0, softness))
        invalidateLUTCache()
        
        print("🎨 [HSLServiceImpl] 更新颜色范围柔和度: \(colorRangeSoftness)")
    }
    
    func getHSLColorRangeSoftness() async -> Float {
        return colorRangeSoftness
    }
    
    func updateHSLColorRangePrecision(_ precision: Float) async throws {
        colorRangePrecision = max(0.5, min(2.0, precision))
        invalidateLUTCache()
        
        print("🎨 [HSLServiceImpl] 更新颜色范围精度: \(colorRangePrecision)")
    }
    
    func getHSLColorRangePrecision() async -> Float {
        return colorRangePrecision
    }
    
    // MARK: - HSL调试和诊断
    
    func printHSLCurrentState() async {
        print("🎨 [HSLServiceImpl] HSL当前状态:")
        print("   - 启用状态: \(isHSLEnabled)")
        print("   - 全局强度: \(globalIntensity)")
        print("   - 当前选中: \(colorRanges[currentColorRangeIndex].name) (索引: \(currentColorRangeIndex))")
        print("   - 颜色范围柔和度: \(colorRangeSoftness)")
        print("   - 颜色范围精度: \(colorRangePrecision)")
        
        print("   - 各颜色范围参数:")
        for (index, colorRange) in colorRanges.enumerated() {
            let params = hslParameters.getParameters(for: index)
            let hasAdj = hslParameters.hasAdjustments(for: index)
            print("     \(colorRange.name): H=\(params.hue)° S=\(params.saturation)% L=\(params.luminance)% \(hasAdj ? "✓" : "○")")
        }
        
        let (avgTime, count, _) = await getPerformanceStats()
        print("   - 性能统计: 平均\(String(format: "%.2f", avgTime * 1000))ms, 更新\(count)次")
    }
    
    func validateHSLParameters() async -> Bool {
        // 验证所有参数是否在有效范围内
        for i in 0..<colorRanges.count {
            let params = hslParameters.getParameters(for: i)
            
            if params.hue < -180.0 || params.hue > 180.0 {
                print("❌ [HSLServiceImpl] 色相参数超出范围: \(params.hue)")
                return false
            }
            
            if params.saturation < -100.0 || params.saturation > 100.0 {
                print("❌ [HSLServiceImpl] 饱和度参数超出范围: \(params.saturation)")
                return false
            }
            
            if params.luminance < -100.0 || params.luminance > 100.0 {
                print("❌ [HSLServiceImpl] 明度参数超出范围: \(params.luminance)")
                return false
            }
        }
        
        // 验证全局参数
        if globalIntensity < 0.0 || globalIntensity > 1.0 {
            print("❌ [HSLServiceImpl] 全局强度超出范围: \(globalIntensity)")
            return false
        }
        
        if colorRangeSoftness < 0.0 || colorRangeSoftness > 1.0 {
            print("❌ [HSLServiceImpl] 颜色范围柔和度超出范围: \(colorRangeSoftness)")
            return false
        }
        
        if colorRangePrecision < 0.5 || colorRangePrecision > 2.0 {
            print("❌ [HSLServiceImpl] 颜色范围精度超出范围: \(colorRangePrecision)")
            return false
        }
        
        print("✅ [HSLServiceImpl] HSL参数验证通过")
        return true
    }
    
    // MARK: - 私有方法 - HSL计算
    
    /// 计算颜色在指定范围内的权重
    private func calculateColorWeight(hue: Float, for colorRange: HSLColorRange) -> Float {
        let hueDiff = abs(hue - Float(colorRange.hueCenter))
        let normalizedDiff = min(hueDiff, 360.0 - hueDiff) // 处理色相环形特性
        
        let rangeRadius = Float(colorRange.hueRange) * colorRangePrecision
        
        if normalizedDiff <= rangeRadius {
            // 在范围内，计算权重
            let weight = 1.0 - (normalizedDiff / rangeRadius)
            
            // 应用柔和度
            return pow(weight, 1.0 / colorRangeSoftness)
        } else {
            return 0.0
        }
    }
    
    /// 应用HSL调整到指定颜色
    private func applyHSLAdjustment(hue: Float, saturation: Float, luminance: Float, colorIndex: Int) -> (Float, Float, Float) {
        let params = hslParameters.getParameters(for: colorIndex)
        let colorRange = colorRanges[colorIndex]
        
        // 计算颜色权重
        let weight = calculateColorWeight(hue: hue, for: colorRange)
        
        if weight <= 0.0 {
            return (hue, saturation, luminance)
        }
        
        // 应用调整
        let adjustedHue = hue + params.hue * weight * globalIntensity
        let adjustedSaturation = saturation * (1.0 + params.saturation / 100.0 * weight * globalIntensity)
        let adjustedLuminance = luminance * (1.0 + params.luminance / 100.0 * weight * globalIntensity)
        
        return (
            fmod(adjustedHue + 360.0, 360.0), // 确保色相在0-360范围内
            max(0.0, min(1.0, adjustedSaturation)), // 限制饱和度范围
            max(0.0, min(1.0, adjustedLuminance))   // 限制明度范围
        )
    }
    
    // MARK: - 缓存管理
    
    private func invalidateLUTCache() {
        lutCacheValid = false
        hslLUTCache.removeAll()
    }
    
    private func updatePerformanceStats(duration: TimeInterval) {
        updateCount += 1
        totalUpdateTime += duration
    }
    
    private func getPerformanceStats() async -> (averageTime: TimeInterval, updateCount: Int, lastDuration: TimeInterval) {
        let averageTime = updateCount > 0 ? totalUpdateTime / Double(updateCount) : 0
        return (averageTime, updateCount, 0) // lastDuration暂时设为0
    }
}

// MARK: - 支持结构

/// HSL参数存储
private class HSLParametersStorage {
    private var parameters: [HSLParameters]
    
    init() {
        // 初始化8个颜色范围的参数
        self.parameters = Array(repeating: HSLParameters(), count: 8)
    }
    
    func getParameters(for index: Int) -> HSLParameters {
        guard index >= 0 && index < parameters.count else {
            return HSLParameters()
        }
        return parameters[index]
    }
    
    func setHue(_ hue: Float, for index: Int) {
        guard index >= 0 && index < parameters.count else { return }
        parameters[index].hue = hue
    }
    
    func setSaturation(_ saturation: Float, for index: Int) {
        guard index >= 0 && index < parameters.count else { return }
        parameters[index].saturation = saturation
    }
    
    func setLuminance(_ luminance: Float, for index: Int) {
        guard index >= 0 && index < parameters.count else { return }
        parameters[index].luminance = luminance
    }
    
    func resetParameters(for index: Int) {
        guard index >= 0 && index < parameters.count else { return }
        parameters[index] = HSLParameters()
    }
    
    func hasAdjustments(for index: Int) -> Bool {
        guard index >= 0 && index < parameters.count else { return false }
        let params = parameters[index]
        return params.hue != 0.0 || params.saturation != 0.0 || params.luminance != 0.0
    }
}

/// HSL参数结构
private struct HSLParameters {
    var hue: Float = 0.0        // -180 到 +180 度
    var saturation: Float = 0.0 // -100 到 +100 百分比
    var luminance: Float = 0.0  // -100 到 +100 百分比
}

/// HSL颜色范围定义
private struct HSLColorRange {
    let name: String        // 颜色名称
    let hueCenter: Int      // 色相中心（0-360度）
    let hueRange: Int       // 色相范围（度数）
}

/// HSL错误类型
enum HSLError: LocalizedError {
    case invalidColorIndex(String)
    case parameterOutOfRange(String)
    case adjustmentFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidColorIndex(let message):
            return "无效的颜色索引: \(message)"
        case .parameterOutOfRange(let message):
            return "参数超出范围: \(message)"
        case .adjustmentFailed(let message):
            return "HSL调整失败: \(message)"
        }
    }
}