import Foundation
import AVFoundation
import Combine

/// 音频服务实现类，用于监控音频电平
class AudioService: NSObject, AudioServiceProtocol {
    
    // MARK: - Public Properties
    
    /// 左声道峰值电平
    let leftChannelPeakLevel = CurrentValueSubject<Float, Never>(0.0)
    
    /// 右声道峰值电平
    let rightChannelPeakLevel = CurrentValueSubject<Float, Never>(0.0)
    
    // MARK: - Private Properties
    
    private var audioConnection: AVCaptureConnection?
    private var audioOutput: AVCaptureAudioDataOutput?
    private var captureSession: AVCaptureSession?
    
    // MARK: - Initialization
    
    override init() {
        super.init()
    }
    
    // MARK: - Public Methods
    
    /// 开始监控音频电平
    /// - Parameter session: 捕获会话
    func startMonitoring(session: AVCaptureSession) {
        captureSession = session
        
        // 创建音频输出
        let audioOutput = AVCaptureAudioDataOutput()
        self.audioOutput = audioOutput
        
        // 设置采样缓冲代理和队列
        let audioQueue = DispatchQueue(label: "audio.queue")
        audioOutput.setSampleBufferDelegate(self, queue: audioQueue)
        
        // 将音频输出添加到会话
        if session.canAddOutput(audioOutput) {
            session.addOutput(audioOutput)
            audioConnection = audioOutput.connection(with: .audio)
        }
    }
    
    /// 停止监控音频电平
    func stopMonitoring() {
        // 移除音频输出
        if let audioOutput = audioOutput, let captureSession = captureSession {
            captureSession.removeOutput(audioOutput)
        }
        
        // 重置音频连接和输出
        audioConnection = nil
        audioOutput = nil
        captureSession = nil
        
        // 重置峰值电平
        leftChannelPeakLevel.send(0.0)
        rightChannelPeakLevel.send(0.0)
    }
}

// MARK: - AVCaptureAudioDataOutputSampleBufferDelegate
extension AudioService: AVCaptureAudioDataOutputSampleBufferDelegate {
    
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        // 确保这是音频样本
        guard connection == audioConnection else { return }
        
        // 获取音频缓冲列表
        guard let audioBufferList = self.audioBufferListFromSampleBuffer(sampleBuffer) else { return }
        
        // 获取通道数量
        let channelsCount = Int(audioBufferList.pointee.mNumberBuffers)
        
        // 分析每个通道的峰值电平
        if channelsCount > 0 {
            // 左声道
            let leftChannelBuffer = audioBufferList.pointee.mBuffers
            let leftLevel = calculatePeakLevel(for: leftChannelBuffer)
            leftChannelPeakLevel.send(leftLevel)
            
            // 右声道（如果存在）
            if channelsCount > 1 {
                let rightChannelBuffer = UnsafeMutableAudioBufferListPointer(audioBufferList)[1]
                let rightLevel = calculatePeakLevel(for: rightChannelBuffer)
                rightChannelPeakLevel.send(rightLevel)
            } else {
                // 如果只有一个通道，左右声道使用相同的值
                rightChannelPeakLevel.send(leftLevel)
            }
        }
        
        // 释放音频缓冲区列表的内存
        free(audioBufferList)
    }
    
    // MARK: - Private Helper Methods
    
    /// 从样本缓冲区获取音频缓冲列表
    private func audioBufferListFromSampleBuffer(_ sampleBuffer: CMSampleBuffer) -> UnsafeMutablePointer<AudioBufferList>? {
        let size = MemoryLayout<AudioBufferList>.size
        
        // 分配内存给AudioBufferList
        guard let rawPointer = malloc(size) else {
            print("Failed to allocate memory for AudioBufferList")
            return nil
        }
        let bufferList = rawPointer.assumingMemoryBound(to: AudioBufferList.self)
        
        // 获取AudioBufferList
        let status = CMSampleBufferGetAudioBufferListWithRetainedBlockBuffer(
            sampleBuffer,
            bufferListSizeNeededOut: nil,
            bufferListOut: bufferList,
            bufferListSize: size,
            blockBufferAllocator: kCFAllocatorDefault,
            blockBufferMemoryAllocator: kCFAllocatorDefault,
            flags: 0,
            blockBufferOut: nil
        )
        
        guard status == noErr else {
            free(bufferList) // 如果失败，释放已分配的内存
            return nil
        }
        
        return bufferList
    }
    
    /// 计算音频缓冲区的峰值电平
    private func calculatePeakLevel(for buffer: AudioBuffer) -> Float {
        // 获取缓冲区数据
        let samples = UnsafeBufferPointer<Int16>(start: buffer.mData?.assumingMemoryBound(to: Int16.self), count: Int(buffer.mDataByteSize) / MemoryLayout<Int16>.size)
        
        // 寻找峰值样本
        var peakValue: Float = 0.0
        for sample in samples {
            let sampleFloat = abs(Float(sample) / Float(Int16.max))
            peakValue = max(peakValue, sampleFloat)
        }
        
        return peakValue
    }
} 
