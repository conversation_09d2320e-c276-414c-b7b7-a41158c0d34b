import Foundation
import Combine

/// 提供通知相关功能的服务实现，封装对NotificationCenter的直接依赖
class NotificationService: NotificationServiceProtocol {
    /// 单例实例
    static let shared = NotificationService()
    
    /// 通知中心实例
    private let notificationCenter: NotificationCenter
    
    /// 自定义初始化，允许传入不同的通知中心
    init(notificationCenter: NotificationCenter = .default) {
        self.notificationCenter = notificationCenter
    }
    
    /// 注册通知观察者
    func addObserver(_ observer: Any, selector: Selector, name: Notification.Name, object: Any?) {
        notificationCenter.addObserver(observer, selector: selector, name: name, object: object)
    }
    
    /// 通过回调块注册通知观察者
    @discardableResult
    func addObserver(forName name: Notification.Name, object: Any?, queue: OperationQueue?, using block: @escaping (Notification) -> Void) -> NSObjectProtocol {
        return notificationCenter.addObserver(forName: name, object: object, queue: queue, using: block)
    }
    
    /// 移除特定观察者
    func removeObserver(_ observer: Any) {
        notificationCenter.removeObserver(observer)
    }
    
    /// 移除特定观察者的特定通知
    func removeObserver(_ observer: Any, name: Notification.Name?, object: Any?) {
        notificationCenter.removeObserver(observer, name: name, object: object)
    }
    
    /// 发布通知
    func post(name: Notification.Name, object: Any?, userInfo: [AnyHashable: Any]? = nil) {
        notificationCenter.post(name: name, object: object, userInfo: userInfo)
    }
    
    /// 发布通知并等待处理完成（同步方式）
    @discardableResult
    func postAndWait(name: Notification.Name, object: Any?, userInfo: [AnyHashable: Any]? = nil, deliverImmediately: Bool = true, forCoalescing: Bool = false) -> Bool {
        // 使用同步方式发送通知，无法等待处理完成
        // 但作为替代，我们可以直接发送并返回true
        notificationCenter.post(name: name, object: object, userInfo: userInfo)
        return true
    }
    
    /// 获取通知发布者
    /// - Parameters:
    ///   - name: 通知名称
    ///   - object: 通知发送者，nil表示接收所有发送者的通知
    /// - Returns: 发布者对象，可用于SwiftUI的onReceive
    func publisher(for name: Notification.Name, object: AnyObject? = nil) -> NotificationCenter.Publisher {
        return notificationCenter.publisher(for: name, object: object)
    }
}

// MARK: - 通知名称扩展
extension Notification.Name {
    // 相机相关通知
    static let cameraConfigurationChanged = Notification.Name("cameraConfigurationChanged")
    static let cameraCaptureModeChanged = Notification.Name("cameraCaptureModeChanged")
    static let cameraOrientationChanged = Notification.Name("cameraOrientationChanged")
    static let cameraCaptureStarted = Notification.Name("cameraCaptureStarted")
    static let cameraCaptureFinished = Notification.Name("cameraCaptureFinished")
    static let cameraFocusPointChanged = Notification.Name("cameraFocusPointChanged")
    
    // 编辑相关通知
    static let editSettingsChanged = Notification.Name("editSettingsChanged")
    static let editImageSaved = Notification.Name("editImageSaved")
    static let editModeChanged = Notification.Name("editModeChanged")
    
    // 滤镜相关通知
    static let filterApplied = Notification.Name("filterApplied")
    static let filterParametersChanged = Notification.Name("filterParametersChanged")
    
    // 应用状态相关通知
    static let appWillEnterBackground = Notification.Name("appWillEnterBackground")
    static let appDidEnterForeground = Notification.Name("appDidEnterForeground")
    static let appMemoryWarning = Notification.Name("appMemoryWarning")
    
    // 用户界面相关通知
    static let userInterfaceOrientationChanged = Notification.Name("userInterfaceOrientationChanged")
    static let userInterfaceStyleChanged = Notification.Name("userInterfaceStyleChanged")
} 