import Foundation
import AVFoundation
import UIKit

class CameraLensManager {
    // 单例模式
    static let shared = CameraLensManager()
    private init() {}
    
    // 设备镜头配置
    private var deviceLensConfig: [String: [String]] = [
        // iPhone 15 系列
        "iPhone16,2": ["0.5", "1", "5"],  // iPhone 15 Pro Max
        "iPhone16,1": ["0.5", "1", "3"],  // iPhone 15 Pro
        "iPhone15,5": ["0.5", "1", "2"],  // iPhone 15 Plus
        "iPhone15,4": ["0.5", "1", "2"],  // iPhone 15
        
        // iPhone 14 系列
        "iPhone15,3": ["0.5", "1", "3"],  // iPhone 14 Pro Max
        "iPhone15,2": ["0.5", "1", "3"],  // iPhone 14 Pro
        "iPhone14,8": ["0.5", "1", "2"],  // iPhone 14 Plus
        "iPhone14,7": ["0.5", "1", "2"],  // iPhone 14
        
        // iPhone 13 系列
        "iPhone14,3": ["0.5", "1", "3"],  // iPhone 13 Pro Max
        "iPhone14,2": ["0.5", "1", "3"],  // iPhone 13 Pro
        "iPhone14,5": ["0.5", "1", "2"],  // iPhone 13
        "iPhone14,4": ["0.5", "1", "2"],  // iPhone 13 mini
        
        // iPhone 12 系列
        "iPhone13,4": ["0.5", "1", "2.5"], // iPhone 12 Pro Max
        "iPhone13,3": ["0.5", "1", "2"],   // iPhone 12 Pro
        "iPhone13,2": ["0.5", "1", "2"],   // iPhone 12
        "iPhone13,1": ["0.5", "1", "2"],   // iPhone 12 mini
        
        // iPhone 11 系列
        "iPhone12,5": ["0.5", "1", "2"],   // iPhone 11 Pro Max
        "iPhone12,3": ["0.5", "1", "2"],   // iPhone 11 Pro
        "iPhone12,1": ["0.5", "1", "2"],   // iPhone 11
        
        // iPhone SE/XR 系列
        "iPhone14,6": ["1", "5"],          // iPhone SE (3rd generation)
        "iPhone12,8": ["1", "5"],          // iPhone SE (2nd generation)
        "iPhone11,8": ["1", "5"],          // iPhone XR
        
        // iPhone XS 系列
        "iPhone11,6": ["1", "2"],          // iPhone XS Max Global
        "iPhone11,4": ["1", "2"],          // iPhone XS Max
        "iPhone11,2": ["1", "2"],          // iPhone XS
        
        // iPhone X 系列
        "iPhone10,6": ["1", "2"],          // iPhone X GSM
        "iPhone10,3": ["1", "2"],          // iPhone X Global
        
        // iPhone 8 系列
        "iPhone10,5": ["1", "2"],          // iPhone 8 Plus GSM
        "iPhone10,2": ["1", "2"],          // iPhone 8 Plus Global
        "iPhone10,4": ["1", "5"],          // iPhone 8 GSM
        "iPhone10,1": ["1", "5"],          // iPhone 8 Global
        
        // iPhone 7 系列
        "iPhone9,4": ["1", "2"],           // iPhone 7 Plus GSM
        "iPhone9,2": ["1", "2"],           // iPhone 7 Plus Global
        "iPhone9,3": ["1", "5"],           // iPhone 7 GSM
        "iPhone9,1": ["1", "5"]            // iPhone 7 Global
    ]
    
    // 默认镜头配置
    private let defaultLensConfig = ["0.5", "1", "3", "5"]  // 修改为支持 0.5x、1x、3x 和 5x
    
    // 获取设备标识符
    private var deviceIdentifier: String {
        var systemInfo = utsname()
        uname(&systemInfo)
        return Mirror(reflecting: systemInfo.machine).children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0 else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
    }
    
    // 获取设备支持的镜头列表
    func getSupportedLenses() -> [String] {
        // 返回与UI层一致的镜头列表
        return ["0.5", "1", "2"].sorted { Float($0) ?? 0 < Float($1) ?? 0 }
    }
    
    // 获取设备类型
    func getDeviceType() -> CameraType {
        let devices = AVCaptureDevice.DiscoverySession.cameraDiscoverySession.devices
        let lensCount = devices.filter { 
            $0.position == .back && 
            ($0.deviceType == .builtInWideAngleCamera || 
             $0.deviceType == .builtInTelephotoCamera || 
             $0.deviceType == .builtInUltraWideCamera)
        }.count
        
        switch lensCount {
        case 2: return .dual
        case 3: return .triple
        case 4: return .quad
        default: return .single
        }
    }
    
    // 切换到指定镜头
    func switchToLens(_ lens: String, for device: AVCaptureDevice) throws {
        guard let zoomFactor = Float(lens) else {
            throw CameraError.lensError
        }
        
        do {
            try device.lockForConfiguration()
            device.videoZoomFactor = CGFloat(zoomFactor)
            device.unlockForConfiguration()
        } catch {
            throw CameraError.lensError
        }
    }
    
    // 获取镜头配置
    func getLensConfiguration(for lens: String, isSelected: Bool) -> LensConfiguration {
        guard let device = AVCaptureDevice.camera(position: .back) else {
            return LensConfiguration(lens: lens, isSelected: isSelected)
        }
        
        let isPhysicalLens = isPhysicalLens(lens)
        let maxZoom = getMaxZoomFactor(for: lens, device: device)
        let minZoom = getMinZoomFactor(for: lens)
        
        return LensConfiguration(
            lens: lens,
            isSelected: isSelected,
            maxZoomFactor: maxZoom,
            minZoomFactor: minZoom,
            isZoomSupported: isPhysicalLens
        )
    }
    
    // 检查设备是否支持指定焦距
    func isLensSupported(_ lens: String) -> Bool {
        return getSupportedLenses().contains(lens)
    }
    
    // 获取设备的变焦范围
    func getZoomRange(for device: AVCaptureDevice) -> ClosedRange<CGFloat> {
        return device.minAvailableVideoZoomFactor...device.maxAvailableVideoZoomFactor
    }
    
    // 获取设备详细信息
    func getDeviceCapabilities(for device: AVCaptureDevice) -> [String: Any] {
        var capabilities: [String: Any] = [:]
        
        // 检查视频支持
        capabilities["supportsVideo"] = device.hasMediaType(.video)
        
        // 检查对焦支持
        capabilities["supportsFocus"] = device.isFocusPointOfInterestSupported
        capabilities["supportsContinuousFocus"] = device.isFocusModeSupported(.continuousAutoFocus)
        capabilities["supportsAutoFocus"] = device.isFocusModeSupported(.autoFocus)
        
        // 检查曝光支持
        capabilities["supportsExposure"] = device.isExposurePointOfInterestSupported
        capabilities["supportsContinuousExposure"] = device.isExposureModeSupported(.continuousAutoExposure)
        capabilities["supportsCustomExposure"] = device.isExposureModeSupported(.custom)
        
        // 获取视场角
        capabilities["fieldOfView"] = device.activeFormat.videoFieldOfView
        
        // 获取支持的捕获格式
        let formats = device.formats.map { format -> [String: Any] in
            let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
            let frameRates = format.videoSupportedFrameRateRanges.map { range -> [String: Float64] in
                return [
                    "minFrameRate": range.minFrameRate,
                    "maxFrameRate": range.maxFrameRate
                ]
            }
            
            return [
                "width": dimensions.width,
                "height": dimensions.height,
                "frameRates": frameRates,
                "fieldOfView": format.videoFieldOfView,
                "isVideoStabilizationSupported": format.isVideoStabilizationModeSupported(.auto)
            ]
        }
        capabilities["supportedFormats"] = formats
        
        // 获取其他重要信息
        capabilities["minFocusDistance"] = device.minimumFocusDistance
        capabilities["maxZoomFactor"] = device.maxAvailableVideoZoomFactor
        capabilities["minZoomFactor"] = device.minAvailableVideoZoomFactor
        capabilities["deviceType"] = device.deviceType.rawValue
        capabilities["position"] = device.position.rawValue
        capabilities["uniqueID"] = device.uniqueID
        
        return capabilities
    }
    
    // 获取所有后置摄像头的详细信息
    func getAllCameraCapabilities() -> [[String: Any]] {
        let devices = AVCaptureDevice.DiscoverySession.cameraDiscoverySession.devices
        return devices.filter { $0.position == .back }.map { getDeviceCapabilities(for: $0) }
    }
    
    // 获取指定设备的支持格式
    func getSupportedFormats(for device: AVCaptureDevice) -> [[String: Any]] {
        return device.formats.map { format -> [String: Any] in
            let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
            let frameRates = format.videoSupportedFrameRateRanges.map { range -> [String: Float64] in
                return [
                    "minFrameRate": range.minFrameRate,
                    "maxFrameRate": range.maxFrameRate
                ]
            }
            
            return [
                "width": dimensions.width,
                "height": dimensions.height,
                "frameRates": frameRates,
                "fieldOfView": format.videoFieldOfView,
                "isVideoStabilizationSupported": format.isVideoStabilizationModeSupported(.auto),
                "pixelFormat": CMFormatDescriptionGetMediaSubType(format.formatDescription)
            ]
        }
    }
    
    // 获取设备的对焦能力
    func getFocusCapabilities(for device: AVCaptureDevice) -> [String: Bool] {
        return [
            "pointOfInterestSupported": device.isFocusPointOfInterestSupported,
            "autoFocusSupported": device.isFocusModeSupported(.autoFocus),
            "continuousAutoFocusSupported": device.isFocusModeSupported(.continuousAutoFocus),
            "lockedFocusSupported": device.isFocusModeSupported(.locked)
        ]
    }
    
    // 获取设备的曝光能力
    func getExposureCapabilities(for device: AVCaptureDevice) -> [String: Bool] {
        return [
            "pointOfInterestSupported": device.isExposurePointOfInterestSupported,
            "autoExposureSupported": device.isExposureModeSupported(.autoExpose),
            "continuousAutoExposureSupported": device.isExposureModeSupported(.continuousAutoExposure),
            "lockedExposureSupported": device.isExposureModeSupported(.locked),
            "customExposureSupported": device.isExposureModeSupported(.custom)
        ]
    }
    
    // 获取设备的视场角信息
    func getFieldOfViewInfo(for device: AVCaptureDevice) -> [String: Float] {
        var fovInfo: [String: Float] = [:]
        
        // 当前激活格式的视场角
        fovInfo["activeFormatFOV"] = device.activeFormat.videoFieldOfView
        
        // 所有支持格式的视场角范围
        let fovValues = device.formats.map { $0.videoFieldOfView }
        fovInfo["minFOV"] = fovValues.min() ?? 0
        fovInfo["maxFOV"] = fovValues.max() ?? 0
        
        return fovInfo
    }
    
    // 获取焦距信息
    func getFocalLength(for deviceType: AVCaptureDevice.DeviceType, model: String) -> Float {
        switch deviceType {
        case .builtInUltraWideCamera:
            if model.contains("15 Pro") || model.contains("14 Pro") {
                return 13.0 // Pro 超广角
            } else if model.contains("13") || model.contains("12") || model.contains("11") {
                return 13.0 // 11-13系列超广角
            } else {
                return 13.0 // 其他机型超广角
            }
            
        case .builtInWideAngleCamera:
            if model.contains("15") || model.contains("14") || model.contains("13") || model.contains("12") || model.contains("11") {
                return 26.0 // 11-15系列主摄
            } else if model.contains("SE") || model.contains("XR") {
                return 28.0 // SE/XR系列主摄
            } else {
                return 28.0 // 早期机型主摄
            }
            
        case .builtInTelephotoCamera:
            switch model {
            case let m where m.contains("15 Pro Max"):
                return 120.0 // 15 Pro Max 5倍长焦
            case let m where m.contains("15 Pro"):
                return 77.0  // 15 Pro 3倍长焦
            case let m where m.contains("14 Pro"):
                return 77.0  // 14 Pro/Pro Max 3倍长焦
            case let m where m.contains("13 Pro"):
                return 77.0  // 13 Pro/Pro Max 3倍长焦
            case let m where m.contains("12 Pro Max"):
                return 65.0  // 12 Pro Max 2.5倍长焦
            case let m where m.contains("12 Pro"):
                return 52.0  // 12 Pro 2倍长焦
            case let m where m.contains("11 Pro"):
                return 52.0  // 11 Pro/Pro Max 2倍长焦
            case let m where m.contains("XS") || m.contains("X"):
                return 52.0  // X/XS系列 2倍长焦
            case let m where m.contains("8 Plus") || m.contains("7 Plus"):
                return 56.0  // 7 Plus/8 Plus 2倍长焦
            default:
                return 52.0  // 其他长焦
            }
            
        default:
            return 26.0 // 默认使用广角焦距
        }
    }
    
    // MARK: - Focal Length Methods
    
    /// 获取当前镜头的等效焦距（35mm）
    func getCurrentFocalLength(for device: AVCaptureDevice) -> Float {
        // 根据设备类型获取焦距
        let model = deviceIdentifier
        return getFocalLength(for: device.deviceType, model: model)
    }
    
    /// 获取指定变焦倍数对应的等效焦距
    func getFocalLengthForZoom(_ zoom: String, device: AVCaptureDevice) -> Float {
        // 获取基础焦距
        let baseFocalLength = getCurrentFocalLength(for: device)
        
        // 如果是数码变焦，根据变焦倍数计算
        if let zoomFactor = Float(zoom) {
            return baseFocalLength * zoomFactor
        }
        
        return baseFocalLength
    }
    
    // MARK: - Static Helpers

    /// Determines the camera system type based on the provided devices.
    static func determineSystemType(from devices: [AVCaptureDevice]) -> CameraSystemType {
        let backCameras = devices.filter { $0.position == .back }
        switch backCameras.count {
        case 2: return .dual
        case 3: return .triple
        case 4: return .quad
        default: return .single
        }
    }
    
    // MARK: - Private Helpers
    
    // 判断是否为物理镜头
    private func isPhysicalLens(_ lens: String) -> Bool {
        switch lens {
        case "0.5", "1", "2", "2.5", "3", "5":
            return true
        default:
            return false
        }
    }
    
    // 获取最大变焦倍数
    private func getMaxZoomFactor(for lens: String, device: AVCaptureDevice) -> Float {
        if isPhysicalLens(lens) {
            return Float(device.maxAvailableVideoZoomFactor)
        } else {
            return Float(device.activeFormat.videoMaxZoomFactor)
        }
    }
    
    // 获取最小变焦倍数
    private func getMinZoomFactor(for lens: String) -> Float {
        if isPhysicalLens(lens) {
            return Float(lens) ?? 1.0
        } else {
            return 1.0
        }
    }
} 