import Foundation
import AVFoundation
import UIKit

class DeviceCapabilityManager {
    // 单例模式
    static let shared = DeviceCapabilityManager()
    private init() {}
    
    // 检测设备特性
    func detectDeviceFeatures(model: String) -> DeviceFeatures {
        let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back)
        let formats = device?.formats ?? []
        
        // 获取最大视频分辨率
        let maxResolution = formats.reduce(CGSize.zero) { currentMax, format in
            let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
            let size = CGSize(width: CGFloat(dimensions.width), height: CGFloat(dimensions.height))
            return size.width * size.height > currentMax.width * currentMax.height ? size : currentMax
        }
        
        // 获取最大帧率
        let maxFPS = formats.reduce(0) { currentMax, format in
            let maxFrameRate = format.videoSupportedFrameRateRanges.map { $0.maxFrameRate }.max() ?? 0
            return max(currentMax, Int(maxFrameRate))
        }
        
        // 检查是否支持RAW
        let supportsRAW = formats.contains { format in
            CMFormatDescriptionGetMediaSubType(format.formatDescription) == kCVPixelFormatType_14Bayer_RGGB
        }
        
        // 检查HDR视频支持
        let supportsHDR = formats.contains { format in
            format.isVideoHDRSupported
        }
        
        // 获取HDR最大帧率
        let maxHDRFrameRate = formats.reduce(0) { currentMax, format in
            guard format.isVideoHDRSupported else { return currentMax }
            let maxFrameRate = format.videoSupportedFrameRateRanges.map { $0.maxFrameRate }.max() ?? 0
            return max(currentMax, Int(maxFrameRate))
        }
        
        // 检查慢动作支持
        let supportsSlowMotion = maxFPS >= 120
        
        // 检查光学防抖支持
        let supportsOIS = device?.isSubjectAreaChangeMonitoringEnabled ?? false
        
        // 基于设备型号判断特性支持
        let isProModel = model.contains("Pro")
        let is13OrLater = ["13", "14", "15"].contains { model.contains($0) }
        
        // 获取人像模式效果选项
        let portraitEffects = isProModel ? [
            "自然光",
            "摄影棚灯光",
            "轮廓光",
            "舞台灯光",
            "黑白舞台灯光",
            "高调单色光"
        ] : [
            "自然光",
            "摄影棚灯光",
            "轮廓光"
        ]
        
        return DeviceFeatures(
            supportsRAW: supportsRAW,
            supportsProRAW: isProModel && is13OrLater,
            supportsNightMode: false,
            supportsPortraitMode: CameraLensManager.shared.getDeviceType() != .single,
            supportsCinematicMode: false,
            supportsProResVideo: isProModel && is13OrLater,
            maxVideoResolution: maxResolution,
            maxSlowMotionFPS: maxFPS,
            
            // 新增特性
            supportsHDRVideo: supportsHDR,
            supportsSlowMotion: supportsSlowMotion,
            supportsTimeLapse: true,
            supportsOIS: supportsOIS,
            supportsDolbyVision: isProModel && is13OrLater,
            maxHDRFrameRate: maxHDRFrameRate,
            maxTimeLapseFrameRate: 30,
            nightModeMaxISO: isProModel ? 12800 : 6400,
            portraitEffectOptions: portraitEffects
        )
    }
    
    // 获取设备规格
    func getDeviceSpecification(model: String) -> DeviceSpecification {
        let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back)
        let formats = device?.formats ?? []
        
        // 创建主摄能力
        let mainCamera = CameraCapability(
            resolution: CGSize(width: 4032, height: 3024), // 默认12MP
            sensorSize: CGSize(width: 7.01, height: 5.79), // 默认传感器尺寸
            aperture: 1.8,
            focalLength: CameraLensManager.shared.getFocalLength(for: .builtInWideAngleCamera, model: model),
            pixelSize: 1.7,
            stabilization: [.standard, .cinematic],
            maxFrameRate: 60.0,
            supportedFormats: formats
        )
        
        // 创建超广角能力（如果支持）
        let ultraWide = CameraLensManager.shared.isLensSupported("0.5") ? CameraCapability(
            resolution: CGSize(width: 4032, height: 3024),
            sensorSize: CGSize(width: 5.9, height: 4.4),
            aperture: 2.4,
            focalLength: CameraLensManager.shared.getFocalLength(for: .builtInUltraWideCamera, model: model),
            pixelSize: 1.4,
            stabilization: [.standard],
            maxFrameRate: 60.0,
            supportedFormats: []
        ) : nil
        
        // 创建长焦能力（如果支持）
        let telephoto = CameraLensManager.shared.getSupportedLenses().contains { Float($0) ?? 0 > 1.0 } ? CameraCapability(
            resolution: CGSize(width: 4032, height: 3024),
            sensorSize: CGSize(width: 6.7, height: 5.0),
            aperture: 2.8,
            focalLength: CameraLensManager.shared.getFocalLength(for: .builtInTelephotoCamera, model: model),
            pixelSize: 1.0,
            stabilization: [.standard],
            maxFrameRate: 60.0,
            supportedFormats: []
        ) : nil
        
        // 将 CameraType 转换为 CameraSystemType
        let cameraType = CameraLensManager.shared.getDeviceType()
        let cameraSystem: CameraSystemType
        switch cameraType {
        case .single:
            cameraSystem = .single
        case .dual:
            cameraSystem = .dual
        case .triple:
            cameraSystem = .triple
        case .quad:
            cameraSystem = .quad
        }
        
        return DeviceSpecification(
            model: model,
            cameraSystem: cameraSystem,
            mainCamera: mainCamera,
            ultraWide: ultraWide,
            telephoto: telephoto,
            lidarSupported: model.contains("Pro") && (model.contains("12") || model.contains("13") || model.contains("14") || model.contains("15")),
            macroSupported: model.contains("Pro") && (model.contains("13") || model.contains("14") || model.contains("15")),
            supportedFeatures: detectDeviceFeatures(model: model)
        )
    }
} 