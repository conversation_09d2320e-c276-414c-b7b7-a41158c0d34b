import Foundation
import UIKit

/// 提供屏幕尺寸信息的服务实现，封装对UIScreen的直接依赖
class ScreenMetricsService: ScreenMetricsServiceProtocol {
    /// 单例实例
    static let shared = ScreenMetricsService()
    
    private init() {}
    
    /// 获取屏幕尺寸
    var screenSize: CGSize {
        return UIScreen.main.bounds.size
    }
    
    /// 获取屏幕宽度
    var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }
    
    /// 获取屏幕高度
    var screenHeight: CGFloat {
        return UIScreen.main.bounds.height
    }
    
    /// 获取屏幕边界
    var screenBounds: CGRect {
        return UIScreen.main.bounds
    }
    
    /// 获取屏幕缩放比例
    var screenScale: CGFloat {
        return UIScreen.main.scale
    }
    
    /// 根据屏幕高度计算相对尺寸
    func relativeToScreenHeight(_ ratio: CGFloat) -> CGFloat {
        return screenHeight * ratio
    }
    
    /// 根据屏幕宽度计算相对尺寸
    func relativeToScreenWidth(_ ratio: CGFloat) -> CGFloat {
        return screenWidth * ratio
    }
} 