import Foundation
import UIKit

/// iOS平台的图像服务实现
class iOSImageService: ImageServiceProtocol {
    typealias ImageType = UIImage
    
    /// 单例实例
    static let shared = iOSImageService()
    
    private init() {}
    
    func createImage(from data: Data) -> UIImage? {
        return UIImage(data: data)
    }
    
    func convertToData(_ image: UIImage) -> Data? {
        return image.jpegData(compressionQuality: 1.0)
    }
    
    func applyFilter(to image: UIImage, with filter: Any) -> UIImage? {
        // 保持原有滤镜处理逻辑
        return image
    }
    
    func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage
    }
} 