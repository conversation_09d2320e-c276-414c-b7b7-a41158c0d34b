import Foundation
import SwiftUI
import UIKit

/// 提供视图控制器相关功能的服务实现，封装对UIKit的直接依赖
class ViewControllerService: ViewControllerServiceProtocol {
    /// 单例实例
    static let shared = ViewControllerService()
    
    private init() {}
    
    /// 获取当前呈现的视图控制器
    func getCurrentViewController() -> Any? {
        guard let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let rootVC = scene.windows.first?.rootViewController else {
            return nil
        }
        
        return findCurrentViewController(from: rootVC)
    }
    
    /// 从SwiftUI视图中创建UIViewController
    func makeViewController<Content: View>(from content: Content) -> Any {
        return UIHostingController(rootView: content)
    }
    
    /// 将SwiftUI视图作为模态呈现
    func presentAsModal<Content: View>(content: Content, animated: Bool = true, completion: (() -> Void)? = nil) {
        guard let currentVC = getCurrentViewController() as? UIViewController else {
            return
        }
        
        let hostingController = UIHostingController(rootView: content)
        currentVC.present(hostingController, animated: animated, completion: completion)
    }
    
    /// 关闭当前呈现的模态视图
    func dismissCurrentModal(animated: Bool = true, completion: (() -> Void)? = nil) {
        guard let currentVC = getCurrentViewController() as? UIViewController else {
            return
        }
        
        currentVC.dismiss(animated: animated, completion: completion)
    }
    
    /// 递归查找当前呈现的视图控制器
    private func findCurrentViewController(from viewController: UIViewController) -> UIViewController {
        if let presentedVC = viewController.presentedViewController {
            return findCurrentViewController(from: presentedVC)
        }
        
        if let tabBarVC = viewController as? UITabBarController,
           let selectedVC = tabBarVC.selectedViewController {
            return findCurrentViewController(from: selectedVC)
        }
        
        if let navigationVC = viewController as? UINavigationController,
           let visibleVC = navigationVC.visibleViewController {
            return findCurrentViewController(from: visibleVC)
        }
        
        return viewController
    }
} 