import AVFoundation

/// 录制服务实现类
class RecordingService: RecordingServiceProtocol {
    // MARK: - 私有属性

    /// 会话
    private let _session: AVCaptureSession

    // 计时器
    private var recordingTimer: Timer?

    // 状态
    private var isRecording: Bool = false
    private var recordingTime: TimeInterval = 0
    private var _isPaused: Bool = false
    
    // MARK: - 状态回调
    
    var onRecordingStateChanged: ((Bool) -> Void)?
    var onRecordingTimeChanged: ((TimeInterval) -> Void)?
    var onRecordingPauseStateChanged: ((Bool) -> Void)?
    
    // MARK: - 初始化
    
    /// 初始化录制服务
    /// - Parameter session: AVCaptureSession实例
    init(session: AVCaptureSession) {
        self._session = session
    }
    
    // MARK: - RecordingServiceProtocol 实现

    var session: AVCaptureSession {
        return _session
    }

    func startRecording() async throws {
        toggleRecordingInternal()
    }

    func stopRecording() async throws {
        toggleRecordingInternal()
    }

    func toggleRecording() async throws {
        toggleRecordingInternal()
    }

    // 添加暂停/继续功能
    func togglePause() {
        togglePauseInternal()
    }

    // 获取当前暂停状态
    func isPauseActive() -> Bool {
        return _isPaused
    }

    // MARK: - 私有实现方法

    private func toggleRecordingInternal() {
        isRecording.toggle()

        if !isRecording {
            _isPaused = false
            onRecordingPauseStateChanged?(false)
        }

        onRecordingStateChanged?(isRecording)

        if isRecording {
            startRecordingTimer()
        } else {
            stopRecordingTimer()
        }
    }

    private func togglePauseInternal() {
        guard isRecording else { return }

        _isPaused.toggle()

        if _isPaused {
            pauseRecordingTimer()
            onRecordingTimeChanged?(recordingTime)
        } else {
            resumeRecordingTimer()
        }

        onRecordingPauseStateChanged?(_isPaused)
    }

    private func startRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTime = 0
        _isPaused = false
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] _ in
            guard let self = self, !self._isPaused else { return }
            self.recordingTime += 1
            self.onRecordingTimeChanged?(self.recordingTime)
        }
    }

    private func pauseRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
    }

    private func resumeRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] _ in
            guard let self = self, !self._isPaused else { return }
            self.recordingTime += 1
            self.onRecordingTimeChanged?(self.recordingTime)
        }
    }

    private func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        recordingTime = 0
        _isPaused = false
        onRecordingTimeChanged?(recordingTime)
    }

    func formattedTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) / 60 % 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
} 