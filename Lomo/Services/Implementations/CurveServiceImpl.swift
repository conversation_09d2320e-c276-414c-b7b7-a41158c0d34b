// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import CoreGraphics
import Accelerate

/// 完整的曲线服务实现 - MVVM-S架构
/// 提供高性能的曲线调整和LUT生成功能
actor CurveServiceImpl: CurveServiceProtocol {
    
    // MARK: - 曲线状态管理
    
    private var curvePoints: [CurveChannel: [CGPoint]] = [:]
    private var curveIntensity: Float = 1.0
    private var isEnabled: Bool = true
    private var currentPreset: CurveProcessor.CurvePreset?
    private var renderQuality: CurveProcessor.CurveQuality = .standard
    
    // MARK: - LUT缓存
    
    private var lutCache: [CurveChannel: [Float]] = [:]
    private var lutCacheValid: [CurveChannel: Bool] = [:]
    private let lutResolution = 256
    
    // MARK: - 性能统计
    
    private var updateCount = 0
    private var totalUpdateTime: TimeInterval = 0
    private var lastUpdateDuration: TimeInterval = 0
    
    // MARK: - 边界约束
    
    private let minPointDistance: CGFloat = 0.01
    private let boundaryBuffer: CGFloat = 0.02
    private let maxPointsPerChannel = 16
    
    // MARK: - 初始化
    
    init() {
        // 初始化所有通道的默认点
        for channel in CurveChannel.allCases {
            curvePoints[channel] = createDefaultPoints()
            lutCacheValid[channel] = false
        }
        
        print("🎨 [CurveServiceImpl] 曲线服务初始化完成")
        print("   - 支持通道: \(CurveChannel.allCases.map { $0.displayName }.joined(separator: ", "))")
        print("   - LUT分辨率: \(lutResolution)")
    }
    
    // MARK: - 曲线点管理
    
    func updateCurvePoints(_ points: [CGPoint], for channel: CurveChannel) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 验证和清理输入点
        let validatedPoints = try validateAndCleanPoints(points)
        
        // 确保点按X坐标排序
        let sortedPoints = validatedPoints.sorted { $0.x < $1.x }
        
        // 更新曲线点
        curvePoints[channel] = sortedPoints
        
        // 标记LUT缓存无效
        lutCacheValid[channel] = false
        
        // 如果是RGB通道，可能影响其他通道的显示
        if channel == .rgb {
            invalidateAllLUTCache()
        }
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [CurveServiceImpl] 更新曲线点完成")
        print("   - 通道: \(channel.displayName)")
        print("   - 点数: \(sortedPoints.count)")
        print("   - 处理时间: \(String(format: "%.2f", duration * 1000))ms")
    }
    
    func getCurrentCurvePoints(for channel: CurveChannel) async -> [CGPoint] {
        return curvePoints[channel] ?? createDefaultPoints()
    }
    
    func updatePointPosition(index: Int, normalizedPoint: CGPoint, for channel: CurveChannel) async throws {
        guard var points = curvePoints[channel], index >= 0, index < points.count else {
            throw CurveError.invalidPointIndex("无效的点索引: \(index)")
        }
        
        // 验证新位置
        let validatedPoint = try validatePointPosition(normalizedPoint, at: index, in: points)
        
        // 更新点位置
        points[index] = validatedPoint
        
        // 重新排序以保持X坐标顺序
        points.sort { $0.x < $1.x }
        
        // 更新曲线点
        try await updateCurvePoints(points, for: channel)
    }
    
    func addPoint(at normalizedPoint: CGPoint, for channel: CurveChannel) async throws -> Int {
        guard var points = curvePoints[channel] else {
            throw CurveError.channelNotFound("通道不存在: \(channel)")
        }
        
        // 检查点数限制
        guard points.count < maxPointsPerChannel else {
            throw CurveError.tooManyPoints("每个通道最多支持\(maxPointsPerChannel)个点")
        }
        
        // 验证新点位置
        let validatedPoint = try validateNewPoint(normalizedPoint, in: points)
        
        // 添加新点
        points.append(validatedPoint)
        
        // 重新排序
        points.sort { $0.x < $1.x }
        
        // 找到新点的索引
        guard let newIndex = points.firstIndex(of: validatedPoint) else {
            throw CurveError.pointAdditionFailed("无法确定新点索引")
        }
        
        // 更新曲线点
        try await updateCurvePoints(points, for: channel)
        
        print("🎨 [CurveServiceImpl] 添加曲线点")
        print("   - 通道: \(channel.displayName)")
        print("   - 位置: (\(String(format: "%.3f", validatedPoint.x)), \(String(format: "%.3f", validatedPoint.y)))")
        print("   - 索引: \(newIndex)")
        
        return newIndex
    }
    
    func removePoint(at index: Int, for channel: CurveChannel) async throws {
        guard var points = curvePoints[channel], index >= 0, index < points.count else {
            throw CurveError.invalidPointIndex("无效的点索引: \(index)")
        }
        
        // 确保至少保留两个点（起点和终点）
        guard points.count > 2 else {
            throw CurveError.cannotRemovePoint("至少需要保留两个控制点")
        }
        
        // 不允许删除起点和终点
        let pointToRemove = points[index]
        if pointToRemove.x <= 0.01 || pointToRemove.x >= 0.99 {
            throw CurveError.cannotRemovePoint("不能删除起点或终点")
        }
        
        // 移除点
        points.remove(at: index)
        
        // 更新曲线点
        try await updateCurvePoints(points, for: channel)
        
        print("🎨 [CurveServiceImpl] 移除曲线点")
        print("   - 通道: \(channel.displayName)")
        print("   - 索引: \(index)")
        print("   - 剩余点数: \(points.count)")
    }
    
    // MARK: - 曲线预设
    
    func applyPreset(_ preset: CurveProcessor.CurvePreset, to channel: CurveChannel, intensity: Float) async throws {
        let presetPoints = generatePresetPoints(preset)
        let currentPoints = curvePoints[channel] ?? createDefaultPoints()
        
        // 根据强度混合预设点和当前点
        let blendedPoints = blendPoints(from: currentPoints, to: presetPoints, intensity: intensity)
        
        // 应用混合后的点
        try await updateCurvePoints(blendedPoints, for: channel)
        
        // 更新当前预设
        currentPreset = preset
        
        print("🎨 [CurveServiceImpl] 应用曲线预设")
        print("   - 预设: \(preset.displayName)")
        print("   - 通道: \(channel.displayName)")
        print("   - 强度: \(intensity)")
    }
    
    func getCurrentPreset() async -> CurveProcessor.CurvePreset? {
        return currentPreset
    }
    
    // MARK: - 曲线重置
    
    func resetAllCurves() async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        for channel in CurveChannel.allCases {
            curvePoints[channel] = createDefaultPoints()
            lutCacheValid[channel] = false
        }
        
        curveIntensity = 1.0
        currentPreset = nil
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [CurveServiceImpl] 重置所有曲线完成")
    }
    
    func resetChannel(_ channel: CurveChannel) async throws {
        curvePoints[channel] = createDefaultPoints()
        lutCacheValid[channel] = false
        
        print("🎨 [CurveServiceImpl] 重置通道: \(channel.displayName)")
    }
    
    func resetCurrentChannel() async throws {
        // 这里需要知道当前选中的通道，简化实现重置RGB通道
        try await resetChannel(.rgb)
    }
    
    // MARK: - 曲线状态
    
    func updateCurveIntensity(_ intensity: Float) async throws {
        curveIntensity = max(0.0, min(1.0, intensity))
        
        // 强度变化会影响所有LUT
        invalidateAllLUTCache()
        
        print("🎨 [CurveServiceImpl] 更新曲线强度: \(curveIntensity)")
    }
    
    func getCurrentCurveIntensity() async -> Float {
        return curveIntensity
    }
    
    func setCurveEnabled(_ enabled: Bool) async throws {
        isEnabled = enabled
        
        if !enabled {
            invalidateAllLUTCache()
        }
        
        print("🎨 [CurveServiceImpl] 设置曲线启用状态: \(enabled)")
    }
    
    func isCurveEnabled() async -> Bool {
        return isEnabled
    }
    
    func hasActiveCurves() async -> Bool {
        guard isEnabled else { return false }
        
        // 检查是否有任何通道偏离默认曲线
        for channel in CurveChannel.allCases {
            if let points = curvePoints[channel], !isDefaultCurve(points) {
                return true
            }
        }
        
        return false
    }
    
    // MARK: - 曲线质量
    
    func updateRenderQuality(_ quality: CurveProcessor.CurveQuality) async throws {
        renderQuality = quality
        
        // 质量变化需要重新生成LUT
        invalidateAllLUTCache()
        
        print("🎨 [CurveServiceImpl] 更新渲染质量: \(quality)")
    }
    
    func getCurrentRenderQuality() async -> CurveProcessor.CurveQuality {
        return renderQuality
    }
    
    // MARK: - LUT生成
    
    func getCurrentLUTs() async -> [CurveChannel: [Float]] {
        var result: [CurveChannel: [Float]] = [:]
        
        for channel in CurveChannel.allCases {
            result[channel] = await getLUT(for: channel)
        }
        
        return result
    }
    
    func forceUpdateLUTs() async throws {
        invalidateAllLUTCache()
        
        // 预生成所有通道的LUT
        for channel in CurveChannel.allCases {
            _ = await getLUT(for: channel)
        }
        
        print("🎨 [CurveServiceImpl] 强制更新所有LUT完成")
    }
    
    // MARK: - 性能监控
    
    func getPerformanceStats() async -> (averageTime: TimeInterval, updateCount: Int, lastDuration: TimeInterval) {
        let averageTime = updateCount > 0 ? totalUpdateTime / Double(updateCount) : 0
        return (averageTime, updateCount, lastUpdateDuration)
    }
    
    // MARK: - 边界验证
    
    func validateAndFixBoundaries() async throws {
        var hasChanges = false
        
        for channel in CurveChannel.allCases {
            if var points = curvePoints[channel] {
                let originalCount = points.count
                
                // 修正边界
                points = points.compactMap { point in
                    let fixedPoint = CGPoint(
                        x: max(0.0, min(1.0, point.x)),
                        y: max(0.0, min(1.0, point.y))
                    )
                    return fixedPoint
                }
                
                // 移除重复点
                points = removeDuplicatePoints(points)
                
                // 确保至少有起点和终点
                if points.isEmpty || points.first?.x != 0.0 {
                    points.insert(CGPoint(x: 0.0, y: 0.0), at: 0)
                }
                if points.last?.x != 1.0 {
                    points.append(CGPoint(x: 1.0, y: 1.0))
                }
                
                // 重新排序
                points.sort { $0.x < $1.x }
                
                if points.count != originalCount || points != curvePoints[channel] {
                    curvePoints[channel] = points
                    lutCacheValid[channel] = false
                    hasChanges = true
                }
            }
        }
        
        if hasChanges {
            print("🎨 [CurveServiceImpl] 边界验证和修正完成")
        }
    }
    
    func isCurveWithinBounds(for channel: CurveChannel) async -> Bool {
        guard let points = curvePoints[channel] else { return true }
        
        for point in points {
            if point.x < 0.0 || point.x > 1.0 || point.y < 0.0 || point.y > 1.0 {
                return false
            }
        }
        
        return true
    }
    
    // MARK: - 私有方法 - 点验证和处理
    
    private func createDefaultPoints() -> [CGPoint] {
        return [CGPoint(x: 0.0, y: 0.0), CGPoint(x: 1.0, y: 1.0)]
    }
    
    private func validateAndCleanPoints(_ points: [CGPoint]) throws -> [CGPoint] {
        guard !points.isEmpty else {
            throw CurveError.invalidPoints("点数组不能为空")
        }
        
        guard points.count <= maxPointsPerChannel else {
            throw CurveError.tooManyPoints("点数超过限制: \(points.count) > \(maxPointsPerChannel)")
        }
        
        // 验证每个点都在边界内
        for (index, point) in points.enumerated() {
            guard point.x >= 0.0 && point.x <= 1.0 && point.y >= 0.0 && point.y <= 1.0 else {
                throw CurveError.pointOutOfBounds("点\(index)超出边界: (\(point.x), \(point.y))")
            }
        }
        
        // 移除重复点
        let cleanedPoints = removeDuplicatePoints(points)
        
        // 确保有起点和终点
        var finalPoints = cleanedPoints
        if finalPoints.first?.x != 0.0 {
            finalPoints.insert(CGPoint(x: 0.0, y: finalPoints.first?.y ?? 0.0), at: 0)
        }
        if finalPoints.last?.x != 1.0 {
            finalPoints.append(CGPoint(x: 1.0, y: finalPoints.last?.y ?? 1.0))
        }
        
        return finalPoints
    }
    
    private func validatePointPosition(_ point: CGPoint, at index: Int, in points: [CGPoint]) throws -> CGPoint {
        // 边界检查
        guard point.x >= 0.0 && point.x <= 1.0 && point.y >= 0.0 && point.y <= 1.0 else {
            throw CurveError.pointOutOfBounds("点位置超出边界: (\(point.x), \(point.y))")
        }
        
        // 检查与相邻点的距离
        if index > 0 {
            let prevPoint = points[index - 1]
            if point.x - prevPoint.x < minPointDistance {
                throw CurveError.pointsTooClose("点与前一个点距离过近")
            }
        }
        
        if index < points.count - 1 {
            let nextPoint = points[index + 1]
            if nextPoint.x - point.x < minPointDistance {
                throw CurveError.pointsTooClose("点与后一个点距离过近")
            }
        }
        
        return point
    }
    
    private func validateNewPoint(_ point: CGPoint, in points: [CGPoint]) throws -> CGPoint {
        // 边界检查
        guard point.x >= 0.0 && point.x <= 1.0 && point.y >= 0.0 && point.y <= 1.0 else {
            throw CurveError.pointOutOfBounds("新点位置超出边界: (\(point.x), \(point.y))")
        }
        
        // 检查是否与现有点过近
        for existingPoint in points {
            let distance = sqrt(pow(point.x - existingPoint.x, 2) + pow(point.y - existingPoint.y, 2))
            if distance < minPointDistance {
                throw CurveError.pointsTooClose("新点与现有点距离过近")
            }
        }
        
        return point
    }
    
    private func removeDuplicatePoints(_ points: [CGPoint]) -> [CGPoint] {
        var uniquePoints: [CGPoint] = []
        
        for point in points {
            let isDuplicate = uniquePoints.contains { existingPoint in
                abs(existingPoint.x - point.x) < minPointDistance && abs(existingPoint.y - point.y) < minPointDistance
            }
            
            if !isDuplicate {
                uniquePoints.append(point)
            }
        }
        
        return uniquePoints
    }
    
    private func isDefaultCurve(_ points: [CGPoint]) -> Bool {
        let defaultPoints = createDefaultPoints()
        
        guard points.count == defaultPoints.count else { return false }
        
        for (index, point) in points.enumerated() {
            let defaultPoint = defaultPoints[index]
            if abs(point.x - defaultPoint.x) > 0.001 || abs(point.y - defaultPoint.y) > 0.001 {
                return false
            }
        }
        
        return true
    }
    
    // MARK: - 预设生成
    
    private func generatePresetPoints(_ preset: CurveProcessor.CurvePreset) -> [CGPoint] {
        switch preset {
        case .linear:
            return [CGPoint(x: 0.0, y: 0.0), CGPoint(x: 1.0, y: 1.0)]
        case .contrastCurve:
            return [
                CGPoint(x: 0.0, y: 0.0),
                CGPoint(x: 0.25, y: 0.15),
                CGPoint(x: 0.75, y: 0.85),
                CGPoint(x: 1.0, y: 1.0)
            ]
        case .brightCurve:
            return [
                CGPoint(x: 0.0, y: 0.1),
                CGPoint(x: 1.0, y: 1.0)
            ]
        case .vintageCurve:
            return [
                CGPoint(x: 0.0, y: 0.05),
                CGPoint(x: 0.3, y: 0.25),
                CGPoint(x: 0.7, y: 0.75),
                CGPoint(x: 1.0, y: 0.95)
            ]
        case .sCurve:
            return [
                CGPoint(x: 0.0, y: 0.0),
                CGPoint(x: 0.25, y: 0.1),
                CGPoint(x: 0.75, y: 0.9),
                CGPoint(x: 1.0, y: 1.0)
            ]
        case .darkCurve:
            return [
                CGPoint(x: 0.0, y: 0.0),
                CGPoint(x: 1.0, y: 0.9)
            ]
        case .softCurve:
            return [
                CGPoint(x: 0.0, y: 0.05),
                CGPoint(x: 0.5, y: 0.5),
                CGPoint(x: 1.0, y: 0.95)
            ]
        case .dramaticCurve:
            return [
                CGPoint(x: 0.0, y: 0.0),
                CGPoint(x: 0.2, y: 0.05),
                CGPoint(x: 0.8, y: 0.95),
                CGPoint(x: 1.0, y: 1.0)
            ]
        default:
            // 对于其他预设，使用预设自身定义的点或返回线性曲线
            let presetPoints = preset.points
            if !presetPoints.isEmpty {
                return presetPoints
            } else {
                // 如果预设没有定义点，返回线性曲线作为默认值
                return [CGPoint(x: 0.0, y: 0.0), CGPoint(x: 1.0, y: 1.0)]
            }
        }
    }
    
    private func blendPoints(from: [CGPoint], to: [CGPoint], intensity: Float) -> [CGPoint] {
        let clampedIntensity = max(0.0, min(1.0, intensity))
        
        // 简化实现：直接插值起点和终点
        var blendedPoints: [CGPoint] = []
        
        let maxCount = max(from.count, to.count)
        for i in 0..<maxCount {
            let fromPoint = i < from.count ? from[i] : from.last ?? CGPoint(x: 1.0, y: 1.0)
            let toPoint = i < to.count ? to[i] : to.last ?? CGPoint(x: 1.0, y: 1.0)
            
            let blendedPoint = CGPoint(
                x: fromPoint.x + (toPoint.x - fromPoint.x) * CGFloat(clampedIntensity),
                y: fromPoint.y + (toPoint.y - fromPoint.y) * CGFloat(clampedIntensity)
            )
            
            blendedPoints.append(blendedPoint)
        }
        
        return blendedPoints
    }
    
    // MARK: - LUT生成和缓存
    
    private func getLUT(for channel: CurveChannel) async -> [Float] {
        // 检查缓存
        if lutCacheValid[channel] == true, let cachedLUT = lutCache[channel] {
            return cachedLUT
        }
        
        // 生成新的LUT
        let lut = generateLUT(for: channel)
        
        // 缓存结果
        lutCache[channel] = lut
        lutCacheValid[channel] = true
        
        return lut
    }
    
    private func generateLUT(for channel: CurveChannel) -> [Float] {
        guard let points = curvePoints[channel], points.count >= 2 else {
            return generateLinearLUT()
        }
        
        var lut: [Float] = []
        lut.reserveCapacity(lutResolution)
        
        for i in 0..<lutResolution {
            let input = Float(i) / Float(lutResolution - 1)
            let output = evaluateCurve(at: input, points: points)
            lut.append(output * curveIntensity + input * (1.0 - curveIntensity))
        }
        
        return lut
    }
    
    private func generateLinearLUT() -> [Float] {
        var lut: [Float] = []
        lut.reserveCapacity(lutResolution)
        
        for i in 0..<lutResolution {
            lut.append(Float(i) / Float(lutResolution - 1))
        }
        
        return lut
    }
    
    private func evaluateCurve(at input: Float, points: [CGPoint]) -> Float {
        let x = CGFloat(input)
        
        // 找到输入值所在的区间
        for i in 0..<(points.count - 1) {
            let p1 = points[i]
            let p2 = points[i + 1]
            
            if x >= p1.x && x <= p2.x {
                // 在区间内进行插值
                if p2.x == p1.x {
                    return Float(p1.y)
                }
                
                let t = (x - p1.x) / (p2.x - p1.x)
                
                // 使用Catmull-Rom样条插值获得更平滑的曲线
                if points.count > 2 {
                    return Float(catmullRomInterpolation(t: t, p0: getControlPoint(at: i - 1, in: points), p1: p1, p2: p2, p3: getControlPoint(at: i + 2, in: points)))
                } else {
                    // 线性插值
                    return Float(p1.y + (p2.y - p1.y) * t)
                }
            }
        }
        
        // 超出范围时返回边界值
        if x <= points.first!.x {
            return Float(points.first!.y)
        } else {
            return Float(points.last!.y)
        }
    }
    
    private func getControlPoint(at index: Int, in points: [CGPoint]) -> CGPoint {
        if index < 0 {
            return points.first!
        } else if index >= points.count {
            return points.last!
        } else {
            return points[index]
        }
    }
    
    private func catmullRomInterpolation(t: CGFloat, p0: CGPoint, p1: CGPoint, p2: CGPoint, p3: CGPoint) -> CGFloat {
        let t2 = t * t
        let t3 = t2 * t
        
        return 0.5 * (
            (2.0 * p1.y) +
            (-p0.y + p2.y) * t +
            (2.0 * p0.y - 5.0 * p1.y + 4.0 * p2.y - p3.y) * t2 +
            (-p0.y + 3.0 * p1.y - 3.0 * p2.y + p3.y) * t3
        )
    }
    
    private func invalidateAllLUTCache() {
        for channel in CurveChannel.allCases {
            lutCacheValid[channel] = false
        }
    }
    
    private func updatePerformanceStats(duration: TimeInterval) {
        updateCount += 1
        totalUpdateTime += duration
        lastUpdateDuration = duration
    }
}

// MARK: - 支持结构和枚举

/// 曲线错误类型
enum CurveError: LocalizedError {
    case invalidPoints(String)
    case invalidPointIndex(String)
    case pointOutOfBounds(String)
    case pointsTooClose(String)
    case tooManyPoints(String)
    case channelNotFound(String)
    case cannotRemovePoint(String)
    case pointAdditionFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidPoints(let message):
            return "无效的曲线点: \(message)"
        case .invalidPointIndex(let message):
            return "无效的点索引: \(message)"
        case .pointOutOfBounds(let message):
            return "点超出边界: \(message)"
        case .pointsTooClose(let message):
            return "点距离过近: \(message)"
        case .tooManyPoints(let message):
            return "点数过多: \(message)"
        case .channelNotFound(let message):
            return "通道未找到: \(message)"
        case .cannotRemovePoint(let message):
            return "无法移除点: \(message)"
        case .pointAdditionFailed(let message):
            return "添加点失败: \(message)"
        }
    }
}


