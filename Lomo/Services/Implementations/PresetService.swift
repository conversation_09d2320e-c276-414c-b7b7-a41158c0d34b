import Foundation

/// 预设服务实现类，提供通用的预设选择和获取逻辑
class PresetService: PresetServiceProtocol {
    /// 单例实例
    static let shared = PresetService()
    
    /// 私有初始化方法
    private init() {}
    
    /// 选择预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    ///   - activeType: 当前激活的预设类型（引用传递）
    ///   - activeIndex: 当前激活的预设索引（引用传递）
    ///   - updateSettings: 自定义设置更新逻辑
    func selectPreset(type: PresetType, index: Int, activeType: inout String, activeIndex: inout Int, updateSettings: (PresetType, Int, Bool) -> Void) {
        let typeString = type.rawValue
        
        // 如果点击已选中的预设，则取消选择
        if activeType == typeString && activeIndex == index {
            // 取消选择
            activeType = ""
            activeIndex = -1
            // 执行自定义更新逻辑
            updateSettings(type, index, true) // true表示取消选择
        } else {
            // 更新全局活跃类型和索引
            activeType = typeString
            activeIndex = index
            // 执行自定义更新逻辑
            updateSettings(type, index, false) // false表示选择
        }
    }
    
    /// 根据预设类型获取所有预设（需要各子类实现具体逻辑）
    /// - Parameter type: 预设类型
    /// - Returns: 预设数组
    func getPresets(for type: PresetType) -> [Any] {
        // 默认返回空数组，具体实现由子类提供
        return []
    }
    
    /// 根据类型和索引获取特定预设（需要各子类实现具体逻辑）
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: 指定的预设
    func getPreset(type: PresetType, index: Int) -> Any? {
        // 默认返回nil，具体实现由子类提供
        return nil
    }
} 