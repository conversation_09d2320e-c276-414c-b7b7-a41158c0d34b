import Foundation

/// 刻度盘服务管理器
class DialServiceManager {
    // MARK: - Singleton
    static let shared = DialServiceManager()
    private init() {}
    
    // MARK: - Properties
    private var services: [DialMode: DialServiceProtocol] = [:]
    private var factory: DialServiceFactory = DefaultDialServiceFactory()
    
    // MARK: - Service Management
    func getService(for mode: DialMode) -> DialServiceProtocol? {
        if let service = services[mode] {
            return service
        }
        
        // 如果服务不存在，创建新服务
        if let newService = factory.createService(for: mode) {
            services[mode] = newService
            return newService
        }
        
        return nil
    }
    
    func registerFactory(_ factory: DialServiceFactory) {
        self.factory = factory
    }
    
    // MARK: - Service Configuration
    func configureService(for mode: DialMode, with configuration: DialServiceConfiguration) throws {
        guard let service = getService(for: mode) else {
            throw DialServiceError.serviceUnavailable
        }
        try service.configure(with: configuration)
    }
    
    // MARK: - Service Lifecycle
    func activateService(for mode: DialMode) throws {
        guard let service = getService(for: mode) else {
            throw DialServiceError.serviceUnavailable
        }
        try service.activate()
    }
    
    func deactivateService(for mode: DialMode) {
        services[mode]?.deactivate()
    }
    
    func resetService(for mode: DialMode) {
        services[mode]?.reset()
    }
    
    // MARK: - Global State Management
    func deactivateAllServices() {
        services.values.forEach { $0.deactivate() }
    }
    
    func resetAllServices() {
        services.values.forEach { $0.reset() }
    }
    
    // MARK: - Service Removal
    func removeService(for mode: DialMode) {
        services[mode]?.deactivate()
        services.removeValue(forKey: mode)
    }
    
    func removeAllServices() {
        deactivateAllServices()
        services.removeAll()
    }
} 