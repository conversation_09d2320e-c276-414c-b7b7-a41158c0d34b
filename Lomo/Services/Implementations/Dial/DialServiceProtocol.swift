import Foundation

/// 刻度盘服务错误类型
enum DialServiceError: Error {
    case invalidValue
    case serviceUnavailable
    case modeNotSupported
    case configurationError
    case hardwareLimitReached
}

/// 刻度盘服务状态
enum DialServiceState {
    case idle
    case active
    case disabled
    case error(DialServiceError)
}

/// 刻度盘服务代理
protocol DialServiceDelegate: AnyObject {
    func dialService(_ service: DialServiceProtocol, didChangeValue value: Double, for mode: DialMode)
    func dialService(_ service: DialServiceProtocol, didChangeState state: DialServiceState)
    func dialService(_ service: DialServiceProtocol, didEncounterError error: DialServiceError)
}

/// 刻度盘服务配置
protocol DialServiceConfiguration {
    var mode: DialMode { get }
    var updateInterval: TimeInterval { get }
    var shouldPersistState: Bool { get }
}

/// 刻度盘服务协议
protocol DialServiceProtocol: AnyObject {
    // MARK: - Properties
    var delegate: DialServiceDelegate? { get set }
    var currentMode: DialMode { get }
    var state: DialServiceState { get }
    
    // MARK: - Configuration
    func configure(with configuration: DialServiceConfiguration) throws
    func getConfiguration(for mode: DialMode) -> DialConfiguration?
    
    // MARK: - Value Management
    func getValue(for mode: DialMode) -> Double
    func setValue(_ value: Double, for mode: DialMode) throws
    
    // MARK: - State Management
    func activate() throws
    func deactivate()
    func reset()
    
    // MARK: - Mode Management
    func isSupported(_ mode: DialMode) -> Bool
    func switchTo(mode: DialMode) throws
    
    // MARK: - Persistence
    func saveState() throws
    func restoreState() throws
}

/// 刻度盘服务工厂协议
protocol DialServiceFactory {
    func createService(for mode: DialMode) -> DialServiceProtocol?
} 