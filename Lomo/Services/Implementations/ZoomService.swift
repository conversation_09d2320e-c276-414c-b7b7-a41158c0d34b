import Foundation
import UIKit
import AVFoundation

/// 变焦服务实现类
class ZoomService: ZoomServiceProtocol {
    // MARK: - 私有属性

    /// 会话
    private let session: AVCaptureSession

    /// 相机位置
    private var currentPosition: AVCaptureDevice.Position = .back

    /// 存储服务
    private let storageService = UserDefaultsService.shared

    // 状态属性
    private var isTouching: Bool = false
    private var selectedLensIndex: Int = 1
    private var availableLenses: [String] = []

    // 新增属性
    private var selectedLens: String = "1x"
    private var isFromButtonSelection: Bool = false
    private var currentZoomFactor: Double = 1.0

    // UI控制服务
    private let uiControlService: UIControlServiceProtocol
    
    // MARK: - 状态回调
    
    var onZoomFactorChanged: ((CGFloat) -> Void)?
    var onSelectedLensIndexChanged: ((Int) -> Void)?
    var onLensSelected: ((String) -> Void)?
    
    // MARK: - 初始化
    
    /// 初始化变焦服务
    /// - Parameters:
    ///   - session: AVCaptureSession实例
    ///   - uiControlService: UI控制服务实例
    init(session: AVCaptureSession, uiControlService: UIControlServiceProtocol) {
        self.session = session
        self.uiControlService = uiControlService
        // 初始化时获取可用镜头列表
        self.availableLenses = CameraLensManager.shared.getSupportedLenses()
        print("📱 [Debug] 初始化时的镜头列表: \(self.availableLenses)")
    }
    
    // MARK: - ZoomServiceProtocol 实现
    
    func showZoomDial() {
        print("📱 显示变焦刻度盘")
        uiControlService.showDial(mode: .zoom)
    }

    func hideZoomDial() {
        print("📱 隐藏变焦刻度盘")
        uiControlService.hideDial()
    }

    func updateZoom(_ factor: CGFloat) {
        print("📱 ZoomService: 开始更新变焦 zoomFactor=\(factor)")

        // 更新选中的镜头索引
        let newIndex = getLensIndexForZoomFactor(factor)
        if selectedLensIndex != newIndex {
            selectedLensIndex = newIndex
            onSelectedLensIndexChanged?(newIndex)
            print("📱 更新选中镜头索引: \(newIndex)")
        }

        // 真机环境下更新设备变焦
        guard let device = AVCaptureDevice.camera(position: currentPosition) else {
            print("⚠️ 无法获取摄像头设备，但仍然更新 UI")
            onZoomFactorChanged?(factor)
            return
        }

        do {
            try device.lockForConfiguration()
            let clampedZoomFactor = min(max(factor, device.minAvailableVideoZoomFactor), device.maxAvailableVideoZoomFactor)
            device.videoZoomFactor = clampedZoomFactor
            device.unlockForConfiguration()
            // 保持传入的精确值，而不是使用 clampedZoomFactor
            onZoomFactorChanged?(factor)
        } catch {
            print("变焦更新错误: \(error.localizedDescription)")
        }
    }

    func selectLens(_ lens: String) {
        print("📱 [Debug] selectLens 开始 - 传入镜头: \(lens)")
        print("📱 [Debug] selectLens 前 - currentZoomFactor: \(currentZoomFactor), selectedLens: \(selectedLens)")

        selectedLens = lens
        isFromButtonSelection = true

        print("📱 [Debug] selectLens 后 - currentZoomFactor: \(currentZoomFactor), selectedLens: \(selectedLens)")

        // 真机环境下更新设备变焦
        guard let device = AVCaptureDevice.camera(position: currentPosition) else {
            print("⚠️ 无法获取摄像头设备")
            return
        }

        do {
            try device.lockForConfiguration()
            if let zoomFactor = Float(lens.replacingOccurrences(of: "x", with: "")) {
                let targetZoomFactor = CGFloat(zoomFactor)
                let clampedZoomFactor = min(max(targetZoomFactor, device.minAvailableVideoZoomFactor), device.maxAvailableVideoZoomFactor)
                device.videoZoomFactor = clampedZoomFactor
                print("📱 [Debug] 设备变焦更新 - targetZoomFactor: \(targetZoomFactor), clampedZoomFactor: \(clampedZoomFactor)")
            }
            device.unlockForConfiguration()
        } catch {
            print("镜头切换错误: \(error.localizedDescription)")
        }
        print("📱 [Debug] selectLens 结束")
        onLensSelected?(lens)
    }

    func updateAvailableLenses(_ lenses: [String]) {
        print("📱 [Debug] 更新镜头列表前: \(self.availableLenses)")
        self.availableLenses = lenses
        print("📱 [Debug] 更新镜头列表后: \(self.availableLenses)")
        print("📱 更新可用镜头列表: \(lenses)")
    }

    // MARK: - 额外方法

    /// 根据索引选择镜头
    /// - Parameter index: 镜头索引
    func selectLensAtIndex(_ index: Int) {
        print("📱 [Debug] selectLensAtIndex 开始")
        print("📱 [Debug] 当前可用镜头列表: \(availableLenses)")
        print("📱 选择镜头索引: \(index)")

        // 更新选中的镜头索引
        selectedLensIndex = index
        onSelectedLensIndexChanged?(index)

        // 获取对应的镜头值
        guard index < availableLenses.count else { return }
        let lens = availableLenses[index]
        print("📱 对应的镜头值: \(lens)")

        // 获取精确的变焦值
        let exactZoomFactor = Float(lens.replacingOccurrences(of: "x", with: "")) ?? 1.0
        print("📱 [Debug] 计算得到的变焦值: \(exactZoomFactor)")

        // 计算并保存旋转角度
        let rotationAngle = calculateRotationAngle(for: exactZoomFactor)
        storageService.saveDialRotationAngle(rotationAngle, forDialType: "zoom")

        // 标记为从按钮切换
        storageService.setIsFromButtonSelection(true)

        // 真机环境下更新设备变焦
        guard let device = AVCaptureDevice.camera(position: currentPosition) else {
            print("⚠️ 无法获取摄像头设备，但仍然更新 UI")
            onZoomFactorChanged?(CGFloat(exactZoomFactor))
            print("📱 [Debug] 发送变焦更新: \(exactZoomFactor)")
            return
        }

        do {
            try device.lockForConfiguration()
            let clampedZoomFactor = min(max(CGFloat(exactZoomFactor), device.minAvailableVideoZoomFactor), device.maxAvailableVideoZoomFactor)
            device.videoZoomFactor = clampedZoomFactor
            device.unlockForConfiguration()
            onZoomFactorChanged?(CGFloat(exactZoomFactor))
        } catch {
            print("镜头切换错误: \(error.localizedDescription)")
        }
    }

    /// 处理触摸开始事件
    func handleTouchBegan() {
        print("📱 开始触摸变焦刻度盘")
        uiControlService.handleTouchBegan()
    }

    /// 处理触摸结束事件
    func handleTouchEnded() {
        print("📱 结束触摸变焦刻度盘")
        uiControlService.handleTouchEnded()
    }

    // MARK: - 私有辅助方法

    // 根据缩放值计算对应的镜头索引
    private func getLensIndexForZoomFactor(_ zoomFactor: CGFloat) -> Int {
        let zoom = Float(zoomFactor)
        print("📱 计算缩放值 \(zoom) 对应的镜头索引")

        // 简单直接的区间判断
        if zoom >= 2.0 {
            return 2  // 2x 按钮
        } else if zoom >= 1.0 {
            return 1  // 1x 按钮
        } else if zoom >= 0.5 {
            return 0  // 0.5x 按钮
        }

        return 1  // 默认返回 1x
    }

    // 根据变焦倍率计算旋转角度
    private func calculateRotationAngle(for zoomFactor: Float) -> Double {
        // 基准角度配置
        let baseAngle = 20.0  // 基准角度
        let q = 0.8  // 公比

        if zoomFactor == 0.5 {
            return baseAngle  // 0.5x位置固定在20度
        } else if zoomFactor == 1.0 {
            return 0.0  // 1x位置固定在0度
        } else {
            // 计算1x右边的角度
            var segmentIndex = 0
            var currentZoom: Float = 1.0

            while currentZoom < zoomFactor {
                currentZoom *= 2  // 每次翻倍
                segmentIndex += 1

                if currentZoom >= zoomFactor {
                    // 找到目标区间
                    let segmentAngle = baseAngle * pow(q, Double(segmentIndex - 1))
                    let progress = (Double(zoomFactor) - Double(currentZoom/2)) / (Double(currentZoom) - Double(currentZoom/2))
                    return -(segmentAngle * progress)
                }
            }
        }

        return 0.0  // 默认返回1x位置
    }
}