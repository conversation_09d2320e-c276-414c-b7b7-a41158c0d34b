// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit
import CoreImage
import Metal
import MetalKit

/// 完整的渲染服务实现 - MVVM-S架构
/// 提供高性能的Metal渲染管线和完整的图像处理功能
actor RenderingServiceImpl: RenderingServiceProtocol {
    
    // MARK: - Metal渲染基础设施
    
    private let metalDevice: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let ciContext: CIContext
    private let textureCache: CVMetalTextureCache
    
    // MARK: - 渲染状态
    
    private var baseImage: UIImage?
    private var currentParameters = FilterParameters()
    private var renderingMode: RenderingMode = .realtime
    private var renderingQuality: RenderingQuality = .standard
    private var isCurrentlyRendering = false
    private var lastError: Error?
    
    // MARK: - LUT管理
    
    private var currentLUTTexture: MTLTexture?
    private var lutIntensity: Float = 1.0
    
    // MARK: - 缓存管理
    
    private var imageCache: [String: UIImage] = [:]
    private var textureCache_internal: [String: MTLTexture] = [:]
    private let maxCacheSize = 50
    
    // MARK: - 性能统计
    
    private var frameCount = 0
    private var totalRenderTime: TimeInterval = 0
    private var lastFrameTime: TimeInterval = 0
    private var memoryUsage: Int = 0
    
    // MARK: - 渲染管线
    
    private var renderPipelineState: MTLRenderPipelineState?
    private var computePipelineState: MTLComputePipelineState?
    
    // MARK: - 初始化
    
    init() throws {
        // 初始化Metal设备
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw RenderingError.metalInitializationFailed("无法创建Metal设备")
        }
        self.metalDevice = device
        
        // 创建命令队列
        guard let queue = device.makeCommandQueue() else {
            throw RenderingError.metalInitializationFailed("无法创建Metal命令队列")
        }
        self.commandQueue = queue
        
        // 创建Core Image上下文
        self.ciContext = CIContext(mtlDevice: device, options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .useSoftwareRenderer: false
        ])
        
        // 创建纹理缓存
        var cache: CVMetalTextureCache?
        let result = CVMetalTextureCacheCreate(kCFAllocatorDefault, nil, device, nil, &cache)
        guard result == kCVReturnSuccess, let textureCache = cache else {
            throw RenderingError.metalInitializationFailed("无法创建Metal纹理缓存")
        }
        self.textureCache = textureCache
        
        // 初始化渲染管线
        try setupRenderingPipeline()
        
        print("🎨 [RenderingServiceImpl] Metal渲染服务初始化完成")
        print("   - 设备: \(device.name)")
        print("   - 支持的特性集: \(device.supportsFeatureSet(.iOS_GPUFamily4_v1) ? "iOS GPU Family 4" : "基础特性集")")
    }
    
    // MARK: - 图像设置
    
    func setBaseImage(_ image: UIImage) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        baseImage = image
        
        // 清理相关缓存
        clearImageSpecificCache()
        
        // 预处理图像
        try await preprocessImage(image)
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [RenderingServiceImpl] 设置基础图像完成")
        print("   - 尺寸: \(image.size)")
        print("   - 处理时间: \(String(format: "%.2f", duration * 1000))ms")
    }
    
    func getCurrentOutputImage() async -> UIImage? {
        guard let base = baseImage else { return nil }
        
        // 如果没有活跃效果，直接返回原图
        if !(await hasActiveEffects()) {
            return base
        }
        
        // 应用当前参数并渲染
        return await renderImageWithCurrentParameters(base)
    }
    
    // MARK: - 参数更新
    
    func updateParameters(_ parameters: FilterParameters) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        currentParameters = parameters
        
        // 清理参数相关缓存
        clearParameterSpecificCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [RenderingServiceImpl] 更新渲染参数完成")
        print("   - 曝光: \(parameters.exposure)")
        print("   - 对比度: \(parameters.contrast)")
        print("   - 饱和度: \(parameters.saturation)")
    }
    
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 暂停实时渲染
        let previousMode = renderingMode
        renderingMode = .preview
        
        // 执行批量更新
        updates()
        
        // 恢复渲染模式
        renderingMode = previousMode
        
        // 清理缓存
        clearParameterSpecificCache()
        
        let duration = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceStats(duration: duration)
        
        print("🎨 [RenderingServiceImpl] 批量参数更新完成")
    }
    
    // MARK: - 渲染模式
    
    func setRenderingMode(_ mode: RenderingMode) async throws {
        renderingMode = mode
        
        // 根据模式调整渲染质量
        switch mode {
        case .realtime:
            try await setRenderingQuality(.standard)
        case .highQuality:
            try await setRenderingQuality(.high)
        case .preview:
            try await setRenderingQuality(.low)
        case .lightroom:
            try await setRenderingQuality(.standard)
        case .vsco:
            try await setRenderingQuality(.standard)
        }
        
        print("🎨 [RenderingServiceImpl] 设置渲染模式: \(mode.displayName)")
    }
    
    func getCurrentRenderingMode() async -> RenderingMode {
        return renderingMode
    }
    
    // MARK: - LUT处理
    
    func applyLUT(lutPath: String?, intensity: Float) async throws {
        lutIntensity = max(0.0, min(1.0, intensity))
        
        if let path = lutPath {
            currentLUTTexture = try await loadLUTTexture(from: path)
            print("🎨 [RenderingServiceImpl] 应用LUT: \(path), 强度: \(intensity)")
        } else {
            currentLUTTexture = nil
            print("🎨 [RenderingServiceImpl] 清除LUT")
        }
        
        // 清理LUT相关缓存
        clearLUTSpecificCache()
    }
    
    func clearLUT() async throws {
        currentLUTTexture = nil
        lutIntensity = 1.0
        clearLUTSpecificCache()
        print("🎨 [RenderingServiceImpl] 清除LUT完成")
    }
    
    // MARK: - 图像输出
    
    func getRealtimePreview() async -> UIImage? {
        guard let base = baseImage else { return nil }
        
        // 实时预览使用低质量快速渲染
        let previousQuality = renderingQuality
        renderingQuality = .low
        
        let result = await renderImageWithCurrentParameters(base)
        
        renderingQuality = previousQuality
        return result
    }
    
    func getFinalOutputImage(highQuality: Bool) async -> UIImage? {
        guard let base = baseImage else { return nil }
        
        // 最终输出使用高质量渲染
        let previousQuality = renderingQuality
        renderingQuality = highQuality ? .ultra : .high
        
        let result = await renderImageWithCurrentParameters(base)
        
        renderingQuality = previousQuality
        return result
    }
    
    func getCurrentDisplayImage() async -> UIImage? {
        return await getCurrentOutputImage()
    }
    
    // MARK: - 渲染状态
    
    func isRendering() async -> Bool {
        return isCurrentlyRendering
    }
    
    func hasActiveEffects() async -> Bool {
        return currentParameters.hasActiveAdjustments() || currentLUTTexture != nil
    }
    
    // MARK: - 性能监控
    
    func getRenderingPerformanceStats() async -> (averageTime: TimeInterval, frameCount: Int, lastFrameTime: TimeInterval) {
        let averageTime = frameCount > 0 ? totalRenderTime / Double(frameCount) : 0
        return (averageTime, frameCount, lastFrameTime)
    }
    
    func resetPerformanceStats() async {
        frameCount = 0
        totalRenderTime = 0
        lastFrameTime = 0
        print("🎨 [RenderingServiceImpl] 性能统计已重置")
    }
    
    // MARK: - 渲染质量
    
    func setRenderingQuality(_ quality: RenderingQuality) async throws {
        renderingQuality = quality
        
        // 根据质量调整渲染参数
        updateRenderingParametersForQuality(quality)
        
        print("🎨 [RenderingServiceImpl] 设置渲染质量: \(quality.displayName)")
    }
    
    func getCurrentRenderingQuality() async -> RenderingQuality {
        return renderingQuality
    }
    
    // MARK: - 内存管理
    
    func clearRenderingCache() async {
        imageCache.removeAll()
        textureCache_internal.removeAll()
        CVMetalTextureCacheFlush(textureCache, 0)
        memoryUsage = 0
        print("🎨 [RenderingServiceImpl] 渲染缓存已清理")
    }
    
    func getMemoryUsage() async -> Int {
        return memoryUsage
    }
    
    // MARK: - 错误处理
    
    func getLastRenderingError() async -> Error? {
        return lastError
    }
    
    func clearRenderingError() async {
        lastError = nil
    }
    
    // MARK: - 私有方法 - Metal渲染管线
    
    private func setupRenderingPipeline() throws {
        // 尝试创建Metal库
        guard let library = metalDevice.makeDefaultLibrary() else {
            print("⚠️ [RenderingServiceImpl] 无法创建Metal库，使用Core Image后备方案")
            return // 不抛出错误，使用Core Image作为后备
        }
        
        // 尝试设置渲染管线（可选，如果着色器不存在则跳过）
        if let vertexFunction = library.makeFunction(name: "vertex_main"),
           let fragmentFunction = library.makeFunction(name: "fragment_main") {
            
            let renderDescriptor = MTLRenderPipelineDescriptor()
            renderDescriptor.vertexFunction = vertexFunction
            renderDescriptor.fragmentFunction = fragmentFunction
            renderDescriptor.colorAttachments[0].pixelFormat = .bgra8Unorm
            
            do {
                renderPipelineState = try metalDevice.makeRenderPipelineState(descriptor: renderDescriptor)
                print("✅ [RenderingServiceImpl] Metal渲染管线创建成功")
            } catch {
                print("⚠️ [RenderingServiceImpl] 渲染管线创建失败: \(error.localizedDescription)")
                print("   使用Core Image后备方案")
            }
        } else {
            print("⚠️ [RenderingServiceImpl] Metal着色器函数不存在，使用Core Image后备方案")
        }
        
        // 尝试设置计算管线（可选，如果着色器不存在则跳过）
        if let computeFunction = library.makeFunction(name: "compute_filter") {
            do {
                computePipelineState = try metalDevice.makeComputePipelineState(function: computeFunction)
                print("✅ [RenderingServiceImpl] Metal计算管线创建成功")
            } catch {
                print("⚠️ [RenderingServiceImpl] 计算管线创建失败: \(error.localizedDescription)")
                print("   使用Core Image后备方案")
            }
        } else {
            print("⚠️ [RenderingServiceImpl] Metal计算着色器函数不存在，使用Core Image后备方案")
        }
    }
    
    private func preprocessImage(_ image: UIImage) async throws {
        // 预处理图像，创建必要的纹理和缓存
        let cacheKey = "base_\(image.size.width)x\(image.size.height)"
        
        if imageCache[cacheKey] == nil {
            imageCache[cacheKey] = image
            updateMemoryUsage()
        }
    }
    
    private func renderImageWithCurrentParameters(_ image: UIImage) async -> UIImage? {
        isCurrentlyRendering = true
        defer { isCurrentlyRendering = false }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        do {
            let result: UIImage?
            
            // 根据可用的渲染管线选择渲染方法
            if renderPipelineState != nil || computePipelineState != nil {
                result = try await renderWithMetal(image)
            } else {
                result = try await renderWithCoreImage(image)
            }
            
            let duration = CFAbsoluteTimeGetCurrent() - startTime
            updatePerformanceStats(duration: duration)
            
            return result
            
        } catch {
            lastError = error
            print("❌ [RenderingServiceImpl] 渲染失败: \(error)")
            return image // 返回原图作为后备
        }
    }
    
    private func renderWithMetal(_ image: UIImage) async throws -> UIImage? {
        // Metal渲染实现
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            throw RenderingError.renderingFailed("无法创建命令缓冲区")
        }
        
        // 创建输入纹理
        let inputTexture = try createTexture(from: image)
        
        // 创建输出纹理
        let outputTexture = try createOutputTexture(size: image.size)
        
        // 应用滤镜效果
        try applyFiltersWithMetal(
            commandBuffer: commandBuffer,
            inputTexture: inputTexture,
            outputTexture: outputTexture
        )
        
        // 提交命令并等待完成
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        // 从纹理创建UIImage
        return try createImage(from: outputTexture)
    }
    
    private func renderWithCoreImage(_ image: UIImage) async throws -> UIImage? {
        guard let ciImage = CIImage(image: image) else {
            throw RenderingError.renderingFailed("无法创建CIImage")
        }
        
        var processedImage = ciImage
        
        // 应用滤镜参数
        processedImage = try applyFiltersWithCoreImage(to: processedImage)
        
        // 应用LUT
        if let lutTexture = currentLUTTexture {
            processedImage = try applyLUTWithCoreImage(to: processedImage, lutTexture: lutTexture)
        }
        
        // 渲染最终图像
        let extent = processedImage.extent
        guard let cgImage = ciContext.createCGImage(processedImage, from: extent) else {
            throw RenderingError.renderingFailed("无法创建CGImage")
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    private func applyFiltersWithMetal(
        commandBuffer: MTLCommandBuffer,
        inputTexture: MTLTexture,
        outputTexture: MTLTexture
    ) throws {
        // Metal滤镜应用实现
        if let computePipeline = computePipelineState,
           let computeEncoder = commandBuffer.makeComputeCommandEncoder() {
            
            computeEncoder.setComputePipelineState(computePipeline)
            computeEncoder.setTexture(inputTexture, index: 0)
            computeEncoder.setTexture(outputTexture, index: 1)
            
            // 设置滤镜参数
            var parameters = FilterParametersBuffer(
                exposure: currentParameters.exposure,
                contrast: currentParameters.contrast,
                brightness: currentParameters.brightness,
                saturation: currentParameters.saturation,
                temperature: currentParameters.temperature,
                tint: currentParameters.tint
            )
            
            computeEncoder.setBytes(&parameters, length: MemoryLayout<FilterParametersBuffer>.size, index: 0)
            
            // 计算线程组
            let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
            let threadgroups = MTLSize(
                width: (inputTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
                height: (inputTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
                depth: 1
            )
            
            computeEncoder.dispatchThreadgroups(threadgroups, threadsPerThreadgroup: threadgroupSize)
            computeEncoder.endEncoding()
        }
    }
    
    private func applyFiltersWithCoreImage(to image: CIImage) throws -> CIImage {
        var processedImage = image
        
        // 曝光调整
        if currentParameters.exposure != 0.0 {
            processedImage = processedImage.applyingFilter("CIExposureAdjust", parameters: [
                "inputEV": currentParameters.exposure
            ])
        }
        
        // 对比度调整
        if currentParameters.contrast != 0.0 {
            processedImage = processedImage.applyingFilter("CIColorControls", parameters: [
                "inputContrast": 1.0 + currentParameters.contrast / 100.0
            ])
        }
        
        // 亮度调整
        if currentParameters.brightness != 0.0 {
            processedImage = processedImage.applyingFilter("CIColorControls", parameters: [
                "inputBrightness": currentParameters.brightness / 100.0
            ])
        }
        
        // 饱和度调整
        if currentParameters.saturation != 0.0 {
            processedImage = processedImage.applyingFilter("CIColorControls", parameters: [
                "inputSaturation": 1.0 + currentParameters.saturation / 100.0
            ])
        }
        
        // 色温调整
        if currentParameters.temperature != 0.0 || currentParameters.tint != 0.0 {
            processedImage = processedImage.applyingFilter("CITemperatureAndTint", parameters: [
                "inputNeutral": CIVector(x: 6500 + CGFloat(currentParameters.temperature * 100), y: CGFloat(currentParameters.tint)),
                "inputTargetNeutral": CIVector(x: 6500, y: 0)
            ])
        }
        
        return processedImage
    }
    
    private func applyLUTWithCoreImage(to image: CIImage, lutTexture: MTLTexture) throws -> CIImage {
        // LUT应用实现（简化版）
        return image.applyingFilter("CIColorCube", parameters: [
            "inputCubeDimension": 64,
            "inputCubeData": Data() // 实际实现需要从LUT纹理提取数据
        ])
    }
    
    // MARK: - 纹理管理
    
    private func createTexture(from image: UIImage) throws -> MTLTexture {
        guard let cgImage = image.cgImage else {
            throw RenderingError.textureCreationFailed("无法获取CGImage")
        }
        
        let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .bgra8Unorm,
            width: cgImage.width,
            height: cgImage.height,
            mipmapped: false
        )
        textureDescriptor.usage = [.shaderRead, .shaderWrite]
        
        guard let texture = metalDevice.makeTexture(descriptor: textureDescriptor) else {
            throw RenderingError.textureCreationFailed("无法创建Metal纹理")
        }
        
        // 将CGImage数据复制到纹理
        let region = MTLRegionMake2D(0, 0, cgImage.width, cgImage.height)
        let bytesPerRow = cgImage.bytesPerRow
        
        if let dataProvider = cgImage.dataProvider,
           let data = dataProvider.data {
            let bytes = CFDataGetBytePtr(data)
            texture.replace(region: region, mipmapLevel: 0, withBytes: bytes!, bytesPerRow: bytesPerRow)
        }
        
        return texture
    }
    
    private func createOutputTexture(size: CGSize) throws -> MTLTexture {
        let textureDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .bgra8Unorm,
            width: Int(size.width),
            height: Int(size.height),
            mipmapped: false
        )
        textureDescriptor.usage = [.shaderRead, .shaderWrite, .renderTarget]
        
        guard let texture = metalDevice.makeTexture(descriptor: textureDescriptor) else {
            throw RenderingError.textureCreationFailed("无法创建输出纹理")
        }
        
        return texture
    }
    
    private func createImage(from texture: MTLTexture) throws -> UIImage {
        // 从Metal纹理创建UIImage的实现
        let bytesPerPixel = 4
        let bytesPerRow = texture.width * bytesPerPixel
        let bufferSize = bytesPerRow * texture.height
        
        let buffer = UnsafeMutableRawPointer.allocate(byteCount: bufferSize, alignment: 1)
        defer { buffer.deallocate() }
        
        texture.getBytes(buffer, bytesPerRow: bytesPerRow, from: MTLRegionMake2D(0, 0, texture.width, texture.height), mipmapLevel: 0)
        
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)
        
        guard let context = CGContext(
            data: buffer,
            width: texture.width,
            height: texture.height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: bitmapInfo.rawValue
        ) else {
            throw RenderingError.imageCreationFailed("无法创建CGContext")
        }
        
        guard let cgImage = context.makeImage() else {
            throw RenderingError.imageCreationFailed("无法创建CGImage")
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    private func loadLUTTexture(from path: String) async throws -> MTLTexture {
        // LUT纹理加载实现
        guard let image = UIImage(contentsOfFile: path) else {
            throw RenderingError.lutLoadingFailed("无法加载LUT文件: \(path)")
        }
        
        return try createTexture(from: image)
    }
    
    // MARK: - 缓存管理
    
    private func clearImageSpecificCache() {
        let keysToRemove = imageCache.keys.filter { $0.hasPrefix("base_") }
        keysToRemove.forEach { imageCache.removeValue(forKey: $0) }
        updateMemoryUsage()
    }
    
    private func clearParameterSpecificCache() {
        let keysToRemove = imageCache.keys.filter { $0.hasPrefix("param_") }
        keysToRemove.forEach { imageCache.removeValue(forKey: $0) }
        updateMemoryUsage()
    }
    
    private func clearLUTSpecificCache() {
        let keysToRemove = imageCache.keys.filter { $0.hasPrefix("lut_") }
        keysToRemove.forEach { imageCache.removeValue(forKey: $0) }
        updateMemoryUsage()
    }
    
    private func updateMemoryUsage() {
        memoryUsage = imageCache.values.reduce(0) { total, image in
            let imageSize = Int(image.size.width * image.size.height * 4) // RGBA
            return total + imageSize
        }
        
        // 如果缓存过大，清理最旧的条目
        if imageCache.count > maxCacheSize {
            let keysToRemove = Array(imageCache.keys.prefix(imageCache.count - maxCacheSize))
            keysToRemove.forEach { imageCache.removeValue(forKey: $0) }
        }
    }
    
    private func updateRenderingParametersForQuality(_ quality: RenderingQuality) {
        // 根据质量调整渲染参数
        switch quality {
        case .low:
            // 低质量：快速渲染
            break
        case .standard:
            // 标准质量：平衡性能和质量
            break
        case .high:
            // 高质量：更好的质量
            break
        case .ultra:
            // 超高质量：最佳质量
            break
        }
    }
    
    private func updatePerformanceStats(duration: TimeInterval) {
        frameCount += 1
        totalRenderTime += duration
        lastFrameTime = duration
    }
}

// MARK: - 支持结构和枚举


/// 滤镜参数缓冲区（用于Metal着色器）
struct FilterParametersBuffer {
    let exposure: Float
    let contrast: Float
    let brightness: Float
    let saturation: Float
    let temperature: Float
    let tint: Float
}

/// 渲染错误类型
enum RenderingError: LocalizedError {
    case metalInitializationFailed(String)
    case pipelineCreationFailed(String)
    case textureCreationFailed(String)
    case renderingFailed(String)
    case imageCreationFailed(String)
    case lutLoadingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .metalInitializationFailed(let message):
            return "Metal初始化失败: \(message)"
        case .pipelineCreationFailed(let message):
            return "渲染管线创建失败: \(message)"
        case .textureCreationFailed(let message):
            return "纹理创建失败: \(message)"
        case .renderingFailed(let message):
            return "渲染失败: \(message)"
        case .imageCreationFailed(let message):
            return "图像创建失败: \(message)"
        case .lutLoadingFailed(let message):
            return "LUT加载失败: \(message)"
        }
    }
}
