import Foundation
import SwiftUI

/// 计时器服务 - 提供统一的计时器管理功能
class TimerService {
    // MARK: - 单例
    static let shared = TimerService()
    
    // MARK: - 依赖
    private let timerManager = TimerManager()
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 计时器重置方法
    
    /// 重置参数面板计时器
    /// - Parameters:
    ///   - isExpanded: 面板是否展开
    ///   - callback: 计时器触发回调
    func resetParameterTimer(isExpanded: Bool, callback: @escaping () -> Void) {
        print("⏰ 参数面板：准备重置计时器，当前展开状态=\(isExpanded)")
        if isExpanded {
            timerManager.resetTimer(for: .parameter, isVisible: true, hideAction: callback)
        } else {
            print("⏰ 参数面板：面板未展开，跳过重置")
        }
    }
    
    /// 重置左侧按钮计时器
    /// - Parameters:
    ///   - isExpanded: 按钮是否展开
    ///   - callback: 计时器触发回调
    func resetLeftButtonTimer(isExpanded: Bool, callback: @escaping () -> Void) {
        print("⏰ 3×3按钮：准备重置计时器，当前展开状态=\(isExpanded)")
        if isExpanded {
            timerManager.resetTimer(for: .leftButton, isVisible: true, hideAction: callback)
        } else {
            print("⏰ 3×3按钮：按钮未展开，跳过重置")
        }
    }
    
    /// 重置右侧按钮计时器
    /// - Parameters:
    ///   - isExpanded: 按钮是否展开
    ///   - callback: 计时器触发回调
    func resetRightButtonTimer(isExpanded: Bool, callback: @escaping () -> Void) {
        print("⏰ A/M按钮：准备重置计时器，当前展开状态=\(isExpanded)")
        if isExpanded {
            timerManager.resetTimer(for: .rightButton, isVisible: true, hideAction: callback)
        } else {
            print("⏰ A/M按钮：按钮未展开，跳过重置")
        }
    }
    
    // MARK: - 计时器启动方法
    
    /// 启动参数面板计时器
    /// - Parameters:
    ///   - callback: 计时器触发回调
    func startParameterTimer(callback: @escaping () -> Void) {
        timerManager.startTimer(for: .parameter, hideAction: callback)
    }
    
    /// 启动左侧按钮计时器
    /// - Parameters:
    ///   - callback: 计时器触发回调
    func startLeftButtonTimer(callback: @escaping () -> Void) {
        timerManager.startTimer(for: .leftButton, hideAction: callback)
    }
    
    /// 启动右侧按钮计时器
    /// - Parameters:
    ///   - callback: 计时器触发回调
    func startRightButtonTimer(callback: @escaping () -> Void) {
        timerManager.startTimer(for: .rightButton, hideAction: callback)
    }
    
    // MARK: - 计时器取消方法
    
    /// 取消参数面板计时器
    func cancelParameterTimer() {
        timerManager.cancelTimer(for: .parameter)
    }
    
    /// 取消左侧按钮计时器
    func cancelLeftButtonTimer() {
        timerManager.cancelTimer(for: .leftButton)
    }
    
    /// 取消右侧按钮计时器
    func cancelRightButtonTimer() {
        timerManager.cancelTimer(for: .rightButton)
    }
} 