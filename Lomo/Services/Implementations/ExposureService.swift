import Foundation
import AVFoundation

/// 曝光服务实现类
class ExposureService: ExposureServiceProtocol {
    // MARK: - 私有属性

    /// 会话
    private let session: AVCaptureSession
    
    // MARK: - 状态回调
    
    var onExposureChanged: ((Double) -> Void)?
    var onISOChanged: ((Double) -> Void)?
    var onShutterSpeedChanged: ((Double) -> Void)?
    var onExposureRangeChanged: ((ClosedRange<Double>) -> Void)?
    var onISORangeChanged: ((ClosedRange<Double>) -> Void)?
    var onShutterSpeedRangeChanged: ((ClosedRange<Double>) -> Void)?
    
    // 默认范围
    private var exposureRange: ClosedRange<Double> = -3.0...3.0
    private var isoRange: ClosedRange<Double> = 0.0...0.0
    private var shutterSpeedRange: ClosedRange<Double> = 1/4000.0...1.0
    
    // MARK: - 初始化
    
    /// 初始化曝光服务
    /// - Parameter session: AVCaptureSession实例
    init(session: AVCaptureSession) {
        self.session = session
    }

    /// 初始化曝光服务（向后兼容）
    /// - Parameter cameraController: 相机控制器实例（已废弃）
    init(cameraController: Any) {
        self.session = SessionManager.shared.session
    }

    // MARK: - 私有方法

    /// 获取当前设备
    private func getCurrentDevice() -> AVCaptureDevice? {
        // 1. 尝试从 session 获取当前设备
        if let input = session.inputs.first as? AVCaptureDeviceInput {
            return input.device
        }

        // 2. 如果 session 中没有设备，尝试获取默认后置摄像头
        if let defaultDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) {
            return defaultDevice
        }

        return nil
    }
    
    // MARK: - ExposureServiceProtocol 实现
    
    func setExposureValue(_ value: Double) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()

            // 设置曝光模式为自定义
            if device.isExposureModeSupported(.custom) {
                device.exposureMode = .custom
            }

            // 设置曝光补偿值
            let targetBias = Float(value)
            device.setExposureTargetBias(targetBias) { _ in
                // 曝光调整完成后的回调
            }

            device.unlockForConfiguration()
            onExposureChanged?(value)
        } catch {
            print("曝光设置错误: \(error.localizedDescription)")
        }
    }

    func setISOValue(_ value: Double) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()

            // 检查设备是否支持ISO调整
            if device.isExposureModeSupported(.custom) {
                // 确保ISO在设备支持范围内
                let targetISO = max(device.activeFormat.minISO,
                                   min(Float(value), device.activeFormat.maxISO))

                // 设置ISO
                device.setExposureModeCustom(
                    duration: device.exposureDuration,
                    iso: targetISO,
                    completionHandler: nil
                )

                print("📱 设置ISO: \(targetISO) (设备支持范围: \(device.activeFormat.minISO)-\(device.activeFormat.maxISO))")
            } else {
                print("⚠️ 设备不支持自定义ISO设置")
            }

            device.unlockForConfiguration()
            onISOChanged?(value)
        } catch {
            print("⚠️ 无法设置ISO: \(error.localizedDescription)")
        }
    }

    func setShutterSpeed(_ value: Double) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()

            // 检查设备是否支持自定义曝光模式
            if device.isExposureModeSupported(.custom) {
                // 确保快门速度在设备支持范围内
                let minSpeed = device.activeFormat.minExposureDuration.seconds
                let maxSpeed = device.activeFormat.maxExposureDuration.seconds
                let clampedSpeed = max(minSpeed, min(value, maxSpeed))

                // 设置快门速度（曝光时间）
                device.setExposureModeCustom(
                    duration: CMTime(seconds: clampedSpeed, preferredTimescale: 1000000),
                    iso: device.iso,
                    completionHandler: nil
                )

                print("📱 设置快门速度: \(clampedSpeed)秒 (设备支持范围: \(minSpeed)-\(maxSpeed)秒)")
            } else {
                print("⚠️ 设备不支持自定义快门速度设置")
            }

            device.unlockForConfiguration()
            onShutterSpeedChanged?(value)
        } catch {
            print("⚠️ 无法设置快门速度: \(error.localizedDescription)")
        }
    }

    func resetExposure() {
        // 重置为自动曝光模式
        if let device = getCurrentDevice() {
            do {
                try device.lockForConfiguration()
                device.exposureMode = .continuousAutoExposure
                device.unlockForConfiguration()
            } catch {
                print("📸 Error resetting exposure: \(error)")
            }
        }
    }
    
    func getExposureRange() -> ClosedRange<Double> {
        return exposureRange
    }
    
    func getISORange() -> ClosedRange<Double> {
        return isoRange
    }
    
    func getShutterSpeedRange() -> ClosedRange<Double> {
        return shutterSpeedRange
    }
} 