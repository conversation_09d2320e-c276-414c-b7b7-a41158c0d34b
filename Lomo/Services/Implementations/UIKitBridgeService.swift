import Foundation
import UIKit
import SwiftUI

/// 提供UIKit相关功能的桥接服务实现，封装对UIKit的直接依赖
class UIKitBridgeService: UIKitBridgeServiceProtocol {
    /// 单例实例
    static let shared = UIKitBridgeService()
    
    private init() {}
    
    /// 获取系统颜色
    func getSystemColor(name: String) -> Any {
        switch name {
        case "systemGray6":
            return UIColor.systemGray6
        case "systemGray5":
            return UIColor.systemGray5
        case "systemBlue":
            return UIColor.systemBlue
        case "systemRed":
            return UIColor.systemRed
        case "systemGreen":
            return UIColor.systemGreen
        case "systemYellow":
            return UIColor.systemYellow
        case "systemPink":
            return UIColor.systemPink
        case "systemPurple":
            return UIColor.systemPurple
        case "black":
            return UIColor.black
        case "white":
            return UIColor.white
        case "clear":
            return UIColor.clear
        default:
            return UIColor.systemGray
        }
    }
    
    /// 获取图片
    func getImage(named: String) -> Any? {
        return UIImage(named: named)
    }
    
    /// 从数据创建图片
    func getImage(from data: Data) -> Any? {
        return UIImage(data: data)
    }
    
    /// 获取动画选项
    func getAnimationOptions(curve: String) -> UInt {
        switch curve {
        case "easeIn":
            return UIView.AnimationOptions.curveEaseIn.rawValue
        case "easeOut":
            return UIView.AnimationOptions.curveEaseOut.rawValue
        case "easeInOut":
            return UIView.AnimationOptions.curveEaseInOut.rawValue
        case "linear":
            return UIView.AnimationOptions.curveLinear.rawValue
        default:
            return UIView.AnimationOptions.curveEaseInOut.rawValue
        }
    }
    
    /// 执行动画
    func animate(withDuration duration: TimeInterval, delay: TimeInterval, options: UInt, animations: @escaping () -> Void, completion: ((Bool) -> Void)?) {
        UIView.animate(withDuration: duration, delay: delay, options: UIView.AnimationOptions(rawValue: options), animations: animations, completion: completion)
    }
    
    /// 获取字体
    func getFont(name: String, size: CGFloat, weight: String? = nil) -> Any {
        if let weight = weight {
            switch weight {
            case "ultraLight":
                return UIFont.systemFont(ofSize: size, weight: .ultraLight)
            case "thin":
                return UIFont.systemFont(ofSize: size, weight: .thin)
            case "light":
                return UIFont.systemFont(ofSize: size, weight: .light)
            case "regular":
                return UIFont.systemFont(ofSize: size, weight: .regular)
            case "medium":
                return UIFont.systemFont(ofSize: size, weight: .medium)
            case "semibold":
                return UIFont.systemFont(ofSize: size, weight: .semibold)
            case "bold":
                return UIFont.systemFont(ofSize: size, weight: .bold)
            case "heavy":
                return UIFont.systemFont(ofSize: size, weight: .heavy)
            case "black":
                return UIFont.systemFont(ofSize: size, weight: .black)
            default:
                return UIFont.systemFont(ofSize: size)
            }
        } else {
            return UIFont.systemFont(ofSize: size)
        }
    }
} 