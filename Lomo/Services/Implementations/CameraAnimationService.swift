import SwiftUI

class CameraAnimationService {
    weak var viewModel: CameraViewModel?
    
    init(viewModel: CameraViewModel?) {
        self.viewModel = viewModel
    }
    
    // MARK: - 动画参数
    func getSpringAnimation(response: Double = AnimationConstants.duration, 
                          dampingFraction: Double = AnimationConstants.dampingFraction) -> Animation {
        .spring(response: response, dampingFraction: dampingFraction)
    }
    
    func getQuickSpringAnimation() -> Animation {
        .spring(response: AnimationConstants.quickDuration, 
                dampingFraction: AnimationConstants.quickDampingFraction)
    }
    
    // MARK: - 动画状态判断
    func shouldAnimateVideoMode() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode && viewModel.state.isRecording
    }
    
    func shouldAnimateParameterExpanded() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isParameterExpanded
    }
    
    func shouldAnimateLeftButtonExpanded() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isLeftButtonExpanded
    }
    
    func shouldAnimateRightButtonExpanded() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isRightButtonExpanded
    }
    
    // MARK: - 动画触发逻辑
    func animateWithTransaction(_ action: @escaping () -> Void) {
        withTransaction(Transaction(animation: nil)) {
            action()
        }
    }
    
    func animateWithSpring(_ action: @escaping () -> Void) {
        withAnimation(getSpringAnimation()) {
            action()
        }
    }
    
    func animateWithQuickSpring(_ action: @escaping () -> Void) {
        withAnimation(getQuickSpringAnimation()) {
            action()
        }
    }
    
    // MARK: - 特定动画逻辑
    func getSymbolBounceAnimation(value: Bool) -> Animation {
        .spring(response: AnimationConstants.symbolBounceResponse, 
                dampingFraction: AnimationConstants.symbolBounceDamping)
    }
    
    func getRotationAnimation() -> Animation {
        .easeInOut(duration: 0.5)
    }
    
    // MARK: - 具体UI动画方法
    
    /// 执行参数面板展开动画
    /// - Parameter action: 动画执行的操作
    func animateParameterExpansion(action: @escaping () -> Void) {
        withAnimation(getQuickSpringAnimation()) {
            action()
        }
    }
    
    /// 执行按钮状态切换动画
    /// - Parameter action: 动画执行的操作
    func animateButtonToggle(action: @escaping () -> Void) {
        withAnimation(getSpringAnimation()) {
            action()
        }
    }
    
    /// 执行选项切换动画
    /// - Parameter action: 动画执行的操作
    func animateOptionToggle(action: @escaping () -> Void) {
        withAnimation(getQuickSpringAnimation()) {
            action()
        }
    }
    
    /// 执行面板收起动画
    /// - Parameter action: 动画执行的操作
    func animateCollapse(action: @escaping () -> Void) {
        withAnimation(getQuickSpringAnimation()) {
            action()
        }
    }
} 