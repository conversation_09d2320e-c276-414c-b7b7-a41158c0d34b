import Foundation
import AVFoundation
import UIKit
import Photos

/// 相机服务实现类
class CameraService: NSObject, CameraServiceProtocol {
    // MARK: - 私有属性

    /// 会话
    private let session: AVCaptureSession

    // 当前状态
    private var currentPosition: AVCaptureDevice.Position = .back
    private var isFlashOn: Bool = false
    private var isRotating: Bool = false
    
    // MARK: - 状态回调
    
    var onDeviceTypeChanged: ((CameraType) -> Void)?
    var onPositionChanged: ((AVCaptureDevice.Position) -> Void)?
    var onCameraReady: (() -> Void)?
    var onAvailableLensesChanged: (([String]) -> Void)?
    var onFlashStateChanged: ((Bool) -> Void)?
    var onRotatingStateChanged: ((Bool) -> Void)?
    var onCameraSetupComplete: (() -> Void)?
    var onZoomFactorChanged: ((CGFloat) -> Void)?
    var onZoomFactorsChanged: ((CGFloat, CGFloat, CGFloat, Bool) -> Void)?
    var onShutterRangeChanged: ((Double, Double) -> Void)?
    var onISORangeChanged: ((Float, Float) -> Void)?
    
    // MARK: - 初始化

    /// 初始化相机服务
    /// - Parameter session: AVCaptureSession实例
    init(session: AVCaptureSession) {
        self.session = session
        super.init()
    }

    /// 初始化相机服务（向后兼容）
    /// - Parameter cameraController: 相机控制器实例（已废弃）
    init(cameraController: Any) {
        // 为了保持向后兼容，从SessionManager获取session
        self.session = SessionManager.shared.session
        super.init()
    }
    
    // MARK: - 私有方法

    /// 权限检查和设置
    private func checkPermissionsAndSetup() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            setupCamera()
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
                if granted {
                    DispatchQueue.main.async {
                        self?.setupCamera()
                    }
                }
            }
        default:
            break
        }
    }

    private func setupCamera() {
        guard let device = AVCaptureDevice.camera(position: currentPosition) else {
            print("⚠️ 无法获取摄像头设备，可能在模拟器环境中运行")
            // 通知状态变化
            onDeviceTypeChanged?(.single)
            onZoomFactorsChanged?(1.0, 1.0, 1.0, false)
            return
        }

        do {
            let input = try AVCaptureDeviceInput(device: device)
            if session.canAddInput(input) {
                session.addInput(input)
            }

            let output = AVCapturePhotoOutput()
            if session.canAddOutput(output) {
                session.addOutput(output)
            }

            // 初始化设备类型和变焦相关参数
            let deviceType = CameraLensManager.shared.getDeviceType()
            let maxZoomFactor = CGFloat(device.maxZoomFactor ?? 1.0)
            let minZoomFactor = 1.0
            let isZoomSupported = device.deviceType != .builtInWideAngleCamera

            // 通知状态变化
            onDeviceTypeChanged?(deviceType)
            onZoomFactorsChanged?(maxZoomFactor, minZoomFactor, 1.0, isZoomSupported)

            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                self?.session.startRunning()
                // 在相机设置完成后检测镜头
                DispatchQueue.main.async {
                    self?.detectCameraLenses()
                    // 添加：通知相机设置完成
                    self?.onCameraSetupComplete?()
                }
            }

            // 在设备配置完成后调用快门范围回调
            let minShutter = device.activeFormat.minExposureDuration.seconds
            let maxShutter = device.activeFormat.maxExposureDuration.seconds
            onShutterRangeChanged?(minShutter, maxShutter)

        } catch {
            print("相机设置错误: \(error.localizedDescription)")
        }
    }

    private func detectCameraLenses() {
        // 使用CameraLensManager获取支持的镜头列表
        let availableLenses = CameraLensManager.shared.getSupportedLenses()
        onAvailableLensesChanged?(availableLenses)
    }

    func toggleFlash() {
        isFlashOn.toggle()
        onFlashStateChanged?(isFlashOn)

        guard let device = AVCaptureDevice.camera(position: .back) else { return }

        do {
            try device.lockForConfiguration()
            device.torchMode = isFlashOn ? .on : .off
            device.unlockForConfiguration()
        } catch {
            print("闪光灯设置错误: \(error.localizedDescription)")
        }
    }
    
    // MARK: - CameraServiceProtocol 实现

    func setup() {
        checkPermissionsAndSetup()
        updateDeviceISORangeIfNeeded()  // 新增：检测并更新ISO范围
    }

    func switchCamera() {
        // 如果已经在旋转，直接返回
        guard !isRotating else { return }

        // 设置动画状态
        isRotating = true
        onRotatingStateChanged?(true)

        session.beginConfiguration()

        // 移除当前输入
        session.inputs.forEach { session.removeInput($0) }

        // 切换相机位置
        currentPosition = currentPosition == .back ? .front : .back
        onPositionChanged?(currentPosition)

        // 添加新输入
        if let device = AVCaptureDevice.camera(position: currentPosition),
           let input = try? AVCaptureDeviceInput(device: device),
           session.canAddInput(input) {
            session.addInput(input)
        }

        session.commitConfiguration()

        // 更新ISO范围
        updateDeviceISORangeIfNeeded()

        // 延迟重置动画状态
        DispatchQueue.main.asyncAfter(deadline: .now() + UIConstants.cameraRotationResetDelay) {
            self.isRotating = false
            self.onRotatingStateChanged?(false)
        }
    }

    func capturePhoto() {
        guard let photoOutput = session.outputs.first as? AVCapturePhotoOutput else { return }
        let settings = AVCapturePhotoSettings()
        settings.flashMode = isFlashOn ? .on : .off
        photoOutput.capturePhoto(with: settings, delegate: self)
    }

    func getCurrentDevice() -> AVCaptureDevice? {
        // 1. 尝试从 session 获取当前设备
        if let input = session.inputs.first as? AVCaptureDeviceInput {
            print("📱 从 session 获取到设备：\(input.device.localizedName)")
            return input.device
        }

        // 2. 如果 session 中没有设备，尝试获取默认后置摄像头
        if let defaultDevice = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) {
            print("📱 使用默认后置摄像头：\(defaultDevice.localizedName)")
            return defaultDevice
        }

        // 3. 如果默认后置摄像头不可用，尝试获取任何可用的摄像头
        let discoverySession = AVCaptureDevice.DiscoverySession(
            deviceTypes: [.builtInWideAngleCamera, .builtInUltraWideCamera, .builtInTelephotoCamera],
            mediaType: .video,
            position: .back
        )

        if let firstDevice = discoverySession.devices.first {
            print("📱 使用可用的第一个摄像头：\(firstDevice.localizedName)")
            return firstDevice
        }

        // 4. 如果实在找不到任何设备（可能在模拟器上），返回 nil
        print("⚠️ 未找到任何可用摄像头，可能在模拟器环境中运行")
        return nil
    }
    
    // MARK: - 高级控制方法实现

    func setISO(_ iso: Float) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()

            // 检查设备是否支持ISO调整
            if device.isExposureModeSupported(.custom) {
                // 确保ISO在设备支持范围内
                let targetISO = max(device.activeFormat.minISO,
                                   min(iso, device.activeFormat.maxISO))

                // 设置ISO
                device.setExposureModeCustom(
                    duration: device.exposureDuration,
                    iso: targetISO,
                    completionHandler: nil
                )

                print("📱 设置ISO: \(targetISO) (设备支持范围: \(device.activeFormat.minISO)-\(device.activeFormat.maxISO))")
            } else {
                print("⚠️ 设备不支持自定义ISO设置")
            }

            device.unlockForConfiguration()
        } catch {
            print("⚠️ 无法设置ISO: \(error.localizedDescription)")
        }
    }

    func setExposure(_ value: Double) {
        guard let device = AVCaptureDevice.camera(position: currentPosition) else { return }

        do {
            try device.lockForConfiguration()

            // 设置曝光模式为自定义
            if device.isExposureModeSupported(.custom) {
                device.exposureMode = .custom
            }

            // 设置曝光补偿值
            let targetBias = Float(value)
            device.setExposureTargetBias(targetBias) { _ in
                // 曝光调整完成后的回调
            }

            device.unlockForConfiguration()
        } catch {
            print("曝光设置错误: \(error.localizedDescription)")
        }
    }

    func setExposureCompensation(_ value: Double) {
        setExposure(value)
    }

    func setShutterSpeed(_ value: Double) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()

            // 检查设备是否支持自定义曝光模式
            if device.isExposureModeSupported(.custom) {
                // 确保快门速度在设备支持范围内
                let minSpeed = device.activeFormat.minExposureDuration.seconds
                let maxSpeed = device.activeFormat.maxExposureDuration.seconds
                let clampedSpeed = max(minSpeed, min(value, maxSpeed))

                // 设置快门速度（曝光时间）
                device.setExposureModeCustom(
                    duration: CMTime(seconds: clampedSpeed, preferredTimescale: 1000000),
                    iso: device.iso,
                    completionHandler: nil
                )

                print("📱 设置快门速度: \(clampedSpeed)秒 (设备支持范围: \(minSpeed)-\(maxSpeed)秒)")
            } else {
                print("⚠️ 设备不支持自定义快门速度设置")
            }

            device.unlockForConfiguration()
        } catch {
            print("⚠️ 无法设置快门速度: \(error.localizedDescription)")
        }
    }

    func setFocus(_ value: Double) {
        guard let device = getCurrentDevice() else { return }

        do {
            try device.lockForConfiguration()
            if device.isFocusModeSupported(.locked) {
                device.focusMode = .locked
                device.setFocusModeLocked(lensPosition: Float(value))
            }
            device.unlockForConfiguration()
        } catch {
            print("对焦设置错误: \(error.localizedDescription)")
        }
    }

    func setTemperature(_ value: Double) {
        guard let device = AVCaptureDevice.camera(position: currentPosition) else { return }

        do {
            try device.lockForConfiguration()

            // 设置白平衡模式为自定义
            if device.isWhiteBalanceModeSupported(.locked) {
                device.whiteBalanceMode = .locked
            }

            // 将色温值转换为设备可接受的色温值
            let temperatureAndTint = AVCaptureDevice.WhiteBalanceTemperatureAndTintValues(
                temperature: Float(value),
                tint: 0.0  // 保持色调为中性
            )

            // 将色温和色调值转换为RGB增益值
            let gains = device.deviceWhiteBalanceGains(for: temperatureAndTint)

            // 确保RGB增益值在设备支持的范围内
            let normalizedGains = self.normalizeGains(gains, device: device)

            // 设置白平衡增益
            try device.setWhiteBalanceModeLocked(with: normalizedGains)

            device.unlockForConfiguration()
        } catch {
            print("色温设置错误: \(error.localizedDescription)")
        }
    }

    // MARK: - 辅助方法

    /// 辅助方法：确保RGB增益值在设备支持的范围内
    private func normalizeGains(_ gains: AVCaptureDevice.WhiteBalanceGains, device: AVCaptureDevice) -> AVCaptureDevice.WhiteBalanceGains {
        var normalizedGains = gains

        // 获取设备支持的最大增益值
        let maxGains = device.maxWhiteBalanceGain

        // 限制RGB值在1.0到maxGains之间
        normalizedGains.redGain = min(max(gains.redGain, 1.0), maxGains)
        normalizedGains.greenGain = min(max(gains.greenGain, 1.0), maxGains)
        normalizedGains.blueGain = min(max(gains.blueGain, 1.0), maxGains)

        return normalizedGains
    }

    /// 获取设备支持的ISO范围
    /// - Returns: (最小ISO, 最大ISO)
    func getDeviceSupportedISORange() -> (min: Float, max: Float) {
        guard let device = getCurrentDevice() else { return (50, 12800) }
        return (device.activeFormat.minISO, device.activeFormat.maxISO)
    }

    /// 获取当前ISO值
    /// - Returns: 当前ISO值
    func getCurrentISO() -> Float {
        guard let device = getCurrentDevice() else { return 100 }
        return device.iso
    }

    // 新增：当相机初始化或切换时更新ISO范围
    private func updateDeviceISORangeIfNeeded() {
        let (minISO, maxISO) = getDeviceSupportedISORange()
        DispatchQueue.main.async {
            self.onISORangeChanged?(minISO, maxISO)
            print("📱 设备支持的ISO范围: \(minISO)-\(maxISO)")
        }
    }
}

// MARK: - AVCapturePhotoCaptureDelegate
extension CameraService: AVCapturePhotoCaptureDelegate {
    func photoOutput(_ output: AVCapturePhotoOutput,
                    didFinishProcessingPhoto photo: AVCapturePhoto,
                    error: Error?) {
        if let error = error {
            print("照片捕获错误: \(error.localizedDescription)")
            return
        }

        guard let imageData = photo.fileDataRepresentation() else { return }

        if let image = UIImage(data: imageData) {
            // 保存到系统相册
            PHPhotoLibrary.shared().performChanges({
                // 创建照片请求
                let creationRequest = PHAssetCreationRequest.forAsset()

                // 添加图片数据
                creationRequest.addResource(with: .photo, data: imageData, options: nil)

                // 设置元数据，标记这是由应用拍摄的照片
                if let appBundleIdentifier = Bundle.main.bundleIdentifier {
                    creationRequest.location = nil // 可以添加位置信息
                    creationRequest.creationDate = Date()

                    // 将自定义元数据添加到创建请求
                    var customMetadata: [String: Any] = [
                        "sourceApplicationIdentifier": appBundleIdentifier,
                        "appName": "Lomo"
                    ]

                    // 如果需要，可以添加其他元数据（如相机设置等）

                    // 尝试将照片添加到应用自己的相册中
                    if let albumPlaceholder = creationRequest.placeholderForCreatedAsset {
                        self.addAssetToAppAlbum(asset: albumPlaceholder)
                    }
                }
            }, completionHandler: { success, error in
                if let error = error {
                    print("保存照片出错: \(error.localizedDescription)")
                } else {
                    print("照片保存成功")
                }
            })
        }
    }

    // 添加照片到应用相册
    private func addAssetToAppAlbum(asset: PHObjectPlaceholder) {
        // 先检查"Lomo"相册是否存在
        let fetchOptions = PHFetchOptions()
        fetchOptions.predicate = NSPredicate(format: "title = %@", "Lomo")
        let collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: fetchOptions)

        // 如果相册不存在，创建相册
        if collections.count == 0 {
            PHPhotoLibrary.shared().performChanges({
                PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: "Lomo")
            }, completionHandler: { success, error in
                if success {
                    // 创建成功后，重新获取相册并添加照片
                    let collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: fetchOptions)
                    if let collection = collections.firstObject {
                        PHPhotoLibrary.shared().performChanges({
                            if let request = PHAssetCollectionChangeRequest(for: collection) {
                                request.addAssets([asset] as NSArray)
                            }
                        }, completionHandler: nil)
                    }
                } else if let error = error {
                    print("创建相册出错: \(error.localizedDescription)")
                }
            })
        } else if let collection = collections.firstObject {
            // 相册已存在，直接添加照片
            PHPhotoLibrary.shared().performChanges({
                if let request = PHAssetCollectionChangeRequest(for: collection) {
                    request.addAssets([asset] as NSArray)
                }
            }, completionHandler: nil)
        }
    }
}