import Foundation

enum CameraError: LocalizedError {
    case setupFailed
    case captureError
    case flashError
    case permissionDenied
    case lensError
    case zoomError
    case deviceError
    case resourceError
    case configurationError
    
    var errorDescription: String? {
        switch self {
        case .setupFailed:
            return "相机设置错误"
        case .captureError:
            return "照片捕获错误"
        case .flashError:
            return "闪光灯设置错误"
        case .permissionDenied:
            return "相机权限被拒绝"
        case .lensError:
            return "镜头切换错误"
        case .zoomError:
            return "变焦操作错误"
        case .deviceError:
            return "设备不可用或被占用"
        case .resourceError:
            return "系统资源不足"
        case .configurationError:
            return "配置不支持"
        }
    }
    
    var failureReason: String? {
        switch self {
        case .setupFailed:
            return "无法初始化相机系统"
        case .captureError:
            return "拍摄过程中出现错误"
        case .flashError:
            return "闪光灯无法使用或不支持"
        case .permissionDenied:
            return "用户未授权使用相机"
        case .lensError:
            return "切换镜头失败"
        case .zoomError:
            return "变焦操作超出范围或不支持"
        case .deviceError:
            return "相机设备不可用或被其他应用占用"
        case .resourceError:
            return "系统内存不足或温度过高"
        case .configurationError:
            return "当前设备不支持该配置"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .setupFailed:
            return "请重启应用或检查系统设置"
        case .captureError:
            return "请重试拍摄或检查存储空间"
        case .flashError:
            return "请关闭闪光灯后重试"
        case .permissionDenied:
            return "请在系统设置中允许应用使用相机"
        case .lensError:
            return "请确保选择的镜头可用"
        case .zoomError:
            return "请在支持的变焦范围内操作"
        case .deviceError:
            return "请确保没有其他应用正在使用相机"
        case .resourceError:
            return "请关闭不必要的应用或等待设备冷却"
        case .configurationError:
            return "请选择设备支持的配置参数"
        }
    }
    
    var helpAnchor: String? {
        switch self {
        case .setupFailed:
            return "camera_setup_help"
        case .captureError:
            return "capture_help"
        case .flashError:
            return "flash_help"
        case .permissionDenied:
            return "permission_help"
        case .lensError:
            return "lens_help"
        case .zoomError:
            return "zoom_help"
        case .deviceError:
            return "device_help"
        case .resourceError:
            return "resource_help"
        case .configurationError:
            return "config_help"
        }
    }
} 