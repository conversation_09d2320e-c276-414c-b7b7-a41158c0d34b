import Foundation

/// UI控制服务实现类
class UIControlService: UIControlServiceProtocol {
    // MARK: - 私有属性
    
    /// 按钮控制逻辑
    private let buttonLogic = ButtonControlLogic()

    // 计时器管理器
    private let timerManager = TimerManager()

    // ViewModel引用
    private weak var viewModel: CameraViewModel?

    // 状态
    private var isTouching: Bool = false
    private var activeMode: DialMode? = nil
    private var autoResetTimer: Timer?  // 添加自动重置计时器
    
    // MARK: - 状态回调
    
    var onDialVisibilityChanged: ((Bool) -> Void)?
    var onDialModeChanged: ((DialMode?) -> Void)?
    var onTouchStateChanged: ((Bool) -> Void)?
    var onLeftButtonStateChanged: ((Bool) -> Void)?
    var onRightButtonStateChanged: ((Bool, Bool) -> Void)?
    
    // MARK: - 初始化
    
    /// 初始化UI控制服务
    init() {
        setupCallbacks()
        setupTimerCallbacks()
    }

    // 设置ViewModel引用
    func setViewModel(_ viewModel: Any) {
        self.viewModel = viewModel as? CameraViewModel
    }
    
    // MARK: - 私有方法
    
    /// 设置回调
    private func setupCallbacks() {
        // 设置按钮逻辑回调
        buttonLogic.onLeftButtonStateChanged = { [weak self] isExpanded in
            self?.onLeftButtonStateChanged?(isExpanded)
        }
        
        buttonLogic.onRightButtonStateChanged = { [weak self] isExpanded, isAMMode in
            self?.onRightButtonStateChanged?(isExpanded, isAMMode)
        }
    }

    private func setupTimerCallbacks() {
        timerManager.onZoomDialStateChanged = { [weak self] isVisible in
            self?.onDialVisibilityChanged?(isVisible)
        }
    }
    
    // MARK: - UIControlServiceProtocol 实现
    
    func showDial(mode: DialMode) {
        print("📱 显示刻度盘: \(mode)")
        activeMode = mode
        onDialModeChanged?(mode)
        onDialVisibilityChanged?(true)
        timerManager.cancelZoomDialHideTimer()
        timerManager.startZoomDialHideTimer(isTouching: isTouching)
        // 重置 A/M 按钮计时器
        timerManager.resetRightButtonTimer(isExpanded: true)

        // 开始自动重置计时器
        startAutoResetTimer()
    }

    func hideDial() {
        print("📱 隐藏刻度盘")
        activeMode = nil
        onDialModeChanged?(nil)
        onDialVisibilityChanged?(false)
        timerManager.startZoomDialHideTimer(isTouching: isTouching)

        // 检查参数面板状态
        if let viewModel = viewModel, viewModel.state.isRightButtonExpanded {
            // 仅当参数面板仍处于展开状态时才重置计时器
            timerManager.resetRightButtonTimer(isExpanded: true)
        }

        // 停止自动重置计时器
        stopAutoResetTimer()
    }

    func handleTouchBegan() {
        print("📱 开始触摸刻度盘")
        isTouching = true
        onTouchStateChanged?(true)
        timerManager.cancelZoomDialHideTimer()

        // 仅当参数面板展开时才重置计时器
        if let viewModel = viewModel, viewModel.state.isRightButtonExpanded {
            timerManager.resetRightButtonTimer(isExpanded: true)
        }
    }

    func handleTouchEnded() {
        print("📱 结束触摸刻度盘")
        isTouching = false
        onTouchStateChanged?(false)
        timerManager.startZoomDialHideTimer(isTouching: isTouching)

        // 仅当参数面板展开时才重置计时器
        if let viewModel = viewModel, viewModel.state.isRightButtonExpanded {
            timerManager.resetRightButtonTimer(isExpanded: true)
        }
    }
    
    func handleCameraSwitch(completion: (() -> Void)?) {
        // 设置旋转状态
        let isRotating = true
        
        // 通知旋转状态变化
        // 如果有需要可以添加旋转状态回调
        
        // 延迟执行完成回调，模拟旋转动画时间
        DispatchQueue.main.asyncAfter(deadline: .now() + UIConstants.cameraRotationResetDelay) {
            // 重置旋转状态
            // 调用完成回调
            completion?()
        }
    }
    
    func expandParameter(isRecording: Bool, completion: ((Bool, Double, Double) -> Void)?) {
        buttonLogic.expandParameter(isRecording: isRecording) { isExpanded, closeOpacity, sideOpacity in
            completion?(isExpanded, closeOpacity, sideOpacity)
        }
    }
    
    func collapseParameter(completion: ((Bool, Double, Double) -> Void)?) {
        buttonLogic.collapseParameter { isExpanded, closeOpacity, sideOpacity in
            completion?(isExpanded, closeOpacity, sideOpacity)
        }
    }
    
    func toggleParameter(_ paramType: ParameterType) {
        // 根据参数类型切换对应的参数面板
        switch paramType {
        case .exposure:
            // 切换曝光参数面板
            buttonLogic.toggleExposure()
        case .focus:
            // 切换对焦参数面板
            buttonLogic.toggleFocus()
        case .whiteBalance:
            // 切换白平衡参数面板
            buttonLogic.toggleWhiteBalance()
        }
    }
    
    func collapseCurrentParameter() {
        // 折叠当前展开的参数面板
        buttonLogic.collapseCurrentParameter()
    }

    // MARK: - 私有计时器方法

    // 开始自动重置计时器
    private func startAutoResetTimer() {
        stopAutoResetTimer()  // 先停止已存在的计时器

        // 创建新的计时器,每2秒重置一次
        autoResetTimer = Timer.scheduledTimer(withTimeInterval: 2.0, repeats: true) { [weak self] _ in
            self?.resetRightButtonTimerIfExpanded()
        }
        RunLoop.main.add(autoResetTimer!, forMode: .common)  // 添加到 common 模式确保稳定运行
    }

    // 只有当参数面板处于展开状态时才重置计时器
    private func resetRightButtonTimerIfExpanded() {
        // 检查参数面板是否展开
        if let viewModel = viewModel, viewModel.state.isRightButtonExpanded {
            timerManager.resetRightButtonTimer(isExpanded: true)
        } else {
            // 如果面板已关闭，则停止自动重置计时器
            stopAutoResetTimer()
        }
    }

    // 停止自动重置计时器
    private func stopAutoResetTimer() {
        autoResetTimer?.invalidate()
        autoResetTimer = nil
    }

    deinit {
        stopAutoResetTimer()
    }
}