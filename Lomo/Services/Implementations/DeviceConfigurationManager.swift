import Foundation
import AVFoundation
import UIKit

class DeviceConfigurationManager {
    // 单例模式
    static let shared = DeviceConfigurationManager()
    private init() {}
    
    // 根据设备能力配置相机
    func configureForDevice(_ specification: DeviceSpecification) {
        // 配置相机参数
        configureCameraSettings(for: specification)
        
        // 配置功能可用性
        configureFeatureAvailability(for: specification)
        
        // 配置UI显示
        configureUIForDevice(specification)
        
        // 保存设备配置
        saveDeviceConfiguration(specification)
    }
    
    // 配置相机设置
    private func configureCameraSettings(for spec: DeviceSpecification) {
        guard let device = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) else { return }
        
        do {
            try device.lockForConfiguration()
            
            // 配置自动对焦
            if device.isFocusModeSupported(.continuousAutoFocus) {
                device.focusMode = .continuousAutoFocus
            }
            
            // 配置自动曝光
            if device.isExposureModeSupported(.continuousAutoExposure) {
                device.exposureMode = .continuousAutoExposure
            }
            
            // 配置白平衡
            if device.isWhiteBalanceModeSupported(.continuousAutoWhiteBalance) {
                device.whiteBalanceMode = .continuousAutoWhiteBalance
            }
            
            device.unlockForConfiguration()
            
            // 配置视频防抖
            if let session = AVCaptureSession().outputs.first?.connections.first,
               session.isVideoStabilizationSupported {
                session.preferredVideoStabilizationMode = .auto
            }
            
        } catch {
            print("设备配置错误: \(error.localizedDescription)")
        }
    }
    
    // 配置功能可用性
    private func configureFeatureAvailability(for spec: DeviceSpecification) {
        // 存储功能可用性状态
        UserDefaults.standard.set(spec.supportedFeatures.supportsRAW, forKey: "supportsRAW")
        UserDefaults.standard.set(spec.supportedFeatures.supportsProRAW, forKey: "supportsProRAW")
        UserDefaults.standard.set(spec.supportedFeatures.supportsNightMode, forKey: "supportsNightMode")
        UserDefaults.standard.set(spec.supportedFeatures.supportsPortraitMode, forKey: "supportsPortraitMode")
        UserDefaults.standard.set(spec.supportedFeatures.supportsCinematicMode, forKey: "supportsCinematicMode")
        UserDefaults.standard.set(spec.supportedFeatures.supportsProResVideo, forKey: "supportsProResVideo")
    }
    
    // 配置UI
    private func configureUIForDevice(_ spec: DeviceSpecification) {
        // 存储UI配置
        let uiConfig: [String: Any] = [
            "showRAWButton": spec.supportedFeatures.supportsRAW,
            "showProRAWButton": spec.supportedFeatures.supportsProRAW,
            "showNightModeButton": spec.supportedFeatures.supportsNightMode,
            "showPortraitModeButton": spec.supportedFeatures.supportsPortraitMode,
            "showCinematicButton": spec.supportedFeatures.supportsCinematicMode,
            "showProResButton": spec.supportedFeatures.supportsProResVideo,
            "maxVideoResolution": [
                "width": spec.supportedFeatures.maxVideoResolution.width,
                "height": spec.supportedFeatures.maxVideoResolution.height
            ],
            "maxSlowMotionFPS": spec.supportedFeatures.maxSlowMotionFPS
        ]
        
        UserDefaults.standard.set(uiConfig, forKey: "deviceUIConfiguration")
    }
    
    // 保存设备配置
    private func saveDeviceConfiguration(_ spec: DeviceSpecification) {
        let config: [String: Any] = [
            "model": spec.model,
            "cameraSystem": spec.cameraSystem.rawValue,
            "lidarSupported": spec.lidarSupported,
            "macroSupported": spec.macroSupported
        ]
        
        UserDefaults.standard.set(config, forKey: "deviceConfiguration")
    }
    
    // 获取保存的设备配置
    func getSavedDeviceConfiguration() -> [String: Any]? {
        return UserDefaults.standard.dictionary(forKey: "deviceConfiguration")
    }
    
    // 获取保存的UI配置
    func getSavedUIConfiguration() -> [String: Any]? {
        return UserDefaults.standard.dictionary(forKey: "deviceUIConfiguration")
    }
    
    // 重置设备配置
    func resetDeviceConfiguration() {
        UserDefaults.standard.removeObject(forKey: "deviceConfiguration")
        UserDefaults.standard.removeObject(forKey: "deviceUIConfiguration")
    }
} 