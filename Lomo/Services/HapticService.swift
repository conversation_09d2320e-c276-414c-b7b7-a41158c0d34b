import UIKit

/// 处理震动反馈的服务
class HapticService {
    static let shared = HapticService()
    
    private init() {}
    
    func mediumImpact() {
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()
    }
    
    func lightImpact() {
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
    }
    
    func heavyImpact() {
        UIImpactFeedbackGenerator(style: .heavy).impactOccurred()
    }
    
    func selectionFeedback() {
        UISelectionFeedbackGenerator().selectionChanged()
    }
    
    func notificationFeedback(type: UINotificationFeedbackGenerator.FeedbackType) {
        UINotificationFeedbackGenerator().notificationOccurred(type)
    }
} 