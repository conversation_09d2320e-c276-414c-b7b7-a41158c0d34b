import Foundation
import SwiftUI
import AVFoundation

// 闪光灯模式
enum FlashMode {
    case off
    case on
    case auto
}

/// UI样式服务 - 提供统一的UI样式决策
class UIStyleService {
    // MARK: - 单例
    static let shared = UIStyleService()
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 参数颜色方法
    
    /// 获取参数图标颜色
    /// - Parameters:
    ///   - option: 参数选项
    ///   - state: 相机状态
    ///   - isFlashEnabled: 闪光灯是否开启
    ///   - isLivePhotoEnabled: 实况照片是否开启
    ///   - isHDREnabled: HDR是否开启
    ///   - isStabilizationEnabled: 防抖是否开启
    /// - Returns: 图标颜色
    func getParameterIconColor(
        for option: String,
        state: CameraState,
        isFlashEnabled: Bool,
        isLivePhotoEnabled: Bool,
        isHDREnabled: Bool,
        isStabilizationEnabled: Bool
    ) -> Color {
        if option == "bolt.fill" {
            return isFlashEnabled ? UIConstants.dialIndicatorColor : .white
        } else if option == "livephoto" {
            return isLivePhotoEnabled ? UIConstants.dialIndicatorColor : .white
        } else if option == "HDR" {
            // 只有在兼容格式下才显示为黄色，否则显示为白色
            if [PhotoFormatMode.jpg, .heif, .tiff].contains(state.photoFormatMode) {
                return isHDREnabled ? UIConstants.dialIndicatorColor : .white
            } else {
                return .white  // 不兼容格式时显示为白色（未选中）
            }
        } else if option == "video.stabilization" {  // 添加防抖选项的判断
            return isStabilizationEnabled ? UIConstants.dialIndicatorColor : .white
        }
        
        // 视频模式参数
        if state.isVideoMode {
            switch option {
            case _ where option.contains("ratio"):
                return state.aspectRatioMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case _ where option.contains("resolution"):
                return state.resolutionMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case _ where option.contains("fps"):
                return .white // 帧率选项始终显示为白色
            case _ where option.contains("format"):
                return state.videoEncodingMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case _ where option.contains("color"):
                return state.colorSpaceMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case "video.fill":
                return UIConstants.dialIndicatorColor
            default:
                return .white
            }
        }
        // 照片模式参数
        else {
            switch option {
            case _ where option.contains("ratio"):
                return state.photoRatioMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case _ where option.contains("format"):
                return state.photoFormatMode.rawValue == option ? UIConstants.dialIndicatorColor : .white
            case "camera.fill":
                return UIConstants.dialIndicatorColor
            default:
                return .white
            }
        }
    }
    
    // MARK: - 参数文本显示方法
    
    /// 获取参数文本显示值
    /// - Parameters:
    ///   - option: 参数选项
    ///   - state: 相机状态
    /// - Returns: 显示文本
    func getParameterTextValue(for option: String, state: CameraState) -> String {
        // 通用参数
        if option == "bolt.fill" || option == "livephoto" || option == "HDR" {
            return option
        }
        
        // 视频模式参数
        if state.isVideoMode {
            if option == "16:9" { // 视频宽高比
                return state.aspectRatioMode.rawValue
            } else if option == "4K" { // 视频分辨率
                return state.resolutionMode.rawValue
            } else if option == "30fps" { // 视频帧率
                return state.frameRateMode.rawValue
            } else if option == "HEVC" { // 视频编码
                // 简化ProRes格式在状态栏/胶囊的显示
                switch state.videoEncodingMode {
                case .proResHQ:
                    return "Pro.HQ"
                case .proRes:
                    return "Pro"
                case .proResLT:
                    return "Pro.LT"
                case .proResProxy:
                    return "Pro.P"
                default:
                    return state.videoEncodingMode.rawValue
                }
            } else if option == "SDR" { // 视频色彩空间
                // 简化Dolby Vision在状态栏/胶囊的显示
                if state.colorSpaceMode == .dolbyVision {
                    return "Dolby"
                } else {
                    return state.colorSpaceMode.rawValue
                }
            } else {
                return option
            }
        }
        // 照片模式参数
        else {
            if option == "4:3" { // 照片比例
                return state.photoRatioMode.rawValue
            } else if option == "JPG" { // 照片格式
                // 简化ProRAW格式在状态栏/胶囊的显示
                if state.photoFormatMode == .proRaw {
                    return "P.RAW"
                } else {
                    return state.photoFormatMode.rawValue
                }
            } else {
                return option
            }
        }
    }
    
    /// 获取闪光灯图标
    /// - Parameter flashMode: 闪光灯模式
    /// - Returns: 图标名称
    func getFlashIcon(for flashMode: FlashMode) -> String {
        switch flashMode {
        case .on:
            return "bolt.fill"
        case .auto:
            return "bolt.badge.a.fill"
        case .off:
            return "bolt.slash.fill"
        }
    }
    
    /// 获取录制背景颜色
    /// - Parameters:
    ///   - isVideoMode: 是否为视频模式
    ///   - isRecording: 是否正在录制
    /// - Returns: 背景颜色
    func getRecordingBackgroundColor(isVideoMode: Bool, isRecording: Bool) -> Color {
        return isVideoMode && isRecording ? 
            UIConstants.recordingColor : 
            Color.black.opacity(UIConstants.topBarBackgroundOpacity)
    }
} 