import Foundation
import SwiftUI

/// 格式化服务 - 提供统一的格式化功能
class FormattingService {
    // MARK: - 单例
    static let shared = FormattingService()
    
    // MARK: - 依赖
    private let buttonLogic = ButtonControlLogic()
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 时间格式化
    
    /// 格式化时间
    /// - Parameter time: 时间间隔（秒）
    /// - Returns: 格式化后的时间字符串（HH:MM:SS）
    func formatTime(_ time: TimeInterval) -> String {
        return buttonLogic.formattedTime(time)
    }
    
    // MARK: - 曝光相关格式化
    
    /// 格式化曝光值
    /// - Parameter value: 曝光值
    /// - Returns: 格式化后的曝光值字符串
    func formatExposureValue(_ value: Double) -> String {
        return ExposureDialUtils.formatValue(value)
    }
    
    // MARK: - 焦距相关格式化
    
    /// 格式化焦距值
    /// - Parameter value: 焦距值
    /// - Returns: 格式化后的焦距字符串
    func formatFocusValue(_ value: Double) -> String {
        return FocusDialUtils.formatFocusValue(value)
    }
    
    // MARK: - 色温相关格式化
    
    /// 格式化色温值
    /// - Parameter value: 色温值
    /// - Returns: 格式化后的色温字符串
    func formatTemperatureValue(_ value: Double) -> String {
        return String(format: "%.0fK", value)
    }
    
    // MARK: - 色调相关格式化
    
    /// 格式化色调值
    /// - Parameter value: 色调值
    /// - Returns: 格式化后的色调字符串
    func formatTintValue(_ value: Double) -> String {
        return TintDialUtils.formatValue(value)
    }
    
    // MARK: - ISO相关格式化
    
    /// 格式化ISO值
    /// - Parameter value: ISO值
    /// - Returns: 格式化后的ISO字符串
    func formatISOValue(_ value: Double) -> String {
        return String(format: "%.0f", value)
    }
    
    // MARK: - 快门相关格式化
    
    /// 格式化快门值
    /// - Parameter value: 快门值
    /// - Returns: 格式化后的快门字符串
    func formatShutterValue(_ value: Double) -> String {
        return ShutterDialUtils.formatShutterValue(value)
    }
    
    // MARK: - 镜头相关格式化
    
    /// 格式化镜头值
    /// - Parameters:
    ///   - lens: 镜头数值
    ///   - isSelected: 是否被选中
    ///   - currentZoomFactor: 当前变焦倍数
    /// - Returns: 格式化后的镜头值字符串
    func formatLensValue(lens: String, isSelected: Bool, currentZoomFactor: CGFloat) -> String {
        if isSelected {
            return ZoomUtils.formatZoomFactor(Double(currentZoomFactor))
        }
        // 非选中状态显示原始镜头值
        if let value = Double(lens.replacingOccurrences(of: "x", with: "")) {
            return ZoomUtils.formatZoomFactor(value)
        }
        return lens.replacingOccurrences(of: "x", with: "")
    }
} 