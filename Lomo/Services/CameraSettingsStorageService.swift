import Foundation
import SwiftData
import AVFoundation
import SwiftUI

/// 相机设置存储服务
/// 负责所有相机设置的存储和读取
class CameraSettingsStorageService {
    // MARK: - 单例
    static let shared = CameraSettingsStorageService()
    
    // MARK: - 依赖
    private let cameraBasicManager = CameraBasicManager.shared
    private let storageService = UserDefaultsService.shared
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 基本设置存储方法
    
    /// 加载所有相机基本设置并应用到状态
    /// - Parameter state: 要更新的相机状态
    /// - Returns: 更新后的相机状态
    func loadSavedBasicSettings(into state: inout CameraState,
                              isGridEnabled: inout Bool,
                              isHistogramEnabled: inout Bool,
                              isLevelEnabled: inout Bool, 
                              isPeakingEnabled: inout Bool,
                              isFlipped: inout Bool,
                              isZebraEnabled: inout Bool,
                              isStabilizationEnabled: inout Bool,
                              isHDREnabled: inout Bool,
                              isTimerEnabled: inout Bool,
                              buttonLogic: ButtonControlLogic) {
        let savedSettings = cameraBasicManager.getSettings()
        
        // 加载基础设置
        state.isVideoMode = savedSettings.isVideoMode
        
        // 相机位置默认为后置摄像头（不从持久化存储加载，与定时器保持一致的实现）
        state.currentPosition = .back
        
        // 加载视频模式状态栏设置
        if let encodingMode = VideoEncodingMode(rawValue: savedSettings.videoEncodingMode) {
            state.videoEncodingMode = encodingMode
        }
        if let aspectRatioMode = AspectRatioMode(rawValue: savedSettings.videoAspectRatioMode) {
            state.aspectRatioMode = aspectRatioMode
        }
        if let resolutionMode = ResolutionMode(rawValue: savedSettings.videoResolutionMode) {
            state.resolutionMode = resolutionMode
        }
        if let frameRateMode = FrameRateMode(rawValue: savedSettings.videoFrameRateMode) {
            state.frameRateMode = frameRateMode
        }
        if let colorSpaceMode = ColorSpaceMode(rawValue: savedSettings.videoColorSpaceMode) {
            state.colorSpaceMode = colorSpaceMode
        }
        
        // 加载照片模式状态栏设置
        if let photoFormatMode = PhotoFormatMode(rawValue: savedSettings.photoFormatMode) {
            state.photoFormatMode = photoFormatMode
        }
        if let photoRatioMode = PhotoRatioMode(rawValue: savedSettings.photoRatioMode) {
            state.photoRatioMode = photoRatioMode
        }
        
        // 加载照片模式
        switch savedSettings.photoMode {
        case "auto":
            state.photoMode = .auto
        case "portrait":
            state.photoMode = .portrait
        case "macro":
            state.photoMode = .macro
        case "night":
            state.photoMode = .night
        case "timelapse":
            state.photoMode = .timelapse
        case "lightTrail":
            state.photoMode = .lightTrail
        case "pano":
            state.photoMode = .pano
        default:
            state.photoMode = .auto
        }
        
        // 加载3x3按钮状态设置
        isGridEnabled = savedSettings.isGridEnabled
        isHistogramEnabled = savedSettings.isHistogramEnabled
        
        // 加载水平仪、峰值对焦和翻转功能设置
        isLevelEnabled = savedSettings.isLevelEnabled
        isPeakingEnabled = savedSettings.isPeakingEnabled
        isFlipped = savedSettings.isFlipped
        
        // 加载翻转模式
        if let flipMode = FlipMode(rawValue: savedSettings.flipMode) {
            state.flipMode = flipMode
        }
        
        // 加载防抖模式
        if let stabilizationMode = StabilizationMode(rawValue: savedSettings.stabilizationMode) {
            state.stabilizationMode = stabilizationMode
        }
        
        // 加载斑马线和防抖功能设置
        isZebraEnabled = savedSettings.isZebraEnabled
        isStabilizationEnabled = savedSettings.isStabilizationEnabled
        
        // 确保防抖状态与防抖模式一致
        isStabilizationEnabled = (state.stabilizationMode != .off)
        
        // 加载HDR状态（状态栏功能，非3x3按钮）
        isHDREnabled = savedSettings.isHDREnabled
        
        // 加载定时器模式
        if let timerMode = TimerMode(rawValue: savedSettings.timerMode) {
            state.timerMode = timerMode
        }
        
        // 加载定时器状态
        isTimerEnabled = savedSettings.isTimerEnabled
        
        // 确保定时器状态与定时器模式一致
        isTimerEnabled = (state.timerMode != .off)
        
        // 同步buttonLogic状态
        buttonLogic.setVideoMode(state.isVideoMode)
    }
    
    /// 保存当前的相机基本设置
    /// - Parameter isVideoMode: 当前是否为视频模式
    func saveBasicSettings(isVideoMode: Bool) {
        let settings = cameraBasicManager.getSettings()
        settings.isVideoMode = isVideoMode
        cameraBasicManager.saveSettings(settings)
    }
    
    /// 更新特定设置
    /// - Parameters:
    ///   - keyPath: 要更新的设置路径
    ///   - value: 要设置的值
    func updateSetting<T>(_ keyPath: WritableKeyPath<CameraBasicSettings, T>, value: T) {
        cameraBasicManager.updateSetting(keyPath, value: value)
    }
    
    // MARK: - 刻度盘角度存储方法
    
    /// 清除所有刻度盘角度设置
    func clearAllDialRotationAngles() {
        storageService.clearAllDialRotationAngles()
    }
    
    /// 保存刻度盘旋转角度
    /// - Parameters:
    ///   - rotationAngle: 旋转角度
    ///   - dialType: 刻度盘类型
    func saveDialRotationAngle(_ rotationAngle: Double, forDialType dialType: String) {
        storageService.saveDialRotationAngle(rotationAngle, forDialType: dialType)
    }
    
    /// 获取保存的刻度盘旋转角度
    /// - Parameter storageKey: 存储键
    /// - Returns: 保存的旋转角度
    func getDialRotationAngle(forKey storageKey: String) -> Double {
        return storageService.getDialRotationAngle(forKey: storageKey)
    }
    
    /// 设置按钮选择标志
    /// - Parameter isFromButton: 是否来自按钮选择
    func setIsFromButtonSelection(_ isFromButton: Bool) {
        storageService.setIsFromButtonSelection(isFromButton)
    }
    
    /// 获取按钮选择标志
    /// - Returns: 是否来自按钮选择
    func isFromButtonSelection() -> Bool {
        return storageService.isFromButtonSelection()
    }
    
    // MARK: - 相机状态存储方法
    
    /// 更新视频模式
    /// - Parameter isVideoMode: 是否为视频模式
    func updateVideoMode(_ isVideoMode: Bool) {
        updateSetting(\.isVideoMode, value: isVideoMode)
    }
    
    /// 更新网格状态
    /// - Parameter isEnabled: 是否启用网格
    func updateGridEnabled(_ isEnabled: Bool) {
        updateSetting(\.isGridEnabled, value: isEnabled)
    }
    
    /// 更新峰值对焦状态
    /// - Parameter isEnabled: 是否启用峰值对焦
    func updatePeakingEnabled(_ isEnabled: Bool) {
        updateSetting(\.isPeakingEnabled, value: isEnabled)
    }
    
    /// 更新直方图状态
    /// - Parameter isEnabled: 是否启用直方图
    func updateHistogramEnabled(_ isEnabled: Bool) {
        updateSetting(\.isHistogramEnabled, value: isEnabled)
    }
    
    /// 更新水平仪状态
    /// - Parameter isEnabled: 是否启用水平仪
    func updateLevelEnabled(_ isEnabled: Bool) {
        updateSetting(\.isLevelEnabled, value: isEnabled)
    }
    
    /// 更新翻转状态
    /// - Parameter isEnabled: 是否启用翻转
    func updateFlippedEnabled(_ isEnabled: Bool) {
        updateSetting(\.isFlipped, value: isEnabled)
    }
    
    /// 更新HDR状态
    /// - Parameter isEnabled: 是否启用HDR
    func updateHDREnabled(_ isEnabled: Bool) {
        updateSetting(\.isHDREnabled, value: isEnabled)
    }
    
    /// 更新斑马线状态
    /// - Parameter isEnabled: 是否启用斑马线
    func updateZebraEnabled(_ isEnabled: Bool) {
        updateSetting(\.isZebraEnabled, value: isEnabled)
    }
    
    /// 更新防抖状态
    /// - Parameter isEnabled: 是否启用防抖
    func updateStabilizationEnabled(_ isEnabled: Bool) {
        updateSetting(\.isStabilizationEnabled, value: isEnabled)
    }
    
    /// 更新防抖设置
    /// - Parameters:
    ///   - mode: 防抖模式
    ///   - isEnabled: 是否启用防抖
    func updateStabilization(mode: StabilizationMode, isEnabled: Bool) {
        updateSetting(\.stabilizationMode, value: mode.rawValue)
        updateSetting(\.isStabilizationEnabled, value: isEnabled)
    }
    
    /// 更新翻转模式
    /// - Parameter mode: 翻转模式
    func updateFlipMode(_ mode: FlipMode) {
        updateSetting(\.flipMode, value: mode.rawValue)
    }
    
    /// 更新视频宽高比模式
    /// - Parameter mode: 宽高比模式
    func updateAspectRatioMode(_ mode: AspectRatioMode) {
        updateSetting(\.videoAspectRatioMode, value: mode.rawValue)
    }
    
    /// 更新视频分辨率模式
    /// - Parameter mode: 分辨率模式
    func updateResolutionMode(_ mode: ResolutionMode) {
        updateSetting(\.videoResolutionMode, value: mode.rawValue)
    }
    
    /// 更新视频帧率模式
    /// - Parameter mode: 帧率模式
    func updateFrameRateMode(_ mode: FrameRateMode) {
        updateSetting(\.videoFrameRateMode, value: mode.rawValue)
    }
    
    /// 更新视频色彩空间模式
    /// - Parameter mode: 色彩空间模式
    func updateColorSpaceMode(_ mode: ColorSpaceMode) {
        updateSetting(\.videoColorSpaceMode, value: mode.rawValue)
    }
    
    /// 更新照片格式模式
    /// - Parameter mode: 照片格式模式
    func updatePhotoFormatMode(_ mode: PhotoFormatMode) {
        updateSetting(\.photoFormatMode, value: mode.rawValue)
    }
    
    /// 更新照片比例模式
    /// - Parameter mode: 照片比例模式
    func updatePhotoRatioMode(_ mode: PhotoRatioMode) {
        updateSetting(\.photoRatioMode, value: mode.rawValue)
    }
    
    /// 更新照片模式
    /// - Parameter mode: 照片模式
    func updatePhotoMode(_ mode: PhotoMode) {
        let modeString: String
        switch mode {
        case .auto: modeString = "auto"
        case .portrait: modeString = "portrait"
        case .macro: modeString = "macro"
        case .night: modeString = "night"
        case .timelapse: modeString = "timelapse"
        case .lightTrail: modeString = "lightTrail"
        case .pano: modeString = "pano"
        }
        updateSetting(\.photoMode, value: modeString)
    }
    
    /// 更新视频编码模式
    /// - Parameter mode: 视频编码模式
    func updateVideoEncodingMode(_ mode: VideoEncodingMode) {
        updateSetting(\.videoEncodingMode, value: mode.rawValue)
    }
    
    /// 更新定时器设置
    /// - Parameters:
    ///   - mode: 定时器模式
    ///   - isEnabled: 是否启用定时器
    func updateTimerSettings(mode: TimerMode, isEnabled: Bool) {
        updateSetting(\.timerMode, value: mode.rawValue)
        updateSetting(\.isTimerEnabled, value: isEnabled)
    }
    
    // MARK: - 高级API
    
    /// 保存所有相机设置
    /// - Parameter state: 当前相机状态
    func saveAllCameraSettings(from state: CameraState) {
        // 保存基本模式
        saveBasicSettings(isVideoMode: state.isVideoMode)
        
        // 保存视频相关设置
        updateVideoEncodingMode(state.videoEncodingMode)
        updateAspectRatioMode(state.aspectRatioMode)
        updateResolutionMode(state.resolutionMode)
        updateFrameRateMode(state.frameRateMode)
        updateColorSpaceMode(state.colorSpaceMode)
        
        // 保存照片相关设置
        updatePhotoFormatMode(state.photoFormatMode)
        updatePhotoRatioMode(state.photoRatioMode)
        updatePhotoMode(state.photoMode)
        
        // 保存其他设置
        updateFlipMode(state.flipMode)
        updateStabilization(mode: state.stabilizationMode, isEnabled: state.stabilizationMode != .off)
        updateTimerSettings(mode: state.timerMode, isEnabled: state.timerMode != .off)
    }
    
    /// 重置所有设置为默认值
    func resetAllSettings() {
        cameraBasicManager.resetToDefaults()
        clearAllDialRotationAngles()
    }
} 