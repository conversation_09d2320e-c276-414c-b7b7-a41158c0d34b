import Foundation

/// 性能监控服务 - MVVM-S架构
/// 负责监控应用性能，提供FPS监控、更新频率控制等功能
class PerformanceService {
    
    // MARK: - 单例
    static let shared = PerformanceService()
    
    // MARK: - 性能监控器
    private var monitors: [String: PerformanceMonitorInstance] = [:]
    
    // MARK: - 初始化
    private init() {
        print("🔧 [PerformanceService] 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 创建或获取性能监控器
    /// - Parameter identifier: 监控器标识符（如"toneSplit", "curve", "hsl"等）
    /// - Returns: 性能监控器实例
    func getMonitor(for identifier: String) -> PerformanceMonitorInstance {
        if let existingMonitor = monitors[identifier] {
            return existingMonitor
        }
        
        let newMonitor = PerformanceMonitorInstance(identifier: identifier)
        monitors[identifier] = newMonitor
        return newMonitor
    }
    
    /// 移除性能监控器
    /// - Parameter identifier: 监控器标识符
    func removeMonitor(for identifier: String) {
        monitors.removeValue(forKey: identifier)
        print("🔧 [PerformanceService] 移除监控器: \(identifier)")
    }
    
    /// 清理所有监控器
    func clearAllMonitors() {
        monitors.removeAll()
        print("🔧 [PerformanceService] 清理所有监控器")
    }
    
    /// 获取性能统计报告
    func getPerformanceReport() -> [String: PerformanceStats] {
        var report: [String: PerformanceStats] = [:]
        for (identifier, monitor) in monitors {
            report[identifier] = monitor.getStats()
        }
        return report
    }
}

// MARK: - 性能监控器实例
class PerformanceMonitorInstance {
    
    // MARK: - 属性
    private let identifier: String
    private var updateCount = 0
    private var lastUpdateTime = Date()
    private let updateThreshold: TimeInterval
    private var totalUpdateTime: TimeInterval = 0
    private var maxUpdateInterval: TimeInterval = 0
    private var minUpdateInterval: TimeInterval = Double.greatestFiniteMagnitude
    
    // MARK: - 配置
    private let reportInterval: Int // 每N次更新打印一次报告
    
    // MARK: - 初始化
    init(identifier: String, targetFPS: Double = 60.0, reportInterval: Int = 100) {
        self.identifier = identifier
        self.updateThreshold = 1.0 / targetFPS // 60 FPS = 0.016s
        self.reportInterval = reportInterval
        print("🔧 [PerformanceMonitor] 创建监控器: \(identifier), 目标FPS: \(targetFPS)")
    }
    
    // MARK: - 监控方法
    
    /// 记录更新并判断是否应该跳过
    /// - Returns: true表示可以继续更新，false表示建议跳过以保持性能
    func recordUpdate() -> Bool {
        let now = Date()
        let timeSinceLastUpdate = now.timeIntervalSince(lastUpdateTime)
        
        updateCount += 1
        totalUpdateTime += timeSinceLastUpdate
        
        // 更新统计信息
        if timeSinceLastUpdate > maxUpdateInterval {
            maxUpdateInterval = timeSinceLastUpdate
        }
        if timeSinceLastUpdate < minUpdateInterval && updateCount > 1 {
            minUpdateInterval = timeSinceLastUpdate
        }
        
        // 如果更新太频繁，返回false建议跳过
        let shouldSkip = timeSinceLastUpdate < updateThreshold
        
        if !shouldSkip {
            lastUpdateTime = now
        }
        
        // 定期打印性能报告
        if updateCount % reportInterval == 0 {
            printPerformanceReport(timeSinceLastUpdate)
        }
        
        return !shouldSkip
    }
    
    /// 强制记录更新（不进行频率控制）
    func forceRecordUpdate() {
        let now = Date()
        let timeSinceLastUpdate = now.timeIntervalSince(lastUpdateTime)
        
        updateCount += 1
        totalUpdateTime += timeSinceLastUpdate
        lastUpdateTime = now
        
        // 更新统计信息
        if timeSinceLastUpdate > maxUpdateInterval {
            maxUpdateInterval = timeSinceLastUpdate
        }
        if timeSinceLastUpdate < minUpdateInterval && updateCount > 1 {
            minUpdateInterval = timeSinceLastUpdate
        }
    }
    
    /// 重置统计信息
    func reset() {
        updateCount = 0
        lastUpdateTime = Date()
        totalUpdateTime = 0
        maxUpdateInterval = 0
        minUpdateInterval = Double.greatestFiniteMagnitude
        print("🔧 [PerformanceMonitor] 重置监控器: \(identifier)")
    }
    
    // MARK: - 统计信息
    
    /// 获取性能统计信息
    func getStats() -> PerformanceStats {
        let averageInterval = updateCount > 0 ? totalUpdateTime / Double(updateCount) : 0
        let currentFPS = averageInterval > 0 ? 1.0 / averageInterval : 0
        
        return PerformanceStats(
            identifier: identifier,
            updateCount: updateCount,
            averageUpdateInterval: averageInterval,
            maxUpdateInterval: maxUpdateInterval,
            minUpdateInterval: minUpdateInterval == Double.greatestFiniteMagnitude ? 0 : minUpdateInterval,
            currentFPS: currentFPS,
            targetFPS: 1.0 / updateThreshold
        )
    }
    
    // MARK: - 私有方法
    
    private func printPerformanceReport(_ lastInterval: TimeInterval) {
        let stats = getStats()
        print("🔧 [性能监控-\(identifier)] 更新次数: \(stats.updateCount), " +
              "平均间隔: \(String(format: "%.3f", stats.averageUpdateInterval))s, " +
              "当前FPS: \(String(format: "%.1f", stats.currentFPS)), " +
              "最后间隔: \(String(format: "%.3f", lastInterval))s")
    }
}

// MARK: - 性能统计数据结构
struct PerformanceStats {
    let identifier: String
    let updateCount: Int
    let averageUpdateInterval: TimeInterval
    let maxUpdateInterval: TimeInterval
    let minUpdateInterval: TimeInterval
    let currentFPS: Double
    let targetFPS: Double
    
    /// 性能健康度（0-1，1表示最佳性能）
    var healthScore: Double {
        let fpsRatio = min(currentFPS / targetFPS, 1.0)
        return fpsRatio
    }
    
    /// 是否性能良好
    var isPerformanceGood: Bool {
        return healthScore > 0.8 // 80%以上的目标FPS认为性能良好
    }
}

// MARK: - 便捷扩展
extension PerformanceService {
    
    /// 便捷方法：色调分离性能监控
    static func toneSplitMonitor() -> PerformanceMonitorInstance {
        return shared.getMonitor(for: "toneSplit")
    }
    
    /// 便捷方法：曲线编辑性能监控
    static func curveMonitor() -> PerformanceMonitorInstance {
        return shared.getMonitor(for: "curve")
    }
    
    /// 便捷方法：HSL性能监控
    static func hslMonitor() -> PerformanceMonitorInstance {
        return shared.getMonitor(for: "hsl")
    }
    
    /// 便捷方法：滤镜应用性能监控
    static func filterMonitor() -> PerformanceMonitorInstance {
        return shared.getMonitor(for: "filter")
    }
}
