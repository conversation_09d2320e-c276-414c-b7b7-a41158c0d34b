import Foundation
import AVFoundation

/// 会话管理器，负责创建和管理AVCaptureSession实例
class SessionManager {
    /// 共享实例
    static let shared = SessionManager()
    
    /// 相机捕获会话
    private(set) var session: AVCaptureSession
    
    /// 私有初始化方法，创建单例
    private init() {
        self.session = AVCaptureSession()
    }
    
    /// 重置会话
    func resetSession() {
        // 停止当前会话
        if session.isRunning {
            session.stopRunning()
        }
        
        // 创建新会话
        session = AVCaptureSession()
    }
    
    /// 开始运行会话
    func startRunning() {
        if !session.isRunning {
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                self?.session.startRunning()
            }
        }
    }
    
    /// 停止运行会话
    func stopRunning() {
        if session.isRunning {
            session.stopRunning()
        }
    }
} 