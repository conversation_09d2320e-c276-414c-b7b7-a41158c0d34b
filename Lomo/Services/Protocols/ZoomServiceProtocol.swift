import Foundation
import CoreGraphics

/// 变焦服务协议，定义相机变焦和镜头选择相关操作
protocol ZoomServiceProtocol {
    // MARK: - 变焦控制
    
    /// 显示变焦刻度盘
    func showZoomDial()
    
    /// 隐藏变焦刻度盘
    func hideZoomDial()
    
    /// 更新变焦倍数
    /// - Parameter factor: 变焦倍数
    func updateZoom(_ factor: CGFloat)
    
    /// 选择特定镜头
    /// - Parameter lens: 镜头标识，如"0.5"、"1"、"2"等
    func selectLens(_ lens: String)
    
    /// 根据索引选择镜头
    /// - Parameter index: 镜头索引
    func selectLensAtIndex(_ index: Int)
    
    /// 更新可用镜头列表
    /// - Parameter lenses: 可用镜头列表
    func updateAvailableLenses(_ lenses: [String])
    
    /// 处理触摸开始事件
    func handleTouchBegan()
    
    /// 处理触摸结束事件
    func handleTouchEnded()
    
    // MARK: - 状态回调
    
    /// 变焦倍数变化回调
    var onZoomFactorChanged: ((CGFloat) -> Void)? { get set }
    
    /// 选中镜头索引变化回调
    var onSelectedLensIndexChanged: ((Int) -> Void)? { get set }
    
    /// 镜头选择变化回调
    var onLensSelected: ((String) -> Void)? { get set }
} 