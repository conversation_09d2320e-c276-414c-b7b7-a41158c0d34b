import Foundation

// 刻度盘配置协议
protocol DialConfiguration {
    var mode: DialMode { get }                    // 模式
    var minValue: Double { get }                  // 最小值
    var maxValue: Double { get }                  // 最大值
    var currentValue: Double { get }              // 当前值
    var step: Double { get }                      // 步进值
    var unit: String { get }                      // 单位
    var isLogarithmic: Bool { get }              // 是否对数刻度
    var mainTickCount: Int { get }                // 主刻度数量
    var subTickCount: Int { get }                 // 副刻度数量
    var lenses: [String] { get }                  // 可用镜头列表
    var dialLenses: [String] { get }              // 刻度盘显示的焦距列表
    func formatValue(_ value: Double) -> String   // 数值格式化
} 