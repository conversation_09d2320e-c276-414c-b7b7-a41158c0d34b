// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 划痕效果服务协议 - MVVM-S架构
/// 定义划痕效果处理的标准接口，支持依赖注入和并发安全
protocol ScratchServiceProtocol: Actor {
    
    /// 获取所有可用的划痕预设
    /// - Returns: 划痕预设数组
    func getAllScratchPresets() async -> [ScratchPreset]
    
    /// 应用划痕效果到图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 划痕效果参数
    /// - Returns: 处理后的图像
    func applyScratch(to image: UIImage, with parameters: ScratchParameters) async -> UIImage
    
    /// 验证划痕参数有效性
    /// - Parameter parameters: 划痕参数
    /// - Returns: 验证结果，true表示有效
    func validateParameters(_ parameters: ScratchParameters) async -> Bool
    
    /// 获取预设的详细信息
    /// - Parameter preset: 划痕预设
    /// - Returns: 预设的密度、长度、宽度信息
    func getPresetDetails(_ preset: ScratchPreset) async -> (density: Double, length: Double, width: Double)
}