import Foundation
import SwiftUI

/// 提供视图控制器相关功能的服务协议，使代码与平台无关
protocol ViewControllerServiceProtocol {
    /// 获取当前呈现的视图控制器
    func getCurrentViewController() -> Any?
    
    /// 从SwiftUI视图中创建平台相关的控制器
    func makeViewController<Content: View>(from content: Content) -> Any
    
    /// 将SwiftUI视图作为模态呈现
    func presentAsModal<Content: View>(content: Content, animated: Bool, completion: (() -> Void)?)
    
    /// 关闭当前呈现的模态视图
    func dismissCurrentModal(animated: Bool, completion: (() -> Void)?)
} 