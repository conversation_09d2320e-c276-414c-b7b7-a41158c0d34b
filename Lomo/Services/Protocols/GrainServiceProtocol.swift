// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 颗粒效果服务协议 - MVVM-S架构
/// 定义颗粒效果处理的标准接口，支持依赖注入和并发安全
protocol GrainServiceProtocol: Actor {
    
    /// 获取所有可用的颗粒预设
    /// - Returns: 颗粒预设数组
    func getAllGrainPresets() async -> [GrainPreset]
    
    /// 应用颗粒效果到图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 颗粒效果参数
    /// - Returns: 处理后的图像
    func applyGrain(to image: UIImage, with parameters: GrainParameters) async -> UIImage
    
    /// 验证颗粒参数有效性
    /// - Parameter parameters: 颗粒参数
    /// - Returns: 验证结果，true表示有效
    func validateParameters(_ parameters: GrainParameters) async -> Bool
    
    /// 获取预设的预览效果
    /// - Parameter preset: 颗粒预设
    /// - Returns: 预设的强度和密度信息
    func getPresetInfo(_ preset: GrainPreset) async -> (intensity: Double, density: Double)
}