// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 特效服务协议 - MVVM-S架构
/// 定义特效模块的统一服务接口，协调各种特效的应用和管理
protocol EffectsServiceProtocol: Actor {
    
    // MARK: - 设置管理
    
    /// 获取当前特效设置
    /// - Returns: 特效设置模型
    func getEffectsSettings() async -> EffectsModel
    
    /// 保存特效设置
    /// - Parameter settings: 要保存的设置
    /// - Throws: 保存失败时抛出错误
    func saveEffectsSettings(_ settings: EffectsModel) async throws
    
    /// 重置所有特效设置为默认值
    func resetAllSettings() async throws
    
    // MARK: - 漏光效果
    
    /// 更新漏光效果强度
    /// - Parameter intensity: 强度值 (0.0-1.0)
    func updateLightLeakIntensity(_ intensity: Double) async
    
    /// 选择漏光预设
    /// - Parameter preset: 漏光预设，nil表示取消选择
    func selectLightLeakPreset(_ preset: LightLeakPreset?) async
    
    /// 切换漏光效果开关
    func toggleLightLeakEnabled() async
    
    /// 获取可用的漏光预设
    /// - Returns: 漏光预设数组
    func getAvailableLightLeakPresets() async -> [LightLeakPreset]
    
    // MARK: - 颗粒效果
    
    /// 更新颗粒效果强度
    /// - Parameter intensity: 强度值 (0.0-1.0)
    func updateGrainIntensity(_ intensity: Double) async
    
    /// 选择颗粒预设
    /// - Parameter preset: 颗粒预设，nil表示取消选择
    func selectGrainPreset(_ preset: GrainPreset?) async
    
    /// 切换颗粒效果开关
    func toggleGrainEnabled() async
    
    /// 获取可用的颗粒预设
    /// - Returns: 颗粒预设数组
    func getAvailableGrainPresets() async -> [GrainPreset]
    
    // MARK: - 划痕效果
    
    /// 更新划痕效果强度
    /// - Parameter intensity: 强度值 (0.0-1.0)
    func updateScratchIntensity(_ intensity: Double) async
    
    /// 选择划痕预设
    /// - Parameter preset: 划痕预设，nil表示取消选择
    func selectScratchPreset(_ preset: ScratchPreset?) async
    
    /// 切换划痕效果开关
    func toggleScratchEnabled() async
    
    /// 获取可用的划痕预设
    /// - Returns: 划痕预设数组
    func getAvailableScratchPresets() async -> [ScratchPreset]
    
    // MARK: - 时间戳效果
    
    /// 更新时间戳样式
    /// - Parameter style: 时间戳样式
    func updateTimeStyle(_ style: String) async
    
    /// 更新时间戳颜色
    /// - Parameter color: 时间戳颜色
    func updateTimeColor(_ color: String) async
    
    /// 更新时间戳位置
    /// - Parameter position: 时间戳位置
    func updateTimePosition(_ position: String) async
    
    /// 切换时间戳效果开关
    func toggleTimeEnabled() async
    
    // MARK: - 图像处理
    
    /// 应用所有启用的特效到图像
    /// - Parameter image: 原始图像
    /// - Returns: 处理后的图像
    /// - Throws: 处理失败时抛出错误
    func applyAllEffectsToImage(_ image: UIImage) async throws -> UIImage
    
    /// 应用单个特效到图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - effectType: 特效类型
    /// - Returns: 处理后的图像
    /// - Throws: 处理失败时抛出错误
    func applySingleEffectToImage(_ image: UIImage, effectType: EffectType) async throws -> UIImage
    
    /// 获取特效处理进度
    /// - Returns: 处理进度 (0.0-1.0)
    func getProcessingProgress() async -> Double
}

/// 特效类型枚举
enum EffectType: String, CaseIterable {
    case timestamp = "timestamp"
    case lightLeak = "lightLeak"
    case grain = "grain"
    case scratch = "scratch"
    
    var displayName: String {
        switch self {
        case .timestamp: return "时间戳"
        case .lightLeak: return "漏光"
        case .grain: return "颗粒"
        case .scratch: return "划痕"
        }
    }
}