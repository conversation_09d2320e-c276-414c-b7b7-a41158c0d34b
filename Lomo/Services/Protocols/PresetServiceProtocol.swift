import Foundation

/// 预设服务协议，提供预设相关的操作
protocol PresetServiceProtocol {
    /// 选择预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    ///   - activeType: 当前激活的预设类型（引用传递）
    ///   - activeIndex: 当前激活的预设索引（引用传递）
    ///   - updateSettings: 自定义设置更新逻辑
    func selectPreset(type: PresetType, index: Int, activeType: inout String, activeIndex: inout Int, updateSettings: (PresetType, Int, Bool) -> Void)
    
    /// 根据预设类型获取所有预设
    /// - Parameter type: 预设类型
    /// - Returns: 预设数组
    func getPresets(for type: PresetType) -> [Any]
    
    /// 根据类型和索引获取特定预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: 指定的预设
    func getPreset(type: PresetType, index: Int) -> Any?
} 