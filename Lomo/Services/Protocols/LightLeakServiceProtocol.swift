// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 漏光效果服务协议 - MVVM-S架构
/// 定义漏光效果处理的标准接口，支持依赖注入和并发安全
protocol LightLeakServiceProtocol: Actor {
    
    /// 获取所有可用的漏光预设
    /// - Returns: 漏光预设数组
    func getAllLightLeakPresets() async -> [LightLeakPreset]
    
    /// 应用漏光效果到图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - parameters: 漏光效果参数
    /// - Returns: 处理后的图像
    func applyLightLeak(to image: UIImage, with parameters: LightLeakParameters) async -> UIImage
    
    /// 验证漏光参数有效性
    /// - Parameter parameters: 漏光参数
    /// - Returns: 验证结果，true表示有效
    func validateParameters(_ parameters: LightLeakParameters) async -> Bool
    
    /// 获取预设的预览图像
    /// - Parameter preset: 漏光预设
    /// - Returns: 预览图像，如果不存在则返回nil
    func getPresetPreviewImage(_ preset: LightLeakPreset) async -> UIImage?
}