// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 存储服务协议 - MVVM-S架构
/// 定义数据持久化的标准接口，支持SwiftData和其他存储方式
protocol StorageServiceProtocol: Actor {
    
    /// 保存特效设置
    /// - Parameter settings: 要保存的特效设置
    /// - Throws: 保存失败时抛出错误
    func saveEffectsSettings(_ settings: EffectsModel) async throws
    
    /// 加载特效设置
    /// - Returns: 特效设置，如果不存在则返回默认设置
    /// - Throws: 加载失败时抛出错误
    func loadEffectsSettings() async throws -> EffectsModel
    
    /// 删除特效设置
    /// - Throws: 删除失败时抛出错误
    func deleteEffectsSettings() async throws
    
    /// 检查特效设置是否存在
    /// - Returns: 存在返回true，否则返回false
    func effectsSettingsExists() async -> Bool
    
    /// 获取设置的最后更新时间
    /// - Returns: 最后更新时间，如果不存在则返回nil
    func getLastUpdateTime() async -> Date?
}