// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 滤镜服务协议 - MVVM-S架构
/// 定义滤镜应用模块的业务逻辑接口
protocol FilterServiceProtocol: Actor {
    
    // MARK: - 预设操作
    
    /// 应用预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    func applyPreset(type: FilterPresetType, index: Int) async throws
    
    /// 清除当前预设
    func clearPreset() async throws
    
    /// 检查是否选中了指定预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: 是否选中
    func isPresetSelected(type: FilterPresetType, index: Int) async -> Bool
    
    /// 获取指定类型的所有预设
    /// - Parameter type: 预设类型
    /// - Returns: 预设数组
    func getPresets(for type: FilterPresetType) async -> [FilterPreset]
    
    // MARK: - 参数操作
    
    /// 更新单个参数
    /// - Parameters:
    ///   - keyPath: 参数路径
    ///   - value: 新值
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) async throws
    
    /// 批量更新参数
    /// - Parameter updates: 更新闭包
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws
    
    /// 获取当前参数的副本
    /// - Returns: 当前参数
    func getCurrentParameters() async -> FilterParameters
    
    /// 重置所有参数
    func resetParameters() async throws
    
    // MARK: - LUT操作
    
    /// 应用LUT文件
    /// - Parameters:
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度
    func applyLUT(lutPath: String, intensity: Float) async throws
    
    /// 更新LUT强度
    /// - Parameter intensity: 新的强度值
    func updateLUTIntensity(_ intensity: Float) async throws
    
    /// 更新预设强度
    /// - Parameter intensity: 新的强度值
    func updatePresetIntensity(_ intensity: Float) async throws
    
    /// 切换LUT启用状态
    func toggleLUT() async throws
    
    /// 清除LUT
    func clearLUT() async throws
    
    // MARK: - 图像处理
    
    /// 设置原始图像
    /// - Parameter image: 原始图像
    func setOriginalImage(_ image: UIImage) async throws
    
    /// 获取当前显示的图像
    /// - Returns: 当前显示图像
    func getCurrentDisplayImage() async -> UIImage?
    
    /// 获取实时预览图像
    /// - Returns: 实时预览图像
    func getRealtimePreview() async -> UIImage?
    
    /// 获取最终输出图像
    /// - Parameter highQuality: 是否高质量
    /// - Returns: 最终输出图像
    func getFinalOutputImage(highQuality: Bool) async -> UIImage?
    
    // MARK: - 状态查询
    
    /// 检查是否有未保存的更改
    /// - Returns: 是否有未保存的更改
    func hasUnsavedChanges() async -> Bool
    
    /// 检查是否有活跃的滤镜效果
    /// - Returns: 是否有活跃滤镜
    func hasActiveFilter() async -> Bool
    
    // MARK: - 设置管理
    
    /// 获取滤镜设置
    /// - Returns: 滤镜设置
    func getSettings() async -> FilterSettings
    
    /// 保存设置
    /// - Parameter settings: 滤镜设置
    func saveSettings(_ settings: FilterSettings) async throws
    
    /// 重置到默认值
    func resetToDefaults() async throws
}