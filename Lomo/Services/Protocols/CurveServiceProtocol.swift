// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import CoreGraphics


/// 曲线服务协议 - MVVM-S架构
/// 定义曲线调整的业务逻辑接口
protocol CurveServiceProtocol: Actor {
    
    // MARK: - 曲线点管理
    
    /// 更新曲线控制点
    /// - Parameters:
    ///   - points: 新的控制点数组
    ///   - channel: 通道类型
    func updateCurvePoints(_ points: [CGPoint], for channel: CurveChannel) async throws
    
    /// 获取当前通道的曲线点
    /// - Parameter channel: 通道类型
    /// - Returns: 曲线点数组
    func getCurrentCurvePoints(for channel: CurveChannel) async -> [CGPoint]
    
    /// 更新指定点的位置
    /// - Parameters:
    ///   - index: 点索引
    ///   - normalizedPoint: 归一化坐标点
    ///   - channel: 通道类型
    func updatePointPosition(index: Int, normalizedPoint: CGPoint, for channel: CurveChannel) async throws
    
    /// 添加曲线点
    /// - Parameters:
    ///   - normalizedPoint: 归一化坐标点
    ///   - channel: 通道类型
    /// - Returns: 新点的索引
    func addPoint(at normalizedPoint: CGPoint, for channel: CurveChannel) async throws -> Int
    
    /// 移除曲线点
    /// - Parameters:
    ///   - index: 点索引
    ///   - channel: 通道类型
    func removePoint(at index: Int, for channel: CurveChannel) async throws
    
    // MARK: - 曲线预设
    
    /// 应用曲线预设
    /// - Parameters:
    ///   - preset: 预设类型
    ///   - channel: 目标通道
    ///   - intensity: 应用强度
    func applyPreset(_ preset: CurveProcessor.CurvePreset, to channel: CurveChannel, intensity: Float) async throws
    
    /// 获取当前预设
    /// - Returns: 当前预设
    func getCurrentPreset() async -> CurveProcessor.CurvePreset?
    
    // MARK: - 曲线重置
    
    /// 重置所有曲线状态到默认值
    func resetAllCurves() async throws
    
    /// 重置指定通道
    /// - Parameter channel: 通道类型
    func resetChannel(_ channel: CurveChannel) async throws
    
    /// 重置当前通道的曲线
    func resetCurrentChannel() async throws
    
    // MARK: - 曲线状态
    
    /// 更新曲线强度
    /// - Parameter intensity: 强度值
    func updateCurveIntensity(_ intensity: Float) async throws
    
    /// 获取当前曲线强度
    /// - Returns: 曲线强度
    func getCurrentCurveIntensity() async -> Float
    
    /// 切换曲线启用状态
    /// - Parameter enabled: 是否启用
    func setCurveEnabled(_ enabled: Bool) async throws
    
    /// 检查曲线是否启用
    /// - Returns: 是否启用
    func isCurveEnabled() async -> Bool
    
    /// 检查是否有活跃的曲线
    /// - Returns: 是否有活跃曲线
    func hasActiveCurves() async -> Bool
    
    // MARK: - 曲线质量
    
    /// 更新渲染质量
    /// - Parameter quality: 渲染质量
    func updateRenderQuality(_ quality: CurveProcessor.CurveQuality) async throws
    
    /// 获取当前渲染质量
    /// - Returns: 渲染质量
    func getCurrentRenderQuality() async -> CurveProcessor.CurveQuality
    
    // MARK: - LUT生成
    
    /// 获取当前LUT数据
    /// - Returns: LUT数据字典
    func getCurrentLUTs() async -> [CurveChannel: [Float]]
    
    /// 强制更新LUT
    func forceUpdateLUTs() async throws
    
    // MARK: - 性能监控
    
    /// 获取性能统计
    /// - Returns: 性能统计数据
    func getPerformanceStats() async -> (averageTime: TimeInterval, updateCount: Int, lastDuration: TimeInterval)
    
    // MARK: - 边界验证
    
    /// 验证并修正所有控制点的边界
    func validateAndFixBoundaries() async throws
    
    /// 检查曲线是否在边界内
    /// - Parameter channel: 通道类型
    /// - Returns: 是否在边界内
    func isCurveWithinBounds(for channel: CurveChannel) async -> Bool
}
