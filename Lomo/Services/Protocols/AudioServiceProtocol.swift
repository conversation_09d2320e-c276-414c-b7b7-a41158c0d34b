import Foundation
import AVFoundation
import Combine

/// 音频服务协议，定义音频监控功能
protocol AudioServiceProtocol {
    
    /// 左声道峰值电平
    var leftChannelPeakLevel: CurrentValueSubject<Float, Never> { get }
    
    /// 右声道峰值电平
    var rightChannelPeakLevel: CurrentValueSubject<Float, Never> { get }
    
    /// 开始监控音频电平
    /// - Parameter session: 捕获会话
    func startMonitoring(session: AVCaptureSession)
    
    /// 停止监控音频电平
    func stopMonitoring()
} 