import Foundation
import AVFoundation

/// 曝光服务协议，定义曝光、ISO和快门相关操作
protocol ExposureServiceProtocol {
    // 控制方法
    func setExposureValue(_ value: Double)
    func setISOValue(_ value: Double)
    func setShutterSpeed(_ value: Double)
    func resetExposure()
    
    // 状态获取
    func getExposureRange() -> ClosedRange<Double>
    func getISORange() -> ClosedRange<Double>
    func getShutterSpeedRange() -> ClosedRange<Double>
    
    // 回调注册
    var onExposureChanged: ((Double) -> Void)? { get set }
    var onISOChanged: ((Double) -> Void)? { get set }
    var onShutterSpeedChanged: ((Double) -> Void)? { get set }
    var onExposureRangeChanged: ((ClosedRange<Double>) -> Void)? { get set }
    var onISORangeChanged: ((ClosedRange<Double>) -> Void)? { get set }
    var onShutterSpeedRangeChanged: ((ClosedRange<Double>) -> Void)? { get set }
} 