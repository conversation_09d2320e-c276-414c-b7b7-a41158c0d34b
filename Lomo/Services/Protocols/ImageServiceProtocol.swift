import Foundation

/// 图像服务协议，提供平台无关的图像处理接口
protocol ImageServiceProtocol {
    /// 图像数据类型
    associatedtype ImageType
    
    /// 从数据创建图像
    /// - Parameter data: 图像数据
    /// - Returns: 图像对象
    func createImage(from data: Data) -> ImageType?
    
    /// 将图像转换为数据
    /// - Parameter image: 图像对象
    /// - Returns: 图像数据
    func convertToData(_ image: ImageType) -> Data?
    
    /// 应用滤镜到图像
    /// - Parameters:
    ///   - image: 原始图像
    ///   - filter: 滤镜参数
    /// - Returns: 处理后的图像
    func applyFilter(to image: ImageType, with filter: Any) -> ImageType?
    
    /// 调整图像大小
    /// - Parameters:
    ///   - image: 原始图像
    ///   - size: 目标大小
    /// - Returns: 调整大小后的图像
    func resizeImage(_ image: ImageType, to size: CGSize) -> ImageType?
} 