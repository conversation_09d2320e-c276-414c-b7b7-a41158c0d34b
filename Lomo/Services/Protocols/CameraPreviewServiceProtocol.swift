import Foundation
import AVFoundation
import SwiftUI

/// 相机预览服务协议，定义相机预览的公共接口
protocol CameraPreviewServiceProtocol {
    /// 获取相机捕获会话
    var session: AVCaptureSession { get }
    
    /// 配置预览层
    /// - Parameter view: 要添加预览层的视图
    /// - Parameter customFrame: 可选的自定义尺寸
    func configurePreviewLayer(for view: UIView, customFrame: CGRect?)
    
    /// 更新预览层
    /// - Parameter view: 包含预览层的视图
    /// - Parameter customFrame: 可选的自定义尺寸
    func updatePreviewLayer(for view: UIView, customFrame: CGRect?)
    
    /// 更新预览层（带动画）
    /// - Parameters:
    ///   - view: 包含预览层的视图
    ///   - customFrame: 可选的自定义尺寸
    ///   - animationDuration: 动画持续时间
    ///   - animationCurve: 动画曲线
    func updatePreviewLayer(for view: UIView, customFrame: CGRect?, animationDuration: TimeInterval, animationCurve: UInt)
    
    /// 根据拖动偏移量更新预览层位置
    /// - Parameters:
    ///   - view: 包含预览层的视图
    ///   - dragOffset: 拖动偏移量
    func updatePreviewLayerPosition(for view: UIView, dragOffset: CGFloat)
} 