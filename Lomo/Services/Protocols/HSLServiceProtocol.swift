// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// HSL服务协议 - MVVM-S架构
/// 定义HSL调整的业务逻辑接口
protocol HSLServiceProtocol: Actor {
    
    // MARK: - HSL参数更新
    
    /// 更新HSL色相
    /// - Parameters:
    ///   - hue: 色相值 (-180 到 +180 度)
    ///   - colorIndex: 颜色范围索引 (0-7)
    func updateHSLHue(_ hue: Float, for colorIndex: Int?) async throws
    
    /// 更新HSL饱和度
    /// - Parameters:
    ///   - saturation: 饱和度值 (-100 到 +100)
    ///   - colorIndex: 颜色范围索引 (0-7)
    func updateHSLSaturation(_ saturation: Float, for colorIndex: Int?) async throws
    
    /// 更新HSL明度
    /// - Parameters:
    ///   - luminance: 明度值 (-100 到 +100)
    ///   - colorIndex: 颜色范围索引 (0-7)
    func updateHSLLuminance(_ luminance: Float, for colorIndex: Int?) async throws
    
    // MARK: - HSL颜色范围管理
    
    /// 切换HSL颜色范围
    /// - Parameter index: 颜色范围索引 (0-7)
    func switchHSLColorRange(to index: Int) async throws
    
    /// 获取当前选中的颜色范围索引
    /// - Returns: 颜色范围索引
    func getCurrentColorRangeIndex() async -> Int
    
    /// 获取颜色范围名称
    /// - Parameter index: 颜色索引
    /// - Returns: 颜色名称
    func getColorRangeName(for index: Int) async -> String
    
    // MARK: - HSL参数查询
    
    /// 获取当前选中颜色的HSL参数
    /// - Returns: HSL参数元组
    func getCurrentHSLParameters() async -> (hue: Float, saturation: Float, luminance: Float)
    
    /// 获取所有HSL参数
    /// - Returns: 所有HSL参数
    func getAllHSLParameters() async -> (hueValues: [Float], saturationValues: [Float], luminanceValues: [Float])
    
    /// 获取指定颜色的HSL参数
    /// - Parameter colorIndex: 颜色索引
    /// - Returns: HSL参数元组
    func getHSLParameters(for colorIndex: Int) async -> (hue: Float, saturation: Float, luminance: Float)
    
    // MARK: - HSL重置操作
    
    /// 重置当前选中颜色的HSL参数
    func resetCurrentHSLColor() async throws
    
    /// 重置所有HSL颜色参数
    func resetAllHSLColors() async throws
    
    /// 重置指定颜色的HSL参数
    /// - Parameter colorIndex: 颜色索引
    func resetHSLColor(at colorIndex: Int) async throws
    
    // MARK: - HSL状态管理
    
    /// 检查是否有活跃的HSL调整
    /// - Returns: 是否有活跃调整
    func hasActiveHSLAdjustments() async -> Bool
    
    /// 检查指定颜色是否有调整
    /// - Parameter colorIndex: 颜色索引
    /// - Returns: 是否有调整
    func hasAdjustments(for colorIndex: Int) async -> Bool
    
    /// 获取HSL启用状态
    /// - Returns: 是否启用
    func isHSLEnabled() async -> Bool
    
    /// 设置HSL启用状态
    /// - Parameter enabled: 是否启用
    func setHSLEnabled(_ enabled: Bool) async throws
    
    // MARK: - HSL高级设置
    
    /// 更新HSL全局强度
    /// - Parameter intensity: 全局强度 (0.0-1.0)
    func updateHSLGlobalIntensity(_ intensity: Float) async throws
    
    /// 获取HSL全局强度
    /// - Returns: 全局强度
    func getHSLGlobalIntensity() async -> Float
    
    /// 更新HSL颜色范围柔和度
    /// - Parameter softness: 柔和度 (0.0-1.0)
    func updateHSLColorRangeSoftness(_ softness: Float) async throws
    
    /// 获取HSL颜色范围柔和度
    /// - Returns: 柔和度
    func getHSLColorRangeSoftness() async -> Float
    
    /// 更新HSL颜色范围精度
    /// - Parameter precision: 精度 (0.5-2.0)
    func updateHSLColorRangePrecision(_ precision: Float) async throws
    
    /// 获取HSL颜色范围精度
    /// - Returns: 精度
    func getHSLColorRangePrecision() async -> Float
    
    // MARK: - HSL调试和诊断
    
    /// 打印HSL当前状态
    func printHSLCurrentState() async
    
    /// 验证HSL参数有效性
    /// - Returns: 验证结果
    func validateHSLParameters() async -> Bool
}