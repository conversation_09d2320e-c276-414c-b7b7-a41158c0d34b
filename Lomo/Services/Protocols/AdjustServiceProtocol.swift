// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import CoreGraphics

/// 调节服务协议 - MVVM-S架构
/// 定义调节模块的业务逻辑接口
protocol AdjustServiceProtocol: Actor {
    
    // MARK: - 参数更新
    
    /// 更新单个参数
    /// - Parameters:
    ///   - keyPath: 参数路径
    ///   - value: 新值
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) async throws
    
    /// 批量更新参数
    /// - Parameter updates: 更新闭包
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws
    
    /// 获取当前参数的副本
    /// - Returns: 当前参数
    func getCurrentParameters() async -> FilterParameters
    
    // MARK: - 参数重置
    
    /// 重置所有参数
    func resetAllParameters() async throws
    
    /// 重置到默认值
    func resetToDefaults() async throws
    
    // MARK: - 设置管理
    
    /// 获取调节设置
    /// - Returns: 调节设置
    func getSettings() async -> AdjustSettings
    
    /// 保存设置
    /// - Parameter settings: 调节设置
    func saveSettings(_ settings: AdjustSettings) async throws
    
    /// 更新特定设置
    /// - Parameters:
    ///   - keyPath: 设置路径
    ///   - value: 新值
    func updateSetting<T>(_ keyPath: WritableKeyPath<AdjustSettings, T>, value: T) async throws
}