// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 相册滤镜服务协议 - MVVM-S架构
/// 定义相册滤镜展示模块的业务逻辑接口
protocol GalleryFilterServiceProtocol {
    
    // MARK: - 滤镜数据获取
    
    /// 获取所有滤镜
    /// - Returns: 所有可用滤镜数组
    func getAllFilters() -> [Filter]
    
    /// 根据类型获取滤镜
    /// - Parameter type: 滤镜类型
    /// - Returns: 指定类型的滤镜数组
    func getFilters(byType type: FilterType) -> [Filter]
    
    /// 获取收藏的滤镜
    /// - Returns: 用户收藏的滤镜数组
    func getFavoriteFilters() -> [Filter]
    
    // MARK: - 滤镜操作
    
    /// 切换滤镜收藏状态
    /// - Parameter filterId: 滤镜ID
    /// - Returns: 切换后的收藏状态
    func toggleFavorite(filterId: String) -> Bool
}