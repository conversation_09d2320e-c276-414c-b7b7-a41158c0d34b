import Foundation
import AVFoundation

protocol RecordingServiceProtocol {
    // 会话管理
    var session: AVCaptureSession { get }
    
    // 录制控制
    func startRecording() async throws
    func stopRecording() async throws
    func toggleRecording() async throws
    func togglePause()
    func isPauseActive() -> Bool
    
    // 状态通知
    var onRecordingStateChanged: ((Bool) -> Void)? { get set }
    var onRecordingTimeChanged: ((TimeInterval) -> Void)? { get set }
    var onRecordingPauseStateChanged: ((Bool) -> Void)? { get set }
} 