import Foundation
import Combine

/// 提供通知相关功能的服务协议，使代码与平台通知机制解耦
protocol NotificationServiceProtocol {
    /// 注册通知观察者
    /// - Parameters:
    ///   - observer: 观察者对象
    ///   - selector: 接收通知时调用的方法
    ///   - name: 通知名称
    ///   - object: 通知发送者，nil表示接收所有发送者的通知
    func addObserver(_ observer: Any, selector: Selector, name: Notification.Name, object: Any?)
    
    /// 通过回调块注册通知观察者
    /// - Parameters:
    ///   - name: 通知名称
    ///   - object: 通知发送者，nil表示接收所有发送者的通知
    ///   - queue: 执行回调的队列，nil表示在发布通知的线程上执行
    ///   - block: 收到通知时执行的回调
    /// - Returns: 观察者token，用于移除观察者
    @discardableResult
    func addObserver(forName name: Notification.Name, object: Any?, queue: OperationQueue?, using block: @escaping (Notification) -> Void) -> NSObjectProtocol
    
    /// 移除特定观察者
    /// - Parameter observer: 要移除的观察者
    func removeObserver(_ observer: Any)
    
    /// 移除特定观察者的特定通知
    /// - Parameters:
    ///   - observer: 要移除的观察者
    ///   - name: 通知名称
    ///   - object: 通知发送者
    func removeObserver(_ observer: Any, name: Notification.Name?, object: Any?)
    
    /// 发布通知
    /// - Parameters:
    ///   - name: 通知名称
    ///   - object: 通知发送者
    ///   - userInfo: 附加信息字典
    func post(name: Notification.Name, object: Any?, userInfo: [AnyHashable: Any]?)
    
    /// 发布通知并等待处理完成（同步方式）
    /// - Parameters:
    ///   - name: 通知名称
    ///   - object: 通知发送者
    ///   - userInfo: 附加信息字典
    ///   - deliverImmediately: 是否立即交付通知
    ///   - forCoalescing: 是否合并相同的通知
    /// - Returns: 是否成功发布
    @discardableResult
    func postAndWait(name: Notification.Name, object: Any?, userInfo: [AnyHashable: Any]?, deliverImmediately: Bool, forCoalescing: Bool) -> Bool
    
    /// 获取通知发布者
    /// - Parameters:
    ///   - name: 通知名称
    ///   - object: 通知发送者，nil表示接收所有发送者的通知
    /// - Returns: 发布者对象，可用于SwiftUI的onReceive
    func publisher(for name: Notification.Name, object: AnyObject?) -> NotificationCenter.Publisher
} 