import Foundation
import AVFoundation

/// 相机服务协议，定义相机基本操作接口
protocol CameraServiceProtocol {
    // MARK: - 设备控制
    
    /// 设置并初始化相机
    func setup()
    
    /// 切换前后摄像头
    func switchCamera()
    
    /// 拍摄照片
    func capturePhoto()
    
    /// 获取当前使用的相机设备
    func getCurrentDevice() -> AVCaptureDevice?
    
    // MARK: - 高级控制方法
    
    /// 设置ISO值
    func setISO(_ iso: Float)
    
    /// 设置曝光值
    func setExposure(_ value: Double)
    
    /// 设置曝光补偿值
    func setExposureCompensation(_ value: Double)
    
    /// 设置快门速度
    func setShutterSpeed(_ value: Double)
    
    /// 设置焦点距离
    func setFocus(_ value: Double)
    
    /// 设置色温
    func setTemperature(_ value: Double)
    
    // MARK: - 状态回调
    
    /// 设备类型变化回调
    var onDeviceTypeChanged: ((CameraType) -> Void)? { get set }
    
    /// 相机位置变化回调
    var onPositionChanged: ((AVCaptureDevice.Position) -> Void)? { get set }
    
    /// 相机就绪回调
    var onCameraReady: (() -> Void)? { get set }
    
    /// 可用镜头变化回调
    var onAvailableLensesChanged: (([String]) -> Void)? { get set }
    
    /// 闪光灯状态变化回调
    var onFlashStateChanged: ((Bool) -> Void)? { get set }
    
    /// 相机旋转状态变化回调
    var onRotatingStateChanged: ((Bool) -> Void)? { get set }
    
    /// 相机设置完成回调
    var onCameraSetupComplete: (() -> Void)? { get set }
    
    /// 变焦因子变化回调
    var onZoomFactorChanged: ((CGFloat) -> Void)? { get set }
    
    /// 变焦因子范围变化回调 (最大值, 最小值, 当前值, 是否支持)
    var onZoomFactorsChanged: ((CGFloat, CGFloat, CGFloat, Bool) -> Void)? { get set }
    
    /// 快门速度范围变化回调
    var onShutterRangeChanged: ((Double, Double) -> Void)? { get set }
    
    /// ISO范围变化回调
    var onISORangeChanged: ((Float, Float) -> Void)? { get set }
} 