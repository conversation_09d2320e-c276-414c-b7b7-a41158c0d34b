// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 相纸服务协议 - MVVM-S架构
/// 定义相纸模块的服务接口，支持依赖注入和测试
@MainActor
protocol PaperServiceProtocol {
    /// 获取相纸设置
    /// - Returns: 相纸模型数据
    func getSettings() async throws -> PaperModel
    
    /// 保存相纸设置
    /// - Parameter settings: 要保存的相纸设置
    func saveSettings(_ settings: PaperModel) async throws
    
    /// 更新特定设置
    /// - Parameters:
    ///   - keyPath: 要更新的属性路径
    ///   - value: 新的属性值
    func updateSetting<T>(_ keyPath: WritableKeyPath<PaperModel, T>, value: T) async throws
    
    /// 添加到最近使用的预设
    /// - Parameter preset: 预设名称
    func addToRecentPresets(_ preset: String) async throws
    
    /// 切换预设收藏状态
    /// - Parameter preset: 预设名称
    func toggleFavorite(_ preset: String) async throws
    
    /// 重置所有设置为默认值
    func resetToDefaults() async throws
}