import Foundation
import CoreGraphics

/// 提供UIKit相关功能的桥接服务协议，使代码与平台无关
protocol UIKitBridgeServiceProtocol {
    /// 获取平台无关的颜色对象
    func getSystemColor(name: String) -> Any
    
    /// 获取平台无关的图像对象
    func getImage(named: String) -> Any?
    
    /// 获取平台无关的图像对象（从数据）
    func getImage(from data: Data) -> Any?
    
    /// 获取平台无关的动画选项
    func getAnimationOptions(curve: String) -> UInt
    
    /// 执行带动画的UI更新
    func animate(withDuration duration: TimeInterval, delay: TimeInterval, options: UInt, animations: @escaping () -> Void, completion: ((Bool) -> Void)?)
    
    /// 获取平台无关的字体
    func getFont(name: String, size: CGFloat, weight: String?) -> Any
} 