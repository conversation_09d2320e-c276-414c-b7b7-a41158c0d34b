// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 渲染服务协议 - MVVM-S架构
/// 定义统一渲染管线的接口
protocol RenderingServiceProtocol: Actor {
    
    // MARK: - 图像设置
    
    /// 设置基础图像
    /// - Parameter image: 基础图像
    func setBaseImage(_ image: UIImage) async throws
    
    /// 获取当前输出图像
    /// - Returns: 当前输出图像
    func getCurrentOutputImage() async -> UIImage?
    
    // MARK: - 参数更新
    
    /// 更新渲染参数
    /// - Parameter parameters: 滤镜参数
    func updateParameters(_ parameters: FilterParameters) async throws
    
    /// 批量更新参数
    /// - Parameter updates: 更新闭包
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws
    
    // MARK: - 渲染模式
    
    /// 设置渲染模式
    /// - Parameter mode: 渲染模式
    func setRenderingMode(_ mode: RenderingMode) async throws
    
    /// 获取当前渲染模式
    /// - Returns: 当前渲染模式
    func getCurrentRenderingMode() async -> RenderingMode
    
    // MARK: - LUT处理
    
    /// 应用LUT
    /// - Parameters:
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度
    func applyLUT(lutPath: String?, intensity: Float) async throws
    
    /// 清除LUT
    func clearLUT() async throws
    
    // MARK: - 图像输出
    
    /// 获取实时预览图像
    /// - Returns: 实时预览图像
    func getRealtimePreview() async -> UIImage?
    
    /// 获取最终输出图像
    /// - Parameter highQuality: 是否高质量
    /// - Returns: 最终输出图像
    func getFinalOutputImage(highQuality: Bool) async -> UIImage?
    
    /// 获取当前显示图像
    /// - Returns: 当前显示图像
    func getCurrentDisplayImage() async -> UIImage?
    
    // MARK: - 渲染状态
    
    /// 检查是否正在渲染
    /// - Returns: 是否正在渲染
    func isRendering() async -> Bool
    
    /// 检查是否有活跃的渲染效果
    /// - Returns: 是否有活跃效果
    func hasActiveEffects() async -> Bool
    
    // MARK: - 性能监控
    
    /// 获取渲染性能统计
    /// - Returns: 性能统计数据
    func getRenderingPerformanceStats() async -> (averageTime: TimeInterval, frameCount: Int, lastFrameTime: TimeInterval)
    
    /// 重置性能统计
    func resetPerformanceStats() async
    
    // MARK: - 渲染质量
    
    /// 设置渲染质量
    /// - Parameter quality: 渲染质量
    func setRenderingQuality(_ quality: RenderingQuality) async throws
    
    /// 获取当前渲染质量
    /// - Returns: 渲染质量
    func getCurrentRenderingQuality() async -> RenderingQuality
    
    // MARK: - 内存管理
    
    /// 清理渲染缓存
    func clearRenderingCache() async
    
    /// 获取内存使用情况
    /// - Returns: 内存使用字节数
    func getMemoryUsage() async -> Int
    
    // MARK: - 错误处理
    
    /// 获取最后的渲染错误
    /// - Returns: 渲染错误
    func getLastRenderingError() async -> Error?
    
    /// 清除渲染错误
    func clearRenderingError() async
}

/// 渲染质量枚举
enum RenderingQuality: String, CaseIterable {
    case low = "低质量"
    case standard = "标准质量"
    case high = "高质量"
    case ultra = "超高质量"
    
    var displayName: String {
        return self.rawValue
    }
    
    var scaleFactor: Float {
        switch self {
        case .low: return 0.5
        case .standard: return 1.0
        case .high: return 1.5
        case .ultra: return 2.0
        }
    }
}