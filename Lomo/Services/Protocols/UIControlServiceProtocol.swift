import Foundation

/// UI控制服务协议，定义刻度盘和触摸状态等UI控制逻辑
protocol UIControlServiceProtocol {
    // MARK: - UI控制
    
    /// 显示特定模式的刻度盘
    /// - Parameter mode: 刻度盘模式（曝光、变焦等）
    func showDial(mode: DialMode)
    
    /// 隐藏当前刻度盘
    func hideDial()
    
    /// 处理触摸开始事件
    func handleTouchBegan()
    
    /// 处理触摸结束事件
    func handleTouchEnded()
    
    /// 处理相机切换UI动画
    /// - Parameter completion: 动画完成后的回调
    func handleCameraSwitch(completion: (() -> Void)?)
    
    /// 展开参数面板
    func expandParameter(isRecording: Bool, completion: ((Bool, Double, Double) -> Void)?)
    
    /// 收起参数面板
    func collapseParameter(completion: ((Bool, Double, Double) -> Void)?)
    
    /// 切换参数面板
    /// - Parameter paramType: 参数类型
    func toggleParameter(_ paramType: ParameterType)
    
    /// 折叠当前参数面板
    func collapseCurrentParameter()

    /// 设置ViewModel引用
    func setViewModel(_ viewModel: Any)

    // MARK: - 状态回调
    
    /// 刻度盘可见性变化回调
    var onDialVisibilityChanged: ((Bool) -> Void)? { get set }
    
    /// 刻度盘模式变化回调
    var onDialModeChanged: ((DialMode?) -> Void)? { get set }
    
    /// 触摸状态变化回调
    var onTouchStateChanged: ((Bool) -> Void)? { get set }
    
    /// 左侧按钮状态变化回调
    var onLeftButtonStateChanged: ((Bool) -> Void)? { get set }
    
    /// 右侧按钮状态变化回调
    var onRightButtonStateChanged: ((Bool, Bool) -> Void)? { get set }
} 