// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData
import SwiftUI
import CoreGraphics

/// 调节服务Actor实现 - MVVM-S架构
/// 替代原有的AdjustService单例模式
actor AdjustServiceActor: AdjustServiceProtocol {
    
    // MARK: - 依赖注入
    
    private let filterService: FilterServiceProtocol
    private let curveService: CurveServiceProtocol
    private let hslService: HSLServiceProtocol
    private let storageService: StorageServiceProtocol
    
    // MARK: - 私有状态
    
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    
    // MARK: - 初始化
    
    init(
        filterService: FilterServiceProtocol,
        curveService: CurveServiceProtocol,
        hslService: HSLServiceProtocol,
        storageService: StorageServiceProtocol,
        modelContainer: ModelContainer? = nil
    ) {
        self.filterService = filterService
        self.curveService = curveService
        self.hslService = hslService
        self.storageService = storageService
        
        // 设置模型容器
        if let container = modelContainer {
            self.modelContainer = container
            self.modelContext = ModelContext(container)
            print("📱 [AdjustServiceActor] 使用注入的 ModelContainer")
        } else {
            // 后备方案：使用共享容器
            self.modelContainer = SharedService.shared.container
            self.modelContext = ModelContext(self.modelContainer!)
            print("📱 [AdjustServiceActor] 使用共享 ModelContainer（后备方案）")
        }
        
        print("🎨 AdjustServiceActor 初始化完成")
    }
    
    // MARK: - 私有方法
    

    
    // MARK: - AdjustServiceProtocol 实现
    
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) async throws {
        print("🔧 [AdjustServiceActor] updateParameter - keyPath: \(keyPath), value: \(value)")
        
        // 参数验证
        if let floatValue = value as? Float {
            guard floatValue.isFinite else {
                throw AdjustServiceError.invalidParameter("无效的Float参数值: \(floatValue)")
            }
        }
        
        // 委托给FilterService处理
        try await filterService.updateParameter(keyPath, value: value)
        
        print("✅ [AdjustServiceActor] 参数更新完成")
    }
    
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws {
        print("🔧 [AdjustServiceActor] batchUpdateParameters 开始")
        
        // 委托给FilterService处理
        try await filterService.batchUpdateParameters(updates)
        
        print("✅ [AdjustServiceActor] 批量参数更新完成")
    }
    
    func getCurrentParameters() async -> FilterParameters {
        return await filterService.getCurrentParameters()
    }
    
    func resetAllParameters() async throws {
        print("🔄 [AdjustServiceActor] 重置所有参数")
        
        // 重置FilterService参数
        try await filterService.resetParameters()
        
        // 重置曲线
        try await curveService.resetAllCurves()
        
        // 重置HSL
        try await hslService.resetAllHSLColors()
        
        // 重置设置中的参数
        var settings = await getSettings()
        settings.resetAllParameters()
        try await saveSettings(settings)
        
        print("✅ [AdjustServiceActor] 所有参数重置完成")
    }
    
    func resetToDefaults() async throws {
        print("🔄 [AdjustServiceActor] 重置到默认值")
        
        guard let context = modelContext else {
            throw AdjustServiceError.contextUnavailable
        }
        
        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<AdjustSettings>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }
            
            // 创建新的默认设置
            let newSettings = AdjustSettings()
            context.insert(newSettings)
            
            // 保存更改
            try context.save()
            
            // 重置所有参数
            try await resetAllParameters()
            
            print("✅ [AdjustServiceActor] 重置到默认值完成")
        } catch {
            print("❌ [AdjustServiceActor] 重置到默认值失败: \(error)")
            throw AdjustServiceError.resetFailed(error)
        }
    }
    
    func getSettings() async -> AdjustSettings {
        guard let context = modelContext else {
            print("⚠️ [AdjustServiceActor] ModelContext 不可用，返回默认设置")
            return AdjustSettings()
        }
        
        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<AdjustSettings>(
                predicate: #Predicate { $0.id == "adjust_settings" }
            )
            let existingSettings = try context.fetch(descriptor)
            
            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }
            
            // 如果不存在，创建新的设置并保存
            let newSettings = AdjustSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("❌ [AdjustServiceActor] 获取调节设置失败: \(error)")
            return AdjustSettings()
        }
    }
    
    func saveSettings(_ settings: AdjustSettings) async throws {
        guard let context = modelContext else {
            throw AdjustServiceError.contextUnavailable
        }
        
        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
            print("✅ [AdjustServiceActor] 设置保存成功")
        } catch {
            print("❌ [AdjustServiceActor] 保存调节设置失败: \(error)")
            throw AdjustServiceError.saveFailed(error)
        }
    }
    
    func updateSetting<T>(_ keyPath: WritableKeyPath<AdjustSettings, T>, value: T) async throws {
        var settings = await getSettings()
        settings[keyPath: keyPath] = value
        try await saveSettings(settings)
        print("✅ [AdjustServiceActor] 设置更新完成: \(keyPath)")
    }
}

// MARK: - 错误定义

enum AdjustServiceError: LocalizedError {
    case invalidParameter(String)
    case contextUnavailable
    case saveFailed(Error)
    case resetFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidParameter(let message):
            return "参数错误: \(message)"
        case .contextUnavailable:
            return "数据上下文不可用"
        case .saveFailed(let error):
            return "保存失败: \(error.localizedDescription)"
        case .resetFailed(let error):
            return "重置失败: \(error.localizedDescription)"
        }
    }
}

// MARK: - AdjustSettings 扩展

extension AdjustSettings {
    /// 重置所有参数到默认值
    func resetAllParameters() {
        self.exposure = 0.0
        self.brightness = 0.0
        self.contrast = 0.0
        self.highlights = 0.0
        self.shadows = 0.0
        self.temperature = 0.0
        self.tint = 0.0
        self.saturation = 0.0
        self.fade = 0.0
        self.sharpness = 0.0
        self.vignette = 0.0
        self.chromaticAberration = 0.0
        self.grainSize = 0.0
        self.grainSaturation = 0.0
        self.hue = 0.0
        self.hslSaturation = 0.0
        self.luminance = 0.0
        self.toneBrightness = 0.0
        self.toneHue = 0.0
    }
}