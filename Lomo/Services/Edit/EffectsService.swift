// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 特效服务实现 - MVVM-S架构
/// 统一管理和协调各种特效的应用，支持依赖注入和并发安全
actor EffectsService: EffectsServiceProtocol {
    
    // MARK: - 依赖注入
    private let lightLeakService: LightLeakServiceProtocol
    private let grainService: GrainServiceProtocol
    private let scratchService: ScratchServiceProtocol
    private let storageService: StorageServiceProtocol
    
    // MARK: - 状态管理
    private var currentSettings = EffectsModel()
    private var processingProgress: Double = 0.0
    
    // MARK: - 缓存
    private var lightLeakPresets: [LightLeakPreset] = []
    private var grainPresets: [GrainPreset] = []
    private var scratchPresets: [ScratchPreset] = []
    
    // MARK: - 初始化
    init(lightLeakService: LightLeakServiceProtocol,
         grainService: GrainServiceProtocol,
         scratchService: ScratchServiceProtocol,
         storageService: StorageServiceProtocol) {
        self.lightLeakService = lightLeakService
        self.grainService = grainService
        self.scratchService = scratchService
        self.storageService = storageService
        
        print("🎨 [EffectsService] 初始化完成 - 使用MVVM-S架构")
        
        // 异步初始化
        Task {
            await loadInitialData()
        }
    }
    
    // MARK: - 设置管理
    
    func getEffectsSettings() async -> EffectsModel {
        do {
            let settings = try await storageService.loadEffectsSettings()
            currentSettings = settings
            return settings
        } catch {
            print("❌ [EffectsService] 加载设置失败: \(error)")
            return currentSettings
        }
    }
    
    func saveEffectsSettings(_ settings: EffectsModel) async throws {
        do {
            try await storageService.saveEffectsSettings(settings)
            currentSettings = settings
            print("✅ [EffectsService] 设置保存成功")
        } catch {
            print("❌ [EffectsService] 保存设置失败: \(error)")
            throw error
        }
    }
    
    func resetAllSettings() async throws {
        let defaultSettings = EffectsModel()
        try await saveEffectsSettings(defaultSettings)
        print("✅ [EffectsService] 重置为默认设置")
    }
    
    // MARK: - 漏光效果
    
    func updateLightLeakIntensity(_ intensity: Double) async {
        currentSettings.leakIntensity = intensity
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新漏光强度: \(intensity)")
    }
    
    func selectLightLeakPreset(_ preset: LightLeakPreset?) async {
        currentSettings.selectedLeakPreset = preset?.id ?? ""
        currentSettings.isLeakEnabled = preset != nil
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 选择漏光预设: \(preset?.name ?? "无")")
    }
    
    func toggleLightLeakEnabled() async {
        currentSettings.isLeakEnabled.toggle()
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 切换漏光效果: \(currentSettings.isLeakEnabled ? "开启" : "关闭")")
    }
    
    func getAvailableLightLeakPresets() async -> [LightLeakPreset] {
        if lightLeakPresets.isEmpty {
            lightLeakPresets = await lightLeakService.getAllLightLeakPresets()
        }
        return lightLeakPresets
    }
    
    // MARK: - 颗粒效果
    
    func updateGrainIntensity(_ intensity: Double) async {
        currentSettings.grainIntensity = intensity
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新颗粒强度: \(intensity)")
    }
    
    func selectGrainPreset(_ preset: GrainPreset?) async {
        currentSettings.selectedGrainPreset = preset?.id ?? ""
        currentSettings.isGrainEnabled = preset != nil
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 选择颗粒预设: \(preset?.name ?? "无")")
    }
    
    func toggleGrainEnabled() async {
        currentSettings.isGrainEnabled.toggle()
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 切换颗粒效果: \(currentSettings.isGrainEnabled ? "开启" : "关闭")")
    }
    
    func getAvailableGrainPresets() async -> [GrainPreset] {
        if grainPresets.isEmpty {
            grainPresets = await grainService.getAllGrainPresets()
        }
        return grainPresets
    }
    
    // MARK: - 划痕效果
    
    func updateScratchIntensity(_ intensity: Double) async {
        currentSettings.scratchIntensity = intensity
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新划痕强度: \(intensity)")
    }
    
    func selectScratchPreset(_ preset: ScratchPreset?) async {
        currentSettings.selectedScratchPreset = preset?.id ?? ""
        currentSettings.isScratchEnabled = preset != nil
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 选择划痕预设: \(preset?.name ?? "无")")
    }
    
    func toggleScratchEnabled() async {
        currentSettings.isScratchEnabled.toggle()
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 切换划痕效果: \(currentSettings.isScratchEnabled ? "开启" : "关闭")")
    }
    
    func getAvailableScratchPresets() async -> [ScratchPreset] {
        if scratchPresets.isEmpty {
            scratchPresets = await scratchService.getAllScratchPresets()
        }
        return scratchPresets
    }
    
    // MARK: - 时间戳效果
    
    func updateTimeStyle(_ style: String) async {
        currentSettings.selectedTimeStyle = style
        currentSettings.isTimeEnabled = !style.isEmpty
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新时间戳样式: \(style)")
    }
    
    func updateTimeColor(_ color: String) async {
        currentSettings.selectedTimeColor = color
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新时间戳颜色: \(color)")
    }
    
    func updateTimePosition(_ position: String) async {
        currentSettings.selectedTimePosition = position
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 更新时间戳位置: \(position)")
    }
    
    func toggleTimeEnabled() async {
        currentSettings.isTimeEnabled.toggle()
        await saveSettingsQuietly()
        print("🎨 [EffectsService] 切换时间戳效果: \(currentSettings.isTimeEnabled ? "开启" : "关闭")")
    }
    
    // MARK: - 图像处理
    
    func applyAllEffectsToImage(_ image: UIImage) async throws -> UIImage {
        processingProgress = 0.0
        var processedImage = image
        
        do {
            // 应用时间戳效果 (CPU处理)
            if currentSettings.isTimeEnabled && !currentSettings.selectedTimeStyle.isEmpty {
                processedImage = await applyTimestampEffect(to: processedImage)
                processingProgress = 0.25
            }
            
            // 应用漏光效果 (Metal GPU)
            if currentSettings.isLeakEnabled {
                let parameters = createLightLeakParameters()
                processedImage = await lightLeakService.applyLightLeak(to: processedImage, with: parameters)
                processingProgress = 0.5
            }
            
            // 应用颗粒效果 (Metal GPU)
            if currentSettings.isGrainEnabled {
                let parameters = createGrainParameters()
                processedImage = await grainService.applyGrain(to: processedImage, with: parameters)
                processingProgress = 0.75
            }
            
            // 应用划痕效果 (Metal GPU)
            if currentSettings.isScratchEnabled {
                let parameters = createScratchParameters()
                processedImage = await scratchService.applyScratch(to: processedImage, with: parameters)
                processingProgress = 1.0
            }
            
            print("✅ [EffectsService] 所有特效应用完成")
            return processedImage
            
        } catch {
            print("❌ [EffectsService] 特效应用失败: \(error)")
            throw EffectsError.imageProcessingFailed(error.localizedDescription)
        }
    }
    
    func applySingleEffectToImage(_ image: UIImage, effectType: EffectType) async throws -> UIImage {
        processingProgress = 0.0
        
        do {
            let result: UIImage
            
            switch effectType {
            case .timestamp:
                result = await applyTimestampEffect(to: image)
            case .lightLeak:
                let parameters = createLightLeakParameters()
                result = await lightLeakService.applyLightLeak(to: image, with: parameters)
            case .grain:
                let parameters = createGrainParameters()
                result = await grainService.applyGrain(to: image, with: parameters)
            case .scratch:
                let parameters = createScratchParameters()
                result = await scratchService.applyScratch(to: image, with: parameters)
            }
            
            processingProgress = 1.0
            print("✅ [EffectsService] \(effectType.displayName)效果应用完成")
            return result
            
        } catch {
            print("❌ [EffectsService] \(effectType.displayName)效果应用失败: \(error)")
            throw EffectsError.imageProcessingFailed(error.localizedDescription)
        }
    }
    
    func getProcessingProgress() async -> Double {
        return processingProgress
    }
    
    // MARK: - 私有方法
    
    /// 异步加载初始数据
    private func loadInitialData() async {
        // 加载设置
        currentSettings = await getEffectsSettings()
        
        // 预加载预设数据
        lightLeakPresets = await lightLeakService.getAllLightLeakPresets()
        grainPresets = await grainService.getAllGrainPresets()
        scratchPresets = await scratchService.getAllScratchPresets()
        
        print("✅ [EffectsService] 初始数据加载完成")
    }
    
    /// 静默保存设置（不抛出错误）
    private func saveSettingsQuietly() async {
        do {
            try await storageService.saveEffectsSettings(currentSettings)
        } catch {
            print("⚠️ [EffectsService] 静默保存失败: \(error)")
        }
    }
    
    /// 创建漏光参数
    private func createLightLeakParameters() -> LightLeakParameters {
        var parameters = LightLeakParameters.default
        parameters.isEnabled = currentSettings.isLeakEnabled
        parameters.intensity = currentSettings.leakIntensity
        
        // 查找选中的预设
        if !currentSettings.selectedLeakPreset.isEmpty {
            parameters.selectedPreset = lightLeakPresets.first { $0.id == currentSettings.selectedLeakPreset }
        }
        
        return parameters
    }
    
    /// 创建颗粒参数
    private func createGrainParameters() -> GrainParameters {
        var parameters = GrainParameters.default
        parameters.isEnabled = currentSettings.isGrainEnabled
        parameters.intensity = currentSettings.grainIntensity
        
        // 查找选中的预设
        if !currentSettings.selectedGrainPreset.isEmpty {
            parameters.selectedPreset = grainPresets.first { $0.id == currentSettings.selectedGrainPreset }
        }
        
        return parameters
    }
    
    /// 创建划痕参数
    private func createScratchParameters() -> ScratchParameters {
        var parameters = ScratchParameters.default
        parameters.isEnabled = currentSettings.isScratchEnabled
        parameters.intensity = currentSettings.scratchIntensity
        
        // 查找选中的预设
        if !currentSettings.selectedScratchPreset.isEmpty {
            parameters.selectedPreset = scratchPresets.first { $0.id == currentSettings.selectedScratchPreset }
        }
        
        return parameters
    }
    
    /// 应用时间戳效果 (CPU处理)
    private func applyTimestampEffect(to image: UIImage) async -> UIImage {
        // 这里应该实现时间戳叠加逻辑
        // 目前返回原图，实际实现时需要添加时间戳渲染
        print("🕐 [EffectsService] 应用时间戳效果 (样式: \(currentSettings.selectedTimeStyle))")
        return image
    }
}

// MARK: - 特效错误类型
enum EffectsError: LocalizedError, Equatable {
    case imageProcessingFailed(String)
    case invalidParameters(String)
    case serviceUnavailable(String)
    case storageError(String)
    
    var errorDescription: String? {
        switch self {
        case .imageProcessingFailed(let reason):
            return "图像处理失败: \(reason)"
        case .invalidParameters(let reason):
            return "参数无效: \(reason)"
        case .serviceUnavailable(let reason):
            return "服务不可用: \(reason)"
        case .storageError(let reason):
            return "存储错误: \(reason)"
        }
    }
}