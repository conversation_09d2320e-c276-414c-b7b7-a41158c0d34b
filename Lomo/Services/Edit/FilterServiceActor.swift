// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData
import SwiftUI
import CoreImage
import UIKit

/// 滤镜服务Actor实现 - MVVM-S架构
/// 替代原有的FilterService单例模式
actor FilterServiceActor: FilterServiceProtocol {
    
    // MARK: - 依赖注入
    
    private let renderingService: RenderingServiceProtocol
    private let storageService: StorageServiceProtocol
    private let presetManager: FilterPresetManager
    private let metalLUTProcessor: MetalLUTProcessor?
    
    // MARK: - 私有状态
    
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    private var currentParameters = FilterParameters()
    private var selectedPresetType: FilterPresetType?
    private var selectedPresetIndex: Int = -1
    private var selectedPreset: FilterPreset?
    private var hasActiveFilterState: Bool = false
    private var originalImage: UIImage?
    private var processedImage: UIImage?
    private var currentLUTPath: String?
    private var lutIntensity: Float = 1.0
    private var isLUTEnabled: Bool = false
    private var presetIntensity: Float = 1.0
    private var isProcessing: Bool = false
    
    // MARK: - 初始化
    
    init(
        renderingService: RenderingServiceProtocol,
        storageService: StorageServiceProtocol,
        modelContainer: ModelContainer? = nil,
        presetManager: FilterPresetManager = FilterPresetManager.shared
    ) {
        self.renderingService = renderingService
        self.storageService = storageService
        self.presetManager = presetManager
        
        // 设置模型容器
        if let container = modelContainer {
            self.modelContainer = container
            self.modelContext = ModelContext(container)
            print("📱 [FilterServiceActor] 使用注入的 ModelContainer")
        } else {
            // 后备方案：使用共享容器
            self.modelContainer = SharedService.shared.container
            self.modelContext = ModelContext(self.modelContainer!)
            print("📱 [FilterServiceActor] 使用共享 ModelContainer（后备方案）")
        }
        
        // 初始化Metal LUT处理器
        do {
            self.metalLUTProcessor = try MetalLUTProcessor()
            print("✅ [FilterServiceActor] Metal LUT处理器初始化成功")
        } catch {
            print("❌ [FilterServiceActor] Metal LUT处理器初始化失败: \(error)")
            self.metalLUTProcessor = nil
        }
        
        Task {
            await loadSavedState()
        }
        
        print("🎨 FilterServiceActor 初始化完成")
    }
    
    // MARK: - 私有方法
    

    
    private func loadSavedState() {
        let userDefaults = UserDefaults.standard
        
        // 加载选中的预设信息
        if let presetTypeString = userDefaults.object(forKey: "FilterService.selectedPresetType") as? String,
           let presetType = FilterPresetType(rawValue: presetTypeString) {
            let presetIndex = userDefaults.integer(forKey: "FilterService.selectedPresetIndex")
            
            selectedPresetType = presetType
            selectedPresetIndex = presetIndex
            selectedPreset = presetManager.getPreset(type: presetType, index: presetIndex)
        }
        
        hasActiveFilterState = userDefaults.bool(forKey: "FilterService.hasActiveFilter")
        
        // 加载参数
        currentParameters.exposure = userDefaults.object(forKey: "FilterService.exposure") as? Float ?? 0.0
        currentParameters.contrast = userDefaults.object(forKey: "FilterService.contrast") as? Float ?? 0.0
        currentParameters.saturation = userDefaults.object(forKey: "FilterService.saturation") as? Float ?? 0.0
        currentParameters.brightness = userDefaults.object(forKey: "FilterService.brightness") as? Float ?? 0.0
        currentParameters.temperature = userDefaults.object(forKey: "FilterService.temperature") as? Float ?? 0.0
        currentParameters.tint = userDefaults.object(forKey: "FilterService.tint") as? Float ?? 0.0
        currentParameters.highlights = userDefaults.object(forKey: "FilterService.highlights") as? Float ?? 0.0
        currentParameters.shadows = userDefaults.object(forKey: "FilterService.shadows") as? Float ?? 0.0
        currentParameters.vibrance = userDefaults.object(forKey: "FilterService.vibrance") as? Float ?? 0.0
        currentParameters.clarity = userDefaults.object(forKey: "FilterService.clarity") as? Float ?? 0.0
        currentParameters.vignetteIntensity = userDefaults.object(forKey: "FilterService.vignetteIntensity") as? Float ?? 0.0
        
        // 加载LUT相关状态
        if let lutPath = userDefaults.object(forKey: "FilterService.currentLUTPath") as? String {
            currentLUTPath = lutPath
        }
        lutIntensity = userDefaults.object(forKey: "FilterService.lutIntensity") as? Float ?? 1.0
        isLUTEnabled = userDefaults.bool(forKey: "FilterService.isLUTEnabled")
        
        print("✅ [FilterServiceActor] 状态加载完成")
    }
    
    private func saveCurrentState() {
        let userDefaults = UserDefaults.standard
        
        // 保存选中的预设信息
        if let selectedPresetType = selectedPresetType {
            userDefaults.set(selectedPresetType.rawValue, forKey: "FilterService.selectedPresetType")
            userDefaults.set(selectedPresetIndex, forKey: "FilterService.selectedPresetIndex")
        } else {
            userDefaults.removeObject(forKey: "FilterService.selectedPresetType")
            userDefaults.removeObject(forKey: "FilterService.selectedPresetIndex")
        }
        
        userDefaults.set(hasActiveFilterState, forKey: "FilterService.hasActiveFilter")
        
        // 保存参数
        userDefaults.set(currentParameters.exposure, forKey: "FilterService.exposure")
        userDefaults.set(currentParameters.contrast, forKey: "FilterService.contrast")
        userDefaults.set(currentParameters.saturation, forKey: "FilterService.saturation")
        userDefaults.set(currentParameters.brightness, forKey: "FilterService.brightness")
        userDefaults.set(currentParameters.temperature, forKey: "FilterService.temperature")
        userDefaults.set(currentParameters.tint, forKey: "FilterService.tint")
        userDefaults.set(currentParameters.highlights, forKey: "FilterService.highlights")
        userDefaults.set(currentParameters.shadows, forKey: "FilterService.shadows")
        userDefaults.set(currentParameters.vibrance, forKey: "FilterService.vibrance")
        userDefaults.set(currentParameters.clarity, forKey: "FilterService.clarity")
        userDefaults.set(currentParameters.vignetteIntensity, forKey: "FilterService.vignetteIntensity")
        
        // 保存LUT相关状态
        if let lutPath = currentLUTPath {
            userDefaults.set(lutPath, forKey: "FilterService.currentLUTPath")
        } else {
            userDefaults.removeObject(forKey: "FilterService.currentLUTPath")
        }
        userDefaults.set(lutIntensity, forKey: "FilterService.lutIntensity")
        userDefaults.set(isLUTEnabled, forKey: "FilterService.isLUTEnabled")
    }
    
    private func processCurrentImage() async {
        guard let originalImage = originalImage else {
            processedImage = nil
            isProcessing = false
            return
        }
        
        // 如果没有活跃滤镜和LUT，直接使用原图
        if !hasActiveFilterState && !isLUTEnabled {
            processedImage = originalImage
            isProcessing = false
            return
        }
        
        isProcessing = true
        
        // 保存当前参数的快照
        let parametersSnapshot = currentParameters.copy()
        let lutPathSnapshot = currentLUTPath
        let lutIntensitySnapshot = lutIntensity
        let isLUTEnabledSnapshot = isLUTEnabled
        
        // 使用Metal LUT处理器
        let result: UIImage?
        
        if let lutPath = isLUTEnabledSnapshot ? lutPathSnapshot : nil,
           let metalLUTProcessor = self.metalLUTProcessor {
            do {
                result = try metalLUTProcessor.processImage(
                    originalImage,
                    lutPath: lutPath,
                    intensity: lutIntensitySnapshot
                )
            } catch {
                print("❌ [FilterServiceActor] Metal LUT处理失败: \(error)")
                result = originalImage
            }
        } else {
            result = originalImage
        }
        
        // 只有在参数没有再次变化时才更新图像
        if currentParameters.isEqual(to: parametersSnapshot) &&
           currentLUTPath == lutPathSnapshot &&
           lutIntensity == lutIntensitySnapshot &&
           isLUTEnabled == isLUTEnabledSnapshot {
            processedImage = result
        }
        isProcessing = false
    }
    
    private func hasActiveParameters() -> Bool {
        return currentParameters.exposure != 0.0 ||
               currentParameters.contrast != 0.0 ||
               currentParameters.saturation != 0.0 ||
               currentParameters.brightness != 0.0 ||
               currentParameters.temperature != 0.0 ||
               currentParameters.tint != 0.0 ||
               currentParameters.highlights != 0.0 ||
               currentParameters.shadows != 0.0 ||
               currentParameters.vibrance != 0.0 ||
               currentParameters.clarity != 0.0 ||
               currentParameters.vignetteIntensity != 0.0
    }
    
    // MARK: - FilterServiceProtocol 实现
    
    func applyPreset(type: FilterPresetType, index: Int) async throws {
        print("🎨 [FilterServiceActor] 应用预设: \(type.rawValue) - \(index)")
        
        guard let preset = presetManager.getPreset(type: type, index: index) else {
            throw FilterServiceError.presetNotFound("未找到预设: \(type.rawValue) - \(index)")
        }
        
        // 更新选中状态
        selectedPresetType = type
        selectedPresetIndex = index
        selectedPreset = preset
        hasActiveFilterState = true
        
        // 应用预设参数
        currentParameters.applyPreset(preset, intensity: presetIntensity)
        
        // 根据滤镜类型设置渲染模式
        let renderingMode = type.renderingMode
        try await renderingService.setRenderingMode(renderingMode)
        print("🎨 滤镜类型: \(type.rawValue) → 渲染模式: \(renderingMode.displayName)")
        
        // 更新渲染器
        if originalImage != nil {
            try await renderingService.updateParameters(currentParameters)
        }
        
        // 检查是否有关联的LUT文件
        if let lutPath = CubeLUTManager.getLUTPathForPreset(type: type, index: index) {
            if FileManager.default.fileExists(atPath: lutPath) {
                currentLUTPath = lutPath
                isLUTEnabled = true
                lutIntensity = 1.0
                print("🎨 已加载关联的LUT: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
            } else {
                print("⚠️ LUT文件不存在，已清除关联: \(lutPath)")
                CubeLUTManager.clearLUTForPreset(type: type, index: index)
                currentLUTPath = nil
                isLUTEnabled = false
            }
        } else {
            currentLUTPath = nil
            isLUTEnabled = false
        }
        
        // 更新LUT
        if originalImage != nil {
            try await renderingService.applyLUT(lutPath: isLUTEnabled ? currentLUTPath : nil, intensity: lutIntensity)
        }
        
        // 保存状态
        saveCurrentState()
        
        // 处理图像
        await processCurrentImage()
        
        print("✅ [FilterServiceActor] 预设应用完成: \(preset.name)")
    }
    
    func clearPreset() async throws {
        print("🗑️ [FilterServiceActor] 清除预设")
        
        selectedPresetType = nil
        selectedPresetIndex = -1
        selectedPreset = nil
        hasActiveFilterState = false
        
        // 重置参数
        currentParameters.reset()
        
        // 清除LUT
        currentLUTPath = nil
        isLUTEnabled = false
        
        // 重置为默认渲染模式
        try await renderingService.setRenderingMode(.lightroom)
        
        // 更新渲染器
        try await renderingService.updateParameters(currentParameters)
        try await renderingService.clearLUT()
        
        // 保存状态
        saveCurrentState()
        
        // 重置图像
        processedImage = originalImage
        
        print("✅ [FilterServiceActor] 预设清除完成")
    }
    
    func isPresetSelected(type: FilterPresetType, index: Int) async -> Bool {
        return selectedPresetType == type && selectedPresetIndex == index
    }
    
    func getPresets(for type: FilterPresetType) async -> [FilterPreset] {
        return presetManager.getPresets(for: type)
    }
    
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) async throws {
        print("🔧 [FilterServiceActor] updateParameter - keyPath: \(keyPath), value: \(value)")
        
        // 参数验证
        if let floatValue = value as? Float {
            guard floatValue.isFinite else {
                throw FilterServiceError.invalidParameter("无效的Float参数值: \(floatValue)")
            }
        }
        
        // 保存旧值用于回滚
        let oldValue = currentParameters[keyPath: keyPath]
        
        // 更新参数
        currentParameters[keyPath: keyPath] = value
        
        // 验证参数范围
        currentParameters.validateAndClampParameters()
        
        // 特殊验证：色调分离参数
        if keyPath == \.highlightHue || keyPath == \.highlightSaturation ||
           keyPath == \.shadowHue || keyPath == \.shadowSaturation ||
           keyPath == \.splitToningBalance {
            
            if !currentParameters.validateSplitToningParameters() {
                print("⚠️ [FilterServiceActor] 色调分离参数验证失败，回滚到旧值")
                currentParameters[keyPath: keyPath] = oldValue
                return
            }
        }
        
        // 标记为有活跃滤镜
        hasActiveFilterState = true
        
        // 确保使用正确的渲染模式
        let renderingMode = selectedPresetType?.renderingMode ?? .lightroom
        try await renderingService.setRenderingMode(renderingMode)
        
        // 更新渲染器
        if originalImage != nil {
            try await renderingService.updateParameters(currentParameters)
        }
        
        // 清除具体预设选择状态
        if selectedPreset != nil {
            selectedPreset = nil
            selectedPresetIndex = -1
        }
        
        // 保存状态
        saveCurrentState()
        
        // 处理图像
        await processCurrentImage()
        
        print("✅ [FilterServiceActor] 参数更新完成")
    }
    
    func batchUpdateParameters(_ updates: @escaping () -> Void) async throws {
        print("🔧 [FilterServiceActor] batchUpdateParameters 开始")
        
        updates()
        
        // 标记为有活跃滤镜
        hasActiveFilterState = true
        
        // 更新渲染器
        try await renderingService.updateParameters(currentParameters)
        
        print("✅ [FilterServiceActor] 批量参数更新完成")
    }
    
    func getCurrentParameters() async -> FilterParameters {
        return currentParameters
    }
    
    func resetParameters() async throws {
        print("🔄 [FilterServiceActor] 重置参数")
        
        currentParameters.reset()
        try await clearPreset()
        
        print("✅ [FilterServiceActor] 参数重置完成")
    }
    
    func applyLUT(lutPath: String, intensity: Float) async throws {
        print("🎨 [FilterServiceActor] 应用LUT: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
        
        currentLUTPath = lutPath
        lutIntensity = max(0.0, min(1.0, intensity))
        isLUTEnabled = true
        hasActiveFilterState = true
        
        // 保存状态
        saveCurrentState()
        
        // 处理图像
        await processCurrentImage()
        
        print("✅ [FilterServiceActor] LUT应用完成")
    }
    
    func updateLUTIntensity(_ intensity: Float) async throws {
        lutIntensity = max(0.0, min(1.0, intensity))
        
        saveCurrentState()
        await processCurrentImage()
        
        print("🔧 [FilterServiceActor] LUT强度更新: \(Int(lutIntensity * 100))%")
    }
    
    func updatePresetIntensity(_ intensity: Float) async throws {
        presetIntensity = max(0.0, min(1.0, intensity))
        
        // 如果有选中的预设，重新应用预设参数
        if let preset = selectedPreset {
            currentParameters.applyPreset(preset, intensity: presetIntensity)
            currentParameters.validateAndClampParameters()
        }
        
        saveCurrentState()
        await processCurrentImage()
        
        print("🔧 [FilterServiceActor] 预设强度更新: \(Int(presetIntensity * 100))%")
    }
    
    func toggleLUT() async throws {
        isLUTEnabled.toggle()
        
        if !isLUTEnabled && !hasActiveParameters() {
            hasActiveFilterState = false
        } else {
            hasActiveFilterState = true
        }
        
        saveCurrentState()
        await processCurrentImage()
        
        print(isLUTEnabled ? "✅ [FilterServiceActor] LUT已启用" : "❌ [FilterServiceActor] LUT已禁用")
    }
    
    func clearLUT() async throws {
        currentLUTPath = nil
        isLUTEnabled = false
        
        if !hasActiveParameters() {
            hasActiveFilterState = false
        }
        
        saveCurrentState()
        await processCurrentImage()
        
        print("🗑️ [FilterServiceActor] LUT已清除")
    }
    
    func setOriginalImage(_ image: UIImage) async throws {
        print("🔧 [FilterServiceActor] 设置原始图像 - 尺寸: \(image.size)")
        
        originalImage = image
        
        // 分析图像格式和色彩空间
        let colorSpaceInfo = ImageFormatProcessor.shared.analyzeColorSpace(image)
        let isLinear = colorSpaceInfo.isLinear || ImageColorSpaceAnalyzer.isLinearImage(image)
        currentParameters.isLinearImage = isLinear
        
        // 设置到渲染服务
        try await renderingService.setBaseImage(image)
        try await renderingService.updateParameters(currentParameters)
        try await renderingService.applyLUT(lutPath: isLUTEnabled ? currentLUTPath : nil, intensity: lutIntensity)
        
        // 处理图像
        await processCurrentImage()
        
        print("✅ [FilterServiceActor] 原始图像设置完成")
    }
    
    func getCurrentDisplayImage() async -> UIImage? {
        return processedImage ?? originalImage
    }
    
    func getRealtimePreview() async -> UIImage? {
        return await renderingService.getRealtimePreview()
    }
    
    func getFinalOutputImage(highQuality: Bool) async -> UIImage? {
        return await renderingService.getFinalOutputImage(highQuality: highQuality)
    }
    
    func hasUnsavedChanges() async -> Bool {
        return hasActiveFilterState || isLUTEnabled
    }
    
    func hasActiveFilter() async -> Bool {
        return hasActiveFilterState
    }
    
    func getSettings() async -> FilterSettings {
        guard let context = modelContext else {
            return FilterSettings()
        }
        
        do {
            let descriptor = FetchDescriptor<FilterSettings>(
                predicate: #Predicate { $0.id == "filter_settings" }
            )
            let existingSettings = try context.fetch(descriptor)
            
            if let settings = existingSettings.first {
                return settings
            }
            
            let newSettings = FilterSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("❌ [FilterServiceActor] 获取滤镜设置失败: \(error)")
            return FilterSettings()
        }
    }
    
    func saveSettings(_ settings: FilterSettings) async throws {
        guard let context = modelContext else {
            throw FilterServiceError.contextUnavailable
        }
        
        do {
            settings.updateTimestamp()
            try context.save()
            print("✅ [FilterServiceActor] 设置保存成功")
        } catch {
            print("❌ [FilterServiceActor] 保存滤镜设置失败: \(error)")
            throw FilterServiceError.saveFailed(error)
        }
    }
    
    func resetToDefaults() async throws {
        guard let context = modelContext else {
            throw FilterServiceError.contextUnavailable
        }
        
        do {
            let descriptor = FetchDescriptor<FilterSettings>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }
            
            let newSettings = FilterSettings()
            context.insert(newSettings)
            try context.save()
            
            print("✅ [FilterServiceActor] 重置到默认值完成")
        } catch {
            print("❌ [FilterServiceActor] 重置滤镜设置失败: \(error)")
            throw FilterServiceError.resetFailed(error)
        }
    }
}

// MARK: - 错误定义

enum FilterServiceError: LocalizedError {
    case presetNotFound(String)
    case invalidParameter(String)
    case contextUnavailable
    case saveFailed(Error)
    case resetFailed(Error)
    
    var errorDescription: String? {
        switch self {
        case .presetNotFound(let message):
            return "预设未找到: \(message)"
        case .invalidParameter(let message):
            return "参数错误: \(message)"
        case .contextUnavailable:
            return "数据上下文不可用"
        case .saveFailed(let error):
            return "保存失败: \(error.localizedDescription)"
        case .resetFailed(let error):
            return "重置失败: \(error.localizedDescription)"
        }
    }
}