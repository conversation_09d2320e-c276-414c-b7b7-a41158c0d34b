// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData
import SwiftUI

// MARK: - 构图服务协议
/// 构图业务服务协议，定义所有构图相关的业务操作
protocol CropServiceProtocol {
    func getSettings() -> CropModel
    func saveSettings(_ settings: CropModel)
    func updateSetting<T>(_ keyPath: WritableKeyPath<CropModel, T>, value: T)
    func resetToDefaults()
}

/// 构图设置服务 - MVVM-S架构实现
class CropService: CropServiceProtocol {
    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?

    // 初始化方法
    init() {
        setupModelContainer()
    }

    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print("🎨 CropService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print("❌ CropService: 获取共享 ModelContainer 失败！")
        }
    }

    // MARK: - 公共方法

    /// 获取构图设置
    func getSettings() -> CropModel {
        guard let context = modelContext else {
            return CropModel()
        }

        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<CropModel>(predicate: #Predicate<CropModel> { $0.id == "crop_settings" })
            let existingSettings = try context.fetch(descriptor)

            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }

            // 如果不存在，创建新的设置并保存
            let newSettings = CropModel()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取构图设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return CropModel()
        }
    }

    /// 保存设置
    func saveSettings(_ settings: CropModel) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }

        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存构图设置失败: \(error.localizedDescription)")
        }
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<CropModel, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }

    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }

        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<CropModel>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }

            // 创建新的默认设置
            let newSettings = CropModel()
            context.insert(newSettings)
            try context.save()
        } catch {
            print("重置构图设置失败: \(error.localizedDescription)")
        }
    }
}
