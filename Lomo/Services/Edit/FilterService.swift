import Foundation
import SwiftData
import SwiftUI
import CoreImage
import Combine

/// 滤镜应用模块的业务服务 - MVVM-S架构
/// 复制自：FilterSettingsManager.swift + FilterStateManager.swift
class FilterService: ObservableObject {

    // MARK: - 单例
    static let shared = FilterService()

    // MARK: - 滤镜设置管理 (从FilterSettingsManager.swift完整复制)

    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?

    // MARK: - 初始化
    init(sharedService: SharedService = SharedService.shared) {
        self.sharedService = sharedService
        setupModelContainer()
        loadSavedState()
        print("🎨 FilterService 初始化完成")
    }

    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print("📱 FilterService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print("❌ FilterService: 获取共享 ModelContainer 失败！")
        }
    }

    // MARK: - 滤镜设置公共方法 (从FilterSettingsManager.swift完整复制)

    /// 获取滤镜设置
    func getSettings() -> FilterSettings {
        guard let context = modelContext else {
            return FilterSettings()
        }

        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<FilterSettings>(predicate: #Predicate { $0.id == "filter_settings" })
            let existingSettings = try context.fetch(descriptor)

            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }

            // 如果不存在，创建新的设置并保存
            let newSettings = FilterSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取滤镜设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return FilterSettings()
        }
    }

    /// 保存设置
    func saveSettings(_ settings: FilterSettings) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }

        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存滤镜设置失败: \(error.localizedDescription)")
        }
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<FilterSettings, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }

    /// 添加到最近使用的滤镜
    func addToRecentFilters(_ filter: String) {
        var settings = getSettings()
        settings.addToRecentFilters(filter)
        saveSettings(settings)
    }

    /// 切换滤镜收藏状态
    func toggleFavorite(_ filter: String) {
        var settings = getSettings()
        settings.toggleFavorite(filter)
        saveSettings(settings)
    }

    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }

        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<FilterSettings>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }

            // 创建新的默认设置
            let newSettings = FilterSettings()
            context.insert(newSettings)
            try context.save()
        } catch {
            print("重置滤镜设置失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 滤镜状态管理 (从FilterStateManager.swift完整复制)

    // MARK: - 滤镜当前状态

    /// 当前滤镜参数
    @Published var currentParameters = FilterParameters()

    /// 当前选中的预设类型
    @Published var selectedPresetType: FilterPresetType? = nil

    /// 当前选中的预设索引
    @Published var selectedPresetIndex: Int = -1

    /// 当前选中的预设
    @Published var selectedPreset: FilterPreset? = nil

    /// 是否有活跃的滤镜效果
    @Published var hasActiveFilter: Bool = false

    /// 原始图像
    @Published var originalImage: UIImage? = nil

    /// 处理后的图像
    @Published var processedImage: UIImage? = nil

    /// 当前使用的LUT文件路径
    @Published var currentLUTPath: String? = nil

    /// LUT强度 (0.0-1.0)
    @Published var lutIntensity: Float = 1.0

    /// 是否启用LUT
    @Published var isLUTEnabled: Bool = false

    /// 预设强度 (0.0-1.0) - 控制预设参数的整体强度
    @Published var presetIntensity: Float = 1.0

    /// 是否正在处理图像
    @Published var isProcessing: Bool = false

    // MARK: - 滤镜预设管理器
    private let presetManager = FilterPresetManager.shared

    // MARK: - 滤镜LUT处理器 (完全Metal实现)
    private lazy var metalLUTProcessor: MetalLUTProcessor? = {
        do {
            let processor = try MetalLUTProcessor()
            print("✅ [FilterService] Metal LUT处理器初始化成功")

            // 运行Metal LUT测试
            #if DEBUG
            MetalLUTTest.runAllTests()
            MetalSpecialEffectsTest.runAllTests()
            // WatermarkMetalRenderingTest.runAllTests() // 已删除测试
            #endif

            return processor
        } catch {
            print("❌ [FilterService] Metal LUT处理器初始化失败: \(error)")

            // 运行Metal诊断测试
            #if DEBUG
            MetalLUTTest.testMetalSupport()
            MetalLUTTest.testShaderCompilation()
            MetalSpecialEffectsTest.testEngineInitialization()
            MetalSpecialEffectsTest.testShaderCompilation()
            #endif

            return nil
        }
    }()

    // MARK: - 滤镜渲染器管理

    /// Metal实时渲染器 (sRGB处理管线)
    private let metalRenderer = MetalFilterRenderer.shared

    /// 高精度Metal渲染器 (RAW/线性空间处理管线)
    private lazy var highPrecisionRenderer: HighPrecisionMetalRenderer? = {
        do {
            return try HighPrecisionMetalRenderer()
        } catch {
            print("❌ [FilterService] 高精度渲染器初始化失败: \(error)")
            return nil
        }
    }()

    /// RAW数据管理器 (注入依赖)
    private let sharedService: SharedService

    /// 当前渲染模式
    var currentRenderingMode: RenderingMode {
        if let selectedPresetType = selectedPresetType {
            return selectedPresetType.renderingMode
        } else {
            return .lightroom  // 默认使用Lightroom模式
        }
    }

    // MARK: - 滤镜预设操作

    /// 应用预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    func applyPreset(type: FilterPresetType, index: Int) {
        guard let preset = presetManager.getPreset(type: type, index: index) else {
            print("未找到预设: \(type.rawValue) - \(index)")
            return
        }

        // 更新选中状态
        selectedPresetType = type
        selectedPresetIndex = index
        selectedPreset = preset
        hasActiveFilter = true

        // 应用预设参数（考虑强度）
        currentParameters.applyPreset(preset, intensity: presetIntensity)

        // 自动设置高光保护模式和强度
        applyAutomaticHighlightProtection(for: type)

        // 根据滤镜类型设置渲染模式
        let renderingMode = type.renderingMode
        metalRenderer.setRenderingMode(renderingMode)
        print("🎨 滤镜类型: \(type.rawValue) → 渲染模式: \(renderingMode.displayName)")

        // 实时更新Metal渲染器 - 立即生效（仅当有图像时）
        if originalImage != nil {
            metalRenderer.updateParameters(currentParameters)
        } else {
            print("⚠️ 没有原始图像，跳过Metal渲染器更新")
        }

        // 检查是否有关联的LUT文件
        if let lutPath = CubeLUTManager.getLUTPathForPreset(type: type, index: index) {
            // 验证LUT文件是否存在
            if FileManager.default.fileExists(atPath: lutPath) {
                currentLUTPath = lutPath
                isLUTEnabled = true
                lutIntensity = 1.0 // 默认100%强度
                print("🎨 已加载关联的LUT: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
            } else {
                print("⚠️ LUT文件不存在，已清除关联: \(lutPath)")
                CubeLUTManager.clearLUTForPreset(type: type, index: index)
                currentLUTPath = nil
                isLUTEnabled = false
            }
        } else {
            // 没有关联的LUT，清除当前LUT状态
            currentLUTPath = nil
            isLUTEnabled = false
        }

        // 实时更新LUT（仅当有图像时）
        if originalImage != nil {
            metalRenderer.applyLUT(lutPath: isLUTEnabled ? currentLUTPath : nil, intensity: lutIntensity)
        }

        // 保存状态
        saveCurrentState()

        // 保留异步处理用于导出
        processCurrentImage()

        print("✅ 已应用预设: \(preset.name)")
        if isLUTEnabled {
            print("   包含LUT效果")
        }
    }

    /// 清除当前预设
    func clearPreset() {
        selectedPresetType = nil
        selectedPresetIndex = -1
        selectedPreset = nil
        hasActiveFilter = false

        // 重置参数
        currentParameters.reset()

        // 重置高光保护为默认模式
        resetHighlightProtectionToDefault()

        // 清除LUT
        currentLUTPath = nil
        isLUTEnabled = false

        // 清除滤镜后，重置为Lightroom模式
        metalRenderer.setRenderingMode(.lightroom)
        print("🎨 清除滤镜，重置为专业调整模式")

        // 实时更新Metal渲染器 - 显示原图
        metalRenderer.updateParameters(currentParameters)
        metalRenderer.applyLUT(lutPath: nil, intensity: lutIntensity)

        // 保存状态
        saveCurrentState()

        // 使用异步调度重置图像
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.processedImage = originalImage
        }

        print("✅ 已清除预设，显示原图")
    }

    /// 检查是否选中了指定预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: 是否选中
    func isPresetSelected(type: FilterPresetType, index: Int) -> Bool {
        return selectedPresetType == type && selectedPresetIndex == index
    }

    /// 更新LUT强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updateLUTIntensity(_ intensity: Float) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.lutIntensity = max(0.0, min(1.0, intensity))
        }

        saveCurrentState()
        processCurrentImage()
    }

    /// 更新预设强度
    /// - Parameter intensity: 新的强度值 (0.0-1.0)
    func updatePresetIntensity(_ intensity: Float) {
        presetIntensity = max(0.0, min(1.0, intensity))

        // 如果有选中的预设，重新应用预设参数
        if let preset = selectedPreset {
            currentParameters.applyPreset(preset, intensity: presetIntensity)
            currentParameters.validateAndClampParameters()
        }

        saveCurrentState()
        processCurrentImage()
    }

    // MARK: - 滤镜参数操作

    /// 批量更新参数（避免多次渲染器调用）
    func batchUpdateParameters(_ updates: () -> Void) {
        updates()

        // 立即标记为有活跃滤镜
        hasActiveFilter = true

        // 更新渲染器
        updateRenderersWithCurrentParameters()
    }

    /// 更新单个参数
    /// - Parameters:
    ///   - keyPath: 参数路径
    ///   - value: 新值
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) {
        print("🔧🔧🔧 [FilterService] updateParameter开始 - keyPath: \(keyPath), value: \(value)")

        // 参数验证 - 特殊处理Float类型
        if let floatValue = value as? Float {
            guard floatValue.isFinite else {
                print("⚠️ [FilterService] 无效的Float参数值: \(floatValue)")
                return
            }
        }

        // 保存旧值用于回滚
        let oldValue = currentParameters[keyPath: keyPath]
        print("🔧 [FilterService] 旧值: \(oldValue) -> 新值: \(value)")

        // 更新参数
        currentParameters[keyPath: keyPath] = value
        print("🔧 [FilterService] 参数已更新到currentParameters")

        // 验证参数范围
        currentParameters.validateAndClampParameters()
        let finalValue = currentParameters[keyPath: keyPath]
        print("🔧 [FilterService] 验证后最终值: \(finalValue)")

        // 特殊验证：色调分离参数
        if keyPath == \.highlightHue || keyPath == \.highlightSaturation ||
           keyPath == \.shadowHue || keyPath == \.shadowSaturation ||
           keyPath == \.splitToningBalance {

            if !currentParameters.validateSplitToningParameters() {
                print("⚠️ [FilterService] 色调分离参数验证失败，回滚到旧值")
                currentParameters[keyPath: keyPath] = oldValue
                return
            }
        }

        // 立即标记为有活跃滤镜（同步更新）
        hasActiveFilter = true
        print("🔧 [FilterService] hasActiveFilter设为true")

        // 确保使用正确的渲染模式
        let renderingMode = selectedPresetType?.renderingMode ?? .lightroom
        print("🔧 [FilterService] 当前渲染模式: \(renderingMode.displayName)")
        metalRenderer.setRenderingMode(renderingMode)

        // 实时更新渲染器 - 根据处理管线类型选择
        if originalImage != nil {
            print("🔧 [FilterService] 有原始图像，更新渲染器...")
            updateRenderersWithCurrentParameters()
        } else {
            print("⚠️ 没有原始图像，跳过参数更新")
        }

        // 清除具体预设选择状态（因为参数已被手动修改），但保持滤镜类型以维持正确的算法
        if selectedPreset != nil {
            selectedPreset = nil
            selectedPresetIndex = -1
            // 注意：保持selectedPresetType不变，以确保算法模式正确
            print("🎨 参数手动修改，清除具体预设但保持滤镜类型: \(selectedPresetType?.rawValue ?? "无")")
        }

        // 保存状态
        saveCurrentState()

        // 保留异步处理用于导出
        processCurrentImage()
    }

    /// 重置所有参数
    func resetParameters() {
        currentParameters.reset()
        clearPreset()
    }

    // MARK: - 滤镜HSL操作

    /// 更新HSL选中的颜色范围索引
    /// - Parameter index: 颜色范围索引 (0-7)
    func updateHSLColorIndex(_ index: Int) {
        let clampedIndex = max(0, min(7, index))
        updateParameter(\.selectedHSLColorIndex, value: clampedIndex)
        print("🎨 [HSL] 更新颜色范围索引: \(clampedIndex) (\(getHSLColorName(for: clampedIndex)))")
    }

    /// 更新HSL色相调整
    /// - Parameter value: 色相值 (-180 到 +180 度)
    func updateHSLHue(_ value: Float) {
        print("🎨🎨🎨 [HSL] updateHSLHue开始: \(value)° (颜色索引: \(currentParameters.selectedHSLColorIndex))")
        updateParameter(\.hue, value: value)
        print("🎨 [HSL] 更新色相完成: \(value)° (颜色: \(getHSLColorName(for: currentParameters.selectedHSLColorIndex)))")
        print("🎨 [HSL] 当前参数状态: 色相=\(currentParameters.hue)°, 饱和度=\(currentParameters.hslSaturation), 明度=\(currentParameters.hslLuminance)")
    }

    /// 更新HSL饱和度调整
    /// - Parameter value: 饱和度值 (-100 到 +100)
    func updateHSLSaturation(_ value: Float) {
        print("🎨🎨🎨 [HSL] updateHSLSaturation开始: \(value) (颜色索引: \(currentParameters.selectedHSLColorIndex))")
        updateParameter(\.hslSaturation, value: value)
        print("🎨 [HSL] 更新饱和度完成: \(value) (颜色: \(getHSLColorName(for: currentParameters.selectedHSLColorIndex)))")
        print("🎨 [HSL] 当前参数状态: 色相=\(currentParameters.hue)°, 饱和度=\(currentParameters.hslSaturation), 明度=\(currentParameters.hslLuminance)")
    }

    /// 更新HSL明度调整
    /// - Parameter value: 明度值 (-100 到 +100)
    func updateHSLLuminance(_ value: Float) {
        print("🎨🎨🎨 [HSL] updateHSLLuminance开始: \(value) (颜色索引: \(currentParameters.selectedHSLColorIndex))")
        updateParameter(\.hslLuminance, value: value)
        print("🎨 [HSL] 更新明度完成: \(value) (颜色: \(getHSLColorName(for: currentParameters.selectedHSLColorIndex)))")
        print("🎨 [HSL] 当前参数状态: 色相=\(currentParameters.hue)°, 饱和度=\(currentParameters.hslSaturation), 明度=\(currentParameters.hslLuminance)")
    }

    /// 重置当前选中颜色的HSL调整
    func resetCurrentHSLColor() {
        updateParameter(\.hue, value: 0.0)
        updateParameter(\.hslSaturation, value: 0.0)
        updateParameter(\.hslLuminance, value: 0.0)
        print("🎨 [HSL] 重置颜色: \(getHSLColorName(for: currentParameters.selectedHSLColorIndex))")
    }

    /// 获取HSL颜色范围名称
    /// - Parameter index: 颜色索引
    /// - Returns: 颜色名称
    private func getHSLColorName(for index: Int) -> String {
        let colorNames = ["红色", "橙色", "黄色", "绿色", "青色", "蓝色", "紫色", "品红"]
        return index >= 0 && index < colorNames.count ? colorNames[index] : "未知"
    }

    // MARK: - 滤镜LUT操作

    /// 应用LUT文件
    /// - Parameters:
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度（0.0-1.0）
    func applyLUT(lutPath: String, intensity: Float = 1.0) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.currentLUTPath = lutPath
            self.lutIntensity = max(0.0, min(1.0, intensity))
            self.isLUTEnabled = true
            self.hasActiveFilter = true

            print("✅ 已应用LUT: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
            print("   强度: \(Int(self.lutIntensity * 100))%")
        }

        // 保存状态
        saveCurrentState()

        // 处理图像
        processCurrentImage()
    }

    /// 调整LUT强度
    /// - Parameter intensity: 新的强度值（0.0-1.0）
    func adjustLUTIntensity(_ intensity: Float) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.lutIntensity = max(0.0, min(1.0, intensity))

            if self.isLUTEnabled {
                print("🔧 LUT强度调整为: \(Int(self.lutIntensity * 100))%")
            }
        }

        if isLUTEnabled {
            // 保存状态
            saveCurrentState()

            // 重新处理图像
            processCurrentImage()
        }
    }

    /// 切换LUT启用状态
    func toggleLUT() {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.isLUTEnabled.toggle()

            if !self.isLUTEnabled && !self.hasActiveParameters() {
                self.hasActiveFilter = false
            } else {
                self.hasActiveFilter = true
            }

            print(self.isLUTEnabled ? "✅ 已启用LUT" : "❌ 已禁用LUT")
        }

        // 保存状态
        saveCurrentState()

        // 处理图像
        processCurrentImage()
    }

    /// 清除LUT
    func clearLUT() {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.currentLUTPath = nil
            self.isLUTEnabled = false

            if !self.hasActiveParameters() {
                self.hasActiveFilter = false
            }

            print("🗑️ 已清除LUT")
        }

        // 保存状态
        saveCurrentState()

        // 处理图像
        processCurrentImage()
    }

    /// 检查是否有活跃的参数
    private func hasActiveParameters() -> Bool {
        return currentParameters.exposure != 0.0 ||
               currentParameters.contrast != 0.0 ||
               currentParameters.saturation != 0.0 ||
               currentParameters.brightness != 0.0 ||
               currentParameters.temperature != 0.0 ||
               currentParameters.tint != 0.0 ||
               currentParameters.highlights != 0.0 ||
               currentParameters.shadows != 0.0 ||
               currentParameters.vibrance != 0.0 ||
               currentParameters.clarity != 0.0 ||
               currentParameters.sharpness != 0.0 ||
               currentParameters.vignetteIntensity != 0.0
    }

    // MARK: - 滤镜图像处理

    /// 设置原始图像
    /// - Parameter image: 原始图像
    func setOriginalImage(_ image: UIImage) {
        print("🔧 [DEBUG] FilterService.setOriginalImage() 开始 - 图像尺寸: \(image.size)")

        originalImage = image

        // 分析图像格式和色彩空间
        let colorSpaceInfo = ImageFormatProcessor.shared.analyzeColorSpace(image)
        print("🔧 [DEBUG] 图像分析结果: \(colorSpaceInfo.description)")

        // 检测是否需要特殊处理
        let requiresSpecialProcessing = ImageFormatProcessor.shared.requiresSpecialProcessing(image)
        print("🔧 [DEBUG] 是否需要特殊处理: \(requiresSpecialProcessing ? "是" : "否")")

        // 自动检测是否为线性图像
        let isLinear = colorSpaceInfo.isLinear || ImageColorSpaceAnalyzer.isLinearImage(image)
        currentParameters.isLinearImage = isLinear

        print("🔧 [DEBUG] 线性图像检测: colorSpaceInfo.isLinear=\(colorSpaceInfo.isLinear), ImageColorSpaceAnalyzer.isLinearImage=\(ImageColorSpaceAnalyzer.isLinearImage(image)), 最终结果=\(isLinear)")

        // 设置RAW数据管理器
        let isRAWData = sharedService.setImageData(image)
        print("🔧 [DEBUG] RAW数据设置: \(isRAWData ? "成功" : "失败")")

        if isLinear && isRAWData {
            print("🎨 检测到RAW/线性图像，使用高精度处理管线")

            // 使用高精度渲染器处理RAW数据
            if let highPrecisionRenderer = highPrecisionRenderer {
                do {
                    try highPrecisionRenderer.setInputImage(image, isLinear: isLinear)
                    print("🔧 [DEBUG] 高精度渲染器设置完成")
                } catch {
                    print("❌ [DEBUG] 高精度渲染器设置失败: \(error)")
                    // 回退到标准处理管线
                    setupStandardPipeline(image: image)
                }
            } else {
                print("⚠️ [DEBUG] 高精度渲染器不可用，回退到标准处理管线")
                setupStandardPipeline(image: image)
            }
        } else {
            print("🎨 检测到sRGB图像，使用标准处理管线")
            setupStandardPipeline(image: image)
        }

        // 保留异步处理用于导出
        processCurrentImage()
        print("🔧 [DEBUG] FilterService.setOriginalImage() 完成")
    }

    /// 设置标准处理管线
    private func setupStandardPipeline(image: UIImage) {
        print("🔧 [DEBUG] 开始设置标准Metal渲染器")

        // 设置到Metal渲染器 - 立即生效（内部会进行智能预处理）
        metalRenderer.setBaseImage(image)
        print("🔧 [DEBUG] metalRenderer.setBaseImage() 完成")

        metalRenderer.updateParameters(currentParameters)
        print("🔧 [DEBUG] metalRenderer.updateParameters() 完成")

        metalRenderer.applyLUT(lutPath: isLUTEnabled ? currentLUTPath : nil, intensity: lutIntensity)
        print("🔧 [DEBUG] metalRenderer.applyLUT() 完成")
    }

    /// 更新渲染器参数 - 根据处理管线类型选择
    private func updateRenderersWithCurrentParameters() {
        print("🔧🔧🔧 [FilterService] updateRenderersWithCurrentParameters开始")
        print("🔧 [FilterService] sharedService.isLinearProcessing: \(sharedService.isLinearProcessing)")

        if sharedService.isLinearProcessing {
            print("🔧 [DEBUG] 更新高精度渲染器参数")
            print("⚠️⚠️⚠️ [FilterService] 警告：使用高精度渲染器，HSL功能可能不支持！")
            // RAW/线性空间处理 - 使用高精度渲染器
            // 注意：高精度渲染器的更新在实时预览时进行
        } else {
            print("🔧 [DEBUG] 更新标准Metal渲染器参数")
            print("✅ [FilterService] 使用标准Metal渲染器，HSL功能应该正常工作")
            // sRGB处理 - 使用标准Metal渲染器
            metalRenderer.updateParameters(currentParameters)
        }

        print("🔧🔧🔧 [FilterService] updateRenderersWithCurrentParameters完成")
    }

    /// 处理当前图像
    private func processCurrentImage() {
        guard let originalImage = originalImage else {
            // 使用异步调度避免在视图更新中修改@Published属性
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.processedImage = nil
                self.isProcessing = false
            }
            return
        }

        // 如果没有活跃滤镜和LUT，直接使用原图
        print("🔧 [DEBUG] processCurrentImage() - hasActiveFilter=\(hasActiveFilter), isLUTEnabled=\(isLUTEnabled)")
        if !hasActiveFilter && !isLUTEnabled {
            print("🔧 [DEBUG] 无活跃滤镜和LUT，直接使用原图")
            // 使用异步调度避免在视图更新中修改@Published属性
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.processedImage = originalImage
                self.isProcessing = false
            }
            return
        } else {
            print("🔧 [DEBUG] 有活跃滤镜或LUT，需要处理图像")
        }

        // 使用异步调度设置处理状态
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.isProcessing = true
        }

        // 保存当前参数的快照，避免处理过程中参数被修改
        let parametersSnapshot = currentParameters.copy()
        let lutPathSnapshot = currentLUTPath
        let lutIntensitySnapshot = lutIntensity
        let isLUTEnabledSnapshot = isLUTEnabled

        // 使用Metal LUT处理器 (完全Metal实现)
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }

            let result: UIImage?

            if let lutPath = isLUTEnabledSnapshot ? lutPathSnapshot : nil,
               let metalLUTProcessor = self.metalLUTProcessor {
                // 使用Metal LUT处理器
                do {
                    result = try metalLUTProcessor.processImage(
                        originalImage,
                        lutPath: lutPath,
                        intensity: lutIntensitySnapshot
                    )
                } catch {
                    print("❌ [FilterService] Metal LUT处理失败: \(error)")
                    result = originalImage
                }
            } else {
                // 无LUT或Metal LUT处理器不可用
                result = originalImage
            }

            DispatchQueue.main.async {
                // 只有在参数没有再次变化时才更新图像，避免过时的结果覆盖新的处理
                if self.currentParameters.isEqual(to: parametersSnapshot) &&
                   self.currentLUTPath == lutPathSnapshot &&
                   self.lutIntensity == lutIntensitySnapshot &&
                   self.isLUTEnabled == isLUTEnabledSnapshot {
                    self.processedImage = result
                }
                self.isProcessing = false
            }
        }
    }

    /// 获取当前显示的图像
    func getCurrentDisplayImage() -> UIImage? {
        let result = processedImage ?? originalImage
        print("🔧 [DEBUG] getCurrentDisplayImage() - processedImage=\(processedImage != nil ? "有" : "nil"), originalImage=\(originalImage != nil ? "有" : "nil"), 返回=\(result != nil ? "有图像" : "nil")")
        return result
    }

    /// 获取实时预览图像 - 根据处理管线类型选择
    func getRealtimePreview() -> UIImage? {
        guard let originalImage = originalImage else { return nil }

        if sharedService.isLinearProcessing {
            // RAW/线性空间处理 - 使用高精度渲染器生成预览
            if let highPrecisionRenderer = highPrecisionRenderer {
                do {
                    return try highPrecisionRenderer.processForPreview(currentParameters)
                } catch {
                    print("❌ [FilterService] 高精度预览生成失败: \(error)")
                    // 回退到标准预览
                    return metalRenderer.currentOutputImage
                }
            }
        }

        // sRGB处理 - 使用标准Metal渲染器
        return metalRenderer.currentOutputImage
    }

    /// 获取最终输出图像 - 高质量处理
    func getFinalOutputImage(highQuality: Bool = true) -> UIImage? {
        guard let originalImage = originalImage else { return nil }

        if sharedService.isLinearProcessing {
            // RAW/线性空间处理 - 使用高精度渲染器生成最终输出
            if let highPrecisionRenderer = highPrecisionRenderer {
                do {
                    return try highPrecisionRenderer.processForFinalOutput(currentParameters, highQuality: highQuality)
                } catch {
                    print("❌ [FilterService] 高精度最终输出生成失败: \(error)")
                    // 回退到标准处理
                    return getCurrentDisplayImage()
                }
            }
        }

        // sRGB处理 - 使用当前处理结果
        return getCurrentDisplayImage()
    }

    // MARK: - 滤镜状态持久化

    /// 保存当前状态
    private func saveCurrentState() {
        let userDefaults = UserDefaults.standard

        // 保存选中的预设信息
        if let selectedPresetType = selectedPresetType {
            userDefaults.set(selectedPresetType.rawValue, forKey: "FilterService.selectedPresetType")
            userDefaults.set(selectedPresetIndex, forKey: "FilterService.selectedPresetIndex")
        } else {
            userDefaults.removeObject(forKey: "FilterService.selectedPresetType")
            userDefaults.removeObject(forKey: "FilterService.selectedPresetIndex")
        }

        userDefaults.set(hasActiveFilter, forKey: "FilterService.hasActiveFilter")

        // 保存参数（简化版，只保存关键参数）
        userDefaults.set(currentParameters.exposure, forKey: "FilterService.exposure")
        userDefaults.set(currentParameters.contrast, forKey: "FilterService.contrast")
        userDefaults.set(currentParameters.saturation, forKey: "FilterService.saturation")
        userDefaults.set(currentParameters.brightness, forKey: "FilterService.brightness")
        userDefaults.set(currentParameters.temperature, forKey: "FilterService.temperature")
        userDefaults.set(currentParameters.tint, forKey: "FilterService.tint")
        userDefaults.set(currentParameters.highlights, forKey: "FilterService.highlights")
        userDefaults.set(currentParameters.shadows, forKey: "FilterService.shadows")
        userDefaults.set(currentParameters.vibrance, forKey: "FilterService.vibrance")
        userDefaults.set(currentParameters.clarity, forKey: "FilterService.clarity")
        userDefaults.set(currentParameters.vignetteIntensity, forKey: "FilterService.vignetteIntensity")

        // 保存LUT相关状态
        if let lutPath = currentLUTPath {
            userDefaults.set(lutPath, forKey: "FilterService.currentLUTPath")
        } else {
            userDefaults.removeObject(forKey: "FilterService.currentLUTPath")
        }
        userDefaults.set(lutIntensity, forKey: "FilterService.lutIntensity")
        userDefaults.set(isLUTEnabled, forKey: "FilterService.isLUTEnabled")
    }

    /// 加载保存的状态
    private func loadSavedState() {
        let userDefaults = UserDefaults.standard

        // 加载选中的预设信息
        if let presetTypeString = userDefaults.object(forKey: "FilterService.selectedPresetType") as? String,
           let presetType = FilterPresetType(rawValue: presetTypeString) {
            let presetIndex = userDefaults.integer(forKey: "FilterService.selectedPresetIndex")

            selectedPresetType = presetType
            selectedPresetIndex = presetIndex
            selectedPreset = presetManager.getPreset(type: presetType, index: presetIndex)
        }

        hasActiveFilter = userDefaults.bool(forKey: "FilterService.hasActiveFilter")

        // 加载参数
        currentParameters.exposure = userDefaults.object(forKey: "FilterService.exposure") as? Float ?? 0.0
        currentParameters.contrast = userDefaults.object(forKey: "FilterService.contrast") as? Float ?? 0.0
        currentParameters.saturation = userDefaults.object(forKey: "FilterService.saturation") as? Float ?? 0.0
        currentParameters.brightness = userDefaults.object(forKey: "FilterService.brightness") as? Float ?? 0.0
        currentParameters.temperature = userDefaults.object(forKey: "FilterService.temperature") as? Float ?? 0.0
        currentParameters.tint = userDefaults.object(forKey: "FilterService.tint") as? Float ?? 0.0
        currentParameters.highlights = userDefaults.object(forKey: "FilterService.highlights") as? Float ?? 0.0
        currentParameters.shadows = userDefaults.object(forKey: "FilterService.shadows") as? Float ?? 0.0
        currentParameters.vibrance = userDefaults.object(forKey: "FilterService.vibrance") as? Float ?? 0.0
        currentParameters.clarity = userDefaults.object(forKey: "FilterService.clarity") as? Float ?? 0.0
        currentParameters.vignetteIntensity = userDefaults.object(forKey: "FilterService.vignetteIntensity") as? Float ?? 0.0

        // 加载LUT相关状态
        currentLUTPath = userDefaults.object(forKey: "FilterService.currentLUTPath") as? String
        lutIntensity = userDefaults.object(forKey: "FilterService.lutIntensity") as? Float ?? 1.0
        isLUTEnabled = userDefaults.bool(forKey: "FilterService.isLUTEnabled")
    }

    // MARK: - 滤镜便利方法

    /// 获取指定类型的所有预设
    /// - Parameter type: 预设类型
    /// - Returns: 预设数组
    func getPresets(for type: FilterPresetType) -> [FilterPreset] {
        return presetManager.getPresets(for: type)
    }

    /// 获取当前参数的副本
    /// - Returns: 参数副本
    func getCurrentParametersCopy() -> FilterParameters {
        return currentParameters.copy()
    }

    /// 检查是否有未保存的更改
    /// - Returns: 是否有更改
    func hasUnsavedChanges() -> Bool {
        // 如果有选中的预设，检查当前参数是否与预设参数一致
        if let preset = selectedPreset {
            let presetParams = FilterParameters(preset: preset)
            // 这里可以实现详细的参数比较逻辑
            return false // 简化实现
        }

        // 如果没有选中预设，检查是否有非默认参数
        let defaultParams = FilterParameters()
        return currentParameters.exposure != defaultParams.exposure ||
               currentParameters.contrast != defaultParams.contrast ||
               currentParameters.saturation != defaultParams.saturation
        // 可以添加更多参数比较
    }

    // MARK: - 滤镜自动高光保护逻辑

    /// 根据滤镜类型自动应用高光保护设置
    /// - Parameter filterType: 滤镜类型
    private func applyAutomaticHighlightProtection(for filterType: FilterPresetType) {
        switch filterType {
        case .film:
            // 胶片滤镜使用胶片模式高光保护
            currentParameters.highlightProtectionMode = HighlightProtectionMode.film.rawValue
            currentParameters.highlightProtectionIntensity = 0.6 // 胶片滤镜适中强度
            print("🎨 自动设置胶片高光保护: 模式=胶片, 强度=0.6")
        case .polaroid, .vintage, .fashion, .ins:
            // 其他滤镜使用标准模式高光保护
            currentParameters.highlightProtectionMode = HighlightProtectionMode.standard.rawValue
            currentParameters.highlightProtectionIntensity = 0.4 // 标准滤镜较低强度
            print("🎨 自动设置标准高光保护: 模式=标准, 强度=0.4")
        }
    }

    /// 重置高光保护为默认设置
    private func resetHighlightProtectionToDefault() {
        currentParameters.highlightProtectionMode = HighlightProtectionMode.standard.rawValue
        currentParameters.highlightProtectionIntensity = 0.4 // 无滤镜时使用标准强度
        print("🎨 重置高光保护为默认: 模式=标准, 强度=0.4")
    }
}
