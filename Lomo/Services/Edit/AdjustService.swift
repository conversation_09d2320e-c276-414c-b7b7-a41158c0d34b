import Foundation
import SwiftData
import SwiftUI
import CoreGraphics
import Combine

/// 调节模块的业务服务 - MVVM-S架构
/// 复制自：AdjustSettingsManager.swift + CurveManager.swift + HSLManager.swift + SplitToningManager.swift
class AdjustService: ObservableObject {

    // MARK: - 单例
    static let shared = AdjustService()

    // MARK: - 调节设置管理 (从AdjustSettingsManager.swift完整复制)

    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?

    // MARK: - 初始化
    private init() {
        setupModelContainer()
        setupCurveObservers()
        setupCurveDefaultState()
        setupHSLManager()
        setupSplitToningManager()
        print("🎨 AdjustService 初始化完成")
    }

    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print("📱 AdjustService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print("❌ AdjustService: 获取共享 ModelContainer 失败！")
        }
    }

    // MARK: - 调节设置公共方法 (从AdjustSettingsManager.swift完整复制)

    /// 获取调节设置
    func getSettings() -> AdjustSettings {
        guard let context = modelContext else {
            return AdjustSettings()
        }

        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<AdjustSettings>(predicate: #Predicate { $0.id == "adjust_settings" })
            let existingSettings = try context.fetch(descriptor)

            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }

            // 如果不存在，创建新的设置并保存
            let newSettings = AdjustSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取调节设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return AdjustSettings()
        }
    }

    /// 保存设置
    func saveSettings(_ settings: AdjustSettings) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }

        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存调节设置失败: \(error.localizedDescription)")
        }
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<AdjustSettings, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }

    /// 重置所有参数
    func resetAllParameters() {
        var settings = getSettings()
        // Moved logic from Model's resetAllParameters here
        settings.exposure = 0.0
        settings.brightness = 0.0
        settings.contrast = 0.0
        settings.highlights = 0.0
        settings.shadows = 0.0
        settings.temperature = 0.0
        settings.tint = 0.0
        settings.saturation = 0.0
        settings.fade = 0.0
        settings.sharpness = 0.0
        settings.vignette = 0.0
        settings.chromaticAberration = 0.0
        settings.grainSize = 0.0
        settings.grainSaturation = 0.0
        settings.hue = 0.0
        settings.hslSaturation = 0.0
        settings.luminance = 0.0
        settings.toneBrightness = 0.0
        settings.toneHue = 0.0
        // settings.updateTimestamp() // Manager's saveSettings already updates timestamp
        saveSettings(settings)
    }

    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }

        do {
            // 获取所有现有设置
            let descriptor = FetchDescriptor<AdjustSettings>()
            let existingSettings = try context.fetch(descriptor)

            // 删除所有现有设置
            for settings in existingSettings {
                context.delete(settings)
            }

            // 创建新的默认设置
            let newSettings = AdjustSettings()
            context.insert(newSettings)

            // 保存更改
            try context.save()
        } catch {
            print("重置调节设置失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 参数更新方法

    /// 更新单个参数
    /// - Parameters:
    ///   - keyPath: 参数路径
    ///   - value: 新值
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) {
        guard let filterService = filterService else {
            print("⚠️ [AdjustService] FilterService 引用为空")
            return
        }
        filterService.updateParameter(keyPath, value: value)
    }

    /// 批量更新参数
    func batchUpdateParameters(_ updates: () -> Void) {
        guard let filterService = filterService else {
            print("⚠️ [AdjustService] FilterService 引用为空")
            return
        }
        filterService.batchUpdateParameters(updates)
    }

    /// 获取当前参数的副本
    func getCurrentParametersCopy() -> FilterParameters {
        guard let filterService = filterService else {
            print("⚠️ [AdjustService] FilterService 引用为空，返回默认参数")
            return FilterParameters()
        }
        return filterService.getCurrentParametersCopy()
    }



    // MARK: - 曲线管理 (从CurveManager.swift完整复制)

    // MARK: - 曲线发布属性

    /// 当前曲线控制点 - 四通道类型安全管理
    @Published var curvePoints: [CurveChannel: [CGPoint]] = [
        .rgb: CurveProcessor.createDefaultPoints(),
        .red: CurveProcessor.createDefaultPoints(),
        .green: CurveProcessor.createDefaultPoints(),
        .blue: CurveProcessor.createDefaultPoints()
    ]

    /// 曲线强度 (0.0-1.0)
    @Published var curveIntensity: Float = 1.0

    /// 是否启用曲线调整
    @Published var isEnabled: Bool = false

    /// 当前渲染质量
    @Published var renderQuality: CurveProcessor.CurveQuality = .standard

    /// RGB曲线是否启用（计算属性）
    var rgbCurveEnabled: Bool {
        !isLinearCurve(curvePoints[.rgb] ?? [])
    }

    /// 分离通道曲线是否启用（计算属性）
    var channelCurvesEnabled: Bool {
        [CurveChannel.red, .green, .blue].contains { channel in
            !isLinearCurve(curvePoints[channel] ?? [])
        }
    }

    /// 当前选中的预设
    @Published var currentPreset: CurveProcessor.CurvePreset? = nil

    // MARK: - 曲线UI状态属性

    /// 当前选中的通道
    @Published var selectedChannel: CurveChannel = .rgb {
        didSet {
            if selectedChannel != oldValue {
                // 通道切换时重置拖拽状态
                resetDragState()
                print("🎨 通道已切换到: \(selectedChannel.displayName)")
            }
        }
    }

    /// 当前拖拽的点索引
    @Published var draggedPointIndex: Int? = nil

    // MARK: - 曲线私有属性

    private var cancellables = Set<AnyCancellable>()
    private var currentLUTs: [CurveChannel: [Float]] = [:]
    private var needsLUTUpdate = true
    private var lastUpdateTime: CFTimeInterval = 0
    private var updateCount: Int = 0

    // MARK: - 曲线性能监控

    private struct PerformanceMetrics {
        var averageLUTGenerationTime: TimeInterval = 0
        var totalUpdates: Int = 0
        var lastUpdateDuration: TimeInterval = 0
        var peakMemoryUsage: Int = 0
    }

    private var performanceMetrics = PerformanceMetrics()

    // MARK: - 曲线设置方法

    private func setupCurveDefaultState() {
        curveIntensity = 1.0
        isEnabled = false
        renderQuality = .standard

        // 初始化默认LUT
        updateLUTsIfNeeded()
    }

    private func setupCurveObservers() {
        // 监听曲线点变化 - 使用防抖避免过度更新
        $curvePoints
            .debounce(for: .milliseconds(16), scheduler: DispatchQueue.main) // 60fps限制
            .sink { [weak self] _ in
                // 使用异步调度避免在视图更新中触发状态修改
                DispatchQueue.main.async {
                    self?.markLUTsNeedUpdate()
                    self?.updateFilterParameters()
                }
            }
            .store(in: &cancellables)

        // 监听强度变化
        $curveIntensity
            .sink { [weak self] _ in
                // 使用异步调度避免在视图更新中触发状态修改
                DispatchQueue.main.async {
                    self?.updateFilterParameters()
                }
            }
            .store(in: &cancellables)

        // 监听启用状态变化
        $isEnabled
            .sink { [weak self] enabled in
                // 使用异步调度避免在视图更新中触发状态修改
                DispatchQueue.main.async {
                    if enabled {
                        self?.updateLUTsIfNeeded()
                    }
                    self?.updateFilterParameters()
                }
            }
            .store(in: &cancellables)

        // 监听质量变化
        $renderQuality
            .sink { [weak self] _ in
                // 使用异步调度避免在视图更新中触发状态修改
                DispatchQueue.main.async {
                    self?.markLUTsNeedUpdate()
                    self?.updateFilterParameters()
                }
            }
            .store(in: &cancellables)
    }

    // MARK: - 曲线UI状态管理方法

    /// 切换到指定通道
    func switchToChannel(_ channel: CurveChannel) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.selectedChannel = channel
            // 切换通道时重置拖拽状态
            self.resetDragState()
            print("🎨 切换到通道: \(channel.displayName)")
        }
    }

    /// 重置拖拽相关状态
    func resetDragState() {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            self.draggedPointIndex = nil
        }
    }

    /// 获取当前通道的曲线点
    func getCurrentCurvePoints() -> [CGPoint] {
        return curvePoints[selectedChannel] ?? [CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 1)]
    }

    /// 更新当前通道的曲线点
    func updateCurrentChannelCurvePoints(_ points: [CGPoint]) {
        updateCurvePoints(points, for: selectedChannel)
    }

    /// 更新指定点的位置（使用归一化坐标）- 增强边界限制版本
    func updatePointPosition(index: Int, normalizedPoint: CGPoint) {
        guard var points = curvePoints[selectedChannel], index < points.count else {
            print("❌ 更新点位置失败：通道 \(selectedChannel.displayName)，索引 \(index)")
            return
        }

        // 设置拖拽状态
        draggedPointIndex = index
        print("🎨 更新通道 \(selectedChannel.displayName) 点 \(index) 位置")

        // 使用专业边界管理器进行实时约束
        let constrainedPoint = CurveBoundaryManager.applyRealtimeBoundaryConstraints(
            to: normalizedPoint,
            at: index,
            in: points,
            allowBuffer: true
        )

        // 更新点位置
        points[index] = constrainedPoint

        // 更新curvePoints并触发完整的更新流程
        updateCurvePoints(points, for: selectedChannel)
    }

    /// 结束点位置更新（拖拽结束时调用）
    func endPointPositionUpdate() {
        draggedPointIndex = nil
    }

    /// 添加曲线点（使用归一化坐标）
    @discardableResult
    func addPoint(at normalizedPoint: CGPoint) -> Int {
        guard var points = curvePoints[selectedChannel] else { return -1 }

        // 基础边界约束
        let constrainedX = max(0.0, min(1.0, normalizedPoint.x))
        let constrainedY = max(0.0, min(1.0, normalizedPoint.y))
        let newPoint = CGPoint(x: constrainedX, y: constrainedY)

        points.append(newPoint)
        points.sort { $0.x < $1.x }

        let newIndex = points.firstIndex(where: { $0.x == newPoint.x && $0.y == newPoint.y }) ?? -1
        updateCurvePoints(points, for: selectedChannel)

        return newIndex
    }

    // MARK: - 曲线边界检查系统

    /// 验证并修正所有控制点的边界 - 使用专业边界管理器
    func validateAndFixBoundaries() {
        for channel in CurveChannel.allCases {
            guard let points = curvePoints[channel] else { continue }

            let originalPoints = points
            let validatedPoints = CurveBoundaryManager.applyCompleteBoundaryValidation(to: points)

            if validatedPoints != originalPoints {
                updateCurvePoints(validatedPoints, for: channel)
                print("🔧 修正通道 \(channel.displayName) 边界约束")
            }
        }
    }

    /// 检查曲线是否在边界内 - 使用专业边界管理器
    func isCurveWithinBounds(for channel: CurveChannel) -> Bool {
        guard let points = curvePoints[channel] else { return true }
        return CurveBoundaryManager.isCurveWithinBounds(points: points)
    }

    /// 移除曲线点（从CurveEditorView复制过来）
    func removePoint(at index: Int) {
        guard var points = curvePoints[selectedChannel] else { return }

        // Do not allow removing the first or last point
        guard index > 0 && index < points.count - 1 else { return }

        points.remove(at: index)
        // 更新curvePoints并触发完整的更新流程
        updateCurvePoints(points, for: selectedChannel)
    }

    // MARK: - 曲线重置功能

    /// 重置所有曲线状态到默认值
    func resetAllCurves() {
        print("🎨 开始重置所有曲线状态")

        // 重置所有通道的曲线点到默认状态
        for channel in CurveChannel.allCases {
            let defaultPoints = CurveProcessor.createDefaultPoints()
            // 确保所有点都在边界内
            let constrainedPoints = defaultPoints.map { point in
                CGPoint(x: max(0.0, min(1.0, point.x)), y: max(0.0, min(1.0, point.y)))
            }
            curvePoints[channel] = constrainedPoints
        }

        // 重置所有相关状态 - 保持启用状态确保线性LUT被应用
        curveIntensity = 1.0
        isEnabled = true  // 修复：保持启用状态，确保线性LUT被应用
        currentPreset = nil
        selectedChannel = .rgb

        // 重置拖拽状态
        resetDragState()

        // 清除LUT缓存
        currentLUTs.removeAll()
        needsLUTUpdate = true

        // 更新滤镜参数（这会触发渲染器更新）
        updateFilterParameters()

        print("✅ 所有曲线状态已重置")
        print("   - 启用状态: \(isEnabled)")
        print("   - 曲线强度: \(curveIntensity)")
        print("   - RGB曲线启用: \(rgbCurveEnabled)")
        print("   - 分离通道启用: \(channelCurvesEnabled)")
    }

    /// 重置当前通道的曲线
    func resetCurrentChannel() {
        let defaultPoints = CurveProcessor.createDefaultPoints()
        updateCurvePoints(defaultPoints, for: selectedChannel)
    }

    // MARK: - 曲线核心方法

    /// 更新曲线控制点
    /// - Parameters:
    ///   - points: 新的控制点数组
    ///   - channel: 通道类型
    func updateCurvePoints(_ points: [CGPoint], for channel: CurveChannel) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 确保所有点都在边界内
            let constrainedPoints = points.map { point in
                CGPoint(x: max(0.0, min(1.0, point.x)), y: max(0.0, min(1.0, point.y)))
            }
            self.curvePoints[channel] = constrainedPoints

            // 如果用户手动编辑了曲线，自动启用
            if !self.isLinearCurve(points) {
                self.isEnabled = true
            }
        }

        updateFilterParameters()
    }

    /// 应用曲线预设
    /// - Parameters:
    ///   - preset: 预设类型
    ///   - channel: 目标通道
    ///   - intensity: 应用强度 (0.0-1.0)
    func applyPreset(_ preset: CurveProcessor.CurvePreset, to channel: CurveChannel, intensity: Float = 1.0) {

        let presetPoints = preset.points
        let adjustedPoints: [CGPoint]

        if intensity < 1.0 {
            // 根据强度调整预设点
            let defaultPoints = CurveProcessor.createDefaultPoints()
            adjustedPoints = blendCurvePoints(from: defaultPoints, to: presetPoints, intensity: intensity)
        } else {
            adjustedPoints = presetPoints
        }

        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 确保所有点都在边界内
            let constrainedPoints = adjustedPoints.map { point in
                CGPoint(x: max(0.0, min(1.0, point.x)), y: max(0.0, min(1.0, point.y)))
            }
            self.curvePoints[channel] = constrainedPoints
            self.currentPreset = preset
            self.curveIntensity = intensity

            // 根据预设类型自动配置启用状态
            if preset != .linear {
                self.isEnabled = true
            }
        }

        updateEnabledState()
        print("🎨 预设 '\(preset.rawValue)' 已应用到通道 \(channel.displayName)，强度: \(intensity)")
    }

    /// 重置指定通道
    /// - Parameter channel: 通道类型
    func resetChannel(_ channel: CurveChannel) {
        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            self.curvePoints[channel] = CurveProcessor.createDefaultPoints()
            self.currentPreset = nil

            print("🎨 通道 \(channel.displayName) 已重置")
        }

        updateEnabledState()
    }

    /// 获取当前性能统计
    func getPerformanceStats() -> (averageTime: TimeInterval, updateCount: Int, lastDuration: TimeInterval) {
        return (performanceMetrics.averageLUTGenerationTime,
                performanceMetrics.totalUpdates,
                performanceMetrics.lastUpdateDuration)
    }

    /// 获取当前LUT数据
    func getCurrentLUTs() -> [CurveChannel: [Float]] {
        updateLUTsIfNeeded()
        return currentLUTs
    }

    /// 检查是否有活跃的曲线
    func hasActiveCurves() -> Bool {
        return isEnabled && (rgbCurveEnabled || channelCurvesEnabled)
    }

    // MARK: - 曲线私有方法

    /// 标记LUT需要更新
    private func markLUTsNeedUpdate() {
        needsLUTUpdate = true
    }

    /// 如果需要则更新LUT
    private func updateLUTsIfNeeded() {
        guard needsLUTUpdate else { return }

        let startTime = CFAbsoluteTimeGetCurrent()

        // 生成新的LUTs
        currentLUTs = CurveProcessor.generateAllChannelLUTs(
            from: curvePoints,
            quality: renderQuality
        )

        needsLUTUpdate = false

        // 更新性能指标
        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        updatePerformanceMetrics(processingTime)

        print("🎨 LUT更新完成，耗时: \(String(format: "%.2f", processingTime * 1000))ms")
    }

    /// 更新性能指标
    private func updatePerformanceMetrics(_ duration: TimeInterval) {
        performanceMetrics.lastUpdateDuration = duration
        performanceMetrics.totalUpdates += 1

        // 计算平均时间
        let totalTime = performanceMetrics.averageLUTGenerationTime * Double(performanceMetrics.totalUpdates - 1) + duration
        performanceMetrics.averageLUTGenerationTime = totalTime / Double(performanceMetrics.totalUpdates)
    }

    /// 更新启用状态
    private func updateEnabledState() {
        let hasActiveCurves = rgbCurveEnabled || channelCurvesEnabled

        // 使用异步调度避免在视图更新中修改@Published属性
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            // 修复重置逻辑：始终保持启用状态和强度1.0，确保线性LUT被正确应用
            self.isEnabled = true
            if self.curveIntensity <= 0.0 {
                self.curveIntensity = 1.0
            }

            print("🎨 启用状态已更新: \(self.isEnabled), 强度: \(self.curveIntensity), 有活跃曲线: \(hasActiveCurves)")
        }

        // 重要：必须触发渲染器更新，包括重置时
        // 注意：这里不能异步调用，因为需要立即更新渲染器
        updateFilterParameters()
    }

    /// 检查曲线是否为线性
    private func isLinearCurve(_ points: [CGPoint]) -> Bool {
        guard points.count == 2 else { return false }
        let first = points[0]
        let last = points[1]
        return abs(first.x - 0.0) < 0.01 && abs(first.y - 0.0) < 0.01 &&
               abs(last.x - 1.0) < 0.01 && abs(last.y - 1.0) < 0.01
    }

    /// 混合两组曲线点
    private func blendCurvePoints(from: [CGPoint], to: [CGPoint], intensity: Float) -> [CGPoint] {
        guard from.count == to.count else { return to }

        var blendedPoints: [CGPoint] = []
        for i in 0..<from.count {
            let fromPoint = from[i]
            let toPoint = to[i]

            let blendedX = fromPoint.x + (toPoint.x - fromPoint.x) * CGFloat(intensity)
            let blendedY = fromPoint.y + (toPoint.y - fromPoint.y) * CGFloat(intensity)

            blendedPoints.append(CGPoint(x: blendedX, y: blendedY))
        }

        return blendedPoints
    }

    /// 更新FilterParameters - 修复数据流
    private func updateFilterParameters() {
        // 确保LUT是最新的
        updateLUTsIfNeeded()

        let filterService = FilterService.shared

        // 计算实际的曲线强度
        let actualIntensity = isEnabled ? curveIntensity : 0.0

        print("🎨 [DEBUG] 更新曲线参数:")
        print("   - 启用状态: \(isEnabled)")
        print("   - 曲线强度: \(curveIntensity)")
        print("   - 实际强度: \(actualIntensity)")
        print("   - RGB曲线启用: \(rgbCurveEnabled)")
        print("   - 分离通道启用: \(channelCurvesEnabled)")

        // 直接更新FilterParameters中的曲线相关参数
        filterService.currentParameters.rgbCurveLUT = currentLUTs[.rgb] ?? CurveProcessor.createLinearLUT()
        filterService.currentParameters.redCurveLUT = currentLUTs[.red] ?? CurveProcessor.createLinearLUT()
        filterService.currentParameters.greenCurveLUT = currentLUTs[.green] ?? CurveProcessor.createLinearLUT()
        filterService.currentParameters.blueCurveLUT = currentLUTs[.blue] ?? CurveProcessor.createLinearLUT()
        filterService.currentParameters.curveIntensity = actualIntensity
        filterService.currentParameters.rgbCurveEnabled = isEnabled
        filterService.currentParameters.channelCurvesEnabled = isEnabled

        print("   - LUT大小: RGB=\(filterService.currentParameters.rgbCurveLUT.count), Red=\(filterService.currentParameters.redCurveLUT.count), Green=\(filterService.currentParameters.greenCurveLUT.count), Blue=\(filterService.currentParameters.blueCurveLUT.count)")

        // 立即触发渲染器更新
        filterService.updateParameter(\.curveIntensity, value: actualIntensity)

        print("✅ 曲线参数更新完成 - hasActiveFilter: \(filterService.hasActiveFilter)")
    }

    // MARK: - HSL管理 (从HSLManager.swift完整复制)

    // MARK: - HSL发布属性

    /// 当前选中的HSL颜色范围索引 (0-7)
    @Published var selectedHSLColorIndex: Int = 0 {
        didSet {
            if selectedHSLColorIndex != oldValue {
                print("🎨 [HSL] 切换到颜色范围: \(getHSLColorName(for: selectedHSLColorIndex))")
                updateHSLFilterParameters()
            }
        }
    }

    /// 8个颜色的独立HSL参数存储 (0=红,1=橙,2=黄,3=绿,4=青,5=蓝,6=紫,7=洋红) - 从备份复制
    @Published private var hslHueValues: [Float] = Array(repeating: 0.0, count: 8)
    @Published private var hslSaturationValues: [Float] = Array(repeating: 0.0, count: 8)
    @Published private var hslLuminanceValues: [Float] = Array(repeating: 0.0, count: 8)

    /// 当前选中颜色的色相调整 (-180 到 +180 度) - 从备份复制
    var hue: Float {
        get { hslHueValues[selectedHSLColorIndex] }
        set { hslHueValues[selectedHSLColorIndex] = newValue }
    }

    /// 当前选中颜色的HSL饱和度调整 (-100 到 +100) - 从备份复制
    var hslSaturation: Float {
        get { hslSaturationValues[selectedHSLColorIndex] }
        set { hslSaturationValues[selectedHSLColorIndex] = newValue }
    }

    /// 当前选中颜色的HSL明度调整 (-100 到 +100) - 从备份复制
    var hslLuminance: Float {
        get { hslLuminanceValues[selectedHSLColorIndex] }
        set { hslLuminanceValues[selectedHSLColorIndex] = newValue }
    }

    /// HSL颜色范围过渡柔和度 (0.0-1.0) - 从备份复制
    @Published var hslColorRangeSoftness: Float = 0.3

    /// HSL颜色范围检测精度 (0.5-2.0, 默认1.0) - 从备份复制
    @Published var hslColorRangePrecision: Float = 1.0

    /// HSL是否启用
    @Published var hslEnabled: Bool = false {
        didSet {
            if hslEnabled != oldValue {
                print("🎨 [HSL] 启用状态变更: \(hslEnabled)")
                updateHSLFilterParameters()
            }
        }
    }

    /// HSL全局强度 (0.0-1.0)
    @Published var hslGlobalIntensity: Float = 1.0 {
        didSet {
            if abs(hslGlobalIntensity - oldValue) > 0.001 {
                print("🎨 [HSL] 全局强度变更: \(hslGlobalIntensity)")
                updateHSLFilterParameters()
            }
        }
    }

    // MARK: - HSL依赖注入

    /// FilterService的弱引用 (替代FilterStateManager)
    weak var filterService: FilterService?

    // MARK: - HSL初始化方法

    private func setupHSLManager() {
        // 设置FilterService引用
        filterService = FilterService.shared

        // 初始化默认状态
        selectedHSLColorIndex = 0
        hslEnabled = false
        hslGlobalIntensity = 1.0

        // 初始化8个颜色范围的默认参数 - 从备份复制
        // HSL参数已在属性声明时初始化

        print("🎨 [HSL] HSLManager 初始化完成")
    }

    // MARK: - HSL公共方法

    /// 更新HSL色相
    /// - Parameters:
    ///   - hue: 色相值 (-180 到 +180 度)
    ///   - colorIndex: 颜色范围索引 (0-7)，默认使用当前选中的索引
    func updateHSLHue(_ hue: Float, for colorIndex: Int? = nil) {
        let index = colorIndex ?? selectedHSLColorIndex
        guard index >= 0 && index < 8 else {
            print("❌ [HSL] 无效的颜色索引: \(index)")
            return
        }

        let clampedHue = max(-180.0, min(180.0, hue))
        hslHueValues[index] = clampedHue

        // 如果有非零调整，自动启用HSL
        if abs(clampedHue) > 0.001 {
            hslEnabled = true
        }

        print("🎨 [HSL] 更新色相: \(getHSLColorName(for: index)) = \(clampedHue)°")
        updateHSLFilterParameters()
    }

    /// 更新HSL饱和度
    /// - Parameters:
    ///   - saturation: 饱和度值 (-100 到 +100)
    ///   - colorIndex: 颜色范围索引 (0-7)，默认使用当前选中的索引
    func updateHSLSaturation(_ saturation: Float, for colorIndex: Int? = nil) {
        let index = colorIndex ?? selectedHSLColorIndex
        guard index >= 0 && index < 8 else {
            print("❌ [HSL] 无效的颜色索引: \(index)")
            return
        }

        let clampedSaturation = max(-100.0, min(100.0, saturation))
        hslSaturationValues[index] = clampedSaturation

        // 如果有非零调整，自动启用HSL
        if abs(clampedSaturation) > 0.001 {
            hslEnabled = true
        }

        print("🎨 [HSL] 更新饱和度: \(getHSLColorName(for: index)) = \(clampedSaturation)")
        updateHSLFilterParameters()
    }

    /// 更新HSL明度
    /// - Parameters:
    ///   - luminance: 明度值 (-100 到 +100)
    ///   - colorIndex: 颜色范围索引 (0-7)，默认使用当前选中的索引
    func updateHSLLuminance(_ luminance: Float, for colorIndex: Int? = nil) {
        let index = colorIndex ?? selectedHSLColorIndex
        guard index >= 0 && index < 8 else {
            print("❌ [HSL] 无效的颜色索引: \(index)")
            return
        }

        let clampedLuminance = max(-100.0, min(100.0, luminance))
        hslLuminanceValues[index] = clampedLuminance

        // 如果有非零调整，自动启用HSL
        if abs(clampedLuminance) > 0.001 {
            hslEnabled = true
        }

        print("🎨 [HSL] 更新明度: \(getHSLColorName(for: index)) = \(clampedLuminance)")
        updateHSLFilterParameters()
    }

    /// 切换HSL颜色范围
    /// - Parameter index: 颜色范围索引 (0-7)
    func switchHSLColorRange(to index: Int) {
        guard index >= 0 && index < 8 else {
            print("❌ [HSL] 无效的颜色范围索引: \(index)")
            return
        }

        selectedHSLColorIndex = index
        print("🎨 [HSL] 切换到颜色范围: \(getHSLColorName(for: index))")
    }

    /// 重置当前选中颜色的HSL参数 - 从备份复制
    func resetCurrentHSLColor() {
        let index = selectedHSLColorIndex
        hslHueValues[index] = 0.0
        hslSaturationValues[index] = 0.0
        hslLuminanceValues[index] = 0.0

        print("🔄 [HSL] 重置颜色: \(getHSLColorName(for: index))")
        updateHSLFilterParameters()
    }

    /// 重置所有HSL颜色参数到默认值 - 从备份复制
    func resetAllHSLColors() {
        hslHueValues = Array(repeating: 0.0, count: 8)
        hslSaturationValues = Array(repeating: 0.0, count: 8)
        hslLuminanceValues = Array(repeating: 0.0, count: 8)
        selectedHSLColorIndex = 0
        hslColorRangeSoftness = 0.3
        hslColorRangePrecision = 1.0
        hslEnabled = false

        print("🔄 [AdjustService] 重置所有HSL颜色参数")
        updateHSLFilterParameters()
    }

    /// 检查是否有活跃的HSL调整 - 从备份复制
    func hasActiveHSLAdjustments() -> Bool {
        if !hslEnabled { return false }

        // 检查是否有任何非零的HSL值
        for i in 0..<8 {
            if abs(hslHueValues[i]) > 0.001 ||
               abs(hslSaturationValues[i]) > 0.001 ||
               abs(hslLuminanceValues[i]) > 0.001 {
                return true
            }
        }
        return false
    }

    // MARK: - HSL私有方法

    /// 获取HSL颜色范围名称
    /// - Parameter index: 颜色索引 (0-7)
    /// - Returns: 颜色名称
    private func getHSLColorName(for index: Int) -> String {
        let colorNames = ["红色", "橙色", "黄色", "绿色", "青色", "蓝色", "紫色", "品红"]
        return index >= 0 && index < colorNames.count ? colorNames[index] : "未知"
    }

    /// 更新HSL滤镜参数
    private func updateHSLFilterParameters() {
        guard let filterService = filterService else {
            print("⚠️ [HSL] FilterService 引用为空")
            return
        }

        // 批量更新HSL参数
        filterService.batchUpdateParameters {
            // 更新当前选中颜色的参数
            let index = selectedHSLColorIndex
            filterService.currentParameters.hue = hslHueValues[index]
            filterService.currentParameters.hslSaturation = hslSaturationValues[index]
            filterService.currentParameters.hslLuminance = hslLuminanceValues[index]
            filterService.currentParameters.selectedHSLColorIndex = selectedHSLColorIndex

            // 注意：hslEnabled和hslGlobalIntensity是AdjustService独有的属性
            // FilterParameters中不包含这些属性，所以不需要传递
        }

        print("🔄 [HSL] 滤镜参数已更新")
    }

    /// 获取当前选中颜色的HSL参数 - 从备份复制
    func getCurrentHSLParameters() -> (hue: Float, saturation: Float, luminance: Float) {
        let index = selectedHSLColorIndex
        return (
            hue: hslHueValues[index],
            saturation: hslSaturationValues[index],
            luminance: hslLuminanceValues[index]
        )
    }

    /// 获取所有HSL参数 - 从备份复制
    func getAllHSLParameters() -> (hueValues: [Float], saturationValues: [Float], luminanceValues: [Float]) {
        return (
            hueValues: hslHueValues,
            saturationValues: hslSaturationValues,
            luminanceValues: hslLuminanceValues
        )
    }

    /// 打印HSL当前状态
    func printHSLCurrentState() {
        print("🎨 [HSL] 当前状态:")
        print("   - 启用状态: \(hslEnabled)")
        print("   - 全局强度: \(hslGlobalIntensity)")
        print("   - 选中颜色: \(getHSLColorName(for: selectedHSLColorIndex)) (索引: \(selectedHSLColorIndex))")

        let index = selectedHSLColorIndex
        print("   - 当前颜色参数:")
        print("     - 色相: \(hslHueValues[index])°")
        print("     - 饱和度: \(hslSaturationValues[index])")
        print("     - 明度: \(hslLuminanceValues[index])")

        print("   - 所有颜色状态:")
        for i in 0..<8 {
            let hue = hslHueValues[i]
            let saturation = hslSaturationValues[i]
            let luminance = hslLuminanceValues[i]
            if abs(hue) > 0.001 || abs(saturation) > 0.001 || abs(luminance) > 0.001 {
                print("     - \(getHSLColorName(for: i)): H=\(hue)°, S=\(saturation), L=\(luminance)")
            }
        }
    }

    // MARK: - 色调分离管理 (从SplitToningManager.swift完整复制)

    // MARK: - 色调分离发布属性

    /// 当前选中的色调选项
    @Published var selectedToneOption: String = "阴影"

    /// 色环控制点的UI偏移量
    @Published var toneHueOffset: CGSize = .zero

    /// 是否正在按压控制点
    @Published var isPressing: Bool = false

    // MARK: - 色调分离初始化方法

    private func setupSplitToningManager() {
        // 设置默认状态
        selectedToneOption = "阴影"
        toneHueOffset = .zero
        isPressing = false

        print("🎨 [SplitToning] SplitToningManager 初始化完成")
    }

    // MARK: - 色调分离公共方法

    /// 切换色调选项
    func switchToToneOption(_ option: String) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            if self.selectedToneOption != option {
                self.selectedToneOption = option
                // 同步UI状态
                withAnimation(.easeInOut(duration: 0.3)) {
                    self.toneHueOffset = self.calculateToneHueOffset()
                }
                print("🎨 [SplitToning] 切换到: \(option)")
            }
        }
    }

    /// 更新色调参数
    func updateToneHue(_ normalizedAngle: Double, saturation: Double) {
        guard let filterService = filterService else { return }

        // 参数验证
        guard normalizedAngle.isFinite && saturation.isFinite else {
            print("⚠️ [SplitToning] 无效参数: angle=\(normalizedAngle), saturation=\(saturation)")
            return
        }

        // 规范化参数
        let clampedAngle = max(0.0, min(1.0, normalizedAngle))
        let clampedSaturation = max(0.0, min(1.0, saturation))

        let hueValue = Float(clampedAngle * 360.0)
        let saturationValue = Float(clampedSaturation * 100.0)

        // 立即更新参数
        switch selectedToneOption {
        case "阴影":
            filterService.updateParameter(\.shadowHue, value: hueValue)
            filterService.updateParameter(\.shadowSaturation, value: saturationValue)

        case "高光":
            filterService.updateParameter(\.highlightHue, value: hueValue)
            filterService.updateParameter(\.highlightSaturation, value: saturationValue)

        default:
            break
        }

        // 同步UI状态
        updateSplitToningUIState()
    }

    /// 重置当前选中的色调（双击重置）
    func resetCurrentTone() {
        guard let filterService = filterService else { return }

        print("🔄 [SplitToning] 双击重置: \(selectedToneOption)")

        // UI状态更新在异步块中
        DispatchQueue.main.async { [weak self] in
            withAnimation(.easeInOut(duration: 0.3)) {
                self?.toneHueOffset = .zero
            }
        }

        // 批量更新参数，避免多次渲染器调用
        filterService.batchUpdateParameters {
            switch selectedToneOption {
            case "阴影":
                filterService.currentParameters.shadowHue = 0.0
                filterService.currentParameters.shadowSaturation = 0.0
                print("🔄 [SplitToning] 阴影色调已重置")

            case "高光":
                filterService.currentParameters.highlightHue = 0.0
                filterService.currentParameters.highlightSaturation = 0.0
                print("🔄 [SplitToning] 高光色调已重置")

            default:
                print("⚠️ [SplitToning] 未知选项: \(selectedToneOption)")
                return
            }
        }

        print("🔄 [SplitToning] 重置完成")
    }

    /// 重置所有色调分离参数（重置按钮）
    func resetAllTones() {
        guard let filterService = filterService else { return }

        print("🔄 [SplitToning] 重置所有色调分离参数")

        // UI状态更新在异步块中
        DispatchQueue.main.async { [weak self] in
            withAnimation(.easeInOut(duration: 0.3)) {
                self?.toneHueOffset = .zero
            }
        }

        // 批量更新所有参数，避免多次渲染器调用
        filterService.batchUpdateParameters {
            filterService.currentParameters.highlightHue = 0.0
            filterService.currentParameters.highlightSaturation = 0.0
            filterService.currentParameters.shadowHue = 0.0
            filterService.currentParameters.shadowSaturation = 0.0
            filterService.currentParameters.splitToningBalance = 0.0
        }

        print("🔄 [SplitToning] 所有参数重置完成")
    }

    /// 更新平衡参数
    func updateBalance(_ value: Float) {
        guard let filterService = filterService else { return }

        filterService.updateParameter(\.splitToningBalance, value: value)
        print("🎨 [SplitToning] 平衡更新: \(value)")
    }

    // MARK: - 色调分离私有方法

    /// 同步色调分离UI状态
    private func syncSplitToningUIState() {
        toneHueOffset = calculateToneHueOffset()
    }

    /// 更新色调分离UI状态
    private func updateSplitToningUIState() {
        DispatchQueue.main.async { [weak self] in
            self?.syncSplitToningUIState()
        }
    }

    /// 计算色相控制点位置
    private func calculateToneHueOffset() -> CGSize {
        guard let filterService = filterService else { return .zero }

        let currentHue: Float
        let currentSaturation: Float

        switch selectedToneOption {
        case "阴影":
            currentHue = filterService.currentParameters.shadowHue
            currentSaturation = filterService.currentParameters.shadowSaturation
        case "高光":
            currentHue = filterService.currentParameters.highlightHue
            currentSaturation = filterService.currentParameters.highlightSaturation
        default:
            return .zero
        }

        // 参数验证
        guard currentHue.isFinite && currentSaturation.isFinite else {
            return .zero
        }

        // 如果饱和度为0，返回中心点
        if currentSaturation <= 0.001 {
            return .zero
        }

        // 计算控制点位置
        let normalizedHue = Double(currentHue.truncatingRemainder(dividingBy: 360.0))
        let angle = normalizedHue * .pi / 180.0 - .pi

        let clampedSaturation = min(max(Double(currentSaturation), 0.0), 100.0)
        let radius = UIScreen.main.bounds.height * 0.1 * clampedSaturation / 100.0

        let x = cos(angle) * radius
        let y = sin(angle) * radius

        return CGSize(width: x, height: y)
    }

    /// 获取当前色调分离状态
    func getCurrentSplitToningState() -> (shadowHue: Float, shadowSaturation: Float, highlightHue: Float, highlightSaturation: Float, balance: Float) {
        guard let filterService = filterService else {
            return (0, 0, 0, 0, 0)
        }

        let params = filterService.currentParameters
        return (
            shadowHue: params.shadowHue,
            shadowSaturation: params.shadowSaturation,
            highlightHue: params.highlightHue,
            highlightSaturation: params.highlightSaturation,
            balance: params.splitToningBalance
        )
    }

    /// 检查是否有活跃的色调分离调整
    func hasActiveSplitToningAdjustments() -> Bool {
        let state = getCurrentSplitToningState()
        return abs(state.shadowHue) > 0.001 ||
               abs(state.shadowSaturation) > 0.001 ||
               abs(state.highlightHue) > 0.001 ||
               abs(state.highlightSaturation) > 0.001 ||
               abs(state.balance) > 0.001
    }

    /// 打印色调分离当前状态
    func printSplitToningCurrentState() {
        let state = getCurrentSplitToningState()
        print("🎨 [SplitToning] 当前状态:")
        print("   - 选中选项: \(selectedToneOption)")
        print("   - 阴影色调: 色相=\(state.shadowHue)°, 饱和度=\(state.shadowSaturation)")
        print("   - 高光色调: 色相=\(state.highlightHue)°, 饱和度=\(state.highlightSaturation)")
        print("   - 平衡: \(state.balance)")
        print("   - UI偏移: \(toneHueOffset)")
        print("   - 按压状态: \(isPressing)")
    }
}
