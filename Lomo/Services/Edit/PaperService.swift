import Foundation
import SwiftData
import SwiftUI

/// 相纸设置服务 - 实现PaperServiceProtocol协议
@MainActor
class PaperService: PaperServiceProtocol {
    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?

    // 初始化方法
    init() {
        setupModelContainer()
    }

    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print("🎨 PaperService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print("❌ PaperService: 获取共享 ModelContainer 失败！")
        }
    }

    // MARK: - 公共方法

    /// 获取相纸设置
    func getSettings() async throws -> PaperModel {
        guard let context = modelContext else {
            return PaperModel()
        }

        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<PaperModel>(predicate: #Predicate<PaperModel> { $0.id == "paper_settings" })
            let existingSettings = try context.fetch(descriptor)

            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }

            // 如果不存在，创建新的设置并保存
            let newSettings = PaperModel()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取相纸设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return PaperModel()
        }
    }

    /// 保存设置
    func saveSettings(_ settings: PaperModel) async throws {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }

        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存相纸设置失败: \(error.localizedDescription)")
        }
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<PaperModel, T>, value: T) async throws {
        var settings = try await getSettings()
        settings[keyPath: keyPath] = value
        try await saveSettings(settings)
    }

    /// 添加到最近使用的预设
    func addToRecentPresets(_ preset: String) async throws {
        let settings = try await getSettings()
        settings.addToRecentPresets(preset)
        try await saveSettings(settings)
    }

    /// 切换预设收藏状态
    func toggleFavorite(_ preset: String) async throws {
        let settings = try await getSettings()
        settings.toggleFavorite(preset)
        try await saveSettings(settings)
    }

    /// 重置所有设置为默认值
    func resetToDefaults() async throws {
        guard let context = modelContext else {
            return
        }

        do {
            // 删除所有现有设置
            let descriptor = FetchDescriptor<PaperModel>()
            let existingSettings = try context.fetch(descriptor)
            for settings in existingSettings {
                context.delete(settings)
            }

            // 创建新的默认设置
            let newSettings = PaperModel()
            context.insert(newSettings)
            try context.save()
        } catch {
            print("重置相纸设置失败: \(error.localizedDescription)")
        }
    }
}
