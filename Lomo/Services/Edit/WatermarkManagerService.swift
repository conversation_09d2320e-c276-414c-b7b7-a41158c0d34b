// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

// MARK: - WatermarkManager服务协议

/// WatermarkManager服务协议，专门用于替代WatermarkManagerProvider
/// 提供更清晰的接口和更好的依赖管理
protocol WatermarkManagerServiceProtocol {
    /// 设置水印管理器
    /// - Parameter container: 预览容器
    func setupWatermarkManager(with container: UIView)
    
    /// 获取水印管理器
    /// - Returns: 水印管理器实例
    func getWatermarkManager() -> WatermarkManager?
    
    /// 检查管理器是否可用
    /// - Returns: 是否可用
    func isManagerAvailable() -> Bool
    
    /// 应用水印样式
    /// - Parameter style: 水印样式
    /// - Returns: 是否成功
    func applyWatermarkStyle(_ style: WatermarkStyle?) -> Bool
    
    /// 移除当前水印
    /// - Returns: 是否成功
    func removeCurrentWatermark() -> Bool
    
    /// 获取当前预览容器
    /// - Returns: 预览容器
    func getCurrentContainer() -> UIView?
}

// MARK: - WatermarkManager服务实现

/// WatermarkManager服务实现类
/// 替代WatermarkManagerProvider单例，提供依赖注入支持
class WatermarkManagerService: WatermarkManagerServiceProtocol {
    
    // MARK: - 私有属性
    
    /// 水印管理器实例
    private var watermarkManager: WatermarkManager?
    
    /// 当前预览容器的弱引用
    private weak var currentContainer: UIView?
    
    // MARK: - 初始化
    
    /// 初始化服务
    init() {
        print("🏭 [WatermarkManagerService] 初始化")
    }
    
    // MARK: - 协议实现
    
    /// 设置水印管理器
    /// - Parameter container: 预览容器
    func setupWatermarkManager(with container: UIView) {
        print("🔧 [WatermarkManagerService] 设置水印管理器")
        print("   - 容器: \(container)")
        print("   - 容器尺寸: \(container.bounds)")
        
        // 检查是否已存在相同容器的管理器
        if let existingManager = watermarkManager,
           let currentContainer = currentContainer,
           currentContainer === container {
            print("   - 复用现有管理器（相同容器）")
            return
        }
        
        // 创建新的管理器
        watermarkManager = WatermarkManager(previewContainer: container)
        currentContainer = container
        
        print("   - ✅ 水印管理器设置完成")
    }
    
    /// 获取水印管理器
    /// - Returns: 水印管理器实例
    func getWatermarkManager() -> WatermarkManager? {
        return watermarkManager
    }
    
    /// 检查管理器是否可用
    /// - Returns: 是否可用
    func isManagerAvailable() -> Bool {
        let available = watermarkManager != nil
        print("🔍 [WatermarkManagerService] 检查管理器可用性: \(available ? "可用" : "不可用")")
        return available
    }
    
    /// 应用水印样式
    /// - Parameter style: 水印样式
    /// - Returns: 是否成功
    func applyWatermarkStyle(_ style: WatermarkStyle?) -> Bool {
        print("🎨 [WatermarkManagerService] 应用水印样式")
        
        guard let manager = watermarkManager else {
            print("   - ❌ 管理器不可用")
            return false
        }
        
        manager.applyWatermarkStyle(style)
        print("   - ✅ 水印样式应用完成")
        return true
    }
    
    /// 移除当前水印
    /// - Returns: 是否成功
    func removeCurrentWatermark() -> Bool {
        print("🗑️ [WatermarkManagerService] 移除当前水印")
        
        guard let manager = watermarkManager else {
            print("   - ❌ 管理器不可用")
            return false
        }
        
        manager.removeCurrentWatermark()
        print("   - ✅ 水印移除完成")
        return true
    }
    
    /// 获取当前预览容器
    /// - Returns: 预览容器
    func getCurrentContainer() -> UIView? {
        return currentContainer
    }
    
    // MARK: - 内部方法
    
    /// 重置服务状态
    func reset() {
        print("🔄 [WatermarkManagerService] 重置服务状态")
        watermarkManager = nil
        currentContainer = nil
    }
}
