// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData
import SwiftUI
import UIKit

// MARK: - 支持数据结构

/// 设置验证结果
struct ValidationResult {
    let isValid: Bool                    // 是否有效
    let errors: [ValidationError]        // 验证错误列表
    let warnings: [ValidationWarning]    // 验证警告列表

    /// 创建成功的验证结果
    static var success: ValidationResult {
        return ValidationResult(isValid: true, errors: [], warnings: [])
    }

    /// 创建失败的验证结果
    static func failure(errors: [ValidationError], warnings: [ValidationWarning] = []) -> ValidationResult {
        return ValidationResult(isValid: false, errors: errors, warnings: warnings)
    }
}

/// 验证错误
struct ValidationError {
    let field: String       // 字段名
    let message: String     // 错误信息
    let code: String        // 错误代码
}

/// 验证警告
struct ValidationWarning {
    let field: String       // 字段名
    let message: String     // 警告信息
    let code: String        // 警告代码
}

/// 设置差异
struct SettingsDifference {
    let hasChanges: Bool                                    // 是否有变化
    let changedFields: [String: (old: Any, new: Any)]     // 变化的字段
    let addedFields: [String: Any]                         // 新增的字段
    let removedFields: [String: Any]                       // 删除的字段
}

/// 水印应用结果
struct WatermarkApplicationResult {
    let success: Bool                   // 是否成功
    let watermarkType: String?          // 应用的水印类型
    let message: String                 // 结果信息
    let error: WatermarkError?          // 错误信息（如果有）

    /// 创建成功结果
    static func success(watermarkType: String?, message: String = "操作成功") -> WatermarkApplicationResult {
        return WatermarkApplicationResult(success: true, watermarkType: watermarkType, message: message, error: nil)
    }

    /// 创建失败结果
    static func failure(message: String, error: WatermarkError? = nil) -> WatermarkApplicationResult {
        return WatermarkApplicationResult(success: false, watermarkType: nil, message: message, error: error)
    }
}

/// 水印错误类型
enum WatermarkError: Error, LocalizedError {
    case invalidContainer(reason: String)          // 无效的容器
    case invalidWatermarkType(type: String)        // 无效的水印类型
    case styleCreationFailed(type: String)         // 样式创建失败
    case applicationFailed(underlying: Error?)     // 应用失败
    case removalFailed(underlying: Error?)         // 移除失败
    case managerNotAvailable                       // 管理器不可用
    case settingsValidationFailed(errors: [ValidationError])  // 设置验证失败
    case batchUpdateFailed(failedCount: Int)       // 批量更新失败
    case serviceUnavailable                        // 服务不可用

    var errorDescription: String? {
        switch self {
        case .invalidContainer(let reason):
            return "无效的预览容器: \(reason)"
        case .invalidWatermarkType(let type):
            return "无效的水印类型: \(type)"
        case .styleCreationFailed(let type):
            return "水印样式创建失败: \(type)"
        case .applicationFailed(let underlying):
            return "水印应用失败\(underlying != nil ? ": \(underlying!.localizedDescription)" : "")"
        case .removalFailed(let underlying):
            return "水印移除失败\(underlying != nil ? ": \(underlying!.localizedDescription)" : "")"
        case .managerNotAvailable:
            return "水印管理器不可用"
        case .settingsValidationFailed(let errors):
            return "设置验证失败: \(errors.count) 个错误"
        case .batchUpdateFailed(let failedCount):
            return "批量更新失败: \(failedCount) 个字段更新失败"
        case .serviceUnavailable:
            return "水印服务不可用"
        }
    }

    var failureReason: String? {
        switch self {
        case .invalidContainer(let reason):
            return reason
        case .invalidWatermarkType(let type):
            return "类型 '\(type)' 不在支持的水印类型列表中"
        case .styleCreationFailed(let type):
            return "无法为类型 '\(type)' 创建对应的水印样式实例"
        case .applicationFailed(let underlying):
            return underlying?.localizedDescription ?? "未知的应用错误"
        case .removalFailed(let underlying):
            return underlying?.localizedDescription ?? "未知的移除错误"
        case .managerNotAvailable:
            return "水印管理器不可用"
        case .settingsValidationFailed(let errors):
            return errors.map { "\($0.field): \($0.message)" }.joined(separator: ", ")
        case .batchUpdateFailed(let failedCount):
            return "有 \(failedCount) 个字段因类型不匹配而更新失败"
        case .serviceUnavailable:
            return "水印服务实例不可用或未正确初始化"
        }
    }

    var recoverySuggestion: String? {
        switch self {
        case .invalidContainer:
            return "请确保预览容器已正确初始化且具有有效的尺寸"
        case .invalidWatermarkType:
            return "请使用 getAvailableWatermarkTypes() 获取支持的水印类型列表"
        case .styleCreationFailed:
            return "请检查水印设置是否正确，或尝试使用其他水印类型"
        case .applicationFailed, .removalFailed:
            return "请检查预览容器状态，或重新初始化水印管理器"
        case .managerNotAvailable:
            return "请确保在使用前正确初始化水印管理器"
        case .settingsValidationFailed:
            return "请修正设置中的错误字段后重试"
        case .batchUpdateFailed:
            return "请检查更新数据的类型是否与目标字段匹配"
        case .serviceUnavailable:
            return "请重新初始化水印服务或检查依赖注入配置"
        }
    }
}

// MARK: - 日志记录系统

/// 水印服务日志级别
enum WatermarkLogLevel: String, CaseIterable {
    case debug = "🔍"      // 调试信息
    case info = "ℹ️"       // 一般信息
    case warning = "⚠️"    // 警告
    case error = "❌"      // 错误
    case success = "✅"    // 成功
    case operation = "🔄"  // 操作
}

/// 水印服务日志记录器
class WatermarkLogger {
    static let shared = WatermarkLogger()

    private let logQueue = DispatchQueue(label: "com.loniceralab.watermark.logger", qos: .utility)
    private var logHistory: [WatermarkLogEntry] = []
    private let maxLogEntries = 1000

    private init() {}

    /// 记录日志
    func log(_ level: WatermarkLogLevel, _ message: String, function: String = #function, file: String = #file, line: Int = #line) {
        logQueue.async { [weak self] in
            let entry = WatermarkLogEntry(
                level: level,
                message: message,
                timestamp: Date(),
                function: function,
                file: URL(fileURLWithPath: file).lastPathComponent,
                line: line
            )

            self?.addLogEntry(entry)

            // 输出到控制台
            DispatchQueue.main.async {
                print("\(level.rawValue) [WatermarkService] \(entry.file):\(entry.line) \(entry.function) - \(message)")
            }
        }
    }

    private func addLogEntry(_ entry: WatermarkLogEntry) {
        logHistory.append(entry)

        // 保持日志数量在限制内
        if logHistory.count > maxLogEntries {
            logHistory.removeFirst(logHistory.count - maxLogEntries)
        }
    }

    /// 获取日志历史
    func getLogHistory(level: WatermarkLogLevel? = nil, limit: Int = 100) -> [WatermarkLogEntry] {
        return logQueue.sync {
            var logs = logHistory

            if let level = level {
                logs = logs.filter { $0.level == level }
            }

            return Array(logs.suffix(limit))
        }
    }

    /// 清除日志历史
    func clearHistory() {
        logQueue.async { [weak self] in
            self?.logHistory.removeAll()
        }
    }
}

/// 日志条目
struct WatermarkLogEntry {
    let level: WatermarkLogLevel
    let message: String
    let timestamp: Date
    let function: String
    let file: String
    let line: Int
}

/// 服务健康状态
struct ServiceHealthStatus {
    let isHealthy: Bool                     // 服务是否健康
    let lastCheckTime: Date                 // 最后检查时间
    let errorCount: Int                     // 错误数量
    let warningCount: Int                   // 警告数量
    let uptime: TimeInterval                // 运行时间
    let memoryUsage: Double                 // 内存使用量（MB）
    let issues: [String]                    // 发现的问题

    /// 创建健康状态
    static func healthy() -> ServiceHealthStatus {
        return ServiceHealthStatus(
            isHealthy: true,
            lastCheckTime: Date(),
            errorCount: 0,
            warningCount: 0,
            uptime: ProcessInfo.processInfo.systemUptime,
            memoryUsage: 0.0,
            issues: []
        )
    }

    /// 创建不健康状态
    static func unhealthy(issues: [String], errorCount: Int = 0, warningCount: Int = 0) -> ServiceHealthStatus {
        return ServiceHealthStatus(
            isHealthy: false,
            lastCheckTime: Date(),
            errorCount: errorCount,
            warningCount: warningCount,
            uptime: ProcessInfo.processInfo.systemUptime,
            memoryUsage: 0.0,
            issues: issues
        )
    }
}

// MARK: - 缓存系统

/// 缓存项协议
protocol CacheItem {
    var key: String { get }
    var timestamp: Date { get }
    var size: Int { get }
}

/// 水印样式缓存项
struct WatermarkStyleCacheItem: CacheItem {
    let key: String
    let timestamp: Date
    let size: Int
    let watermarkType: String
    let settingsHash: String
    let stylePreview: WatermarkStylePreview
}

/// 设置缓存项
struct SettingsCacheItem: CacheItem {
    let key: String
    let timestamp: Date
    let size: Int
    let settings: WatermarkSettings
}

/// 缓存统计信息
struct CacheStats {
    let totalSize: Int              // 总大小（字节）
    let itemCount: Int              // 总项目数
    let stylePreviewCount: Int      // 样式预览缓存数
    let settingsCount: Int          // 设置缓存数
    let typeInfoCount: Int          // 类型信息缓存数
    let hitRate: Double             // 命中率
}

/// 性能优化结果
struct PerformanceOptimizationResult {
    let success: Bool                       // 是否成功
    let optimizationsApplied: [String]      // 应用的优化
    let performanceGain: Double             // 性能提升百分比
    let memoryFreed: Int                    // 释放的内存（字节）
    let cacheHitRate: Double                // 缓存命中率
    let message: String                     // 结果信息

    /// 创建成功结果
    static func success(optimizations: [String], gain: Double, memoryFreed: Int, hitRate: Double) -> PerformanceOptimizationResult {
        return PerformanceOptimizationResult(
            success: true,
            optimizationsApplied: optimizations,
            performanceGain: gain,
            memoryFreed: memoryFreed,
            cacheHitRate: hitRate,
            message: "性能优化完成，应用了 \(optimizations.count) 项优化"
        )
    }

    /// 创建失败结果
    static func failure(message: String) -> PerformanceOptimizationResult {
        return PerformanceOptimizationResult(
            success: false,
            optimizationsApplied: [],
            performanceGain: 0.0,
            memoryFreed: 0,
            cacheHitRate: 0.0,
            message: message
        )
    }
}

/// 缓存管理器
class WatermarkCache {
    static let shared = WatermarkCache()

    private let cacheQueue = DispatchQueue(label: "com.loniceralab.watermark.cache", qos: .utility)
    private var styleCache: [String: WatermarkStyleCacheItem] = [:]
    private var settingsCache: [String: SettingsCacheItem] = [:]
    private var typeInfoCache: [String: WatermarkTypeInfo] = [:]

    // 缓存配置
    private let maxCacheSize: Int = 50 * 1024 * 1024  // 50MB
    private let maxCacheAge: TimeInterval = 300       // 5分钟
    private let maxItems: Int = 1000

    private var currentCacheSize: Int = 0
    private let logger = WatermarkLogger.shared

    private init() {}

    /// 获取样式预览缓存
    func getStylePreview(for type: String, settingsHash: String) -> WatermarkStylePreview? {
        return cacheQueue.sync {
            let key = "\(type)_\(settingsHash)"

            guard let item = styleCache[key] else {
                return nil
            }

            // 检查缓存是否过期
            if Date().timeIntervalSince(item.timestamp) > maxCacheAge {
                styleCache.removeValue(forKey: key)
                currentCacheSize -= item.size
                logger.log(.debug, "样式预览缓存过期，已移除: \(key)")
                return nil
            }

            logger.log(.debug, "命中样式预览缓存: \(key)")
            return item.stylePreview
        }
    }

    /// 设置样式预览缓存
    func setStylePreview(_ preview: WatermarkStylePreview, for type: String, settingsHash: String) {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }

            let key = "\(type)_\(settingsHash)"
            let size = MemoryLayout<WatermarkStylePreview>.size
            let item = WatermarkStyleCacheItem(
                key: key,
                timestamp: Date(),
                size: size,
                watermarkType: type,
                settingsHash: settingsHash,
                stylePreview: preview
            )

            self.styleCache[key] = item
            self.currentCacheSize += size

            self.logger.log(.debug, "缓存样式预览: \(key)")

            // 检查缓存大小限制
            self.evictIfNeeded()
        }
    }

    /// 获取类型信息缓存
    func getTypeInfo(for type: String) -> WatermarkTypeInfo? {
        return cacheQueue.sync {
            let info = typeInfoCache[type]
            if info != nil {
                logger.log(.debug, "命中类型信息缓存: \(type)")
            }
            return info
        }
    }

    /// 设置类型信息缓存
    func setTypeInfo(_ info: WatermarkTypeInfo, for type: String) {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }

            self.typeInfoCache[type] = info
            self.logger.log(.debug, "缓存类型信息: \(type)")
        }
    }

    /// 清除所有缓存
    func clearAll() {
        cacheQueue.async { [weak self] in
            guard let self = self else { return }

            self.styleCache.removeAll()
            self.settingsCache.removeAll()
            self.typeInfoCache.removeAll()
            self.currentCacheSize = 0

            self.logger.log(.operation, "清除所有缓存")
        }
    }

    /// 获取缓存统计信息
    func getCacheStats() -> CacheStats {
        return cacheQueue.sync {
            return CacheStats(
                totalSize: currentCacheSize,
                itemCount: styleCache.count + settingsCache.count + typeInfoCache.count,
                stylePreviewCount: styleCache.count,
                settingsCount: settingsCache.count,
                typeInfoCount: typeInfoCache.count,
                hitRate: 0.0 // TODO: 实现命中率统计
            )
        }
    }

    /// 缓存清理
    private func evictIfNeeded() {
        // 检查缓存大小
        if currentCacheSize > maxCacheSize {
            evictLRU()
        }

        // 检查项目数量
        let totalItems = styleCache.count + settingsCache.count
        if totalItems > maxItems {
            evictLRU()
        }
    }

    /// LRU清理策略
    private func evictLRU() {
        // 合并所有缓存项并按时间排序
        var allItems: [(key: String, timestamp: Date, size: Int, type: String)] = []

        for (key, item) in styleCache {
            allItems.append((key: key, timestamp: item.timestamp, size: item.size, type: "style"))
        }

        for (key, item) in settingsCache {
            allItems.append((key: key, timestamp: item.timestamp, size: item.size, type: "settings"))
        }

        // 按时间排序，最旧的在前
        allItems.sort { $0.timestamp < $1.timestamp }

        // 移除最旧的项目，直到满足限制
        let targetSize = maxCacheSize * 3 / 4  // 清理到75%
        var removedCount = 0

        for item in allItems {
            if currentCacheSize <= targetSize {
                break
            }

            switch item.type {
            case "style":
                styleCache.removeValue(forKey: item.key)
            case "settings":
                settingsCache.removeValue(forKey: item.key)
            default:
                break
            }

            currentCacheSize -= item.size
            removedCount += 1
        }

        if removedCount > 0 {
            logger.log(.operation, "LRU清理完成，移除 \(removedCount) 个缓存项")
        }
    }
}

/// 水印类型信息
struct WatermarkTypeInfo {
    let type: String                    // 水印类型标识
    let displayName: String             // 显示名称
    let category: String                // 所属分类
    let description: String             // 描述
    let isAvailable: Bool               // 是否可用
    let supportedFeatures: [String]     // 支持的功能
    let previewImageName: String?       // 预览图片名称

    /// 创建基础水印类型信息
    static func basic(type: String, displayName: String, category: String) -> WatermarkTypeInfo {
        return WatermarkTypeInfo(
            type: type,
            displayName: displayName,
            category: category,
            description: "水印样式 \(displayName)",
            isAvailable: true,
            supportedFeatures: ["border", "text", "logo"],
            previewImageName: nil
        )
    }
}

/// 水印分类信息
struct WatermarkCategoryInfo {
    let category: String                // 分类标识
    let displayName: String             // 显示名称
    let description: String             // 描述
    let watermarkTypes: [String]        // 包含的水印类型
    let isEnabled: Bool                 // 是否启用

    /// 创建基础分类信息
    static func basic(category: String, displayName: String, types: [String]) -> WatermarkCategoryInfo {
        return WatermarkCategoryInfo(
            category: category,
            displayName: displayName,
            description: "水印分类 \(displayName)",
            watermarkTypes: types,
            isEnabled: true
        )
    }
}

/// 水印样式预览
struct WatermarkStylePreview {
    let type: String                    // 水印类型
    let previewDescription: String      // 预览描述
    let estimatedSize: CGSize           // 预估尺寸
    let requiredFeatures: [String]      // 需要的功能
    let compatibilityInfo: String       // 兼容性信息
}

// MARK: - 水印服务协议
/// 水印业务服务协议，定义所有水印相关的业务操作
/// **第二阶段扩展：添加完善的业务逻辑方法**
protocol WatermarkServiceProtocol {
    // MARK: - 基础设置管理
    func getSettings() -> WatermarkSettings
    func saveSettings(_ settings: WatermarkSettings)
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T)
    func resetToDefaults()

    // MARK: - 批量操作
    func batchUpdateSettings(_ updates: [PartialKeyPath<WatermarkSettings>: Any]) throws
    func batchUpdateSettings(using closure: (inout WatermarkSettings) -> Void)

    // MARK: - 设置验证
    func validateSettings(_ settings: WatermarkSettings) -> ValidationResult
    func isValidWatermarkType(_ type: String) -> Bool

    // MARK: - 设置比较
    func compareSettings(_ settings1: WatermarkSettings, _ settings2: WatermarkSettings) -> SettingsDifference
    func hasChanges(_ settings: WatermarkSettings) -> Bool

    // MARK: - 水印应用逻辑
    func applyWatermark(_ styleType: String, to container: UIView) -> WatermarkApplicationResult
    func removeWatermark(from container: UIView) -> WatermarkApplicationResult
    func getCurrentWatermarkType(for container: UIView) -> String?
    func isWatermarkApplied(to container: UIView) -> Bool



    // MARK: - 样式管理
    func getAvailableWatermarkTypes() -> [WatermarkTypeInfo]
    func getWatermarkTypeInfo(_ type: String) -> WatermarkTypeInfo?
    func getWatermarkTypesByCategory(_ category: String) -> [String]
    func getAllWatermarkCategories() -> [WatermarkCategoryInfo]
    func previewWatermarkStyle(_ type: String, settings: WatermarkSettings?) -> WatermarkStylePreview?

    // MARK: - 错误处理和监控
    func getServiceHealth() -> ServiceHealthStatus
    func getErrorHistory(limit: Int) -> [WatermarkLogEntry]
    func clearErrorHistory()
    func performHealthCheck() -> ValidationResult

    // MARK: - 性能优化和缓存
    func getCacheStats() -> CacheStats
    func clearCache()
    func preloadCommonWatermarkTypes()
    func optimizePerformance() -> PerformanceOptimizationResult
}

// MARK: - 水印服务实现
/// 水印业务服务实现类
/// **真正重构：直接操作SwiftData，彻底移除Manager依赖**
/// **这是真正的MVVM-S架构实现**
class WatermarkService: WatermarkServiceProtocol {

    // MARK: - 私有属性
    /// SwiftData容器
    private let container: ModelContainer
    /// 日志记录器
    private let logger = WatermarkLogger.shared
    /// 缓存管理器
    private let cache = WatermarkCache.shared

    /// ModelContext（用于数据操作）
    @MainActor
    private var modelContext: ModelContext {
        return container.mainContext
    }

    /// 同步获取ModelContext（创建新的上下文以避免主线程隔离问题）
    private func getModelContextSync() -> ModelContext {
        // 创建一个新的 ModelContext 而不是使用 mainContext
        // 这样可以避免主线程隔离问题
        return ModelContext(container)
    }

    // MARK: - 初始化
    /// 初始化水印服务
    /// - Parameter container: SwiftData容器
    init(container: ModelContainer) {
        self.container = container
        print("🏭 [WatermarkService] 初始化水印服务（真正的SwiftData操作）")
    }

    /// 便利初始化方法（使用共享容器）
    convenience init() {
        // 使用SharedService的容器
        self.init(container: SharedService.shared.container)
    }

    // MARK: - 设置管理（真正的SwiftData实现）

    /// 获取水印设置
    /// **真正实现：直接从SwiftData获取，不依赖Manager**
    func getSettings() -> WatermarkSettings {
        print("📖 [WatermarkService] getSettings() - 直接从SwiftData获取")

        // 使用同步方式获取ModelContext并执行操作
        let context = getModelContextSync()

        do {
            let descriptor = FetchDescriptor<WatermarkSettings>(
                predicate: #Predicate { $0.id == "watermark_settings" }
            )
            let existingSettings = try context.fetch(descriptor)

            if let settings = existingSettings.first {
                print("✅ [WatermarkService] 成功获取现有设置，水印类型: \(settings.activeWatermarkStyleType)")
                return settings
            } else {
                print("🆕 [WatermarkService] 未找到设置，创建默认设置")
                let newSettings = WatermarkSettings()
                context.insert(newSettings)

                do {
                    try context.save()
                    print("✅ [WatermarkService] 默认设置已保存")
                } catch {
                    print("❌ [WatermarkService] 保存默认设置失败: \(error.localizedDescription)")
                }
                return newSettings
            }
        } catch {
            print("❌ [WatermarkService] 获取设置失败: \(error.localizedDescription)")
            return WatermarkSettings() // 返回临时设置，避免崩溃
        }
    }

    /// 保存水印设置
    /// **真正实现：直接保存到SwiftData，不依赖Manager**
    /// - Parameter settings: 要保存的设置
    func saveSettings(_ settings: WatermarkSettings) {
        print("� [WatermarkService] saveSettings() - 直接保存到SwiftData")

        let saveOperation = {
            let context = self.getModelContextSync()
            do {
                settings.updateTimestamp()
                try context.save()
                print("✅ [WatermarkService] 设置保存成功")
            } catch {
                print("❌ [WatermarkService] 保存设置失败: \(error.localizedDescription)")
            }
        }

        if Thread.isMainThread {
            saveOperation()
        } else {
            DispatchQueue.main.sync(execute: saveOperation)
        }
    }

    /// 更新特定设置
    /// **真正实现：直接操作SwiftData，不依赖Manager**
    /// - Parameters:
    ///   - keyPath: 设置的键路径
    ///   - value: 新值
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T) {
        print("🔄 [WatermarkService] updateSetting() - 直接更新SwiftData")

        var settings = getSettings() // 获取当前设置
        let oldValue = "\(settings[keyPath: keyPath])"
        settings[keyPath: keyPath] = value

        print("🔄 [WatermarkService] 更新 \(String(describing: keyPath)): '\(oldValue)' -> '\(value)'")
        saveSettings(settings)

        // 如果更新的是水印类型，发送通知（保持兼容性）
        if keyPath == \WatermarkSettings.activeWatermarkStyleType {
            NotificationCenter.default.post(
                name: Notification.Name("WatermarkTypeChanged"),
                object: value
            )
            print("📢 [WatermarkService] 发送水印类型变更通知: \(value)")
        }
    }

    /// 重置为默认设置
    /// **真正实现：直接操作SwiftData，不依赖Manager**
    func resetToDefaults() {
        print("🔄 [WatermarkService] resetToDefaults() - 直接重置SwiftData")

        let resetOperation = {
            let context = self.getModelContextSync()
            do {
                // 删除所有现有设置
                let descriptor = FetchDescriptor<WatermarkSettings>()
                let existingSettings = try context.fetch(descriptor)

                for settings in existingSettings {
                    context.delete(settings)
                }

                // 创建新的默认设置
                let newSettings = WatermarkSettings()
                context.insert(newSettings)
                try context.save()

                print("✅ [WatermarkService] 重置为默认设置成功")
            } catch {
                print("❌ [WatermarkService] 重置设置失败: \(error.localizedDescription)")
            }
        }

        if Thread.isMainThread {
            resetOperation()
        } else {
            DispatchQueue.main.sync(execute: resetOperation)
        }
    }

    // MARK: - 批量操作方法

    /// 批量更新设置（使用键值对）
    /// - Parameter updates: 要更新的设置键值对
    /// - Throws: WatermarkError.batchUpdateFailed 如果有字段更新失败
    func batchUpdateSettings(_ updates: [PartialKeyPath<WatermarkSettings>: Any]) throws {
        logger.log(.operation, "开始批量更新 \(updates.count) 个设置")

        var settings = getSettings()
        var updateCount = 0
        var failedCount = 0
        var updateDetails: [String] = []

        for (keyPath, value) in updates {
            // 使用反射来设置值（这里需要类型安全的实现）
            if let writableKeyPath = keyPath as? WritableKeyPath<WatermarkSettings, String>,
               let stringValue = value as? String {
                let oldValue = settings[keyPath: writableKeyPath]
                settings[keyPath: writableKeyPath] = stringValue
                updateDetails.append("字符串字段: '\(oldValue)' -> '\(stringValue)'")
                updateCount += 1
            } else if let writableKeyPath = keyPath as? WritableKeyPath<WatermarkSettings, Double>,
                      let doubleValue = value as? Double {
                let oldValue = settings[keyPath: writableKeyPath]
                settings[keyPath: writableKeyPath] = doubleValue
                updateDetails.append("数值字段: \(oldValue) -> \(doubleValue)")
                updateCount += 1
            } else if let writableKeyPath = keyPath as? WritableKeyPath<WatermarkSettings, Bool>,
                      let boolValue = value as? Bool {
                let oldValue = settings[keyPath: writableKeyPath]
                settings[keyPath: writableKeyPath] = boolValue
                updateDetails.append("布尔字段: \(oldValue) -> \(boolValue)")
                updateCount += 1
            } else {
                logger.log(.warning, "字段类型不匹配，跳过更新: \(keyPath)")
                failedCount += 1
            }
        }

        // 记录更新详情
        for detail in updateDetails {
            logger.log(.debug, "更新: \(detail)")
        }

        if updateCount > 0 {
            do {
                saveSettings(settings)
                logger.log(.success, "批量更新完成，成功更新 \(updateCount) 个字段")

                if failedCount > 0 {
                    logger.log(.warning, "有 \(failedCount) 个字段更新失败")
                }
            } catch {
                logger.log(.error, "保存设置失败: \(error.localizedDescription)")
                throw WatermarkError.batchUpdateFailed(failedCount: failedCount + updateCount)
            }
        } else {
            logger.log(.warning, "批量更新完成，但没有字段被更新")
            if failedCount > 0 {
                throw WatermarkError.batchUpdateFailed(failedCount: failedCount)
            }
        }
    }

    /// 批量更新设置（使用闭包）
    /// - Parameter closure: 更新闭包
    func batchUpdateSettings(using closure: (inout WatermarkSettings) -> Void) {
        print("🔄 [WatermarkService] batchUpdateSettings(closure) - 使用闭包批量更新")

        var settings = getSettings()
        let originalSettings = settings // 保存原始设置用于比较

        closure(&settings)

        // 比较变化
        let difference = compareSettings(originalSettings, settings)
        if difference.hasChanges {
            saveSettings(settings)
            print("✅ [WatermarkService] 闭包批量更新完成，变更字段数: \(difference.changedFields.count)")
        } else {
            print("ℹ️ [WatermarkService] 闭包批量更新完成，但没有实际变化")
        }
    }

    // MARK: - 设置验证方法

    /// 验证水印设置
    /// - Parameter settings: 要验证的设置
    /// - Returns: 验证结果
    func validateSettings(_ settings: WatermarkSettings) -> ValidationResult {
        print("🔍 [WatermarkService] validateSettings() - 验证设置")

        var errors: [ValidationError] = []
        var warnings: [ValidationWarning] = []

        // 验证水印类型
        if !isValidWatermarkType(settings.activeWatermarkStyleType) {
            errors.append(ValidationError(
                field: "activeWatermarkStyleType",
                message: "无效的水印类型: \(settings.activeWatermarkStyleType)",
                code: "INVALID_WATERMARK_TYPE"
            ))
        }

        // 验证数值范围
        if settings.borderThicknessMultiplier < 0 || settings.borderThicknessMultiplier > 1 {
            errors.append(ValidationError(
                field: "borderThicknessMultiplier",
                message: "边框粗细必须在0-1之间，当前值: \(settings.borderThicknessMultiplier)",
                code: "INVALID_BORDER_THICKNESS"
            ))
        }

        if settings.logoSizeMultiplier < 0.1 || settings.logoSizeMultiplier > 3.0 {
            warnings.append(ValidationWarning(
                field: "logoSizeMultiplier",
                message: "Logo大小超出推荐范围(0.1-3.0)，当前值: \(settings.logoSizeMultiplier)",
                code: "LOGO_SIZE_OUT_OF_RANGE"
            ))
        }

        // 验证字体设置
        if settings.selectedFontName.isEmpty {
            warnings.append(ValidationWarning(
                field: "selectedFontName",
                message: "未设置字体名称，将使用默认字体",
                code: "EMPTY_FONT_NAME"
            ))
        }

        // 验证颜色值范围
        let colorFields = [
            ("borderColorRed", settings.borderColorRed),
            ("borderColorGreen", settings.borderColorGreen),
            ("borderColorBlue", settings.borderColorBlue),
            ("fontColorRed", settings.fontColorRed),
            ("fontColorGreen", settings.fontColorGreen),
            ("fontColorBlue", settings.fontColorBlue)
        ]

        for (fieldName, colorValue) in colorFields {
            if colorValue < 0 || colorValue > 1 {
                errors.append(ValidationError(
                    field: fieldName,
                    message: "颜色值必须在0-1之间，当前值: \(colorValue)",
                    code: "INVALID_COLOR_VALUE"
                ))
            }
        }

        let result = errors.isEmpty ? ValidationResult.success : ValidationResult.failure(errors: errors, warnings: warnings)
        print("✅ [WatermarkService] 验证完成 - 有效: \(result.isValid), 错误: \(errors.count), 警告: \(warnings.count)")
        return result
    }

    /// 检查水印类型是否有效
    /// - Parameter type: 水印类型
    /// - Returns: 是否有效
    func isValidWatermarkType(_ type: String) -> Bool {
        // 定义有效的水印类型列表
        let validTypes = [
            "none", "watermark1", "watermark2", "watermark3", "watermark4", "watermark5",
            "watermark6", "watermark7", "watermark8", "watermark9", "watermark10",
            "watermark11", "watermark12", "watermark13", "watermark14", "watermark15",
            "watermark16", "watermark17", "watermark18", "watermark19", "watermark20",
            "watermark21", "watermark22", "watermark23", "watermark24", "watermark25"
        ]

        let isValid = validTypes.contains(type)
        print("🔍 [WatermarkService] 验证水印类型 '\(type)': \(isValid ? "有效" : "无效")")
        return isValid
    }

    // MARK: - 设置比较方法

    /// 比较两个设置对象的差异
    /// - Parameters:
    ///   - settings1: 第一个设置对象
    ///   - settings2: 第二个设置对象
    /// - Returns: 设置差异
    func compareSettings(_ settings1: WatermarkSettings, _ settings2: WatermarkSettings) -> SettingsDifference {
        print("🔍 [WatermarkService] compareSettings() - 比较设置差异")

        var changedFields: [String: (old: Any, new: Any)] = [:]

        // 比较主要字段
        if settings1.activeWatermarkStyleType != settings2.activeWatermarkStyleType {
            changedFields["activeWatermarkStyleType"] = (old: settings1.activeWatermarkStyleType, new: settings2.activeWatermarkStyleType)
        }

        if settings1.selectedFontName != settings2.selectedFontName {
            changedFields["selectedFontName"] = (old: settings1.selectedFontName, new: settings2.selectedFontName)
        }

        if settings1.selectedEnglishFontName != settings2.selectedEnglishFontName {
            changedFields["selectedEnglishFontName"] = (old: settings1.selectedEnglishFontName, new: settings2.selectedEnglishFontName)
        }

        // 比较数值字段
        let numericFields: [(String, Double, Double)] = [
            ("borderThicknessMultiplier", settings1.borderThicknessMultiplier, settings2.borderThicknessMultiplier),
            ("wideBorderThicknessMultiplier", settings1.wideBorderThicknessMultiplier, settings2.wideBorderThicknessMultiplier),
            ("logoSizeMultiplier", settings1.logoSizeMultiplier, settings2.logoSizeMultiplier),
            ("signatureFontSizeMultiplier", settings1.signatureFontSizeMultiplier, settings2.signatureFontSizeMultiplier),
            ("textFontSizeMultiplier", settings1.textFontSizeMultiplier, settings2.textFontSizeMultiplier),
            ("preferenceScaleFactor", settings1.preferenceScaleFactor, settings2.preferenceScaleFactor)
        ]

        for (fieldName, oldValue, newValue) in numericFields {
            if abs(oldValue - newValue) > 0.001 { // 使用小的阈值来比较浮点数
                changedFields[fieldName] = (old: oldValue, new: newValue)
            }
        }

        // 比较颜色字段
        let colorFields: [(String, Double, Double)] = [
            ("borderColorRed", settings1.borderColorRed, settings2.borderColorRed),
            ("borderColorGreen", settings1.borderColorGreen, settings2.borderColorGreen),
            ("borderColorBlue", settings1.borderColorBlue, settings2.borderColorBlue),
            ("fontColorRed", settings1.fontColorRed, settings2.fontColorRed),
            ("fontColorGreen", settings1.fontColorGreen, settings2.fontColorGreen),
            ("fontColorBlue", settings1.fontColorBlue, settings2.fontColorBlue)
        ]

        for (fieldName, oldValue, newValue) in colorFields {
            if abs(oldValue - newValue) > 0.001 {
                changedFields[fieldName] = (old: oldValue, new: newValue)
            }
        }

        // 比较布尔字段
        let boolFields: [(String, Bool, Bool)] = [
            ("isWatermarkTextEnabled", settings1.isWatermarkTextEnabled, settings2.isWatermarkTextEnabled),
            ("isWatermarkSignatureEnabled", settings1.isWatermarkSignatureEnabled, settings2.isWatermarkSignatureEnabled),
            ("isBlurBorderEnabled", settings1.isBlurBorderEnabled, settings2.isBlurBorderEnabled),
            ("isShadowEnabled", settings1.isShadowEnabled, settings2.isShadowEnabled)
        ]

        for (fieldName, oldValue, newValue) in boolFields {
            if oldValue != newValue {
                changedFields[fieldName] = (old: oldValue, new: newValue)
            }
        }

        let hasChanges = !changedFields.isEmpty
        let result = SettingsDifference(
            hasChanges: hasChanges,
            changedFields: changedFields,
            addedFields: [:], // 暂时不支持动态字段
            removedFields: [:] // 暂时不支持动态字段
        )

        print("✅ [WatermarkService] 比较完成 - 有变化: \(hasChanges), 变更字段数: \(changedFields.count)")
        return result
    }

    /// 检查设置是否有变化（与默认设置比较）
    /// - Parameter settings: 要检查的设置
    /// - Returns: 是否有变化
    func hasChanges(_ settings: WatermarkSettings) -> Bool {
        let defaultSettings = WatermarkSettings()
        let difference = compareSettings(defaultSettings, settings)

        print("🔍 [WatermarkService] hasChanges() - 与默认设置比较: \(difference.hasChanges)")
        return difference.hasChanges
    }

    // MARK: - 水印应用逻辑方法

    /// 应用水印到指定容器
    /// **注意：这个方法暂时不实现，保持现有的架构分层**
    /// - Parameters:
    ///   - styleType: 水印样式类型
    ///   - container: 目标容器
    /// - Returns: 应用结果
    func applyWatermark(_ styleType: String, to container: UIView) -> WatermarkApplicationResult {
        // ✅ 第三阶段重构：Service层不应该直接操作UI管理器
        // 这违反了MVVM-S架构的分层原则
        logger.log(.info, "applyWatermark() - 已重构为依赖注入模式，请使用ViewModel层调用")
        return WatermarkApplicationResult.failure(
            message: "请使用ViewModel层的依赖注入方式",
            error: WatermarkError.managerNotAvailable
        )
    }

    /// 从指定容器移除水印
    /// **注意：这个方法暂时不实现，保持现有的架构分层**
    /// - Parameter container: 目标容器
    /// - Returns: 移除结果
    func removeWatermark(from container: UIView) -> WatermarkApplicationResult {
        // ✅ 第三阶段重构：Service层不应该直接操作UI管理器
        // 这违反了MVVM-S架构的分层原则
        logger.log(.info, "removeWatermark() - 已重构为依赖注入模式，请使用ViewModel层调用")
        return WatermarkApplicationResult.failure(
            message: "请使用ViewModel层的依赖注入方式",
            error: WatermarkError.managerNotAvailable
        )
    }

    /// 获取指定容器的当前水印类型
    /// - Parameter container: 目标容器
    /// - Returns: 当前水印类型，如果没有水印则返回nil
    func getCurrentWatermarkType(for container: UIView) -> String? {
        let settings = getSettings()
        let currentType = settings.activeWatermarkStyleType

        // 如果是"none"，返回nil表示没有水印
        if currentType == "none" {
            print("🔍 [WatermarkService] getCurrentWatermarkType() - 当前无水印")
            return nil
        }

        print("🔍 [WatermarkService] getCurrentWatermarkType() - 当前水印类型: \(currentType)")
        return currentType
    }

    /// 检查指定容器是否已应用水印
    /// - Parameter container: 目标容器
    /// - Returns: 是否已应用水印
    func isWatermarkApplied(to container: UIView) -> Bool {
        let currentType = getCurrentWatermarkType(for: container)
        let isApplied = currentType != nil

        print("🔍 [WatermarkService] isWatermarkApplied() - 是否已应用水印: \(isApplied)")
        return isApplied
    }

    // MARK: - 样式管理方法

    /// 获取所有可用的水印类型信息
    /// - Returns: 水印类型信息数组
    func getAvailableWatermarkTypes() -> [WatermarkTypeInfo] {
        print("📋 [WatermarkService] getAvailableWatermarkTypes() - 获取可用水印类型")

        var watermarkTypes: [WatermarkTypeInfo] = []

        // 添加"无水印"选项
        watermarkTypes.append(WatermarkTypeInfo(
            type: "none",
            displayName: "无水印",
            category: "基础",
            description: "不应用任何水印效果",
            isAvailable: true,
            supportedFeatures: [],
            previewImageName: nil
        ))

        // 添加基础水印类型（watermark1-watermark18）
        for i in 1...18 {
            let type = "watermark\(i)"
            watermarkTypes.append(WatermarkTypeInfo.basic(
                type: type,
                displayName: "水印\(i)",
                category: "经典"
            ))
        }

        // 添加特殊水印类型
        let specialTypes = [
            ("watermark19", "大疆水印", "大疆"),
            ("watermark20", "节日水印1", "节日"),
            ("watermark21", "节日水印2", "节日"),
            ("watermark22", "节日水印3", "节日"),
            ("watermark23", "拼图水印1", "拼图"),
            ("watermark24", "拼图水印2", "拼图"),
            ("watermark25", "拼图水印3", "拼图")
        ]

        for (type, displayName, category) in specialTypes {
            watermarkTypes.append(WatermarkTypeInfo.basic(
                type: type,
                displayName: displayName,
                category: category
            ))
        }

        print("📋 [WatermarkService] 返回 \(watermarkTypes.count) 个水印类型")
        return watermarkTypes
    }

    /// 获取指定水印类型的详细信息
    /// - Parameter type: 水印类型
    /// - Returns: 水印类型信息，如果不存在则返回nil
    func getWatermarkTypeInfo(_ type: String) -> WatermarkTypeInfo? {
        logger.log(.debug, "获取水印类型信息: \(type)")

        // 先检查缓存
        if let cachedInfo = cache.getTypeInfo(for: type) {
            return cachedInfo
        }

        // 缓存未命中，从完整列表中查找
        let allTypes = getAvailableWatermarkTypes()
        let typeInfo = allTypes.first { $0.type == type }

        if let info = typeInfo {
            // 缓存结果
            cache.setTypeInfo(info, for: type)
            logger.log(.success, "找到水印类型信息: \(info.displayName)")
        } else {
            logger.log(.warning, "未找到水印类型: \(type)")
        }

        return typeInfo
    }

    /// 根据分类获取水印类型列表
    /// - Parameter category: 分类名称
    /// - Returns: 该分类下的水印类型数组
    func getWatermarkTypesByCategory(_ category: String) -> [String] {
        print("📂 [WatermarkService] getWatermarkTypesByCategory(\(category)) - 获取分类水印")

        // 定义分类映射（与WatermarkControlView中的categoryWatermarkMap保持一致）
        let categoryMap: [String: [String]] = [
            "经典": Array(1...17).map { "watermark\($0)" },
            "无框": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17].map { "watermark\($0)" },
            "简约": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17].map { "watermark\($0)" },
            "大师": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17].map { "watermark\($0)" },
            "大疆": ["watermark19"],
            "节日": ["watermark20", "watermark21", "watermark22"],
            "拼图": ["watermark23", "watermark24", "watermark25"]
        ]

        let types = categoryMap[category] ?? []
        print("📂 [WatermarkService] 分类 '\(category)' 包含 \(types.count) 个水印类型")
        return types
    }

    /// 获取所有水印分类信息
    /// - Returns: 水印分类信息数组
    func getAllWatermarkCategories() -> [WatermarkCategoryInfo] {
        print("📋 [WatermarkService] getAllWatermarkCategories() - 获取所有分类")

        let categories = [
            WatermarkCategoryInfo.basic(
                category: "经典",
                displayName: "经典",
                types: getWatermarkTypesByCategory("经典")
            ),
            WatermarkCategoryInfo.basic(
                category: "无框",
                displayName: "无框",
                types: getWatermarkTypesByCategory("无框")
            ),
            WatermarkCategoryInfo.basic(
                category: "简约",
                displayName: "简约",
                types: getWatermarkTypesByCategory("简约")
            ),
            WatermarkCategoryInfo.basic(
                category: "大师",
                displayName: "大师",
                types: getWatermarkTypesByCategory("大师")
            ),
            WatermarkCategoryInfo.basic(
                category: "大疆",
                displayName: "大疆",
                types: getWatermarkTypesByCategory("大疆")
            ),
            WatermarkCategoryInfo.basic(
                category: "节日",
                displayName: "节日",
                types: getWatermarkTypesByCategory("节日")
            ),
            WatermarkCategoryInfo.basic(
                category: "拼图",
                displayName: "拼图",
                types: getWatermarkTypesByCategory("拼图")
            )
        ]

        print("📋 [WatermarkService] 返回 \(categories.count) 个分类")
        return categories
    }

    /// 预览水印样式（不实际应用）
    /// - Parameters:
    ///   - type: 水印类型
    ///   - settings: 水印设置（可选，使用当前设置如果为nil）
    /// - Returns: 水印样式预览信息
    func previewWatermarkStyle(_ type: String, settings: WatermarkSettings? = nil) -> WatermarkStylePreview? {
        logger.log(.debug, "预览水印样式: \(type)")

        // 验证水印类型
        guard isValidWatermarkType(type) else {
            logger.log(.error, "无效的水印类型: \(type)")
            return nil
        }

        // 使用提供的设置或当前设置
        let watermarkSettings = settings ?? getSettings()

        // 生成设置哈希用于缓存
        let settingsHash = generateSettingsHash(watermarkSettings)

        // 检查缓存
        if let cachedPreview = cache.getStylePreview(for: type, settingsHash: settingsHash) {
            return cachedPreview
        }

        // 缓存未命中，创建预览
        // 尝试创建水印样式以验证可行性
        guard WatermarkStyleFactory.createWatermarkStyle(type: type, settings: watermarkSettings) != nil else {
            logger.log(.error, "无法创建水印样式: \(type)")
            return nil
        }

        // 分析水印特性
        var requiredFeatures: [String] = []
        var compatibilityInfo = "兼容所有设备"

        // 根据水印类型分析需要的功能
        if type.contains("watermark") && type != "none" {
            requiredFeatures.append("边框")

            if watermarkSettings.isWatermarkTextEnabled {
                requiredFeatures.append("文字")
            }

            if !watermarkSettings.selectedLogo.isEmpty {
                requiredFeatures.append("Logo")
            }

            if watermarkSettings.isWatermarkSignatureEnabled {
                requiredFeatures.append("署名")
            }
        }

        // 特殊水印的兼容性信息
        if type == "watermark19" {
            compatibilityInfo = "适用于航拍照片"
        } else if ["watermark20", "watermark21", "watermark22"].contains(type) {
            compatibilityInfo = "适用于节日主题照片"
        } else if ["watermark23", "watermark24", "watermark25"].contains(type) {
            compatibilityInfo = "需要多张照片进行拼图"
        }

        let preview = WatermarkStylePreview(
            type: type,
            previewDescription: "水印样式 \(type) 的预览",
            estimatedSize: CGSize(width: 300, height: 400), // 预估尺寸
            requiredFeatures: requiredFeatures,
            compatibilityInfo: compatibilityInfo
        )

        // 缓存预览结果
        cache.setStylePreview(preview, for: type, settingsHash: settingsHash)

        logger.log(.success, "生成预览信息: \(requiredFeatures.count) 个功能")
        return preview
    }

    /// 生成设置哈希值用于缓存
    /// - Parameter settings: 水印设置
    /// - Returns: 哈希字符串
    private func generateSettingsHash(_ settings: WatermarkSettings) -> String {
        // 简化的哈希生成，实际项目中可能需要更复杂的实现
        let hashComponents = [
            settings.activeWatermarkStyleType,
            settings.selectedFontName,
            settings.selectedEnglishFontName,
            String(settings.borderThicknessMultiplier),
            String(settings.logoSizeMultiplier),
            String(settings.isWatermarkTextEnabled),
            String(settings.isWatermarkSignatureEnabled)
        ]

        return hashComponents.joined(separator: "_").hash.description
    }

    // MARK: - 错误处理和监控方法

    /// 获取服务健康状态
    /// - Returns: 服务健康状态
    func getServiceHealth() -> ServiceHealthStatus {
        logger.log(.debug, "检查服务健康状态")

        let errorLogs = logger.getLogHistory(level: .error, limit: 100)
        let warningLogs = logger.getLogHistory(level: .warning, limit: 100)

        var issues: [String] = []

        // 检查错误数量
        if errorLogs.count > 10 {
            issues.append("错误日志过多: \(errorLogs.count) 条")
        }

        // 检查警告数量
        if warningLogs.count > 20 {
            issues.append("警告日志过多: \(warningLogs.count) 条")
        }

        // ✅ 第三阶段重构：不再检查单例管理器状态
        // 现在使用依赖注入模式，管理器状态由ViewModel层管理

        // 检查设置有效性
        let settings = getSettings()
        let validationResult = validateSettings(settings)
        if !validationResult.isValid {
            issues.append("当前设置无效: \(validationResult.errors.count) 个错误")
        }

        let isHealthy = issues.isEmpty
        let status = isHealthy ?
            ServiceHealthStatus.healthy() :
            ServiceHealthStatus.unhealthy(issues: issues, errorCount: errorLogs.count, warningCount: warningLogs.count)

        logger.log(isHealthy ? .success : .warning, "服务健康检查完成: \(isHealthy ? "健康" : "发现 \(issues.count) 个问题")")
        return status
    }

    /// 获取错误历史
    /// - Parameter limit: 限制数量
    /// - Returns: 错误日志条目数组
    func getErrorHistory(limit: Int = 50) -> [WatermarkLogEntry] {
        logger.log(.debug, "获取错误历史，限制: \(limit)")
        return logger.getLogHistory(level: .error, limit: limit)
    }

    /// 清除错误历史
    func clearErrorHistory() {
        logger.log(.operation, "清除错误历史")
        logger.clearHistory()
    }

    /// 执行健康检查
    /// - Returns: 健康检查结果
    func performHealthCheck() -> ValidationResult {
        logger.log(.operation, "执行完整健康检查")

        var errors: [ValidationError] = []
        var warnings: [ValidationWarning] = []

        // ✅ 第三阶段重构：不再检查单例管理器依赖
        // 现在使用依赖注入模式，依赖关系由DI容器管理

        // 检查设置
        let settings = getSettings()
        let settingsValidation = validateSettings(settings)
        errors.append(contentsOf: settingsValidation.errors)
        warnings.append(contentsOf: settingsValidation.warnings)

        // 检查可用水印类型
        let availableTypes = getAvailableWatermarkTypes()
        if availableTypes.isEmpty {
            warnings.append(ValidationWarning(
                field: "watermarkTypes",
                message: "没有可用的水印类型",
                code: "NO_AVAILABLE_TYPES"
            ))
        }

        // 检查日志系统
        let errorLogs = getErrorHistory(limit: 10)
        if errorLogs.count > 5 {
            warnings.append(ValidationWarning(
                field: "errorLogs",
                message: "最近有较多错误日志: \(errorLogs.count) 条",
                code: "HIGH_ERROR_COUNT"
            ))
        }

        let result = errors.isEmpty ? ValidationResult.success : ValidationResult.failure(errors: errors, warnings: warnings)
        logger.log(result.isValid ? .success : .error, "健康检查完成: \(result.isValid ? "通过" : "失败") - 错误: \(errors.count), 警告: \(warnings.count)")

        return result
    }

    // MARK: - 性能优化和缓存方法

    /// 获取缓存统计信息
    /// - Returns: 缓存统计信息
    func getCacheStats() -> CacheStats {
        logger.log(.debug, "获取缓存统计信息")
        return cache.getCacheStats()
    }

    /// 清除所有缓存
    func clearCache() {
        logger.log(.operation, "清除所有缓存")
        cache.clearAll()
    }

    /// 预加载常用水印类型
    func preloadCommonWatermarkTypes() {
        logger.log(.operation, "开始预加载常用水印类型")

        let commonTypes = ["watermark1", "watermark2", "watermark3", "watermark4", "watermark5"]
        let settings = getSettings()

        DispatchQueue.global(qos: .utility).async { [weak self] in
            guard let self = self else { return }

            var preloadedCount = 0

            for type in commonTypes {
                // 预加载类型信息
                if let typeInfo = self.getWatermarkTypeInfo(type) {
                    self.cache.setTypeInfo(typeInfo, for: type)
                    preloadedCount += 1
                }

                // 预加载样式预览
                if let preview = self.previewWatermarkStyle(type, settings: settings) {
                    let settingsHash = self.generateSettingsHash(settings)
                    self.cache.setStylePreview(preview, for: type, settingsHash: settingsHash)
                }
            }

            DispatchQueue.main.async {
                self.logger.log(.success, "预加载完成，加载了 \(preloadedCount) 个常用水印类型")
            }
        }
    }

    /// 执行性能优化
    /// - Returns: 优化结果
    func optimizePerformance() -> PerformanceOptimizationResult {
        logger.log(.operation, "开始执行性能优化")

        var optimizations: [String] = []
        var memoryFreed = 0

        // 1. 清理过期缓存
        let initialStats = cache.getCacheStats()
        cache.clearAll() // 简化实现，实际中可能只清理过期项
        let afterClearStats = cache.getCacheStats()

        if initialStats.totalSize > afterClearStats.totalSize {
            let freed = initialStats.totalSize - afterClearStats.totalSize
            memoryFreed += freed
            optimizations.append("清理过期缓存，释放 \(freed) 字节")
        }

        // 2. 预加载常用类型
        preloadCommonWatermarkTypes()
        optimizations.append("预加载常用水印类型")

        // 3. 验证服务健康状态
        let healthResult = performHealthCheck()
        if healthResult.isValid {
            optimizations.append("服务健康状态验证通过")
        } else {
            optimizations.append("发现并记录了 \(healthResult.errors.count) 个健康问题")
        }

        // 4. 内存优化建议
        let currentStats = cache.getCacheStats()
        if currentStats.totalSize > 10 * 1024 * 1024 { // 10MB
            optimizations.append("建议定期清理缓存以优化内存使用")
        }

        let performanceGain = optimizations.count > 0 ? Double(optimizations.count) * 10.0 : 0.0
        let result = PerformanceOptimizationResult.success(
            optimizations: optimizations,
            gain: performanceGain,
            memoryFreed: memoryFreed,
            hitRate: currentStats.hitRate
        )

        logger.log(.success, "性能优化完成: \(optimizations.count) 项优化，释放 \(memoryFreed) 字节内存")
        return result
    }
}

// MARK: - 扩展方法（为未来准备，暂时不使用）
extension WatermarkService {

    // ✅ 已移除重复的getAvailableWatermarkTypes方法，使用返回[WatermarkTypeInfo]的版本

    /// 应用水印到容器视图
    /// **注意：这个方法暂时不实现，保持现有的WatermarkManagerProvider逻辑**
    /// - Parameters:
    ///   - styleType: 水印样式类型
    ///   - container: 容器视图
    func applyWatermark(_ styleType: String, to container: UIView) {
        // 暂时不实现，保持现有逻辑不变
        print("🎨 [WatermarkService] applyWatermark() - 暂时不实现，保持现有逻辑")
    }

    /// 移除水印
    /// **注意：这个方法暂时不实现，保持现有的WatermarkManagerProvider逻辑**
    /// - Parameter container: 容器视图
    func removeWatermark(from container: UIView) {
        // 暂时不实现，保持现有逻辑不变
        print("🧹 [WatermarkService] removeWatermark() - 暂时不实现，保持现有逻辑")
    }


}