import Foundation
import SwiftData
import UIKit
import CoreImage
import ImageIO

/// 共享服务 - MVVM-S架构
/// 复制自：RAWDataManager.swift + SharedContainer.swift
class SharedService {

    // MARK: - 单例
    static let shared = SharedService()

    // MARK: - 共享容器管理 (从SharedContainer.swift完整复制)

    let container: ModelContainer

    // MARK: - 初始化
    private init() {
        // 初始化共享容器 (从SharedContainer.swift完整复制)
        let schema = Schema([
            AppSettings.self,
            AdjustSettings.self,
            FilterSettings.self,
            EffectsModel.self,
            PaperModel.self,
            CameraBasicSettings.self,
            WatermarkSettings.self,
            CropModel.self
        ])

        do {
            let config = ModelConfiguration(schema: schema, isStoredInMemoryOnly: false)
            container = try ModelContainer(for: schema, configurations: [config])
        } catch {
            fatalError("初始化ModelContainer失败: \(error)")
        }

        print("🎨 SharedService 初始化完成")
    }

    // MARK: - RAW数据管理 (从RAWDataManager.swift完整复制)

    // MARK: - RAW数据结构

    /// RAW图像数据结构
    struct RAWImageData {
        let originalImage: UIImage
        let ciImage: CIImage?
        let colorSpace: CGColorSpace?
        let metadata: [String: Any]?
        let isLinear: Bool
        let bitDepth: Int
        let hasRAWData: Bool

        init(image: UIImage) {
            self.originalImage = image
            self.ciImage = CIImage(image: image)
            self.colorSpace = image.cgImage?.colorSpace
            self.metadata = SharedService.shared.getImageMetadata(from: image)
            self.isLinear = SharedService.shared.detectLinearColorSpace(image)
            self.bitDepth = SharedService.shared.detectBitDepth(image)
            self.hasRAWData = SharedService.shared.detectRAWData(image)
        }
    }

    /// 处理管线类型
    enum ProcessingPipeline {
        case standard    // 标准sRGB处理管线
        case linear      // 线性空间处理管线 (RAW/高精度)
        case hybrid      // 混合处理管线

        var description: String {
            switch self {
            case .standard: return "标准sRGB处理"
            case .linear: return "线性空间处理"
            case .hybrid: return "混合处理"
            }
        }
    }

    // MARK: - RAW私有属性

    /// 当前图像数据
    private var currentImageData: RAWImageData?

    /// 当前处理管线
    private var currentPipeline: ProcessingPipeline = .standard

    /// 线性空间处理上下文
    private lazy var linearContext: CIContext = {
        let options: [CIContextOption: Any] = [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.linearSRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.linearSRGB)!,
            .useSoftwareRenderer: false
        ]
        return CIContext(options: options)
    }()

    /// sRGB处理上下文
    private lazy var srgbContext: CIContext = {
        let options: [CIContextOption: Any] = [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .useSoftwareRenderer: false
        ]
        return CIContext(options: options)
    }()

    // MARK: - RAW公共接口

    /// 设置图像数据并检测RAW特性
    /// - Parameter image: 输入图像
    /// - Returns: 是否成功设置RAW数据
    func setImageData(_ image: UIImage) -> Bool {
        print("🎨 [SharedService] 开始分析图像数据...")

        currentImageData = RAWImageData(image: image)

        guard let imageData = currentImageData else {
            print("❌ [SharedService] 图像数据创建失败")
            currentPipeline = .standard
            return false
        }

        // 根据图像特性选择处理管线
        if imageData.hasRAWData && imageData.isLinear {
            currentPipeline = .linear
            print("✅ [SharedService] 检测到RAW数据，使用线性处理管线")
        } else if imageData.isLinear {
            currentPipeline = .hybrid
            print("✅ [SharedService] 检测到线性色彩空间，使用混合处理管线")
        } else {
            currentPipeline = .standard
            print("✅ [SharedService] 使用标准sRGB处理管线")
        }

        print("🎨 [SharedService] 图像分析完成:")
        print("   - 尺寸: \(image.size)")
        print("   - 色彩空间: \(imageData.colorSpace?.name.map { String($0) } ?? "未知")")
        print("   - 位深度: \(imageData.bitDepth)位")
        print("   - 线性空间: \(imageData.isLinear ? "是" : "否")")
        print("   - RAW数据: \(imageData.hasRAWData ? "是" : "否")")
        print("   - 处理管线: \(currentPipeline.description)")

        return imageData.hasRAWData
    }

    /// 当前处理管线类型
    var processingPipeline: ProcessingPipeline {
        return currentPipeline
    }

    /// 获取原始图像数据
    var originalImageData: RAWImageData? {
        return currentImageData
    }

    /// 是否有RAW数据
    var hasRAWData: Bool {
        return currentImageData?.hasRAWData ?? false
    }

    /// 是否使用线性空间处理
    var isLinearProcessing: Bool {
        return currentPipeline == .linear || currentPipeline == .hybrid
    }

    /// 获取当前图像的色彩空间
    var currentColorSpace: CGColorSpace? {
        return currentImageData?.colorSpace
    }

    /// 获取当前图像的位深度
    var currentBitDepth: Int {
        return currentImageData?.bitDepth ?? 8
    }

    // MARK: - RAW处理接口

    /// 在线性空间中应用调整参数
    /// - Parameter parameters: 滤镜参数
    /// - Returns: 处理后的CIImage，如果失败返回nil
    func applyAdjustmentsInLinearSpace(_ parameters: FilterParameters) -> CIImage? {
        guard let imageData = currentImageData,
              let ciImage = imageData.ciImage else {
            print("❌ [SharedService] 没有可用的图像数据")
            return nil
        }

        print("🎨 [SharedService] 开始线性空间调整...")

        // 确保图像在线性空间中
        let linearImage: CIImage
        if imageData.isLinear {
            linearImage = ciImage
        } else {
            linearImage = convertSRGBToLinear(ciImage)
        }

        // 应用线性空间调整
        let adjustedImage = applyLinearSpaceAdjustments(to: linearImage, parameters: parameters)

        print("✅ [SharedService] 线性空间调整完成")
        return adjustedImage
    }

    /// 生成sRGB预览图像
    /// - Parameter parameters: 滤镜参数
    /// - Returns: 预览图像，如果失败返回nil
    func generateSRGBPreview(_ parameters: FilterParameters) -> UIImage? {
        guard let linearImage = applyAdjustmentsInLinearSpace(parameters) else {
            return nil
        }

        print("🎨 [SharedService] 生成sRGB预览...")

        // 转换到sRGB空间
        let srgbImage = convertLinearToSRGB(linearImage)

        // 渲染为UIImage
        guard let cgImage = srgbContext.createCGImage(srgbImage, from: srgbImage.extent) else {
            print("❌ [SharedService] sRGB预览渲染失败")
            return nil
        }

        let previewImage = UIImage(cgImage: cgImage)
        print("✅ [SharedService] sRGB预览生成完成")
        return previewImage
    }

    /// 生成最终输出图像
    /// - Parameters:
    ///   - parameters: 滤镜参数
    ///   - outputColorSpace: 输出色彩空间
    /// - Returns: 最终输出图像，如果失败返回nil
    func generateFinalOutput(_ parameters: FilterParameters, outputColorSpace: CGColorSpace? = nil) -> UIImage? {
        guard let linearImage = applyAdjustmentsInLinearSpace(parameters) else {
            return nil
        }

        print("🎨 [SharedService] 生成最终输出...")

        let targetColorSpace = outputColorSpace ?? CGColorSpace(name: CGColorSpace.sRGB)!

        // 根据目标色彩空间选择转换方式
        let outputImage: CIImage
        if targetColorSpace.name == CGColorSpace.linearSRGB {
            outputImage = linearImage
        } else {
            outputImage = convertLinearToSRGB(linearImage)
        }

        // 使用目标色彩空间渲染
        let outputContext = CIContext(options: [
            .workingColorSpace: targetColorSpace,
            .outputColorSpace: targetColorSpace,
            .useSoftwareRenderer: false
        ])

        guard let cgImage = outputContext.createCGImage(outputImage, from: outputImage.extent) else {
            print("❌ [SharedService] 最终输出渲染失败")
            return nil
        }

        let finalImage = UIImage(cgImage: cgImage)
        print("✅ [SharedService] 最终输出生成完成")
        return finalImage
    }

    // MARK: - RAW私有方法

    /// 在线性空间中应用调整参数
    /// - Parameters:
    ///   - image: 线性空间的CIImage
    ///   - parameters: 滤镜参数
    /// - Returns: 调整后的CIImage
    private func applyLinearSpaceAdjustments(to image: CIImage, parameters: FilterParameters) -> CIImage {
        var adjustedImage = image

        // 曝光调整 (线性空间中的乘法操作)
        if parameters.exposure != 0.0 {
            let exposureValue = pow(2.0, parameters.exposure)
            adjustedImage = adjustedImage.applyingFilter("CIExposureAdjust", parameters: [
                "inputEV": exposureValue
            ])
        }

        // 高光和阴影调整 (线性空间中更精确)
        if parameters.highlights != 0.0 || parameters.shadows != 0.0 {
            adjustedImage = adjustedImage.applyingFilter("CIHighlightShadowAdjust", parameters: [
                "inputHighlightAmount": 1.0 + parameters.highlights / 100.0,
                "inputShadowAmount": 1.0 + parameters.shadows / 100.0
            ])
        }

        // 色温调整 (线性空间中的色彩矩阵变换)
        if parameters.temperature != 0.0 || parameters.tint != 0.0 {
            adjustedImage = adjustedImage.applyingFilter("CITemperatureAndTint", parameters: [
                "inputNeutral": CIVector(x: 6500 + CGFloat(parameters.temperature * 100), y: CGFloat(parameters.tint)),
                "inputTargetNeutral": CIVector(x: 6500, y: 0)
            ])
        }

        return adjustedImage
    }

    /// 将线性空间图像转换为sRGB
    /// - Parameter image: 线性空间的CIImage
    /// - Returns: sRGB空间的CIImage
    private func convertLinearToSRGB(_ image: CIImage) -> CIImage {
        return image.applyingFilter("CILinearToSRGBToneCurve")
    }

    /// 将sRGB图像转换为线性空间
    /// - Parameter image: sRGB空间的CIImage
    /// - Returns: 线性空间的CIImage
    private func convertSRGBToLinear(_ image: CIImage) -> CIImage {
        return image.applyingFilter("CISRGBToneCurveToLinear")
    }

    /// 获取图像元数据
    /// - Parameter image: 输入图像
    /// - Returns: 元数据字典，如果失败返回nil
    private func getImageMetadata(from image: UIImage) -> [String: Any]? {
        guard let cgImage = image.cgImage,
              let dataProvider = cgImage.dataProvider,
              let data = dataProvider.data,
              let imageSource = CGImageSourceCreateWithData(data, nil) else {
            return nil
        }

        let metadata = CGImageSourceCopyPropertiesAtIndex(imageSource, 0, nil) as? [String: Any]
        return metadata
    }

    /// 检测线性色彩空间
    /// - Parameter image: 输入图像
    /// - Returns: 是否为线性色彩空间
    private func detectLinearColorSpace(_ image: UIImage) -> Bool {
        guard let colorSpace = image.cgImage?.colorSpace else {
            return false
        }

        // 检查色彩空间名称
        if let name = colorSpace.name {
            let linearSpaces: [CFString] = [
                CGColorSpace.linearSRGB,
                CGColorSpace.linearGray,
                CGColorSpace.extendedLinearSRGB
            ]
            return linearSpaces.contains(name)
        }

        // 检查伽马值
        if let gamma = colorSpace.copyICCData() {
            // 简化的伽马检测，实际实现可能更复杂
            return false // 默认假设非线性
        }

        return false
    }

    /// 检测位深度
    /// - Parameter image: 输入图像
    /// - Returns: 位深度
    private func detectBitDepth(_ image: UIImage) -> Int {
        guard let cgImage = image.cgImage else {
            return 8
        }

        return cgImage.bitsPerComponent
    }

    /// 检测RAW数据
    /// - Parameter image: 输入图像
    /// - Returns: 是否包含RAW数据
    private func detectRAWData(_ image: UIImage) -> Bool {
        guard let metadata = getImageMetadata(from: image) else {
            return false
        }

        // 检查EXIF数据中的RAW标识
        if let exif = metadata[kCGImagePropertyExifDictionary as String] as? [String: Any] {
            // 检查相机制造商和型号
            if let make = exif[kCGImagePropertyExifMakerNote as String] as? String {
                let rawMakers = ["Canon", "Nikon", "Sony", "Fujifilm", "Leica", "Panasonic"]
                if rawMakers.contains(where: { make.contains($0) }) {
                    return true
                }
            }
        }

        // 检查TIFF数据
        if let tiff = metadata[kCGImagePropertyTIFFDictionary as String] as? [String: Any] {
            if let software = tiff[kCGImagePropertyTIFFSoftware as String] as? String {
                let rawSoftware = ["Adobe Camera Raw", "Lightroom", "Capture One", "RawTherapee"]
                if rawSoftware.contains(where: { software.contains($0) }) {
                    return true
                }
            }
        }

        // 检查色彩空间和位深度组合
        let bitDepth = detectBitDepth(image)
        let isLinear = detectLinearColorSpace(image)

        // 高位深度 + 线性空间通常表示RAW处理过的图像
        return bitDepth > 8 && isLinear
    }

    // MARK: - RAW扩展支持

    /// 处理参数结构
    struct ProcessingParameters {
        let useHighPrecision: Bool
        let preserveColorSpace: Bool
        let outputBitDepth: Int
        let enableNoiseReduction: Bool

        static let `default` = ProcessingParameters(
            useHighPrecision: true,
            preserveColorSpace: true,
            outputBitDepth: 16,
            enableNoiseReduction: false
        )
    }

    /// 获取推荐的处理参数
    /// - Returns: 推荐的处理参数，如果没有RAW数据返回nil
    func getRecommendedProcessingParameters() -> ProcessingParameters? {
        guard let imageData = currentImageData, imageData.hasRAWData else {
            return nil
        }

        return ProcessingParameters(
            useHighPrecision: imageData.bitDepth > 8,
            preserveColorSpace: imageData.isLinear,
            outputBitDepth: max(imageData.bitDepth, 16),
            enableNoiseReduction: imageData.bitDepth >= 12
        )
    }

    /// 是否支持高精度处理
    var supportsHighPrecisionProcessing: Bool {
        guard let imageData = currentImageData else {
            return false
        }

        return imageData.hasRAWData && imageData.bitDepth > 8
    }

    /// 获取当前处理能力描述
    var processingCapabilities: String {
        guard let imageData = currentImageData else {
            return "无图像数据"
        }

        var capabilities: [String] = []

        if imageData.hasRAWData {
            capabilities.append("RAW处理")
        }

        if imageData.isLinear {
            capabilities.append("线性空间")
        }

        if imageData.bitDepth > 8 {
            capabilities.append("\(imageData.bitDepth)位精度")
        }

        if supportsHighPrecisionProcessing {
            capabilities.append("高精度处理")
        }

        return capabilities.isEmpty ? "标准处理" : capabilities.joined(separator: ", ")
    }

    /// 清除当前图像数据
    func clearImageData() {
        currentImageData = nil
        currentPipeline = .standard
        print("🗑️ [SharedService] 已清除图像数据")
    }
}
