// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 设置服务协议 - MVVM-S架构
/// 定义设置数据操作的标准接口，提高可测试性和模块解耦
protocol SettingsServiceProtocol {
    
    // MARK: - 基础数据操作
    
    /// 获取应用设置
    /// - Returns: 应用设置对象，如果不存在则创建新的
    func getSettings() -> AppSettings
    
    /// 保存设置
    /// - Parameter settings: 要保存的设置对象
    func saveSettings(_ settings: AppSettings)
    
    /// 更新特定设置
    /// - Parameters:
    ///   - keyPath: 设置项的属性路径
    ///   - value: 新的值
    func updateSetting<T>(_ keyPath: WritableKeyPath<AppSettings, T>, value: T)
    
    /// 重置所有设置为默认值
    func resetToDefaults()
    
    // MARK: - 扩展功能
    
    /// 验证设置值的有效性
    /// - Parameter settings: 要验证的设置对象
    /// - Returns: 验证结果，true表示有效
    func validateSettings(_ settings: AppSettings) -> Bool
    
    /// 导出设置到字典
    /// - Returns: 设置的字典表示
    func exportSettings() -> [String: Any]
    
    /// 从字典导入设置
    /// - Parameter dictionary: 包含设置数据的字典
    /// - Returns: 导入是否成功
    func importSettings(from dictionary: [String: Any]) -> Bool
    
    /// 获取设置的最后更新时间
    /// - Returns: 最后更新时间
    func getLastUpdateTime() -> Date?
}

// MARK: - 设置验证规则

/// 设置验证规则枚举
enum SettingsValidationRule {
    case copyrightSignatureLength(min: Int, max: Int) // 版权署名长度限制
    case photoRatioFormat // 照片比例格式验证
    case videoRatioFormat // 视频比例格式验证
    case resolutionValue // 分辨率值验证
    case bitrateRange(min: Int, max: Int) // 码率范围验证
    
    /// 验证设置值
    /// - Parameter value: 要验证的值
    /// - Returns: 验证结果
    func validate(_ value: Any) -> Bool {
        switch self {
        case .copyrightSignatureLength(let min, let max):
            guard let string = value as? String else { return false }
            return string.count >= min && string.count <= max
            
        case .photoRatioFormat:
            guard let ratio = value as? String else { return false }
            let validRatios = ["1:1", "4:3", "3:2", "16:9", "自定义"]
            return validRatios.contains(ratio)
            
        case .videoRatioFormat:
            guard let ratio = value as? String else { return false }
            let validRatios = ["16:9", "4:3", "1:1", "自定义"]
            return validRatios.contains(ratio)
            
        case .resolutionValue:
            guard let resolution = value as? String else { return false }
            let validResolutions = ["12MP", "24MP", "48MP"]
            return validResolutions.contains(resolution)
            
        case .bitrateRange(let min, let max):
            guard let bitrateString = value as? String else { return false }
            // 从字符串中提取数值（如"中（50 Mbps）"中的50）
            let numbers = bitrateString.components(separatedBy: CharacterSet.decimalDigits.inverted)
                .compactMap { Int($0) }
            guard let bitrate = numbers.first else { return false }
            return bitrate >= min && bitrate <= max
        }
    }
}

// MARK: - 设置错误类型

/// 设置操作错误类型
enum SettingsError: LocalizedError {
    case loadFailed(String) // 加载失败
    case saveFailed(String) // 保存失败
    case validationFailed(String) // 验证失败
    case importFailed(String) // 导入失败
    case exportFailed(String) // 导出失败
    case contextUnavailable // 数据上下文不可用
    
    var errorDescription: String? {
        switch self {
        case .loadFailed(let message):
            return "加载设置失败: \(message)" // 中文友好错误信息
        case .saveFailed(let message):
            return "保存设置失败: \(message)"
        case .validationFailed(let message):
            return "设置验证失败: \(message)"
        case .importFailed(let message):
            return "导入设置失败: \(message)"
        case .exportFailed(let message):
            return "导出设置失败: \(message)"
        case .contextUnavailable:
            return "数据上下文不可用，请重试"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .loadFailed:
            return "请检查存储权限或重启应用"
        case .saveFailed:
            return "请检查存储空间或重试保存"
        case .validationFailed:
            return "请检查输入值是否符合要求"
        case .importFailed:
            return "请检查导入文件格式是否正确"
        case .exportFailed:
            return "请检查存储权限或重试导出"
        case .contextUnavailable:
            return "请重启应用或联系技术支持"
        }
    }
}

// MARK: - 设置常量配置

/// 设置模块配置常量
struct SettingsConfig {
    // 版权署名配置
    static let copyrightSignatureMinLength = 0 // 最小长度
    static let copyrightSignatureMaxLength = 100 // 最大长度
    
    // 比例设置配置
    static let defaultPhotoRatio = "4:3" // 默认照片比例
    static let defaultVideoRatio = "16:9" // 默认视频比例
    
    // 分辨率配置
    static let defaultProRAWResolution = "48MP" // 默认ProRAW分辨率
    static let defaultHEICResolution = "12MP" // 默认HEIC分辨率
    
    // 码率配置
    static let minBitrate = 1 // 最小码率 (Mbps)
    static let maxBitrate = 200 // 最大码率 (Mbps)
    static let defaultVideoBitrate = "自动" // 默认视频码率
    
    // 音频配置
    static let defaultAudioSource = "自动" // 默认音频来源
    
    // 峰值对焦配置
    static let defaultFocusPeakingColor = "黄色" // 默认峰值对焦颜色
    static let availableFocusPeakingColors = ["黄色", "白色", "红色"] // 可用颜色
    
    // 快门声配置
    static let defaultShutterSound = "快门声1" // 默认快门声
    static let availableShutterSounds = ["静音", "快门声1", "快门声2", "快门声3"] // 可用快门声
    
    // 语言配置
    static let defaultLanguage = "跟随系统" // 默认语言
    static let availableLanguages = ["跟随系统", "简体中文", "繁體中文", "English"] // 可用语言
    
    // 设备朝向配置
    static let defaultDeviceOrientation = "自动旋转" // 默认设备朝向
    static let availableOrientations = ["自动旋转", "竖屏锁定", "横屏锁定"] // 可用朝向
}
