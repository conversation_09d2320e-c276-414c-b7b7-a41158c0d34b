import Foundation
import SwiftData
import SwiftUI

/// 设置服务，负责管理应用设置的持久化
class SettingsService {
    // MARK: - 属性
    
    /// 模型容器
    private var modelContainer: ModelContainer?
    
    /// 模型上下文
    private var modelContext: ModelContext?
    
    // MARK: - 初始化方法
    
    init() {
        setupModelContainer()
    }
    
    // MARK: - 设置方法
    
    /// 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print(" SettingsService: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print(" SettingsService: 获取共享 ModelContainer 失败！")
        }
    }
    
    // MARK: - 公共方法
    
    /// 获取应用设置
    /// - Returns: 应用设置对象，如果不存在则创建新的
    func getSettings() -> AppSettings {
        guard let context = modelContext else {
            return AppSettings()
        }
        
        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<AppSettings>(predicate: #Predicate { $0.id == "app_settings" })
            let existingSettings = try context.fetch(descriptor)
            
            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }
            
            // 如果不存在，创建新的设置并保存
            let newSettings = AppSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return AppSettings()
        }
    }
    
    /// 保存设置
    /// - Parameter settings: 要保存的设置对象
    func saveSettings(_ settings: AppSettings) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }
        
        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存设置失败: \(error.localizedDescription)")
        }
    }
    
    /// 更新特定设置
    /// - Parameters:
    ///   - keyPath: 设置项的属性路径
    ///   - value: 新的值
    func updateSetting<T>(_ keyPath: WritableKeyPath<AppSettings, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }
    
    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }
        
        do {
            // 获取所有现有设置
            let descriptor = FetchDescriptor<AppSettings>()
            let existingSettings = try context.fetch(descriptor)
            
            // 删除所有现有设置
            for settings in existingSettings {
                context.delete(settings)
            }
            
            // 创建新的默认设置
            let newSettings = AppSettings()
            context.insert(newSettings)
            
            // 保存更改
            try context.save()
        } catch {
            print("重置设置失败: \(error.localizedDescription)")
        }
    }
}