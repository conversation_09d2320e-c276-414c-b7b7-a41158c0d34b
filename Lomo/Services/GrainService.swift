// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 颗粒效果服务实现 - MVVM-S架构
/// 基于Metal的高性能颗粒效果处理服务，支持依赖注入和并发安全
actor GrainService: GrainServiceProtocol {
    
    // MARK: - 属性
    private let metalEngine: MetalSpecialEffectsEngine?
    private var availablePresets: [GrainPreset] = []
    
    // MARK: - 初始化
    init(metalEngine: MetalSpecialEffectsEngine?) {
        self.metalEngine = metalEngine
        loadPresets()
        print("🎨 [GrainService] 初始化完成 - 使用Metal实现")
    }
    
    // MARK: - 协议实现
    
    func getAllGrainPresets() async -> [GrainPreset] {
        return availablePresets
    }
    
    func applyGrain(to image: UIImage, with parameters: GrainParameters) async -> UIImage {
        // 如果颗粒效果未启用，直接返回原图
        guard parameters.isEnabled else {
            print("ℹ️ [GrainService] 颗粒效果未启用，返回原图")
            return image
        }
        
        // 使用Metal引擎处理
        guard let metalEngine = self.metalEngine else {
            print("❌ [GrainService] Metal引擎不可用")
            return image
        }
        
        do {
            let result = try metalEngine.applyGrain(to: image, parameters: parameters)
            print("✅ [GrainService] 颗粒效果应用成功")
            return result
        } catch {
            print("❌ [GrainService] Metal颗粒处理失败: \(error)")
            return image
        }
    }
    
    func validateParameters(_ parameters: GrainParameters) async -> Bool {
        // 检查强度范围
        guard parameters.intensity >= 0.0 && parameters.intensity <= 1.0 else {
            print("❌ [GrainService] 强度参数超出范围: \(parameters.intensity)")
            return false
        }
        
        // 检查预设有效性
        if let preset = parameters.selectedPreset {
            let validPresets = await getAllGrainPresets()
            guard validPresets.contains(preset) else {
                print("❌ [GrainService] 无效的颗粒预设: \(preset.id)")
                return false
            }
        }
        
        return true
    }
    
    func getPresetInfo(_ preset: GrainPreset) async -> (intensity: Double, density: Double) {
        return (intensity: preset.contrast, density: preset.density)
    }
    
    // MARK: - 私有方法
    
    /// 加载所有可用的颗粒预设
    private func loadPresets() {
        availablePresets = [
            GrainPreset(id: "fine", name: "细腻", 
                size: 0.3, density: 0.8, contrast: 0.4),
            GrainPreset(id: "standard", name: "标准", 
                size: 0.5, density: 0.6, contrast: 0.5),
            GrainPreset(id: "rough", name: "粗糙", 
                size: 0.8, density: 0.4, contrast: 0.7),
            GrainPreset(id: "film", name: "胶片", 
                size: 0.6, density: 0.7, contrast: 0.6),
            GrainPreset(id: "cinema", name: "电影", 
                size: 0.7, density: 0.5, contrast: 0.65),
            GrainPreset(id: "polaroid", name: "宝丽来", 
                size: 0.65, density: 0.55, contrast: 0.75)
        ]
        print("🎨 [GrainService] 加载了 \(availablePresets.count) 个颗粒预设")
    }
}