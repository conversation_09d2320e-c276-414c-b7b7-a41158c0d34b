// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 水印配置协议 - 用于Gallery模块访问水印配置信息
/// 通过协议抽象消除对WatermarkService的直接依赖
protocol WatermarkConfigurationProtocol {
    /// 获取当前激活的水印类型
    /// - Returns: 水印类型字符串 (如: "custom23", "custom24", "normal")
    func getActiveWatermarkType() -> String
    
    /// 获取指定水印类型所需的照片数量
    /// - Parameter watermarkType: 水印类型
    /// - Returns: 所需的照片数量
    func getRequiredPhotoCount(for watermarkType: String) -> Int
    
    /// 检查当前是否为拼图水印模式
    /// - Returns: 是否为拼图水印模式
    func isPuzzleWatermarkActive() -> Bool
}

/// 水印配置适配器 - 将WatermarkService适配为协议接口
/// 消除Gallery模块对Edit模块的直接依赖
class WatermarkConfigurationAdapter: WatermarkConfigurationProtocol {
    private let watermarkService: WatermarkService
    
    init(watermarkService: WatermarkService = WatermarkService()) {
        self.watermarkService = watermarkService
    }
    
    func getActiveWatermarkType() -> String {
        let settings = watermarkService.getSettings()
        return settings.activeWatermarkStyleType
    }
    
    func getRequiredPhotoCount(for watermarkType: String) -> Int {
        switch watermarkType {
        case "custom23":
            return 4 // 拼图水印23需要4张照片
        case "custom24":
            return 5 // 拼图水印24需要5张照片
        default:
            return 1 // 普通水印只需要1张照片
        }
    }
    
    func isPuzzleWatermarkActive() -> Bool {
        let watermarkType = getActiveWatermarkType()
        return watermarkType == "custom23" || watermarkType == "custom24"
    }
}