import UIKit

/// 一个简单的单例服务，用于在应用的不同部分之间共享为拼图水印选择的图像。
/// 这避免了在视图模型和水印样式对象之间创建强耦合。
class PuzzleImageProvider {
    /// 共享的单例实例
    static let shared = PuzzleImageProvider()

    /// 存储为拼图水印选择的图像数组
    var images: [UIImage] = []

    /// 私有化初始化方法以确保单例模式
    private init() {}

    /// 清除存储的图像
    func clear() {
        images.removeAll()
        print("🧩 PuzzleImageProvider: Cleared all images.")
    }
} 