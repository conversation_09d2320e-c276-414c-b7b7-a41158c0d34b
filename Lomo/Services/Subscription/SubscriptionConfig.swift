// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI

/// 订阅模块配置中心 - 统一管理所有常量
/// 遵循精简主义原则，避免重复定义
struct SubscriptionConfig {
    
    // MARK: - 订阅计划配置（原样搬运，不改变任何值）
    static let monthlyPlan = "monthly" // 月度计划
    static let yearlyPlan = "yearly" // 年度计划
    static let lifetimePlan = "lifetime" // 终身计划
    
    // MARK: - 轮播配置（原样搬运现有数值）
    static let autoScrollInterval: TimeInterval = 3.0 // 自动轮播间隔
    static let restartTimerInterval: TimeInterval = 3.0 // 重启计时器间隔
    static let bufferCount = 3 // 轮播缓冲数量
    
    // MARK: - 存储配置（原样搬运现有键值）
    static let isProUserKey = "isProUser" // Pro用户状态键
    static let proUserStatusNotification = "ProUserStatusChanged" // Pro状态通知
    
    // MARK: - 功能卡片配置（原样搬运现有数据）
    static let featureCards: [FeatureCardModel] = [
        FeatureCardModel(id: 1, title: "滤镜或胶片模拟", description: "专业滤镜库，胶片复刻", backgroundColor: Color.black),
        FeatureCardModel(id: 2, title: "批处理图片", description: "一键处理多张照片", backgroundColor: Color.black),
        FeatureCardModel(id: 3, title: "摄影师签名", description: "添加专业签名和水印", backgroundColor: Color.black),
        FeatureCardModel(id: 4, title: "RAW格式支持", description: "支持编辑和处理RAW格式文件", backgroundColor: Color.black),
        FeatureCardModel(id: 5, title: "高级曲线调整", description: "精确控制色调和颜色曲线", backgroundColor: Color.black),
        FeatureCardModel(id: 6, title: "定制预设", description: "创建和保存您自己的编辑预设", backgroundColor: Color.black),
        FeatureCardModel(id: 7, title: "批量导出", description: "多种格式批量导出，高效处理", backgroundColor: Color.black),
        FeatureCardModel(id: 8, title: "无水印输出", description: "移除所有作品水印，纯净输出", backgroundColor: Color.black),
    ]
}
