// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 划痕效果服务实现 - MVVM-S架构
/// 基于Metal的高性能划痕效果处理服务，支持依赖注入和并发安全
actor ScratchService: ScratchServiceProtocol {
    
    // MARK: - 属性
    private let metalEngine: MetalSpecialEffectsEngine?
    private var availablePresets: [ScratchPreset] = []
    
    // MARK: - 初始化
    init(metalEngine: MetalSpecialEffectsEngine?) {
        self.metalEngine = metalEngine
        loadPresets()
        print("🎨 [ScratchService] 初始化完成 - 使用Metal实现")
    }
    
    // MARK: - 协议实现
    
    func getAllScratchPresets() async -> [ScratchPreset] {
        return availablePresets
    }
    
    func applyScratch(to image: UIImage, with parameters: ScratchParameters) async -> UIImage {
        // 如果划痕效果未启用，直接返回原图
        guard parameters.isEnabled else {
            print("ℹ️ [ScratchService] 划痕效果未启用，返回原图")
            return image
        }
        
        // 使用Metal引擎处理
        guard let metalEngine = self.metalEngine else {
            print("❌ [ScratchService] Metal引擎不可用")
            return image
        }
        
        do {
            let result = try metalEngine.applyScratch(to: image, parameters: parameters)
            print("✅ [ScratchService] 划痕效果应用成功")
            return result
        } catch {
            print("❌ [ScratchService] Metal划痕处理失败: \(error)")
            return image
        }
    }
    
    func validateParameters(_ parameters: ScratchParameters) async -> Bool {
        // 检查强度范围
        guard parameters.intensity >= 0.0 && parameters.intensity <= 1.0 else {
            print("❌ [ScratchService] 强度参数超出范围: \(parameters.intensity)")
            return false
        }
        
        // 检查预设有效性
        if let preset = parameters.selectedPreset {
            let validPresets = await getAllScratchPresets()
            guard validPresets.contains(preset) else {
                print("❌ [ScratchService] 无效的划痕预设: \(preset.id)")
                return false
            }
        }
        
        return true
    }
    
    func getPresetDetails(_ preset: ScratchPreset) async -> (density: Double, length: Double, width: Double) {
        return (density: preset.density, length: preset.length, width: preset.width)
    }
    
    // MARK: - 私有方法
    
    /// 加载所有可用的划痕预设
    private func loadPresets() {
        availablePresets = ScratchPreset.allPresets
        print("🎨 [ScratchService] 加载了 \(availablePresets.count) 个划痕预设")
    }
}