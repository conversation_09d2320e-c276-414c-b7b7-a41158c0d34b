// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import UIKit

/// 漏光效果服务实现 - MVVM-S架构
/// 基于Metal的高性能漏光效果处理服务，支持依赖注入和并发安全
actor LightLeakService: LightLeakServiceProtocol {
    
    // MARK: - 属性
    private let metalEngine: MetalSpecialEffectsEngine?
    private var availablePresets: [LightLeakPreset] = []
    private var presetCache: [String: UIImage] = [:]
    
    // MARK: - 初始化
    init(metalEngine: MetalSpecialEffectsEngine?) {
        self.metalEngine = metalEngine
        loadPresets()
        print("🎨 [LightLeakService] 初始化完成 - 使用Metal实现")
    }
    
    // MARK: - 协议实现
    
    func getAllLightLeakPresets() async -> [LightLeakPreset] {
        return availablePresets
    }
    
    func applyLightLeak(to image: UIImage, with parameters: LightLeakParameters) async -> UIImage {
        // 如果漏光效果未启用，直接返回原图
        guard parameters.isEnabled else { 
            print("ℹ️ [LightLeakService] 漏光效果未启用，返回原图")
            return image 
        }
        
        // 如果没有选择预设，直接返回原图
        guard let preset = parameters.selectedPreset else {
            print("ℹ️ [LightLeakService] 未选择漏光预设，返回原图")
            return image
        }
        
        // 获取漏光纹理图像
        guard let leakImage = await getLeakImage(for: preset.id) else {
            print("⚠️ [LightLeakService] 漏光预设图像不存在: \(preset.id)")
            return image
        }
        
        // 使用Metal引擎处理
        guard let metalEngine = self.metalEngine else {
            print("❌ [LightLeakService] Metal引擎不可用")
            return image
        }
        
        do {
            let result = try metalEngine.applyLightLeak(to: image, leakImage: leakImage, parameters: parameters)
            print("✅ [LightLeakService] 漏光效果应用成功")
            return result
        } catch {
            print("❌ [LightLeakService] Metal漏光处理失败: \(error)")
            return image
        }
    }
    
    func validateParameters(_ parameters: LightLeakParameters) async -> Bool {
        // 检查强度范围
        guard parameters.intensity >= 0.0 && parameters.intensity <= 1.0 else {
            print("❌ [LightLeakService] 强度参数超出范围: \(parameters.intensity)")
            return false
        }
        
        // 检查预设有效性
        if let preset = parameters.selectedPreset {
            let validPresets = await getAllLightLeakPresets()
            guard validPresets.contains(preset) else {
                print("❌ [LightLeakService] 无效的漏光预设: \(preset.id)")
                return false
            }
        }
        
        return true
    }
    
    func getPresetPreviewImage(_ preset: LightLeakPreset) async -> UIImage? {
        return await getLeakImage(for: preset.id)
    }
    
    // MARK: - 私有方法
    
    /// 加载所有可用的漏光预设
    private func loadPresets() {
        availablePresets = LightLeakPreset.allPresets
        print("🎨 [LightLeakService] 加载了 \(availablePresets.count) 个漏光预设")
    }
    
    /// 获取预设漏光图像
    /// - Parameter presetId: 预设ID
    /// - Returns: 漏光图像
    private func getLeakImage(for presetId: String) async -> UIImage? {
        // 检查缓存
        if let cachedImage = presetCache[presetId] {
            return cachedImage
        }
        
        // 实际开发中，从Assets或文件中加载图像
        // let image = UIImage(named: presetId)
        
        // 模拟图像创建（临时代码，实际开发应替换）
        let tempImage = createPlaceholderLeakImage(for: presetId)
        
        // 存入缓存
        if let tempImage = tempImage {
            presetCache[presetId] = tempImage
        }
        
        return tempImage
    }
    
    /// 创建临时占位漏光图像（仅用于开发）
    private func createPlaceholderLeakImage(for presetId: String) -> UIImage? {
        // 创建不同颜色的示例漏光图像
        let size = CGSize(width: 1024, height: 1024)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 根据ID选择不同颜色
            let color: UIColor
            
            // 根据预设ID选择颜色
            switch presetId {
            case "light_leak_1":
                color = UIColor(red: 1.0, green: 0.8, blue: 0.4, alpha: 0.7) // 暖黄色
            case "light_leak_2":
                color = UIColor(red: 0.4, green: 0.6, blue: 1.0, alpha: 0.7) // 蓝色
            case "light_leak_3":
                color = UIColor(red: 1.0, green: 0.4, blue: 0.4, alpha: 0.7) // 红色
            case "light_leak_4":
                color = UIColor(red: 0.7, green: 0.4, blue: 1.0, alpha: 0.7) // 紫色
            case "light_leak_5":
                color = UIColor(red: 0.4, green: 0.9, blue: 0.6, alpha: 0.7) // 绿色
            default:
                color = UIColor(red: 1.0, green: 1.0, blue: 0.8, alpha: 0.7) // 默认
            }
            
            // 绘制渐变
            let gradient = CGGradient(
                colorsSpace: CGColorSpaceCreateDeviceRGB(),
                colors: [color.cgColor, UIColor.clear.cgColor] as CFArray,
                locations: [0.0, 1.0]
            )!
            
            // 渐变起点和终点
            let center = CGPoint(x: size.width / 2, y: size.height / 2)
            context.cgContext.drawRadialGradient(
                gradient,
                startCenter: center, 
                startRadius: 0,
                endCenter: center, 
                endRadius: size.width / 1.5,
                options: [.drawsBeforeStartLocation, .drawsAfterEndLocation]
            )
        }
    }
}