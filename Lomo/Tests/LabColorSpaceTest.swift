import Foundation
import Metal

/// Lab色彩空间转换测试
class LabColorSpaceTest {
    
    /// 测试Lab转换的往返精度
    static func testLabRoundTrip() {
        print("🧪 [LabColorSpaceTest] 开始Lab往返精度测试...")
        
        // 测试颜色样本
        let testColors: [(String, (Float, Float, Float))] = [
            ("纯白", (1.0, 1.0, 1.0)),
            ("纯黑", (0.0, 0.0, 0.0)),
            ("中灰", (0.5, 0.5, 0.5)),
            ("纯红", (1.0, 0.0, 0.0)),
            ("纯绿", (0.0, 1.0, 0.0)),
            ("纯蓝", (0.0, 0.0, 1.0)),
            ("黄色", (1.0, 1.0, 0.0)),
            ("青色", (0.0, 1.0, 1.0)),
            ("品红", (1.0, 0.0, 1.0))
        ]
        
        for (name, rgb) in testColors {
            let originalRGB = (rgb.0, rgb.1, rgb.2)
            
            // 模拟Metal着色器中的转换过程
            let xyz = rgbToXYZ(rgb)
            let lab = xyzToLab(xyz)
            let xyzBack = labToXYZ(lab)
            let rgbBack = xyzToRGB(xyzBack)
            
            // 计算误差
            let errorR = abs(originalRGB.0 - rgbBack.0)
            let errorG = abs(originalRGB.1 - rgbBack.1)
            let errorB = abs(originalRGB.2 - rgbBack.2)
            let maxError = max(errorR, max(errorG, errorB))
            
            print("🎨 \(name): RGB(\(String(format: "%.3f", originalRGB.0)), \(String(format: "%.3f", originalRGB.1)), \(String(format: "%.3f", originalRGB.2))) -> Lab(\(String(format: "%.1f", lab.0)), \(String(format: "%.1f", lab.1)), \(String(format: "%.1f", lab.2))) -> RGB(\(String(format: "%.3f", rgbBack.0)), \(String(format: "%.3f", rgbBack.1)), \(String(format: "%.3f", rgbBack.2)))")
            print("   误差: R=\(String(format: "%.6f", errorR)), G=\(String(format: "%.6f", errorG)), B=\(String(format: "%.6f", errorB)), 最大=\(String(format: "%.6f", maxError))")
            
            if maxError > 0.01 {
                print("⚠️ 误差过大！")
            } else if maxError > 0.001 {
                print("⚠️ 误差较大")
            } else {
                print("✅ 精度良好")
            }
            print("")
        }
    }
    
    /// 测试色温色调调整是否保持亮度
    static func testTemperatureTintLuminancePreservation() {
        print("🧪 [LabColorSpaceTest] 开始亮度保持测试...")
        
        let testColors: [(String, (Float, Float, Float))] = [
            ("中灰", (0.5, 0.5, 0.5)),
            ("暖灰", (0.6, 0.5, 0.4)),
            ("冷灰", (0.4, 0.5, 0.6)),
            ("肤色", (0.8, 0.6, 0.5))
        ]
        
        let temperatureValues: [Float] = [-0.5, -0.2, 0.0, 0.2, 0.5]
        let tintValues: [Float] = [-0.3, -0.1, 0.0, 0.1, 0.3]
        
        for (name, rgb) in testColors {
            let originalXYZ = rgbToXYZ(rgb)
            let originalLab = xyzToLab(originalXYZ)
            let originalL = originalLab.0
            
            print("🎨 测试颜色: \(name) - 原始亮度L*=\(String(format: "%.2f", originalL))")
            
            for temp in temperatureValues {
                for tint in tintValues {
                    let adjustedRGB = applyLabTemperatureTint(rgb, temperature: temp, tint: tint)
                    let adjustedXYZ = rgbToXYZ(adjustedRGB)
                    let adjustedLab = xyzToLab(adjustedXYZ)
                    let adjustedL = adjustedLab.0
                    
                    let luminanceError = abs(originalL - adjustedL)
                    
                    if luminanceError > 1.0 {
                        print("❌ 温度=\(temp), 色调=\(tint): L*变化=\(String(format: "%.2f", luminanceError)) (过大!)")
                    } else if luminanceError > 0.1 {
                        print("⚠️ 温度=\(temp), 色调=\(tint): L*变化=\(String(format: "%.2f", luminanceError))")
                    }
                }
            }
            print("")
        }
    }
    
    // MARK: - 辅助函数 (模拟Metal着色器逻辑)
    
    static func rgbToXYZ(_ rgb: (Float, Float, Float)) -> (Float, Float, Float) {
        // sRGB到XYZ转换矩阵
        let r = rgb.0, g = rgb.1, b = rgb.2
        let x = 0.4124564 * r + 0.3575761 * g + 0.1804375 * b
        let y = 0.2126729 * r + 0.7151522 * g + 0.0721750 * b
        let z = 0.0193339 * r + 0.1191920 * g + 0.9503041 * b
        return (x, y, z)
    }
    
    static func xyzToRGB(_ xyz: (Float, Float, Float)) -> (Float, Float, Float) {
        // XYZ到sRGB转换矩阵
        let x = xyz.0, y = xyz.1, z = xyz.2
        let r = 3.2404542 * x - 1.5371385 * y - 0.4985314 * z
        let g = -0.9692660 * x + 1.8760108 * y + 0.0415560 * z
        let b = 0.0556434 * x - 0.2040259 * y + 1.0572252 * z
        return (max(0, min(1, r)), max(0, min(1, g)), max(0, min(1, b)))
    }
    
    static func labF(_ t: Float) -> Float {
        return (t > 0.008856) ? pow(t, 1.0/3.0) : (7.787 * t + 16.0/116.0)
    }
    
    static func labFInv(_ t: Float) -> Float {
        let t3 = t * t * t
        return (t3 > 0.008856) ? t3 : (t - 16.0/116.0) / 7.787
    }
    
    static func xyzToLab(_ xyz: (Float, Float, Float)) -> (Float, Float, Float) {
        // D65白点
        let whitePoint = (Float(0.95047), Float(1.00000), Float(1.08883))
        
        // 归一化到白点
        let x = xyz.0 / whitePoint.0
        let y = xyz.1 / whitePoint.1
        let z = xyz.2 / whitePoint.2
        
        let fx = labF(x)
        let fy = labF(y)
        let fz = labF(z)
        
        let L = 116.0 * fy - 16.0
        let a = 500.0 * (fx - fy)
        let b = 200.0 * (fy - fz)
        
        return (L, a, b)
    }
    
    static func labToXYZ(_ lab: (Float, Float, Float)) -> (Float, Float, Float) {
        // D65白点
        let whitePoint = (Float(0.95047), Float(1.00000), Float(1.08883))
        
        let fy = (lab.0 + 16.0) / 116.0
        let fx = lab.1 / 500.0 + fy
        let fz = fy - lab.2 / 200.0
        
        let x = labFInv(fx) * whitePoint.0
        let y = labFInv(fy) * whitePoint.1
        let z = labFInv(fz) * whitePoint.2
        
        return (x, y, z)
    }
    
    static func applyLabTemperatureTint(_ rgb: (Float, Float, Float), temperature: Float, tint: Float) -> (Float, Float, Float) {
        // 1. RGB → XYZ → Lab
        let xyz = rgbToXYZ(rgb)
        var lab = xyzToLab(xyz)
        
        // 2. 保存原始亮度
        let originalL = lab.0
        
        // 3. 计算色相角度和色度
        let angle = atan2(lab.2, lab.1)
        var chroma = sqrt(lab.1 * lab.1 + lab.2 * lab.2)
        
        // 4. 色温调整
        let newAngle = angle + temperature * 0.1
        
        // 5. 色调调整
        if abs(angle) < Float.pi / 4 {
            chroma += tint * 0.2
            chroma = max(0.0, chroma)
        }
        
        // 6. 重建a*b*值，保持L*不变
        lab.1 = chroma * cos(newAngle)
        lab.2 = chroma * sin(newAngle)
        lab.0 = originalL  // 关键：保持亮度不变
        
        // 7. Lab → XYZ → RGB
        let newXYZ = labToXYZ(lab)
        return xyzToRGB(newXYZ)
    }
}
