import SwiftUI
import Photos
import PhotosUI

// 水印预览容器提供者，避免在视图更新中修改状态
class WatermarkPreviewProvider: ObservableObject {
    @Published var previewContainer: UIView?
    
    func setPreviewContainer(_ view: UIView) {
        DispatchQueue.main.async {
            self.previewContainer = view
        }
    }
}

// 创建一个SwiftUI视图来包装MockCameraPreviewService
struct MockPreviewView: UIViewRepresentable {
    let frame: CGRect
    var selectedImage: UIImage? = nil
    
    // 添加预览视图提供者
    var previewProvider: WatermarkPreviewProvider?

    // 用于持有 imageHostView 的引用，以便在 updateUIView 中正确设置给 previewProvider
    private let imageHostViewTag = 123 // Tag to identify the imageHostView

    func makeUIView(context: Context) -> UIView {
        let view = UIView(frame: frame)
        view.clipsToBounds = true // 添加裁剪约束
        view.backgroundColor = .black // 设置预览区域的背景色为黑色
        
        // 获取默认动画参数（与updateUIView中保持一致）
        let animationDuration = AnimationConstants.duration
        let animationCurve = UIView.AnimationOptions.curveEaseInOut.rawValue
        
        if let image = selectedImage {
            // 使用ImageRenderingService显示图片，并传递动画参数
            ImageRenderingService.shared.displayImage(image, in: view, animationDuration: animationDuration, animationCurve: animationCurve, previewProvider: previewProvider)
        } else {
            // 否则显示模拟相机预览
            let mockService = MockCameraPreviewService()
            mockService.configurePreviewLayer(for: view, customFrame: frame) // 方法不返回值
            if let provider = previewProvider {
                provider.setPreviewContainer(view) // 将 view 本身作为容器
            }
        }
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // 从上下文的事务中获取动画信息
        let transaction = context.transaction
        var animationDuration: TimeInterval = AnimationUtils.sharedAnimationParameters["duration"] as? TimeInterval ?? 0.25
        if transaction.animation != nil {
            animationDuration = 0.35
        }
        let animationCurve = AnimationUtils.sharedAnimationParameters["curve"] as? UInt ?? UIView.AnimationOptions.curveEaseInOut.rawValue
        
        UIView.animate(withDuration: animationDuration, delay: 0, options: UIView.AnimationOptions(rawValue: animationCurve)) {
            uiView.frame = self.frame
        }
        uiView.clipsToBounds = true
        
        // 清除之前的 imageHostView (如果存在) 或相机预览层子视图
        uiView.subviews.forEach { $0.removeFromSuperview() }
        
        if let image = selectedImage {
            // 使用ImageRenderingService显示图片，传递动画参数
            ImageRenderingService.shared.displayImage(image, in: uiView, animationDuration: animationDuration, animationCurve: animationCurve, previewProvider: previewProvider)
        } else {
            // 否则显示模拟相机预览
            let mockService = MockCameraPreviewService()
            mockService.updatePreviewLayer(for: uiView, customFrame: frame, animationDuration: animationDuration, animationCurve: animationCurve) // 方法不返回值
            if let provider = previewProvider {
                provider.setPreviewContainer(uiView) // 将 uiView 本身作为容器
            }
        }
    }
    
    // 删除原来的 displayImage 方法，因为现在使用 ImageRenderingService
}

// 新增：照片导航胶囊视图
struct PhotoNavigationCapsuleView: View {
    @ObservedObject var editViewModel: EditViewModel
    let screenWidth: CGFloat
    let screenHeight: CGFloat

    var body: some View {
        HStack(spacing: screenWidth * 0.08) { // 左右箭头和文本的间距8%屏幕宽度
            Button(action: {
                editViewModel.previousPhoto()
            }) {
                Image(systemName: "chevron.left")
                    .font(.system(size: screenHeight * UIConstants.standardFontSize, weight: .medium))
                    .foregroundColor(.white)
            }
            
            Text("\(editViewModel.currentPhotoIndex + 1) / \(editViewModel.selectedImages.count)")
                .font(.system(size: screenHeight * 0.015, weight: .regular))
                .foregroundColor(.white)
                .lineLimit(1)
            
            Button(action: {
                editViewModel.nextPhoto()
            }) {
                Image(systemName: "chevron.right")
                    .font(.system(size: screenHeight * UIConstants.standardFontSize, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, screenWidth * 0.03)
        .frame(height: screenHeight * 0.03)
        .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
        .clipShape(Capsule())
    }
}

// 编辑视图，提供图片编辑功能
struct EditView: View {
    // 添加对CameraViewModel的依赖
    @ObservedObject var cameraViewModel: CameraViewModel
    // 添加对SharedTabViewModel的依赖
    @ObservedObject var viewModel: SharedTabViewModel
    // 添加对EditViewModel的依赖
    @ObservedObject var editViewModel: EditViewModel
    // 添加对GalleryFilterViewModel的依赖
    @ObservedObject var filterViewModel: GalleryFilterViewModel

    // 临时使用旧的FilterViewModel，待步骤5更新
    @StateObject private var filterViewModelInstance = FilterDependencyContainer.filterViewModel()

    // 使用新的AdjustViewModel
    @StateObject private var adjustViewModelInstance = AdjustDependencyContainer.adjustViewModel()

    // 添加键盘状态跟踪
    @State private var isKeyboardVisible = false

    // 跟踪之前的页面，用于水印移除逻辑
    @State private var previousCategory: WatermarkCategory? = nil

    // 使用预览容器提供者
    @StateObject private var previewProvider = WatermarkPreviewProvider()

    // MARK: - 第三阶段：依赖注入服务
    /// 水印管理器服务 - 替代WatermarkManagerProvider
    private let watermarkManagerService = WatermarkDependencyContainer.shared.watermarkManagerService

    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 预览图片尺寸
    private let previewHeight: CGFloat
    
    init(cameraViewModel: CameraViewModel, viewModel: SharedTabViewModel, editViewModel: EditViewModel, filterViewModel: GalleryFilterViewModel = GalleryFilterViewModel()) {
        self.cameraViewModel = cameraViewModel
        self.viewModel = viewModel
        self.editViewModel = editViewModel
        self.filterViewModel = filterViewModel
        // 预览区域高度设置为屏幕高度的 50%
        previewHeight = UIScreen.main.bounds.height * 0.5
    }
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 0) {
                // 1. 固定的顶部导航
                NavigationTopBar(
                    selectedTab: $editViewModel.selectedCategory,
                    tabs: [
                        ("构图", .crop, "crop"),
                        ("滤镜", .filter, "camera.filters"),
                        ("调节", .adjust, "slider.horizontal.3"),
                        ("水印", .watermark, "note"),
                        ("特效", .effect, "sparkles"),
                        ("相纸", .paper, "rectangle.portrait")
                    ],
                    onTabSelected: { newTab in
                        print("🔍 [EditView] onTabSelected被调用")
                        print("   - 之前页面: \(previousCategory?.description ?? "nil")")
                        print("   - 当前页面: \(editViewModel.selectedCategory)")
                        print("   - 新页面: \(newTab)")

                        // 关键修复：使用previousCategory而不是editViewModel.selectedCategory
                        // 因为editViewModel.selectedCategory可能已经被SwiftUI更新了
                        if previousCategory == .watermark && newTab != .watermark {
                            print("🔄 [EditView] 从水印页面切换到\(newTab)，刷新视图移除水印")
                            refreshPreviewView()
                        } else {
                            print("🔍 [EditView] 页面切换不需要移除水印 - 之前: \(previousCategory?.description ?? "nil"), 新: \(newTab)")
                        }

                        // 更新之前页面的记录
                        previousCategory = newTab
                    },
                    useIcons: true,
                    iconWeight: .medium,
                    navigationMode: .editor,
                    onCancelPressed: {
                        print("取消按钮被点击")
                    },
                    onConfirmPressed: {
                        print("确定按钮被点击")
                    }
                )
                
                // 2. 共享的预览区域（固定，所有选项卡共用）
                ZStack(alignment: .top) {
                    // 根据是否有导入图像选择渲染方式
                    if editViewModel.selectedImage != nil {
                        // 有导入图像时使用Metal实时渲染 - 专业级GPU滤镜处理
                        MetalFilterView(
                            frame: CGRect(x: 0, y: 0, width: screenWidth, height: isKeyboardVisible ? (screenHeight * 0.25) : previewHeight),
                            previewProvider: previewProvider // 传递预览提供者
                        )
                        .frame(height: isKeyboardVisible ? (screenHeight * 0.25) : previewHeight)
                        .onAppear {
                            // 确保图像已设置到FilterViewModel
                            if let image = editViewModel.selectedImage {
                                print("🔧 [DEBUG] EditView: 确保图像设置到FilterViewModel")
                                filterViewModelInstance.setOriginalImage(image)
                            }
                        }
                    } else {
                        // 没有导入图像时使用原有的MockPreviewView
                        MockPreviewView(
                            frame: CGRect(x: 0, y: 0, width: screenWidth, height: isKeyboardVisible ? (screenHeight * 0.25) : previewHeight),
                            selectedImage: nil,
                            previewProvider: previewProvider
                        )
                        .frame(height: isKeyboardVisible ? (screenHeight * 0.25) : previewHeight)
                    }
                    
                    // 左上角 X 按钮 (仅当有选中图片时显示，用于返回相机预览)
                    if editViewModel.selectedImage != nil {
                        HStack {
                            Button(action: {
                                editViewModel.clearSelectedImageForEditing()
                            }) {
                                Image(systemName: "xmark")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .fontWeight(.bold)
                                    .foregroundColor(.black)
                                    .padding(screenHeight * 0.01)
                                    .background(Color.white.opacity(UIConstants.highOpacity))
                                    .clipShape(Circle())
                                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                            }
                            Spacer() // 将按钮推到HStack的左边
                        }
                        .padding(.leading, screenWidth * 0.03) // 整个HStack距离左边缘的间距
                        .padding(.top, screenHeight * 0.01)     // 整个HStack距离顶部的间距
                    }
                    
                    // 右上角 旋转按钮 (仅在非构图页面显示)
                    if editViewModel.selectedCategory != .crop {
                        HStack {
                            Spacer() // 将按钮推到HStack的右边
                            Button(action: {
                                // TODO: 实现旋转功能
                                print("旋转按钮被点击")
                            }) {
                                Image(systemName: "rotate.left")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: screenHeight * 0.02, height: screenHeight * 0.02)
                                    .fontWeight(.bold)
                                    .foregroundColor(.black)
                                    .padding(screenHeight * 0.01)
                                    .background(Color.white.opacity(UIConstants.highOpacity))
                                    .clipShape(Circle())
                                    .shadow(color: Color.black.opacity(0.2), radius: 4, x: 0, y: 2)
                            }
                        }
                        .padding(.trailing, screenWidth * 0.03) // 整个HStack距离右边缘的间距
                        .padding(.top, screenHeight * 0.01)     // 整个HStack距离顶部的间距
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topTrailing)
                    }
                    
                    // 将底部控制元素拆分为两个独立的ZStack布局，避免相互影响
                    
                    // 1. 直方图和加号按钮 - 固定在底部
                    VStack {
                        Spacer()
                        HStack(alignment: .center) {
                            HistogramView(viewModel: cameraViewModel, screenHeight: screenHeight, alwaysVisible: true, deviceOrientation: .portrait)
                            Spacer() 
                            AddButton {
                                // 先停用当前水印，防止在视图切换过程中进行截图操作导致崩溃
                                watermarkManagerService.removeCurrentWatermark()

                                // 记住当前页面是编辑页面
                                viewModel.previousTabBeforePhotoSelection = .watermark
                                // 记住当前的编辑子页面
                                viewModel.previousEditCategoryBeforePhotoSelection = editViewModel.selectedCategory
                                print("🔄 设置返回页面为: .watermark, 子页面: \(editViewModel.selectedCategory)")

                                // 延迟切换到相册，确保水印已被移除
                                DispatchQueue.main.async {
                                viewModel.shouldEnterGallerySelectionMode = true
                                viewModel.selectedTab = .gallery
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.03)
                        .padding(.bottom, screenHeight * 0.01)
                    }
                    
                    // 2. 照片导航控件 - 独立布局，不影响直方图和加号
                    if editViewModel.selectedImages.count > 1 {
                        VStack {
                            Spacer()
                            HStack {
                                Spacer()
                                PhotoNavigationCapsuleView(editViewModel: editViewModel, screenWidth: screenWidth, screenHeight: screenHeight)
                                Spacer()
                            }
                            .padding(.horizontal, screenWidth * 0.03)
                            .padding(.bottom, screenHeight * 0.01) // 恢复为原始的内边距值
                        }
                    }
                }
                .frame(height: isKeyboardVisible ? (screenHeight * 0.25) : previewHeight)

                // 3. 根据选中的类别显示不同的控制区域内容
                if editViewModel.selectedCategory == .crop {
                    // 裁切控制区域
                    CropView()
                } else if editViewModel.selectedCategory == .filter {
                    // 滤镜控制区域
                    FilterView(filterViewModel: filterViewModelInstance)
                } else if editViewModel.selectedCategory == .adjust {
                    // 调节控制区域
                    AdjustView(adjustViewModel: adjustViewModelInstance)
                } else if editViewModel.selectedCategory == .watermark {
                    // 水印控制区域
                    WatermarkControlView(isKeyboardVisible: $isKeyboardVisible, previewContainer: previewProvider.previewContainer)
                } else if editViewModel.selectedCategory == .effect {
                    // 特效控制区域
                    EffectsDependencyContainer.effectsView()
                } else if editViewModel.selectedCategory == .paper {
                    // 相纸控制区域
                    PaperView()
                }
                if !isKeyboardVisible {
                    Spacer(minLength: 0)
                }
            }
        }
        .background(Color(uiColor: .systemGray6).edgesIgnoringSafeArea(.all))
        .alert(isPresented: $editViewModel.showPuzzlePhotoAlert) {
            Alert(
                title: Text("提示"),
                message: Text(editViewModel.puzzleAlertMessage),
                dismissButton: .default(Text("确定"))
            )
        }
        .onAppear {
            // 初始化previousCategory
            previousCategory = editViewModel.selectedCategory
            print("🔄 EditView出现，初始化previousCategory: \(previousCategory?.description ?? "nil")")

            // 只在第一次进入时设置默认页面，避免覆盖恢复的页面
            if editViewModel.selectedCategory == .watermark {
                // 保持当前页面，不强制重置
                print("🔄 EditView出现，当前页面: \(editViewModel.selectedCategory)")
            }

            // 添加恢复编辑子页面的通知观察
            NotificationCenter.default.addObserver(
                forName: Notification.Name("RestoreEditCategory"),
                object: nil,
                queue: .main
            ) { notification in
                if let editCategory = notification.object as? WatermarkCategory {
                    print("🔄 恢复到编辑子页面: \(editCategory)")
                    DispatchQueue.main.async {
                        editViewModel.selectedCategory = editCategory
                    }
                }
            }

            // 添加键盘显示/隐藏通知观察
            let notificationService = NotificationService.shared
            
            notificationService.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                // 使用动画工具类处理键盘显示动画
                let animation = AnimationUtils.updateSharedAnimationParameters(from: notification)
                
                // 使用获取的动画
                withAnimation(animation) {
                    isKeyboardVisible = true
                }
            }
            
            notificationService.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { notification in
                // 使用动画工具类处理键盘隐藏动画
                let animation = AnimationUtils.updateSharedAnimationParameters(from: notification)
                
                // 使用获取的动画
                withAnimation(animation) {
                    isKeyboardVisible = false
                }
            }
        }
    }

    /// 刷新预览视图，移除水印效果
    private func refreshPreviewView() {
        print("🔍 [EditView] refreshPreviewView() 被调用")
        print("   - selectedImage: \(editViewModel.selectedImage != nil ? "有图像" : "nil")")
        print("   - watermarkManager: \(watermarkManagerService.isManagerAvailable() ? "存在" : "nil")")

        // 关键修复：水印是UI元素，需要通过水印管理器来移除，而不是重新渲染图像
        print("🔄 [EditView] 移除水印UI元素")
        print("   - 调用watermarkManager.removeCurrentWatermark()")
        watermarkManagerService.removeCurrentWatermark()
        print("   - watermarkManager.removeCurrentWatermark() 调用完成")

        print("🔍 [EditView] refreshPreviewView() 完成")
    }
}
