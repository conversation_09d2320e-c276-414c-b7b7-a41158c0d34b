import SwiftUI

// Copyright (c) 2025 LoniceraLab. All rights reserved.

// MARK: - 用户体验增强工具
// 注意：使用项目现有的HapticService，不再需要单独的HapticFeedbackManager

// 注意：性能监控已移至PerformanceService，符合MVVM-S架构

// 调节控制视图组件 - MVVM-S架构重构版本
struct AdjustView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // 使用MVVM-S架构的重构后ViewModel
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored

    // MARK: - 初始化
    init(adjustViewModel: AdjustViewModelRefactored) {
        self.adjustViewModel = adjustViewModel
    }
    
    // MARK: - 兼容性初始化（支持依赖注入容器）
    init() {
        self.adjustViewModel = AdjustDependencyContainer.shared.createAdjustViewModel()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 调整参数选择 - 可滚动
            HStack {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: screenWidth * 0.04) {
                        AdjustParameterButton(title: "曝光", isSelected: adjustViewModel.selectedParameter == "exposure") {
                            adjustViewModel.selectedParameter = "exposure"
                        }
                        AdjustParameterButton(title: "色彩", isSelected: adjustViewModel.selectedParameter == "color") {
                            adjustViewModel.selectedParameter = "color"
                        }
                        AdjustParameterButton(title: "曲线", isSelected: adjustViewModel.selectedParameter == "curve") {
                            adjustViewModel.selectedParameter = "curve"
                        }
                        AdjustParameterButton(title: "色调分离", isSelected: adjustViewModel.selectedParameter == "toneSplit") {
                            adjustViewModel.selectedParameter = "toneSplit"
                        }
                        AdjustParameterButton(title: "HSL", isSelected: adjustViewModel.selectedParameter == "hsl") {
                            adjustViewModel.selectedParameter = "hsl"
                        }
                        AdjustParameterButton(title: "细节", isSelected: adjustViewModel.selectedParameter == "detail") {
                            adjustViewModel.selectedParameter = "detail"
                        }
                        AdjustParameterButton(title: "校准", isSelected: adjustViewModel.selectedParameter == "calibration") {
                            adjustViewModel.selectedParameter = "calibration"
                        }
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                }

                // 重置按钮
                Button(action: {
                    resetCurrentTab()
                }) {
                    Image(systemName: "arrow.counterclockwise")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
                .padding(.trailing, screenWidth * 0.04)
            }
            .padding(.top, screenHeight * 0.01)
            .padding(.bottom, screenHeight * 0.0075)
            
            // 根据选中的参数类型显示不同的选项
            ZStack {
                // 曝光操作区
                if adjustViewModel.selectedParameter == "exposure" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            ExposureSlider(
                                title: "曝光补偿",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.exposure) },
                                    set: { adjustViewModel.updateParameter(\.exposure, value: Float($0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "亮度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.brightness) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.brightness, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "对比度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.contrast) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.contrast, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "高光",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.highlights) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.highlights, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "阴影",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.shadows) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.shadows, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            // 白色/黑色选项 - 暂时移除条件显示，因为需要适配到当前架构
                            FilterParameterSlider(
                                title: "白色",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.whites) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.whites, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "黑色",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.blacks) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.blacks, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            // 去雾选项
                            FilterParameterSlider(
                                title: "去雾",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.dehaze) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.dehaze, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }

                // 色彩操作区
                else if adjustViewModel.selectedParameter == "color" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            FilterParameterSlider(
                                title: "色温",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.temperature) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.temperature, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "色调",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.tint) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.tint, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "饱和度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.saturation) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.saturation, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "自然饱和度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.vibrance) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.vibrance, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "褪色",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.fadeEffect) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.fadeEffect, value: Float($0 * 100.0)) }
                                ),
                                range: 0.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "黑白",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.monoEffect) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.monoEffect, value: Float($0 * 100.0)) }
                                ),
                                range: 0.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }

                // 色调分离操作区
                else if adjustViewModel.selectedParameter == "toneSplit" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 色调选择器
                            HStack(spacing: screenWidth * 0.05) {
                                Spacer()
                                Button(action: {
                                    HapticService.shared.selectionFeedback()
                                    adjustViewModel.switchToToneOption("阴影")
                                }) {
                                    Image(systemName: "circle.bottomhalf.filled")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(adjustViewModel.selectedToneOption == "阴影" ? UIConstants.dialIndicatorColor : .white)
                                        .scaleEffect(adjustViewModel.selectedToneOption == "阴影" ? 1.1 : 1.0)
                                        .animation(.easeInOut(duration: 0.2), value: adjustViewModel.selectedToneOption)
                                }

                                Button(action: {
                                    HapticService.shared.selectionFeedback()
                                    adjustViewModel.switchToToneOption("高光")
                                }) {
                                    Image(systemName: "circle.tophalf.filled")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(adjustViewModel.selectedToneOption == "高光" ? UIConstants.dialIndicatorColor : .white)
                                        .scaleEffect(adjustViewModel.selectedToneOption == "高光" ? 1.1 : 1.0)
                                        .animation(.easeInOut(duration: 0.2), value: adjustViewModel.selectedToneOption)
                                }
                                Spacer()
                            }
                            .padding(.vertical, screenHeight * 0.01)

                            // 色彩圆环选择器
                            GeometryReader { geometry in
                                ZStack {
                                    // 色彩环形渐变底图
                                    EnhancedColorWheel(size: CGSize(width: screenHeight * 0.2, height: screenHeight * 0.2))
                                        .frame(width: screenHeight * 0.2, height: screenHeight * 0.2)

                                    // 选择指示器
                                    Circle()
                                        .fill(Color.white)
                                        .frame(width: screenHeight * (adjustViewModel.isPressing ? 0.024 : 0.02), height: screenHeight * (adjustViewModel.isPressing ? 0.024 : 0.02))
                                        .shadow(color: Color.black.opacity(0.4), radius: 2, x: 0, y: 1)
                                        .offset(x: adjustViewModel.toneHueOffset.width, y: adjustViewModel.toneHueOffset.height)
                                        .animation(.spring(response: 0.2), value: adjustViewModel.isPressing)
                                }
                                .frame(width: screenHeight * 0.2, height: screenHeight * 0.2)
                                .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                                .gesture(
                                    // 组合手势：双击 + 拖拽
                                    SimultaneousGesture(
                                        // 双击手势 - 恢复默认
                                        TapGesture(count: 2)
                                            .onEnded {
                                                HapticService.shared.lightImpact()
                                                adjustViewModel.resetCurrentTone()
                                            },

                                        // 拖拽手势 - 调节色相和饱和度
                                        DragGesture(minimumDistance: 0)
                                            .onChanged { value in
                                                // 优化：只在第一次触摸时设置按压状态
                                                if !adjustViewModel.isPressing {
                                                    withAnimation(.easeInOut(duration: 0.1)) {
                                                        adjustViewModel.isPressing = true
                                                    }
                                                }

                                                // 缓存计算值避免重复计算
                                                let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
                                                let radius = screenHeight * 0.1

                                                let dx = value.location.x - center.x
                                                let dy = value.location.y - center.y
                                                let distance = sqrt(dx * dx + dy * dy)

                                                // 性能优化：预计算角度
                                                let angle = atan2(dy, dx)
                                                let normalizedAngle = (angle + .pi) / (2 * .pi)

                                                if distance <= radius {
                                                    // 在色轮范围内 - 直接使用实际位置
                                                    let newOffset = CGSize(width: dx, height: dy)

                                                    // 优化：只在位置变化足够大时更新
                                                    let offsetDelta = sqrt(pow(newOffset.width - adjustViewModel.toneHueOffset.width, 2) +
                                                                         pow(newOffset.height - adjustViewModel.toneHueOffset.height, 2))

                                                    if offsetDelta > 2.0 { // 2点的最小移动距离
                                                        adjustViewModel.toneHueOffset = newOffset
                                                        let saturation = distance / radius
                                                        adjustViewModel.updateToneHue(normalizedAngle, saturation: saturation)
                                                    }
                                                } else {
                                                    // 超出范围，限制在色轮边缘
                                                    let constrainedX = cos(angle) * radius
                                                    let constrainedY = sin(angle) * radius
                                                    let constrainedOffset = CGSize(width: constrainedX, height: constrainedY)

                                                    // 优化：只在边界位置变化时更新
                                                    let offsetDelta = sqrt(pow(constrainedOffset.width - adjustViewModel.toneHueOffset.width, 2) +
                                                                         pow(constrainedOffset.height - adjustViewModel.toneHueOffset.height, 2))

                                                    if offsetDelta > 2.0 {
                                                        adjustViewModel.toneHueOffset = constrainedOffset
                                                        adjustViewModel.updateToneHue(normalizedAngle, saturation: 1.0)
                                                    }
                                                }
                                            }
                                            .onEnded { _ in
                                                withAnimation(.easeInOut(duration: 0.2)) {
                                                    adjustViewModel.isPressing = false
                                                }
                                            }
                                    )
                                )
                            }
                            .frame(height: screenHeight * 0.2)
                            .padding(.vertical, screenHeight * 0.01)

                            FilterParameterSlider(
                                title: "平衡",
                                value: Binding(
                                    get: {
                                        return Double(adjustViewModel.currentParameters.splitToningBalance) / 100.0
                                    },
                                    set: { value in
                                        let balanceValue = Float(value * 100.0)
                                        adjustViewModel.updateBalance(balanceValue)
                                    }
                                ),
                                range: -1.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                    .onAppear {
                        // 初始化已由AdjustViewModel自动处理
                        print("🎨 [色调分离] UI初始化完成")
                    }
                }

                // HSL操作区
                else if adjustViewModel.selectedParameter == "hsl" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            HSLColorSelector(selectedColorIndex: Binding(
                                get: { adjustViewModel.selectedColorIndex },
                                set: { adjustViewModel.switchHSLColorRange(to: $0) }
                            ))
                                .padding(.vertical, screenHeight * 0.005)
                            Spacer().frame(height: screenHeight * 0.0175)

                            FilterParameterSlider(
                                title: "色相",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.hue) / 180.0 },
                                    set: { adjustViewModel.updateHSLHue(Float($0 * 180.0)) }
                                ),
                                range: -1.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "饱和度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.hslSaturation) / 100.0 },
                                    set: { adjustViewModel.updateHSLSaturation(Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "明度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.hslLuminance) / 100.0 },
                                    set: { adjustViewModel.updateHSLLuminance(Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }

                // 曲线操作区
                else if adjustViewModel.selectedParameter == "curve" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            ColorSelector(colors: [.white, .red, .green, .blue], selectedIndex: Binding(
                                get: { adjustViewModel.selectedChannel.rawValue },
                                set: { newValue in
                                    if let channel = CurveChannel(rawValue: newValue) {
                                        adjustViewModel.switchToChannel(channel)
                                    }
                                }
                            ), hasStroke: true, circleSize: 0.025)
                                .padding(.vertical, screenHeight * 0.005)
                            Spacer().frame(height: screenHeight * 0.0075)
                            CurveEditorView(adjustViewModel: adjustViewModel)
                                .padding(.vertical, screenHeight * 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }

                // 细节操作区
                else if adjustViewModel.selectedParameter == "detail" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            FilterParameterSlider(
                                title: "清晰度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.clarity) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.clarity, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "清晰度2",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.clarity2) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.clarity2, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            FilterParameterSlider(
                                title: "暗角强度",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.vignetteIntensity) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.vignetteIntensity, value: Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            VStack(spacing: screenHeight * 0.01) {
                                HStack {
                                    Text("暗角半径")
                                        .font(.system(size: screenHeight * 0.015))
                                        .foregroundColor(.white)
                                    Spacer()
                                    Text("\(Int(adjustViewModel.currentParameters.vignetteRadius * 50))")
                                        .font(.system(size: screenHeight * 0.015))
                                        .foregroundColor(.white)
                                }
                                .padding(.horizontal, screenWidth * 0.04)

                                CustomSlider(
                                    value: Binding(
                                        get: { Double(adjustViewModel.currentParameters.vignetteRadius) / 2.0 },
                                        set: { adjustViewModel.updateParameter(\.vignetteRadius, value: Float($0 * 2.0)) }
                                    ),
                                    range: 0.0...1.0,
                                    step: 0.01
                                )
                            }
                            .padding(.vertical, screenHeight * 0.005)

                            // 降噪选项 - 暂时移除条件显示
                            FilterParameterSlider(
                                title: "降噪",
                                value: Binding(
                                    get: { Double(adjustViewModel.currentParameters.noiseReduction) / 100.0 },
                                    set: { adjustViewModel.updateParameter(\.noiseReduction, value: Float($0 * 100.0)) }
                                ),
                                range: 0.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }

                // 色彩校准操作区 - 使用现有的ColorSelector组件
                else if adjustViewModel.selectedParameter == "calibration" {
                    ScrollView(showsIndicators: false) {
                        VStack(spacing: 0) {
                            // 校准颜色选择器 - 红/绿/蓝三个原色，使用两倍间距和文字标签
                            ColorSelector(
                                colors: [.red, .green, .blue],
                                selectedIndex: Binding(
                                    get: { adjustViewModel.selectedCalibrationColorIndex },
                                    set: { adjustViewModel.selectCalibrationColor($0) }
                                ),
                                spacing: screenWidth * 0.10, // 两倍间距 (原来是0.05)
                                labels: ["红原色", "绿原色", "蓝原色"]
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            Spacer().frame(height: screenHeight * 0.015)

                            // 色相调整 - 复用现有的FilterParameterSlider
                            FilterParameterSlider(
                                title: "色相",
                                value: Binding(
                                    get: { Double(adjustViewModel.getCurrentCalibrationHue()) / 100.0 },
                                    set: { adjustViewModel.updateCalibrationHue(Float($0 * 100.0)) }
                                ),
                                range: -1.0...1.0
                            )
                            .padding(.vertical, screenHeight * 0.005)

                            // 饱和度调整 - 复用现有的FilterParameterSlider
                            FilterParameterSlider(
                                title: "饱和度",
                                value: Binding(
                                    get: { Double(adjustViewModel.getCurrentCalibrationSaturation()) / 100.0 },
                                    set: { adjustViewModel.updateCalibrationSaturation(Float($0 * 100.0)) }
                                )
                            )
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)
                        .frame(maxWidth: .infinity)
                    }
                    .background(Color.clear)
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
    }

    // 重置当前选中的调节选项卡
    private func resetCurrentTab() {
        switch adjustViewModel.selectedParameter {
        case "exposure":
            adjustViewModel.updateParameter(\.exposure, value: 0.0)
            adjustViewModel.updateParameter(\.brightness, value: 0.0)
            adjustViewModel.updateParameter(\.contrast, value: 0.0)
            adjustViewModel.updateParameter(\.highlights, value: 0.0)
            adjustViewModel.updateParameter(\.shadows, value: 0.0)
            // 暂时移除条件重置，因为需要适配到当前架构
            adjustViewModel.updateParameter(\.whites, value: 0.0)
            adjustViewModel.updateParameter(\.blacks, value: 0.0)
            adjustViewModel.updateParameter(\.dehaze, value: 0.0)
        case "color":
            adjustViewModel.updateParameter(\.temperature, value: 0.0)
            adjustViewModel.updateParameter(\.tint, value: 0.0)
            adjustViewModel.updateParameter(\.saturation, value: 0.0)
            adjustViewModel.updateParameter(\.vibrance, value: 0.0)
            adjustViewModel.updateParameter(\.fadeEffect, value: 0.0)
            adjustViewModel.updateParameter(\.monoEffect, value: 0.0)
        case "toneSplit":
            // 使用AdjustViewModel的统一重置方法
            adjustViewModel.resetAllTones()
        case "hsl":
            // 使用AdjustViewModel的统一重置方法
            adjustViewModel.resetAllHSLColors()
        case "curve":
            // 使用AdjustViewModel的统一重置方法
            adjustViewModel.resetCurrentChannel()
            print("🔄 曲线通道 \(adjustViewModel.selectedChannel.displayName) 已重置")
        case "detail":
            adjustViewModel.updateParameter(\.clarity, value: 0.0)
            adjustViewModel.updateParameter(\.clarity2, value: 0.0)
            adjustViewModel.updateParameter(\.vignetteIntensity, value: 0.0)
            adjustViewModel.updateParameter(\.vignetteRadius, value: 1.0)
            // 暂时移除条件重置
            adjustViewModel.updateParameter(\.noiseReduction, value: 0.0)
        case "calibration":
            adjustViewModel.resetAllCalibrationColors()
        default:
            break
        }
    }
}

// MARK: - 滤镜参数滑块组件

/// 专门用于滤镜参数调节的滑块组件
struct FilterParameterSlider: View {
    let title: String
    @Binding var value: Double
    var range: ClosedRange<Double> = -1.0...1.0

    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        VStack(spacing: screenHeight * 0.01) {
            HStack {
                Text(title)
                    .font(.system(size: screenHeight * 0.015))
                    .foregroundColor(.white)
                Spacer()
                Text("\(Int(value * 100))")
                    .font(.system(size: screenHeight * 0.015))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, screenWidth * 0.04)

            CustomSlider(value: $value, range: range, step: 0.01)
        }
    }
}

// 修改通用的滑块选项组件，移除 onValueChanged 回调
struct AdjustSliderOption: View {
    let title: String
    @Binding var value: Double
    // Removed: var onValueChanged: ((Double) -> Void)? = nil

    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        VStack(spacing: screenHeight * 0.01) {
            HStack {
                Text(title).font(.system(size: screenHeight * 0.015)).foregroundColor(.white)
                Spacer()
                Text("\(Int(value * 100))").font(.system(size: screenHeight * 0.015)).foregroundColor(.white)
            }
            .padding(.horizontal, screenWidth * 0.04)

            CustomSlider(value: $value, range: -1.0...1.0, step: 0.01)
            // Removed: .onChange(of: value) { ... }
        }
    }
}

// 调节参数按钮组件
struct AdjustParameterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
        }
    }
}

// HSL颜色选择器组件兼容性封装 (恢复原始结构)
struct HSLColorSelector: View {
    @Binding var selectedColorIndex: Int

    var body: some View {
        ColorSelector(
            colors: [.red, .orange, .yellow, .green, .cyan, .blue, .purple, .pink],
            selectedIndex: $selectedColorIndex
        )
    }
}

// 通用颜色选择器组件 - 支持自定义间距和文字标签
struct ColorSelector: View {
    let colors: [Color]
    @Binding var selectedIndex: Int
    var hasStroke: Bool = true
    var circleSize: CGFloat = 0.025
    var spacing: CGFloat? = nil // 自定义间距，nil时使用默认值
    var labels: [String]? = nil // 可选的文字标签

    private let screenHeight = UIScreen.main.bounds.height
    private let screenWidth = UIScreen.main.bounds.width

    // 计算实际间距
    private var actualSpacing: CGFloat {
        spacing ?? (screenWidth * 0.05) // 默认间距
    }

    var body: some View {
        HStack {
            Spacer()
            HStack(spacing: actualSpacing) {
                ForEach(0..<colors.count, id: \.self) { index in
                    VStack(spacing: screenHeight * 0.01) { // 圆形和文字之间的间距
                        // 颜色圆形
                        Circle()
                            .fill(colors[index])
                            .frame(width: screenHeight * circleSize, height: screenHeight * circleSize)
                            .overlay(
                                Circle()
                                    .stroke(selectedIndex == index ? .white : .clear, lineWidth: hasStroke ? 1.5 : 0)
                                    .padding(hasStroke ? -1.5 : 0)
                            )
                            .onTapGesture {
                                selectedIndex = index
                            }

                        // 可选的文字标签
                        if let labels = labels, index < labels.count {
                            Text(labels[index])
                                .font(.system(size: screenHeight * 0.0125, weight: .medium))
                                .foregroundColor(.white)
                                .opacity(0.8)
                        }
                    }
                }
            }
            Spacer()
        }
        .background(Color.clear)
    }
}

// MARK: - Curve Editor View (严格复制原始代码)
struct CurveEditorView: View {
    // MVVM重构：完全使用AdjustViewModel作为唯一数据源
    @ObservedObject private var adjustViewModel: AdjustViewModelRefactored

    // Screen dimensions
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // 通道颜色和索引
    private let channelColors: [CurveChannel: Color] = [
        .rgb: .white,
        .red: .red,
        .green: .green,
        .blue: .blue
    ]

    // 线条粗细
    private var selectedLineWidth: CGFloat {
        return screenHeight * 0.0025 // 选中状态线条粗细为0.25%屏幕高度
    }

    private var unselectedLineWidth: CGFloat {
        return 1.0 // 未选中状态线条粗细，与参考线相近
    }

    // 通道不透明度
    private let selectedOpacity: Double = 1.0
    private let unselectedOpacity: Double = 0.6

    // Define colors for different curves
    private func curveColor(_ channel: CurveChannel) -> Color {
        return channelColors[channel] ?? .white
    }

    // 初始化方法 - 适配MVVM-S架构
    init(adjustViewModel: AdjustViewModelRefactored) {
        self.adjustViewModel = adjustViewModel
    }

    var body: some View {
        CurveEditorContentView(adjustViewModel: adjustViewModel)
    }

    // MARK: - 私有方法
    private func curveColor(_ channel: CurveChannel) -> Color {
        switch channel {
        case .rgb: return .white
        case .red: return .red
        case .green: return .green
        case .blue: return .blue
        }
    }

    private func provideBoundaryFeedback() {
        // 触觉反馈实现
    }
}

// MARK: - CurveEditor内容视图
private struct CurveEditorContentView: View {
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height










// MARK: - 边界指示器组件
struct CurveBoundaryIndicator: View {
    let width: CGFloat
    let height: CGFloat

    var body: some View {
        ZStack {
            // 边界缓冲区指示
            let bufferSize = width * 0.02 // 2% 缓冲区

            Rectangle()
                .stroke(Color.orange, lineWidth: 1)
                .frame(width: width - bufferSize * 2, height: height - bufferSize * 2)
                .position(x: width / 2, y: height / 2)

            // 安全区域指示
            let safeZoneSize = width * 0.05 // 5% 安全区域

            Rectangle()
                .stroke(Color.green.opacity(0.5), lineWidth: 0.5)
                .frame(width: width - safeZoneSize * 2, height: height - safeZoneSize * 2)
                .position(x: width / 2, y: height / 2)
        }
    }
}

// Helper for drawing the grid
struct CurveGrid: View {
    let width: CGFloat
    let height: CGFloat
    let horizontalLines: Int = 4
    let verticalLines: Int = 4

    var body: some View {
        ZStack {
            // 网格线
            Path { path in
                // Draw horizontal lines
                for i in 0...horizontalLines {
                    let y = CGFloat(i) * height / CGFloat(horizontalLines)
                    path.move(to: CGPoint(x: 0, y: y))
                    path.addLine(to: CGPoint(x: width, y: y))
                }
                // Draw vertical lines
                for i in 0...verticalLines {
                    let x = CGFloat(i) * width / CGFloat(verticalLines)
                    path.move(to: CGPoint(x: x, y: 0))
                    path.addLine(to: CGPoint(x: x, y: height))
                }
            }
            .stroke(Color.gray.opacity(UIConstants.lightOpacity), lineWidth: 1)

            // 添加从左下角到右上角的参考线
            Path { path in
                path.move(to: CGPoint(x: 0, y: height))
                path.addLine(to: CGPoint(x: width, y: 0))
            }
            .stroke(Color.gray.opacity(UIConstants.lightOpacity), lineWidth: 1)
        }
    }
}

// 替换原ToneSplitControlView的实现
struct ToneSplitControlView: View {
    @ObservedObject var viewModel: EditViewModel
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    @State private var dragLocation: CGPoint = .zero
    @State private var isPressing = false
    private let hapticFeedback = UIImpactFeedbackGenerator(style: .light)

    var body: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: 0) {
                HStack(spacing: screenWidth * 0.05) {
                    Spacer()
                    Button(action: { viewModel.adjustSelectedToneOption = "阴影" }) {
                        Image(systemName: "circle.bottomhalf.filled")
                            .font(.system(size: screenHeight * 0.025))
                            .foregroundColor(viewModel.adjustSelectedToneOption == "阴影" ? UIConstants.dialIndicatorColor : .white)
                    }
                    Button(action: { viewModel.adjustSelectedToneOption = "中间调" }) {
                        Image(systemName: "circle.lefthalf.filled")
                            .font(.system(size: screenHeight * 0.025))
                            .foregroundColor(viewModel.adjustSelectedToneOption == "中间调" ? UIConstants.dialIndicatorColor : .white)
                    }
                    Button(action: { viewModel.adjustSelectedToneOption = "高光" }) {
                        Image(systemName: "circle.tophalf.filled")
                            .font(.system(size: screenHeight * 0.025))
                            .foregroundColor(viewModel.adjustSelectedToneOption == "高光" ? UIConstants.dialIndicatorColor : .white)
                    }
                    Button(action: { viewModel.adjustSelectedToneOption = "全局" }) {
                        Image(systemName: "circle.fill")
                            .font(.system(size: screenHeight * 0.025))
                            .foregroundColor(viewModel.adjustSelectedToneOption == "全局" ? UIConstants.dialIndicatorColor : .white)
                    }
                    Spacer()
                }
                .padding(.vertical, screenHeight * 0.005)

                Spacer().frame(height: screenHeight * 0.01)

                // 改进的色轮控件
                GeometryReader { geometry in
                    ZStack {
                        // 色彩环形渐变底图
                        EnhancedColorWheel(size: CGSize(width: screenHeight * 0.2, height: screenHeight * 0.2))
                            .frame(width: screenHeight * 0.2, height: screenHeight * 0.2)

                        // 选择指示器
                        Circle()
                            .fill(Color.white)
                            .frame(width: screenHeight * (isPressing ? 0.024 : 0.02), height: screenHeight * (isPressing ? 0.024 : 0.02))
                            .shadow(color: Color.black.opacity(0.4), radius: 2, x: 0, y: 1)
                            .offset(x: viewModel.toneHueOffset.width, y: viewModel.toneHueOffset.height)
                            .animation(.spring(response: 0.2), value: isPressing)
                    }
                    .frame(width: screenHeight * 0.2, height: screenHeight * 0.2)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2)
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { value in
                                // 第一次拖动时触发触感反馈
                                if !isPressing {
                                    hapticFeedback.prepare()
                                    hapticFeedback.impactOccurred()
                                    isPressing = true
                                }

                                // 计算中心点和位置
                                let center = CGPoint(x: geometry.size.width / 2, y: geometry.size.height / 2)
                                let location = value.location
                                let radius = screenHeight * 0.1 // 色轮半径

                                // 计算相对于中心的位置
                                let dx = location.x - center.x
                                let dy = location.y - center.y
                                let distance = sqrt(dx*dx + dy*dy)
                                let angle = atan2(dy, dx)

                                if distance <= radius {
                                    // 在色轮范围内
                                    viewModel.toneHueOffset = CGSize(
                                        width: dx,
                                        height: dy
                                    )

                                    // 计算并更新色相值 (0-1范围)
                                    let normalizedAngle = (angle + .pi) / (2 * .pi)

                                    // 计算饱和度 (0-1范围)
                                    let saturation = distance / radius

                                    // 更新色调值和饱和度(这里仅更新色调，如需饱和度需扩展ViewModel)
                                    viewModel.updateToneHue(normalizedAngle)
                                } else {
                                    // 超出范围，限制在色轮边缘
                                    let constrainedX = cos(angle) * radius
                                    let constrainedY = sin(angle) * radius

                                    viewModel.toneHueOffset = CGSize(
                                        width: constrainedX,
                                        height: constrainedY
                                    )

                                    // 更新色调值
                                    let normalizedAngle = (angle + .pi) / (2 * .pi)
                                    viewModel.updateToneHue(normalizedAngle)
                                }
                            }
                            .onEnded { _ in
                                isPressing = false
                            }
                    )
                }
                .frame(height: screenHeight * 0.2)

                AdjustSliderOption(title: "亮度", value: $viewModel.adjustToneBrightness)
                    .padding(.vertical, screenHeight * 0.005)
            }
            .padding(.vertical, screenHeight * 0.0125)
            .frame(maxWidth: .infinity)
        }
        .background(Color.clear)
    }
}

// 增强型色彩轮 - 使用SwiftUI Canvas高效绘制
struct EnhancedColorWheel: View {
    let size: CGSize

    var body: some View {
        if #available(iOS 17.0, *) {
            // 使用着色器实现 - 保持相同的色彩计算逻辑
            Circle()
                .frame(width: size.width, height: size.height)
                .colorEffect(ShaderLibrary.hsbColorWheel(size: size))
                .clipShape(Circle()) // 确保剪裁为圆形
        } else {
            // 低版本iOS回退到改进的点阵实现
            Canvas { context, canvasSize in
                // 绘制HSB色轮
                let center = CGPoint(x: size.width/2, y: size.height/2)
                let radius = min(size.width, size.height)/2

                // 提高精度，减少锯齿问题
                let step = max(1, Int(size.width / 400))

                // 超采样设置
                let samplesCount = 4 // 2x2超采样
                let subPixelStep: CGFloat = 0.4 // 子像素采样距离

                for x in stride(from: 0, to: Int(size.width), by: step) {
                    for y in stride(from: 0, to: Int(size.height), by: step) {
                        let point = CGPoint(x: CGFloat(x), y: CGFloat(y))

                        // 为超采样准备累加值
                        var colorSum = [CGFloat](repeating: 0, count: 4) // R,G,B,A
                        var samplesUsed: CGFloat = 0

                        // 对每个点进行超采样 (2x2)
                        for i in 0..<samplesCount {
                            // 计算子像素采样点的偏移
                            let offsetX = CGFloat(i % 2) * subPixelStep - subPixelStep / 2
                            let offsetY = CGFloat(i / 2) * subPixelStep - subPixelStep / 2

                            // 当前子像素采样点位置
                            let samplePos = CGPoint(x: point.x + offsetX, y: point.y + offsetY)

                            // 计算到中心的距离
                            let dx = samplePos.x - center.x
                            let dy = samplePos.y - center.y
                            let distance = sqrt(dx*dx + dy*dy)

                            // 如果点在圆内或接近边缘(用于抗锯齿)
                            if distance <= radius + 1 {
                                // 计算饱和度 (0-1)
                                let saturation = min(distance / radius, 1.0)

                                // 计算色相 (0-1)
                                let angle = atan2(dy, dx)
                                let hue = (angle + .pi) / (2 * .pi)

                                // 使用高饱和度和高亮度创建鲜艳的颜色
                                let color = Color(hue: hue, saturation: saturation, brightness: 1.0)

                                // 边缘平滑处理
                                var alpha: CGFloat = 1.0
                                if distance > radius - 1 && distance <= radius + 1 {
                                    alpha = 1.0 - (distance - (radius - 1)) / 2
                                }

                                // 转换为RGBA并累加到结果中
                                if let components = UIColor(color).cgColor.components {
                                    colorSum[0] += components[0] * alpha
                                    colorSum[1] += components[1] * alpha
                                    colorSum[2] += components[2] * alpha
                                    colorSum[3] += alpha
                                    samplesUsed += 1
                                }
                            }
                        }

                        // 如果有有效采样
                        if samplesUsed > 0 {
                            // 计算平均值
                            let r = colorSum[0] / samplesUsed
                            let g = colorSum[1] / samplesUsed
                            let b = colorSum[2] / samplesUsed
                            let a = colorSum[3] / samplesUsed

                            // 创建平均颜色并绘制
                            let finalColor = Color(.sRGB,
                                                red: Double(r),
                                                green: Double(g),
                                                blue: Double(b),
                                                opacity: Double(a))

                            context.fill(Path(CGRect(x: x, y: y, width: step, height: step)), with: .color(finalColor))
                        }
                    }
                }
            }
            .frame(width: size.width, height: size.height)
            .clipShape(Circle())
            .drawingGroup() // 启用离屏渲染，提高抗锯齿质量
        }
    }
}

// MARK: - 专用曝光补偿滑块组件

/// 专门用于曝光补偿的滑块组件，显示EV档位
struct ExposureSlider: View {
    let title: String
    @Binding var value: Double

    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        VStack(spacing: screenHeight * 0.01) {
            HStack {
                Text(title)
                    .font(.system(size: screenHeight * 0.015))
                    .foregroundColor(.white)
                Spacer()
                // 显示EV档位格式
                Text(formatExposureValue(value))
                    .font(.system(size: screenHeight * 0.015))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, screenWidth * 0.04)

            CustomSlider(value: $value, range: -5.0...5.0, step: 0.1)
        }
    }

    /// 格式化曝光值为EV档位显示
    private func formatExposureValue(_ value: Double) -> String {
        if value == 0.0 {
            return "0 EV"
        } else if value > 0 {
            return String(format: "+%.1f EV", value)
        } else {
            return String(format: "%.1f EV", value)
        }
    }
}
    var body: some View {
        GeometryReader { geometry in
            CurveCanvasView(adjustViewModel: adjustViewModel, geometry: geometry)
        }
    }
}

// MARK: - 曲线画布视图
private struct CurveCanvasView: View {
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored
    let geometry: GeometryProxy
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        let curveAreaWidth = geometry.size.width
        let curveAreaHeight = geometry.size.height

        ZStack {
            CurveGridView(width: curveAreaWidth, height: curveAreaHeight)
            CurvePathsView(adjustViewModel: adjustViewModel, geometry: geometry)
            CurveControlPointsView(adjustViewModel: adjustViewModel, geometry: geometry)
        }
    }
}

// MARK: - 曲线网格视图
private struct CurveGridView: View {
    let width: CGFloat
    let height: CGFloat

    var body: some View {
        CurveGrid(width: width, height: height)
    }
}

// MARK: - 曲线路径视图
private struct CurvePathsView: View {
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored
    let geometry: GeometryProxy

    var body: some View {
        ZStack {
            // 绘制其他通道的曲线
            let otherChannels = CurveChannel.allCases.filter { $0 != adjustViewModel.selectedChannel }
            ForEach(otherChannels, id: \.self) { channel in
                if adjustViewModel.curvePoints[channel]?.count ?? 0 > 1 {
                    // drawCurvePath(for: channel, in: geometry, isSelected: false)
                    EmptyView() // 临时占位符
                }
            }

            // 绘制选中通道的曲线
            // drawCurvePath(for: adjustViewModel.selectedChannel, in: geometry, isSelected: true)
            EmptyView() // 临时占位符
        }
    }
}

// MARK: - 曲线控制点视图
private struct CurveControlPointsView: View {
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored
    let geometry: GeometryProxy
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        let selectedChannelPoints = adjustViewModel.curvePoints[adjustViewModel.selectedChannel] ?? []
        let enumeratedPoints = Array(selectedChannelPoints.enumerated())

        ForEach(enumeratedPoints, id: \.offset) { index, point in
            CurveControlPointView(
                index: index,
                point: point,
                adjustViewModel: adjustViewModel,
                geometry: geometry
            )
        }
    }
}

// MARK: - 单个控制点视图
private struct CurveControlPointView: View {
    let index: Int
    let point: CGPoint
    @ObservedObject var adjustViewModel: AdjustViewModelRefactored
    let geometry: GeometryProxy
    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        Circle()
            .fill(curveColor(adjustViewModel.selectedChannel))
            .frame(width: screenHeight * 0.02, height: screenHeight * 0.02)
            .overlay(Circle().stroke(Color.black.opacity(0.5), lineWidth: 1))
            .position(x: point.x * geometry.size.width,
                      y: (1.0 - point.y) * geometry.size.height)
            .gesture(createPointGesture())
    }

    private func curveColor(_ channel: CurveChannel) -> Color {
        switch channel {
        case .rgb: return .white
        case .red: return .red
        case .green: return .green
        case .blue: return .blue
        }
    }

    private func createPointGesture() -> some Gesture {
        TapGesture(count: 2)
            .onEnded { _ in
                adjustViewModel.removePoint(at: index)
            }
            .simultaneously(with:
                DragGesture(minimumDistance: 3)
                    .onChanged { value in
                        let rawX = value.location.x / geometry.size.width
                        let rawY = 1.0 - (value.location.y / geometry.size.height)
                        let clampedX = max(0.0, min(1.0, rawX))
                        let clampedY = max(0.0, min(1.0, rawY))
                        let normalizedPoint = CGPoint(x: clampedX, y: clampedY)
                        adjustViewModel.updatePointPosition(index: index, normalizedPoint: normalizedPoint)
                    }
            )
    }
}
