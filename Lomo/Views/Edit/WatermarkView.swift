import SwiftUI
import UIKit

// MARK: - 水印视图
/// 水印视图 - 渐进式重构的第4步
/// **重要：这是一个测试版本，不会替换现有的WatermarkControlView**
/// **目标：验证ViewModel状态迁移的可行性**
struct WatermarkView: View {
    
    // MARK: - 依赖注入
    @StateObject private var viewModel: WatermarkViewModel
    
    // MARK: - 绑定属性
    @Binding var isKeyboardVisible: Bool
    
    // MARK: - 属性
    private var previewContainer: UIView?
    
    // MARK: - 初始化
    /// 初始化水印视图
    /// - Parameters:
    ///   - isKeyboardVisible: 键盘可见性绑定
    ///   - previewContainer: 预览容器视图
    ///   - viewModel: 水印视图模型
    init(
        isKeyboardVisible: Binding<Bool>,
        previewContainer: UIView? = nil,
        viewModel: WatermarkViewModel
    ) {
        self._isKeyboardVisible = isKeyboardVisible
        self.previewContainer = previewContainer
        self._viewModel = StateObject(wrappedValue: viewModel)
        
        print("🏭 [WatermarkView] 初始化（渐进式重构 - 第4步）")
    }
    
    // MARK: - Body
    var body: some View {
        VStack(spacing: 0) {
            // 水印分类选择栏
            // 迁移自：WatermarkControlView的分类选择HStack
            categorySelectionBar

            // 水印预览网格
            // 迁移自：WatermarkControlView的PreviewScrollView
            watermarkPreviewGrid

            // 水印选项列表
            // 迁移自：WatermarkControlView的选项列表ScrollView
            watermarkOptionsPanel
        }
        .onAppear {
            setupInitialState()
        }
        .onChange(of: viewModel.selectedWatermarkType) { _, newType in
            handleWatermarkTypeChange(newType)
        }
        .alert("错误", isPresented: $viewModel.showError) {
            Button("确定") { }
        } message: {
            Text(viewModel.errorMessage)
        }
    }

    // MARK: - 子视图

    /// 分类选择栏
    private var categorySelectionBar: some View {
        WatermarkCategoryBarView(
            selectedCategory: viewModel.selectedWatermarkCategory,
            onCategorySelected: viewModel.selectCategory
        )
    }

    /// 水印预览网格
    private var watermarkPreviewGrid: some View {
        WatermarkStyleGridView(
            watermarkIndices: viewModel.filteredWatermarkIndices,
            selectedType: viewModel.selectedWatermarkType,
            onWatermarkSelected: { index, container in
                viewModel.selectWatermark(at: index, container: container)
            }
        )
    }

    /// 水印选项面板
    private var watermarkOptionsPanel: some View {
        WatermarkOptionsPanelView(
            settings: viewModel.currentSettings,
            isKeyboardVisible: $isKeyboardVisible,
            onTextChanged: viewModel.updateWatermarkText,
            onTextToggled: viewModel.toggleWatermarkText
        )
    }

    // MARK: - 私有方法

    /// 设置初始状态
    /// 迁移自：WatermarkControlView的onAppear逻辑
    private func setupInitialState() {
        print("📱 [WatermarkView] 设置初始状态")
        print("   - 当前水印类型: \(viewModel.selectedWatermarkType)")
        print("   - 当前水印分类: \(viewModel.selectedWatermarkCategory)")

        // 刷新当前设置
        viewModel.refreshCurrentSettings()
    }

    /// 处理水印类型变更
    /// 迁移自：WatermarkControlView的onChange逻辑
    private func handleWatermarkTypeChange(_ newType: String) {
        print("🎨 [WatermarkView] 水印类型变更: \(newType)")

        // 确保水印在当前分类中可见
        viewModel.updateFilteredWatermarks()

        // 应用水印到预览容器
        if let container = previewContainer {
            viewModel.applyWatermark(to: container)
        }
    }
}

// MARK: - 预览
#Preview {
    let service = WatermarkService()
    let viewModel = WatermarkViewModel(watermarkService: service)
    return WatermarkView(
        isKeyboardVisible: .constant(false),
        viewModel: viewModel
    )
}
