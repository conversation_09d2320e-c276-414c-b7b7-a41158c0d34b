import SwiftUI

// MARK: - 水印分类选择栏
/// 水印分类选择栏组件 - 第6步UI组件拆分
/// **重要：这是一个新的独立组件，不会影响原始的WatermarkControlView**
/// **目标：提供可复用的分类选择UI组件**
struct WatermarkCategoryBarView: View {
    
    // MARK: - 属性
    let selectedCategory: String
    let onCategorySelected: (String) -> Void
    
    // MARK: - 私有属性
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 分类列表
    private let categories = ["经典", "无框", "简约", "大师", "大疆", "节日", "拼图"]
    
    // MARK: - Body
    var body: some View {
        HStack {
            HStack(spacing: screenWidth * 0.04) {
                ForEach(categories, id: \.self) { category in
                    CategoryBarButton(
                        title: category,
                        isSelected: selectedCategory == category
                    ) {
                        onCategorySelected(category)
                    }
                }
            }
            Spacer()
        }
        .padding(.horizontal, screenWidth * 0.04)
        .padding(.vertical, screenHeight * 0.015)
        .background(Color.black.opacity(0.1))
    }
}

// MARK: - 内部分类按钮组件
/// 内部分类按钮组件
/// **注意：为了避免与原始WatermarkCategoryButton冲突，使用不同的名称**
private struct CategoryBarButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    private let screenHeight = UIScreen.main.bounds.height

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(isSelected ? .white : .gray)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(isSelected ? Color.blue : Color.clear)
                )
        }
    }
}

// MARK: - 预览
#Preview {
    WatermarkCategoryBarView(
        selectedCategory: "经典",
        onCategorySelected: { category in
            print("选择分类: \(category)")
        }
    )
    .background(Color.black)
}
