import SwiftUI

// MARK: - 水印样式网格视图
/// 水印样式网格视图组件 - 第6步UI组件拆分
/// **重要：这是一个新的独立组件，不会影响原始的WatermarkControlView**
/// **目标：提供可复用的水印预览网格UI组件**
struct WatermarkStyleGridView: View {
    
    // MARK: - 属性
    let watermarkIndices: [Int]
    let selectedType: String
    let onWatermarkSelected: (Int, UIView) -> Void
    
    // MARK: - 私有属性
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // MARK: - Body
    var body: some View {
        // 使用现有的PreviewScrollView组件，保持一致性
        PreviewScrollView(
            itemCount: watermarkIndices.count,
            onItemTap: { index in
                print("🎨 [WatermarkStyleGridView] 选择水印索引: \(index)")
                
                // 创建一个临时的UIView作为容器（实际使用时会传入真实的预览容器）
                let tempContainer = UIView()
                onWatermarkSelected(index, tempContainer)
            }
        )
        .frame(height: screenHeight * 0.15) // 设置固定高度
        .background(Color.black.opacity(0.05))
    }
}

// MARK: - 水印预览项
/// 水印预览项组件
/// **注意：这是一个简化版本，实际的PreviewScrollView在原始代码中已经实现**
private struct WatermarkPreviewItem: View {
    let index: Int
    let isSelected: Bool
    let onTap: () -> Void
    
    private let screenWidth = UIScreen.main.bounds.width
    
    var body: some View {
        Button(action: onTap) {
            RoundedRectangle(cornerRadius: 8)
                .fill(isSelected ? Color.blue : Color.gray.opacity(0.3))
                .frame(width: screenWidth * 0.2, height: screenWidth * 0.2)
                .overlay(
                    Text("\(index)")
                        .font(.caption)
                        .foregroundColor(.white)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.white : Color.clear, lineWidth: 2)
                )
        }
    }
}

// MARK: - 预览
#Preview {
    WatermarkStyleGridView(
        watermarkIndices: Array(0...10),
        selectedType: "custom1",
        onWatermarkSelected: { index, container in
            print("选择水印: \(index)")
        }
    )
    .background(Color.black)
}
