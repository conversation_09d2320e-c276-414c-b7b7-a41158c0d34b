import SwiftUI
import UIKit

// Color的hex初始化扩展
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

// 在 WatermarkControlView 结构体之前添加选项配置结构
struct WatermarkOptionConfig {
    let title: String
    let type: WatermarkOptionType
    let isVisible: (String) -> Bool  // 根据水印类型判断是否显示
}

enum WatermarkOptionType {
    case logo
    case text
    case text2 // 为自定义水印13添加的描述选项
    case signature
    case preference
    case position
    case textColor
    case textFontSize
    case fontWeight
    case signatureFontWeight
    case englishFontWeight  // 新增：英文字体粗细选项类型
    case signatureFontSize
    case font
    case englishFont  // 新增：英文字体选项类型
    case borderColor
    case wideBorderPosition
    case borderThickness
    case wideBorderThickness
    case logoColor
    case logoSize  // 新增：Logo大小选项类型
    case topBottomBorderThickness
    case leftRightBorderThickness // 新增：左右边框粗细选项类型
    case blurBorder
    case shadowEffect
    case preferenceScale
}

// 水印类别按钮组件
struct WatermarkCategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
        }
    }
}

// 修改水印控制视图组件
struct WatermarkControlView: View {
    // 添加键盘显示状态绑定
    @Binding var isKeyboardVisible: Bool

    // 添加对预览容器的引用
    var previewContainer: UIView? = nil

    // MARK: - 真正重构：添加Service实例
    /// 水印服务实例 - 真正的SwiftData操作，不依赖Manager
    /// **这是真正重构的核心：逐步替换Manager.shared调用**
    private let watermarkService = WatermarkService()
    
    // 水印样式类型状态
    @State private var selectedWatermarkType: String = "none" // 初始为无水印
    
    // 添加水印分类选中状态
    @State private var selectedWatermarkCategory: String = "经典" // 默认选中经典分类
    
    // 添加过滤状态变量
    @State private var filteredWatermarkIndices: [Int] = Array(0...18) // 默认显示所有水印，包括水印18
    
    // 类别与水印索引的映射
    private let categoryWatermarkMap: [String: [Int]] = [
        "经典": [0, 2, 4, 9, 10, 11, 12, 13, 14, 15, 16, 25], // 包含"不启用"选项(0)，添加自定义水印25
        "简约": [0, 1, 18],
        "无框": [0, 3, 7, 8, 17],
        "大师": [0, 5, 6],
        "大疆": [0, 19, 20, 21, 22], // 添加自定义水印19、20、21和22
        "节日": [0], // 暂时只有不启用选项
        "拼图": [0, 23, 24]  // 添加自定义水印23和24
    ]
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 添加辅助方法：将过滤索引映射到实际水印索引
    private func getActualWatermarkIndex(for filteredIndex: Int) -> Int {
        if filteredIndex >= 0 && filteredIndex < filteredWatermarkIndices.count {
            return filteredWatermarkIndices[filteredIndex]
        }
        return 0 // 默认返回不启用水印选项
    }
    
    // 添加辅助方法：获取水印类型对应的索引
    private func getIndexForWatermarkType(_ type: String) -> Int {
        switch type {
        case "none": return 0
        case "border_2percent": return 1
        case "polaroid": return 2
        case "film": return 3
        case "custom4": return 4
        case "custom5": return 5
        case "custom6": return 6
        case "custom7": return 7
        case "custom8": return 8
        case "custom9": return 9
        case "custom10": return 10
        case "custom11": return 11
        case "custom12": return 12
        case "custom13": return 13
        case "custom14": return 14
        case "custom15": return 15
        case "custom16": return 16
        case "custom17": return 17
        case "custom18": return 18
        case "custom19": return 19
        case "custom20": return 20
        case "custom21": return 21
        case "custom22": return 22
        case "custom23": return 23
        case "custom24": return 24
        case "custom25": return 25
        default: return 0
        }
    }
    
    // 标准间距常量 - 6%屏幕宽度
    private let standardSpacing: CGFloat = UIScreen.main.bounds.width * 0.06
    
    // 更新：边框颜色选择相关状态和数据
    @State private var selectedBorderColorIndex: Int = 0 // 0 for white, 1 for dark gray
    private let availableBorderColors: [Color] = [
        .white,                // 1. 白色
        .black,                // 2. 黑色（显示用）
        Color(hex: "#535353"), // 3. 灰色
        Color(hex: "#000000"), // 4. 纯黑色
        Color(hex: "#F79329"), // 5. 橙色
        Color(hex: "#e6524f"), // 6. 红色
        Color(hex: "#fbc333"), // 7. 黄色
        Color(hex: "#74c15d"), // 8. 绿色
        Color(hex: "#25ade2"), // 9. 蓝色
        Color(hex: "#a756a1")  // 10. 紫色
    ] // 将 #191a1b 改为 .black 提高可见性
    // 实际应用的边框颜色
    private let actualBorderColors: [Color] = [
        .white,                // 1. 白色
        Color(hex: "#191a1b"), // 2. 黑色（实际使用）
        Color(hex: "#535353"), // 3. 灰色
        Color(hex: "#000000"), // 4. 纯黑色
        Color(hex: "#F79329"), // 5. 橙色
        Color(hex: "#e6524f"), // 6. 红色
        Color(hex: "#fbc333"), // 7. 黄色
        Color(hex: "#74c15d"), // 8. 绿色
        Color(hex: "#25ade2"), // 9. 蓝色
        Color(hex: "#a756a1")  // 10. 紫色
    ] // 实际使用的颜色
    private let borderColorCircleSize: CGFloat // 与ColorSelector一致的circleSize
    
    // 新增：字体颜色选择相关状态和数据
    @State private var selectedFontColorIndex: Int = 1 // 0 for white, 1 for black (默认黑色)
    private let availableFontColors: [Color] = [.white, .black]
    
    // 新增：Logo颜色选择相关状态和数据
    @State private var selectedLogoColorIndex: Int = 2 // 0表示不使用颜色调整, 1为白色, 2为黑色 (默认黑色)
    private let availableLogoColors: [Color] = [Color.gray.opacity(0.5), .white, .black] // 添加灰色表示不使用颜色调整
    
    // 新增：字体选择相关状态和数据
    @State private var selectedFontName: String = "PingFang-SC" // 默认字体改为PingFang-SC
    private let availableFonts: [String] = ["PingFang-SC", "HarmonyOS_Sans_SC", "SourceHanSansSC", "HONORSansCN"] // 移除Makinas字体
    
    // 新增：英文字体选择相关状态和数据
    @State private var selectedEnglishFontName: String = "" // 默认英文字体为空，表示使用系统字体
    private let availableEnglishFonts: [String] = ["FuturaPT", "TYPOGRAPH PRO", "CourierPrimeCode", "Miamo", "ADELE", "Quantico", "Syntax", "Makinas-Square", "Makinas-Flat"] // 添加Makinas字体到英文字体选项
    
    // 新增：字体粗细滑块相关状态 - 改为预设选项
    @State private var selectedFontWeight: String = "常规"
    // 更新为字体与粗细选项的映射
    private var availableFontWeights: [String] {
        if selectedFontName == "HarmonyOS_Sans_SC" {
            return ["Thin", "Light", "Regular", "Medium", "Bold", "Black"] // HarmonyOS_Sans_SC的六种粗细
        } else if selectedFontName == "PingFang-SC" {
            return ["Ultralight", "Thin", "Light", "Regular", "Medium", "Semibold"] // PingFang-SC的六种粗细
        } else if selectedFontName == "SourceHanSansSC" {
            return ["ExtraLight", "Light", "Normal", "Regular", "Medium", "Bold", "Heavy"] // 思源黑体的七种粗细
        } else if selectedFontName == "HONORSansCN" {
            return ["Thin", "ExtraLight", "Light", "Regular", "Medium", "DemiBold", "Bold", "ExtraBold", "Heavy"] // HONORSansCN的九种粗细
        } else {
            return ["Ultralight", "Thin", "Light", "Regular", "Medium", "Semibold"] // 修改系统字体的粗细选项，与PingFang-SC保持一致
        }
    }
    // 移除数值滑块，直接使用字符串表示字体粗细
    
    // 新增：署名字体粗细滑块相关状态
    @State private var selectedSignatureFontWeight: String = "常规"
    // 更新为字体与粗细选项的映射(与字体粗细保持一致)
    private var availableSignatureFontWeights: [String] {
        if selectedFontName == "HarmonyOS_Sans_SC" {
            return ["Thin", "Light", "Regular", "Medium", "Bold", "Black"] // HarmonyOS_Sans_SC的六种粗细
        } else if selectedFontName == "PingFang-SC" {
            return ["Ultralight", "Thin", "Light", "Regular", "Medium", "Semibold"] // PingFang-SC的六种粗细
        } else if selectedFontName == "SourceHanSansSC" {
            return ["ExtraLight", "Light", "Normal", "Regular", "Medium", "Bold", "Heavy"] // 思源黑体的七种粗细
        } else if selectedFontName == "HONORSansCN" {
            return ["Thin", "ExtraLight", "Light", "Regular", "Medium", "DemiBold", "Bold", "ExtraBold", "Heavy"] // HONORSansCN的九种粗细
        } else {
            return ["Ultralight", "Thin", "Light", "Regular", "Medium", "Semibold"] // 修改系统字体的粗细选项，与PingFang-SC保持一致
        }
    }
    // 移除数值滑块，直接使用字符串表示署名字体粗细
    
    // 新增：边框粗细滑块相关状态
    @State private var borderThicknessSliderValue: Double = 0.2 // 默认值与WatermarkSettings一致
    
    // 新增：宽边框粗细滑块相关状态
    @State private var wideBorderThicknessSliderValue: Double = 0.5 // 默认为中间值50%
    
    // 新增：上下边框粗细滑块相关状态
    @State private var topBottomBorderThicknessSliderValue: Double = 0.2 // 默认值与WatermarkSettings一致
    
    // 新增：左右边框粗细滑块相关状态
    @State private var leftRightBorderThicknessSliderValue: Double = 0.2 // 默认值与WatermarkSettings一致
    
    // 新增：模糊边框相关状态
    @State private var isBlurBorderEnabled: Bool = false
    @State private var blurIntensity: Double = 0.5
    @State private var blurStyle: String = "regular" // 新增：用于存储选中的模糊样式
    
    // 新增：阴影效果相关状态
    @State private var isShadowEnabled: Bool = false
    
    // 新增：偏好选项缩放比例滑块
    @State private var preferenceScaleFactorSliderValue: Double = 1.0
    
    // 新增：署名大小滑块相关状态
    @State private var signatureFontSizeSliderValue: Double = 1.0 // 默认值为1.0 (100%)
    
    // 新增：文字大小滑块相关状态
    @State private var textFontSizeSliderValue: Double = 1.0 // 默认值为1.0 (100%)
    
    // 新增：Logo大小滑块相关状态
    @State private var logoSizeSliderValue: Double = 1.0 // 默认值为1.0 (100%)
    
    // 新增：水印6等宽边框模式开关
    @State private var isEqualWidthBorderEnabled: Bool = false
    
    // 新增：水印5右侧宽边框开关
    @State private var isRightWideBorderEnabled: Bool = false
    
    // 新增：英文字体粗细滑块相关状态
    @State private var selectedEnglishFontWeight: String = "Regular"
    // 更新为英文字体与粗细选项的映射
    private var availableEnglishFontWeights: [String] {
        if selectedEnglishFontName == "FuturaPT" {
            return ["Light", "Book", "Medium", "Demi", "Light Obl", "Book Obl", "Medium Obl", "Demi Obl"] // FuturaPT的八种变体，包括正常和斜体(Oblique)
        } else if selectedEnglishFontName == "TYPOGRAPH PRO" {
            return ["Ultra Light", "Light", "Semi Bold", "Extra Bold", "Ultra Light Italic", "Light Italic", "Semi Bold Italic"] // TYPOGRAPH PRO的七种变体
        } else if selectedEnglishFontName == "CourierPrimeCode" {
            return ["Regular", "Italic"] // CourierPrimeCode的两种变体
        } else if selectedEnglishFontName == "Miamo" {
            return ["Thin", "Light", "Regular", "Medium", "SemiBold", "Bold"] // Miamo的六种变体
        } else if selectedEnglishFontName == "ADELE" {
            return ["Light"] // ADELE字体目前只有Light变体
        } else if selectedEnglishFontName == "Quantico" {
            return ["Regular", "Bold", "Italic", "Bold Italic"] // Quantico的四种变体
        } else if selectedEnglishFontName == "Syntax" {
            return ["Roman", "Bold", "Italic", "Black", "UltraBlack"] // Syntax的五种变体
        } else if selectedEnglishFontName == "Makinas-Flat" || selectedEnglishFontName == "Makinas-Square" {
            return ["Regular"] // Makinas字体只有Regular粗细
        } else {
            return ["Light", "Regular", "Medium", "Semibold"] // 默认SF字体的粗细选项
        }
    }
    // 移除数值滑块，直接使用字符串表示英文字体粗细
    
    // 添加统一的选项配置
    private let watermarkOptions: [WatermarkOptionConfig] = [
        WatermarkOptionConfig(title: "Logo", type: .logo) { type in
            // 保留Logo选项
            return type != "none" && type != "custom8"
        },
        WatermarkOptionConfig(title: "Logo颜色", type: .logoColor) { type in
            // 保留Logo颜色选项
            return type == "polaroid" || type == "film" || type == "custom17" || type == "custom22" || type == "custom7" || type == "custom4" || type == "custom5" || type == "custom6" || type == "custom10" || type == "custom18" || type == "custom19" || (type != "none" && type != "custom8")
        },
        WatermarkOptionConfig(title: "Logo大小", type: .logoSize) { type in
            // 保留Logo大小选项
            return type == "polaroid" || type == "film" || type == "custom17" || type == "custom22" || type == "custom7" || type == "custom4" || type == "custom5" || type == "custom6" || type == "custom10" || type == "custom18" || type == "custom19" || (type != "none" && type != "custom8")
        },
        WatermarkOptionConfig(title: "署名", type: .signature) { type in
            // 移除自定义水印22的署名选项
            return type == "polaroid" || type == "custom10" || type == "custom6" || type == "custom8" || type == "custom9" || type == "custom5" || type == "custom11" || type == "custom12" || type == "custom15" || type == "custom18" || (type != "none" && type != "custom4" && type != "film" && type != "custom17" && type != "custom7" && type != "custom19" && type != "custom20" && type != "custom21" && type != "custom22")
        },
        WatermarkOptionConfig(title: "文字", type: .text) { type in
            // Hide for polaroid, custom10, custom16, custom15, custom25 as Signature will be used
            // Show for custom19, custom21 to enable text for first row layout
            return (type != "none" && type != "polaroid" && type != "custom10" && type != "custom16" && type != "custom15" && type != "custom20" && type != "custom25") || type == "custom19" || type == "custom21" // 移除 custom20 和 custom22
        },
        WatermarkOptionConfig(title: "描述", type: .text2) { type in
            // 仅在支持描述的水印类型中显示，且只有当启用了3个常规元素时显示
            if type == "custom13" || type == "custom14" {
                let settings = WatermarkSettingsManager.shared.getSettings()
                let hasLogo = !settings.selectedLogo.isEmpty
                let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                
                // 统计已启用的常规元素数量
                var enabledCount = 0
                if hasLogo { enabledCount += 1 }
                if hasSignature { enabledCount += 1 }
                if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素
                
                return enabledCount == 3
            }
            return false
        },
        WatermarkOptionConfig(title: "偏好", type: .preference) { type in
            // 移除自定义水印22的偏好选项
            return type != "none" && type != "custom17" && type != "custom22"
        },
        WatermarkOptionConfig(title: "偏好大小", type: .preferenceScale) { type in
            // 移除自定义水印22的偏好大小选项
            return (type == "custom10" || type == "custom16" || type == "custom25") && (WatermarkSettingsManager.shared.getSettings().selectedPreferences.contains("参数") || WatermarkSettingsManager.shared.getSettings().preferenceOption == "参数") && type != "custom22"
        },
        WatermarkOptionConfig(title: "文字颜色", type: .textColor) { type in
            // 移除自定义水印22的文字颜色选项
            return type != "none" && type != "custom22"
        },
        WatermarkOptionConfig(title: "文字大小", type: .textFontSize) { type in
            // 移除自定义水印22的文字大小选项
            return (type != "none" && type != "polaroid" && type != "custom10" && type != "custom16" && type != "custom15" && type != "custom22") || type == "custom19" || type == "custom20" || type == "custom21"
        },
        WatermarkOptionConfig(title: "文字粗细", type: .fontWeight) { type in
            // 移除自定义水印22的文字粗细选项
            return type != "none" && type != "custom22"
        },
        WatermarkOptionConfig(title: "署名粗细", type: .signatureFontWeight) { type in
            // 移除自定义水印22的署名粗细选项
            return type == "polaroid" || type == "custom10" || type == "custom6" || type == "custom8" || type == "custom9" || type == "custom5" || type == "custom11" || type == "custom12" || type == "custom15" || type == "custom18" || (type != "none" && type != "custom4" && type != "film" && type != "custom17" && type != "custom7" && type != "custom19" && type != "custom20" && type != "custom21" && type != "custom22")
        },
        WatermarkOptionConfig(title: "英文粗细", type: .englishFontWeight) { type in
            // 移除自定义水印22的英文粗细选项
            return type != "none" && type != "custom22"
        },
        WatermarkOptionConfig(title: "署名大小", type: .signatureFontSize) { type in
            // 移除自定义水印22的署名大小选项
            return type == "polaroid" || type == "custom10" || type == "custom6" || type == "custom8" || type == "custom9" || type == "custom5" || type == "custom11" || type == "custom12" || type == "custom15" || type == "custom18" || (type != "none" && type != "custom4" && type != "film" && type != "custom17" && type != "custom7" && type != "custom19" && type != "custom20" && type != "custom21" && type != "custom22")
        },
        WatermarkOptionConfig(title: "字体", type: .font) { type in
            // 移除自定义水印22的字体选项
            return type != "none" && type != "custom22"
        },
        WatermarkOptionConfig(title: "英文字体", type: .englishFont) { type in
            // 移除自定义水印22的英文字体选项
            return type != "none" && type != "custom22"
        },
        WatermarkOptionConfig(title: "位置", type: .position) { type in
            // 保留自定义水印22的位置选项
            return type == "film" || type == "custom17" || type == "custom22" || type == "custom7" || type == "custom8" || (type != "none" && type != "polaroid" && type != "custom4" && type != "custom5" && type != "custom6" && type != "border_2percent" && type != "custom9" && type != "custom10" && type != "custom16" && type != "custom11" && type != "custom12" && type != "custom13" && type != "custom14" && type != "custom15" && type != "custom18" && type != "custom19" && type != "custom20" && type != "custom21" && type != "custom25")
        },
        WatermarkOptionConfig(title: "边框颜色", type: .borderColor) { type in
            // 移除自定义水印22的边框颜色选项
            return type == "border_2percent" || type == "polaroid" || type == "custom4" || type == "custom5" || type == "custom6" || type == "custom9" || type == "custom10" || type == "custom11" || type == "custom12" || type == "custom15" || type == "custom19" || (type != "none" && type != "film" && type != "custom17" && type != "custom7" && type != "custom8" && type != "custom22")
        },
        // 新增宽边框位置选项，放在边框颜色后面
        WatermarkOptionConfig(title: "边框位置", type: .wideBorderPosition) { type in
            // 移除自定义水印22的边框位置选项
            return (type == "custom5" || type == "custom10" || type == "custom16") && type != "custom22"
        },
        WatermarkOptionConfig(title: "边框粗细", type: .borderThickness) { type in
            // 移除自定义水印22的边框粗细选项
            return type == "border_2percent" || type == "polaroid" || type == "custom4" || type == "custom5" || type == "custom6" || type == "custom9" || type == "custom10" || type == "custom11" || type == "custom12" || type == "custom15" || type == "custom19" || (type != "none" && type != "film" && type != "custom17" && type != "custom7" && type != "custom8" && type != "custom22")
        },
        WatermarkOptionConfig(title: "宽边框粗细", type: .wideBorderThickness) { type in
            // 移除自定义水印22的宽边框粗细选项
            return (type == "custom5" || type == "custom10" || type == "polaroid" || type == "custom9" || type == "custom11" || type == "custom12" || type == "custom4" || type == "custom13" || type == "custom14" || type == "custom15" || type == "custom16" || type == "custom19" || type == "custom25") && type != "custom22"
        },
        WatermarkOptionConfig(title: "上下边框粗细", type: .topBottomBorderThickness) { type in
            // 移除自定义水印22的上下边框粗细选项
            return (type == "border_2percent" || type == "custom1") && type != "custom22"
        },
        WatermarkOptionConfig(title: "左右边框粗细", type: .leftRightBorderThickness) { type in
            // 移除自定义水印22的左右边框粗细选项
            return type == "custom18" && type != "custom22"
        },
        WatermarkOptionConfig(title: "模糊边框", type: .blurBorder) { type in
            // 移除自定义水印22的模糊边框选项
            return type != "none" && type != "film" && type != "custom17" && type != "custom22" && type != "custom7" && type != "custom8"
        },
        WatermarkOptionConfig(title: "阴影效果", type: .shadowEffect) { type in
            // 移除自定义水印22、自定义水印23和自定义水印24的阴影效果选项
            return type != "none" && type != "film" && type != "custom17" && type != "custom22" && type != "custom7" && type != "custom8" && type != "custom23" && type != "custom24"
        }
    ]
    
    // 添加初始化方法
    init(isKeyboardVisible: Binding<Bool>, previewContainer: UIView? = nil) {
        self._isKeyboardVisible = isKeyboardVisible
        self.previewContainer = previewContainer
        self.borderColorCircleSize = UIScreen.main.bounds.height * 0.03
        
        // 从设置中读取并初始化状态
        // 🔄 真正重构：使用Service替代Manager（第1个替换）
        let settings = watermarkService.getSettings()
        
        // 初始化字体选择
        self._selectedFontName = State(initialValue: settings.selectedFontName)
        self._selectedEnglishFontName = State(initialValue: settings.selectedEnglishFontName)
        
        // 初始化边框颜色选择状态
        if settings.borderColorRed > 0.5 {
            // 如果红色通道 > 0.5，假设是白色
            self._selectedBorderColorIndex = State(initialValue: 0)
        } else {
            // 否则假设是黑色
            self._selectedBorderColorIndex = State(initialValue: 1)
        }
        
        // 初始化字体颜色选择状态
        if settings.fontColorRed > 0.5 {
            // 如果红色通道 > 0.5，假设是白色
            self._selectedFontColorIndex = State(initialValue: 0)
        } else {
            // 否则假设是黑色
            self._selectedFontColorIndex = State(initialValue: 1)
        }
        
        // 初始化Logo颜色选择状态
        let logoRed = settings.logoColorRed
        let logoGreen = settings.logoColorGreen
        let logoBlue = settings.logoColorBlue
        
        if abs(logoRed - 0.5) < 0.1 && abs(logoGreen - 0.5) < 0.1 && abs(logoBlue - 0.5) < 0.1 {
            // 如果RGB都接近0.5，表示是"不使用颜色调整"选项
            self._selectedLogoColorIndex = State(initialValue: 0)
        } else if logoRed > 0.5 {
            // 如果红色通道 > 0.5，假设是白色
            self._selectedLogoColorIndex = State(initialValue: 1)
        } else {
            // 否则假设是黑色
            self._selectedLogoColorIndex = State(initialValue: 2)
        }
        
        // 初始化字体粗细设置
        // 移除数值滑块初始化，直接使用字符串表示字体粗细
        
        // 优先使用保存的中文字体粗细名称
        if !settings.selectedFontWeight.isEmpty {
            self._selectedFontWeight = State(initialValue: settings.selectedFontWeight)
        } else {
            // 如果没有保存字体粗细名称，则根据字体类型设置默认值
            switch settings.selectedFontName {
            case "HarmonyOS_Sans_SC":
                self._selectedFontWeight = State(initialValue: "Regular")
            case "PingFang-SC":
                self._selectedFontWeight = State(initialValue: "Regular")
            case "SourceHanSansSC":
                self._selectedFontWeight = State(initialValue: "Regular")
            case "HONORSansCN":
                self._selectedFontWeight = State(initialValue: "Regular")
            case "Makinas-Flat", "Makinas-Square":
                self._selectedFontWeight = State(initialValue: "常规")
            default:
                self._selectedFontWeight = State(initialValue: "Regular")
            }
        }
        
        // 初始化署名字体粗细设置
        // 移除数值滑块初始化，直接使用字符串表示署名字体粗细
        
        // 优先使用保存的署名字体粗细名称
        if !settings.selectedSignatureFontWeight.isEmpty {
            self._selectedSignatureFontWeight = State(initialValue: settings.selectedSignatureFontWeight)
        } else {
            // 如果没有保存字体粗细名称，则根据字体类型设置默认值
            switch settings.selectedFontName {
            case "HarmonyOS_Sans_SC":
                self._selectedSignatureFontWeight = State(initialValue: "Regular")
            case "PingFang-SC":
                self._selectedSignatureFontWeight = State(initialValue: "Regular")
            case "SourceHanSansSC":
                self._selectedSignatureFontWeight = State(initialValue: "Regular")
            case "HONORSansCN":
                self._selectedSignatureFontWeight = State(initialValue: "Regular")
            case "Makinas-Flat", "Makinas-Square":
                self._selectedSignatureFontWeight = State(initialValue: "常规")
            default:
                self._selectedSignatureFontWeight = State(initialValue: "Regular")
            }
        }
        
        // 初始化边框粗细滑块值
        self._borderThicknessSliderValue = State(initialValue: settings.borderThicknessMultiplier)
        
        // 初始化宽边框粗细滑块值  
        self._wideBorderThicknessSliderValue = State(initialValue: settings.wideBorderThicknessMultiplier)
        
        // 初始化上下边框粗细滑块值
        self._topBottomBorderThicknessSliderValue = State(initialValue: settings.topBottomBorderThicknessMultiplier)
        
        // 初始化左右边框粗细滑块值
        self._leftRightBorderThicknessSliderValue = State(initialValue: settings.leftRightBorderThicknessMultiplier)
        
        // 初始化选中的水印类型
        self._selectedWatermarkType = State(initialValue: settings.activeWatermarkStyleType)
        
        // 初始化水印管理器
        if let container = previewContainer {
            WatermarkManagerProvider.shared.setupWatermarkManager(with: container)
        }
        
        // 初始化时，根据当前设置匹配selectedBorderColorIndex
        // 🔄 真正重构：使用Service替代Manager（第2个替换）
        let currentSettings = watermarkService.getSettings()

        // 新增：从持久化设置中初始化 selectedWatermarkType
        self._selectedWatermarkType = State(initialValue: currentSettings.activeWatermarkStyleType)

        // 初始化模糊边框相关状态
        self._isBlurBorderEnabled = State(initialValue: settings.isBlurBorderEnabled)
        self._blurIntensity = State(initialValue: settings.blurIntensity)
        self._blurStyle = State(initialValue: settings.blurStyle)

        // 初始化阴影效果状态
        self._isShadowEnabled = State(initialValue: settings.isShadowEnabled)
        
        // 初始化偏好选项缩放比例
        self._preferenceScaleFactorSliderValue = State(initialValue: settings.preferenceScaleFactor)

        // 初始化署名大小滑块值
        self._signatureFontSizeSliderValue = State(initialValue: settings.signatureFontSizeMultiplier) // <<<< INITIALIZE NEW STATE

        // 初始化文字大小滑块值
        self._textFontSizeSliderValue = State(initialValue: settings.textFontSizeMultiplier) // <<<< INITIALIZE NEW STATE

        // 初始化Logo大小滑块值
        self._logoSizeSliderValue = State(initialValue: settings.logoSizeMultiplier)

        // 初始化等宽边框模式开关
        self._isEqualWidthBorderEnabled = State(initialValue: settings.isEqualWidthBorderEnabled)

        let initialBorderUIColor = UIColor(
            red: CGFloat(currentSettings.borderColorRed),
            green: CGFloat(currentSettings.borderColorGreen),
            blue: CGFloat(currentSettings.borderColorBlue),
            alpha: CGFloat(currentSettings.borderColorAlpha)
        )
        
        // 检查边框颜色并匹配对应的索引（按新顺序）
        let colorMappings: [(UIColor, Int)] = [
            (UIColor.white, 0),                          // 1. 白色
            (UIColor(Color(hex: "#191a1b")), 1),         // 2. 黑色
            (UIColor(Color(hex: "#535353")), 2),         // 3. 灰色
            (UIColor(Color(hex: "#000000")), 3),         // 4. 纯黑色
            (UIColor(Color(hex: "#F79329")), 4),         // 5. 橙色
            (UIColor(Color(hex: "#e6524f")), 5),         // 6. 红色
            (UIColor(Color(hex: "#fbc333")), 6),         // 7. 黄色
            (UIColor(Color(hex: "#74c15d")), 7),         // 8. 绿色
            (UIColor(Color(hex: "#25ade2")), 8),         // 9. 蓝色
            (UIColor(Color(hex: "#a756a1")), 9)          // 10. 紫色
        ]

        var matchedIndex = 0 // 默认为白色
        for (color, index) in colorMappings {
            if initialBorderUIColor.isApproximatelyEqualTo(color) {
                matchedIndex = index
                break
            }
        }
        self._selectedBorderColorIndex = State(initialValue: matchedIndex)

        // 初始化边框粗细滑块值
        self._borderThicknessSliderValue = State(initialValue: currentSettings.borderThicknessMultiplier)

        // 初始化上下边框粗细滑块值
        self._topBottomBorderThicknessSliderValue = State(initialValue: currentSettings.topBottomBorderThicknessMultiplier)

        // 初始化左右边框粗细滑块值
        self._leftRightBorderThicknessSliderValue = State(initialValue: currentSettings.leftRightBorderThicknessMultiplier)

        // Phase 2b: 在初始化结束时，根据持久化的类型主动应用一次水印样式
        // 确保预览容器有效，且水印管理器已正确配置后再应用水印
        let watermarkType = self.selectedWatermarkType
        let container = self.previewContainer
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            // 延迟一点时间确保视图已完全加载
            if let container = container, container.bounds.width > 0, container.bounds.height > 0 {
                print("🔍 [WatermarkControlView] 延迟初始化水印 - 容器尺寸: \(container.bounds)")
                if WatermarkManagerProvider.shared.watermarkManager == nil {
                    // 确保水印管理器已设置
                    WatermarkManagerProvider.shared.setupWatermarkManager(with: container)
                }
                
                // 应用水印样式（会先移除当前样式）
                print("ℹ️ WatermarkControlView.init: 安全应用水印样式: \(watermarkType)")
                // 创建并应用水印样式
                // 获取当前水印设置
                let settings = WatermarkSettingsManager.shared.getSettings()
                
                // 确保水印管理器有效
                guard let watermarkManager = WatermarkManagerProvider.shared.watermarkManager,
                      container.bounds.width > 0, container.bounds.height > 0 // 确保视图尺寸有效
                else {
                    print("⚠️ WatermarkControlView.init: 水印管理器或预览容器无效，无法应用样式")
                    print("   - watermarkManager: \(WatermarkManagerProvider.shared.watermarkManager != nil ? "存在" : "nil")")
                    print("   - container.bounds: \(container.bounds)")
                    return
                }
                
                // 使用工厂创建水印样式
                guard let style = WatermarkStyleFactory.createWatermarkStyle(type: watermarkType, settings: settings) else {
                    print("ℹ️ WatermarkControlView.init: 移除水印，类型: \(watermarkType)")
                    watermarkManager.removeCurrentWatermark()
                    return
                }
                
                // 应用水印样式
                print("✅ WatermarkControlView.init: 应用水印样式，类型: \(watermarkType)")
                watermarkManager.applyWatermarkStyle(style)
            } else {
                print("⚠️ WatermarkControlView.init: 预览容器无效或未完全加载，暂不应用水印")
            }
        }
        
        // 初始化英文字体粗细设置
        // 移除数值滑块初始化，直接使用字符串表示英文字体粗细
        
        // 优先使用保存的英文字体粗细名称
        if !settings.selectedEnglishFontWeight.isEmpty {
            self._selectedEnglishFontWeight = State(initialValue: settings.selectedEnglishFontWeight)
        } else {
            // 如果没有保存字体粗细名称，则根据字体类型设置默认值
            switch settings.selectedEnglishFontName {
            case "FuturaPT":
                self._selectedEnglishFontWeight = State(initialValue: "Book")
            case "TYPOGRAPH PRO":
                self._selectedEnglishFontWeight = State(initialValue: "Light")
            case "CourierPrimeCode":
                self._selectedEnglishFontWeight = State(initialValue: "Regular")
            case "Miamo":
                self._selectedEnglishFontWeight = State(initialValue: "Regular")
            case "ADELE":
                self._selectedEnglishFontWeight = State(initialValue: "Light")
            case "Quantico":
                self._selectedEnglishFontWeight = State(initialValue: "Regular")
            case "Syntax":
                self._selectedEnglishFontWeight = State(initialValue: "Roman")
            default:
                self._selectedEnglishFontWeight = State(initialValue: "Regular")
            }
        }
    }
    
    // 应用水印样式
    private func applyWatermarkStyle(type: String) {
        // 更新选中的水印类型
        selectedWatermarkType = type
        
        // 获取当前水印设置
        let settings = WatermarkSettingsManager.shared.getSettings()
        
        // 确保水印管理器和预览容器有效
        guard let watermarkManager = WatermarkManagerProvider.shared.watermarkManager,
              let container = previewContainer,
              container.bounds.width > 0, container.bounds.height > 0 // 确保视图尺寸有效
        else {
            print("⚠️ WatermarkControlView.applyWatermarkStyle: 水印管理器或预览容器无效，无法应用样式")
            print("   - watermarkManager: \(WatermarkManagerProvider.shared.watermarkManager != nil ? "存在" : "nil")")
            print("   - previewContainer: \(previewContainer != nil ? "存在" : "nil")")
            if let container = previewContainer {
                print("   - container.bounds: \(container.bounds)")
                print("   - container.window: \(container.window != nil ? "存在" : "nil")")
            }
            return
        }
        
        // 使用工厂创建水印样式
        guard let style = WatermarkStyleFactory.createWatermarkStyle(type: type, settings: settings) else {
            print("ℹ️ WatermarkControlView.applyWatermarkStyle: 移除水印，类型: \(type)")
            watermarkManager.removeCurrentWatermark()
            return
        }
        
        // 应用水印样式
        print("✅ WatermarkControlView.applyWatermarkStyle: 应用水印样式，类型: \(type)")
        watermarkManager.applyWatermarkStyle(style)
    }
    
    // 移除水印
    private func removeWatermark() {
        selectedWatermarkType = "none"
        WatermarkManagerProvider.shared.watermarkManager?.removeCurrentWatermark()
    }

    // 新增：检查特定水印类型是否需要应用互斥逻辑
    private func shouldApplyMutualExclusionForType(_ type: String) -> Bool {
        // 显式排除custom10和custom16，允许它们同时显示文字和偏好
        if type == "custom10" || type == "custom16" {
            return false
        }
        return type == "custom4" || 
               type == "film" || 
               type == "custom7" || 
               type == "custom8" || 
               type == "custom9" || 
               type == "custom5" ||
               type == "custom11" ||
               type == "custom12" ||
               type == "custom13" ||
               type == "custom14" ||
               type == "border_2percent" ||
               type == "custom18" ||
               type == "custom19" || // 添加自定义水印19
               type == "custom20" || // 添加自定义水印20
               type == "custom21" || // 添加自定义水印21
               type == "custom22"    // 添加自定义水印22
    }

    // 新增：主动应用水印互斥逻辑到当前设置
    private func applyMutualExclusionForSettings() {
        var settings = WatermarkSettingsManager.shared.getSettings() // 获取可变副本

        let hasLogo = !settings.selectedLogo.isEmpty
        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        let hasPreference = settings.preferenceOption != "OFF" || !settings.selectedPreferences.isEmpty
        let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty

        // 对于所有互斥逻辑的水印，确保文字和偏好只能有一个开启
            if hasText && hasPreference {
                // 默认关闭偏好，保留文字（可以根据产品需求调整哪个优先）
            print("WatermarkControlView: 应用切换互斥 - 文字和偏好都开启，关闭偏好")
                WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: "OFF")
                WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: []) // 同时清空新格式的偏好数组
                NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
        }
        
        // 移除这部分特殊逻辑，因为我们现在通过UI配置控制描述选项的可见性
        
        // 重新获取最新的settings，因为可能已被修改
        settings = WatermarkSettingsManager.shared.getSettings()
        // 确保在应用新类型之前，设置是符合互斥规则的
        // （这一步主要是为了确保 WatermarkManager接收到的是正确的settings，尽管UI可能已通过通知更新）
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // 添加水印分类标题栏
            HStack {
                HStack(spacing: screenWidth * 0.04) {
                    WatermarkCategoryButton(title: "经典", isSelected: selectedWatermarkCategory == "经典") {
                        selectedWatermarkCategory = "经典"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["经典"] ?? Array(0...17)
                    }
                    WatermarkCategoryButton(title: "无框", isSelected: selectedWatermarkCategory == "无框") {
                        selectedWatermarkCategory = "无框"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["无框"] ?? Array(0...17)
                    }
                    WatermarkCategoryButton(title: "简约", isSelected: selectedWatermarkCategory == "简约") {
                        selectedWatermarkCategory = "简约"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["简约"] ?? Array(0...17)
                    }
                    WatermarkCategoryButton(title: "大师", isSelected: selectedWatermarkCategory == "大师") {
                        selectedWatermarkCategory = "大师"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["大师"] ?? Array(0...17)
                    }
                    WatermarkCategoryButton(title: "大疆", isSelected: selectedWatermarkCategory == "大疆") {
                        selectedWatermarkCategory = "大疆"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["大疆"] ?? [0]
                    }
                    WatermarkCategoryButton(title: "节日", isSelected: selectedWatermarkCategory == "节日") {
                        // 暂不实现功能，使用空数组，只显示不启用选项
                        selectedWatermarkCategory = "节日"
                        filteredWatermarkIndices = [0]
                    }
                    WatermarkCategoryButton(title: "拼图", isSelected: selectedWatermarkCategory == "拼图") {
                        selectedWatermarkCategory = "拼图"
                        // 应用过滤
                        filteredWatermarkIndices = categoryWatermarkMap["拼图"] ?? [0]
                    }
                }
                
                Spacer()
            }
            .padding(.horizontal, screenWidth * 0.04)
            .padding(.top, screenHeight * 0.01)
            .padding(.bottom, screenHeight * 0.0075)
            .onAppear {
                // 初始化时应用默认分类的过滤
                filteredWatermarkIndices = categoryWatermarkMap[selectedWatermarkCategory] ?? Array(0...17)
                
                // 如果有默认选中的水印类型，确保它在当前分类中可见
                if selectedWatermarkType != "none" {
                    let typeIndex = getIndexForWatermarkType(selectedWatermarkType)
                    if !filteredWatermarkIndices.contains(typeIndex) {
                        // 如果当前分类不包含该水印，找到包含它的分类并切换
                        for (category, indices) in categoryWatermarkMap {
                            if indices.contains(typeIndex) {
                                selectedWatermarkCategory = category
                                filteredWatermarkIndices = indices
                                break
                            }
                        }
                    }
                }
            }
            // 添加监听器，当水印类型变更时，确保它在当前分类中可见
            .onChange(of: selectedWatermarkType) { _, newType in
                if newType != "none" {
                    let typeIndex = getIndexForWatermarkType(newType)
                    if !filteredWatermarkIndices.contains(typeIndex) {
                        // 如果当前分类不包含该水印，找到包含它的分类并切换
                        for (category, indices) in categoryWatermarkMap {
                            if indices.contains(typeIndex) {
                                selectedWatermarkCategory = category
                                filteredWatermarkIndices = indices
                                break
                            }
                        }
                    }
                }
            }
            
            // 使用通用的预览框组件
            PreviewScrollView(itemCount: filteredWatermarkIndices.count, onItemTap: { index in
                print("预览项 \(index) 被点击")
                // 将过滤后的索引转换为实际的水印索引
                let actualIndex = filteredWatermarkIndices[index]
                
                guard let manager = WatermarkManagerProvider.shared.watermarkManager else {
                    print("❌ WatermarkManager 不可用")
                    return
                }
                
                var styleTypeToApply: String? = nil
                
                switch actualIndex {
                case 0: // 不启用水印
                    print("选择：不启用水印")
                    styleTypeToApply = "none"
                case 1: // 四周白色边框
                    print("选择：预览项1 - 白色边框水印")
                    styleTypeToApply = "border_2percent"
                case 2: // 拍立得风格
                    print("选择：预览项2 - 拍立得风格水印")
                    styleTypeToApply = "polaroid"
                case 3: // 胶片风格
                    print("选择：预览项3 - 胶片风格水印")
                    styleTypeToApply = "film"
                case 4: // 自定义水印4
                    print("选择：预览项4 - 自定义水印4")
                    styleTypeToApply = "custom4"
                case 5: // 自定义水印5
                    print("选择：预览项5 - 左侧宽边框水印")
                    styleTypeToApply = "custom5"
                case 6: // 自定义水印6
                    print("选择：预览项6 - 上下宽边框水印")
                    styleTypeToApply = "custom6"
                case 7: // 自定义水印7
                    print("选择：预览项7 - 自定义水印7")
                    styleTypeToApply = "custom7"
                case 8: // 自定义水印8
                    print("选择：预览项8 - 自定义水印8")
                    styleTypeToApply = "custom8"
                case 9: // 自定义水印9
                    print("选择：预览项9 - 自定义水印9")
                    styleTypeToApply = "custom9"
                case 10: // 自定义水印10
                    print("选择：预览项10 - 右侧宽边框水印")
                    styleTypeToApply = "custom10"
                case 11: // 自定义水印11
                    print("选择：预览项11 - 自定义水印11")
                    styleTypeToApply = "custom11"
                case 12: // 自定义水印12
                    print("选择：预览项12 - 自定义水印12")
                    styleTypeToApply = "custom12"
                case 13: // 自定义水印13
                    print("选择：预览项13 - 自定义水印13")
                    styleTypeToApply = "custom13"
                case 14: // 自定义水印14
                    print("选择：预览项14 - 自定义水印14")
                    styleTypeToApply = "custom14"
                case 15: // 自定义水印15
                    print("选择：预览项15 - 自定义水印15")
                    styleTypeToApply = "custom15"
                case 16: // 自定义水印16
                    print("选择：预览项16 - 自定义水印16")
                    styleTypeToApply = "custom16"
                case 17: // 自定义水印17
                    print("选择：预览项17 - 自定义水印17")
                    styleTypeToApply = "custom17"
                case 18: // 自定义水印18
                    print("选择：预览项18 - 自定义水印18")
                    styleTypeToApply = "custom18"
                case 19: // 自定义水印19
                    print("选择：预览项19 - 自定义水印19")
                    styleTypeToApply = "custom19"
                case 20: // 自定义水印20
                    print("选择：预览项20 - 自定义水印20")
                    styleTypeToApply = "custom20"
                case 21: // 自定义水印21
                    print("选择：预览项21 - 自定义水印21")
                    styleTypeToApply = "custom21"
                case 22: // 自定义水印22
                    print("选择：预览项22 - 自定义水印22")
                    styleTypeToApply = "custom22"
                case 23: // 自定义水印23
                    print("选择：预览项23 - 拼图水印")
                    styleTypeToApply = "custom23"
                case 24: // 自定义水印24
                    print("选择：预览项24 - 拼图水印")
                    styleTypeToApply = "custom24"
                case 25: // 自定义水印25
                    print("选择：预览项25 - 自定义水印25")
                    styleTypeToApply = "custom25"
                default:
                    print("选择：预览项 \(actualIndex) - 行为未定义，暂不应用样式。")
                    break 
                }
                
                // 更新状态变量以反映当前选择
                if let newType = styleTypeToApply {
                    self.selectedWatermarkType = newType
                    
                    // 检查水印13/14类型时是否需要关闭描述选项
                    if newType == "custom13" || newType == "custom14" {
                        let settings = WatermarkSettingsManager.shared.getSettings()
                        let hasLogo = !settings.selectedLogo.isEmpty
                        let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                        let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                        
                        // 统计已启用的常规元素数量
                        var enabledCount = 0
                        if hasLogo { enabledCount += 1 }
                        if hasSignature { enabledCount += 1 }
                        if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素
                        
                        // 如果常规元素少于3个，自动关闭描述选项
                        if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                            WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                        }
                    }
                    
                    // 根据水印类型动态设置宽边框粗细的默认值
                    if ["polaroid", "custom4", "custom5", "custom9", "custom10", "custom11", "custom12", "custom13", "custom14", "custom15", "custom16", "custom19", "custom25"].contains(newType) {
                        let defaultSliderValue = WideBorderThicknessUtils.getDefaultSliderValue(for: newType)
                        self.wideBorderThicknessSliderValue = defaultSliderValue
                        // 同时更新到设置中
                        WatermarkSettingsManager.shared.updateSetting(\.wideBorderThicknessMultiplier, value: defaultSliderValue)
                    }
                    
                    // 处理自定义水印10、水印16和水印25的偏好选项限制
                    if newType == "custom10" || newType == "custom16" || newType == "custom25" {
                        // 获取当前设置
                        let settings = WatermarkSettingsManager.shared.getSettings()
                        // 检查偏好选项
                        if !settings.selectedPreferences.isEmpty {
                            // 检查所有已选择的偏好是否有不支持的选项
                            let unsupportedOptions = settings.selectedPreferences.filter { prefOption in
                                return prefOption != "OFF" && prefOption != "参数"
                            }
                            
                            // 如果有不支持的选项
                            if !unsupportedOptions.isEmpty {
                                // 检查是否包含参数选项
                                if settings.selectedPreferences.contains("参数") {
                                    // 只保留参数选项
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: ["参数"])
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: "参数")
                                } else {
                                    // 重置为OFF
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: [])
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: "OFF")
                                }
                                // 发送通知刷新偏好UI
                                NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
                            }
                        }
                        
                        // 自定义水印10不再执行互斥逻辑，可以同时显示文字和参数
                    }
                } else if actualIndex == 0 {
                    self.selectedWatermarkType = "none"
                }

                // 持久化选择的水印类型
                WatermarkSettingsManager.shared.updateSetting(\.activeWatermarkStyleType, value: self.selectedWatermarkType)

                // 如果切换到需要互斥逻辑的水印类型，则主动应用一次互斥规则
                if shouldApplyMutualExclusionForType(self.selectedWatermarkType) {
                    applyMutualExclusionForSettings()
                }
                
                // 获取水印设置
                let settings = WatermarkSettingsManager.shared.getSettings()
                
                // 使用工厂创建并应用样式
                if let style = WatermarkStyleFactory.createWatermarkStyle(type: self.selectedWatermarkType, settings: settings) {
                    print("✅ 应用水印样式: \(self.selectedWatermarkType)")
                    manager.applyWatermarkStyle(style)
                } else {
                    print("ℹ️ 移除当前水印")
                    manager.removeCurrentWatermark()
                }
            }, watermarkIndices: filteredWatermarkIndices)
            
            // 水印选项列表
            ScrollView {
                VStack(spacing: 0) {
                    // 根据水印类型显示对应的选项
                    ForEach(watermarkOptions.filter { option in
                        // 如果是custom21或custom20，添加边框相关选项，与custom19保持一致
                        if selectedWatermarkType == "custom21" || selectedWatermarkType == "custom20" {
                            return option.title == "Logo" || 
                                   option.title == "Logo颜色" || 
                                   option.title == "Logo大小" ||
                                   option.title == "边框颜色" ||
                                   option.title == "边框粗细" ||
                                   option.title == "宽边框粗细" ||
                                   option.title == "模糊边框" ||
                                   option.title == "阴影效果"
                        }
                        // 如果是custom22，显示Logo、Logo颜色、Logo大小和位置选项
                        else if selectedWatermarkType == "custom22" {
                            return option.title == "Logo" || 
                                   option.title == "Logo颜色" || 
                                   option.title == "Logo大小" || 
                                   option.title == "位置"
                        }
                        // 如果是custom23或custom24，仅显示指定的边框相关选项
                        else if selectedWatermarkType == "custom23" || selectedWatermarkType == "custom24" {
                            // 只显示边框颜色和边框粗细选项，不包括阴影效果、宽边框粗细和左右边框粗细
                            return option.type == .borderColor ||
                                  option.type == .borderThickness
                        } else {
                            // 其他水印类型正常显示
                            return option.isVisible(selectedWatermarkType)
                        }
                    }, id: \.title) { option in
                        switch option.type {
                        case .logo, .text, .text2, .signature, .preference, .position:
                            WatermarkOptionItem(title: option.title, isKeyboardVisible: $isKeyboardVisible, watermarkType: selectedWatermarkType) {
                                // 获取当前水印设置
                            let settings = WatermarkSettingsManager.shared.getSettings()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        
                        case .textColor:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            HStack(spacing: screenWidth * 0.04) {
                                Circle().fill(availableFontColors[0]).frame(width: borderColorCircleSize, height: borderColorCircleSize)
                                    .overlay(Circle().stroke(selectedFontColorIndex == 0 ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
                                    .onTapGesture { selectedFontColorIndex = 0 }
                                Circle().fill(availableFontColors[1]).frame(width: borderColorCircleSize, height: borderColorCircleSize)
                                    .overlay(Circle().stroke(selectedFontColorIndex == 1 ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
                                    .onTapGesture { selectedFontColorIndex = 1 }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04) 
                        .padding(.vertical, screenHeight * 0.0075) 
                        .onChange(of: selectedFontColorIndex) { oldValue, newValue in 
                            let selectedColor = availableFontColors[newValue]
                            let uiColor = UIColor(selectedColor)
                            var r: CGFloat = 0; var g: CGFloat = 0; var b: CGFloat = 0; var a: CGFloat = 0
                            uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
                            WatermarkSettingsManager.shared.updateSetting(\.fontColorRed, value: Double(r))
                            WatermarkSettingsManager.shared.updateSetting(\.fontColorGreen, value: Double(g))
                            WatermarkSettingsManager.shared.updateSetting(\.fontColorBlue, value: Double(b))
                            WatermarkSettingsManager.shared.updateSetting(\.fontColorAlpha, value: Double(a))
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                        }
                        
                        case .fontWeight:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.02) {
                                    ForEach(availableFontWeights, id: \.self) { weight in
                                        Button(action: {
                                            selectedFontWeight = weight
                                            // 保存选中的中文字体粗细名称
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedFontWeight, value: weight)
                                            
                                            // 直接保存字体粗细名称，不再使用数值映射
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(weight)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))
                                                .foregroundColor(selectedFontWeight == weight ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(selectedFontWeight == weight ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        
                        case .signatureFontWeight:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.02) {
                                    ForEach(availableSignatureFontWeights, id: \.self) { weight in
                                        Button(action: {
                                            selectedSignatureFontWeight = weight
                                            // 保存选中的署名字体粗细名称
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedSignatureFontWeight, value: weight)
                                            
                                            // 直接保存字体粗细名称，不再使用数值映射
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(weight)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))
                                                .foregroundColor(selectedSignatureFontWeight == weight ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(selectedSignatureFontWeight == weight ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        
                        case .signatureFontSize:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", signatureFontSizeSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $signatureFontSizeSliderValue, range: 0.5...2.0, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: signatureFontSizeSliderValue) { oldValue, newValue in
                            // 更新设置
                            WatermarkSettingsManager.shared.updateSetting(\.signatureFontSizeMultiplier, value: newValue)
                            // 重新应用水印样式
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                        }
                        
                        case .textFontSize:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", textFontSizeSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $textFontSizeSliderValue, range: 0.5...2.0, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: textFontSizeSliderValue) { oldValue, newValue in
                            // 更新设置
                            WatermarkSettingsManager.shared.updateSetting(\.textFontSizeMultiplier, value: newValue)
                            // 重新应用水印样式
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                        }
                        
                        case .font:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                                
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.02) {
                                    ForEach(availableFonts, id: \.self) { font in
                                        Button(action: {
                                            selectedFontName = font
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedFontName, value: font)
                                            
                                            // 当字体改变时，更新粗细选项为默认值
                                            if font == "HarmonyOS_Sans_SC" || font == "PingFang-SC" || font == "SourceHanSansSC" || font == "HONORSansCN" {
                                                selectedFontWeight = "Regular"
                                                selectedSignatureFontWeight = "Regular"
                                            } else {
                                                selectedFontWeight = "Regular"
                                                selectedSignatureFontWeight = "Regular"
                                            }
                                            
                                            // 保存字体粗细名称
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedFontWeight, value: selectedFontWeight)
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedSignatureFontWeight, value: selectedSignatureFontWeight)
                                            
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(font)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))
                                                .foregroundColor(selectedFontName == font ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(selectedFontName == font ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        .onAppear {
                            // 从设置中加载字体选择
                            let settings = WatermarkSettingsManager.shared.getSettings()
                            selectedFontName = settings.selectedFontName
                        }
                            
                        case .englishFont:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                                
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.02) {
                                    ForEach(availableEnglishFonts, id: \.self) { font in
                                        Button(action: {
                                            // 如果已经选中，再次点击则取消选择（设为空字符串表示使用默认系统字体）
                                            if selectedEnglishFontName == font {
                                                selectedEnglishFontName = ""
                                                WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontName, value: "")
                                                
                                                // 重置为默认的SF字体粗细
                                                selectedEnglishFontWeight = "Regular"
                                                // 保存字体粗细名称
                                                WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontWeight, value: selectedEnglishFontWeight)
                                            } else {
                                            selectedEnglishFontName = font
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontName, value: font)
                                                
                                                // 设置默认的字体粗细
                                                if font == "FuturaPT" {
                                                    selectedEnglishFontWeight = "Book"
                                                } else if font == "Makinas-Square" || font == "Makinas-Flat" {
                                                    selectedEnglishFontWeight = "Regular" // Makinas字体只有一种粗细
                                                }
                                                    // 保存字体粗细名称
                                                    WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontWeight, value: selectedEnglishFontWeight)
                                            }
                                            
                                            // 应用水印样式
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(font)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))
                                                .foregroundColor(selectedEnglishFontName == font ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(selectedEnglishFontName == font ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        .onAppear {
                            // 从设置中加载英文字体粗细选择
                            let settings = WatermarkSettingsManager.shared.getSettings()
                            
                            // 优先使用保存的英文字体粗细名称
                            if !settings.selectedEnglishFontWeight.isEmpty {
                                selectedEnglishFontWeight = settings.selectedEnglishFontWeight
                            } else {
                                // 直接设置默认的字体粗细名称，不再使用数值映射
                                switch selectedEnglishFontName {
                                case "FuturaPT":
                                    selectedEnglishFontWeight = "Book"
                                case "TYPOGRAPH PRO":
                                    selectedEnglishFontWeight = "Light"
                                case "CourierPrimeCode":
                                    selectedEnglishFontWeight = "Regular"
                                case "Miamo":
                                    selectedEnglishFontWeight = "Regular"
                                case "ADELE":
                                    selectedEnglishFontWeight = "Light"
                                case "Quantico":
                                    selectedEnglishFontWeight = "Regular"
                                case "Syntax":
                                    selectedEnglishFontWeight = "Roman"
                                case "Makinas-Square", "Makinas-Flat":
                                    selectedEnglishFontWeight = "Regular" // Makinas字体只有一种粗细
                                default:
                                    selectedEnglishFontWeight = "Regular"
                                }
                                
                                // 保存字体粗细名称
                                WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontWeight, value: selectedEnglishFontWeight)
                            }
                        }
                            
                        case .borderColor:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.04) {
                                    ForEach(0..<availableBorderColors.count, id: \.self) { index in
                                        Circle()
                                            .fill(availableBorderColors[index])
                                            // 为深色选项添加浅灰色边框，让它在黑色背景下更明显
                                            .overlay(
                                                Circle()
                                                    .stroke(
                                                        // 为深色选项添加边框：黑色(1)、灰色(2)、纯黑色(3)、紫色(9)
                                                        (index == 1 || index == 2 || index == 3 || index == 9) ? Color.gray.opacity(0.5) : Color.clear,
                                                        lineWidth: 1
                                                    )
                                            )
                                            .frame(width: borderColorCircleSize, height: borderColorCircleSize)
                                            .overlay(Circle().stroke(selectedBorderColorIndex == index ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
                                            .onTapGesture { selectedBorderColorIndex = index }
                                    }
                                }
                                .padding(.horizontal, screenWidth * 0.02)
                                .padding(.vertical, screenHeight * 0.0025) // 方案A：最小内边距防止遮挡（0.25%）
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04) 
                        .padding(.vertical, screenHeight * 0.0075) 
                        .onChange(of: selectedBorderColorIndex) { oldValue, newValue in 
                            let selectedColor = actualBorderColors[newValue] // 使用实际颜色
                            let uiColor = UIColor(selectedColor)
                            var r: CGFloat = 0; var g: CGFloat = 0; var b: CGFloat = 0; var a: CGFloat = 0
                            uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
                            WatermarkSettingsManager.shared.updateSetting(\.borderColorRed, value: Double(r))
                            WatermarkSettingsManager.shared.updateSetting(\.borderColorGreen, value: Double(g))
                            WatermarkSettingsManager.shared.updateSetting(\.borderColorBlue, value: Double(b))
                            WatermarkSettingsManager.shared.updateSetting(\.borderColorAlpha, value: Double(a))
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                        }
                        
                        case .wideBorderPosition:
                        HStack(spacing: standardSpacing) { 
                            Text(selectedWatermarkType == "custom5" ? "右侧" : "左侧")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                            
                            Spacer()
                            
                            // 宽边框位置开关
                            Toggle("", isOn: $isRightWideBorderEnabled)
                                .labelsHidden()
                                .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                                .scaleEffect(0.75)
                                .onChange(of: isRightWideBorderEnabled) { oldValue, newValue in
                                // 更新设置
                                WatermarkSettingsManager.shared.updateSetting(\.isRightWideBorderEnabled, value: newValue)
                                // 重新应用水印样式
                                applyWatermarkStyle(type: selectedWatermarkType)
                                }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        
                        case .borderThickness:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                    Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", borderThicknessSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $borderThicknessSliderValue, range: 0...1, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: borderThicknessSliderValue) { oldValue, newValue in
                            WatermarkSettingsManager.shared.updateSetting(\.borderThicknessMultiplier, value: newValue)
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                            
                        case .wideBorderThickness:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                    Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", wideBorderThicknessSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $wideBorderThicknessSliderValue, range: 0...1, step: 0.01)
                            
                            // 水印6等宽边框模式开关 - 仅当选中水印6时显示
                            if selectedWatermarkType == "custom6" {
                                HStack(spacing: standardSpacing) { 
                                    Text("等宽模式")
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    // 等宽模式开关
                                    Toggle("", isOn: $isEqualWidthBorderEnabled)
                                        .labelsHidden()
                                        .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                                        .scaleEffect(0.75)
                                        .onChange(of: isEqualWidthBorderEnabled) { oldValue, newValue in
                                            // 更新设置
                                            WatermarkSettingsManager.shared.updateSetting(\.isEqualWidthBorderEnabled, value: newValue)
                                            // 重新应用水印样式
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }
                                }
                                .padding(.horizontal, screenWidth * 0.04)
                                .padding(.vertical, screenHeight * 0.01)
                            }
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: wideBorderThicknessSliderValue) { oldValue, newValue in
                            // 保存到WatermarkSettings
                            WatermarkSettingsManager.shared.updateSetting(\.wideBorderThicknessMultiplier, value: newValue)
                            // 重新应用水印样式以反映变化
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                            
                        case .topBottomBorderThickness:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                    Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                    Text(String(format: "%.0f%%", topBottomBorderThicknessSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                                CustomSlider(value: $topBottomBorderThicknessSliderValue, range: 0...1, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                            .onChange(of: topBottomBorderThicknessSliderValue) { oldValue, newValue in
                                WatermarkSettingsManager.shared.updateSetting(\.topBottomBorderThicknessMultiplier, value: newValue)
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                            
                        case .leftRightBorderThickness:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                    Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                    Text(String(format: "%.0f%%", leftRightBorderThicknessSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                                CustomSlider(value: $leftRightBorderThicknessSliderValue, range: 0...1, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                            .onChange(of: leftRightBorderThicknessSliderValue) { oldValue, newValue in
                                WatermarkSettingsManager.shared.updateSetting(\.leftRightBorderThicknessMultiplier, value: newValue)
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                            
                        case .logoColor:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            HStack(spacing: screenWidth * 0.04) {
                                    // 不使用颜色调整选项 - 使用DisabledOptionCircle组件
                                    DisabledOptionCircle(size: borderColorCircleSize, isSelected: selectedLogoColorIndex == 0, showSelectionBorder: false)
                                        .onTapGesture { selectedLogoColorIndex = 0 }
                                    
                                    Circle().fill(availableLogoColors[1]).frame(width: borderColorCircleSize, height: borderColorCircleSize)
                                        .overlay(Circle().stroke(selectedLogoColorIndex == 1 ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
                                        .onTapGesture { selectedLogoColorIndex = 1 }
                                    
                                    Circle().fill(availableLogoColors[2]).frame(width: borderColorCircleSize, height: borderColorCircleSize)
                                        .overlay(Circle().stroke(selectedLogoColorIndex == 2 ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
                                        .onTapGesture { selectedLogoColorIndex = 2 }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04) 
                        .padding(.vertical, screenHeight * 0.0075) 
                            .onChange(of: selectedLogoColorIndex) { oldValue, newValue in 
                                if newValue == 0 {
                                    // 不使用颜色调整选项 - 设置为接近0.5的灰色
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorRed, value: 0.5)
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorGreen, value: 0.5)
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorBlue, value: 0.5)
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorAlpha, value: 1.0)
                                } else {
                                    // 白色或黑色选项
                                    let selectedColor = availableLogoColors[newValue]
                                    let uiColor = UIColor(selectedColor)
                                    var r: CGFloat = 0; var g: CGFloat = 0; var b: CGFloat = 0; var a: CGFloat = 0
                                    uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorRed, value: Double(r))
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorGreen, value: Double(g))
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorBlue, value: Double(b))
                                    WatermarkSettingsManager.shared.updateSetting(\.logoColorAlpha, value: Double(a))
                                }
                                applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                        
                        case .logoSize:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", logoSizeSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $logoSizeSliderValue, range: 0.5...1.5, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: logoSizeSliderValue) { oldValue, newValue in
                            // 更新设置
                            WatermarkSettingsManager.shared.updateSetting(\.logoSizeMultiplier, value: newValue)
                            // 重新应用水印样式
                                applyWatermarkStyle(type: self.selectedWatermarkType)
                            }
                        
                        case .blurBorder:
                        HStack(spacing: standardSpacing) { 
                            Text(option.title)
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                            
                            Spacer()
                            
                            // 模糊边框开关
                            Toggle("", isOn: $isBlurBorderEnabled)
                                .labelsHidden()
                                .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                                .scaleEffect(0.75)
                                .onChange(of: isBlurBorderEnabled) { oldValue, newValue in
                                    // 更新设置
                                    WatermarkSettingsManager.shared.updateSetting(\.isBlurBorderEnabled, value: newValue)
                                    // 重新应用水印样式
                                    applyWatermarkStyle(type: self.selectedWatermarkType)
                                }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        
                        if isBlurBorderEnabled {
                            // 模糊样式选项 - 完全仿照偏好选项的实现
                            HStack(spacing: screenWidth * 0.06) {
                                    Text("模糊强度")
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                        .foregroundColor(.white)
                                
                                // 模糊样式切换器，使用与偏好切换器相同的样式
                                HStack(spacing: screenWidth * 0.02) {
                                    // 将显示名称与系统样式名称映射
                                    let blurOptions = [
                                        ("轻微", "systemUltraThinMaterial"),
                                        ("较低", "systemThinMaterial"),
                                        ("标准", "systemMaterial"),
                                        ("较高", "systemThickMaterial"),
                                        ("很高", "systemChromeMaterial")
                                    ]
                                    
                                    ForEach(blurOptions, id: \.0) { option in
                                        Button(action: {
                                            // 更新选项并保存设置
                                            blurStyle = option.1
                                            WatermarkSettingsManager.shared.updateSetting(\.blurStyle, value: option.1)
                                        // 重新应用水印样式
                                        applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(option.0)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))  // 文本大小为1.5%
                                                .foregroundColor(blurStyle == option.1 ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)  // 垂直内边距0.5%屏幕高度
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(blurStyle == option.1 ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                                
                                    Spacer()
                                }
                                .padding(.horizontal, screenWidth * 0.04)
                            .padding(.vertical, screenHeight * 0.0075)
                        }
                        
                        case .shadowEffect:
                        HStack(spacing: standardSpacing) { 
                            Text(option.title)
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                            
                            Spacer()
                            
                            // 阴影效果开关
                            Toggle("", isOn: $isShadowEnabled)
                                .labelsHidden()
                                .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                                .scaleEffect(0.75)
                                .onChange(of: isShadowEnabled) { oldValue, newValue in
                                        // 更新设置
                                    WatermarkSettingsManager.shared.updateSetting(\.isShadowEnabled, value: newValue)
                                        // 重新应用水印样式
                                        applyWatermarkStyle(type: self.selectedWatermarkType)
                                    }
                            }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        case .preferenceScale:
                        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                            HStack {
                                Text(option.title)
                                    .font(.system(size: screenHeight * 0.02, weight: .bold))
                                    .foregroundColor(.white)
                                Spacer()
                                Text(String(format: "%.0f%%", preferenceScaleFactorSliderValue * 100))
                                    .font(.system(size: screenHeight * 0.02, weight: .medium))
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                            CustomSlider(value: $preferenceScaleFactorSliderValue, range: 0.75...1.25, step: 0.01)
                        }
                        .padding(.vertical, screenHeight * 0.01)
                        .onChange(of: preferenceScaleFactorSliderValue) { newValue in
                            // 更新设置
                            WatermarkSettingsManager.shared.updateSetting(\.preferenceScaleFactor, value: newValue)
                            // 重新应用水印样式
                            applyWatermarkStyle(type: self.selectedWatermarkType)
                        }
                        case .englishFontWeight:
                        HStack(spacing: standardSpacing) { 
                                Text(option.title)
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.02) {
                                    ForEach(availableEnglishFontWeights, id: \.self) { weight in
                                        Button(action: {
                                            selectedEnglishFontWeight = weight
                                            // 保存选中的英文字体粗细名称
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontWeight, value: weight)
                                            
                                            // 应用水印样式
                                            applyWatermarkStyle(type: self.selectedWatermarkType)
                                        }) {
                                            Text(weight)
                                                .font(.system(size: screenHeight * 0.015, weight: .medium))
                                                .foregroundColor(selectedEnglishFontWeight == weight ? .black : .white)
                                                .padding(.horizontal, screenWidth * 0.02)
                                                .padding(.vertical, screenHeight * 0.005)
                                                .background(
                                                    RoundedRectangle(cornerRadius: 4)
                                                        .fill(selectedEnglishFontWeight == weight ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                                )
                                        }
                                    }
                                }
                            }
                            Spacer()
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0075)
                        .onAppear {
                            // 从设置中加载英文字体粗细选择
                            let settings = WatermarkSettingsManager.shared.getSettings()
                            
                            // 优先使用保存的英文字体粗细名称
                            if !settings.selectedEnglishFontWeight.isEmpty {
                                selectedEnglishFontWeight = settings.selectedEnglishFontWeight
                            } else {
                                // 直接设置默认的字体粗细名称，不再使用数值映射
                                switch selectedEnglishFontName {
                                case "FuturaPT":
                                    selectedEnglishFontWeight = "Book"
                                case "TYPOGRAPH PRO":
                                    selectedEnglishFontWeight = "Light"
                                case "CourierPrimeCode":
                                    selectedEnglishFontWeight = "Regular"
                                case "Miamo":
                                    selectedEnglishFontWeight = "Regular"
                                case "ADELE":
                                    selectedEnglishFontWeight = "Light"
                                case "Quantico":
                                    selectedEnglishFontWeight = "Regular"
                                case "Syntax":
                                    selectedEnglishFontWeight = "Roman"
                                case "Makinas-Square", "Makinas-Flat":
                                    selectedEnglishFontWeight = "Regular" // Makinas字体只有一种粗细
                                default:
                                    selectedEnglishFontWeight = "Regular"
                                }
                                
                                // 保存字体粗细名称
                                WatermarkSettingsManager.shared.updateSetting(\.selectedEnglishFontWeight, value: selectedEnglishFontWeight)
                            }
                        }
                        
                        default:
                            EmptyView()
                        }
                    }
                }
                                                .padding(.vertical, screenHeight * 0.005)
            }
            .safeAreaInset(edge: .bottom) {
                Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        }
    }
}

// 辅助扩展，用于近似比较UIColor (处理浮点数精度问题)
extension UIColor {
    func isApproximatelyEqualTo(_ color: UIColor, tolerance: CGFloat = 0.001) -> Bool {
        var r1: CGFloat = 0, g1: CGFloat = 0, b1: CGFloat = 0, a1: CGFloat = 0
        var r2: CGFloat = 0, g2: CGFloat = 0, b2: CGFloat = 0, a2: CGFloat = 0

        self.getRed(&r1, green: &g1, blue: &b1, alpha: &a1)
        color.getRed(&r2, green: &g2, blue: &b2, alpha: &a2)

        return abs(r1 - r2) < tolerance &&
               abs(g1 - g2) < tolerance &&
               abs(b1 - b2) < tolerance &&
               abs(a1 - a2) < tolerance
    }
}

// 在WatermarkOptionItem结构体之前添加一个通用的"不使用"选项圆圈视图
struct DisabledOptionCircle: View {
    var size: CGFloat
    var isSelected: Bool
    var showSelectionBorder: Bool = true // 新增参数，默认为true
    
    var body: some View {
        ZStack {
            Circle()
                .fill(Color.white)
                .frame(width: size, height: size)
                .overlay(Circle().stroke((isSelected && showSelectionBorder) ? Color.white : Color.clear, lineWidth: 1.5).padding(-1.5))
            
            Image("LH Wu")
                .resizable()
                .frame(width: size * 0.66, height: size * 0.66)
        }
    }
}

// 添加通用的文本输入选项视图
struct TextInputOptionView: View {
    let title: String
    @Binding var inputText: String
    @Binding var isEnabled: Bool
    @Binding var isKeyboardVisible: Bool
    
    // 添加水印类型和应用样式的回调
    var watermarkType: String
    var onUpdateSetting: (String) -> Void
    var onUpdateEnabled: (Bool) -> Void
    var onApplyStyle: () -> Void
    
    // 添加控制是否显示开关的参数
    var showToggle: Bool = true
    
    // 添加显示单位的参数
    var showUnit: Bool = false
    var unitText: String = ""
    
    // 添加控制是否显示输入框的参数
    var showInputField: Bool = true
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 辅助方法：检查是否需要应用互斥逻辑
    private func shouldApplyMutualExclusionLogic() -> Bool {
        return watermarkType == "custom4" || 
               watermarkType == "film" || 
               watermarkType == "custom7" || 
               watermarkType == "custom8" || 
               watermarkType == "custom9" || 
               watermarkType == "custom5" ||
               watermarkType == "custom11" ||
               watermarkType == "custom12" ||
               watermarkType == "custom13" ||
               watermarkType == "custom14" ||
               watermarkType == "border_2percent" ||
               watermarkType == "custom18" ||
               watermarkType == "custom19"  // 添加自定义水印19
    }
    
    var body: some View {
        HStack(spacing: screenWidth * 0.06) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)
            
            if showInputField {
                // 文本输入框和单位
                HStack(spacing: 4) {
                    // 根据不同的标题显示不同的提示文本
                    let placeholder = title == "经度" ? "请输入经度值 (°E)" :
                                       title == "纬度" ? "请输入纬度值 (°N)" :
                                       title == "焦距" ? "请输入焦距值 (mm)" :
                                       title == "光圈" ? "请输入光圈值 (f)" :
                                       title == "快门" ? "请输入快门值 (s)" :
                                       title == "感光" ? "请输入ISO值" :
                                       "请输入\(title)"
                    
                    TextField(placeholder, text: $inputText)
                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, screenHeight * 0.0075)
                    .background(Color(uiColor: .systemGray5))
                    .cornerRadius(6)
                    .accentColor(UIConstants.dialIndicatorColor)
                    .onTapGesture {
                        isKeyboardVisible = true
                    }
                    .onSubmit {
                        isKeyboardVisible = false
                        onUpdateSetting(inputText)
                        onApplyStyle()
                    }
                    .onChange(of: inputText) { newValue in
                        onUpdateSetting(newValue)
                        onApplyStyle()
                        }
                    
                    // 显示单位
                    if showUnit {
                        Text(unitText)
                            .font(.system(size: screenHeight * 0.015, weight: .medium))
                            .foregroundColor(.white)
                    }
                }
                
                // 当显示输入框时，开关按钮在最右侧
                if showToggle {
                    Toggle("", isOn: $isEnabled)
                        .labelsHidden()
                        .tint(UIConstants.dialIndicatorColor)
                        .scaleEffect(0.75)
                        .onChange(of: isEnabled) { newValue in
                            onUpdateEnabled(newValue)
                            
                            // 文字选项特有的互斥逻辑处理
                            if title == "文字" && shouldApplyMutualExclusionLogic() && newValue {
                                // 获取当前设置
                                let settings = WatermarkSettingsManager.shared.getSettings()
                                
                                // 检查偏好是否已启用（使用新格式检查）
                                let hasPreference = !settings.selectedPreferences.isEmpty
                                
                                // 如果偏好已启用，则清空所有偏好选择并关闭旧格式偏好
                                if hasPreference {
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: []) // 清空新格式的偏好数组
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: "OFF") // 关闭旧格式的偏好
                                    // 发送通知刷新偏好UI
                                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
                                }
                            }
                            
                            onApplyStyle()
                        }
                } else {
                    // 使用隐藏的Toggle组件而不是Spacer，确保宽度完全一致
                    Toggle("", isOn: .constant(false))
                        .labelsHidden()
                        .tint(UIConstants.dialIndicatorColor)
                        .scaleEffect(0.75)
                        .opacity(0) // 完全透明
                        .disabled(true) // 禁止交互
                }
            } else {
                // 当不显示输入框时，开关按钮紧邻标题
                if showToggle {
                    Toggle("", isOn: $isEnabled)
                        .labelsHidden()
                        .tint(UIConstants.dialIndicatorColor)
                        .scaleEffect(0.75)
                        .onChange(of: isEnabled) { newValue in
                            onUpdateEnabled(newValue)
                            
                            // 文字选项特有的互斥逻辑处理
                            if title == "文字" && shouldApplyMutualExclusionLogic() && newValue {
                                // 获取当前设置
                                let settings = WatermarkSettingsManager.shared.getSettings()
                                
                                // 检查偏好是否已启用（使用新格式检查）
                                let hasPreference = !settings.selectedPreferences.isEmpty
                                
                                // 如果偏好已启用，则清空所有偏好选择并关闭旧格式偏好
                                if hasPreference {
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: []) // 清空新格式的偏好数组
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: "OFF") // 关闭旧格式的偏好
                                    // 发送通知刷新偏好UI
                                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
                                }
                            }
                            
                            onApplyStyle()
                        }
                }
                
                // 添加一个Spacer将内容推到左侧
                Spacer()
            }
        }
        .padding(.vertical, screenHeight * 0.0075)
    }
}

// 水印选项项目组件
struct WatermarkOptionItem: View {
    let title: String
    let action: () -> Void
    
    // 添加键盘状态绑定
    @Binding var isKeyboardVisible: Bool
    
    // 添加水印类型属性
    var watermarkType: String = "none"
    
    // 添加选中的Logo状态变量
    @State private var selectedLogo: String = ""
    
    // 添加自定义水印19的第二行Logo选择状态变量
    @State private var selectedLogo2: String = ""
    
    // 添加自定义水印21的第三行Logo选择状态变量
    @State private var selectedLogo3: String = ""
    
    // 添加用于跟踪是否显示二级Logo的状态变量
    @State private var showSubLogos: Bool = false
    @State private var parentLogo: String = ""
    
    // 为DJI系列定义子Logo数组
    private let djiSubLogos: [String] = [
        "LH OSMO Action", "LH OSMO Action-2", "LH OSMO Action-3", "LH OSMO Action-4", "LH OSMO Action-5pro",
        "LH Air-3s", "LH Air-3", "LH Avata", "LH Avata-2", "LH Inspire",
        "LH Mavic-3", "LH Mavic-3c", "LH Mavic-3pro", "LH Mavic-4pro",
        "LH Mini-3", "LH Mini-4pro", "LH Mini-4k",
        "LH Mobile-6", "LH Mobile-7",
        "LH Pocket-3", "LH Pocket-2",
        "LH RONIN",
        "LH Noe", "LH Flip"
    ]
    
    // 为华为系列定义子Logo数组
    private let huaweiSubLogos: [String] = [
        "LH Huawei-1", "LH Huawei-2", "LH Huawei-3", 
        "LH Huawei-xmage", "LH Huawei-xmage-1"
    ]
    
    // 自定义水印19的第一行Logo选项 - 仅包含"无Logo"和"DJI主Logo"
    private let custom19FirstRowLogos: [String] = ["", "LH DJI"]  // 空字符串表示"无Logo"
    
    // 自定义水印19的第二行Logo选项 - 包含"无Logo"、两个哈苏Logo和所有DJI子Logo
    private let custom19SecondRowLogos: [String] = [
        "", // 空字符串表示"无Logo"选项
        // 两个哈苏Logo
        "LH Hasselblad", "LH H",
        // DJI所有子Logo - 确保与djiSubLogos保持一致
        "LH OSMO Action", "LH OSMO Action-2", "LH OSMO Action-3", "LH OSMO Action-4", "LH OSMO Action-5pro",
        "LH Air-3s", "LH Air-3", "LH Avata", "LH Avata-2", "LH Inspire",
        "LH Mavic-3", "LH Mavic-3c", "LH Mavic-3pro", "LH Mavic-4pro",
        "LH Mini-3", "LH Mini-4pro", "LH Mini-4k",
        "LH Mobile-6", "LH Mobile-7",
        "LH Pocket-3", "LH Pocket-2",
        "LH RONIN",
        "LH Noe", "LH Flip"
    ]
    
    // 自定义水印21的第一行Logo选项 - 仅包含"无Logo"和"DJI主Logo"
    private let custom21FirstRowLogos: [String] = ["", "LH DJI"]  // 空字符串表示"无Logo"
    
    // 自定义水印21的第二行Logo选项 - 仅包含哈苏的两个Logo
    private let custom21SecondRowLogos: [String] = ["", "LH Hasselblad", "LH H"]  // 空字符串表示"无Logo"选项
    
    // 自定义水印21的第三行Logo选项 - 包含除DJI主Logo和哈苏Logo外的其他所有DJI子Logo
    private let custom21ThirdRowLogos: [String] = [
        "", // 空字符串表示"无Logo"选项
        // DJI所有子Logo - 但不包括主Logo和哈苏Logo
        "LH OSMO Action", "LH OSMO Action-2", "LH OSMO Action-3", "LH OSMO Action-4", "LH OSMO Action-5pro",
        "LH Air-3s", "LH Air-3", "LH Avata", "LH Avata-2", "LH Inspire",
        "LH Mavic-3", "LH Mavic-3c", "LH Mavic-3pro", "LH Mavic-4pro",
        "LH Mini-3", "LH Mini-4pro", "LH Mini-4k",
        "LH Mobile-6", "LH Mobile-7",
        "LH Pocket-3", "LH Pocket-2",
        "LH RONIN",
        "LH Noe", "LH Flip"
    ]
    
    // 初始化方法添加键盘绑定参数和水印类型参数
    init(title: String, isKeyboardVisible: Binding<Bool> = .constant(false), watermarkType: String = "none", action: @escaping () -> Void) {
        self.title = title
        self.action = action
        self.watermarkType = watermarkType
        self._isKeyboardVisible = isKeyboardVisible
        
        // 如果是Logo选项，初始化时获取当前选中的Logo
        if title == "Logo" {
            let settings = WatermarkSettingsManager.shared.getSettings()
            self._selectedLogo = State(initialValue: settings.selectedLogo)
            self._selectedLogo2 = State(initialValue: settings.selectedLogo2)
            self._selectedLogo3 = State(initialValue: settings.selectedLogo3)
        }
    }
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 标准间距常量 - 6%屏幕宽度，与WatermarkControlView保持一致
    private let standardSpacing: CGFloat = UIScreen.main.bounds.width * 0.06
    
    // 为文字和署名添加状态变量
    @State private var textInput: String = ""
    @State private var text2Input: String = "" // 添加描述输入状态变量
    @State private var isEnabled: Bool = false
    @State private var isText2Enabled: Bool = false // 添加描述启用状态变量
    
    // 为焦距添加显示开关状态变量
    @State private var isFocalLengthEnabled: Bool = true
    
    // 为偏好选项添加状态变量 - 改为数组支持多选（最多两个）
    @State private var selectedPreferences: [PreferenceOption] = []
    // 添加最大选择数量常量
    private let maxPreferenceSelections = 2
    
    // 为偏好分行开关添加状态变量
    @State private var isMultilinePreferenceEnabled: Bool = false
    
    // 为水印5右侧宽边框开关添加状态变量
    @State private var isRightWideBorderEnabled: Bool = false
    
    // 为位置选项添加状态变量
    @State private var selectedPosition: PositionOption = .center
    
    // 为偏好输入添加状态变量
    @State private var preferenceInputText: String = ""
    // 为经纬度偏好添加额外的状态变量
    @State private var longitudeText: String = ""
    @State private var latitudeText: String = ""
    // 为参数选项添加状态变量
    @State private var focalLengthText: String = ""
    @State private var apertureText: String = ""
    @State private var shutterSpeedText: String = ""
    @State private var isoText: String = ""
    // 为日期和地点添加单独的状态变量
    @State private var dateText: String = ""
    @State private var placeText: String = ""
    
    // 偏好选项枚举
    enum PreferenceOption: String, CaseIterable {
        case off = "OFF"
        case date = "日期"
        case place = "位置"
        case parameters = "参数"
        case location = "经纬度"
    }
    
    // 位置选项枚举
    enum PositionOption: String, CaseIterable {
        case left = "左"
        case center = "中"
        case right = "右"
        case bottom = "下"
    }
    
    // 辅助方法：检查是否需要应用互斥逻辑
    private func shouldApplyMutualExclusionLogic() -> Bool {
        return watermarkType == "custom4" || 
               watermarkType == "film" || 
               watermarkType == "custom7" || 
               watermarkType == "custom8" || 
               watermarkType == "custom9" || 
               watermarkType == "custom5" ||
               watermarkType == "custom11" ||
               watermarkType == "custom12" ||
               watermarkType == "custom13" ||
               watermarkType == "custom14" ||
               watermarkType == "border_2percent" ||
               watermarkType == "custom18" ||
               watermarkType == "custom19" || // 添加自定义水印19，启用互斥逻辑
               watermarkType == "custom20" || // 添加自定义水印20，启用互斥逻辑
               // watermarkType == "custom21" || // 移除自定义水印21，不再应用互斥逻辑
               watermarkType == "custom22"  // 添加自定义水印22，启用互斥逻辑
    }
    
    // 辅助方法：检查是否是自定义水印19特殊处理
    private var isCustom19LogoHandling: Bool {
        return watermarkType == "custom19" || watermarkType == "custom20" || watermarkType == "custom21" || watermarkType == "custom22"
    }
    
    // 辅助方法：检查是否是自定义水印21特殊处理（三行Logo）
    private var isCustom21LogoHandling: Bool {
        return watermarkType == "custom21"
    }
    
    // 辅助方法：应用水印样式
    private func applyCurrentWatermarkStyle() {
        guard watermarkType != "none" else { return }
        
        // 获取当前水印设置
        let settings = WatermarkSettingsManager.shared.getSettings()
        
        // 使用工厂创建并应用水印样式
        if let style = WatermarkStyleFactory.createWatermarkStyle(type: watermarkType, settings: settings) {
            WatermarkManagerProvider.shared.watermarkManager?.applyWatermarkStyle(style)
        }
    }
    
    // 辅助方法：获取水印类型对应的位置选项
    private func positionOptionsForWatermarkType(_ type: String) -> [PositionOption] {
        switch type {
        case "film":
            // 水印3（胶片风格）使用中下选项
            return [.center, .bottom]
        case "custom17":
            // 水印17（基于胶片风格）使用中下选项
            return [.center, .bottom]
        case "custom7":
            // 水印7使用左中右选项
            return [.left, .center, .right]
        case "custom8":
            // 水印8（胶片风格，署名替换Logo）使用中下选项
            return [.center, .bottom]
        case "custom19", "custom20", "custom21":
            // 自定义水印19、20、21不显示位置选项（返回空数组）
            return []
        case "custom22":
            // 水印22（基于胶片风格）使用中下选项，与水印17保持一致
            // 即使它使用自定义水印19的Logo布局，位置选项仍保持中下
            return [.center, .bottom]
        case "custom23":
            // 水印23（拼图水印）使用中下位置选项
            return [.center, .bottom]
        case "custom24":
            // 水印24（拼图水印）使用中下位置选项
            return [.center, .bottom]
        default:
            // 其他水印使用中下选项
            return [.center, .bottom]
        }
    }
    
    // 通用主Logo列表
    private let mainLogos: [String] = [
        "apple.logo", // 苹果Logo (特殊处理)
        "LH H", "LH Hasselblad", "LH Zeiss", "LH Leica-1", "LH Leica", "LH Leica-2",
        "LH Kodak", "LH Polaroid", "LH polaroid-1", "LH DJI", "LH Sony", "LH Alpha",
        "LH Nikon", "LH Nikon-2", "LH Z", "LH Z-2", "LH canon", "LH Lumix",
        "LH Fujifilm", "LH RICOH", "LH pentax", "LH Olympus", "LH Sigma", "LH Huawei",
        "LH Samsung", "LH GoPro", "LH Konica", "LH Minolta", "LH Contax", "LH Mamiya",
        "LH Rollei", "LH PhaseOne", "LH Lomo"
    ]
    
    var body: some View {
        Button(action: action) {
            if title == "Logo" {
                // Logo选项特殊处理 - 使用Grid布局
                Grid(alignment: .leading, horizontalSpacing: screenWidth * 0.06, verticalSpacing: screenHeight * 0.015) {
                    // 主Logo行
                    GridRow {
                    Text(isCustom19LogoHandling ? "Logo" : title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                        // 主Logo选项
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 无Logo选项 - 对所有水印类型都显示
                            Button(action: {
                                // 处理无Logo选择 - 表示不启用Logo
                                selectedLogo = ""
                                WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: "")
                                
                                // 隐藏子Logo行
                                showSubLogos = false
                                parentLogo = ""
                                
                                // 添加水印4的互斥逻辑
                                if shouldApplyMutualExclusionLogic() {
                                    // 获取当前设置
                                    let settings = WatermarkSettingsManager.shared.getSettings()
                                    
                                    // 检查文字是否已启用
                                    let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                    
                                    // 检查偏好是否已启用（不是OFF）
                                    let hasPreference = settings.preferenceOption != "OFF"
                                    
                                    // 如果文字和偏好都启用，保留最近使用的一个
                                    if hasText && hasPreference {
                                        // 这里我们保留偏好（最近使用的），关闭文字
                                        WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                        // 发送通知刷新文字UI
                                        NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                    }
                                }
                                
                                // 重新应用水印样式
                                applyCurrentWatermarkStyle()
                            }) {
                                DisabledOptionCircle(size: screenHeight * 0.03, isSelected: selectedLogo == "", showSelectionBorder: false)
                            }
                            
                            // 根据水印类型显示不同的Logo选项
                            if isCustom19LogoHandling {
                                // 自定义水印19/20/22的第一行Logo选项（不包括已处理的"无Logo"选项）
                                let firstRowLogos = isCustom21LogoHandling ? custom21FirstRowLogos : custom19FirstRowLogos
                                ForEach(firstRowLogos.filter { !$0.isEmpty }, id: \.self) { logoName in
                                    Button(action: {
                                        // 处理Logo选择
                                        selectedLogo = logoName
                                        WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: logoName)
                                        
                                        // 应用互斥逻辑 - 自定义水印19不应用Logo和Logo2之间的互斥
                                        if shouldApplyMutualExclusionLogic() {
                                            let settings = WatermarkSettingsManager.shared.getSettings()
                                            let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                            let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                            
                                            if hasText && hasPreference {
                                                WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                                NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                            }
                                        }
                                        
                                        // 重新应用水印样式
                                        applyCurrentWatermarkStyle()
                                    }) {
                                        ZStack {
                                            Circle()
                                                .fill(selectedLogo == logoName ? UIConstants.logoSelectedColor : Color.white)
                                                .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                            
                                            Image(logoName)
                                                .resizable()
                                                .aspectRatio(contentMode: .fit)
                                                .frame(width: screenHeight * 0.0175)
                                        }
                                    }
                                }
                            } else {
                                // 通用的Logo选项 - 苹果Logo特殊处理
                                Button(action: {
                                    // 处理苹果Logo选择
                                    selectedLogo = "apple.logo"
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: "apple.logo")
                                    
                                    // 隐藏子Logo行
                                    showSubLogos = false
                                    parentLogo = ""
                                    
                                    // 添加水印4的互斥逻辑
                                    if shouldApplyMutualExclusionLogic() {
                                        // 获取当前设置
                                        let settings = WatermarkSettingsManager.shared.getSettings()
                                        
                                        // 检查文字是否已启用
                                        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                        
                                        // 检查偏好是否已启用（不是OFF）
                                        let hasPreference = settings.preferenceOption != "OFF"
                                        
                                        // 如果文字和偏好都启用，保留最近使用的一个
                                        if hasText && hasPreference {
                                            // 这里我们保留偏好（最近使用的），关闭文字
                                            WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                            // 发送通知刷新文字UI
                                            NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                        }
                                    }
                                    
                                    // 重新应用水印样式
                                    applyCurrentWatermarkStyle()
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(selectedLogo == "apple.logo" ? UIConstants.logoSelectedColor : Color.white)
                                            .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                        
                                        Image(systemName: "apple.logo")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(height: screenHeight * 0.02)
                                            .foregroundColor(.black) // 黑色苹果logo
                                    }
                                }
                                
                                // 通用Logo列表
                                ForEach(mainLogos.dropFirst(), id: \.self) { logoName in
                                
                                Button(action: {
                                    // 处理其他Logo选择
                                    selectedLogo = logoName
                                    WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: logoName)
                                    
                                    // 检查是否点击DJI或华为图标并控制子Logo行显示
                                    if logoName == "LH DJI" {
                                        // 如果点击的是DJI，显示子Logo行
                                        showSubLogos = true
                                        parentLogo = "LH DJI"
                                    } else if logoName == "LH Huawei" {
                                        // 如果点击的是华为，显示子Logo行
                                        showSubLogos = true
                                        parentLogo = "LH Huawei"
                                    } else {
                                        // 如果点击的是其他Logo，隐藏子Logo行
                                        showSubLogos = false
                                        parentLogo = ""
                                    }
                                    
                                    // Logo 变化后检查是否需要关闭描述选项
                                    if watermarkType == "custom13" || watermarkType == "custom14" {
                                        let settings = WatermarkSettingsManager.shared.getSettings()
                                        let hasLogo = !logoName.isEmpty
                                        let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                                        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                        let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                        
                                        // 统计已启用的常规元素数量
                                        var enabledCount = 0
                                        if hasLogo { enabledCount += 1 }
                                        if hasSignature { enabledCount += 1 }
                                        if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素
                                        
                                        // 如果常规元素少于3个，自动关闭描述选项
                                        if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                                            WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                                        }
                                    }
                                    
                                    // 添加水印4的互斥逻辑
                                    if shouldApplyMutualExclusionLogic() {
                                        // 获取当前设置
                                        let settings = WatermarkSettingsManager.shared.getSettings()
                                        
                                        // 检查文字是否已启用
                                        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                        
                                        // 检查偏好是否已启用（不是OFF）
                                        let hasPreference = settings.preferenceOption != "OFF"
                                        
                                        // 如果文字和偏好都启用，保留最近使用的一个
                                        if hasText && hasPreference {
                                            // 这里我们保留偏好（最近使用的），关闭文字
                                            WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                            // 发送通知刷新文字UI
                                            NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                        }
                                    }
                                    
                                    // 重新应用水印样式
                                    applyCurrentWatermarkStyle()
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(selectedLogo == logoName ? UIConstants.logoSelectedColor : Color.white)
                                            .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                        
                                        Image(logoName)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: screenHeight * 0.0175)
                                    }
                                }
                            }
                            }
                        }
                        .padding(.trailing, screenWidth * 0.04)
                    }
                }
                
                    // 显示子Logo选项行
                if isCustom19LogoHandling || showSubLogos {
                        GridRow {
                        // 空占位，对齐第一行标题
                        Text(isCustom19LogoHandling ? "Logo2" : title)
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(isCustom19LogoHandling ? .white : .clear)
                            
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: screenWidth * 0.04) {
                                // 根据水印类型选择显示的Logo选项
                                if isCustom19LogoHandling {
                                    // 自定义水印19或21的第二行选项
                                    let secondRowLogos = isCustom21LogoHandling ? custom21SecondRowLogos : custom19SecondRowLogos
                                    ForEach(secondRowLogos, id: \.self) { logoName in
                                        Button(action: {
                                            // 处理Logo2选择 - 使用独立的设置项
                                            selectedLogo2 = logoName
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedLogo2, value: logoName)
                                            
                                            // 应用互斥逻辑 - 自定义水印19不应用Logo和Logo2之间的互斥
                                            if shouldApplyMutualExclusionLogic() {
                                                let settings = WatermarkSettingsManager.shared.getSettings()
                                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                                let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                                
                                                if hasText && hasPreference {
                                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                                }
                                            }
                                            
                                            // 重新应用水印样式
                                            applyCurrentWatermarkStyle()
                                        }) {
                                            ZStack {
                                                Circle()
                                                    .fill(selectedLogo2 == logoName ? UIConstants.logoSelectedColor : Color.white)
                                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                
                                                if !logoName.isEmpty {
                                                    Image(logoName)
                                                        .resizable()
                                                        .aspectRatio(contentMode: .fit)
                                                        .frame(width: screenHeight * 0.0175)
                                                } else {
                                                    // 使用DisabledOptionCircle组件保持与第一行无Logo选项一致
                                                    DisabledOptionCircle(size: screenHeight * 0.03, isSelected: selectedLogo2 == "", showSelectionBorder: false)
                                                }
                                            }
                                        }
                                    }
                                // 根据parentLogo选择显示的子Logo数组
                                } else if parentLogo == "LH DJI" {
                                    // 显示DJI子Logo
                                    ForEach(djiSubLogos, id: \.self) { subLogoName in
                                        Button(action: {
                                            // 处理子Logo选择
                                            selectedLogo = subLogoName
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: subLogoName)
                                            
                                                // 应用互斥逻辑（水印4和类似水印）
                                            if watermarkType == "custom13" || watermarkType == "custom14" {
                                                let settings = WatermarkSettingsManager.shared.getSettings()
                                                let hasLogo = !subLogoName.isEmpty
                                                let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                                let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                                
                                                var enabledCount = 0
                                                if hasLogo { enabledCount += 1 }
                                                if hasSignature { enabledCount += 1 }
                                                if hasText || hasPreference { enabledCount += 1 }
                                                
                                                    // 如果描述元素已启用但常规元素少于3个，自动关闭描述选项
                                                if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                                                }
                                            }
                                            
                                            if shouldApplyMutualExclusionLogic() {
                                                let settings = WatermarkSettingsManager.shared.getSettings()
                                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                                let hasPreference = settings.preferenceOption != "OFF"
                                                
                                                if hasText && hasPreference {
                                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                                }
                                            }
                                            
                                            // 重新应用水印样式
                                            applyCurrentWatermarkStyle()
                                        }) {
                                            ZStack {
                                                Circle()
                                                    .fill(selectedLogo == subLogoName ? UIConstants.logoSelectedColor : Color.white)
                                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                
                                                Image(subLogoName)
                                                    .resizable()
                                                    .aspectRatio(contentMode: .fit)
                                                    .frame(width: screenHeight * 0.0175)
                                            }
                                        }
                                    }
                                } else if parentLogo == "LH Huawei" {
                                    // 显示华为子Logo
                                    ForEach(huaweiSubLogos, id: \.self) { subLogoName in
                                        Button(action: {
                                            // 处理子Logo选择
                                            selectedLogo = subLogoName
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedLogo, value: subLogoName)
                                            
                                            // 应用互斥逻辑等（与上面相同）
                                            if watermarkType == "custom13" || watermarkType == "custom14" {
                                                let settings = WatermarkSettingsManager.shared.getSettings()
                                                let hasLogo = !subLogoName.isEmpty
                                                let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                                let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                                
                                                var enabledCount = 0
                                                if hasLogo { enabledCount += 1 }
                                                if hasSignature { enabledCount += 1 }
                                                if hasText || hasPreference { enabledCount += 1 }
                                                
                                                if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                                                }
                                            }
                                            
                                            if shouldApplyMutualExclusionLogic() {
                                                let settings = WatermarkSettingsManager.shared.getSettings()
                                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                                let hasPreference = settings.preferenceOption != "OFF"
                                                
                                                if hasText && hasPreference {
                                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                                }
                                            }
                                            
                                            // 重新应用水印样式
                                            applyCurrentWatermarkStyle()
                                        }) {
                                            ZStack {
                                                Circle()
                                                    .fill(selectedLogo == subLogoName ? UIConstants.logoSelectedColor : Color.white)
                                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                
                                                Image(subLogoName)
                                                    .resizable()
                                                    .aspectRatio(contentMode: .fit)
                                                    .frame(width: screenHeight * 0.0175)
                                            }
                                        }
                                    }
                                }
                            }
                            .padding(.trailing, screenWidth * 0.04)
                        }
                    }
                    }
                
                    // 如果是自定义水印21，显示第三行Logo3选项
                    if isCustom21LogoHandling {
                        GridRow {
                            Text("Logo3")
                                .font(.system(size: screenHeight * 0.02, weight: .bold))
                                .foregroundColor(.white)
                                
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: screenWidth * 0.04) {
                                    // 无Logo3选项
                                    Button(action: {
                                        selectedLogo3 = ""
                                        WatermarkSettingsManager.shared.updateSetting(\.selectedLogo3, value: "")
                                        applyCurrentWatermarkStyle()
                                    }) {
                                        DisabledOptionCircle(size: screenHeight * 0.03, isSelected: selectedLogo3 == "", showSelectionBorder: false)
                                    }
                                    
                                    // Logo3选项
                                    ForEach(custom21ThirdRowLogos.filter { !$0.isEmpty }, id: \.self) { logoName in
                                        Button(action: {
                                            selectedLogo3 = logoName
                                            WatermarkSettingsManager.shared.updateSetting(\.selectedLogo3, value: logoName)
                                            applyCurrentWatermarkStyle()
                                        }) {
                                            ZStack {
                                                Circle()
                                                    .fill(selectedLogo3 == logoName ? UIConstants.logoSelectedColor : Color.white)
                                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                
                                                Image(logoName)
                                                    .resizable()
                                                    .aspectRatio(contentMode: .fit)
                                                    .frame(width: screenHeight * 0.0175)
                                        }
                                    }
                                }
                            }
                            .padding(.trailing, screenWidth * 0.04)
                        }
                    }
                    }
                }
                .padding(.vertical, screenHeight * 0.0075)
                .onAppear {
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    selectedLogo = settings.selectedLogo
                    
                    // 为自定义水印19/21加载Logo2和Logo3设置
                    if isCustom19LogoHandling {
                        selectedLogo2 = settings.selectedLogo2
                        
                        // 为自定义水印21加载Logo3设置
                        if isCustom21LogoHandling {
                            selectedLogo3 = settings.selectedLogo3
                        }
                    }
                    
                    // 对于通用水印类型，处理子Logo显示
                    if !isCustom19LogoHandling {
                        if selectedLogo == "LH DJI" || djiSubLogos.contains(selectedLogo) {
                            // 如果当前选中的是DJI主Logo或任何一个子Logo，显示子Logo行并设置父Logo
                            showSubLogos = true
                            parentLogo = "LH DJI"
                        } else if selectedLogo == "LH Huawei" || huaweiSubLogos.contains(selectedLogo) {
                            // 如果当前选中的是华为主Logo或任何一个子Logo，显示子Logo行并设置父Logo
                            showSubLogos = true
                            parentLogo = "LH Huawei"
                        } else {
                            showSubLogos = false
                            parentLogo = ""
                        }
                    }
                }
            } else if title == "文字" || title == "署名" {
                // 文字和署名选项特殊处理
                TextInputOptionView(
                    title: title, 
                    inputText: $textInput, 
                    isEnabled: $isEnabled, 
                    isKeyboardVisible: $isKeyboardVisible, 
                    watermarkType: watermarkType, 
                    onUpdateSetting: { newValue in
                        // 根据选项类型更新不同的设置
                            if title == "文字" {
                                WatermarkSettingsManager.shared.updateSetting(\.watermarkText, value: newValue)
                            } else {
                                WatermarkSettingsManager.shared.updateSetting(\.watermarkSignature, value: newValue)
                            }
                    }, 
                    onUpdateEnabled: { newValue in
                        // 根据选项类型更新不同的设置
                            if title == "文字" {
                                WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: newValue)
                            } else {
                                WatermarkSettingsManager.shared.updateSetting(\.isWatermarkSignatureEnabled, value: newValue)
                            }
                            
                            // 元素变化时检查是否需要关闭描述选项
                            if watermarkType == "custom13" || watermarkType == "custom14" {
                                let settings = WatermarkSettingsManager.shared.getSettings()
                                let hasLogo = !settings.selectedLogo.isEmpty
                                let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                                let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"
                                
                                // 统计已启用的常规元素数量
                                var enabledCount = 0
                                if hasLogo { enabledCount += 1 }
                                if hasSignature { enabledCount += 1 }
                                if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素
                                
                                // 如果常规元素少于3个，自动关闭描述选项
                                if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                                    WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                                }
                            }
                    }, 
                    onApplyStyle: {
                            applyCurrentWatermarkStyle()
                        }
                )
                .onAppear {
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    textInput = title == "文字" ? settings.watermarkText : settings.watermarkSignature
                    isEnabled = title == "文字" ? settings.isWatermarkTextEnabled : settings.isWatermarkSignatureEnabled
                }
                // 添加通知监听，用于刷新UI状态
                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RefreshWatermarkTextUI"))) { _ in
                    if title == "文字" {
                        let settings = WatermarkSettingsManager.shared.getSettings()
                        isEnabled = settings.isWatermarkTextEnabled
                    }
                }
            } else if title == "描述" {
                // 描述选项特殊处理
                TextInputOptionView(
                    title: title, 
                    inputText: $text2Input, 
                    isEnabled: $isText2Enabled, 
                    isKeyboardVisible: $isKeyboardVisible, 
                    watermarkType: watermarkType, 
                    onUpdateSetting: { newValue in
                        // 更新描述设置
                        WatermarkSettingsManager.shared.updateSetting(\.watermarkDescription, value: newValue)
                    }, 
                    onUpdateEnabled: { newValue in
                        // 简单地更新描述启用状态
                        WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: newValue)
                    }, 
                    onApplyStyle: {
                        applyCurrentWatermarkStyle()
                    }
                )
                .onAppear {
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    text2Input = settings.watermarkDescription
                    isText2Enabled = settings.isWatermarkDescriptionEnabled
                }
                // 不再需要特别的通知监听
            } else if title == "偏好" {
                VStack(alignment: .leading, spacing: 8) {
                HStack(spacing: screenWidth * 0.06) {
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                        // 偏好切换器 - 不使用滚动视图
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(availablePreferenceOptions(for: watermarkType), id: \.self) { option in
                            Button(action: {
                                // 使用新的切换逻辑
                                togglePreference(option)
                                
                                // 更新设置 - 同时更新旧格式(preferenceOption)和新格式(selectedPreferences,preferenceSelectionOrder)
                                // 旧格式保留第一个选择作为兼容性设置
                                let preferenceValue = selectedPreferences.isEmpty ? "OFF" : selectedPreferences.first!.rawValue
                                WatermarkSettingsManager.shared.updateSetting(\.preferenceOption, value: preferenceValue)
                                // 新格式 - 保存所有选择的选项
                                WatermarkSettingsManager.shared.updateSetting(\.selectedPreferences, value: selectedPreferences.map { $0.rawValue })
                                // 新格式 - 保存选择顺序
                                WatermarkSettingsManager.shared.updateSetting(\.preferenceSelectionOrder, value: selectedPreferences.map { $0.rawValue })
                                
                                // 偏好选项变化后检查是否需要关闭描述选项
                                if watermarkType == "custom13" || watermarkType == "custom14" {
                                    let settings = WatermarkSettingsManager.shared.getSettings()
                                    let hasLogo = !settings.selectedLogo.isEmpty
                                    let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
                                    let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                    let hasPreference = !selectedPreferences.isEmpty || option != .off
                                    
                                    // 统计已启用的常规元素数量
                                    var enabledCount = 0
                                    if hasLogo { enabledCount += 1 }
                                    if hasSignature { enabledCount += 1 }
                                    if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素
                                    
                                    // 如果常规元素少于3个，自动关闭描述选项
                                    if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                                        WatermarkSettingsManager.shared.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
                                    }
                                }
                                            
                                    // 初始化偏好输入文本
                                if option != .off && isPreferenceSelected(option) {
                                        let settings = WatermarkSettingsManager.shared.getSettings()
                                        switch option {
                                        case .parameters:
                                        // 解析参数文本
                                        let parametersText = settings.preferenceParametersText
                                        parseParametersText(parametersText)
                                            preferenceInputText = settings.preferenceParametersText
                                        case .location:
                                        // 解析经纬度
                                        let locationText = settings.preferenceLocationText
                                        // 尝试解析格式为 "X°E Y°N" 的字符串
                                        if locationText.contains("°E") && locationText.contains("°N") {
                                            let components = locationText.components(separatedBy: "°E")
                                            if components.count > 1 {
                                                longitudeText = components[0].trimmingCharacters(in: .whitespaces)
                                                if let northPart = components[1].components(separatedBy: "°N").first {
                                                    latitudeText = northPart.trimmingCharacters(in: .whitespaces)
                                                }
                                            }
                                        } else {
                                            // 无法解析，显示原始文本
                                            preferenceInputText = locationText
                                            longitudeText = ""
                                            latitudeText = ""
                                        }
                                        case .date:
                                            preferenceInputText = settings.preferenceDateText
                                        case .place:
                                            preferenceInputText = settings.preferencePlaceText
                                        default:
                                            preferenceInputText = ""
                                        }
                                } else if selectedPreferences.isEmpty {
                                        preferenceInputText = ""
                                    }
                                
                                // 添加互斥逻辑
                                if shouldApplyMutualExclusionLogic() && option != .off && isPreferenceSelected(option) {
                                    // 获取当前设置
                                    let settings = WatermarkSettingsManager.shared.getSettings()
                                    
                                    // 检查文字是否已启用
                                    let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
                                    
                                    // 如果文字已启用，则关闭文字(仅禁用，不清除内容)
                                    if hasText {
                                        WatermarkSettingsManager.shared.updateSetting(\.isWatermarkTextEnabled, value: false)
                                        // 发送通知刷新文字UI
                                        NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkTextUI"), object: nil)
                                    }
                                }
                                
                                applyCurrentWatermarkStyle()
                            }) {
                                Text(option.rawValue)
                                        .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(isPreferenceSelected(option) ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                        .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(isPreferenceSelected(option) ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                        .padding(.trailing, screenWidth * 0.04)
                    
                    Spacer() // 添加Spacer()确保布局与TextInputOptionView一致
                    }
                    
                    // 分行显示开关 - 只有在选中两个偏好时显示
                    if selectedPreferences.count == 2 {
                        HStack(spacing: standardSpacing) { 
                            Text("分行")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                            
                            Spacer()
                            
                            // 分行显示开关
                            Toggle("", isOn: $isMultilinePreferenceEnabled)
                                .labelsHidden()
                                .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                                .scaleEffect(0.75)
                                .onChange(of: isMultilinePreferenceEnabled) { oldValue, newValue in
                                // 更新设置
                                WatermarkSettingsManager.shared.updateSetting(\.isMultilinePreferenceEnabled, value: newValue)
                                // 重新应用水印样式
                                applyCurrentWatermarkStyle()
                                }
                        }
                        .padding(.vertical, screenHeight * 0.0075)
                    }
                    
                    // 当选择了任何选项时，使用TextInputOptionView组件显示输入框
                    if hasAnyPreferenceSelected {
                        // 使用垂直排列方式，显示所有选中的偏好选项
                        VStack(spacing: 8) {
                            // 遍历所有选中的偏好选项
                            ForEach(selectedPreferences, id: \.self) { option in
                                // 为每个选项创建对应的输入视图
                                if option == .location {
                                    // 经度输入
                        TextInputOptionView(
                                        title: "经度",
                                        inputText: $longitudeText,
                                        isEnabled: .constant(true),
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并经度和纬度值
                                            let combinedText = "\(newValue)°E \(latitudeText)°N"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceLocationText, value: combinedText)
                                        },
                                        onUpdateEnabled: { _ in },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: false
                                    )
                                    
                                    // 纬度输入
                                    TextInputOptionView(
                                        title: "纬度",
                                        inputText: $latitudeText,
                                        isEnabled: .constant(true),
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并经度和纬度值
                                            let combinedText = "\(longitudeText)°E \(newValue)°N"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceLocationText, value: combinedText)
                                        },
                                        onUpdateEnabled: { _ in },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: false
                                    )
                                } else if option == .parameters {
                                    // 焦距输入
                                    TextInputOptionView(
                                        title: "焦距",
                                        inputText: $focalLengthText,
                                        isEnabled: $isFocalLengthEnabled,
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并所有参数值
                                            let combinedText = isFocalLengthEnabled ? "\(newValue)mm f/\(apertureText) \(shutterSpeedText)s ISO\(isoText)" : "f/\(apertureText) \(shutterSpeedText)s ISO\(isoText)"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: combinedText)
                                        },
                                        onUpdateEnabled: { newValue in
                                            // 更新焦距显示状态
                                            isFocalLengthEnabled = newValue
                                            // 合并所有参数值，根据开关状态决定是否包含焦距
                                            let combinedText = newValue ? "\(focalLengthText)mm f/\(apertureText) \(shutterSpeedText)s ISO\(isoText)" : "f/\(apertureText) \(shutterSpeedText)s ISO\(isoText)"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: combinedText)
                                        },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: true
                                    )
                                    
                                    // 光圈输入
                                    TextInputOptionView(
                                        title: "光圈",
                                        inputText: $apertureText,
                                        isEnabled: .constant(true),
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并所有参数值
                                            let combinedText = isFocalLengthEnabled ? "\(focalLengthText)mm f/\(newValue) \(shutterSpeedText)s ISO\(isoText)" : "f/\(newValue) \(shutterSpeedText)s ISO\(isoText)"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: combinedText)
                                        },
                                        onUpdateEnabled: { _ in },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: false
                                    )
                                    
                                    // 快门速度输入
                                    TextInputOptionView(
                                        title: "快门",
                                        inputText: $shutterSpeedText,
                                        isEnabled: .constant(true),
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并所有参数值
                                            let combinedText = isFocalLengthEnabled ? "\(focalLengthText)mm f/\(apertureText) \(newValue)s ISO\(isoText)" : "f/\(apertureText) \(newValue)s ISO\(isoText)"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: combinedText)
                                        },
                                        onUpdateEnabled: { _ in },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: false
                                    )
                                    
                                    // ISO输入
                                    TextInputOptionView(
                                        title: "感光",
                                        inputText: $isoText,
                                        isEnabled: .constant(true),
                                        isKeyboardVisible: $isKeyboardVisible,
                                        watermarkType: watermarkType,
                                        onUpdateSetting: { newValue in
                                            // 合并所有参数值
                                            let combinedText = isFocalLengthEnabled ? "\(focalLengthText)mm f/\(apertureText) \(shutterSpeedText)s ISO\(newValue)" : "f/\(apertureText) \(shutterSpeedText)s ISO\(newValue)"
                                            WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: combinedText)
                                        },
                                        onUpdateEnabled: { _ in },
                                        onApplyStyle: {
                                            applyCurrentWatermarkStyle()
                                        },
                                        showToggle: false
                                    )
                                } else {
                                    // 其他简单选项的单个输入框
                                    TextInputOptionView(
                                        title: getPreferenceTitle(for: option),
                                        inputText: getBindingForPreferenceOption(option),
                                        isEnabled: .constant(true),
                            isKeyboardVisible: $isKeyboardVisible,
                            watermarkType: watermarkType,
                            onUpdateSetting: { newValue in
                                // 根据不同的偏好选项，更新不同的设置字段
                                            switch option {
                                case .parameters:
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceParametersText, value: newValue)
                                case .location:
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceLocationText, value: newValue)
                                case .date:
                                    WatermarkSettingsManager.shared.updateSetting(\.preferenceDateText, value: newValue)
                                                dateText = newValue // 更新本地状态
                                case .place:
                                    WatermarkSettingsManager.shared.updateSetting(\.preferencePlaceText, value: newValue)
                                                placeText = newValue // 更新本地状态
                                default:
                                    break
                                }
                            },
                                        onUpdateEnabled: { _ in },
                            onApplyStyle: {
                                applyCurrentWatermarkStyle()
                            },
                                        showToggle: false
                        )
                                }
                            }
                        }
                        
                        // 分行显示开关已移至偏好选项下方
                    }
                }
                .padding(.vertical, screenHeight * 0.0075)
                .onAppear {
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    // 加载保存的分行开关状态
                    isMultilinePreferenceEnabled = settings.isMultilinePreferenceEnabled
                    // 加载保存的右侧宽边框状态
                    isRightWideBorderEnabled = settings.isRightWideBorderEnabled
                    
                    // 临时处理，只处理一个选项，后续需要扩展以支持多选
                    if let option = PreferenceOption(rawValue: settings.preferenceOption) {
                        // 清空当前选择
                        selectedPreferences = []
                        
                        // 添加新选择（如果不是OFF）
                        if option != .off {
                            selectedPreferences.append(option)
                        }
                        
                        // 加载保存的偏好输入文本
                        if option != .off {
                            switch option {
                            case .parameters:
                                // 解析参数文本
                                let parametersText = settings.preferenceParametersText
                                parseParametersText(parametersText)
                                preferenceInputText = settings.preferenceParametersText
                                // 根据参数文本判断焦距是否启用
                                isFocalLengthEnabled = parametersText.contains("mm") && !parametersText.hasPrefix("f/")
                            case .location:
                                // 解析经纬度
                                let locationText = settings.preferenceLocationText
                                // 尝试解析格式为 "X°E Y°N" 的字符串
                                if locationText.contains("°E") && locationText.contains("°N") {
                                    let components = locationText.components(separatedBy: "°E")
                                    if components.count > 1 {
                                        longitudeText = components[0].trimmingCharacters(in: .whitespaces)
                                        if let northPart = components[1].components(separatedBy: "°N").first {
                                            latitudeText = northPart.trimmingCharacters(in: .whitespaces)
                                        }
                                    }
                                } else {
                                    // 无法解析，显示原始文本
                                    preferenceInputText = locationText
                                    longitudeText = ""
                                    latitudeText = ""
                                }
                            case .date:
                                preferenceInputText = settings.preferenceDateText
                            case .place:
                                preferenceInputText = settings.preferencePlaceText
                            default:
                                preferenceInputText = ""
                            }
                        }
                    }
                }
                // 添加通知监听，用于刷新UI状态
                .onReceive(NotificationCenter.default.publisher(for: Notification.Name("RefreshWatermarkPreferenceUI"))) { _ in
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    
                    // 加载保存的分行开关状态
                    isMultilinePreferenceEnabled = settings.isMultilinePreferenceEnabled
                    
                    // 首先检查新格式
                    if !settings.selectedPreferences.isEmpty {
                        // 清空当前选择
                        selectedPreferences = []
                        
                        // 添加所有保存的选择
                        for prefString in settings.selectedPreferences {
                            if let option = PreferenceOption(rawValue: prefString), option != .off {
                                selectedPreferences.append(option)
                                
                                // 初始化对应选项的文本
                                switch option {
                                case .date:
                                    dateText = settings.preferenceDateText
                                case .place:
                                    placeText = settings.preferencePlaceText
                                case .parameters:
                                    parseParametersText(settings.preferenceParametersText)
                                case .location:
                                    let locationText = settings.preferenceLocationText
                                    if locationText.contains("°E") && locationText.contains("°N") {
                                        let components = locationText.components(separatedBy: "°E")
                                        if components.count > 1 {
                                            longitudeText = components[0].trimmingCharacters(in: .whitespaces)
                                            if let northPart = components[1].components(separatedBy: "°N").first {
                                                latitudeText = northPart.trimmingCharacters(in: .whitespaces)
                                            }
                                        }
                                    }
                                default:
                                    break
                                }
                            }
                        }
                    } else {
                        // 向后兼容：使用旧格式
                    if let option = PreferenceOption(rawValue: settings.preferenceOption) {
                            // 清空当前选择
                            selectedPreferences = []
                            
                            // 添加新选择（如果不是OFF）
                            if option != .off {
                                selectedPreferences.append(option)
                                
                                // 初始化对应选项的文本
                                switch option {
                                case .date:
                                    dateText = settings.preferenceDateText
                                case .place:
                                    placeText = settings.preferencePlaceText
                                case .parameters:
                                    parseParametersText(settings.preferenceParametersText)
                                case .location:
                                    let locationText = settings.preferenceLocationText
                                    if locationText.contains("°E") && locationText.contains("°N") {
                                        let components = locationText.components(separatedBy: "°E")
                                        if components.count > 1 {
                                            longitudeText = components[0].trimmingCharacters(in: .whitespaces)
                                            if let northPart = components[1].components(separatedBy: "°N").first {
                                                latitudeText = northPart.trimmingCharacters(in: .whitespaces)
                                            }
                                        }
                                    }
                                default:
                                    break
                                }
                            }
                        }
                    }
                }
            } else if title == "位置" {
                // 位置选项特殊处理
                HStack(spacing: screenWidth * 0.06) {
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 位置切换器
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(positionOptionsForWatermarkType(watermarkType), id: \.self) { option in
                            Button(action: {
                                selectedPosition = option
                                // 更新设置并应用水印
                                WatermarkSettingsManager.shared.updateSetting(\.positionOption, value: option.rawValue)
                                applyCurrentWatermarkStyle()
                            }) {
                                Text(option.rawValue)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))  // 文本大小为1.5%
                                    .foregroundColor(selectedPosition == option ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)  // 垂直内边距0.5%屏幕高度
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(selectedPosition == option ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, screenHeight * 0.0075)
                .onAppear {
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    let currentSavedOptionString = settings.positionOption
                    let validOptionsForCurrentType = positionOptionsForWatermarkType(watermarkType)

                    if let currentSavedOption = PositionOption(rawValue: currentSavedOptionString), validOptionsForCurrentType.contains(currentSavedOption) {
                        selectedPosition = currentSavedOption
                    } else if let firstValidOption = validOptionsForCurrentType.first {
                        selectedPosition = firstValidOption
                        WatermarkSettingsManager.shared.updateSetting(\.positionOption, value: firstValidOption.rawValue)
                    }
                }
                .onChange(of: watermarkType) { oldValue, newValue in // 正确的参数顺序：oldValue, newValue
                    // 当水印类型改变时，重新评估并设置 selectedPosition
                    let settings = WatermarkSettingsManager.shared.getSettings()
                    let currentSavedOptionString = settings.positionOption
                    let validOptionsForNewType = positionOptionsForWatermarkType(newValue) // 使用新的watermarkType (newValue)

                    if let currentSavedOption = PositionOption(rawValue: currentSavedOptionString), validOptionsForNewType.contains(currentSavedOption) {
                        // 如果当前保存的选项对新类型有效，则使用它
                        selectedPosition = currentSavedOption
                    } else if let firstValidOption = validOptionsForNewType.first {
                        // 否则，使用新类型的第一个有效选项
                        selectedPosition = firstValidOption
                        // 并且更新设置以保持同步
                        WatermarkSettingsManager.shared.updateSetting(\.positionOption, value: firstValidOption.rawValue)
                        // 主动应用一次水印，确保切换类型时位置立即生效
                        // applyCurrentWatermarkStyle() // 考虑是否需要，因为外部类型切换已经会触发
                    } else {
                        print("⚠️ WatermarkOptionItem: 位置选项在类型切换时无法确定有效选项 for \(newValue)")
                    }
                }
            } else {
                // 其他选项保持原样
                HStack {
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.gray)
                }
                .padding(.vertical, screenHeight * 0.0075)
            }
        }
    }
    
    // 获取偏好选项对应的标题
    private func getPreferenceTitle(for option: PreferenceOption) -> String {
        switch option {
        case .parameters:
            return "参数"
        case .location:
            return "经纬度"
        case .date:
            return "日期"
        case .place:
            return "位置"
        default:
            return ""
        }
    }
    
    // 计算属性：检查是否有任何偏好选择（等同于原来的selectedPreference != .off）
    private var hasAnyPreferenceSelected: Bool {
        return !selectedPreferences.isEmpty
    }
    
    // 辅助方法：检查特定选项是否被选中
    private func isPreferenceSelected(_ option: PreferenceOption) -> Bool {
        return selectedPreferences.contains(option)
    }
    
    // 新增：根据偏好选项类型返回对应的Binding
    private func getBindingForPreferenceOption(_ option: PreferenceOption) -> Binding<String> {
        switch option {
        case .date:
            return $dateText
        case .place:
            return $placeText
        default:
            return $preferenceInputText
        }
    }
    
    // 辅助方法：添加或移除选项
    private func togglePreference(_ option: PreferenceOption) {
        // 如果是OFF选项，则清空所有选择
        if option == .off {
            selectedPreferences = []
            return
        }
        
        // 如果该选项已选中，则移除它
        if isPreferenceSelected(option) {
            selectedPreferences.removeAll(where: { $0 == option })
            return
        }
        
        // 如果已有两个选择，且要添加新选择，则移除最早的选择
        if selectedPreferences.count >= maxPreferenceSelections {
            selectedPreferences.removeFirst()
        }
        
        // 添加新选择
        selectedPreferences.append(option)
        
        // 初始化对应选项的输入文本
        let settings = WatermarkSettingsManager.shared.getSettings()
        switch option {
        case .parameters:
            // 解析参数文本
            let parametersText = settings.preferenceParametersText
            parseParametersText(parametersText)
            // 根据参数文本判断焦距是否启用
            isFocalLengthEnabled = parametersText.contains("mm") && !parametersText.hasPrefix("f/")
        case .location:
            // 解析经纬度
            let locationText = settings.preferenceLocationText
            // 尝试解析格式为 "X°E Y°N" 的字符串
            if locationText.contains("°E") && locationText.contains("°N") {
                let components = locationText.components(separatedBy: "°E")
                if components.count > 1 {
                    longitudeText = components[0].trimmingCharacters(in: .whitespaces)
                    if let northPart = components[1].components(separatedBy: "°N").first {
                        latitudeText = northPart.trimmingCharacters(in: .whitespaces)
                    }
                }
            } else {
                longitudeText = ""
                latitudeText = ""
            }
        case .date:
            dateText = settings.preferenceDateText
        case .place:
            placeText = settings.preferencePlaceText
        default:
            break
        }
    }
    
    // 解析参数文本 (格式: "50mm f/1.8 1/125s ISO100")
    private func parseParametersText(_ text: String) {
        // 初始化参数为默认值
        focalLengthText = "50"
        apertureText = "1.8"
        shutterSpeedText = "1/125"
        isoText = "100"
        
        // 如果文本为空或不包含所需信息，使用默认值
        if text.isEmpty {
            return
        }
        
        // 解析焦距 (50mm)
        if let focalLengthRange = text.range(of: "\\d+mm", options: .regularExpression) {
            let focalLength = text[focalLengthRange]
            focalLengthText = focalLength.replacingOccurrences(of: "mm", with: "")
        }
        
        // 解析光圈 (f/X.X)
        if let apertureRange = text.range(of: "f/\\d+(\\.\\d+)?", options: .regularExpression) {
            let aperture = text[apertureRange]
            apertureText = aperture.replacingOccurrences(of: "f/", with: "")
        }
        
        // 解析快门速度 (X/Xs或X.Xs)
        if let shutterRange = text.range(of: "\\d+(/\\d+)?(\\.\\d+)?s", options: .regularExpression) {
            let shutter = text[shutterRange]
            shutterSpeedText = shutter.replacingOccurrences(of: "s", with: "")
        }
        
        // 解析ISO值 (ISOXXX)
        if let isoRange = text.range(of: "ISO\\d+", options: .regularExpression) {
            let iso = text[isoRange]
            isoText = iso.replacingOccurrences(of: "ISO", with: "")
        }
        
        // 根据文本格式判断焦距是否启用
        isFocalLengthEnabled = text.contains("mm") && !text.hasPrefix("f/")
    }
    
    // 辅助方法：根据水印类型获取可用的偏好选项
    private func availablePreferenceOptions(for watermarkType: String) -> [PreferenceOption] {
        // 自定义水印10、16和25只显示OFF和参数选项
        if watermarkType == "custom10" || watermarkType == "custom16" || watermarkType == "custom25" {
            return [.off, .parameters]
        }
        // 其他水印类型显示所有选项
        return PreferenceOption.allCases
    }
}
