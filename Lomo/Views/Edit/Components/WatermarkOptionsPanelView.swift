import SwiftUI

// MARK: - 水印选项面板
/// 水印选项面板组件 - 第6步UI组件拆分
/// **重要：这是一个新的独立组件，不会影响原始的WatermarkControlView**
/// **目标：提供可复用的水印选项设置UI组件**
struct WatermarkOptionsPanelView: View {
    
    // MARK: - 属性
    let settings: WatermarkSettings
    @Binding var isKeyboardVisible: Bool
    let onTextChanged: (String) -> Void
    let onTextToggled: (Bool) -> Void
    
    // MARK: - 私有属性
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // MARK: - Body
    var body: some View {
        ScrollView {
            VStack(spacing: screenHeight * 0.02) {
                // 文字选项
                textOptionView
                
                // 署名选项
                signatureOptionView
                
                // Logo选项
                logoOptionView
                
                // 偏好选项
                preferenceOptionView
                
                // 位置选项
                positionOptionView
            }
            .padding(.horizontal, screenWidth * 0.04)
            .padding(.vertical, screenHeight * 0.02)
        }
        .background(Color.black.opacity(0.05))
    }
    
    // MARK: - 子视图
    
    /// 文字选项视图
    private var textOptionView: some View {
        WatermarkTextOptionView(
            title: "文字",
            text: settings.watermarkText,
            isEnabled: settings.isWatermarkTextEnabled,
            isKeyboardVisible: $isKeyboardVisible,
            onTextChanged: onTextChanged,
            onToggled: onTextToggled
        )
    }
    
    /// 署名选项视图
    private var signatureOptionView: some View {
        WatermarkTextOptionView(
            title: "署名",
            text: settings.watermarkSignature,
            isEnabled: settings.isWatermarkSignatureEnabled,
            isKeyboardVisible: $isKeyboardVisible,
            onTextChanged: { _ in }, // TODO: 实现署名更新
            onToggled: { _ in } // TODO: 实现署名切换
        )
    }
    
    /// Logo选项视图
    private var logoOptionView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Logo")
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)
            
            Text("当前Logo: \(settings.selectedLogo.isEmpty ? "无" : settings.selectedLogo)")
                .font(.system(size: screenHeight * 0.015))
                .foregroundColor(.gray)
            
            // TODO: 实现Logo选择UI
        }
    }
    
    /// 偏好选项视图
    private var preferenceOptionView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("偏好")
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)
            
            Text("当前偏好: \(settings.preferenceOption)")
                .font(.system(size: screenHeight * 0.015))
                .foregroundColor(.gray)
            
            // TODO: 实现偏好选择UI
        }
    }
    
    /// 位置选项视图
    private var positionOptionView: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("位置")
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)
            
            Text("当前位置: \(settings.positionOption)")
                .font(.system(size: screenHeight * 0.015))
                .foregroundColor(.gray)
            
            // TODO: 实现位置选择UI
        }
    }
}

// MARK: - 文字选项组件
/// 文字选项组件
private struct WatermarkTextOptionView: View {
    let title: String
    let text: String
    let isEnabled: Bool
    @Binding var isKeyboardVisible: Bool
    let onTextChanged: (String) -> Void
    let onToggled: (Bool) -> Void
    
    @State private var localText: String = ""
    
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        HStack(spacing: screenWidth * 0.06) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(.white)
            
            TextField("请输入\(title)", text: $localText)
                .font(.system(size: screenHeight * 0.015, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, screenHeight * 0.0075)
                .background(Color(uiColor: .systemGray5))
                .cornerRadius(6)
                .onTapGesture {
                    isKeyboardVisible = true
                }
                .onChange(of: localText) { _, newValue in
                    onTextChanged(newValue)
                }
            
            Toggle("", isOn: Binding(
                get: { isEnabled },
                set: { onToggled($0) }
            ))
            .labelsHidden()
        }
        .onAppear {
            localText = text
        }
        .onChange(of: text) { _, newValue in
            localText = newValue
        }
    }
}

// MARK: - 预览
#Preview {
    let settings = WatermarkSettings()
    return WatermarkOptionsPanelView(
        settings: settings,
        isKeyboardVisible: .constant(false),
        onTextChanged: { text in
            print("文字变更: \(text)")
        },
        onTextToggled: { enabled in
            print("文字切换: \(enabled)")
        }
    )
    .background(Color.black)
}
