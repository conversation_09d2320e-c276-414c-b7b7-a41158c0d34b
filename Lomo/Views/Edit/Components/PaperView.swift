import SwiftUI

/// 相纸控制视图 - MVVM-S架构 (重构版)
/// 使用通用PresetSelectionView组件，消除重复代码
struct PaperView: View {
    // MARK: - 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    
    // MARK: - MVVM-S架构：使用PaperViewModel
    @StateObject private var paperViewModel: PaperViewModel
    
    // MARK: - 初始化方法
    
    /// 依赖注入初始化
    /// - Parameter paperViewModel: 相纸视图模型
    init(paperViewModel: PaperViewModel) {
        self._paperViewModel = StateObject(wrappedValue: paperViewModel)
    }
    
    /// 便捷初始化方法 - 使用依赖注入容器
    init() {
        self._paperViewModel = StateObject(wrappedValue: PaperDependencyContainer.paperViewModel())
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 使用通用组件创建各种相纸类型选择
                ForEach(Array(zip(PaperConstants.presetTypes, PaperConstants.presetNames)), id: \.0) { typeString, displayName in
                    if let presetType = PaperViewModel.PresetType(rawValue: typeString) {
                        PresetSelectionView(
                            title: displayName,
                            presetType: presetType,
                            presetsCount: PaperConstants.presetsPerType,
                            isSelected: { index in
                                paperViewModel.activePaperType == typeString && 
                                paperViewModel.activePaperPresetIndex == index
                            },
                            onSelection: { index in
                                paperViewModel.selectPreset(type: presetType, index: index)
                            }
                        )
                    }
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * PaperConstants.bottomSafeAreaHeight)
        }
        .frame(maxWidth: .infinity)
    }
}

// MARK: - 预览
#Preview {
    PaperView()
        .background(Color.black)
}
