// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI
import Photos

// 新增：用于显示单个照片/视频的网格单元格视图
struct GalleryGridItemView: View {
    let asset: PHAsset
    @ObservedObject var viewModel: GalleryViewModel
    let imageSize: CGSize
    let watermarkConfiguration: WatermarkConfigurationProtocol?

    @State private var thumbnail: UIImage? = nil
    
    // 计算当前照片是否可选
    private var isSelectable: Bool {
        // 如果不是拼图水印选择模式，所有照片都可选
        if !viewModel.isPuzzleWatermarkSelectionMode() {
            return true
        }
        
        // 如果照片已被选中，则可选（可以取消选择）
        if viewModel.isAssetSelected(asset) {
            return true
        }
        
        // 获取当前激活的水印类型所需的照片数量
        guard let watermarkConfig = watermarkConfiguration else {
            return true // 如果没有水印配置，默认可选
        }
        
        let watermarkType = watermarkConfig.getActiveWatermarkType()
        let requiredPhotoCount = watermarkConfig.getRequiredPhotoCount(for: watermarkType)
        
        // 如果已选择了足够数量的照片，且当前照片未被选中，则不可选
        if viewModel.selectedAssets.count >= requiredPhotoCount {
            return false
        }
        
        // 其他情况都可选
        return true
    }
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            // 占位符或实际图片
            Group {
                if let image = thumbnail {
                    Image(uiImage: image)
                        .resizable()
                } else {
                    // 加载时显示灰色占位符
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                }
            }
            .aspectRatio(1, contentMode: .fill) // 保持 1:1 宽高比
            .frame(height: imageSize.height) // 使用传入的高度
            .clipped() // 裁剪内容
            
            // 视频标记
            if asset.mediaType == .video {
                Image(systemName: "video.fill")
                    .font(.system(size: UIScreen.main.bounds.height * 0.015))
                    .foregroundColor(.white)
                    .padding(6)
                    .background(Color.black.opacity(0.3).blur(radius: 3))
                    .clipShape(Circle())
                    .padding(4) // 给图标一点边距
            }
            
            // 选择模式下显示的选择指示器
            if viewModel.isSelectionMode {
                ZStack {
                    Circle()
                        .stroke(Color.white, lineWidth: 2)
                        .background(
                            Circle()
                                .fill(viewModel.isAssetSelected(asset) ? Color.blue : Color.clear)
                        )
                        .frame(width: 22, height: 22)
                    
                    if viewModel.isAssetSelected(asset) {
                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(.white)
                    }
                }
                .padding(8)
                .transition(.scale)
                // 当照片不可选时，选择指示器显示为灰色
                .opacity(isSelectable ? 1.0 : 0.3)
            }
        }
        .overlay(
            Group {
                if viewModel.isSelectionMode && viewModel.isAssetSelected(asset) {
                    // 已选中的照片显示蓝色半透明覆盖层
                    Color.blue.opacity(0.2)
                } else if viewModel.isSelectionMode && !isSelectable {
                    // 不可选的照片显示灰色半透明覆盖层
                    Color.black.opacity(0.5)
                } else {
                    // 其他情况不显示覆盖层
                    Color.clear
                }
            }
        )
        .onAppear {
            // 异步加载缩略图
            GalleryService.shared.getThumbnail(for: asset, size: imageSize) { image in
                self.thumbnail = image
            }
        }
    }
}

struct GalleryView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 照片网格布局
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    // 计算单元格尺寸
    private var itemSize: CGSize {
        let itemWidth = (screenWidth - 8) / 3 // 3列，中间2个间距4
        return CGSize(width: itemWidth, height: itemWidth) // 正方形
    }
    
    // 引入 ViewModel
    @ObservedObject var viewModel: GalleryViewModel
    @ObservedObject var sharedTabViewModel: SharedTabViewModel

    // 水印配置协议
    private let watermarkConfiguration: WatermarkConfigurationProtocol?

    // 添加状态变量用于跟踪选中的主选项卡
    @State private var selectedMainTab: MainGalleryTab = .myWorks

    // 添加初始化方法
    init(viewModel: GalleryViewModel, sharedTabViewModel: SharedTabViewModel, watermarkConfiguration: WatermarkConfigurationProtocol? = nil) {
        self.viewModel = viewModel
        self.sharedTabViewModel = sharedTabViewModel
        self.watermarkConfiguration = watermarkConfiguration
    }
    
    // 存储系统相册分类 (从ViewModel获取)
    // @State private var albumCategories: [AlbumCategory] = []
    
    var body: some View {
        ZStack {
            VStack(spacing: 0) {
                // 照片网格
                ScrollView {
                    LazyVGrid(columns: columns, spacing: 4) {
                        // 修改为遍历 viewModel.photoAssets
                        ForEach(viewModel.photoAssets, id: \.localIdentifier) { asset in
                            // 使用新的 GalleryGridItemView
                            GalleryGridItemView(asset: asset, viewModel: viewModel, imageSize: itemSize, watermarkConfiguration: watermarkConfiguration)
                                .onTapGesture {
                                    handleAssetTap(asset)
                                }
                        }
                    }
                    .padding(.horizontal, 2)
                    .padding(.top, screenHeight * 0.04) // 保留关键的顶部 padding
                }
            }
            .background(Color(uiColor: .systemGray6).edgesIgnoringSafeArea(.all))
            
            // 顶部选项卡
            VStack {
                HStack {
                    // 左侧选项卡组
                    HStack(spacing: screenWidth * 0.06) {
                        tabButton(title: "我的作品", isSelected: selectedMainTab == .myWorks, tab: .myWorks)
                        tabButton(title: "相册", isSelected: selectedMainTab == .gallery, tab: .gallery)
                    }
                    
                    Spacer()
                    
                    // 右侧按钮：选择模式下显示取消和全选，非选择模式下显示选择
                    if viewModel.isSelectionMode {
                        // 已选择数量显示 - 为拼图水印特殊处理
                        if viewModel.isPuzzleWatermarkSelectionMode() {
                            Text(viewModel.getPuzzleWatermarkSelectionCountText())
                                .font(.system(size: screenHeight * 0.018))
                                .foregroundColor(.white)
                                .padding(.trailing, 10)
                        } else {
                        Text("\(viewModel.selectedAssets.count)/\(viewModel.photoAssets.count)")
                            .font(.system(size: screenHeight * 0.018))
                            .foregroundColor(.white)
                            .padding(.trailing, 10)
                        }
                        
                        // 取消按钮
                        Button(action: {
                            exitSelectionMode()
                        }) {
                            Text("取消")
                                .font(.system(size: screenHeight * 0.018))
                                .foregroundColor(.white)
                        }
                    } else {
                        // 选择按钮
                        Button(action: {
                            enterSelectionMode()
                        }) {
                            Text("选择")
                                .font(.system(size: screenHeight * 0.02))
                                .foregroundColor(.white)
                        }
                    }
                }
                .padding(.horizontal, screenWidth * 0.05)
                .padding(.vertical, screenHeight * 0.005)
                
                Spacer()
            }
        }
        .onAppear {
            // 加载相册分类，权限检查在 viewModel 中处理
            viewModel.loadAlbumCategories()
            
            // 检查是否需要直接进入选择模式 (从其他页面跳转过来)
            if sharedTabViewModel.shouldEnterGallerySelectionMode {
                enterSelectionMode()
                // 重置标志位，避免下次进入时再次触发
                sharedTabViewModel.shouldEnterGallerySelectionMode = false
            }
        }
        .onChange(of: viewModel.selectedAssets) { newAssets in
            // 同步选中照片的 PHAsset 数组到 SharedTabViewModel
            sharedTabViewModel.currentGallerySelectedAssets = newAssets
        }
        .onChange(of: viewModel.selectedAssets.count) { newCount in
            // 同步选中照片的数量到 SharedTabViewModel
            sharedTabViewModel.selectedPhotosCount = newCount
        }
        .onDisappear {
            // 离开视图时确保清理选择模式状态
            if viewModel.isSelectionMode {
                viewModel.toggleSelectionMode(false)
                sharedTabViewModel.isSelectionMode = false
                sharedTabViewModel.selectedPhotosCount = 0
            }
        }
        // 添加拼图水印选择提示的弹窗
        .alert(isPresented: $viewModel.showPuzzleWatermarkAlert) {
            Alert(
                title: Text("照片选择提示"),
                message: Text(viewModel.puzzleWatermarkAlertMessage),
                dismissButton: .default(Text("好的"))
            )
        }
    }
    
    // MARK: - 辅助方法
    
    // 处理照片点击
    private func handleAssetTap(_ asset: PHAsset) {
        if viewModel.isSelectionMode {
            // 检查照片是否可选
            let isSelectable = isPhotoSelectable(asset)
            if isSelectable {
            // 选择模式下点击照片切换选中状态
            viewModel.togglePhotoSelection(asset)
            }
        } else {
            // 非选择模式下执行预览等操作
            print("Tapped asset: \(asset.localIdentifier)")
        }
    }
    
    // 判断照片是否可选
    private func isPhotoSelectable(_ asset: PHAsset) -> Bool {
        // 如果不是拼图水印选择模式，所有照片都可选
        if !viewModel.isPuzzleWatermarkSelectionMode() {
            return true
        }
        
        // 如果照片已被选中，则可选（可以取消选择）
        if viewModel.isAssetSelected(asset) {
            return true
        }
        
        // 获取当前激活的水印类型所需的照片数量
        guard let watermarkConfig = watermarkConfiguration else {
            return true // 如果没有水印配置，默认可选
        }
        
        let watermarkType = watermarkConfig.getActiveWatermarkType()
        let requiredPhotoCount = watermarkConfig.getRequiredPhotoCount(for: watermarkType)
        
        // 如果已选择了足够数量的照片，且当前照片未被选中，则不可选
        if viewModel.selectedAssets.count >= requiredPhotoCount {
            return false
        }
        
        // 其他情况都可选
        return true
    }
    
    // 标签按钮
    private func tabButton(title: String, isSelected: Bool, tab: MainGalleryTab) -> some View {
        Text(title)
            .font(.system(size: screenHeight * (isSelected ? 0.025 : 0.02),
                          weight: isSelected ? .bold : .regular))
            .foregroundColor(.white)
            .opacity(isSelected ? 1 : 0.6)
            .onTapGesture {
                // 允许在选择模式下切换标签
                withAnimation(.easeInOut(duration: 0.2)) {
                    selectedMainTab = tab
                    viewModel.switchMainTab(tab) // 同步ViewModel状态
                    
                    // 保持选择模式状态
                    if viewModel.isSelectionMode {
                        // 确保切换标签时保持选择模式
                        sharedTabViewModel.isSelectionMode = true
                        sharedTabViewModel.selectedPhotosCount = viewModel.selectedAssets.count
                    }
                }
            }
    }
    
    // 进入选择模式
    private func enterSelectionMode() {
        viewModel.toggleSelectionMode(true)
        sharedTabViewModel.isSelectionMode = true
        
        // 确保选择模式状态一致
        DispatchQueue.main.async {
            self.sharedTabViewModel.selectedPhotosCount = self.viewModel.selectedAssets.count
        }
    }
    
    // 退出选择模式
    private func exitSelectionMode() {
        viewModel.toggleSelectionMode(false)
        sharedTabViewModel.isSelectionMode = false
        
        // 确保清空选中照片计数
        sharedTabViewModel.selectedPhotosCount = 0
    }
    
    // 移除本地的权限检查和加载方法，因为ViewModel会处理
    // private func checkPhotoLibraryPermission() { ... }
    // private func loadAlbumCategories() { ... }
}

// 模型定义已移动到 GalleryModel.swift

struct GalleryView_Previews: PreviewProvider {
    static var previews: some View {
        GalleryDependencyContainer.galleryView(sharedTabViewModel: SharedTabViewModel())
            .background(Color.black) // 仅预览时添加背景色
    }
}