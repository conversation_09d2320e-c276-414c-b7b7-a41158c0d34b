import SwiftUI

// 通用的水印装饰组件 - 用于显示圆形和长方形
struct WatermarkDecoration: View {
    let geometry: GeometryProxy
    let position: CGPoint  // 装饰在父视图中的位置
    let circleSize: CGFloat  // 圆形大小
    let rectWidth: CGFloat  // 长方形宽度
    let rectHeight: CGFloat  // 长方形高度
    let spacing: CGFloat  // 元素间距
    let color: Color  // 装饰颜色
    let rectCount: Int  // 长方形数量
    let centerMode: Bool  // 是否使用整体居中模式，true表示position是整体中心，false表示position是圆形中心
    let isHorizontal: Bool  // 是否使用水平布局，true表示水平布局，false表示垂直布局
    let edgeMode: Bool  // 是否使用边缘模式，true表示圆形靠左矩形靠右，而不居中计算
    let showCircle: Bool  // 是否显示圆形，默认为true
    
    // 使用简单的初始化方法
    init(geometry: GeometryProxy, position: CGPoint, circleSize: CGFloat, rectWidth: CGFloat, rectHeight: CGFloat, spacing: CGFloat = 0.004, color: Color = .white, rectCount: Int = 2, centerMode: Bool = false, isHorizontal: Bool = false, edgeMode: Bool = false, showCircle: Bool = true) {
        self.geometry = geometry
        self.position = position
        self.circleSize = circleSize
        self.rectWidth = rectWidth
        self.rectHeight = rectHeight
        self.spacing = spacing * UIScreen.main.bounds.height
        self.color = color
        self.rectCount = rectCount
        self.centerMode = centerMode
        self.isHorizontal = isHorizontal
        self.edgeMode = edgeMode
        self.showCircle = showCircle
    }
    
    // 计算实际绘制的元素数量
    private var elementsCount: Int {
        (showCircle ? 1 : 0) + rectCount
    }
    
    // 计算标准元素高度（将圆形和矩形视为等高元素）
    private var standardElementHeight: CGFloat {
        rectHeight // 使用矩形高度作为标准元素高度
    }
    
    // 根据元素数量计算总体高度
    private var totalHeight: CGFloat {
        switch elementsCount {
        case 1:
            return standardElementHeight
        case 2:
            // 两个元素总高度 = 3个常量高度（2个元素高度 + 1个间距）
            return 2 * standardElementHeight + spacing
        case 3:
            // 三个元素总高度 = 5个常量高度（3个元素高度 + 2个间距）
            return 3 * standardElementHeight + 2 * spacing
        default:
            // 更多元素以此类推，总是 元素数量*高度 + (元素数量-1)*间距
            return CGFloat(elementsCount) * standardElementHeight + CGFloat(elementsCount - 1) * spacing
        }
    }
    
    var body: some View {
        ZStack {
            if isHorizontal {
                if edgeMode {
                    // 边缘模式：圆形靠左，矩形靠右
                    let screenHeight = UIScreen.main.bounds.height
                    let edgePadding = screenHeight * 0.01  // 1%屏幕高度的内边距
                    let width = geometry.size.width
                    
                    // 圆形 - 靠左（加上内边距）
                    if showCircle {
                    Circle()
                        .fill(color)
                        .frame(width: circleSize, height: circleSize)
                        .position(x: edgePadding + circleSize / 2, y: position.y)
                    }
                    
                    // 长方形 - 靠右（加上内边距）
                    Rectangle()
                        .fill(color)
                        .frame(width: rectWidth, height: rectHeight)
                        .position(x: width - edgePadding - rectWidth / 2, y: position.y)
                } else {
                    // 原水平布局模式（居中）
                    // 计算圆形和长方形的位置
                    let horizontalSpacing = spacing
                    let totalWidth = (showCircle ? circleSize : 0) + (showCircle ? horizontalSpacing : 0) + rectWidth
                    let leftOffset = -totalWidth / 2 + (showCircle ? circleSize / 2 : 0)
                    let rightOffset = showCircle ? (leftOffset + circleSize + horizontalSpacing) : leftOffset
                    
                    // 圆形 - 左侧
                    if showCircle {
                    Circle()
                        .fill(color)
                        .frame(width: circleSize, height: circleSize)
                        .position(x: position.x + leftOffset, y: position.y)
                    }
                    
                    // 长方形 - 右侧
                    Rectangle() // 使用Rectangle替代RoundedRectangle避免cornerRadius影响实际高度
                        .fill(color)
                        .frame(width: rectWidth, height: rectHeight)
                        .position(x: position.x + rightOffset + rectWidth / 2, y: position.y)
                }
            } else if centerMode {
                // 整体居中模式 - 使用固定规则计算元素间距和位置
                
                // 计算整体居中后的第一个元素位置
                let startY = position.y - totalHeight / 2 + standardElementHeight / 2
                
                // 依次绘制各个元素
                Group {
                    // 如果需要显示圆形，在第一个位置显示
                    if showCircle {
                Circle()
                    .fill(color)
                    .frame(width: circleSize, height: circleSize)
                            .position(x: position.x, y: startY)
                
                        // 绘制矩形，从第二个位置开始
                        ForEach(0..<rectCount, id: \.self) { index in
                Rectangle()
                    .fill(color)
                    .frame(width: rectWidth, height: rectHeight)
                                .position(x: position.x, y: startY + CGFloat(index + 1) * (standardElementHeight + spacing))
                        }
                    } else {
                        // 不显示圆形，直接绘制矩形
                        ForEach(0..<rectCount, id: \.self) { index in
                    Rectangle()
                        .fill(color)
                        .frame(width: rectWidth, height: rectHeight)
                                .position(x: position.x, y: startY + CGFloat(index) * (standardElementHeight + spacing))
                        }
                    }
                }
            } else {
                // 原始模式 - 以圆形中心为基准
                // 圆形 - 顶部元素
                if showCircle {
                Circle()
                    .fill(color)
                    .frame(width: circleSize, height: circleSize)
                    .position(x: position.x, y: position.y)
                }
                
                // 第一个长方形 - 中间元素（只在rectCount > 0时显示）
                if rectCount > 0 {
                Rectangle()
                    .fill(color)
                    .frame(width: rectWidth, height: rectHeight)
                        .position(x: position.x, y: position.y + (showCircle ? spacing + standardElementHeight / 2 : 0))
                }
                
                // 第二个长方形 - 底部元素 (只在rectCount > 1时显示)
                if rectCount > 1 {
                    Rectangle()
                        .fill(color)
                        .frame(width: rectWidth, height: rectHeight)
                        .position(x: position.x, y: position.y + (showCircle ? spacing + standardElementHeight + spacing + standardElementHeight / 2 : spacing + standardElementHeight))
                }
            }
        }
    }
}

// 通用的预览框组件
struct PreviewScrollView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 预览项数量
    let itemCount: Int
    // 预览项点击回调
    let onItemTap: (Int) -> Void
    // 水印索引映射（可选），用于在过滤模式下显示正确的预览
    var watermarkIndices: [Int]? = nil
    
    var body: some View {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.02) {
                ForEach(0..<itemCount, id: \.self) { index in
                    Button(action: {
                        onItemTap(index)
                    }) {
                        // 如果提供了索引映射，使用映射后的索引来决定显示哪个预览
                        let displayIndex = watermarkIndices != nil ? watermarkIndices![index] : index
                        
                        if displayIndex == 0 {
                            // 第一个预览项显示"不启用水印"图标
                            ZStack {
                                Rectangle()
                                    .fill(Color(uiColor: .systemGray4))
                                    .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                    .cornerRadius(4)
                                
                                // 使用LH Wu图像表示不启用
                                Image("LH Wu")
                                    .resizable()
                                    .renderingMode(.template)
                                    .foregroundColor(Color(uiColor: .systemGray))
                                    .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                            }
                        } else if displayIndex == 1 {
                            // 第二个预览项显示简单白色边框水印
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        Path { path in
                                            let width = geometry.size.width
                                            let height = geometry.size.height
                                            let normalBorderWidth = screenHeight * 0.004 // 边框宽度改为0.4%
                                            
                                            // 左边框
                                            path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                            
                                            // 上边框
                                            path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                            
                                            // 右边框
                                            path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                            
                                            // 底部边框
                                            path.addRect(CGRect(x: 0, y: height - normalBorderWidth, width: width, height: normalBorderWidth))
                                        }
                                        .fill(Color.white)
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 2 {
                            // 第三个预览项显示宝丽来样式水印（底部边框更宽）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 更名以区分
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 使用通用装饰组件
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width // 底部边框的实际高度
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue // 底部边框的上边缘Y坐标
                                            
                                            // 计算底部边框区域的垂直中心点
                                            let decorationContainerCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 水印2装饰元素在底部边框内居中
                                            WatermarkDecoration(
                                                geometry: geometry, 
                                                // 将 position.y 设置为底部边框的垂直中心
                                                position: CGPoint(x: centerX, y: decorationContainerCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing, // 传入比例值
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 2,        // 明确指定2个矩形
                                                centerMode: true,    // 启用中心模式
                                                showCircle: true     // 确保显示圆形
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 3 {
                            // 第四个预览项显示胶片风格水印（无边框，中心有装饰元素）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        // --- 元素尺寸与其他水印保持一致 ---
                                        let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                        let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                        let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                        
                                        let centerX = geometry.size.width / 2 
                                        let centerY = geometry.size.height / 2 
                                        
                                        // 使用通用组件，但只显示一个长方形，并使用整体居中模式
                                        WatermarkDecoration(
                                            geometry: geometry, 
                                            position: CGPoint(x: centerX, y: centerY),  // 直接使用中心点，不需要手动计算偏移
                                            circleSize: circleSize,
                                            rectWidth: elementWidth,
                                            rectHeight: rectHeight,
                                            spacing: UIConstants.watermarkElementSpacing, // 使用统一的间距常量
                                            color: UIConstants.watermarkFilmDecorColor, // 使用胶片风格专用白色常量
                                            rectCount: 1,  // 只显示一个长方形
                                            centerMode: true  // 使用整体居中模式
                                        )
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 4 {
                            // 第五个预览项显示自定义水印4（类似水印2的拍立得风格，但底部边框略窄）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印4使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: horizontalPadding + circleSize / 2, y: bottomCenterY)
                                            
                                            // 右侧矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth, height: rectHeight)
                                                .position(x: geometry.size.width - horizontalPadding - elementWidth / 2, y: bottomCenterY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 5 {
                            // 第六个预览项显示自定义水印5（上下右边框宽度一致，左边框宽度较宽）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let leftBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth
                                                
                                                // 左边框（较宽）
                                                path.addRect(CGRect(x: 0, y: 0, width: leftBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - normalBorderWidth, width: width, height: normalBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 用相同的逻辑为左边框添加装饰组件
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerY = geometry.size.height / 2 // 中心Y坐标
                                            let leftBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth
                                            
                                            // 水印5改为在左边框上垂直排列装饰元素
                                            WatermarkDecoration(
                                                geometry: geometry, 
                                                position: CGPoint(x: leftBorderWidth / 2, y: centerY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                centerMode: true  // 添加centerMode参数，确保间距处理一致
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 6 {
                            // 第七个预览项显示自定义水印6（上下宽边框风格）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let topBorderWidth = screenHeight * 0.012 // 上边框高度为屏幕高度的1.2%
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder2Width // 底边框保持1.6%
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: topBorderWidth, width: normalBorderWidth, height: height - topBorderWidth - bottomBorderWidth))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: topBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: topBorderWidth, width: normalBorderWidth, height: height - topBorderWidth - bottomBorderWidth))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 上边框中的装饰元素（水平排列的圆形和矩形）
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerX = geometry.size.width / 2
                                            let topBorderWidth = screenHeight * 0.012
                                            
                                            // 上边框装饰（水平排列的圆和矩形）
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: centerX, y: topBorderWidth / 2),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 1,
                                                centerMode: false,
                                                isHorizontal: true
                                            )
                                            
                                            // 底部边框装饰（两个垂直排列的矩形）
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            
                                            // 使用WatermarkDecoration组件，只显示两个矩形，不显示圆形
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: centerX, y: bottomBase + bottomBorderWidth / 2),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 2, // 显示两个矩形
                                                centerMode: true, // 使用整体居中模式
                                                showCircle: false // 不显示圆形
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 7 {
                            // 自定义水印7 (Custom7) - 类胶片风格，但装饰元素在底部
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                        let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                        let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                        let spacing = rectHeight // 使用矩形高度作为间距
                                        
                                        let centerX = geometry.size.width / 2
                                        
                                        // 计算装饰元素的总高度 (当 rectCount = 1 且 centerMode = true)
                                        // 按照新的规则：2个元素总高度 = 3个常量高度（2个元素高度 + 1个间距）
                                        let decorationTotalHeight = circleSize + spacing + rectHeight
                                        
                                        // 我们希望装饰的中心点Y坐标，使得其整体在底部
                                        let previewBottomPadding = screenHeight * 0.005 // 预览框内底部的小间距
                                        let decorationCenterY = geometry.size.height - previewBottomPadding - (decorationTotalHeight / 2)

                                        WatermarkDecoration(
                                            geometry: geometry,
                                            position: CGPoint(x: centerX, y: decorationCenterY),
                                            circleSize: circleSize,
                                            rectWidth: elementWidth,
                                            rectHeight: rectHeight,
                                            spacing: UIConstants.watermarkElementSpacing, // 使用原始的 spacing 定义方式
                                            color: UIConstants.watermarkFilmDecorColor, // 与胶片风格颜色一致
                                            rectCount: 1,  // 只显示一个长方形
                                            centerMode: true // 保持centerMode为true，让其内部垂直排列圆形和矩形
                                        )
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 8 {
                            // 自定义水印8 (Custom8) - 类胶片风格，署名替换Logo，装饰元素在底部
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                        let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                        let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                        
                                        let centerX = geometry.size.width / 2
                                        
                                        // 装饰元素在底部
                                        let previewBottomPadding = screenHeight * 0.005
                                        // 使用标准的元素大小和间距计算
                                        let decorationTotalHeight = rectHeight * 2 + screenHeight * UIConstants.watermarkElementSpacing
                                        let decorationCenterY = geometry.size.height - previewBottomPadding - (decorationTotalHeight / 2)

                                        // 使用WatermarkDecoration组件，不显示圆形，只显示两个矩形
                                        WatermarkDecoration(
                                            geometry: geometry,
                                            position: CGPoint(x: centerX, y: decorationCenterY),
                                            circleSize: circleSize,
                                            rectWidth: elementWidth,
                                            rectHeight: rectHeight,
                                            spacing: UIConstants.watermarkElementSpacing,
                                            color: UIConstants.watermarkFilmDecorColor,
                                            rectCount: 2, // 显示两个矩形
                                            centerMode: true, // 使用整体居中模式
                                            showCircle: false // 不显示圆形
                                        )
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 9 {
                            // 自定义水印9 - 基于水印4但新增署名功能
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框 - 与水印4相同的布局
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印9的装饰元素：使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧Logo（圆形）- 使用统一的定位
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: horizontalPadding + circleSize / 2, y: bottomCenterY)
                                            
                                            // 右侧两个矩形（署名和文字/偏好）- 使用统一的定位
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: geometry.size.width - horizontalPadding - elementWidth / 2, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 2, // 显示两个矩形
                                                centerMode: true, // 使用整体居中模式
                                                showCircle: false // 不显示圆形
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 10 {
                            // 自定义水印10 - 上下左边框宽度一致，右边框宽度较宽
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let rightBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth // 使用与水印5相同的边框宽度
                                                
                                                // 左边框（普通）
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框（较宽）
                                                path.addRect(CGRect(x: width - rightBorderWidth, y: 0, width: rightBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - normalBorderWidth, width: width, height: normalBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 右边框中的装饰元素
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize // 与其他水印保持一致大小
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let rightBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth
                                            let rightBorderCenterX = geometry.size.width - rightBorderWidth / 2
                                            
                                            // 使用与矩形高度相等的间距
                                            let elementSpacing = screenHeight * UIConstants.watermarkElementSpacing * 2 // 间距加倍
                                            // 计算总高度：5个矩形，每个矩形间有一个间距，最后加上一个圆形和一个间距
                                            let totalHeight = rectHeight * 5 + circleSize + elementSpacing * 5
                                            
                                            // 元素起始Y坐标
                                            let startY = (geometry.size.height - totalHeight) / 2
                                            
                                            // 5个半宽矩形(先显示矩形)
                                            ForEach(0..<5) { i in
                                                let y = startY + CGFloat(i) * (rectHeight + elementSpacing) + rectHeight / 2
                                                Rectangle()
                                                    .fill(UIConstants.watermarkPreviewDecorColor)
                                                    .frame(width: elementWidth / 2, height: rectHeight)
                                                    .position(x: rightBorderCenterX, y: y)
                                            }
                                            
                                            // 底部圆形(改为最后显示，位置在所有矩形下方)
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: rightBorderCenterX, y: startY + 5 * (rectHeight + elementSpacing) + circleSize / 2)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 11 {
                            // 自定义水印11 - 修改预览框样式
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印11使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧显示宽度为右侧一半的矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: horizontalPadding + (elementWidth / 4), y: bottomCenterY)
                                            
                                            // 右侧使用与自定义水印6完全相同的方式显示圆形和矩形
                                            // 右侧矩形位置需要向左偏移圆形宽度加上间距一半的宽度
                                            let circleWithSpacingOffset = circleSize + (UIConstants.watermarkElementSpacing * screenHeight / 2)
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: geometry.size.width - horizontalPadding - elementWidth / 2 - circleWithSpacingOffset, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 1,
                                                centerMode: false,
                                                isHorizontal: true
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 12 {
                            // 自定义水印12 - 与水印11预览完全相同，后续可以根据需要修改
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印12使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧显示宽度为右侧一半的矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: horizontalPadding + (elementWidth / 4), y: bottomCenterY)
                                            
                                            // 右侧使用与自定义水印6完全相同的方式显示圆形和矩形
                                            // 右侧矩形位置需要向左偏移圆形宽度加上间距一半的宽度
                                            let circleWithSpacingOffset = circleSize + (UIConstants.watermarkElementSpacing * screenHeight / 2)
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: geometry.size.width - horizontalPadding - elementWidth / 2 - circleWithSpacingOffset, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 1,
                                                centerMode: false,
                                                isHorizontal: true
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 13 {
                            // 自定义水印13 - 基于水印11的预览框样式
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印13使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧水平排布两个相同的矩形
                                            let rectSpacing = UIConstants.watermarkElementSpacing * screenHeight // 与右侧间距一致
                                            let totalRectWidth = elementWidth / 2 * 2 + rectSpacing // 两个矩形加间距
                                            let firstRectX = horizontalPadding + (elementWidth / 4)
                                            
                                            // 第一个矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: firstRectX, y: bottomCenterY)
                                                
                                            // 第二个矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: firstRectX + elementWidth / 2 + rectSpacing, y: bottomCenterY)
                                                
                                            // 右侧使用与自定义水印6完全相同的方式显示圆形和矩形
                                            // 右侧矩形位置需要向左偏移圆形宽度加上间距一半的宽度
                                            let circleWithSpacingOffset = circleSize + (UIConstants.watermarkElementSpacing * screenHeight / 2)
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: geometry.size.width - horizontalPadding - elementWidth / 2 - circleWithSpacingOffset + elementWidth / 4, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth / 2, 
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 1,
                                                centerMode: false,
                                                isHorizontal: true
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 14 {
                            // 自定义水印14 - 基于水印11的预览框样式
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidth, width: width, height: bottomBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 水印14使用统一的组件定位方式
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let bottomBorderWidth = screenHeight * UIConstants.watermarkBorder4Width
                                            let bottomBase = geometry.size.height - bottomBorderWidth
                                            let bottomCenterY = bottomBase + bottomBorderWidth / 2
                                            let horizontalPadding = screenHeight * 0.008 // 统一的水平间距
                                            
                                            // 左侧垂直排布两个矩形，使用与自定义水印9一致的WatermarkDecoration组件
                                            let firstRectX = horizontalPadding + (elementWidth / 4)
                                            
                                            // 使用WatermarkDecoration组件，不显示圆形，只显示两个矩形
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: firstRectX, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth / 2,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 2, // 显示两个矩形
                                                centerMode: true, // 使用整体居中模式
                                                showCircle: false // 不显示圆形
                                            )
                                            
                                            // 右侧使用与自定义水印6完全相同的方式显示圆形和矩形
                                            // 右侧矩形位置需要向左偏移圆形宽度加上间距一半的宽度
                                            let circleWithSpacingOffset = circleSize + (UIConstants.watermarkElementSpacing * screenHeight / 2)
                                            WatermarkDecoration(
                                                geometry: geometry,
                                                position: CGPoint(x: geometry.size.width - horizontalPadding - elementWidth / 2 - circleWithSpacingOffset, y: bottomCenterY),
                                                circleSize: circleSize,
                                                rectWidth: elementWidth,
                                                rectHeight: rectHeight,
                                                spacing: UIConstants.watermarkElementSpacing,
                                                color: UIConstants.watermarkPreviewDecorColor,
                                                rectCount: 1,
                                                centerMode: false,
                                                isHorizontal: true
                                            )
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 15 {
                            // 第16个预览项显示自定义水印15 (两行布局：第一行圆+半宽矩形，第二行完整矩形)
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 使用与水印2相同的底部边框宽度
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 定义元素尺寸
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            // 底部边框区域计算
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue
                                            let bottomCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 第一行：圆形+半宽矩形（水平排列）
                                            let horizontalSpacing = UIScreen.main.bounds.width * 0.01
                                            let circleWithHalfRectWidth = circleSize + horizontalSpacing + (elementWidth / 2)
                                            
                                            // 计算第一行的起始X坐标，使整体水平居中
                                            let firstRowStartX = (geometry.size.width - circleWithHalfRectWidth) / 2
                                            
                                            // 计算两行的垂直位置
                                            // 通过计算两行元素的总高度，让它们在底部边框内整体垂直居中
                                            let verticalSpacing = screenHeight * UIConstants.watermarkElementSpacing
                                            let twoRowsTotalHeight = rectHeight * 2 + verticalSpacing
                                            
                                            // 第一行Y位置
                                            let firstRowY = bottomCenterY - (twoRowsTotalHeight / 2) + (rectHeight / 2)
                                            
                                            // 第二行Y位置
                                            let secondRowY = firstRowY + rectHeight + verticalSpacing
                                            
                                            // 第一行：圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstRowStartX + (circleSize / 2), y: firstRowY)
                                            
                                            // 第一行：半宽矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: firstRowStartX + circleSize + horizontalSpacing + (elementWidth / 4), y: firstRowY)
                                            
                                            // 第二行：完整矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth, height: rectHeight)
                                                .position(x: centerX, y: secondRowY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 16 {
                            // 自定义水印16 - 圆形在上，5个矩形在下
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let rightBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth // 使用与水印5、10相同的边框宽度
                                                
                                                // 左边框（普通）
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框（较宽）
                                                path.addRect(CGRect(x: width - rightBorderWidth, y: 0, width: rightBorderWidth, height: height))
                                                
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - normalBorderWidth, width: width, height: normalBorderWidth))
                                            }
                                            .fill(Color.white)
                                            
                                            // 右边框中的装饰元素
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize // 与其他水印保持一致大小
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let rightBorderWidth = screenHeight * UIConstants.watermarkBorder5LeftWidth
                                            let rightBorderCenterX = geometry.size.width - rightBorderWidth / 2
                                            
                                            // 使用与矩形高度相等的间距
                                            let elementSpacing = screenHeight * UIConstants.watermarkElementSpacing * 2 // 间距加倍
                                            // 计算总高度：5个矩形，每个矩形间有一个间距，最后加上一个圆形和一个间距
                                            let totalHeight = rectHeight * 5 + circleSize + elementSpacing * 5
                                            
                                            // 元素起始Y坐标
                                            let startY = (geometry.size.height - totalHeight) / 2
                                            
                                            // 顶部圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: rightBorderCenterX, y: startY + circleSize / 2)
                                            
                                            // 下方5个半宽矩形
                                            ForEach(0..<5) { i in
                                                let y = startY + circleSize + elementSpacing + CGFloat(i) * (rectHeight + elementSpacing) + rectHeight / 2
                                                Rectangle()
                                                    .fill(UIConstants.watermarkPreviewDecorColor)
                                                    .frame(width: elementWidth / 2, height: rectHeight)
                                                    .position(x: rightBorderCenterX, y: y)
                                            }
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 17 {
                            // 自定义水印17 - 基于胶片风格水印，但使用水平排布的圆形和矩形
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                        let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                        let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                        
                                        let centerX = geometry.size.width / 2
                                        let centerY = geometry.size.height / 2
                                        
                                        // 使用WatermarkDecoration组件，显示圆形和矩形，水平排列
                                        WatermarkDecoration(
                                            geometry: geometry,
                                            position: CGPoint(x: centerX, y: centerY), // 使用中心点，确保居中显示
                                            circleSize: circleSize,
                                            rectWidth: elementWidth,
                                            rectHeight: rectHeight,
                                            spacing: UIConstants.watermarkElementSpacing,
                                            color: UIConstants.watermarkFilmDecorColor, // 与胶片风格颜色一致
                                            rectCount: 1,  // 显示一个长方形
                                            centerMode: true, // 使整体元素居中
                                            isHorizontal: true // 保持水平布局
                                        )
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 18 {
                            // 自定义水印18 - 与自定义水印1相同的简单边框水印
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        Path { path in
                                            let width = geometry.size.width
                                            let height = geometry.size.height
                                            let normalBorderWidth = screenHeight * 0.004 // 边框宽度改为0.4%
                                            
                                            // 左边框
                                            path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                            
                                            // 上边框
                                            path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                            
                                            // 右边框
                                            path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                            
                                            // 底部边框
                                            path.addRect(CGRect(x: 0, y: height - normalBorderWidth, width: width, height: normalBorderWidth))
                                        }
                                        .fill(Color.white)
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 19 {
                            // 自定义水印19 (两行布局：第一行圆+圆，第二行完整矩形)
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 使用与水印2相同的底部边框宽度
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 定义元素尺寸
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            // 底部边框区域计算
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue
                                            let bottomCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 第一行：圆形+圆形（水平排列）
                                            let horizontalSpacing = UIScreen.main.bounds.width * 0.01
                                            let circleWithCircleWidth = circleSize + horizontalSpacing + circleSize
                                            
                                            // 计算第一行的起始X坐标，使整体水平居中
                                            let firstRowStartX = (geometry.size.width - circleWithCircleWidth) / 2
                                            
                                            // 计算两行的垂直位置
                                            // 通过计算两行元素的总高度，让它们在底部边框内整体垂直居中
                                            let verticalSpacing = screenHeight * UIConstants.watermarkElementSpacing
                                            let twoRowsTotalHeight = rectHeight * 2 + verticalSpacing
                                            
                                            // 第一行Y位置
                                            let firstRowY = bottomCenterY - (twoRowsTotalHeight / 2) + (rectHeight / 2)
                                            
                                            // 第二行Y位置
                                            let secondRowY = firstRowY + rectHeight + verticalSpacing
                                            
                                            // 第一行：第一个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstRowStartX + (circleSize / 2), y: firstRowY)
                                            
                                            // 第一行：第二个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstRowStartX + circleSize + horizontalSpacing + (circleSize / 2), y: firstRowY)
                                            
                                            // 第二行：完整矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth, height: rectHeight)
                                                .position(x: centerX, y: secondRowY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 20 {
                            // 自定义水印20 (两行垂直布局：上下两个圆形)
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 使用与水印2相同的底部边框宽度
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 定义元素尺寸
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            // 底部边框区域计算
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue
                                            let bottomCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 垂直排列两个圆形
                                            let verticalSpacing = screenHeight * UIConstants.watermarkElementSpacing
                                            let twoCirclesTotalHeight = circleSize * 2 + verticalSpacing
                                            
                                            // 上圆形Y位置
                                            let topCircleY = bottomCenterY - (twoCirclesTotalHeight / 2) + (circleSize / 2)
                                            
                                            // 下圆形Y位置
                                            let bottomCircleY = topCircleY + circleSize + verticalSpacing
                                            
                                            // 上圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: centerX, y: topCircleY)
                                            
                                            // 下圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: centerX, y: bottomCircleY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 21 {
                            // 自定义水印21 (三个水平排列的圆形)
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 使用与水印2相同的底部边框宽度
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 定义元素尺寸
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            // 底部边框区域计算
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue
                                            let bottomCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 计算三个圆形并排的总宽度（两个间距和三个圆形）
                                            // 修改为与水印19相同的水平间距 - 屏幕宽度的1%
                                            let horizontalSpacing = UIScreen.main.bounds.width * 0.01
                                            let threeCirclesWidth = (circleSize * 3) + (horizontalSpacing * 2)
                                            
                                            // 计算第一个圆形的X坐标，使三个圆形整体水平居中
                                            let firstCircleX = (geometry.size.width - threeCirclesWidth) / 2 + (circleSize / 2)
                                            
                                            // 第一个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstCircleX, y: bottomCenterY)
                                            
                                            // 第二个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstCircleX + circleSize + horizontalSpacing, y: bottomCenterY)
                                            
                                            // 第三个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstCircleX + (circleSize + horizontalSpacing) * 2, y: bottomCenterY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 22 {
                            // 自定义水印22 - 复制自定义水印17的预览框, 并修改为两个圆形
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                        let centerX = geometry.size.width / 2
                                        let centerY = geometry.size.height / 2

                                        // 水平间距 - 与水印19一致
                                        let horizontalSpacing = UIScreen.main.bounds.width * 0.01
                                        let totalWidth = circleSize * 2 + horizontalSpacing

                                        // 计算第一个圆形的X坐标，使整体水平居中
                                        let firstCircleX = centerX - totalWidth / 2 + circleSize / 2
                                        
                                        // 使用ZStack来容纳两个圆形
                                        ZStack {
                                            // 第一个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkFilmDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstCircleX, y: centerY)

                                            // 第二个圆形
                                            Circle()
                                                .fill(UIConstants.watermarkFilmDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstCircleX + circleSize + horizontalSpacing, y: centerY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 23 {
                            // 自定义水印23 - 田字形(2x2)网格水印
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 定义边框宽度
                                            let borderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                            let width = geometry.size.width
                                            let height = geometry.size.height
                                            
                                            // 绘制田字形边框
                                            Path { path in
                                                // 外边框
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: borderWidth, height: height))
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: borderWidth))
                                                // 右边框
                                                path.addRect(CGRect(x: width - borderWidth, y: 0, width: borderWidth, height: height))
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - borderWidth, width: width, height: borderWidth))
                                                
                                                // 中间十字线
                                                // 垂直分隔线
                                                path.addRect(CGRect(x: width/2 - borderWidth/2, y: 0, width: borderWidth, height: height))
                                                // 水平分隔线
                                                path.addRect(CGRect(x: 0, y: height/2 - borderWidth/2, width: width, height: borderWidth))
                                            }
                                            .fill(Color.white)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 24 {
                            // 自定义水印24 - 三格式水印（两条水平分隔线）
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 定义边框宽度
                                            let borderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                            let width = geometry.size.width
                                            let height = geometry.size.height
                                            
                                            // 计算可用内部空间的高度（减去四条水平线的宽度：顶部边框、两条分隔线和底部边框）
                                            let availableHeight = height - (borderWidth * 4)
                                            
                                            // 计算每个格子的高度（可用高度的三分之一）
                                            let cellHeight = availableHeight / 3
                                            
                                            // 绘制三格式边框
                                            Path { path in
                                                // 外边框
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: borderWidth, height: height))
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: borderWidth))
                                                // 右边框
                                                path.addRect(CGRect(x: width - borderWidth, y: 0, width: borderWidth, height: height))
                                                // 底部边框
                                                path.addRect(CGRect(x: 0, y: height - borderWidth, width: width, height: borderWidth))
                                                
                                                // 两条水平分隔线
                                                // 第一条水平分隔线（位于第一个和第二个单元格之间）
                                                let firstLineY = borderWidth + cellHeight
                                                path.addRect(CGRect(x: 0, y: firstLineY, width: width, height: borderWidth))
                                                
                                                // 第二条水平分隔线（位于第二个和第三个单元格之间）
                                                let secondLineY = firstLineY + cellHeight + borderWidth
                                                path.addRect(CGRect(x: 0, y: secondLineY, width: width, height: borderWidth))
                                            }
                                            .fill(Color.white)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else if displayIndex == 25 {
                            // 自定义水印25 - 与水印15完全相同的预览效果
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                                .overlay(
                                    GeometryReader { geometry in
                                        ZStack {
                                            // 主边框
                                            Path { path in
                                                let width = geometry.size.width
                                                let height = geometry.size.height
                                                let normalBorderWidth = screenHeight * UIConstants.watermarkBorderNormalWidth
                                                let bottomBorderWidthPath = screenHeight * UIConstants.watermarkBorder2Width // 使用与水印2相同的底部边框宽度
                                                
                                                // 左边框
                                                path.addRect(CGRect(x: 0, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 上边框
                                                path.addRect(CGRect(x: 0, y: 0, width: width, height: normalBorderWidth))
                                                
                                                // 右边框
                                                path.addRect(CGRect(x: width - normalBorderWidth, y: 0, width: normalBorderWidth, height: height))
                                                
                                                // 底部边框（更宽）
                                                path.addRect(CGRect(x: 0, y: height - bottomBorderWidthPath, width: width, height: bottomBorderWidthPath))
                                            }
                                            .fill(Color.white)
                                            
                                            // 定义元素尺寸
                                            let elementWidth = screenHeight * UIConstants.watermarkRectWidth
                                            let circleSize = screenHeight * UIConstants.watermarkCircleSize
                                            let rectHeight = screenHeight * UIConstants.watermarkRectHeight
                                            let centerX = geometry.size.width / 2 // 中心X坐标
                                            
                                            // 底部边框区域计算
                                            let bottomBorderHeightValue = screenHeight * UIConstants.watermarkBorder2Width
                                            let bottomBaseY = geometry.size.height - bottomBorderHeightValue
                                            let bottomCenterY = bottomBaseY + (bottomBorderHeightValue / 2)
                                            
                                            // 第一行：圆形+半宽矩形（水平排列）
                                            let horizontalSpacing = UIScreen.main.bounds.width * 0.01
                                            let circleWithHalfRectWidth = circleSize + horizontalSpacing + (elementWidth / 2)
                                            
                                            // 计算第一行的起始X坐标，使整体水平居中
                                            let firstRowStartX = (geometry.size.width - circleWithHalfRectWidth) / 2
                                            
                                            // 计算两行的垂直位置
                                            // 通过计算两行元素的总高度，让它们在底部边框内整体垂直居中
                                            let verticalSpacing = screenHeight * UIConstants.watermarkElementSpacing
                                            let twoRowsTotalHeight = rectHeight * 2 + verticalSpacing
                                            
                                            // 第一行Y位置
                                            let firstRowY = bottomCenterY - (twoRowsTotalHeight / 2) + (rectHeight / 2)
                                            
                                            // 第二行Y位置
                                            let secondRowY = firstRowY + rectHeight + verticalSpacing
                                            
                                            // 第一行：圆形
                                            Circle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: circleSize, height: circleSize)
                                                .position(x: firstRowStartX + (circleSize / 2), y: firstRowY)
                                            
                                            // 第一行：半宽矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth / 2, height: rectHeight)
                                                .position(x: firstRowStartX + circleSize + horizontalSpacing + (elementWidth / 4), y: firstRowY)
                                            
                                            // 第二行：完整矩形
                                            Rectangle()
                                                .fill(UIConstants.watermarkPreviewDecorColor)
                                                .frame(width: elementWidth, height: rectHeight)
                                                .position(x: centerX, y: secondRowY)
                                        }
                                    }
                                    .cornerRadius(4)
                                )
                        } else {
                            // 其他预览项保持原样
                            Rectangle()
                                .fill(Color(uiColor: .systemGray4))
                                .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                                .cornerRadius(4)
                        }
                    }
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0075)  // 修改为0.75%
            }
            .background(Color(uiColor: .systemGray6))
    }
}
