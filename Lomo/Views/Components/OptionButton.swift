import SwiftUI

// 通用选项按钮样式
struct OptionButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    let screenHeight: CGFloat
    let screenWidth: CGFloat = UIScreen.main.bounds.width
    
    var body: some View {
        Button(action: {
            // 立即执行动作，不延迟
            withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
                action()
            }
        }) {
            Text(title)
                .font(.system(size: screenHeight * UIConstants.parameterFontSize))
                .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
                .padding(.horizontal, screenWidth * UIConstants.optionButtonPadding)
                .frame(height: screenHeight * UIConstants.optionButtonHeight)
                .background(Color.black.opacity(UIConstants.buttonBackgroundOpacity))
                .clipShape(RoundedRectangle(cornerRadius: UIConstants.optionButtonCornerRadius))
        }
    }
}

// Pro标签组件
struct ProLabel: View {
    let screenHeight: CGFloat
    var isProUser: Bool = false  // 默认为非Pro用户
    
    var body: some View {
        // 当用户是Pro用户时，不显示标签
        if !isProUser {
            Text("PRO")
                .font(.system(size: screenHeight * 0.01, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 2)
                .padding(.vertical, 1)
                .background(UIConstants.dialIndicatorColor.opacity(UIConstants.effectOpacity))
                .clipShape(RoundedRectangle(cornerRadius: 2))
        } else {
            // 返回空视图
            EmptyView()
        }
    }
}

// 带有Pro标签的选项按钮
struct ProOptionButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    let screenHeight: CGFloat
    let screenWidth: CGFloat = UIScreen.main.bounds.width
    var isProUser: Bool = false  // 默认为非Pro用户，保留这个参数以保持API兼容性，但不使用它
    
    // 添加订阅管理器（通过依赖注入容器获取）
    @ObservedObject private var subscriptionManager = SubscriptionDependencyContainer.shared.subscriptionService as! SubscriptionService
    
    var body: some View {
        Button(action: {
            // 检查用户是否为Pro用户
            if subscriptionManager.handleProFeatureAccess() {
                // 如果是Pro用户，则执行原本的操作
                withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
                    action()
                }
            }
            // 如果不是Pro用户，handleProFeatureAccess会自动显示订阅页面
        }) {
            HStack(spacing: screenWidth * 0.01) {
                Text(title)
                    .font(.system(size: screenHeight * UIConstants.parameterFontSize))
                
                // 使用订阅管理器的isProUser，而不是传入的isProUser
                ProLabel(screenHeight: screenHeight, isProUser: subscriptionManager.isProUser)
            }
            .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
            .padding(.horizontal, screenWidth * UIConstants.optionButtonPadding)
            .frame(height: screenHeight * UIConstants.optionButtonHeight)
            .background(Color.black.opacity(UIConstants.buttonBackgroundOpacity))
            .clipShape(RoundedRectangle(cornerRadius: UIConstants.optionButtonCornerRadius))
        }
    }
}

// 选项组位置枚举
enum OptionGroupPosition {
    case top     // 顶部参数区域
    case bottom  // 底部控制区域
}

// 通用选项组视图
struct OptionGroup<T: Hashable>: View {
    let options: [(title: String, value: T)]
    let selectedValue: T
    let onSelect: (T) -> Void
    let screenHeight: CGFloat
    let isVisible: Bool
    let position: OptionGroupPosition
    let screenWidth: CGFloat = UIScreen.main.bounds.width
    
    init(
        options: [(title: String, value: T)],
        selectedValue: T,
        onSelect: @escaping (T) -> Void,
        screenHeight: CGFloat,
        isVisible: Bool,
        position: OptionGroupPosition = .bottom
    ) {
        self.options = options
        self.selectedValue = selectedValue
        self.onSelect = onSelect
        self.screenHeight = screenHeight
        self.isVisible = isVisible
        self.position = position
    }
    
    var body: some View {
        if isVisible {
            GeometryReader { geometry in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: screenWidth * UIConstants.optionButtonSpacing) {
                        ForEach(options, id: \.value) { option in
                            OptionButton(
                                title: option.title,
                                isSelected: option.value == selectedValue,
                                action: { onSelect(option.value) },
                                screenHeight: screenHeight
                            )
                        }
                    }
                    .frame(minWidth: geometry.size.width)
                    .frame(maxWidth: .infinity)
                }
            }
            .frame(height: screenHeight * UIConstants.optionButtonHeight)
            .modifier(OptionGroupPositionModifier(position: position))
        }
    }
}

// 带有Pro选项的选项组视图
struct OptionGroupWithPro<T: Hashable>: View {
    let options: [(title: String, value: T)]
    let selectedValue: T
    let onSelect: (T) -> Void
    let screenHeight: CGFloat
    let isVisible: Bool
    let position: OptionGroupPosition
    let proOptions: [T]  // 需要显示Pro标签的选项值列表
    let screenWidth: CGFloat = UIScreen.main.bounds.width
    var isProUser: Bool = false  // 默认为非Pro用户，保留这个参数以保持API兼容性，但不使用它
    
    // 添加订阅管理器（通过依赖注入容器获取）
    @ObservedObject private var subscriptionManager = SubscriptionDependencyContainer.shared.subscriptionService as! SubscriptionService
    
    init(
        options: [(title: String, value: T)],
        selectedValue: T,
        onSelect: @escaping (T) -> Void,
        screenHeight: CGFloat,
        isVisible: Bool,
        proOptions: [T], // 新增参数，用于指定哪些选项是Pro选项
        position: OptionGroupPosition = .bottom,
        isProUser: Bool = false
    ) {
        self.options = options
        self.selectedValue = selectedValue
        self.onSelect = onSelect
        self.screenHeight = screenHeight
        self.isVisible = isVisible
        self.proOptions = proOptions
        self.position = position
        self.isProUser = isProUser
    }
    
    var body: some View {
        if isVisible {
            GeometryReader { geometry in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: screenWidth * UIConstants.optionButtonSpacing) {
                        ForEach(options, id: \.value) { option in
                            // 如果该选项在proOptions列表中，则使用ProOptionButton
                            if proOptions.contains(option.value) {
                                ProOptionButton(
                                    title: option.title,
                                    isSelected: option.value == selectedValue,
                                    action: { 
                                        // Pro选项的操作已经在ProOptionButton内部处理
                                        onSelect(option.value)
                                    },
                                    screenHeight: screenHeight,
                                    isProUser: subscriptionManager.isProUser
                                )
                            } else {
                                OptionButton(
                                    title: option.title,
                                    isSelected: option.value == selectedValue,
                                    action: { onSelect(option.value) },
                                    screenHeight: screenHeight
                                )
                            }
                        }
                    }
                    .frame(minWidth: geometry.size.width)
                    .frame(maxWidth: .infinity)
                }
            }
            .frame(height: screenHeight * UIConstants.optionButtonHeight)
            .modifier(OptionGroupPositionModifier(position: position))
        }
    }
}

// 位置布局修改器
private struct OptionGroupPositionModifier: ViewModifier {
    let position: OptionGroupPosition
    
    func body(content: Content) -> some View {
        content
            .frame(maxWidth: .infinity)
            .padding(.horizontal, UIScreen.main.bounds.width * UIConstants.horizontalPadding)
            .padding(.top, position == .top ? UIScreen.main.bounds.height * 0.01 : 0)
            .padding(.bottom, position == .bottom ? UIScreen.main.bounds.height * 0.01 : 0)
    }
} 