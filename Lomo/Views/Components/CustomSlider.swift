import SwiftUI

/// 自定义滑块组件
struct CustomSlider: View {
    @Binding var value: Double
    var range: ClosedRange<Double>
    var step: Double = 0.01
    var horizontalPadding: CGFloat? = nil
    var onEditingChanged: (Bool) -> Void = { _ in }
    
    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let screenWidth = UIScreen.main.bounds.width
    
    // 滑块尺寸常量 - 基于屏幕高度百分比
    private var trackHeight: CGFloat { return screenHeight * 0.0025 }  // 0.25%屏幕高度
    private let trackCornerRadius: CGFloat = 2
    private var thumbSize: CGFloat { return screenHeight * 0.02 }     // 2%屏幕高度
    private let thumbShadowRadius: CGFloat = 2
    
    init(value: Binding<Double>, range: ClosedRange<Double>, step: Double = 0.01, horizontalPadding: CGFloat? = nil, onEditingChanged: @escaping (Bool) -> Void = { _ in }) {
        self._value = value
        self.range = range
        self.step = step
        self.horizontalPadding = horizontalPadding
        self.onEditingChanged = onEditingChanged
        
        // 如果未提供horizontalPadding，则设置为屏幕宽度的4%
        if self.horizontalPadding == nil {
            self.horizontalPadding = UIScreen.main.bounds.width * 0.04
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景轨道
                RoundedRectangle(cornerRadius: trackCornerRadius)
                    .fill(Color.gray.opacity(0.25))
                    .frame(height: trackHeight)
                
                // 填充轨道
                RoundedRectangle(cornerRadius: trackCornerRadius)
                    .fill(UIConstants.dialIndicatorColor)
                    .frame(width: thumbPosition(in: geometry.size.width) + thumbSize/2, height: trackHeight)
                
                // 滑块手柄
                Circle()
                    .fill(Color.white)
                    .frame(width: thumbSize, height: thumbSize)
                    .shadow(radius: thumbShadowRadius)
                    .offset(x: thumbPosition(in: geometry.size.width))
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                onEditingChanged(true)
                                updateValue(at: gesture.location.x, in: geometry.size.width)
                            }
                            .onEnded { _ in
                                onEditingChanged(false)
                            }
                    )
            }
            .frame(height: max(trackHeight, thumbSize))
        }
        .frame(height: thumbSize)
        .padding(.horizontal, horizontalPadding)
    }
    
    // 计算滑块手柄位置
    private func thumbPosition(in width: CGFloat) -> CGFloat {
        let percentage = (value - range.lowerBound) / (range.upperBound - range.lowerBound)
        let trackWidth = width - thumbSize
        return CGFloat(percentage) * trackWidth
    }
    
    // 从拖动位置更新值
    private func updateValue(at position: CGFloat, in width: CGFloat) {
        let trackWidth = width - thumbSize
        let clampedPosition = max(0, min(position, trackWidth))
        let percentage = Double(clampedPosition / trackWidth)
        
        // 应用步进逻辑
        let rawValue = percentage * (range.upperBound - range.lowerBound) + range.lowerBound
        let steps = round(rawValue / step) * step
        
        // 确保在范围内
        value = max(range.lowerBound, min(steps, range.upperBound))
    }
} 