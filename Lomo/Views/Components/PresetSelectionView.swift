// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI

/// 通用预设选择组件 - 可复用的相纸预设选择界面
/// 支持任意类型的预设选择，提供统一的视觉效果和交互体验
struct PresetSelectionView: View {
    // MARK: - 配置参数
    let title: String                           // 分类标题
    let presetType: PaperViewModel.PresetType   // 预设类型
    let presetsCount: Int                       // 预设数量
    let isSelected: (Int) -> Bool              // 选中状态判断
    let onSelection: (Int) -> Void             // 选择回调
    
    // MARK: - 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        VStack(alignment: .leading, spacing: screenHeight * PaperConstants.categorySpacing) {
            // 分类标题
            categoryTitle
            
            // 预设选择区域
            presetScrollView
        }
        .padding(.horizontal, screenWidth * PaperConstants.horizontalPadding)
        .padding(.vertical, screenHeight * PaperConstants.verticalPadding)
    }
    
    // MARK: - 子视图
    
    /// 分类标题视图
    private var categoryTitle: some View {
        Text(title)
            .font(.system(size: screenHeight * PaperConstants.categoryTitleFontSize, weight: .bold))
            .foregroundColor(PaperConstants.categoryTitleColor)
    }
    
    /// 预设滚动视图
    private var presetScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: screenWidth * PaperConstants.presetItemSpacing) {
                ForEach(0..<presetsCount, id: \.self) { index in
                    presetItem(index: index)
                }
            }
            .padding(.horizontal, screenWidth * PaperConstants.presetItemSpacing)
        }
    }
    
    /// 单个预设项视图
    /// - Parameter index: 预设索引
    /// - Returns: 预设项视图
    private func presetItem(index: Int) -> some View {
        Button(action: {
            onSelection(index)
        }) {
            VStack(spacing: screenHeight * PaperConstants.categorySpacing / 2) {
                // 预设预览区域
                presetPreview(index: index)
                
                // 预设标题
                presetTitle(index: index)
            }
        }
    }
    
    /// 预设预览视图
    /// - Parameter index: 预设索引
    /// - Returns: 预设预览视图
    private func presetPreview(index: Int) -> some View {
        Rectangle()
            .fill(PaperConstants.presetBackgroundColor)
            .frame(
                width: screenWidth * PaperConstants.presetItemWidth,
                height: screenHeight * PaperConstants.presetItemHeight
            )
            .overlay(alignment: .bottomTrailing) {
                // 选中状态指示器
                if isSelected(index) {
                    selectedIndicator
                }
            }
    }
    
    /// 选中状态指示器
    private var selectedIndicator: some View {
        Image(systemName: "checkmark.circle.fill")
            .foregroundColor(PaperConstants.checkmarkColor)
            .font(.system(size: screenHeight * PaperConstants.checkmarkSize))
            .offset(
                x: screenHeight * PaperConstants.checkmarkOffset,
                y: screenHeight * PaperConstants.checkmarkOffset
            )
    }
    
    /// 预设标题视图
    /// - Parameter index: 预设索引
    /// - Returns: 预设标题视图
    private func presetTitle(index: Int) -> some View {
        Text("预设\(index + 1)")
            .font(.system(size: screenHeight * PaperConstants.presetTitleFontSize))
            .foregroundColor(PaperConstants.presetTitleColor)
    }
}

// MARK: - 预览
#Preview {
    PresetSelectionView(
        title: "宝丽来",
        presetType: .polaroid,
        presetsCount: 5,
        isSelected: { index in index == 2 }, // 模拟第3个预设被选中
        onSelection: { index in
            print("选择了预设 \(index)")
        }
    )
    .background(Color.black)
}