import SwiftUI

/// 通用右下角添加按钮组件
struct AddButton: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 按钮点击事件
    var action: () -> Void
    
    // 移除所有初始化方法，因为不再需要控制padding
    
    var body: some View {
        Button(action: action) {
            Image("LH +")
                .resizable()
                .scaledToFit()
                .frame(width: screenHeight * 0.02, height: screenHeight * 0.02)
                .foregroundColor(.black)  // 设置图标为纯黑色
                .padding(screenHeight * 0.01)  // 内边距改为1%屏幕高度
                .background(Color.white.opacity(UIConstants.highOpacity))  // 使用常量替代硬编码值
                .clipShape(Circle())
                .shadow(color: Color.black.opacity(UIConstants.lightOpacity), radius: 4, x: 0, y: 2)
        }
        // 这里删除了原有的 padding 修饰符
    }
}

#Preview {
    ZStack(alignment: .bottomTrailing) {
        Color.gray.ignoresSafeArea()
        AddButton {
            print("添加按钮被点击")
        }
        .padding(.trailing, 20) // 在Preview中添加padding以便于查看
        .padding(.bottom, 20)
    }
} 