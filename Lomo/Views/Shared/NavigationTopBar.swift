import SwiftUI

// 导入包含ProLabel的文件
// import Lomo.OptionButton  // 使用Swift模块导入方式

/// 统一的顶部导航栏组件，用于所有页面
struct NavigationTopBar<Tab: Hashable>: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 导航栏显示模式
    enum NavigationMode {
        case normal    // 普通模式：可滚动，无按钮
        case editor    // 编辑模式：不可滚动，带有取消确定按钮
    }
    
    // 状态和数据
    @Binding var selectedTab: Tab
    let tabs: [(title: String, value: Tab, iconName: String?)]
    let onTabSelected: (Tab) -> Void
    var useIcons: Bool = false
    let iconWeight: Font.Weight
    var navigationMode: NavigationMode = .normal
    
    // 按钮回调
    var onCancelPressed: (() -> Void)?
    var onConfirmPressed: (() -> Void)?
    
    // 初始化方法 - 完整参数
    init(
        selectedTab: Binding<Tab>,
        tabs: [(title: String, value: Tab, iconName: String?)],
        onTabSelected: @escaping (Tab) -> Void,
        useIcons: Bool = false,
        iconWeight: Font.Weight = .regular,
        navigationMode: NavigationMode = .normal,
        onCancelPressed: (() -> Void)? = nil,
        onConfirmPressed: (() -> Void)? = nil
    ) {
        self._selectedTab = selectedTab
        self.tabs = tabs
        self.onTabSelected = onTabSelected
        self.useIcons = useIcons
        self.iconWeight = iconWeight
        self.navigationMode = navigationMode
        self.onCancelPressed = onCancelPressed
        self.onConfirmPressed = onConfirmPressed
    }
    
    // 兼容旧版初始化方法 - 带图标名称
    init(
        selectedTab: Binding<Tab>,
        tabs: [(title: String, value: Tab)],
        onTabSelected: @escaping (Tab) -> Void,
        useIcons: Bool = false,
        iconWeight: Font.Weight = .regular,
        navigationMode: NavigationMode = .normal,
        onCancelPressed: (() -> Void)? = nil,
        onConfirmPressed: (() -> Void)? = nil
    ) {
        self._selectedTab = selectedTab
        self.tabs = tabs.map { ($0.title, $0.value, nil) }
        self.onTabSelected = onTabSelected
        self.useIcons = useIcons
        self.iconWeight = iconWeight
        self.navigationMode = navigationMode
        self.onCancelPressed = onCancelPressed
        self.onConfirmPressed = onConfirmPressed
    }
    
    // 向后兼容方法 - 原始参数格式，保持旧接口
    init(
        selectedTab: Binding<Tab>,
        tabs: [(title: String, value: Tab)],
        onTabSelected: @escaping (Tab) -> Void,
        showActionButtons: Bool = false,
        iconWeight: Font.Weight = .regular,
        onCancelPressed: (() -> Void)? = nil,
        onConfirmPressed: (() -> Void)? = nil
    ) {
        self._selectedTab = selectedTab
        self.tabs = tabs.map { ($0.title, $0.value, nil) }
        self.onTabSelected = onTabSelected
        self.useIcons = false
        self.iconWeight = iconWeight
        self.navigationMode = showActionButtons ? .editor : .normal
        self.onCancelPressed = onCancelPressed
        self.onConfirmPressed = onConfirmPressed
    }
    
    var body: some View {
        print("🔍 [NavigationTopBar] body渲染 - navigationMode: \(navigationMode), tabs数量: \(tabs.count)")
        return HStack {
            // 根据模式不同显示不同内容
            if navigationMode == .editor {
                // 编辑模式：带取消确定按钮，不可滚动
                
                // 取消按钮
                Button(action: {
                    onCancelPressed?()
                }) {
                    Text("取消")
                        .font(.system(size: screenHeight * 0.015))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // 中间标题区域
                HStack(spacing: screenWidth * 0.06) {
                    ForEach(0..<tabs.count, id: \.self) { index in
                        let tab = tabs[index]
                        Button(action: {
                            print("🔍 [NavigationTopBar] 编辑模式按钮点击 - 标签: \(tab.title), 值: \(tab.value)")
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedTab = tab.value
                                print("🔍 [NavigationTopBar] 调用onTabSelected(\(tab.value))")
                                onTabSelected(tab.value)
                            }
                        }) {
                            if useIcons && tab.iconName != nil {
                                Image(systemName: tab.iconName!)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: screenHeight * UIConstants.headerIconSize, 
                                          height: screenHeight * UIConstants.headerIconSize)
                                    .fontWeight(iconWeight)
                                    .foregroundColor(selectedTab == tab.value ? UIConstants.dialIndicatorColor : .white)
                                    .animation(.easeInOut(duration: 0.2), value: selectedTab)
                            } else {
                                Text(tab.title)
                                    .font(.system(size: screenHeight * (selectedTab == tab.value ? 0.025 : 0.02), 
                                               weight: selectedTab == tab.value ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(selectedTab == tab.value ? 1 : 0.6)
                            }
                        }
                    }
                }
                
                Spacer()
                
                // 确定按钮
                Button(action: {
                    onConfirmPressed?()
                }) {
                    Text("确定")
                        .font(.system(size: screenHeight * 0.015))
                        .foregroundColor(.white)
                }
            } else {
                // 普通模式：可滚动，无按钮
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: screenWidth * 0.06) {
                        ForEach(0..<tabs.count, id: \.self) { index in
                            let tab = tabs[index]
                            Button(action: {
                                print("🔍 [NavigationTopBar] 普通模式按钮点击 - 标签: \(tab.title), 值: \(tab.value)")
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    selectedTab = tab.value
                                    print("🔍 [NavigationTopBar] 调用onTabSelected(\(tab.value))")
                                    onTabSelected(tab.value)
                                }
                            }) {
                                if useIcons && tab.iconName != nil {
                                    Image(systemName: tab.iconName!)
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: screenHeight * UIConstants.headerIconSize, 
                                              height: screenHeight * UIConstants.headerIconSize)
                                        .fontWeight(iconWeight)
                                        .foregroundColor(selectedTab == tab.value ? UIConstants.dialIndicatorColor : .white)
                                        .animation(.easeInOut(duration: 0.2), value: selectedTab)
                                } else {
                                    Text(tab.title)
                                        .font(.system(size: screenHeight * (selectedTab == tab.value ? 0.025 : 0.02), 
                                                   weight: selectedTab == tab.value ? .bold : .regular))
                                        .foregroundColor(.white)
                                        .opacity(selectedTab == tab.value ? 1 : 0.6)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, screenWidth * 0.05)
                }
            }
        }
        .padding(.horizontal, navigationMode == .editor ? screenWidth * 0.03 : 0)  // 编辑模式使用3%的屏幕宽度
        .padding(.top, screenHeight * UIConstants.navBarTopPadding)  // 上内边距为0.25%屏幕高度
        .padding(.bottom, navigationMode == .editor ? screenHeight * UIConstants.navBarVerticalPadding : screenHeight * UIConstants.navBarBottomPadding)  // 编辑模式下内边距1%，普通模式下内边距0.5%
    }
}

/// 带有右侧操作按钮的导航栏组件
struct NavigationTopBarWithAction<Tab: Hashable>: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 状态和数据
    @Binding var selectedTab: Tab
    let tabs: [(title: String, value: Tab)]
    let actionTitle: String
    let onTabSelected: (Tab) -> Void
    let onAction: () -> Void
    
    // 添加显示LOMO符号的标志
    var showLomoLogo: Bool = false
    
    // 用户状态 - 是否是Pro用户
    var isProUser: Bool = false
    
    // 初始化方法
    init(
        selectedTab: Binding<Tab>,
        tabs: [(title: String, value: Tab)],
        actionTitle: String,
        onTabSelected: @escaping (Tab) -> Void,
        onAction: @escaping () -> Void,
        showLomoLogo: Bool = false,
        isProUser: Bool = false
    ) {
        self._selectedTab = selectedTab
        self.tabs = tabs
        self.actionTitle = actionTitle
        self.onTabSelected = onTabSelected
        self.onAction = onAction
        self.showLomoLogo = showLomoLogo
        self.isProUser = isProUser
    }
    
    var body: some View {
        HStack {
            if showLomoLogo {
                // LOMO符号和Pro标签
                HStack(alignment: .bottom, spacing: screenWidth * 0.01) {
                    Image("LH Lomo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)           
                        .foregroundColor(.white)
                        .frame(height: screenHeight * 0.025)
                    
                    // 使用导航栏专用的Pro标签
                    NavProLabel(screenHeight: screenHeight, isProUser: isProUser)
                }
                
                Spacer()
                
                // 免费试用按钮 - 根据用户状态显示不同文本
                Button(action: onAction) {
                    Text(isProUser ? "已订阅" : actionTitle)
                        .font(.system(size: screenHeight * 0.015, weight: .medium))
                        .foregroundColor(.black)
                        .padding(.horizontal, screenWidth * 0.03)
                        .padding(.vertical, screenHeight * 0.005)
                        .background(UIConstants.dialIndicatorColor)
                        .cornerRadius(UIConstants.freeTrialButtonCornerRadius)
                }
            } else {
                // 主标题区域
                Spacer()
                HStack(spacing: screenWidth * 0.04) {
                    ForEach(0..<tabs.count, id: \.self) { index in
                        let tab = tabs[index]
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.2)) {
                                selectedTab = tab.value
                                onTabSelected(tab.value)
                            }
                        }) {
                            Text(tab.title)
                                .font(.system(size: screenHeight * (selectedTab == tab.value ? 0.025 : 0.02), 
                                           weight: selectedTab == tab.value ? .bold : .regular))
                                .foregroundColor(.white)
                                .opacity(selectedTab == tab.value ? 1 : 0.6)
                        }
                    }
                }
                Spacer()
            }
        }
        .padding(.horizontal, screenWidth * 0.05)  // 统一使用5%的屏幕宽度
        .padding(.top, screenHeight * UIConstants.navBarTopPadding)  // 上内边距为0.25%屏幕高度
        .padding(.bottom, screenHeight * UIConstants.navBarVerticalPadding)  // 下内边距为1%屏幕高度
    }
}

/// 相册页专用导航栏，带有右侧选择按钮
struct GalleryNavigationTopBar<Tab: Hashable>: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 状态和数据
    @Binding var selectedTab: Tab
    let tabs: [(title: String, value: Tab)]
    let onTabSelected: (Tab) -> Void
    let onSelectPressed: () -> Void
    
    // 初始化方法
    init(
        selectedTab: Binding<Tab>,
        tabs: [(title: String, value: Tab)],
        onTabSelected: @escaping (Tab) -> Void,
        onSelectPressed: @escaping () -> Void
    ) {
        self._selectedTab = selectedTab
        self.tabs = tabs
        self.onTabSelected = onTabSelected
        self.onSelectPressed = onSelectPressed
    }
    
    var body: some View {
        HStack {
            Spacer()
            HStack(spacing: screenWidth * 0.04) {
                ForEach(0..<tabs.count, id: \.self) { index in
                    let tab = tabs[index]
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedTab = tab.value
                            onTabSelected(tab.value)
                        }
                    }) {
                        Text(tab.title)
                            .font(.system(size: screenHeight * (selectedTab == tab.value ? 0.025 : 0.02), 
                                       weight: selectedTab == tab.value ? .bold : .regular))
                            .foregroundColor(.white)
                            .opacity(selectedTab == tab.value ? 1 : 0.6)
                    }
                }
            }
            
            Spacer()
            
            // 选择按钮
            Button(action: onSelectPressed) {
                Text("选择")
                    .font(.system(size: screenHeight * UIConstants.tabFontSize))
                    .foregroundColor(.white)
            }
        }
        .padding(.horizontal, screenWidth * 0.05)
        .padding(.top, screenHeight * UIConstants.navBarTopPadding)  // 上内边距为0.25%屏幕高度
        .padding(.bottom, screenHeight * UIConstants.navBarBottomPadding)  // 下内边距为0.5%屏幕高度
    }
}

// 专用于导航栏的Pro标签组件
fileprivate struct NavProLabel: View {
    let screenHeight: CGFloat
    let isProUser: Bool
    
    var body: some View {
        Text("PRO")
            .font(.system(size: screenHeight * 0.01, weight: .medium))
            .foregroundColor(.white)
            .padding(.horizontal, 2)
            .padding(.vertical, 1)
            .background(isProUser ? UIConstants.dialIndicatorColor.opacity(UIConstants.effectOpacity) : Color.gray.opacity(UIConstants.effectOpacity))
            .clipShape(RoundedRectangle(cornerRadius: 2))
    }
} 