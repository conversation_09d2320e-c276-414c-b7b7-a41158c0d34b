import SwiftUI

struct BottomTabBar: View {
    @ObservedObject var viewModel: SharedTabViewModel

    // 水印服务
    private let watermarkService: WatermarkService

    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height

    // 初始化方法
    init(viewModel: SharedTabViewModel, watermarkService: WatermarkService = WatermarkService()) {
        self.viewModel = viewModel
        self.watermarkService = watermarkService
    }
    
    // 获取底部安全区域高度
    private var bottomSafeAreaHeight: CGFloat {
        UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0
    }
    
    // 背景圆形尺寸常量
    private let backgroundCircleSize: CGFloat = UIScreen.main.bounds.height * 0.035
    
    // 获取当前模式下的标签页数组
    private var currentTabs: [TabBarItem] {
        viewModel.isSelectionMode ? TabBarItem.selectionModeTabs : TabBarItem.allTabs
    }
    
    var body: some View {
        HStack(alignment: .top, spacing: 0) {
            if viewModel.isSelectionMode {
                // 选择模式下的布局：三个按钮均匀分布
                ForEach(currentTabs) { tab in
                    Spacer()
                    
                    Button(action: {
                        // 获取当前激活的水印类型
                        let watermarkSettings = watermarkService.getSettings()
                        let watermarkType = watermarkSettings.activeWatermarkStyleType
                        let isPuzzleWatermarkActive = watermarkType == "custom23" || watermarkType == "custom24"
                        
                        // 检查是否有选中的照片
                        if viewModel.selectedPhotosCount > 0 {
                            // 为拼图水印类型添加特殊限制
                            if tab.tag == 3 { // 编辑按钮
                                if isPuzzleWatermarkActive {
                                    // 确定所需照片数量
                                    let requiredCount: Int
                                    if watermarkType == "custom23" {
                                        requiredCount = 4 // 自定义水印23需要4张照片
                                    } else if watermarkType == "custom24" {
                                        // 自定义水印24支持2-5张照片，这里不再使用固定数量
                                        // 只检查是否在合法范围内
                                        if viewModel.selectedPhotosCount >= 2 && viewModel.selectedPhotosCount <= 5 {
                                            // 如果照片数量在2-5张范围内，直接继续处理
                                            // 设置requiredCount为当前选择的照片数量
                                            requiredCount = viewModel.selectedPhotosCount
                                        } else {
                                            // 如果照片数量不在2-5张范围内，显示提示
                                            let message = "此拼图水印支持2-5张照片使用，比例以第一张照片为准"
                                            NotificationCenter.default.post(
                                                name: Notification.Name("ShowPuzzleWatermarkAlert"),
                                                object: message
                                            )
                                            return
                                        }
                                    } else {
                                        requiredCount = 4 // 默认为4张
                                    }
                                    
                                    // 检查照片数量是否符合要求（仅对水印23和其他拼图水印进行检查）
                                    if watermarkType != "custom24" && viewModel.selectedPhotosCount != requiredCount {
                                        // 如果是拼图水印但照片数量不符合要求，显示提示
                                        let message: String
                                        if watermarkType == "custom23" {
                                            message = "此拼图水印仅支持四张照片使用，比例以第一张照片为准"
                                        } else if watermarkType == "custom24" {
                                            message = "此拼图水印支持2-5张照片使用，比例以第一张照片为准"
                                        } else {
                                            message = "此拼图水印需要特定数量的照片，请重新选择"
                                        }
                                        
                                        NotificationCenter.default.post(
                                            name: Notification.Name("ShowPuzzleWatermarkAlert"),
                                            object: message
                                        )
                                        return
                                    }
                                }
                                
                                // 获取选中的 assets
                                let assetsToEdit = viewModel.currentGallerySelectedAssets

                                // 通知系统准备编辑选中的照片
                                // IMPORTANT: Post notification *before* changing states that might clear selections
                                NotificationCenter.default.post(
                                    name: Notification.Name("EditSelectedPhotos"),
                                    object: assetsToEdit // 传递 PHAsset 数组
                                )

                                // 返回到用户之前所在的页面
                                if let previousTab = viewModel.previousTabBeforePhotoSelection {
                                    print("🔄 返回到之前的页面: \(previousTab)")

                                    // 保存编辑子页面信息，在页面跳转后使用
                                    let previousEditCategory = viewModel.previousEditCategoryBeforePhotoSelection

                                    viewModel.switchTab(to: previousTab)

                                    // 如果是编辑页面，延迟恢复编辑子页面，确保页面已经加载
                                    if previousTab == .watermark, let editCategory = previousEditCategory {
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                            print("🔄 恢复编辑子页面: \(editCategory)")
                                            // 通知EditView恢复到指定的编辑子页面
                                            NotificationCenter.default.post(
                                                name: Notification.Name("RestoreEditCategory"),
                                                object: editCategory
                                            )
                                        }
                                    }

                                    // 清除记录
                                    viewModel.previousTabBeforePhotoSelection = nil
                                    viewModel.previousEditCategoryBeforePhotoSelection = nil
                                } else {
                                    print("⚠️ 没有记录之前的页面，保持在相册页面")
                                }

                                // 恢复默认模式
                                viewModel.isSelectionMode = false
                                viewModel.selectedPhotosCount = 0
                            } else if tab.tag == 5 { // 分享按钮
                                // 处理分享逻辑
                                print("分享选中的照片")
                            } else if tab.tag == 6 { // 删除按钮
                                // 处理删除逻辑
                                print("删除选中的照片")
                            }
                        } else {
                            // 没有选中照片，按钮点击无效
                            print("请先选择照片")
                        }
                    }) {
                        VStack(alignment: .center, spacing: screenHeight * 0.001) {
                            // 自定义图标区域
                            VStack {
                                if viewModel.selectedTab == tab {
                                    // 选中状态：黄色图标
                                    if tab.tag == 3 { // 编辑图标
                                        Image(systemName: "note")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                            .symbolRenderingMode(.hierarchical)
                                            .foregroundColor(UIConstants.dialIndicatorColor)
                                    } else { // 其他图标
                                        Image(systemName: tab.iconName)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                            .symbolRenderingMode(.hierarchical)
                                            .foregroundColor(UIConstants.dialIndicatorColor)
                                    }
                                } else {
                                    // 未选中状态：灰色图标
                                    if tab.tag == 3 { // 编辑图标
                                        Image(systemName: "note")
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                            .symbolRenderingMode(.hierarchical)
                                            .foregroundColor(.gray)
                                    } else { // 其他图标
                                        Image(systemName: tab.iconName)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                            .symbolRenderingMode(.hierarchical)
                                            .foregroundColor(.gray)
                                    }
                                }
                            }
                            .frame(width: screenHeight * 0.04, height: screenHeight * 0.03)
                            
                            // 文字标题
                            Text(tab.title)
                                .font(.system(size: screenHeight * 0.0125, 
                                           weight: viewModel.selectedTab == tab ? .bold : .regular))
                                .foregroundColor(viewModel.selectedTab == tab ? UIConstants.dialIndicatorColor : .gray)
                        }
                        .frame(width: screenHeight * 0.05, height: screenHeight * 0.045)
                        // 如果没有选中照片，降低按钮透明度
                        .opacity(viewModel.selectedPhotosCount > 0 ? 1.0 : 0.5)
                    }
                    
                    Spacer()
                }
            } else {
                // 默认模式下的布局：五个按钮
                ForEach(currentTabs) { tab in
                    Spacer()
                    
                    Button(action: {
                        if tab.isSpecial {
                            // 拍摄按钮 - 返回相机界面
                            viewModel.hide()
                        } else {
                            // 普通标签 - 切换标签页
                            viewModel.switchTab(to: tab)
                        }
                    }) {
                        if tab.isSpecial {
                            // 拍摄按钮样式
                            VStack(alignment: .center, spacing: screenHeight * 0.001) {
                                // 自定义图标区域
                                VStack {
                                    Image(systemName: "inset.filled.circle")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                        .foregroundColor(.gray)
                                }
                                .frame(width: screenHeight * 0.04, height: screenHeight * 0.03)
                                
                                // 文字标题
                                Text("拍摄")
                                    .font(.system(size: screenHeight * 0.0125, 
                                               weight: .regular))
                                    .foregroundColor(.gray)
                            }
                            .frame(width: screenHeight * 0.05, height: screenHeight * 0.045)
                        } else {
                            // 普通按钮样式
                            VStack(alignment: .center, spacing: screenHeight * 0.001) {
                                // 自定义图标区域
                                VStack {
                                    if viewModel.selectedTab == tab {
                                        // 选中状态：黄色图标（不再使用圆形背景）
                                        if tab.tag == 4 { // 设置图标
                                            Image("LH Setting")
                                                .resizable()
                                                .renderingMode(.template)
                                                .aspectRatio(contentMode: .fit)
                                                .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                .foregroundColor(UIConstants.dialIndicatorColor) // 使用黄色
                                        } else { // 普通图标
                                            Image(systemName: tab.iconName)
                                                .resizable()
                                                .aspectRatio(contentMode: .fit)
                                                .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                                .symbolRenderingMode(tab.tag == 1 ? .monochrome : .hierarchical)
                                                .foregroundColor(UIConstants.dialIndicatorColor) // 使用黄色
                                        }
                                    } else {
                                        // 未选中状态：只有灰色图标
                                        if tab.tag == 4 { // 设置图标
                                            Image("LH Setting")
                                                .resizable()
                                                .renderingMode(.template)
                                                .aspectRatio(contentMode: .fit)
                                                .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                                .foregroundColor(.gray)
                                        } else { // 普通图标
                                            Image(systemName: tab.iconName)
                                                .resizable()
                                                .aspectRatio(contentMode: .fit)
                                                .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                                                .symbolRenderingMode(tab.tag == 1 ? .monochrome : .hierarchical)
                                                .foregroundColor(.gray)
                                        }
                                    }
                                }
                                .frame(width: screenHeight * 0.04, height: screenHeight * 0.03)
                                
                                // 文字标题
                                Text(tab.title)
                                    .font(.system(size: screenHeight * 0.0125, 
                                               weight: viewModel.selectedTab == tab ? .bold : .regular))
                                    .foregroundColor(viewModel.selectedTab == tab ? UIConstants.dialIndicatorColor : .gray)
                            }
                            .frame(width: screenHeight * 0.05, height: screenHeight * 0.045)
                        }
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(.top, screenHeight * 0.01) // 整体顶部添加1%的内边距
        .frame(width: screenWidth, height: screenHeight * 0.06 + bottomSafeAreaHeight, alignment: .top)
        .background(
            // 使用 ultraThinMaterial 实现磨砂效果
            ZStack {
                Color.black.opacity(0.5)  // 从0.4加深到0.5
                Rectangle()
                    .fill(.ultraThinMaterial)  // 磨砂效果
            }
            .edgesIgnoringSafeArea(.bottom)  // 忽略底部安全区域，确保背景延伸到屏幕底部
        )
    }
}

struct BottomTabBar_Previews: PreviewProvider {
    static var previews: some View {
        BottomTabBar(viewModel: SharedTabViewModel())
            .previewLayout(.sizeThatFits)
    }
} 