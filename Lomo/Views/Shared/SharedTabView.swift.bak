import SwiftUI
import Photos
import PhotosUI

// 如果 PreviewScrollView 是在单独的文件中定义的，则需要导入
// 注意：如果 Views/Components 是同一模块内的目录，可能不需要特别导入

struct SharedTabView: View {
    @ObservedObject var viewModel: SharedTabViewModel
    @ObservedObject var cameraViewModel: CameraViewModel
    @ObservedObject var filterViewModel: FilterViewModel
    @ObservedObject var editViewModel: EditViewModel
    @ObservedObject var galleryViewModel: GalleryViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 底部标签栏的高度（包括安全区域）
    private let tabBarHeight: CGFloat = UIScreen.main.bounds.height * 0.08 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Color(uiColor: .systemGray6).ignoresSafeArea()
                
                // 内容视图
                VStack(spacing: 0) {
                    // 内容区域 - 根据选中的标签显示相应的页面
                    ZStack {
                        // 根据选中的标签显示相应的页面，使用固定顶部的布局结构
                        // 重要：不再整体包裹ScrollView，而是让每个页面自行处理滚动区域
                        switch viewModel.selectedTab.tag {
                        case 0: // 相册
                            GalleryViewFixed(viewModel: galleryViewModel)
                        case 1: // 滤镜
                            FilterViewFixed(viewModel: filterViewModel)
                        case 3: // 编辑
                            EditViewFixed(cameraViewModel: cameraViewModel, viewModel: viewModel, editViewModel: editViewModel)
                        case 4: // 设置
                            SettingsView()
                        default:
                            GalleryViewFixed(viewModel: galleryViewModel)
                        }
                    }
                    .frame(height: geometry.size.height)
                    
                    Spacer(minLength: 0)
                }
                
                // 底部标签栏 - 固定在底部
                VStack {
                    Spacer()
                    BottomTabBar(viewModel: viewModel)
                }
                .ignoresSafeArea(.keyboard) // 确保键盘不会推动底部标签栏
            }
        }
    }
}

// 修改后的GalleryView，顶部标题固定
struct GalleryViewFixed: View {
    @ObservedObject var viewModel: GalleryViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 照片网格布局
    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible()),
        GridItem(.flexible())
    ]
    
    var body: some View {
        VStack(spacing: 0) {
            // 顶部导航（固定）
            HStack {
                // 主标题区域
                HStack(spacing: screenWidth * 0.06) {
                    // 我的作品选项卡
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            viewModel.switchMainTab(.myWorks)
                        }
                    }) {
                        Text("我的作品")
                            .font(.system(size: screenHeight * (viewModel.selectedMainTab == .myWorks ? 0.03 : 0.02), 
                                       weight: viewModel.selectedMainTab == .myWorks ? .bold : .regular))
                            .foregroundColor(.white)
                            .opacity(viewModel.selectedMainTab == .myWorks ? 1 : 0.6)
                    }
                    
                    // 相册选项卡
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            viewModel.switchMainTab(.gallery)
                        }
                    }) {
                        Text("相册")
                            .font(.system(size: screenHeight * (viewModel.selectedMainTab == .gallery ? 0.03 : 0.02), 
                                       weight: viewModel.selectedMainTab == .gallery ? .bold : .regular))
                            .foregroundColor(.white)
                            .opacity(viewModel.selectedMainTab == .gallery ? 1 : 0.6)
                    }
                }
                
                Spacer()
                
                // 选择按钮
                Button(action: {}) {
                    Text("选择")
                        .font(.system(size: screenHeight * UIConstants.tabFontSize))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, screenWidth * 0.04)
            .padding(.vertical, 10)
            .background(Color(uiColor: .systemGray6))
            
            // 照片网格 - 可滚动内容区域
            ScrollView {
                LazyVGrid(columns: columns, spacing: 1) {
                    ForEach(0..<30) { _ in
                        Color.gray
                            .aspectRatio(1, contentMode: .fill)
                    }
                }
            }
        }
    }
}

// 修改后的FilterView，顶部标题固定
struct FilterViewFixed: View {
    @ObservedObject var viewModel: FilterViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        ZStack(alignment: .bottomTrailing) {  // 使用ZStack包裹内容并设置右下角对齐
            VStack(spacing: 0) {
                // 顶部导航 - 滤镜类别（固定）
                HStack {
                    // 主标题区域 - 滤镜类别选项
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.06) {
                            // 收藏选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .favorites)
                                }
                            }) {
                                Text("收藏")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .favorites ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .favorites ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .favorites ? 1 : 0.6)
                            }
                            
                            // 胶片选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .film)
                                }
                            }) {
                                Text("胶片")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .film ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .film ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .film ? 1 : 0.6)
                            }
                            
                            // 宝丽来选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .polaroid)
                                }
                            }) {
                                Text("宝丽来")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .polaroid ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .polaroid ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .polaroid ? 1 : 0.6)
                            }
                            
                            // 自然选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .nature)
                                }
                            }) {
                                Text("自然")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .nature ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .nature ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .nature ? 1 : 0.6)
                            }
                            
                            // 清新选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .fresh)
                                }
                            }) {
                                Text("清新")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .fresh ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .fresh ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .fresh ? 1 : 0.6)
                            }
                            
                            // 复古选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .vintage)
                                }
                            }) {
                                Text("复古")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .vintage ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .vintage ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .vintage ? 1 : 0.6)
                            }
                            
                            // 黑白选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .blackAndWhite)
                                }
                            }) {
                                Text("黑白")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .blackAndWhite ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .blackAndWhite ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .blackAndWhite ? 1 : 0.6)
                            }
                            
                            // 自定义选项卡
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    viewModel.loadFilters(for: .custom)
                                }
                            }) {
                                Text("自定义")
                                    .font(.system(size: screenHeight * (viewModel.selectedCategory == .custom ? 0.03 : 0.02), 
                                               weight: viewModel.selectedCategory == .custom ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(viewModel.selectedCategory == .custom ? 1 : 0.6)
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                    }
                }
                .padding(.vertical, screenHeight * 0.0075)  // 改为0.75%的屏幕高度
                .background(Color(uiColor: .systemGray6))
                
                // 滤镜内容区域 - 可滚动
                ScrollView {
                    // 滤镜列表
                    VStack(spacing: 0) {
                        ForEach(viewModel.filters) { filter in
                            FilterItemView(filter: filter) { selectedFilter in
                                viewModel.selectFilter(selectedFilter)
                            }
                            .padding(.horizontal, screenWidth * 0.04)
                        }
                    }
                    .padding(.vertical, screenHeight * 0.0075)  // 修改为0.75%
                }
            }
            .onAppear {
                // 确保初始加载胶片类别的滤镜
                viewModel.loadFilters(for: .film)
            }
            
            // 使用共享的添加按钮组件
            AddButton {
                // 滤镜添加按钮点击逻辑
                print("滤镜页面添加按钮被点击")
            }
        }
    }
}

// 修改后的WatermarkView，顶部标题固定
struct EditViewFixed: View {
    // 添加对CameraViewModel的依赖
    @ObservedObject var cameraViewModel: CameraViewModel
    // 添加对SharedTabViewModel的依赖
    @ObservedObject var viewModel: SharedTabViewModel
    // 添加对EditViewModel的依赖
    @ObservedObject var editViewModel: EditViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 预览图片尺寸
    private let previewHeight: CGFloat
    
    init(cameraViewModel: CameraViewModel, viewModel: SharedTabViewModel, editViewModel: EditViewModel) {
        self.cameraViewModel = cameraViewModel
        self.viewModel = viewModel
        self.editViewModel = editViewModel
        // 预览区域高度设置为屏幕高度的 50%
        previewHeight = UIScreen.main.bounds.height * 0.5
    }
    
    var body: some View {
            VStack(spacing: 0) {
            // 1. 固定的顶部导航
                HStack {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.06) {
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .crop
                                }
                            }) {
                                Text("构图")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .crop ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .crop ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .crop ? 1 : 0.6)
                            }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .filter
                                }
                            }) {
                                Text("滤镜")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .filter ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .filter ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .filter ? 1 : 0.6)
                            }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .adjust
                                }
                            }) {
                                Text("调节")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .adjust ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .adjust ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .adjust ? 1 : 0.6)
                            }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .watermark
                                }
                            }) {
                                Text("水印")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .watermark ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .watermark ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .watermark ? 1 : 0.6)
                            }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .effect
                                }
                            }) {
                                Text("特效")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .effect ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .effect ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .effect ? 1 : 0.6)
                            }
                            
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.2)) {
                                    editViewModel.selectedCategory = .paper
                                }
                            }) {
                                Text("相纸")
                                    .font(.system(size: screenHeight * (editViewModel.selectedCategory == .paper ? 0.03 : 0.02), 
                                               weight: editViewModel.selectedCategory == .paper ? .bold : .regular))
                                    .foregroundColor(.white)
                                    .opacity(editViewModel.selectedCategory == .paper ? 1 : 0.6)
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                    }
                }
                .padding(.vertical, screenHeight * 0.0075)  // 改为0.75%的屏幕高度
                .background(Color(uiColor: .systemGray6))
                
            // 2. 共享的预览区域（固定，所有选项卡共用）
            ZStack {
                // 使用相同的相机会话，但尺寸为屏幕高度的50%
                if cameraViewModel.state.session.isRunning {
                    CameraPreviewView(session: cameraViewModel.state.session, 
                                      frame: CGRect(x: 0, y: 0, width: screenWidth, height: previewHeight))
                    .frame(height: previewHeight)
                } else {
                    // 如果相机未运行，显示占位符
                    Rectangle()
                        .fill(Color.black)
                        .frame(height: previewHeight)
                }
                
                // TODO: 这里添加预览内容，如照片、水印、特效等
            }
            
            // 3. 根据选中的类别显示不同的控制区域内容
            if editViewModel.selectedCategory == .crop {
                // 裁切控制区域
                CropControlView(viewModel: viewModel)
            } else if editViewModel.selectedCategory == .filter {
                // 滤镜控制区域
                FilterControlView()
            } else if editViewModel.selectedCategory == .adjust {
                // 调节控制区域
                AdjustControlView()
            } else if editViewModel.selectedCategory == .watermark {
                // 水印控制区域
                WatermarkControlView()
            } else if editViewModel.selectedCategory == .effect {
                // 特效控制区域
                EffectControlView(viewModel: cameraViewModel)
            } else if editViewModel.selectedCategory == .paper {
                // 相纸控制区域
                PaperControlView()
                }
            }
            .onAppear {
                editViewModel.selectedCategory = .watermark
        }
    }
}

// 添加通用的预览框组件
struct PreviewScrollView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 预览项数量
    let itemCount: Int
    // 预览项点击回调
    let onItemTap: (Int) -> Void
    
    var body: some View {
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.02) {
                ForEach(0..<itemCount, id: \.self) { index in
                    Button(action: {
                        onItemTap(index)
                    }) {
                        Rectangle()
                            .fill(Color.gray)
                            .frame(width: screenHeight * 0.06, height: screenHeight * 0.06)
                            .cornerRadius(4)
                    }
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0075)  // 修改为0.75%
            }
            .background(Color(uiColor: .systemGray6))
    }
}

// 修改水印控制视图组件
struct WatermarkControlView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        VStack(spacing: 0) {
            // 使用通用的预览框组件
            PreviewScrollView(itemCount: 10) { index in
                // 处理预览项点击
                print("预览项 \(index) 被点击")
            }
            
            // 水印选项列表
            ScrollView {
                VStack(spacing: 0) {
                    WatermarkOptionItem(title: "Logo") {
                        // LOGO点击事件处理
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    WatermarkOptionItem(title: "文字") {
                        // 文字点击事件处理
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    WatermarkOptionItem(title: "署名") {
                        // 署名点击事件处理
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    WatermarkOptionItem(title: "偏好") {
                        // 偏好点击事件处理
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    WatermarkOptionItem(title: "位置") {
                        // 位置点击事件处理
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.0075)
            }
            }
            .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)  // 添加6%的底部安全区域
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}

// 特效控制视图组件
struct EffectControlView: View {
    @ObservedObject var viewModel: CameraViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 效果参数控制
    @State private var selectedTimeStyle = "digital_orange"
    @State private var grainIntensity: Double = 0.5
    @State private var scratchIntensity: Double = 0.5
    @State private var leakIntensity: Double = 0.5
    
    // 根据预设ID返回对应的颜色
    private func colorForPreset(_ presetId: String) -> Color {
        switch presetId {
        case "warm":
            return Color.orange.opacity(0.8)
        case "cool":
            return Color.blue.opacity(0.6)
        case "vintage":
            return Color(red: 0.8, green: 0.6, blue: 0.4).opacity(0.7)
        case "dramatic":
            return Color.purple.opacity(0.6)
        case "soft":
            return Color.pink.opacity(0.5)
        case "golden":
            return Color.yellow.opacity(0.7)
        default:
            return Color.orange.opacity(0.6)
        }
    }
    
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 时间特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                    Text("时间")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                        
                        Spacer()
                    }
                        .padding(.horizontal, screenWidth * 0.04)
                    
                    // 时间样式水平滑动
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 橙色数字时钟样式
                            Button(action: {
                                selectedTimeStyle = "digital_orange"
                            }) {
                                ZStack(alignment: .bottomTrailing) {
                                    // 时间样式预览
                                    Text("3 16 '25")
                                        .font(.system(size: screenHeight * 0.025, weight: .medium))
                                        .foregroundColor(Color.orange)
                                        .padding(.vertical, screenHeight * 0.0125)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(Color.black)
                                        )
                                    
                                    // 选中图标
                                    if selectedTimeStyle == "digital_orange" {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(UIConstants.dialIndicatorColor)
                                            .font(.system(size: screenHeight * 0.015))
                                    }
                                }
                            }
                            
                            // 红色数字时钟样式
                            Button(action: {
                                selectedTimeStyle = "digital_red"
                            }) {
                                ZStack(alignment: .bottomTrailing) {
                                    // 时间样式预览
                                    Text("3 16 '25")
                                        .font(.system(size: screenHeight * 0.025, weight: .medium))
                                        .foregroundColor(Color.red)
                                        .padding(.vertical, screenHeight * 0.0125)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(Color.black)
                                        )
                                    
                                    // 选中图标
                                    if selectedTimeStyle == "digital_red" {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(UIConstants.dialIndicatorColor)
                                            .font(.system(size: screenHeight * 0.015))
                                    }
                                }
                            }
                            
                            // 点阵数字时钟样式
                            Button(action: {
                                selectedTimeStyle = "digital_dot"
                            }) {
                                ZStack(alignment: .bottomTrailing) {
                                    // 时间样式预览
                                    Text("3 16 '25")
                                        .font(.custom("Menlo", size: screenHeight * 0.025))
                                        .foregroundColor(Color.orange.opacity(0.8))
                                        .padding(.vertical, screenHeight * 0.0125)
                                        .background(
                                            RoundedRectangle(cornerRadius: 8)
                                                .fill(Color.black)
                                        )
                                    
                                    // 选中图标
                                    if selectedTimeStyle == "digital_dot" {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(UIConstants.dialIndicatorColor)
                                            .font(.system(size: screenHeight * 0.015))
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.04)
                    }
                }
                .padding(.vertical, screenHeight * 0.0125)
                
                // 颗粒特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("颗粒")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Text(String(format: "%.0f%%", grainIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { grainIntensity },
                        set: { newValue in
                            grainIntensity = newValue
                            viewModel.updateGrainIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )
                    
                    // 颗粒预设选择
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(viewModel.availableGrainPresets) { preset in
                            Button(action: {
                                viewModel.selectGrainPreset(preset)
                            }) {
                                Text(preset.name)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(viewModel.grainParameters.selectedPreset == preset ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(viewModel.grainParameters.selectedPreset == preset ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.vertical, screenHeight * 0.0125)
                .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.0125)
                
                // 划痕特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("划痕")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Text(String(format: "%.0f%%", scratchIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { scratchIntensity },
                        set: { newValue in
                            scratchIntensity = newValue
                            viewModel.updateScratchIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )
                    
                    // 划痕预设选择
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(viewModel.availableScratchPresets) { preset in
                            Button(action: {
                                viewModel.selectScratchPreset(preset)
                            }) {
                                Text(preset.name)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))
                                    .foregroundColor(viewModel.scratchParameters.selectedPreset == preset ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(viewModel.scratchParameters.selectedPreset == preset ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    .padding(.vertical, screenHeight * 0.0125)
                .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.0125)
                
                // 漏光特效
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    HStack {
                        Text("漏光")
                            .font(.system(size: screenHeight * 0.02, weight: .bold))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                        Text(String(format: "%.0f%%", leakIntensity * 100))
                            .font(.system(size: screenHeight * 0.02, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                    // 强度滑块
                    CustomSlider(
                        value: Binding(
                        get: { leakIntensity },
                        set: { newValue in
                            leakIntensity = newValue
                            viewModel.updateLightLeakIntensity(newValue)
                        }
                        ),
                        range: 0...1
                    )
                    
                    // 漏光预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 随机按钮
                            Button(action: {
                                // 随机选择一个预设
                                if let randomPreset = viewModel.availableLightLeakPresets.randomElement() {
                                    viewModel.selectLightLeakPreset(randomPreset)
                                }
                            }) {
                                VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                    // 随机按钮预览图
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color(uiColor: .systemGray5))
                                        .frame(width: screenHeight * 0.05, height: screenHeight * 0.05)
                                        .overlay(
                                            Image(systemName: "shuffle")
                                                .font(.system(size: screenHeight * 0.02, weight: .medium))
                                                .foregroundColor(.white)
                                        )
                                    
                                    // 预设名称
                                    Text("随机")
                                        .font(.system(size: screenHeight * 0.012, weight: .medium))
                                        .foregroundColor(.white)
                                }
                            }
                            
                            // 其他预设
                            ForEach(viewModel.availableLightLeakPresets) { preset in
                                Button(action: {
                                    viewModel.selectLightLeakPreset(preset)
                                }) {
                                    VStack(alignment: .center, spacing: screenHeight * 0.005) {
                                        // 预设效果预览图
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(colorForPreset(preset.id))
                                            .frame(width: screenHeight * 0.05, height: screenHeight * 0.05)
                                            .overlay(alignment: .bottomTrailing) {
                                                if viewModel.lightLeakParameters.selectedPreset == preset {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        // 预设名称
                                        Text(preset.name)
                                            .font(.system(size: screenHeight * 0.012, weight: .medium))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                }
                .padding(.horizontal, screenWidth * 0.04)
                }
                .padding(.vertical, screenHeight * 0.0125)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .onAppear {
            // 初始化UI状态
            leakIntensity = viewModel.lightLeakParameters.intensity
            grainIntensity = viewModel.grainParameters.intensity
            scratchIntensity = viewModel.scratchParameters.intensity
            
            // 确保效果系统已初始化
            viewModel.setupEffects()
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}

// 颗粒图案视图 - 生成随机噪点图案
struct GrainPatternView: View {
    let size: Double
    let density: Double
    let contrast: Double
    let opacity: Double
    
    @State private var randomSeed = Int.random(in: 0...1000)
    
    var body: some View {
        Canvas { context, size in
            // 创建噪点图案
            context.drawLayer { ctx in
                // 基于size、density和contrast参数计算噪点
                let pointCount = Int(size.width * size.height * CGFloat(density) * 0.1)
                let pointSize = CGFloat(self.size) * 1.5
                
                for _ in 0..<pointCount {
                    let x = CGFloat.random(in: 0...size.width)
                    let y = CGFloat.random(in: 0...size.height)
                    let alpha = CGFloat.random(in: 0...CGFloat(contrast))
                    
                    ctx.opacity = alpha * CGFloat(opacity)
                    ctx.fill(Path(ellipseIn: CGRect(x: x, y: y, width: pointSize, height: pointSize)), with: .color(.white))
                }
            }
        }
    }
}

// 划痕图案视图 - 生成随机划痕图案
struct ScratchPatternView: View {
    let density: Double
    let length: Double
    let width: Double
    let opacity: Double
    
    @State private var randomSeed = Int.random(in: 0...1000)
    
    var body: some View {
        Canvas { context, size in
            // 创建划痕图案
            context.drawLayer { ctx in
                // 计算划痕数量
                let scratchCount = Int(size.width * size.height * CGFloat(density) * 0.01)
                
                for _ in 0..<scratchCount {
                    // 随机位置
                    let x = CGFloat.random(in: 0...size.width)
                    let y = CGFloat.random(in: 0...size.height)
                    
                    // 随机长度和角度
                    let angle: CGFloat = CGFloat.random(in: 0...(2 * .pi))
                    let len = CGFloat(length) * CGFloat.random(in: 0.3...1.0) * min(size.width, size.height) * 0.5
                    let strokeWidth = CGFloat(width) * CGFloat.random(in: 0.5...1.0) * 1.5
                    let alpha = CGFloat.random(in: 0.5...1.0) * CGFloat(opacity)
                    
                    ctx.opacity = alpha
                    ctx.stroke(
                        Path { path in
                            path.move(to: CGPoint(x: x, y: y))
                            path.addLine(to: CGPoint(
                                x: x + cos(angle) * len,
                                y: y + sin(angle) * len
                            ))
                        },
                        with: .color(.white),
                        lineWidth: strokeWidth
                    )
                }
            }
        }
    }
}

// 相纸控制视图组件
struct PaperControlView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 选中的预设
    @State private var selectedPolaroidPreset = 0
    @State private var selectedFilmPreset = 0
    @State private var selectedVintagePreset = 0
    @State private var selectedFashionPreset = 0
    @State private var selectedINSPreset = 0
    
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
                // 宝丽来
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("宝丽来")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedPolaroidPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedPolaroidPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 胶卷
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("胶卷")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFilmPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedFilmPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 复古
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("复古")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedVintagePreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedVintagePreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                .foregroundColor(.white)
                        }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 时尚
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("时尚")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFashionPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedFashionPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // INS
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("INS")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedINSPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedINSPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}

// 水印选项项目组件
struct WatermarkOptionItem: View {
    let title: String
    let action: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 为文字和署名添加状态变量
    @State private var textInput: String = ""
    @State private var isEnabled: Bool = false
    
    // 为偏好选项添加状态变量
    @State private var selectedPreference: PreferenceOption = .off
    
    // 为位置选项添加状态变量
    @State private var selectedPosition: PositionOption = .center
    
    // 偏好选项枚举
    enum PreferenceOption: String, CaseIterable {
        case off = "OFF"
        case parameters = "参数"
        case location = "经纬度"
        case date = "日期"
    }
    
    // 位置选项枚举
    enum PositionOption: String, CaseIterable {
        case center = "中"
        case bottom = "下"
    }
    
    var body: some View {
        Button(action: action) {
            if title == "Logo" {
                // Logo选项特殊处理
                HStack(spacing: screenWidth * 0.06) {  // 修改间距为6%
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // Logo图标滚动列表
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.04) {
                            // 无Logo选项
                            Button(action: {
                                // 处理无Logo选择
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color.white)
                                        .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                    
                                    Image("LH Wu")
                                        .resizable()
                                        .frame(width: screenHeight * 0.02, height: screenHeight * 0.02)
                                }
                            }
                            
                            // 苹果Logo
                            Button(action: {
                                // 处理苹果Logo选择
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(Color.white)
                                        .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                    
                                    Image(systemName: "apple.logo")
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(height: screenHeight * 0.02)
                                        .foregroundColor(.black)  // 添加黑色修饰符
                                }
                            }
                            
                            // 更多Logo选项...可以继续添加
                            ForEach(1...5, id: \.self) { index in
                                Button(action: {
                                    // 处理其他Logo选择
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(Color.white)
                                            .frame(width: screenHeight * 0.03, height: screenHeight * 0.03)
                                        
                                        if index == 1 {
                                            Image("LH Zeiss")
                                                .resizable()
                                                .frame(width: screenHeight * 0.0175, height: screenHeight * 0.0175)
                                        } else if index == 2 {
                                            Image("LH H")
                                                .resizable()
                                                .frame(width: screenHeight * 0.0175, height: screenHeight * 0.0175)
                                        } else if index == 3 {
                                            Image("LH Leica-1")
                                                .resizable()
                                                .frame(width: screenHeight * 0.0175, height: screenHeight * 0.0175)
                                        } else if index == 4 {
                                            Image("LH Kodak")
                                                .resizable()
                                                .frame(width: screenHeight * 0.0175, height: screenHeight * 0.0175)
                                        } else if index == 5 {
                                            Image("LH Polaroid")
                                                .resizable()
                                                .frame(width: screenHeight * 0.0175, height: screenHeight * 0.0175)
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.trailing, screenWidth * 0.04)
                    }
                }
                .padding(.vertical, screenHeight * 0.0125)
            } else if title == "文字" || title == "署名" {
                // 文字和署名选项特殊处理
                HStack(spacing: screenWidth * 0.06) {  // 修改间距为6%
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 文本输入框
                    TextField("请输入\(title)", text: $textInput)
                        .font(.system(size: screenHeight * 0.015, weight: .medium))  // 文本大小为1.5%
                        .foregroundColor(.white)
                        .padding(.horizontal, 8)  // 水平内边距
                        .padding(.vertical, screenHeight * 0.0075)  // 垂直内边距0.75%屏幕高度
                        .background(Color(uiColor: .systemGray5))
                        .cornerRadius(6)  // 稍微调小圆角
                    
                    // 开关按钮
                    Toggle("", isOn: $isEnabled)
                        .labelsHidden()
                        .tint(UIConstants.dialIndicatorColor)  // 使用系统黄色，与设置页面保持一致
                }
                .padding(.vertical, screenHeight * 0.0125)
            } else if title == "偏好" {
                // 偏好选项特殊处理
                HStack(spacing: screenWidth * 0.06) {  // 修改间距为6%
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 偏好切换器
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(PreferenceOption.allCases, id: \.self) { option in
                            Button(action: {
                                selectedPreference = option
                            }) {
                                Text(option.rawValue)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))  // 文本大小为1.5%
                                    .foregroundColor(selectedPreference == option ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)  // 垂直内边距0.5%屏幕高度
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(selectedPreference == option ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, screenHeight * 0.0125)
            } else if title == "位置" {
                // 位置选项特殊处理
                HStack(spacing: screenWidth * 0.06) {  // 修改间距为6%
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 位置切换器
                    HStack(spacing: screenWidth * 0.02) {
                        ForEach(PositionOption.allCases, id: \.self) { option in
                            Button(action: {
                                selectedPosition = option
                            }) {
                                Text(option.rawValue)
                                    .font(.system(size: screenHeight * 0.015, weight: .medium))  // 文本大小为1.5%
                                    .foregroundColor(selectedPosition == option ? .black : .white)
                                    .padding(.horizontal, screenWidth * 0.02)
                                    .padding(.vertical, screenHeight * 0.005)  // 垂直内边距0.5%屏幕高度
                                    .background(
                                        RoundedRectangle(cornerRadius: 4)
                                            .fill(selectedPosition == option ? UIConstants.dialIndicatorColor : Color(uiColor: .systemGray5))
                                    )
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding(.vertical, screenHeight * 0.0125)
            } else {
                // 其他选项保持原样
                HStack {
                    Text(title)
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    Spacer()
                }
                .padding(.vertical, screenHeight * 0.0125)
            }
        }
        .buttonStyle(PlainButtonStyle()) // 防止按钮样式影响文本输入和开关按钮
    }
}

struct SharedTabView_Previews: PreviewProvider {
    static var previews: some View {
        SharedTabView(
            viewModel: SharedTabViewModel(),
            cameraViewModel: CameraViewModel(),
            filterViewModel: FilterViewModel(),
            editViewModel: EditViewModel(),
            galleryViewModel: GalleryViewModel()
        )
    }
}

// 裁切控制视图组件
struct CropControlView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // ViewModel
    @ObservedObject var viewModel: SharedTabViewModel
    
    var body: some View {
        ScrollView {
            VStack(spacing: 0) {
            // 第一行：复原按钮和操作按钮
                VStack(spacing: screenHeight * 0.0275) {  // 主要行之间的间距改为2.75%
            HStack(spacing: screenWidth * 0.05) {  // 5%间距
                // 复原按钮
                Button(action: {
                    // 重置处理
                    viewModel.selectedRatio = "original"
                    viewModel.rotationAngle = 0
                    // 重置刻度尺位置到中心
                    viewModel.resetCropScaleToCenter()
                }) {
                    Text("复原")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // 垂直按钮
                Button(action: {
                    // 垂直翻转处理
                }) {
                    Image(systemName: "arrow.up.and.down.righttriangle.up.righttriangle.down")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
                
                // 镜像按钮
                Button(action: {
                    // 镜像处理
                }) {
                    Image(systemName: "arrow.left.and.right.righttriangle.left.righttriangle.right")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
                
                // 旋转按钮
                Button(action: {
                    // 旋转处理
                }) {
                    Image(systemName: "rotate.left")
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                }
            }
            .padding(.horizontal, screenWidth * 0.04)
                    .padding(.vertical, screenHeight * 0.0125)  // 行内垂直内边距改为1.25%
            
            // 第二行：旋转角度刻度条
            VStack {
                // 刻度条
                GeometryReader { geometry in
                    ZStack(alignment: .center) {
                        // 背景区域
                        Rectangle()
                            .fill(Color.clear)
                            .frame(width: screenWidth * 0.92, height: 40)
                            .contentShape(Rectangle())
                            .zIndex(0)
                        
                        // 刻度线层
                        ZStack {
                            // 主内容
                            ZStack {
                                // 刻度线容器
                                HStack(spacing: 0) {
                                    ZStack(alignment: .leading) {
                                        ForEach(-9...9, id: \.self) { i in
                                            let position = CGFloat(i + 9) / 18.0
                                            let xPosition = position * (screenWidth * 0.92) - 0.5
                                            let offset: CGFloat = 0
                                            let mainTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialMainTickLength
                                            let subTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialSubTickLength
                                            
                                            Path { path in
                                                let startPoint = CGPoint(x: xPosition, y: offset)
                                                let endPoint = CGPoint(x: xPosition, y: offset + (i % 3 == 0 ? mainTickLength : subTickLength))
                                                
                                                path.move(to: startPoint)
                                                path.addLine(to: endPoint)
                                            }
                                            .stroke(i % 3 == 0 ? Color.white : Color.white.opacity(0.4), 
                                                   lineWidth: UIConstants.dialTickWidth)
                                        }
                                    }
                                    .frame(width: screenWidth * 0.92)
                                }
                            }
                            .offset(x: viewModel.cropScaleDragOffset)
                            
                            // 遮罩视图 - 使用独立的Path
                            MaskUtils.createScaleRulerMaskPath(centerX: geometry.size.width / 2, config: .scaleRuler)
                                .fill(Color.black)
                                .blendMode(.destinationOut)
                        }
                        .compositingGroup()
                        .zIndex(1)
                        
                        // 指示器和角度值一起放在VStack中
                        VStack(spacing: screenHeight * 0.015) { // 设置间距为屏幕高度的1.5%
                            // 指示器三角形
                            Path { path in
                                let width: CGFloat = UIScreen.main.bounds.width * 0.015
                                let height: CGFloat = UIScreen.main.bounds.height * 0.015
                                let centerX = geometry.size.width / 2
                                let topPoint = CGPoint(x: centerX, y: height)
                                let leftPoint = CGPoint(x: centerX - width/2, y: topPoint.y - height)
                                let rightPoint = CGPoint(x: centerX + width/2, y: topPoint.y - height)
                                
                                path.move(to: topPoint)
                                path.addLine(to: leftPoint)
                                path.addLine(to: rightPoint)
                                path.closeSubpath()
                            }
                            .fill(UIConstants.dialIndicatorColor)
                            
                            // 添加角度数值显示，只显示整数
                            Text("\(Int(viewModel.rotationAngle))°")
                                .font(.system(size: screenHeight * 0.016, weight: .medium))
                                .foregroundColor(UIConstants.dialIndicatorColor)
                        }
                        .zIndex(2)
                    }
                    .gesture(
                        DragGesture(minimumDistance: 0)
                            .onChanged { gesture in
                                if !viewModel.isTouching {
                                    viewModel.startLocation = gesture.location
                                    viewModel.startDragOffset = viewModel.cropScaleDragOffset
                                    viewModel.isTouching = true
                                    viewModel.onScaleTouchBegan?()
                                }
                                
                                // 计算水平方向的移动距离
                                let translation = gesture.location.x - viewModel.startLocation.x
                                
                                // 计算新的偏移量 (直接使用translation而不是取反)
                                let newOffset = viewModel.startDragOffset + translation
                                
                                // 限制拖动范围
                                viewModel.updateCropScaleOffset(newOffset)
                            }
                            .onEnded { _ in
                                viewModel.isTouching = false
                                viewModel.lastDragOffset = viewModel.cropScaleDragOffset
                                viewModel.onScaleTouchEnded?()
                                
                                // 保存偏移量
                                UserDefaults.standard.set(viewModel.cropScaleDragOffset, forKey: "LastCropScaleOffset")
                            }
                    )
                }
                        .frame(height: screenHeight * 0.02)
            }
            .padding(.horizontal, screenWidth * 0.04)
                    .padding(.vertical, screenHeight * 0.0125)  // 行内垂直内边距改为1.25%
            
            // 第三行：比例选择
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.06) {
                    // 原始选项
                    VStack(spacing: 4) {
                        // 图标容器，保持与其他比例相同的高度
                        ZStack {
                            Image(systemName: "nosign")
                                .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                    }
                        .frame(height: screenHeight * 0.04)
                        
                        Text("原始")
                                .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.white)
                    }
                    .onTapGesture {
                        viewModel.selectedRatio = "original"
                    }
                    
                    // 其他比例选项
                    ForEach([
                        ("4:3", "4:3"),         // 标准比例
                        ("3:4", "3:4"),         // 标准比例（竖版）
                        ("3:2", "3:2"),         // 专业相机比例
                        ("2:3", "2:3"),         // 专业相机比例（竖版）
                        ("16:9", "16:9"),       // 宽屏比例
                        ("9:16", "9:16"),       // 宽屏比例（竖版）
                        ("1:1", "1:1"),         // 方形比例
                        ("6:7", "6:7"),         // 竖版比例
                        ("7:6", "7:6"),         // 横版比例
                        ("2:1", "2:1"),         // 宽幅比例
                        ("1:2", "1:2"),         // 宽幅比例（竖版）
                        ("1.85:1", "1.85:1"),   // 电影比例
                        ("2.39:1", "2.39:1"),   // 影院比例
                        ("1.66:1", "1.66:1"),   // 欧洲宽银幕
                        ("XPAN", "XPAN")        // 哈苏XPAN
                    ], id: \.1) { ratio in
                        VStack(spacing: 4) {
                            // 比例形状预览
                            RatioPreviewShape(ratio: ratio.1)
                                .stroke(viewModel.selectedRatio == ratio.1 ? UIConstants.dialIndicatorColor : Color.white, lineWidth: 1)
                                .frame(width: screenHeight * 0.04, height: screenHeight * 0.04)
                            
                            // 比例文字
                            Text(ratio.0)
                                .font(.system(size: screenHeight * 0.016))
                                .foregroundColor(viewModel.selectedRatio == ratio.1 ? UIConstants.dialIndicatorColor : Color.white)
                        }
                        .onTapGesture {
                            viewModel.selectedRatio = ratio.1
                    }
                }
            }
                .padding(.horizontal, screenWidth * 0.04)
                        .padding(.vertical, screenHeight * 0.0125)  // 行内垂直内边距改为1.25%
                    }
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}


// 比例预览形状
struct RatioPreviewShape: Shape {
    let ratio: String
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 根据比例计算矩形大小
        let size: CGSize
        switch ratio {
        case "1:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.8)
        case "4:3":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.6)
        case "3:4":
            size = CGSize(width: rect.width * 0.6, height: rect.width * 0.8)
        case "3:2":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.533)
        case "2:3":
            size = CGSize(width: rect.width * 0.533, height: rect.width * 0.8)
        case "16:9":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.45)
        case "9:16":
            size = CGSize(width: rect.width * 0.45, height: rect.width * 0.8)
        case "6:7":
            size = CGSize(width: rect.width * 0.686, height: rect.width * 0.8)
        case "7:6":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.686)
        case "2:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.4)
        case "1:2":
            size = CGSize(width: rect.width * 0.4, height: rect.width * 0.8)
        case "1.85:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.432)
        case "2.39:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.335)
        case "1.66:1":
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.482)
        case "XPAN":
            // XPAN 的比例接近 65:24 (约2.7:1)
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.296)
        default:
            size = CGSize(width: rect.width * 0.8, height: rect.width * 0.8)
        }
        
        let origin = CGPoint(
            x: (rect.width - size.width) / 2,
            y: (rect.height - size.height) / 2
        )
        path.addRect(CGRect(origin: origin, size: size))
        
        return path
    }
}

// 滤镜控制视图组件
struct FilterControlView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 选中的预设
    @State private var selectedPolaroidPreset = 0
    @State private var selectedFilmPreset = 0
    @State private var selectedVintagePreset = 0
    @State private var selectedFashionPreset = 0
    @State private var selectedINSPreset = 0
    
    var body: some View {
        ScrollView {
        VStack(spacing: 0) {
                // 宝丽来
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("宝丽来")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
            ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedPolaroidPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedPolaroidPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 胶卷
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("胶卷")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFilmPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedFilmPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 复古
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("复古")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
            ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedVintagePreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedVintagePreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                .foregroundColor(.white)
                        }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // 时尚
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("时尚")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedFashionPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedFashionPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
                
                // INS
                VStack(alignment: .leading, spacing: screenHeight * 0.01) {
                    Text("INS")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 预设选择（水平滚动）
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: screenWidth * 0.02) {
                            ForEach(0..<5) { index in
                                Button(action: {
                                    selectedINSPreset = index
                                }) {
                                    VStack(spacing: screenHeight * 0.005) {
                                        Rectangle()
                                            .fill(Color(uiColor: .systemGray5))
                                            .frame(width: screenWidth * 0.15, height: screenHeight * 0.08)
                                            .overlay(alignment: .bottomTrailing) {
                                                if selectedINSPreset == index {
                                                    Image(systemName: "checkmark.circle.fill")
                                                        .foregroundColor(UIConstants.dialIndicatorColor)
                                                        .font(.system(size: screenHeight * 0.015))
                                                        .offset(x: screenHeight * 0.005, y: screenHeight * 0.005)
                                                }
                                            }
                                        
                                        Text("预设\(index + 1)")
                                            .font(.system(size: screenHeight * 0.012))
                .foregroundColor(.white)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, screenWidth * 0.02)
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}

// 调节控制视图组件
struct AdjustControlView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 调整参数
    @State private var exposure: Double = 0.0
    @State private var brightness: Double = 0.0
    @State private var contrast: Double = 0.0
    @State private var highlights: Double = 0.0
    @State private var shadows: Double = 0.0
    
    // 色彩参数
    @State private var temperature: Double = 0.0
    @State private var tint: Double = 0.0
    @State private var saturation: Double = 0.0
    @State private var fade: Double = 0.0
    
    // 细节参数
    @State private var sharpness: Double = 0.0
    @State private var vignette: Double = 0.0
    @State private var chromaticAberration: Double = 0.0
    @State private var grainSize: Double = 0.0
    @State private var grainSaturation: Double = 0.0
    
    // 当前选中的参数类型
    @State private var selectedParameter = "exposure"
    
    // 添加HSL相关状态
    @State private var selectedHSLColorIndex: Int = 0
    @State private var hue: Double = 0.0
    @State private var hslSaturation: Double = 0.0
    @State private var luminance: Double = 0.0
    
    // 色调分离参数
    @State private var selectedToneOption: String = "全局"
    @State private var toneBrightness: Double = 0.0
    
    // 添加曲线相关状态
    @State private var curveColorIndex: Int = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // 调整参数选择
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: screenWidth * 0.06) {
                    AdjustParameterButton(title: "曝光", isSelected: selectedParameter == "exposure") {
                        selectedParameter = "exposure"
                    }
                    
                    AdjustParameterButton(title: "色彩", isSelected: selectedParameter == "color") {
                        selectedParameter = "color"
                    }
                    
                    AdjustParameterButton(title: "色调分离", isSelected: selectedParameter == "toneSplit") {
                        selectedParameter = "toneSplit"
                    }
                    
                    AdjustParameterButton(title: "HSL", isSelected: selectedParameter == "hsl") {
                        selectedParameter = "hsl"
                    }
                    
                    AdjustParameterButton(title: "曲线", isSelected: selectedParameter == "curve") {
                        selectedParameter = "curve"
                    }
                    
                    AdjustParameterButton(title: "细节", isSelected: selectedParameter == "detail") {
                        selectedParameter = "detail"
                    }
                }
                .padding(.horizontal, screenWidth * 0.04)
                .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
            }
            .background(Color(uiColor: .systemGray6))
            
            // 根据选中的参数类型显示不同的选项，每个操作区使用独立的滚动视图
            ZStack {
                // 曝光操作区 - 独立滚动视图
                if selectedParameter == "exposure" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // 曝光参数选项
                            AdjustSliderOption(title: "曝光补偿", value: $exposure)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "亮度", value: $brightness)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "对比度", value: $contrast)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "高光", value: $highlights)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "阴影", value: $shadows)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("exposure") // 强制在切换时重新创建视图
                }
                
                // 色彩操作区 - 独立滚动视图
                else if selectedParameter == "color" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // 色彩参数选项
                            AdjustSliderOption(title: "色温", value: $temperature)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "色调", value: $tint)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "饱和度", value: $saturation)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "褪色", value: $fade)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("color") // 强制在切换时重新创建视图
                }
                
                // 色调分离操作区 - 独立滚动视图
                else if selectedParameter == "toneSplit" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // 色调分离参数选项
                            // 顶部四个选项按钮
                            HStack(spacing: screenWidth * 0.05) {
                    Spacer()
                    
                                // 阴影按钮
                                Button(action: {
                                    selectedToneOption = "阴影"
                                }) {
                                    Image(systemName: "circle.bottomhalf.filled")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(selectedToneOption == "阴影" ? UIConstants.dialIndicatorColor : .white)
                                }
                                
                                // 中间调按钮
                                Button(action: {
                                    selectedToneOption = "中间调"
                                }) {
                                    Image(systemName: "circle.lefthalf.filled")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(selectedToneOption == "中间调" ? UIConstants.dialIndicatorColor : .white)
                                }
                                
                                // 高光按钮
                                Button(action: {
                                    selectedToneOption = "高光"
                                }) {
                                    Image(systemName: "circle.tophalf.filled")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(selectedToneOption == "高光" ? UIConstants.dialIndicatorColor : .white)
                                }
                                
                                // 全局按钮
                                Button(action: {
                                    selectedToneOption = "全局"
                                }) {
                                    Image(systemName: "circle.fill")
                                        .font(.system(size: screenHeight * 0.025))
                                        .foregroundColor(selectedToneOption == "全局" ? UIConstants.dialIndicatorColor : .white)
                                }
                                
                                Spacer()
                            }
                            .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            
                            // 中间色环
                            ZStack {
                                // 色彩环
                                Circle()
                                    .fill(
                                        AngularGradient(
                                            gradient: Gradient(colors: [
                                                .red, .orange, .yellow, .green,
                                                .blue, .purple, .pink, .red
                                            ]),
                                            center: .center
                                        )
                                    )
                                    .frame(width: screenHeight * 0.2, height: screenHeight * 0.2)
                                
                                // 中心白色圆
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.02, height: screenHeight * 0.02)
                            }
                            .padding(.vertical, screenHeight * 0.02) // 增加上下内边距
                            .background(Color(uiColor: .systemGray6))
                            
                            // 底部明度滑块
                VStack(spacing: screenHeight * 0.01) {
                    HStack {
                                    Text("明度")
                            .font(.system(size: screenHeight * 0.015))
                        .foregroundColor(.white)
                        
                        Spacer()
                        
                                    Text("\(Int(toneBrightness * 100))")
                            .font(.system(size: screenHeight * 0.015))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
                                // 使用通用的滑块组件
                    CustomSlider(
                                    value: $toneBrightness,
                        range: -1.0...1.0,
                        step: 0.01
                    )
                            }
                            .padding(.vertical, screenHeight * 0.005)
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("toneSplit") // 强制在切换时重新创建视图
                }
                
                // HSL操作区 - 独立滚动视图
                else if selectedParameter == "hsl" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // HSL颜色选择器
                            HSLColorSelector(selectedColorIndex: $selectedHSLColorIndex)
                                .padding(.vertical, screenHeight * 0.005)  // 添加0.5%的垂直内边距
            
            Spacer()
                                .frame(height: screenHeight * 0.015)  // 添加1.5%屏幕高度的行间距
                            
                            // HSL调节选项
                            AdjustSliderOption(title: "色相", value: $hue)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "饱和度", value: $hslSaturation)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "明度", value: $luminance)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("hsl") // 强制在切换时重新创建视图
                }
                
                // 曲线操作区 - 独立滚动视图
                else if selectedParameter == "curve" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // 曲线颜色选择器
                            ColorSelector(
                                colors: [.white, .red, .green, .blue],
                                selectedIndex: $curveColorIndex
                            )
                            .padding(.vertical, screenHeight * 0.005)  // 添加0.5%的垂直内边距
                            
                            Spacer()
                                .frame(height: screenHeight * 0.015)  // 添加1.5%屏幕高度的行间距
                            
                            // 曲线调节器
                            ZStack {
                                // 网格背景
                                VStack(spacing: 0) {
                                    ForEach(0..<4) { _ in
                                        HStack(spacing: 0) {
                                            ForEach(0..<4) { _ in
                                                Rectangle()
                                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                                    .background(Color.clear)
                                                    .frame(width: screenHeight * 0.1, height: screenHeight * 0.05)
                                            }
                                        }
                                    }
                                }
                                
                                // 连接五个控制点的线条
                                Path { path in
                                    // 从左下角开始
                                    path.move(to: CGPoint(x: 0, y: screenHeight * 0.2))
                                    // 连接到左下偏上点
                                    path.addLine(to: CGPoint(x: screenHeight * 0.1, y: screenHeight * 0.15))
                                    // 连接到中间点
                                    path.addLine(to: CGPoint(x: screenHeight * 0.2, y: screenHeight * 0.1))
                                    // 连接到右上偏下点
                                    path.addLine(to: CGPoint(x: screenHeight * 0.3, y: screenHeight * 0.05))
                                    // 连接到右上角点
                                    path.addLine(to: CGPoint(x: screenHeight * 0.4, y: 0))
                                }
                                .stroke(Color.white, lineWidth: 1.5)
                                
                                // 对角线上的控制点
                                // 左下角控制点
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .position(x: 0, y: screenHeight * 0.2)
                                
                                // 左下偏上控制点
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .position(x: screenHeight * 0.1, y: screenHeight * 0.15)
                                
                                // 中间控制点
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .position(x: screenHeight * 0.2, y: screenHeight * 0.1)
                                
                                // 右上偏下控制点
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .position(x: screenHeight * 0.3, y: screenHeight * 0.05)
                                
                                // 右上角控制点
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: screenHeight * 0.015, height: screenHeight * 0.015)
                                    .position(x: screenHeight * 0.4, y: 0)
                            }
                            .frame(width: screenHeight * 0.4, height: screenHeight * 0.2)
                            .padding(.vertical, screenHeight * 0.005)  // 添加0.5%的垂直内边距
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("curve") // 强制在切换时重新创建视图
                }
                
                // 细节操作区 - 独立滚动视图
                else if selectedParameter == "detail" {
                    ScrollView {
                        VStack(spacing: 0) {
                            // 细节参数选项
                            AdjustSliderOption(title: "锐化", value: $sharpness)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "暗角", value: $vignette)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "色差", value: $chromaticAberration)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "颗粒大小", value: $grainSize)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                            AdjustSliderOption(title: "颗粒饱和度", value: $grainSaturation)
                                .padding(.vertical, screenHeight * 0.005)  // 改为0.5%
                        }
                        .padding(.vertical, screenHeight * 0.0125)  // 添加1.25%的垂直内边距
                        .frame(maxWidth: .infinity)
                        .padding(.bottom, screenHeight * 0.02)  // 只保留底部内边距
                    }
                    .id("detail") // 强制在切换时重新创建视图
                }
            }
        }
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: screenHeight * 0.06)
        }
        .frame(maxWidth: .infinity)
        .padding(.bottom, screenHeight * 0.02)  // 添加2%的底部内边距
    }
}

// 添加通用的滑块选项组件
struct AdjustSliderOption: View {
    let title: String
    @Binding var value: Double
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
                VStack(spacing: screenHeight * 0.01) {
                    HStack {
                Text(title)
                            .font(.system(size: screenHeight * 0.015))
                            .foregroundColor(.white)
                        
                        Spacer()
                        
                Text("\(Int(value * 100))")
                            .font(.system(size: screenHeight * 0.015))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, screenWidth * 0.04)
                    
            // 使用通用的滑块组件
                    CustomSlider(
                value: $value,
                        range: -1.0...1.0,
                        step: 0.01
                    )
                }
            }
}

// 调节参数按钮组件
struct AdjustParameterButton: View {
    var title: String
    var isSelected: Bool
    var action: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .bold))
                .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
        }
    }
} 

// 通用颜色选择器组件
struct ColorSelector: View {
    let colors: [Color]
    @Binding var selectedIndex: Int
    var hasStroke: Bool = true
    var circleSize: CGFloat = 0.03 // 默认3%屏幕高度
    
    private let screenHeight = UIScreen.main.bounds.height
    private let screenWidth = UIScreen.main.bounds.width
    
    var body: some View {
        HStack {
            Spacer()
            // 水平排列，居中布局
            HStack(spacing: screenWidth * 0.05) {
                ForEach(0..<colors.count, id: \.self) { index in
                    Circle()
                        .fill(colors[index])
                        .frame(width: screenHeight * circleSize, height: screenHeight * circleSize)
                        .overlay(
                            Circle()
                                .stroke(selectedIndex == index ? .white : .clear, lineWidth: hasStroke ? 2 : 0)
                                .padding(hasStroke ? -2 : 0) // 边框外扩
                        )
                        .onTapGesture {
                            selectedIndex = index
                        }
                }
            }
            Spacer()
        }
        .background(Color(uiColor: .systemGray6))
    }
}

// HSL颜色选择器组件兼容性封装
struct HSLColorSelector: View {
    @Binding var selectedColorIndex: Int
    
    var body: some View {
        ColorSelector(
            colors: [.red, .orange, .yellow, .green, .cyan, .blue, .purple, .pink],
            selectedIndex: $selectedColorIndex
        )
    }
}

// 色调选项按钮组件
struct ToneOptionButton: View {
    var title: String
    var isSelected: Bool
    var action: () -> Void
    
    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: screenHeight * 0.016))
                .foregroundColor(isSelected ? .black : .white)
                .padding(.horizontal, screenHeight * 0.015)
                .padding(.vertical, screenHeight * 0.008)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? UIConstants.dialIndicatorColor : Color.gray.opacity(0.3))
                )
        }
    }
}
