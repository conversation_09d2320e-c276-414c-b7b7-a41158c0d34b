import SwiftUI
import Photos
import PhotosUI

// 滤镜预览模型（简化版）
struct FilterPreviewModel {
    let name: String
    let previewColor: Color
}

struct SharedTabView: View {
    @ObservedObject var viewModel: SharedTabViewModel
    @ObservedObject var cameraViewModel: CameraViewModel
    @ObservedObject var filterViewModel: GalleryFilterViewModel
    @ObservedObject var editViewModel: EditViewModel
    @ObservedObject var galleryViewModel: GalleryViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 底部标签栏的高度（包括安全区域）
    private let tabBarHeight: CGFloat = UIScreen.main.bounds.height * 0.08 + (UIApplication.shared.windows.first?.safeAreaInsets.bottom ?? 0)
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景
                Color(uiColor: .systemGray6).ignoresSafeArea()
                
                // 内容视图
                VStack(spacing: 0) {
                    // 内容区域 - 根据选中的标签显示相应的页面
                    ZStack {
                        // 根据选中的标签显示相应的页面
                        // 现在使用直接导入的视图组件，而不是Fixed版本
                        switch viewModel.selectedTab.tag {
                        case 0: // 相册
                            GalleryView(viewModel: galleryViewModel, sharedTabViewModel: viewModel)
                        case 1: // 滤镜
                            GalleryFilterView(viewModel: filterViewModel)
                        case 3: // 编辑
                            EditView(cameraViewModel: cameraViewModel, viewModel: viewModel, editViewModel: editViewModel, filterViewModel: filterViewModel)
                        case 4: // 设置
                            SettingsView()
                        default:
                            GalleryView(viewModel: galleryViewModel, sharedTabViewModel: viewModel)
                        }
                    }
                    .frame(height: geometry.size.height)
                    
                    Spacer(minLength: 0)
                }
                
                // 底部标签栏 - 固定在底部
                VStack {
                    Spacer()
                    BottomTabBar(viewModel: viewModel)
                }
                .ignoresSafeArea(.keyboard) // 确保键盘不会推动底部标签栏
                .edgesIgnoringSafeArea(.bottom) // 忽略底部安全区域，确保导航栏延伸到屏幕底部
            }
        }
    }
}

// 滤镜预览项组件
struct FilterPreviewItem: View {
    let filter: FilterPreviewModel
    let isSelected: Bool
    let onSelect: () -> Void
    
    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        VStack {
            // 滤镜预览效果
            ZStack {
                    Rectangle()
                    .fill(filter.previewColor)
                    .frame(width: screenHeight * 0.08, height: screenHeight * 0.08)
                    .cornerRadius(5)
                                        .overlay(
                        RoundedRectangle(cornerRadius: 5)
                            .stroke(isSelected ? UIConstants.dialIndicatorColor : Color.clear, lineWidth: 2)
                    )
                
                // 原图滤镜显示无效果水印
                if filter.name == "原图" {
                    Text("无效果")
                                            .font(.system(size: screenHeight * 0.012))
                                            .foregroundColor(.white)
                                    }
                                }
            .onTapGesture {
                onSelect()
            }
            
            // 滤镜名称
            Text(filter.name)
                                                        .font(.system(size: screenHeight * 0.015))
                .foregroundColor(isSelected ? UIConstants.dialIndicatorColor : .white)
        }
    }
}

struct SharedTabView_Previews: PreviewProvider {
    static var previews: some View {
        SharedTabView(
            viewModel: SharedTabViewModel(),
            cameraViewModel: CameraViewModel(),
            filterViewModel: GalleryFilterViewModel(),
            editViewModel: EditViewModel(),
            galleryViewModel: GalleryDependencyContainer.galleryViewModel()
        )
    }
}
