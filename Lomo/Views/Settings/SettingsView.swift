import SwiftUI

struct SettingsView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    // 使用ViewModel - 依赖注入
    @ObservedObject var viewModel: SettingsViewModel
    
    // 添加初始化方法
    init(viewModel: SettingsViewModel) {
        self.viewModel = viewModel
    }
    
    // 订阅管理器（通过依赖注入容器获取）
    @StateObject private var subscriptionManager = SubscriptionDependencyContainer.shared.subscriptionService as! SubscriptionService
    
    var body: some View {
        NavigationStack {
            ZStack {
                Color(uiColor: .systemGray6).edgesIgnoringSafeArea(.all)
                
        VStack(spacing: 0) {
                    // 使用统一的导航栏组件
                    NavigationTopBarWithAction(
                        selectedTab: .constant(.settings),  // 设置页面只有一个标签
                        tabs: [("设置", TabBarItem.settings)],
                        actionTitle: "免费试用",
                        onTabSelected: { _ in },
                        onAction: {
                            // 显示LOMO Pro页面(使用全局管理器)
                            subscriptionManager.showProSubscription()
                        },
                        showLomoLogo: true,  // 显示LOMO符号
                        isProUser: subscriptionManager.isProUser  // 使用订阅管理器的状态
                    )
                    
                    List {
                        Section(header: Text("相机")
                            .foregroundColor(.gray)
                            .font(.system(size: screenHeight * 0.02, weight: .semibold))
                            .textCase(nil)
                        ) {
                            SettingsRow(icon: "photo.badge.arrow.down", title: "保存原图", value: viewModel.isSaveOriginalEnabled ? "开启" : "关闭", toggleAction: { viewModel.toggleSetting(.saveOriginal) }, showToggle: true, showRightArrow: false, isFirstInSection: true, isLastInSection: false)
                                .listRowBackground(Color.clear)
                            
                            NavigationLink {
                                CopyrightEditorView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "c.circle")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("版权署名")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.copyrightSignature)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            SettingsRow(icon: "location", title: "记录位置", value: viewModel.isLocationRecordingEnabled ? "开启" : "关闭", toggleAction: { viewModel.toggleSetting(.locationRecording) }, showToggle: true, showRightArrow: false, isFirstInSection: false, isLastInSection: false)
                                .listRowBackground(Color.clear)
                            
                            // 自定义比例选项（带Pro标签）
                            HStack {
                                Image(systemName: "rectangle.dashed")
                                    .font(.system(size: screenHeight * 0.0175))
                                    .foregroundColor(.white)
                                    .frame(width: screenHeight * 0.03)
                                
                                HStack(spacing: screenWidth * 0.01) {
                                    Text("自定义比例")
                                        .font(.system(size: screenHeight * 0.0175))
                                    ProLabel(screenHeight: screenHeight, isProUser: subscriptionManager.isProUser)
                                }
                                
                                Spacer()
                                
                                Toggle("", isOn: Binding(
                                    get: { 
                                        // 非Pro用户始终返回false
                                        return subscriptionManager.isProUser ? viewModel.isCustomRatioEnabled : false
                                    },
                                    set: { newValue in 
                                        // 简化逻辑：Pro用户正常设置，非Pro用户显示订阅页面
                                        if subscriptionManager.isProUser {
                                            viewModel.isCustomRatioEnabled = newValue
                                        } else {
                                            subscriptionManager.showProView = true
                                        }
                                    }
                                ))
                                .labelsHidden()
                                .tint(UIConstants.dialIndicatorColor)
                                .scaleEffect(0.75)
                            }
                            .frame(height: screenHeight * 0.04)
                            .frame(minHeight: screenHeight * 0.04)
                            .frame(maxHeight: screenHeight * 0.04)
                            .listRowBackground(Color.clear)
                            
                            if viewModel.isCustomRatioEnabled && subscriptionManager.isProUser {
                                NavigationLink {
                                    PhotoRatioSettingsView(viewModel: viewModel)
                                } label: {
                                    HStack {
                                        Rectangle()
                                            .fill(Color.clear)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Image(systemName: "circle.rectangle.dashed")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Text("照片比例")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                        
                                        Spacer()
                                        
                                        Text(viewModel.photoRatio)
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.gray)
                                    }
                                    .frame(height: screenHeight * 0.04)
                                    .frame(minHeight: screenHeight * 0.04)
                                    .frame(maxHeight: screenHeight * 0.04)
                                }
                                .listRowBackground(Color.clear)
                                
                                NavigationLink {
                                    VideoRatioSettingsView(viewModel: viewModel)
                                } label: {
                                    HStack {
                                        Rectangle()
                                            .fill(Color.clear)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Image(systemName: "rectangle.dashed.badge.record")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Text("视频比例")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                        
                                        Spacer()
                                        
                                        Text(viewModel.videoRatio)
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.gray)
                                    }
                                    .frame(height: screenHeight * 0.04)
                                    .frame(minHeight: screenHeight * 0.04)
                                    .frame(maxHeight: screenHeight * 0.04)
                                }
                                .listRowBackground(Color.clear)
                            }
                            
                            NavigationLink {
                                FocusPeakingColorView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "person.and.background.dotted")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("峰值对焦颜色")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.focusPeakingColor)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            SettingsRow(icon: "ipod.shuffle.gen3", title: "音量键快门", value: viewModel.isVolumeButtonShutterEnabled ? "开启" : "关闭", toggleAction: { viewModel.toggleSetting(.volumeButtonShutter) }, showToggle: true, showRightArrow: false, isFirstInSection: false, isLastInSection: false)
                                .listRowBackground(Color.clear)
                            
                            NavigationLink {
                                ShutterSoundView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "speaker.wave.2")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("快门声")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.shutterSound)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            SettingsRow(icon: "plus.viewfinder", title: "对焦声", value: viewModel.isFocusSoundEnabled ? "开启" : "关闭", toggleAction: { viewModel.toggleSetting(.focusSound) }, showToggle: true, showRightArrow: false, isFirstInSection: false, isLastInSection: false)
                                .listRowBackground(Color.clear)
                            SettingsRow(icon: "photo.fill", title: "图片分辨率", value: viewModel.isResolutionSettingsEnabled ? "开启" : "关闭", toggleAction: { viewModel.toggleSetting(.resolutionSettings) }, showToggle: true, showRightArrow: false, isFirstInSection: false, isLastInSection: false)
                                .listRowBackground(Color.clear)
                            if viewModel.isResolutionSettingsEnabled {
                                NavigationLink {
                                    ProRAWResolutionView(viewModel: viewModel)
                                } label: {
                                    HStack {
                                        Rectangle()
                                            .fill(Color.clear)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Image(systemName: "text.magnifyingglass")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Text("ProRAW分辨率")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                        
                                        Spacer()
                                        
                                        Text(viewModel.proRAWResolution)
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.gray)
                                    }
                                    .frame(height: screenHeight * 0.04)
                                    .frame(minHeight: screenHeight * 0.04)
                                    .frame(maxHeight: screenHeight * 0.04)
                                }
                                .listRowBackground(Color.clear)
                                
                                NavigationLink {
                                    HEICResolutionView(viewModel: viewModel)
                                } label: {
                                    HStack {
                                        Rectangle()
                                            .fill(Color.clear)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Image(systemName: "magnifyingglass")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                            .frame(width: screenHeight * 0.03)
                                        
                                        Text("HEIC分辨率")
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.white)
                                        
                                        Spacer()
                                        
                                        Text(viewModel.heicResolution)
                                            .font(.system(size: screenHeight * 0.0175))
                                            .foregroundColor(.gray)
                                    }
                                    .frame(height: screenHeight * 0.04)
                                    .frame(minHeight: screenHeight * 0.04)
                                    .frame(maxHeight: screenHeight * 0.04)
                                }
                                .listRowBackground(Color.clear)
                            }
                            NavigationLink {
                                VideoBitrateView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "video.and.waveform")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("视频码率")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.videoBitrate)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            NavigationLink {
                                AudioSourceView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "mic.fill")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("音频来源")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.audioSource)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                        }
                        
                        Section(header: Text("偏好")
                            .foregroundColor(.gray)
                            .font(.system(size: screenHeight * 0.02, weight: .semibold))
                            .textCase(nil)
                        ) {
                            NavigationLink {
                                LanguageSettingsView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "globe")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("语言")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.language)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            NavigationLink {
                                DeviceOrientationView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "rectangle.portrait.rotate")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("设备朝向")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(viewModel.deviceOrientation)
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                        }
                        
                        Section(header: Text("关于")
                            .foregroundColor(.gray)
                            .font(.system(size: screenHeight * 0.02, weight: .semibold))
                            .textCase(nil)
                        ) {
                            NavigationLink {
                                FeedbackView(viewModel: viewModel)
                            } label: {
                                HStack {
                                    Image(systemName: "envelope")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("意见反馈")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            NavigationLink {
                                TermsOfServiceView()
                            } label: {
                                HStack {
                                    Image(systemName: "doc.text")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("服务条款")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            NavigationLink {
                                PrivacyPolicyView()
                            } label: {
                                HStack {
                                    Image(systemName: "hand.raised")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("隐私政策")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            Button(action: {
                                if let url = URL(string: "itms-apps://itunes.apple.com/app/idYOUR_APP_ID") {
                                    UIApplication.shared.open(url)
                                }
                            }) {
                                HStack {
                                    Image(systemName: "heart")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text("给 Lomo 好评")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                            
                            HStack {
                                Image(systemName: "info.circle")
                                    .font(.system(size: screenHeight * 0.0175))
                                    .foregroundColor(.white)
                                    .frame(width: screenHeight * 0.03)
                                
                                Text("版本")
                                    .font(.system(size: screenHeight * 0.0175))
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Text(viewModel.appVersion)
                                    .font(.system(size: screenHeight * 0.0175))
                                    .foregroundColor(.gray)
                            }
                            .frame(height: screenHeight * 0.04)
                            .frame(minHeight: screenHeight * 0.04)
                            .frame(maxHeight: screenHeight * 0.04)
                            .listRowBackground(Color.clear)
                            
                            // 临时测试按钮 - Pro用户切换
                            Button(action: {
                                // 切换Pro用户状态
                                subscriptionManager.isProUser.toggle()
                                
                                // 如果用户变成Pro用户，关闭Pro页面
                                if subscriptionManager.isProUser && viewModel.showProView {
                                    viewModel.showProView = false
                                }
                                
                                // 同步CameraViewModel的Pro用户状态
                                // 通过通知中心发送通知，让所有CameraViewModel实例更新状态
                                NotificationCenter.default.post(
                                    name: Notification.Name("ProUserStatusChanged"),
                                    object: nil,
                                    userInfo: ["isProUser": subscriptionManager.isProUser]
                                )
                            }) {
                                HStack {
                                    Image(systemName: "person.fill.badge.plus")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                        .frame(width: screenHeight * 0.03)
                                    
                                    Text(subscriptionManager.isProUser ? "模拟基础用户" : "模拟Pro用户")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(.white)
                                    
                                    Spacer()
                                    
                                    Text(subscriptionManager.isProUser ? "已激活" : "未激活")
                                        .font(.system(size: screenHeight * 0.0175))
                                        .foregroundColor(subscriptionManager.isProUser ? UIConstants.dialIndicatorColor : .gray)
                                }
                                .frame(height: screenHeight * 0.04)
                                .frame(minHeight: screenHeight * 0.04)
                                .frame(maxHeight: screenHeight * 0.04)
                            }
                            .listRowBackground(Color.clear)
                        }
                    }
                    .listStyle(PlainListStyle())
                    .scrollContentBackground(.hidden)
                    .background(Color.clear)
                    .safeAreaInset(edge: .bottom) {
                        Color.clear.frame(height: screenHeight * 0.06)  // 6% 屏幕高度的底部安全区域
                    }
                    .padding(.bottom, screenHeight * 0.02)  // 2% 屏幕高度的底部内边距
                }
            }
            .navigationBarHidden(true)
        }
    }
}

struct SettingsSectionView<Content: View>: View {
    let title: String
    let content: Content
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .semibold))
                .foregroundColor(.gray)
                .padding(.leading, screenWidth * 0.02)
            
            VStack(spacing: 0) {
                content
            }
            .background(Color.clear)
            .cornerRadius(UIConstants.commonCornerRadius)
        }
    }
}

struct SettingsRow: View {
    let icon: String
    let title: String
    let value: String
    let toggleAction: (() -> Void)?
    let showToggle: Bool
    let showRightArrow: Bool
    let isFirstInSection: Bool
    let isLastInSection: Bool
    
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
            HStack {
                    Image(systemName: icon)
                        .font(.system(size: screenHeight * 0.0175))
                        .foregroundColor(.white)
                        .frame(width: screenHeight * 0.03)
                
                Text(title)
                    .font(.system(size: screenHeight * 0.0175))
                    .foregroundColor(.white)
                
                Spacer()
                
                if showToggle {
                    Toggle("", isOn: Binding(
                        get: { value == "开启" },
                    set: { _ in toggleAction?() }
                    ))
                    .labelsHidden()
                    .tint(UIConstants.dialIndicatorColor)
                .scaleEffect(0.75)
                } else {
                    Text(value)
                        .font(.system(size: screenHeight * 0.0175))
                        .foregroundColor(.gray)
                }
            }
        .frame(height: screenHeight * 0.04)
        .frame(minHeight: screenHeight * 0.04)
        .frame(maxHeight: screenHeight * 0.04)
    }
}

// 添加带缩进的设置行组件
struct SettingsSubRow: View {
    let icon: String
    let title: String
    let value: String
    var showRightArrow: Bool = false
    var isFirstInSection: Bool = false
    var isLastInSection: Bool = false
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        HStack {
            // 缩进空间
            Rectangle()
                .fill(Color.clear)
                .frame(width: screenHeight * 0.03)
            
            // 图标
            Image(systemName: icon)
                .font(.system(size: screenHeight * 0.0175))
                .foregroundColor(.white)
                .frame(width: screenHeight * 0.03)
            
            Text(title)
                .font(.system(size: screenHeight * 0.0175))
                .foregroundColor(.white)
            
            Spacer()
            
            if showRightArrow {
                HStack(spacing: 4) {
                    Text(value)
                        .font(.system(size: screenHeight * 0.0175))
                        .foregroundColor(.gray)
                    
                    Image(systemName: "chevron.right")
                        .font(.system(size: screenHeight * 0.0175))
                        .foregroundColor(.gray)
                }
            } else {
            Text(value)
                .font(.system(size: screenHeight * 0.0175))
                .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, screenWidth * 0.03)
        .padding(.vertical, screenHeight * UIConstants.settingsRowVerticalPadding)
        .padding(.top, isFirstInSection ? screenHeight * UIConstants.settingsFirstRowTopPadding : 0)
        .padding(.bottom, isLastInSection ? screenHeight * UIConstants.settingsLastRowBottomPadding : 0)
    }
}

// 通用的设置子页面视图
struct SettingsDetailView<Content: View>: View {
    @Environment(\.dismiss) private var dismiss
    let title: String
    let content: Content
    var rightButtonTitle: String? = nil
    var rightButtonAction: (() -> Void)? = nil
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    init(title: String, rightButtonTitle: String? = nil, rightButtonAction: (() -> Void)? = nil, @ViewBuilder content: () -> Content) {
        self.title = title
        self.rightButtonTitle = rightButtonTitle
        self.rightButtonAction = rightButtonAction
        self.content = content()
    }
    
    var body: some View {
        VStack(spacing: 0) {
            content
                .padding(.horizontal, screenWidth * 0.05)
                .padding(.top, screenHeight * 0.02)
            
            Spacer()
        }
        .background(Color(uiColor: .systemGray6).edgesIgnoringSafeArea(.all))
        .navigationTitle(title)
        .navigationBarTitleDisplayMode(.inline)
        .navigationBarBackButtonHidden(true)
        .enableSwipeBack()
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button {
                    dismiss()
                } label: {
                    Image(systemName: "chevron.left")
                        .foregroundColor(.white)
                        .font(.system(size: screenHeight * 0.02))
                }
            }
            ToolbarItem(placement: .principal) {
                Text(title)
                    .font(.system(size: screenHeight * 0.02, weight: .semibold))
                    .foregroundColor(.white)
            }
            
            if let rightButtonTitle = rightButtonTitle, let rightButtonAction = rightButtonAction {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: rightButtonAction) {
                        Text(rightButtonTitle)
                            .font(.system(size: screenHeight * 0.02))
                            .foregroundColor(UIConstants.dialIndicatorColor)
                    }
                }
            }
        }
    }
}

// 版权署名编辑页面
struct CopyrightEditorView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @State private var tempCopyrightName: String = ""
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "版权署名",
            rightButtonTitle: "保存",
            rightButtonAction: {
                viewModel.updateSetting(.copyright, value: tempCopyrightName)
            }
        ) {
            VStack(alignment: .leading, spacing: screenHeight * 0.02) {
                Text("输入版权署名")
                    .font(.system(size: screenHeight * 0.018))
                    .foregroundColor(.gray)
                
                TextField("", text: $tempCopyrightName)
                    .font(.system(size: screenHeight * 0.02))
                    .foregroundColor(.white)
                    .padding(.horizontal)
                    .padding(.vertical, screenHeight * 0.01)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(UIConstants.commonCornerRadius)
                    .accentColor(UIConstants.dialIndicatorColor)
                    .onAppear {
                        tempCopyrightName = viewModel.copyrightSignature
                    }
            }
        }
    }
}

struct FocusPeakingColorView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "峰值对焦颜色"
        ) {
            VStack(spacing: 0) {
                // 黄色选项
                ColorListItem(color: .yellow, title: "黄色", isSelected: viewModel.focusPeakingColor == "黄色") {
                    viewModel.updateSetting(.focusPeakingColor, value: "黄色")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 白色选项
                ColorListItem(color: .white, title: "白色", isSelected: viewModel.focusPeakingColor == "白色") {
                    viewModel.updateSetting(.focusPeakingColor, value: "白色")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 红色选项
                ColorListItem(color: .red, title: "红色", isSelected: viewModel.focusPeakingColor == "红色") {
                    viewModel.updateSetting(.focusPeakingColor, value: "红色")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 颜色列表项组件
struct ColorListItem: View {
    let color: Color
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            HStack {
                Circle()
                    .fill(color)
                    .frame(width: screenHeight * 0.025, height: screenHeight * 0.025)
                    .padding(.leading, screenHeight * 0.01)
                
                Text(title)
                    .font(.system(size: screenHeight * 0.0175))
                    .foregroundColor(.white)
                    .padding(.leading, screenHeight * 0.01)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(UIConstants.dialIndicatorColor)
                        .font(.system(size: screenHeight * 0.0175))
                        .padding(.trailing, screenHeight * 0.01)
                }
            }
            .padding(.vertical, screenHeight * 0.015)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 设置列表项组件 - 通用组件，可用于所有设置页面中的列表样式选项
struct SettingsListItem: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .font(.system(size: screenHeight * 0.0175))
                    .foregroundColor(.white)
                    .padding(.leading, screenHeight * 0.01)
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(UIConstants.dialIndicatorColor)
                        .font(.system(size: screenHeight * 0.0175))
                        .padding(.trailing, screenHeight * 0.01)
                }
            }
            .padding(.vertical, screenHeight * 0.015)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// ProRAW分辨率设置页面
struct ProRAWResolutionView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "ProRAW分辨率"
        ) {
            VStack(spacing: 0) {
                // 12MP选项
                SettingsListItem(title: "12MP", isSelected: viewModel.proRAWResolution == "12MP") {
                    viewModel.updateSetting(.proRAWResolution, value: "12MP")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 48MP选项
                SettingsListItem(title: "48MP", isSelected: viewModel.proRAWResolution == "48MP") {
                    viewModel.updateSetting(.proRAWResolution, value: "48MP")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// HEIC分辨率设置页面
struct HEICResolutionView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "HEIC分辨率"
        ) {
            VStack(spacing: 0) {
                // 12MP选项
                SettingsListItem(title: "12MP", isSelected: viewModel.heicResolution == "12MP") {
                    viewModel.updateSetting(.heicResolution, value: "12MP")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 24MP选项
                SettingsListItem(title: "24MP", isSelected: viewModel.heicResolution == "24MP") {
                    viewModel.updateSetting(.heicResolution, value: "24MP")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 48MP选项
                SettingsListItem(title: "48MP", isSelected: viewModel.heicResolution == "48MP") {
                    viewModel.updateSetting(.heicResolution, value: "48MP")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 视频码率设置页面
struct VideoBitrateView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "视频码率"
        ) {
            VStack(spacing: 0) {
                // 自动选项
                SettingsListItem(title: "自动", isSelected: viewModel.videoBitrate == "自动") {
                    viewModel.updateSetting(.videoBitrate, value: "自动")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 低码率选项
                SettingsListItem(title: "低（10 Mbps）", isSelected: viewModel.videoBitrate == "低（10 Mbps）") {
                    viewModel.updateSetting(.videoBitrate, value: "低（10 Mbps）")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 中码率选项
                SettingsListItem(title: "中（50 Mbps）", isSelected: viewModel.videoBitrate == "中（50 Mbps）") {
                    viewModel.updateSetting(.videoBitrate, value: "中（50 Mbps）")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 高码率选项
                SettingsListItem(title: "高（100 Mbps）", isSelected: viewModel.videoBitrate == "高（100 Mbps）") {
                    viewModel.updateSetting(.videoBitrate, value: "高（100 Mbps）")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 音频来源设置页面
struct AudioSourceView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "音频来源"
        ) {
            VStack(spacing: 0) {
                // 自动选项
                SettingsListItem(title: "自动", isSelected: viewModel.audioSource == "自动") {
                    viewModel.updateSetting(.audioSource, value: "自动")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 内置麦克风选项
                SettingsListItem(title: "内置麦克风", isSelected: viewModel.audioSource == "内置麦克风") {
                    viewModel.updateSetting(.audioSource, value: "内置麦克风")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 外置麦克风选项
                SettingsListItem(title: "外置麦克风", isSelected: viewModel.audioSource == "外置麦克风") {
                    viewModel.updateSetting(.audioSource, value: "外置麦克风")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 语言设置页面
struct LanguageSettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "语言"
        ) {
            VStack(spacing: 0) {
                // 跟随系统选项
                SettingsListItem(title: "跟随系统", isSelected: viewModel.language == "跟随系统") {
                    viewModel.updateSetting(.language, value: "跟随系统")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 简体中文选项
                SettingsListItem(title: "简体中文", isSelected: viewModel.language == "简体中文") {
                    viewModel.updateSetting(.language, value: "简体中文")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 繁体中文选项
                SettingsListItem(title: "繁體中文", isSelected: viewModel.language == "繁體中文") {
                    viewModel.updateSetting(.language, value: "繁體中文")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 英文选项
                SettingsListItem(title: "English", isSelected: viewModel.language == "English") {
                    viewModel.updateSetting(.language, value: "English")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 设备朝向设置页面
struct DeviceOrientationView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "设备朝向"
        ) {
            VStack(spacing: 0) {
                // 自动旋转选项
                SettingsListItem(title: "自动旋转", isSelected: viewModel.deviceOrientation == "自动旋转") {
                    viewModel.updateSetting(.deviceOrientation, value: "自动旋转")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 竖屏锁定选项
                SettingsListItem(title: "竖屏锁定", isSelected: viewModel.deviceOrientation == "竖屏锁定") {
                    viewModel.updateSetting(.deviceOrientation, value: "竖屏锁定")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 横屏锁定选项
                SettingsListItem(title: "横屏锁定", isSelected: viewModel.deviceOrientation == "横屏锁定") {
                    viewModel.updateSetting(.deviceOrientation, value: "横屏锁定")
                }
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// MARK: - 便捷初始化方法
extension SettingsView {
    /// 便捷初始化方法 - 使用依赖注入容器
    init() {
        self.init(viewModel: SettingsDependencyContainer.shared.createSettingsViewModel())
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView()
            .background(Color(uiColor: .systemGray6))
    }
}

// 意见反馈页面
struct FeedbackView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @State private var feedbackText: String = ""
    @State private var email: String = ""
    @State private var showAlert = false
    @State private var alertMessage = ""
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "意见反馈",
            rightButtonTitle: "提交",
            rightButtonAction: {
                // 验证并提交反馈
                if feedbackText.isEmpty {
                    alertMessage = "请输入反馈内容"
                    showAlert = true
                    return
                }
                
                // 这里可以调用ViewModel中的方法发送反馈
                viewModel.sendFeedback(email: email, content: feedbackText)
                
                // 显示成功提示
                alertMessage = "感谢您的反馈！"
                showAlert = true
                
                // 清空输入
                feedbackText = ""
                email = ""
            }
        ) {
            VStack(alignment: .leading, spacing: screenHeight * 0.02) {
                Text("您的邮箱（选填）")
                    .font(.system(size: screenHeight * 0.016))
                    .foregroundColor(.gray)
                
                TextField("", text: $email)
                    .font(.system(size: screenHeight * 0.018))
                    .foregroundColor(.white)
                    .padding(.horizontal)
                    .padding(.vertical, screenHeight * 0.0075)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(UIConstants.commonCornerRadius)
                    .keyboardType(.emailAddress)
                    .autocapitalization(.none)
                    .accentColor(UIConstants.dialIndicatorColor)
                
                Text("反馈内容")
                    .font(.system(size: screenHeight * 0.016))
                    .foregroundColor(.gray)
                
                TextEditor(text: $feedbackText)
                    .scrollContentBackground(.hidden)
                    .font(.system(size: screenHeight * 0.018))
                    .foregroundColor(.white)
                    .padding(.horizontal)
                    .padding(.vertical, screenHeight * 0.005)
                    .frame(height: screenHeight * 0.25)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(UIConstants.commonCornerRadius)
                    .accentColor(UIConstants.dialIndicatorColor)
                
                Text("我们会认真阅读每一条反馈，并努力改进产品")
                    .font(.system(size: screenHeight * 0.016))
                    .foregroundColor(.gray)
            }
            .alert(isPresented: $showAlert) {
                Alert(
                    title: Text("提示"),
                    message: Text(alertMessage),
                    dismissButton: .default(Text("确定"))
                )
            }
        }
    }
}

// 服务条款页面
struct TermsOfServiceView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "服务条款"
        ) {
            ScrollView(.vertical, showsIndicators: false) {
                VStack(alignment: .leading, spacing: screenHeight * 0.02) {
                    Text("Lomo 服务条款")
                        .font(.system(size: screenHeight * 0.022, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.bottom, screenHeight * 0.01)
                    
                    Group {
                        TermsSection(title: "1. 接受条款", content: "欢迎使用 Lomo 相机应用（以下简称\"本应用\"）。通过下载、安装或使用本应用，您表示您已阅读、理解并同意接受本服务条款的约束。如果您不同意本条款的任何部分，请勿使用本应用。")
                        
                        TermsSection(title: "2. 服务说明", content: "本应用提供专业相机功能，包括但不限于拍摄照片、录制视频、编辑图像等服务。我们保留随时修改、暂停或终止服务的权利，恕不另行通知。")
                        
                        TermsSection(title: "3. 用户责任", content: "您应负责维护您的设备安全和账户信息保密。对于因您未能保护您的设备或信息而导致的任何损失，我们概不负责。您同意仅将本应用用于合法目的，并遵守所有适用的法律法规。")
                        
                        TermsSection(title: "4. 隐私政策", content: "使用本应用受我们的隐私政策约束，该政策说明了我们如何收集、使用和保护您的个人信息。请查阅我们的隐私政策了解更多详情。")
                        
                        TermsSection(title: "5. 知识产权", content: "本应用及其内容（包括但不限于软件、图像、文本、商标、标识）均受知识产权法律保护。未经明确许可，不得复制、修改、分发或使用本应用的任何部分。")
                    }
                    
                    Group {
                        TermsSection(title: "6. 免责声明", content: "本应用按\"原样\"提供，不提供任何形式的明示或暗示保证。我们不保证服务不会中断或无错误，也不保证缺陷会被纠正。")
                        
                        TermsSection(title: "7. 责任限制", content: "在法律允许的最大范围内，我们对因使用或无法使用本应用而导致的任何直接、间接、偶然、特殊、后果性或惩罚性损害不承担责任。")
                        
                        TermsSection(title: "8. 条款修改", content: "我们保留随时修改本服务条款的权利。修改后的条款将在本应用内发布并立即生效。继续使用本应用将视为您接受修改后的条款。")
                        
                        TermsSection(title: "9. 适用法律", content: "本服务条款受中华人民共和国法律管辖，任何与本条款有关的争议应提交至有管辖权的法院解决。")
                        
                        TermsSection(title: "10. 联系我们", content: "如您对本服务条款有任何疑问或建议，请通过应用内的\"意见反馈\"功能与我们联系。")
                    }
                    
                    Text("最后更新日期：2023年12月1日")
                        .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.gray)
                        .padding(.top, screenHeight * 0.02)
                }
                .padding(.vertical, screenHeight * 0.02)
            }
            .safeAreaInset(edge: .bottom) {
                Color.clear.frame(height: screenHeight * 0.06)  // 添加6%的底部安全区域
            }
        }
    }
}

// 服务条款/隐私政策的章节组件
struct TermsSection: View {
    let title: String
    let content: String
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        VStack(alignment: .leading, spacing: screenHeight * 0.01) {
            Text(title)
                .font(.system(size: screenHeight * 0.02, weight: .semibold))
                .foregroundColor(.white)
            
            Text(content)
                .font(.system(size: screenHeight * 0.016))
                .foregroundColor(.white.opacity(0.8))
                .fixedSize(horizontal: false, vertical: true)
                .lineSpacing(5)
        }
        .padding(.bottom, screenHeight * 0.02)
    }
}

// 隐私政策页面
struct PrivacyPolicyView: View {
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "隐私政策"
        ) {
            ScrollView(.vertical, showsIndicators: false) {
                VStack(alignment: .leading, spacing: screenHeight * 0.02) {
                    Text("Lomo 隐私政策")
                        .font(.system(size: screenHeight * 0.022, weight: .bold))
                        .foregroundColor(.white)
                        .padding(.bottom, screenHeight * 0.01)
                    
                    Group {
                        TermsSection(title: "1. 引言", content: "Lomo 相机应用（以下简称\"本应用\"）尊重并保护所有用户的隐私权。本隐私政策旨在向您说明我们如何收集、使用、存储和共享您的个人信息，以及您享有的相关权利。请您在使用本应用前仔细阅读本隐私政策。")
                        
                        TermsSection(title: "2. 我们收集的信息", content: "当您使用本应用时，我们可能会收集以下信息：\n• 设备信息：如设备型号、操作系统版本、设备标识符等\n• 位置信息：当您启用位置服务时，我们会收集您的地理位置信息\n• 使用数据：如您使用的功能、设置和偏好\n• 照片和视频：仅当您明确授权时，我们才会访问您的相机和相册")
                        
                        TermsSection(title: "3. 信息使用", content: "我们使用收集到的信息用于：\n• 提供、维护和改进我们的服务\n• 开发新功能和优化用户体验\n• 回应您的反馈和提供客户支持\n• 遵守法律法规的要求")
                        
                        TermsSection(title: "4. 信息共享", content: "我们不会出售您的个人信息。在以下情况下，我们可能会共享您的信息：\n• 经您明确同意\n• 与我们的服务提供商合作以支持我们的业务\n• 遵守法律要求、保护我们的权利或防止非法活动")
                        
                        TermsSection(title: "5. 数据安全", content: "我们采取合理的安全措施保护您的个人信息免受未经授权的访问、使用或披露。然而，请注意互联网通信并非绝对安全，我们无法保证信息传输的绝对安全性。")
                    }
                    
                    Group {
                        TermsSection(title: "6. 数据存储", content: "我们会在实现本政策所述目的所必需的期间内保留您的个人信息，除非法律要求或允许更长的保留期限。")
                        
                        TermsSection(title: "7. 儿童隐私", content: "本应用不针对13岁以下儿童。我们不会故意收集13岁以下儿童的个人信息。如果您发现我们可能收集了儿童的个人信息，请通过本隐私政策中提供的联系方式与我们联系。")
                        
                        TermsSection(title: "8. 您的权利", content: "根据适用的法律，您可能拥有以下权利：\n• 访问您的个人信息\n• 更正不准确的个人信息\n• 删除您的个人信息\n• 限制或反对处理您的个人信息\n• 数据可携带性")
                        
                        TermsSection(title: "9. 隐私政策更新", content: "我们可能会不时更新本隐私政策。当我们进行重大变更时，我们会在应用内通知您。继续使用本应用将表示您接受修订后的隐私政策。")
                        
                        TermsSection(title: "10. 联系我们", content: "如果您对我们的隐私政策有任何疑问或顾虑，请通过应用内的\"意见反馈\"功能与我们联系。")
                    }
                    
                    Text("最后更新日期：2023年12月1日")
                        .font(.system(size: screenHeight * 0.016))
                        .foregroundColor(.gray)
                        .padding(.top, screenHeight * 0.02)
                }
                .padding(.vertical, screenHeight * 0.02)
            }
            .safeAreaInset(edge: .bottom) {
                Color.clear.frame(height: screenHeight * 0.06)  // 添加6%的底部安全区域
            }
        }
        .enableSwipeBack()
    }
}

// 启用边缘滑动返回手势的扩展
struct EnableSwipeBackModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .background(SwipeBackController())
    }
}

extension View {
    func enableSwipeBack() -> some View {
        self.modifier(EnableSwipeBackModifier())
    }
}

struct SwipeBackController: UIViewControllerRepresentable {
    func makeUIViewController(context: Context) -> UIViewController {
        let vc = UIViewController()
        return vc
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        DispatchQueue.main.async {
            if let navigationController = uiViewController.navigationController {
                navigationController.interactivePopGestureRecognizer?.isEnabled = true
                navigationController.interactivePopGestureRecognizer?.delegate = nil
            }
        }
    }
}

// 快门声选项组件
struct ShutterSoundItem: View {
    let title: String
    let isSelected: Bool
    let isSilent: Bool
    let needsProLabel: Bool
    let isProUser: Bool
    let selectAction: () -> Void
    let playAction: () -> Void
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        Button(action: selectAction) {
            HStack {
                HStack(spacing: screenWidth * 0.03) {
                    if isSilent {
                        Image(systemName: "speaker.slash")
                            .font(.system(size: screenHeight * 0.0175))
                            .foregroundColor(.white)
                            .frame(width: screenHeight * 0.0175)
                    } else {
                        Button(action: playAction) {
                            Image(systemName: "play.circle")
                                .font(.system(size: screenHeight * 0.0175))
                                .foregroundColor(.white)
                                .frame(width: screenHeight * 0.0175)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    if needsProLabel {
                        HStack(spacing: screenWidth * 0.01) {
                            Text(title)
                                .font(.system(size: screenHeight * 0.0175))
                            ProLabel(screenHeight: screenHeight, isProUser: isProUser)
                        }
                    } else {
                        Text(title)
                            .font(.system(size: screenHeight * 0.0175))
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(UIConstants.dialIndicatorColor)
                        .font(.system(size: screenHeight * 0.0175))
                        .padding(.trailing, screenHeight * 0.01)
                }
            }
            .foregroundColor(.white)
            .padding(.vertical, screenHeight * 0.015)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// 快门声选择页面
struct ShutterSoundView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "快门声"
        ) {
            VStack(spacing: 0) {
                // 静音选项
                ShutterSoundItem(
                    title: "静音", 
                    isSelected: viewModel.shutterSound == "静音",
                    isSilent: true,
                    needsProLabel: false,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "静音") },
                    playAction: {}
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 快门声1选项
                ShutterSoundItem(
                    title: "快门声1", 
                    isSelected: viewModel.shutterSound == "快门声1",
                    isSilent: false,
                    needsProLabel: false,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "快门声1") },
                    playAction: {
                        // 播放快门声1的音效
                    }
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 快门声2选项（带Pro标签）
                ShutterSoundItem(
                    title: "快门声2", 
                    isSelected: viewModel.shutterSound == "快门声2",
                    isSilent: false,
                    needsProLabel: true,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "快门声2") },
                    playAction: {
                        // 播放快门声2的音效
                    }
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 快门声3选项（带Pro标签）
                ShutterSoundItem(
                    title: "快门声3", 
                    isSelected: viewModel.shutterSound == "快门声3",
                    isSilent: false,
                    needsProLabel: true,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "快门声3") },
                    playAction: {
                        // 播放快门声3的音效
                    }
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 快门声4选项（带Pro标签）
                ShutterSoundItem(
                    title: "快门声4", 
                    isSelected: viewModel.shutterSound == "快门声4",
                    isSilent: false,
                    needsProLabel: true,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "快门声4") },
                    playAction: {
                        // 播放快门声4的音效
                    }
                )
                
              Divider()
                    .background(Color.gray.opacity(0.3))
                
                // 快门声5选项（带Pro标签）
                ShutterSoundItem(
                    title: "快门声5", 
                    isSelected: viewModel.shutterSound == "快门声5",
                    isSilent: false,
                    needsProLabel: true,
                    isProUser: viewModel.isProUser,
                    selectAction: { viewModel.updateSetting(.shutterSound, value: "快门声5") },
                    playAction: {
                        // 播放快门声5的音效
                    }
                )
                
                Divider()
                    .background(Color.gray.opacity(0.3))
            }
        }
    }
}

// 自定义比例设置视图
struct AspectRatioSettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    
    // 添加订阅管理器（通过依赖注入容器获取）
    @ObservedObject private var subscriptionManager = SubscriptionDependencyContainer.shared.subscriptionService as! SubscriptionService
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "自定义比例"
        ) {
            VStack(spacing: 0) {
                HStack {
                    HStack(spacing: screenWidth * 0.01) {
                        Text("自定义比例")
                        ProLabel(screenHeight: screenHeight, isProUser: viewModel.isProUser)
                    }
                    Spacer()
                    Toggle("", isOn: Binding(
                        get: { 
                            // 非Pro用户始终返回false
                            return subscriptionManager.isProUser ? viewModel.isCustomRatioEnabled : false 
                        },
                        set: { newValue in 
                            // 简化逻辑：Pro用户正常设置，非Pro用户显示订阅页面
                            if subscriptionManager.isProUser {
                                viewModel.isCustomRatioEnabled = newValue
                            } else {
                                subscriptionManager.showProView = true
                            }
                        }
                    ))
                    .labelsHidden()
                }
                .padding(.horizontal, screenWidth * 0.02)
                .padding(.vertical, screenWidth * 0.015)
                
                if viewModel.isCustomRatioEnabled && subscriptionManager.isProUser {
                    // ... existing code ...
                }
            }
        }
    }
} 