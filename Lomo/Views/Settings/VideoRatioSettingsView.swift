import SwiftUI

// 视频比例设置页面
struct VideoRatioSettingsView: View {
    @ObservedObject var viewModel: SettingsViewModel
    @State private var widthValue: String = ""
    @State private var heightValue: String = ""
    
    // 屏幕尺寸
    private let screenWidth = UIScreen.main.bounds.width
    private let screenHeight = UIScreen.main.bounds.height
    
    var body: some View {
        SettingsDetailView(
            title: "视频比例",
            rightButtonTitle: "保存",
            rightButtonAction: {
                // 直接保存用户输入，不管是否为空
                let ratio = widthValue.isEmpty || heightValue.isEmpty ? "" : "\(widthValue):\(heightValue)"
                viewModel.updateSetting(.videoRatio, value: ratio)
            }
        ) {
            VStack(alignment: .leading, spacing: screenHeight * 0.02) {
                Text("设置视频比例")
                    .font(.system(size: screenHeight * 0.018))
                    .foregroundColor(.gray)
                
                HStack(spacing: screenWidth * 0.03) {
                    // 宽度输入框
                    TextField("宽", text: $widthValue)
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                        .padding(.horizontal)
                        .padding(.vertical, screenHeight * 0.01)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(UIConstants.commonCornerRadius)
                        .keyboardType(.numberPad)
                        .accentColor(UIConstants.dialIndicatorColor)
                        .frame(maxWidth: .infinity)
                    
                    // 分隔符
                    Text(":")
                        .font(.system(size: screenHeight * 0.02, weight: .bold))
                        .foregroundColor(.white)
                    
                    // 高度输入框
                    TextField("高", text: $heightValue)
                        .font(.system(size: screenHeight * 0.02))
                        .foregroundColor(.white)
                        .padding(.horizontal)
                        .padding(.vertical, screenHeight * 0.01)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(UIConstants.commonCornerRadius)
                        .keyboardType(.numberPad)
                        .accentColor(UIConstants.dialIndicatorColor)
                        .frame(maxWidth: .infinity)
                }
            }
        }
    }
} 