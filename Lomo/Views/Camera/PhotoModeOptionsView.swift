import SwiftUI

struct PhotoModeOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 照片模式选项数据
    private let photoModeOptions: [(title: String, value: PhotoMode)] = [
        ("自动", .auto),         // 自动模式
        ("人像", .portrait),     // 人像模式
        ("夜景", .night),        // 夜景模式
        ("延时", .timelapse),    // 延时摄影
        ("微距", .macro),        // 微距模式
        ("光绘", .lightTrail),   // 光绘模式
        ("全景", .pano)          // 全景模式
    ]
    
    // Pro照片模式选项
    private let proPhotoModeOptions: [PhotoMode] = [
        .macro,        // 微距为Pro选项
        .lightTrail,   // 光绘为Pro选项
        .pano          // 全景为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: photoModeOptions,
            selectedValue: viewModel.state.photoMode,
            onSelect: viewModel.selectPhotoMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isPhotoModeOptionsVisible,
            proOptions: proPhotoModeOptions,
            position: .bottom,  // 设置为底部位置
            isProUser: viewModel.isProUser  // 传递Pro用户状态
        )
    }
} 