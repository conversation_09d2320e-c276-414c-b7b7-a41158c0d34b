import SwiftUI

struct TimerOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 定时器选项数据
    private let timerOptions: [(title: String, value: TimerMode)] = [
        ("关闭", .off),       // 立即拍摄
        ("3秒", .three),     // 3秒延时
        ("5秒", .five),      // 5秒延时
        ("10秒", .ten),      // 10秒延时
        ("15秒", .fifteen),  // 15秒延时
        ("30秒", .thirty)    // 30秒延时
    ]
    
    var body: some View {
        OptionGroup(
            options: timerOptions,
            selectedValue: viewModel.state.timerMode,
            onSelect: viewModel.selectTimerMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isTimerOptionsVisible,
            position: .bottom  // 设置为底部位置
        )
    }
} 