import SwiftUI
import AVFoundation
import Combine  // 添加Combine库

// 自定义形状：实现圆形到方形的平滑过渡
struct RecordingShape: Shape {
    var cornerRadius: CGFloat
    
    var animatableData: CGFloat {
        get { cornerRadius }
        set { cornerRadius = newValue }
    }
    
    func path(in rect: CGRect) -> Path {
        let path = RoundedRectangle(cornerRadius: cornerRadius)
        return path.path(in: rect)
    }
}

// 镜头按钮样式
private struct LensButtonStyle: ViewModifier {
    let screenHeight: CGFloat
    let baseSize: CGFloat
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(.white)
            .frame(width: screenHeight * UIConstants.lensButtonBaseSize, height: screenHeight * UIConstants.lensButtonBaseSize)
            .background(Color.black.opacity(UIConstants.buttonBackgroundOpacity))
            .clipShape(Circle())
    }
}

// 镜头容器样式
private struct LensContainerStyle: ViewModifier {
    let screenHeight: CGFloat
    private let screenMetrics = ScreenMetricsService.shared
    
    func body(content: Content) -> some View {
        content
            .foregroundColor(.white)
            .frame(height: screenHeight * UIConstants.lensContainerHeight)
            .padding(.horizontal, screenMetrics.screenWidth * UIConstants.lensContainerHorizontalPadding)
            .background(Color.black.opacity(0.25))
            .clipShape(Capsule())
    }
}

private extension View {
    func lensButtonStyle(screenHeight: CGFloat, baseSize: CGFloat) -> some View {
        modifier(LensButtonStyle(screenHeight: screenHeight, baseSize: UIConstants.lensButtonBaseSize))
    }
    
    func lensContainerStyle(screenHeight: CGFloat) -> some View {
        modifier(LensContainerStyle(screenHeight: screenHeight))
    }
}

struct LensButton: View {
    let configuration: LensConfiguration
    let action: () -> Void
    let longPressAction: () -> Void
    let screenHeight: CGFloat
    let currentZoomFactor: CGFloat
    @ObservedObject var viewModel: CameraViewModel
    let deviceOrientation: UIDeviceOrientation
    private let screenMetrics = ScreenMetricsService.shared
    
    init(configuration: LensConfiguration, action: @escaping () -> Void, longPressAction: @escaping () -> Void, screenHeight: CGFloat, currentZoomFactor: CGFloat, viewModel: CameraViewModel, deviceOrientation: UIDeviceOrientation) {
        self.configuration = configuration
        self.action = action
        self.longPressAction = longPressAction
        self.screenHeight = screenHeight
        self.currentZoomFactor = currentZoomFactor
        self.viewModel = viewModel
        self.deviceOrientation = deviceOrientation
    }
    
    // 格式化倍率显示
    private var formattedValue: String {
        viewModel.formatLensValue(
            lens: configuration.lens,
            isSelected: configuration.isSelected,
            currentZoomFactor: currentZoomFactor
        )
    }
    
    var body: some View {
        print("📱 LensButton: lens=\(configuration.lens), isSelected=\(configuration.isSelected), currentZoom=\(currentZoomFactor), display=\(formattedValue)")
        
        return Button(action: {
            // 按钮点击直接调用action
            action()
        }) {
            HStack(alignment: .lastTextBaseline, spacing: UIConstants.zoomDisplaySpacing) {
                Text(formattedValue)
                    .font(.system(size: screenHeight * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                
                if configuration.isSelected {
                    Image(systemName: "multiply")
                        .font(.system(size: screenHeight * UIConstants.zoomDisplayMultiplierSize, weight: UIConstants.zoomDisplayMultiplierWeight, design: .default))
                        .symbolRenderingMode(.monochrome)
                        .transition(
                            .asymmetric(
                                insertion: .move(edge: .trailing)
                                    .combined(with: .opacity.animation(.easeInOut(duration: AnimationConstants.duration))),
                                removal: .move(edge: .trailing)
                                    .combined(with: .opacity.animation(.easeInOut(duration: AnimationConstants.duration)))
                            )
                        )
                }
            }
            .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
            .foregroundColor(configuration.isSelected ? UIConstants.dialIndicatorColor : .white)
            .animation(viewModel.getSpringAnimation(response: AnimationConstants.duration, dampingFraction: 0.75), value: configuration.isSelected)
        }
        // 长按手势使用更高优先级的gesture处理
        .highPriorityGesture(
            LongPressGesture(minimumDuration: 0.25)
                .onEnded { _ in
                    longPressAction()
                }
        )
        .lensButtonStyle(screenHeight: screenHeight, baseSize: UIConstants.lensButtonBaseSize)
        .scaleEffect(configuration.isSelected ? 1.0 : 0.75)
        .animation(viewModel.getSpringAnimation(response: AnimationConstants.duration, dampingFraction: 0.75), value: configuration.isSelected)
    }
}

// 参数样式
private struct ParameterStyle: ViewModifier {
    let isParameterExpanded: Bool
    let screenHeight: CGFloat
    private let screenMetrics = ScreenMetricsService.shared
    
    func body(content: Content) -> some View {
        content
            .frame(
                width: isParameterExpanded ? screenMetrics.screenWidth * UIConstants.parameterExpandedWidth : nil,
                height: screenHeight * UIConstants.parameterHeight
            )
            .background(isParameterExpanded ? Color.black.opacity(UIConstants.topBarBackgroundOpacity) : Color.clear)
            .clipShape(Capsule())
    }
}

private extension View {
    func parameterStyle(isExpanded: Bool, screenHeight: CGFloat) -> some View {
        modifier(ParameterStyle(isParameterExpanded: isExpanded, screenHeight: screenHeight))
    }
}

// 添加自定义按钮样式
private struct TopBarButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
    }
}

// 添加按钮缩放样式
private struct BounceButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .scaleEffect(configuration.isPressed ? AnimationConstants.symbolBounceScale : 1.0)
            .animation(.spring(response: AnimationConstants.symbolBounceResponse, 
                               dampingFraction: AnimationConstants.symbolBounceDamping), 
                       value: configuration.isPressed)
    }
}

// 添加可切换的图标按钮
struct ToggleableIconButton: View {
    let imageName: String
    let isEnabled: Bool
    let size: CGFloat
    let action: () -> Void
    let resetTimer: () -> Void
    let isSystemImage: Bool
    let deviceOrientation: UIDeviceOrientation
    private let screenMetrics = ScreenMetricsService.shared
    
    var body: some View {
        Button(action: {
            action()
            resetTimer()
        }) {
            if isSystemImage {
                Image(systemName: imageName)
                    .resizable()
                    .scaledToFit()
                    .frame(width: screenMetrics.screenHeight * size,
                           height: screenMetrics.screenHeight * size)
                    .foregroundColor(isEnabled ? UIConstants.dialIndicatorColor : .white)
                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                    .frame(width: screenMetrics.screenHeight * UIConstants.functionButtonWidth,
                           height: screenMetrics.screenHeight * UIConstants.lensContainerHeight)
            } else {
                Image(imageName)
                    .renderingMode(.template)
                    .resizable()
                    .scaledToFit()
                    .frame(width: screenMetrics.screenHeight * size,
                           height: screenMetrics.screenHeight * size)
                    .foregroundColor(isEnabled ? UIConstants.dialIndicatorColor : .white)
                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                    .frame(width: screenMetrics.screenHeight * UIConstants.functionButtonWidth,
                           height: screenMetrics.screenHeight * UIConstants.lensContainerHeight)
            }
        }
    }
    
    // 添加默认参数的初始化方法
    init(
        imageName: String,
        isEnabled: Bool,
        size: CGFloat,
        action: @escaping () -> Void,
        resetTimer: @escaping () -> Void,
        isSystemImage: Bool = false,
        deviceOrientation: UIDeviceOrientation = .portrait
    ) {
        self.imageName = imageName
        self.isEnabled = isEnabled
        self.size = size
        self.action = action
        self.resetTimer = resetTimer
        self.isSystemImage = isSystemImage
        self.deviceOrientation = deviceOrientation
    }
}

// 参数选项视图
private struct ParameterOptionView: View {
    let option: String
    let optionType: ButtonControlLogic.ParameterOptionType
    let isExpanded: Bool
    let screenHeight: CGFloat
    @ObservedObject var viewModel: CameraViewModel
    let deviceOrientation: UIDeviceOrientation
    
    var body: some View {
        HStack(spacing: 0) {
            switch optionType {
            case .separator:
                if !isExpanded {
                    Text("・")
                        // 直接使用工具类判断横屏
                        .fixedSize(horizontal: !OrientationUtils.isLandscape(deviceOrientation), vertical: OrientationUtils.isLandscape(deviceOrientation))
                        // 先应用尺寸，再旋转
                        .frame(
                            width: nil,
                            // 直接使用工具类判断横屏
                            height: OrientationUtils.isLandscape(deviceOrientation) ? nil : screenHeight * UIConstants.parameterHeight
                        )
                        .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                        .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                }
            case .icon:
                if isExpanded {
                    Button(action: {
                        viewModel.handleParameterIconTap(option: option)
                        viewModel.resetParameterTimer()
                    }) {
                        if option == "bolt.fill" {
                            Image(systemName: viewModel.getParameterFlashIcon())
                                .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                                .foregroundColor(viewModel.getParameterIconColor(for: option))
                                .fixedSize(horizontal: !OrientationUtils.isLandscape(deviceOrientation), vertical: OrientationUtils.isLandscape(deviceOrientation))
                                .frame(
                                    width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : UIScreen.main.bounds.width * UIConstants.parameterExpandedWidth,
                                    height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.025 : screenHeight * UIConstants.parameterHeight
                                )
                                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                .clipShape(Capsule())
                                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                        } else {
                            Image(systemName: option)
                                .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                                .foregroundColor(viewModel.getParameterIconColor(for: option))
                                .fixedSize(horizontal: !OrientationUtils.isLandscape(deviceOrientation), vertical: OrientationUtils.isLandscape(deviceOrientation))
                                .frame(
                                    width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : UIScreen.main.bounds.width * UIConstants.parameterExpandedWidth,
                                    height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.025 : screenHeight * UIConstants.parameterHeight
                                )
                                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                .clipShape(Capsule())
                                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                        }
                    }
                    .id(option)
                } else {
                    // 非展开状态下的图标组
                    Group {
                        if option == "bolt.fill" {
                            Image(systemName: viewModel.getParameterFlashIcon())
                                .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                                .foregroundColor(viewModel.getParameterIconColor(for: option))
                        } else {
                            Image(systemName: option)
                                .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                                .foregroundColor(viewModel.getParameterIconColor(for: option))
                        }
                    }
                    .fixedSize(horizontal: !OrientationUtils.isLandscape(deviceOrientation), vertical: OrientationUtils.isLandscape(deviceOrientation))
                    .frame(
                        width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : nil,
                        height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.025 : screenHeight * UIConstants.parameterHeight
                    )
                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                }
            case .text:
                if isExpanded {
                    Button(action: {
                        viewModel.handleParameterIconTap(option: option)
                        viewModel.resetParameterTimer()
                    }) {
                        // 为文本添加正确的布局处理
                        Text(viewModel.getParameterTextValue(for: option))
                            .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                            .foregroundColor(viewModel.getParameterIconColor(for: option))
                            // 确保文本在横屏模式下居中
                            .frame(width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : nil, alignment: .center)
                            // 在横屏时固定高度而非宽度
                            .fixedSize(horizontal: false, vertical: OrientationUtils.isLandscape(deviceOrientation))
                            // 为按钮添加正确的尺寸
                            .frame(
                                width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : UIScreen.main.bounds.width * UIConstants.parameterExpandedWidth,
                                height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.025 : screenHeight * UIConstants.parameterHeight
                            )
                            .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                            .clipShape(Capsule())
                            .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                    }
                    .id(option)
                } else {
                    // 非展开状态下的文本
                    Text(viewModel.getParameterTextValue(for: option))
                        .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? UIConstants.parameterFontSizeLandscape : UIConstants.parameterFontSize), weight: .semibold))
                        .foregroundColor(viewModel.getParameterIconColor(for: option))
                        // 确保文本在横屏模式下居中
                        .frame(width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : nil, alignment: .center)
                        // 禁用文本的fixedSize以允许截断
                        .lineLimit(1)
                        .fixedSize(horizontal: false, vertical: OrientationUtils.isLandscape(deviceOrientation))
                        // 先应用尺寸，再旋转
                        .frame(
                            width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.045 : nil,
                            height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * 0.025 : screenHeight * UIConstants.parameterHeight
                        )
                        .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                        .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                        .id(option)
                }
            }
        }
    }
}

// 参数选项组视图
private struct ParameterOptionsView: View {
    let options: [String]
    let isExpanded: Bool
    let getOptionType: (String) -> ButtonControlLogic.ParameterOptionType
    let screenHeight: CGFloat
    let viewModel: CameraViewModel
    let deviceOrientation: UIDeviceOrientation
    
    var body: some View {
        HStack(spacing: isExpanded ? nil : 0) {
            ForEach(options.indices, id: \.self) { index in
                if isExpanded { Spacer() }
                
                ParameterOptionView(
                    option: options[index],
                    optionType: getOptionType(options[index]),
                    isExpanded: isExpanded,
                    screenHeight: screenHeight,
                    viewModel: viewModel,
                    deviceOrientation: deviceOrientation
                )
            }
        }
    }
}

// 顶部控制栏视图
private struct TopBarView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    let deviceOrientation: UIDeviceOrientation
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 0) {
                if viewModel.isParameterExpanded() {
                    Button(action: { 
                        viewModel.toggleVideoModeWithAnimation()
                    }) {
                        ZStack {
                            Circle()
                                .fill(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                .frame(width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * UIConstants.topBarHeightLandscape : screenHeight * UIConstants.topBarHeight, 
                                       height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * UIConstants.topBarHeightLandscape : screenHeight * UIConstants.topBarHeight)
                            
                            if viewModel.state.isVideoMode {
                                Image(systemName: "video.fill")
                                    .resizable()
                                    .renderingMode(.template)
                                    .scaledToFit()
                                    .frame(width: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize), 
                                          height: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize))
                                    .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize), weight: .bold))
                                    .foregroundColor(.white)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                    .transition(.opacity.animation(AnimationConstants.standardEaseInOut))
                            } else {
                                Image(systemName: "camera.fill")
                                    .resizable()
                                    .renderingMode(.template)
                                    .scaledToFit()
                                    .frame(width: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize), 
                                          height: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize))
                                    .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.025 : UIConstants.modeSwitchIconSize), weight: .bold))
                                    .foregroundColor(.white)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                    .transition(.opacity.animation(AnimationConstants.standardEaseInOut))
                            }
                        }
                        .animation(AnimationConstants.standardEaseInOut, value: viewModel.state.isVideoMode)
                    }
                    .buttonStyle(TopBarButtonStyle())
                    
                    Spacer()
                }
                
                Group {
                    HStack(spacing: viewModel.state.isParameterExpanded ? nil : 0) {
                        if viewModel.state.isParameterExpanded {
                            Button(action: {
                                viewModel.collapseParameter()
                            }) {
                                Image(systemName: "xmark")
                                    .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: OrientationUtils.isLandscape(deviceOrientation) ? .semibold : .bold))
                                    .foregroundColor(.white)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                            }
                            .frame(width: screenHeight * UIConstants.parameterHeight, height: screenHeight * UIConstants.parameterHeight)
                            .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                            .clipShape(Circle())
                        }

                        ParameterOptionsView(
                            options: viewModel.currentOptions,
                            isExpanded: viewModel.state.isParameterExpanded,
                            getOptionType: viewModel.getOptionType,
                            screenHeight: screenHeight,
                            viewModel: viewModel,
                            deviceOrientation: deviceOrientation
                        )
                    }
                }
                .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .medium))
                .foregroundColor(.white)
                .frame(height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * UIConstants.topBarHeightLandscape : screenHeight * UIConstants.topBarHeight)
                .frame(maxWidth: viewModel.state.isParameterExpanded ? .infinity : nil)
                .padding(.horizontal, UIScreen.main.bounds.width * (viewModel.state.isParameterExpanded ? UIConstants.lensContainerHorizontalPadding : UIConstants.horizontalPadding))
                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                .clipShape(Capsule())
                .animation(AnimationConstants.standardSpring, value: viewModel.state.isVideoMode) // 保留胶囊的动画
                .animation(AnimationConstants.standardSpring, value: viewModel.state.isParameterExpanded)
                .onTapGesture {
                    viewModel.expandParameterWithAnimation()
                }
                
                // 使用新的计算属性判断是否显示次要控件
                if viewModel.canShowSecondaryTopBarControls {
                    Spacer()

                    if viewModel.shouldShowCameraSwitchButton() {
                        Button(action: { 
                            viewModel.switchCamera()
                        }) {
                            Image(systemName: "arrow.trianglehead.2.clockwise.rotate.90")
                                .font(.system(size: screenHeight * (OrientationUtils.isLandscape(deviceOrientation) ? 0.0225 : UIConstants.headerIconSize), weight: .bold))
                                .foregroundColor(.white)
                                .rotationEffect(.degrees(viewModel.rotationAngle), anchor: .center)
                                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                .animation(.easeInOut(duration: 0.5), value: viewModel.rotationAngle)
                                .frame(width: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * UIConstants.topBarHeightLandscape : screenHeight * UIConstants.topBarHeight, 
                                       height: OrientationUtils.isLandscape(deviceOrientation) ? screenHeight * UIConstants.topBarHeightLandscape : screenHeight * UIConstants.topBarHeight)
                                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                .clipShape(Circle())
                        }
                        .buttonStyle(TopBarButtonStyle())
                        .disabled(viewModel.isRotating)
                    }
                }
            }
            
            // 视频模式相关选项
            if viewModel.shouldShowVideoModeControls() {
                if viewModel.state.isVideoEncodingOptionsVisible {
                    VideoEncodingOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                } else if viewModel.state.isAspectRatioOptionsVisible {
                    AspectRatioOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                } else if viewModel.state.isResolutionOptionsVisible {
                    ResolutionOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                } else if viewModel.state.isFrameRateOptionsVisible {
                    FrameRateOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                } else if viewModel.state.isColorSpaceOptionsVisible {
                    ColorSpaceOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                }
            } else {
                // 照片模式相关选项
                if viewModel.state.isPhotoFormatOptionsVisible {
                    PhotoFormatOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                } else if viewModel.state.isPhotoRatioOptionsVisible {
                    PhotoRatioOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                }
            }
        }
        .padding(.horizontal, UIScreen.main.bounds.width * UIConstants.horizontalPadding)
    }
}

struct CameraView: View {
    @ObservedObject var viewModel: CameraViewModel
    @ObservedObject var sharedTabViewModel: SharedTabViewModel // 改为@ObservedObject，从外部接收
    @StateObject private var filterViewModel = GalleryFilterViewModel()
    @StateObject private var editViewModel = EditViewModel()
    @StateObject private var galleryViewModel = GalleryDependencyContainer.galleryViewModel()
    
    // 添加设备方向状态
    @State private var deviceOrientation: UIDeviceOrientation = UIDevice.current.orientation
    private var isLandscapeOrientation: Bool {
        OrientationUtils.isLandscape(deviceOrientation)
    }
    
    // 状态栏控制
    @Environment(\.presentationMode) var presentationMode
    
    // 屏幕尺寸服务
    private let screenMetrics = ScreenMetricsService.shared
    
    // 屏幕尺寸计算属性
    private var screenHeight: CGFloat {
        return screenMetrics.screenHeight
    }
    
    private var screenWidth: CGFloat {
        return screenMetrics.screenWidth
    }
    
    // 存储服务
    private let storageService = UserDefaultsService.shared
    
    var lensButtons: some View {
        ZStack(alignment: .center) {
            if viewModel.state.isDialVisible {
                // 根据当前激活的刻度盘模式显示对应的刻度盘
                switch viewModel.state.activeDialMode {
                case .zoom:
                    DialView(
                        config: ZoomDialConfiguration(
                            lenses: viewModel.state.availableLenses,
                            currentZoom: viewModel.state.currentZoomFactor,
                            isVideoMode: viewModel.state.isVideoMode,
                            hasTelephotos: viewModel.state.availableLenses.contains("3") || viewModel.state.availableLenses.contains("5")
                        ),
                        value: Binding(
                            get: { Double(viewModel.state.currentZoomFactor) },
                            set: { viewModel.updateZoom(CGFloat($0)) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateZoom(CGFloat(value))
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .exposure:
                    DialView(
                        config: ExposureDialConfiguration(currentValue: viewModel.state.exposureValue),
                        value: Binding(
                            get: { viewModel.state.exposureValue },
                            set: { viewModel.updateExposure($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateExposure(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .focus:
                    DialView(
                        config: FocusDialConfiguration(currentValue: viewModel.state.focusDistance),
                        value: Binding(
                            get: { viewModel.state.focusDistance },
                            set: { viewModel.updateFocus($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateFocus(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .shutter:
                    DialView(
                        config: ShutterDialConfiguration(currentValue: viewModel.state.shutterValue),
                        value: Binding(
                            get: { viewModel.state.shutterValue },
                            set: { viewModel.updateShutter($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateShutter(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .iso:
                    DialView(
                        config: ISODialConfiguration(currentValue: viewModel.state.isoValue),
                        value: Binding(
                            get: { viewModel.state.isoValue },
                            set: { viewModel.updateISO($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateISO(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .wb:
                    // 白平衡刻度盘暂未实现
                    EmptyView()
                case .tint:
                    DialView(
                        config: TintDialConfiguration(currentValue: viewModel.state.tintValue),
                        value: Binding(
                            get: { viewModel.state.tintValue },
                            set: { viewModel.updateTint($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateTint(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .temperature:
                    DialView(
                        config: TemperatureDialConfiguration(currentValue: viewModel.state.temperatureValue),
                        value: Binding(
                            get: { viewModel.state.temperatureValue },
                            set: { viewModel.updateTemperature($0) }
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onValueChanged: { value in
                            viewModel.updateTemperature(value)
                        },
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .aperture:
                    DialView(
                        config: ApertureDialConfiguration(device: viewModel.getCurrentDevice()),
                        value: Binding(
                            get: { viewModel.state.apertureValue },
                            set: { _ in }  // 光圈是固定值，不需要更新
                        ),
                        mode: viewModel.state.isVideoMode ? .video : .photo,
                        availableLenses: Set(viewModel.state.availableLenses),
                        device: viewModel.getCurrentDevice(),
                        onTouchBegan: {
                            viewModel.handleTouchBeganForDial()
                        },
                        onTouchEnded: {
                            viewModel.handleTouchEndedForDial()
                        },
                        viewModel: viewModel
                    )
                case .none:
                    EmptyView()
                }
            } else {
                HStack {
                    // 左侧按钮
                    if viewModel.shouldShowLeftButton() {
                        HStack {
                            Button(action: {
                                withAnimation(AnimationConstants.standardSpring) {
                                    viewModel.toggleLeftButton()
                                }
                            }) {
                                Image(systemName: "circle.grid.3x3.fill")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                           height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                    .foregroundColor(.white)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                            }
                            
                            if viewModel.state.isLeftButtonExpanded {
                                Spacer()
                                
                                // 直方图
                                ToggleableIconButton(
                                    imageName: "LH BarChart",
                                    isEnabled: viewModel.isHistogramEnabled,
                                    size: 0.03,
                                    action: viewModel.toggleHistogramWithTimer,
                                    resetTimer: { },
                                    deviceOrientation: deviceOrientation
                                )
                                
                                Spacer()
                                
                                // 参考线
                                ToggleableIconButton(
                                    imageName: "LH Grid3x3",
                                    isEnabled: viewModel.isGridEnabled,
                                    size: 0.035,
                                    action: viewModel.toggleGridWithTimer,
                                    resetTimer: { },
                                    deviceOrientation: deviceOrientation
                                )
                                
                                Spacer()
                                
                                // HDR
                                if !viewModel.state.isVideoMode {
                                    ToggleableIconButton(
                                        imageName: "timer",
                                        isEnabled: viewModel.isTimerEnabled,
                                        size: 0.0225,
                                        action: viewModel.toggleTimerOptionsWithTimer,
                                        resetTimer: { },
                                        isSystemImage: true,
                                        deviceOrientation: deviceOrientation
                                    )
                                } else {
                                    // 防抖（仅视频模式显示）
                                    ToggleableIconButton(
                                        imageName: "dot.radiowaves.left.and.right",
                                        isEnabled: viewModel.isStabilizationEnabled,
                                        size: 0.03,
                                        action: viewModel.toggleStabilizationWithTimer,
                                        resetTimer: { },
                                        isSystemImage: true,
                                        deviceOrientation: deviceOrientation
                                    )
                                }
                                
                                Spacer()
                                
                                if !viewModel.state.isVideoMode {
                                    // 拍摄模式（照片模式）
                                    Button(action: {
                                        viewModel.togglePhotoModeOptionsWithTimer()
                                    }) {
                                        Image(systemName: "a.circle")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * 0.025,
                                                   height: UIScreen.main.bounds.height * 0.025)
                                            .foregroundColor(viewModel.state.photoMode != .auto ? UIConstants.dialIndicatorColor : .white)
                                            .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                                   height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    }
                                } else {
                                    // 斑马纹（视频模式）
                                    Button(action: {
                                        viewModel.toggleZebraWithTimer()
                                    }) {
                                        Image("LH BMX")
                                            .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * 0.035,
                                                   height: UIScreen.main.bounds.height * 0.035)
                                            .foregroundColor(viewModel.isZebraEnabled ? UIConstants.dialIndicatorColor : .white)
                                            .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                                   height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    }
                                }
                                
                                Spacer()
                                
                                // 翻转
                                Button(action: {
                                    viewModel.toggleFlipOptionsWithTimer()
                                }) {
                                    Image(systemName: "arrowtriangle.right.and.line.vertical.and.arrowtriangle.left.fill")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: UIScreen.main.bounds.height * UIConstants.flipIconSize,
                                               height: UIScreen.main.bounds.height * UIConstants.flipIconSize)
                                        .foregroundColor(viewModel.state.flipMode != .off ? UIConstants.dialIndicatorColor : .white)
                                        .rotationEffect(.degrees(90))
                                        .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                        .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                        .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                               height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                }
                                
                                Spacer()
                                
                                // 峰值对焦
                                Button(action: {
                                    viewModel.togglePeakingWithTimer()
                                }) {
                                    Image(systemName: "person.and.background.dotted")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: UIScreen.main.bounds.height * UIConstants.peakFocusIconSize,
                                               height: UIScreen.main.bounds.height * UIConstants.peakFocusIconSize)
                                        .symbolRenderingMode(.palette)
                                        .foregroundStyle(
                                            viewModel.isPeakingEnabled ? UIConstants.dialIndicatorColor : .white,
                                            .white
                                        )
                                        .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                        .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                        .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                               height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                }
                                
                                Spacer()
                                
                                // 水平仪
                                ToggleableIconButton(
                                    imageName: "circle.and.line.horizontal",
                                    isEnabled: viewModel.isLevelEnabled,
                                    size: 0.03,
                                    action: viewModel.toggleLevelWithTimer,
                                    resetTimer: { },
                                    isSystemImage: true,
                                    deviceOrientation: deviceOrientation
                                )
                                .padding(.trailing, UIScreen.main.bounds.width * 0.01)  // 1%屏幕宽度的右内边距
                            }
                        }
                        .frame(width: viewModel.state.isLeftButtonExpanded ? 
                            UIScreen.main.bounds.width * (1 - 2 * UIConstants.horizontalPadding) : 
                            UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                            height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                        .background(Color.black.opacity(UIConstants.buttonBackgroundOpacity))
                        .clipShape(Capsule())
                        .animation(AnimationConstants.standardSpring, value: viewModel.state.isLeftButtonExpanded)
                    }
                    
                    if !viewModel.state.isLeftButtonExpanded && !viewModel.state.isRightButtonExpanded {
                        Spacer()
                        
                        // 使用新的计算属性判断是否显示镜头选择器
                        if viewModel.shouldShowLensSelector {
                            // 恢复原始的 HStack 和 ForEach 循环逻辑
                            HStack(spacing: screenHeight * UIConstants.lensButtonSpacing) {
                                // 遍历可用镜头的索引
                                ForEach(viewModel.state.availableLenses.indices, id: \.self) { index in
                                    // 在循环内部构建 LensConfiguration
                                    let lens = viewModel.state.availableLenses[index]
                                    let isSelected = viewModel.state.selectedLensIndex == index
                                    let config = LensConfiguration(lens: lens, isSelected: isSelected)
                                    
                                    LensButton(
                                        configuration: config,
                                        // 恢复原始的 action
                                        action: { 
                                            viewModel.selectLensAtIndex(index)
                                            // 标记为从按钮切换 (如果需要保留)
                                            storageService.setIsFromButtonSelection(true)
                                        },
                                        // 恢复原始的 longPressAction
                                        longPressAction: { 
                                            viewModel.showZoomDial() 
                                        },
                                        screenHeight: screenHeight,
                                        // 使用正确的 currentZoomFactor
                                        currentZoomFactor: viewModel.state.currentZoomFactor,
                                        viewModel: viewModel,
                                        deviceOrientation: deviceOrientation
                                    )
                                }
                            }
                            .lensContainerStyle(screenHeight: screenHeight)
                            .transition(.opacity.animation(AnimationConstants.standardSpring))
                        }
                        
                        Spacer()
                    }
                    
                    // 右侧按钮
                    if viewModel.shouldShowRightButton() {
                        HStack {
                            if viewModel.state.isRightButtonExpanded {
                                // 光圈固定在左侧
                                Button(action: {
                                    viewModel.showApertureDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)  // 顶部间隙1%
                                        Image(systemName: "camera.aperture")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(ApertureDialUtils.formatValue(Double(viewModel.getCurrentDevice()?.lensAperture ?? 1.8)))
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)  // 底部间隙0.5%
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                                .padding(.leading, UIScreen.main.bounds.width * 0.01)  // 1%屏幕宽度的左内边距
                                
                                Spacer()
                                
                                // 快门
                                Button(action: {
                                    viewModel.showShutterDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image(systemName: "s.circle")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(ShutterDialUtils.formatShutterValue(viewModel.state.shutterValue))
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                                
                                Spacer()
                                
                                // ISO
                                Button(action: {
                                    viewModel.showISODialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image("LH ISO")
                                            .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(viewModel.formattedISOValue)
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                            .onAppear {
                                                print("📊 ISO按钮显示 - isoValue: \(viewModel.state.isoValue)")
                                            }
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                                
                                Spacer()
                                
                                // 曝光
                                Button(action: {
                                    viewModel.showExposureDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image(systemName: "plusminus.circle")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(viewModel.formattedExposureValue)
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                                
                                Spacer()
                                
                                // 对焦
                                Button(action: {
                                    viewModel.showFocusDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image(systemName: "viewfinder.rectangular")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(viewModel.formattedFocusValue)
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                                
                                Spacer()
                                
                                // 色调
                                Button(action: {
                                    viewModel.showTintDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image(systemName: "aqi.medium")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(viewModel.formattedTintValue)
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                            .onAppear {
                                                print("🎨 色调按钮显示 - tintValue: \(viewModel.state.tintValue)")
                                            }
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }

                                Spacer()

                                // 色温
                                Button(action: {
                                    viewModel.showTemperatureDialWithTimer()
                                }) {
                                    VStack {
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.01)
                                        Image(systemName: "thermometer")
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.headerIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.headerIconSize)
                                        Text(viewModel.formattedTemperatureValue)
                                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.valueTextSize))
                                            .onAppear {
                                                print("🌡️ 色温按钮显示 - temperatureValue: \(viewModel.state.temperatureValue)K")
                                            }
                                        Spacer(minLength: UIScreen.main.bounds.height * 0.005)
                                    }
                                    .foregroundColor(.white)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionButtonWidth,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                }
                            }
                            
                            // A/M 图标保持在右侧
                            Button(action: {
                                viewModel.toggleRightButtonWithTimer()
                            }) {
                                Image(systemName: viewModel.state.isAMMode ? "m.circle.fill" : "a.circle.fill")
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                           height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                    .foregroundColor(.white)
                                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                    .frame(width: UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                                           height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                            }
                        }
                        .frame(width: viewModel.state.isRightButtonExpanded ? 
                            UIScreen.main.bounds.width * (1 - 2 * UIConstants.horizontalPadding) : 
                            UIScreen.main.bounds.height * UIConstants.lensContainerHeight,
                            height: UIScreen.main.bounds.height * UIConstants.lensContainerHeight)
                        .background(Color.black.opacity(UIConstants.buttonBackgroundOpacity))
                        .clipShape(Capsule())
                        .animation(AnimationConstants.standardSpring, value: viewModel.state.isRightButtonExpanded)
                    }
                }
                .padding(.horizontal, UIScreen.main.bounds.width * UIConstants.horizontalPadding)
            }
        }
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    if viewModel.state.isDialVisible {
                        viewModel.handleTouchBeganForDial()
                    }
                }
                .onEnded { _ in
                    if viewModel.state.isDialVisible {
                        viewModel.handleTouchEndedForDial()
                    }
                }
        )
    }
    
    var body: some View {
        // 相机界面 - 移除外层ZStack和SharedTabView，只保留核心部分
        GeometryReader { geometry in
            ZStack {
                // 相机预览层
                CameraPreviewView()
                    .ignoresSafeArea()
                    .edgesIgnoringSafeArea(.all)
                
                // UI控制层
                VStack(spacing: 0) {
                    // 顶部控制栏 - 传递设备方向参数
                    TopBarView(viewModel: viewModel, screenHeight: screenHeight, deviceOrientation: deviceOrientation)
                    
                    // 视频模式时间显示 - 根据设备方向选择不同布局
                    if viewModel.state.isVideoMode {
                        // 使用新的统一RecordingInfoView组件
                        RecordingInfoView(
                            viewModel: viewModel,
                            screenHeight: screenHeight,
                            deviceOrientation: deviceOrientation
                        )
                    }
                    
                    Spacer()
                    
                    // 底部控制区
                    VStack(spacing: 0) {
                        // 直方图视图
                        HistogramView(viewModel: viewModel, screenHeight: screenHeight, deviceOrientation: deviceOrientation)
                            .padding(.horizontal, screenWidth * UIConstants.horizontalPadding)
                            .padding(.bottom, screenHeight * 0.03)
                        
                        if viewModel.state.isStabilizationOptionsVisible {
                            StabilizationOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                        } else if viewModel.state.isFlipOptionsVisible {
                            FlipOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                        } else if !viewModel.state.isVideoMode && viewModel.state.isPhotoModeOptionsVisible {
                            PhotoModeOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                        } else if !viewModel.state.isVideoMode && viewModel.state.isTimerOptionsVisible {
                            TimerOptionsView(viewModel: viewModel, screenHeight: screenHeight)
                        }
                        
                        // 镜头切换和底部控制栏容器
                        VStack(spacing: screenHeight * UIConstants.verticalSpacing) {
                            lensButtons
                            
                            // 底部控制栏
                            HStack {
                                // 最左侧预览缩略图按钮
                                Button(action: { 
                                    sharedTabViewModel.show(initialTab: .gallery)
                                }) {
                                    RoundedRectangle(cornerRadius: screenHeight * UIConstants.previewCornerRadius)
                                        .stroke(Color.white, lineWidth: screenHeight * UIConstants.previewBorderWidth)
                                        .frame(width: screenHeight * UIConstants.previewSize, height: screenHeight * UIConstants.previewSize)
                                        .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                        .clipShape(RoundedRectangle(cornerRadius: screenHeight * UIConstants.previewCornerRadius))
                                }
                                .opacity(viewModel.shouldShowPreviewButton() ? 1 : 0)
                                
                                Spacer()
                                
                                // 滤镜按钮（原来是锁定按钮的位置）
                                Button(action: { 
                                    // 根据录制状态决定行为
                                    if viewModel.isVideoRecording {
                                        viewModel.toggleRecordingPauseWithFeedback() // 改为暂停/继续录制
                                    } else {
                                        sharedTabViewModel.show(initialTab: .filter)
                                    }
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(viewModel.isVideoRecording && viewModel.isRecordingPaused ? 
                                                UIConstants.recordingColor : // 暂停状态下为红色
                                                Color.black.opacity(UIConstants.topBarBackgroundOpacity)) // 其他状态为半透明黑色
                                            .frame(width: screenHeight * UIConstants.previewSize, height: screenHeight * UIConstants.previewSize)
                                        
                                        if viewModel.isVideoRecording {
                                            // 录制状态下始终显示暂停图标（两条竖线）
                                            Image(systemName: "pause.fill")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                                       height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                                .foregroundColor(.white)
                                        } else {
                                            // 非录制状态显示滤镜图标
                                            Image(systemName: "camera.filters")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                                       height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                                .foregroundColor(.white)
                                                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                        }
                                    }
                                }
                                .buttonStyle(BounceButtonStyle())
                                
                                Spacer()
                                
                                // 拍摄/录制按钮
                                Button(action: {
                                    viewModel.handleShutterButtonPress()
                                }) {
                                    ZStack {
                                        // 外圈 - 修改颜色根据暂停状态变化
                                        Circle()
                                            .stroke(viewModel.isVideoRecording && viewModel.isRecordingPaused ? Color.gray : Color.white, lineWidth: screenHeight * UIConstants.shutterButtonBorderWidth)
                                            .frame(width: screenHeight * UIConstants.shutterButtonSize, height: screenHeight * UIConstants.shutterButtonSize)
                                        
                                        // 内部形状
                                        if viewModel.state.isVideoMode {
                                            // 使用单一形状视图实例并添加整体动画
                                            RecordingShape(cornerRadius: viewModel.isVideoRecording ? 
                                                          UIConstants.shutterButtonRecordingCornerRadius : 
                                                          screenHeight * (UIConstants.shutterButtonInnerSize / 2))
                                                .fill(UIConstants.recordingColor)
                                                .frame(width: viewModel.isVideoRecording ? 
                                                      screenHeight * UIConstants.shutterButtonRecordingSize : 
                                                      screenHeight * UIConstants.shutterButtonInnerSize, 
                                                         height: viewModel.isVideoRecording ? 
                                                         screenHeight * UIConstants.shutterButtonRecordingSize : 
                                                         screenHeight * UIConstants.shutterButtonInnerSize)
                                                .animation(AnimationConstants.standardSpring, value: viewModel.isVideoRecording)
                                        } else {
                                            Circle()
                                                .fill(Color.white)
                                                .frame(width: screenHeight * UIConstants.shutterButtonInnerSize, height: screenHeight * UIConstants.shutterButtonInnerSize)
                                        }
                                    }
                                }
                                
                                Spacer()
                                
                                // 编辑按钮
                                Button(action: { 
                                    // 仅在非录制状态才跳转到编辑页面
                                    if !viewModel.isVideoRecording {
                                        // 确保相机会话保持活跃
                                        let sessionManager = SessionManager.shared
                                        if !sessionManager.session.isRunning {
                                            // 修复警告：在后台线程启动相机会话
                                            DispatchQueue.global(qos: .userInitiated).async {
                                                sessionManager.startRunning()
                                                
                                                // 在主线程展示共享Tab视图
                                                DispatchQueue.main.async {
                                                    sharedTabViewModel.show(initialTab: .watermark)
                                                }
                                            }
                                        } else {
                                            // 如果相机已经在运行，直接展示
                                            sharedTabViewModel.show(initialTab: .watermark)
                                        }
                                    } else {
                                        // 录制状态下的点击行为
                                        // 使用与toggleLockWithFeedback相同的模式提供触觉反馈
                                        viewModel.animationService.animateButtonToggle {
                                            // 可选的操作（如需添加）
                                        }
                                        // 触觉反馈
                                        HapticService.shared.mediumImpact()
                                    }
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(viewModel.isVideoRecording ? Color.white : Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                            .frame(width: screenHeight * UIConstants.previewSize, height: screenHeight * UIConstants.previewSize)
                                        
                                        if !viewModel.isVideoRecording {
                                            Image(systemName: "note")
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                                       height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                                .foregroundColor(.white)
                                                .rotationEffect(.degrees(180)) // 保留原有的180度旋转
                                                .transition(.opacity.animation(AnimationConstants.standardEaseInOut))
                                                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                        }
                                    }
                                    .animation(AnimationConstants.standardEaseInOut, value: viewModel.isVideoRecording)
                                }
                                .buttonStyle(BounceButtonStyle())

                                Spacer()

                                // 最右侧锁定按钮（原来是滤镜按钮的位置）
                                Button(action: {
                                    viewModel.toggleLockWithFeedback()
                                }) {
                                    Group {
                                        // 移除录制状态的判断，始终显示锁定图标
                                        Image(viewModel.isLocked ? "LH lock" : "LH lock-open")
                                            .renderingMode(.template)
                                            .resizable()
                                            .scaledToFit()
                                            .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                                                   height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                                            // 根据具体横屏方向旋转图标
                                            .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                                            // 添加旋转动画
                                            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                                    }
                                    .foregroundColor(.white)
                                    .contentTransition(.symbolEffect(.replace.byLayer, options: .nonRepeating))
                                    .frame(width: screenHeight * UIConstants.previewSize, height: screenHeight * UIConstants.previewSize)
                                    .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                                    .clipShape(Circle())
                                }
                                .simultaneousGesture(
                                    DragGesture(minimumDistance: 0)
                                        .onChanged { _ in
                                            viewModel.isLockPressed = true
                                        }
                                        .onEnded { _ in
                                            viewModel.isLockPressed = false
                                        }
                                )
                                .buttonStyle(BounceButtonStyle())
                                .opacity(viewModel.shouldShowFilterButton() ? 1 : 0)
                            }
                            .padding(.horizontal, UIScreen.main.bounds.width * UIConstants.horizontalPadding)
                        }
                    }
                    .padding(.bottom, screenHeight * 0.025)  // 底部内边距2.5%
                }
            }
        }
        // 移除offset修饰符
        // 保留设备方向监听和其他相关修饰符
        .onReceive(NotificationService.shared.publisher(for: UIDevice.orientationDidChangeNotification)) { _ in
            let orientation = UIDevice.current.orientation

            // 只处理有效的横竖屏状态
            if orientation.isPortrait || orientation.isLandscape {
                // 只更新 deviceOrientation，isLandscapeOrientation 會自動計算
                self.deviceOrientation = orientation
            }
        }
        .onAppear {
            // 获取初始方向
            let orientation = UIDevice.current.orientation
            if orientation.isPortrait || orientation.isLandscape {
                // 只更新 deviceOrientation，isLandscapeOrientation 會自動計算
                self.deviceOrientation = orientation
            }
        }
        .onDisappear {
            // 可选：停止方向通知
            // UIDevice.current.endGeneratingDeviceOrientationNotifications()
        }
    }
}

// 状态信息显示
struct StatusInfo: View {
    let time: String
    
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "hourglass")
            Text(time)
        }
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.white)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.black.opacity(0.5))
        .clipShape(Capsule())
    }
}

// 电池状态显示
struct BatteryView: View {
    var body: some View {
        HStack(spacing: 4) {
            Image(systemName: "battery.75")
            Text("75%")
        }
        .font(.system(size: 14, weight: .medium))
        .foregroundColor(.white)
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Color.black.opacity(0.5))
        .clipShape(Capsule())
    }
}

// 音频通道视图 (可重用子视图)
struct AudioChannelView: View {
    enum Mode { case display, mask }
    let mode: Mode
    let level: Float // 音频电平值 (0.0 - 1.0)
    // let channelLabel: String // 标签不再由此视图处理

    // 根据模式和电平计算填充颜色
    private func fillColor(for index: Int) -> Color {
        if mode == .mask {
            return .white // 遮罩模式固定白色
        } else {
            // 显示模式
            let levelHeight = Int(level * 16) // 转换为 0-16 的高度
            let shouldFill = (15 - index) < levelHeight // 判断当前格子是否在需要填充的高度内
            
            // --- 添加打印语句 --- 
            print("fillColor - Mode: \(mode), Level: \(level), Index: \(index), LevelHeight: \(levelHeight), ShouldFill: \(shouldFill)")
            // ---------------------
            
            if shouldFill { 
                // 根据 index 决定 绿/橙/红 (index 越大越靠下)
                if index >= 6 { return .green } // 底部 10 格 (index 6-15)
                else if index >= 2 { return .orange } // 中间 4 格 (index 2-5)
                else { return .red } // 顶部 2 格 (index 0-1)
            } else {
                return .clear // 高于电平则透明
            }
        }
    }

    var body: some View {
        VStack(spacing: 0) { // 移除VStack的spacing
            ForEach(0..<16, id: \.self) { index in
                Rectangle()
                    .fill(fillColor(for: index))
                    .frame(width: UIScreen.main.bounds.width * 0.0175, // 宽度1.75%屏幕宽度
                           height: UIScreen.main.bounds.height * 0.0025) // 高度0.25%屏幕高度
                if index < 15 {
                    Spacer()
                        .frame(height: UIScreen.main.bounds.height * 0.00125) // 间距0.125%屏幕高度
                }
            }
            // 不再包含 Spacer 和 Text，让调用者处理标签和底部对齐
        }
    }
}

// 音频示波器
struct AudioWaveformView: View {
    let screenHeight: CGFloat
    var leftLevel: Float
    var rightLevel: Float

    // 定义公共的布局常量
    private let hStackSpacing = UIScreen.main.bounds.width * 0.0075
    private let totalWidth = UIScreen.main.bounds.width * 0.0575
    private let topPadding = UIScreen.main.bounds.height * 0.005
    private let bottomPadding = UIScreen.main.bounds.height * 0.0025
    private let labelBottomSpacerHeight = UIScreen.main.bounds.height * 0.0025
    private let labelFontSize = UIScreen.main.bounds.height * 0.01

    var body: some View {
        // 1. 基础内容层 (彩色电平条 + L/R标签)
        let contentLayer = HStack(spacing: hStackSpacing) {
            VStack(spacing: 0) { // 左声道
                AudioChannelView(mode: .display, level: leftLevel)
                Spacer().frame(height: labelBottomSpacerHeight) // 标签与音频条的间距
                Text("L") // 左声道标签
                    .font(.system(size: labelFontSize, weight: .medium))
                    .foregroundColor(.white)
            }
            VStack(spacing: 0) { // 右声道
                AudioChannelView(mode: .display, level: rightLevel)
                Spacer().frame(height: labelBottomSpacerHeight)
                Text("R") // 右声道标签
                    .font(.system(size: labelFontSize, weight: .medium))
                    .foregroundColor(.white)
            }
        }
        .frame(width: totalWidth) // 固定宽度
        .padding(.top, topPadding)
        .padding(.bottom, bottomPadding)
        .animation(AnimationConstants.standardEaseInOut, value: leftLevel)
        .animation(AnimationConstants.standardEaseInOut, value: rightLevel)

        // 2. 应用带遮罩的背景
        let contentWithMaskedBackground = contentLayer
            .background {
                // 在 background 内部创建 ZStack 来组合背景和遮罩
                ZStack {
                    // 底层：半透明黑色背景
                    Color.black.opacity(UIConstants.topBarBackgroundOpacity)
                        .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius))

                    // 顶层：白色遮罩层
                    HStack(spacing: hStackSpacing) {
                        VStack(spacing: 0) { // 左声道遮罩
                            AudioChannelView(mode: .mask, level: 0)
                            Spacer().frame(height: labelBottomSpacerHeight)
                            Text("L").font(.system(size: labelFontSize, weight: .medium)).foregroundColor(.white)
                        }
                        VStack(spacing: 0) { // 右声道遮罩
                            AudioChannelView(mode: .mask, level: 0)
                            Spacer().frame(height: labelBottomSpacerHeight)
                            Text("R").font(.system(size: labelFontSize, weight: .medium)).foregroundColor(.white)
                        }
                    }
                     // --- 重要：确保遮罩层尺寸与 contentLayer 一致 --- 
                    .frame(width: totalWidth) 
                    .padding(.top, topPadding)
                    .padding(.bottom, bottomPadding)
                    // -------------------------------------------
                    .blendMode(.destinationOut) // 对遮罩层应用混合模式
                }
                .compositingGroup() // 隔离 ZStack 内部的混合效果
            }

        // 返回最终视图并应用 Compositing Group
        return contentWithMaskedBackground
            .compositingGroup()
    }
}

// 相机预览视图
struct CameraPreviewView: UIViewRepresentable {
    private let previewService: CameraPreviewServiceProtocol
    
    init() {
        self.previewService = ServiceFactory.createPreviewService()
    }
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView(frame: UIScreen.main.bounds)
        previewService.configurePreviewLayer(for: view, customFrame: nil)
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // 从上下文的事务中获取动画信息
        let transaction = context.transaction
        
        // 使用标准动画时长0.5秒，保持与SharedTabViewModel一致
        var animationDuration: TimeInterval = AnimationConstants.duration
        
        // 如果事务中包含动画，使用事务的动画参数
        if transaction.animation != nil {
            // 确保使用与SharedTabViewModel相同的动画参数
            animationDuration = AnimationConstants.duration
            
            // 更新共享动画参数，确保所有UIKit组件使用相同的动画参数
            AnimationUtils.sharedAnimationParameters["duration"] = animationDuration
            AnimationUtils.sharedAnimationParameters["curve"] = UIView.AnimationOptions.curveEaseInOut.rawValue
        }
        
        // 获取动画曲线
        let animationCurve = UIView.AnimationOptions.curveEaseInOut.rawValue
        
        // 使用支持动画的更新方法
        previewService.updatePreviewLayer(for: uiView, customFrame: nil, animationDuration: animationDuration, animationCurve: animationCurve)
    }
}

struct RecordingTimeView: View {
    @ObservedObject var viewModel: CameraViewModel
    
    var body: some View {
        // 使用新的计算属性
        Text(viewModel.formattedRecordingTime)
            .foregroundColor(.white)
            .font(.system(size: 14, weight: .medium))
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
            .cornerRadius(UIConstants.videoInfoCornerRadius)
    }
}

// Helper function to determine rotation angle based on orientation
private func rotationAngleForOrientation(_ orientation: UIDeviceOrientation) -> Angle {
    switch orientation {
    case .landscapeLeft:
        return .degrees(90) // Home button on right
    case .landscapeRight:
        return .degrees(-90) // Home button on left
    case .portrait:
        return .degrees(0)
    default: // Includes portraitUpsideDown, faceUp, faceDown, unknown
        return .degrees(0) // Default to no rotation for other states
    }
}

// Define a PreferenceKey to pass size up the view hierarchy
struct SizePreferenceKey: PreferenceKey {
    static var defaultValue: CGSize = .zero
    static func reduce(value: inout CGSize, nextValue: () -> CGSize) {
        value = nextValue() // Take the last value reported
    }
}

// 横屏录制信息视图
struct LandscapeRecordingInfoView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    let deviceOrientation: UIDeviceOrientation
    let screenWidth = UIScreen.main.bounds.width // 添加屏幕宽度属性

    // State variables to store measured sizes
    @State private var remainingTimeSize: CGSize = .zero
    @State private var recordingTimeSize: CGSize = .zero

    var body: some View {
        ZStack(alignment: .top) {
            // 中间：音频示波器
            AudioWaveformView(screenHeight: screenHeight,
                             leftLevel: 0.7,  // 使用测试值
                             rightLevel: 0.5) // 使用测试值
                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                .offset(y: calculateAudioWaveformOffset())
                .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)

            // 右侧：录制时间
            HStack {
                Spacer()
                // 使用新的计算属性
                Text(viewModel.formattedRecordingTime)
                    .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .medium))
                    .foregroundColor(.white)
                    .padding(.horizontal, UIScreen.main.bounds.width * 0.02) // 1. Padding
                    .frame(height: screenHeight * UIConstants.recordingTimeHeight) // 2. Frame
                    .background(withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) { // 3. Visual Background
                        viewModel.getRecordingBackgroundColor()
                    })
                    .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius)) // 4. Clip Shape
                    .background( // 5. Measurement Background (after frame)
                        GeometryReader { proxy in
                            Color.clear.preference(key: SizePreferenceKey.self, value: proxy.size)
                        }
                    )
                    .onPreferenceChange(SizePreferenceKey.self) { size in
                         if size.width > 0 && size.height > 0 {
                             DispatchQueue.main.async {
                                 self.recordingTimeSize = size
                             }
                         }
                     }
                    .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                    // Apply both dx and dy from the precise offset calculation
                    .offset(x: LayoutUtils.calculateRightAlignedOffsetForRotation(size: recordingTimeSize, orientation: deviceOrientation).width, 
                            y: LayoutUtils.calculateRightAlignedOffsetForRotation(size: recordingTimeSize, orientation: deviceOrientation).height)
                    .animation(.easeInOut(duration: 0.5), value: recordingTimeSize)
                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
            }
            .offset(y: screenHeight * UIConstants.recordingTimeHeight) // Keep outer vertical offset

            // 左侧：剩余空间显示
            HStack {
                HStack(spacing: 4) {
                    Image(systemName: "movieclapper.fill")
                        .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .ultraLight))
                        .foregroundColor(.white)
                    Text("2小时30分")
                        .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .medium))
                        .foregroundColor(.white)
                }
                .padding(.horizontal, UIScreen.main.bounds.width * 0.02) // 1. Padding
                .frame(height: UIScreen.main.bounds.height * 0.03) // 2. Frame
                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity)) // 3. Visual Background
                .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius)) // 4. Clip Shape
                 .background( // 5. Measurement Background (after frame)
                     GeometryReader { proxy in
                         Color.clear.preference(key: SizePreferenceKey.self, value: proxy.size)
                     }
                 )
                 .onPreferenceChange(SizePreferenceKey.self) { size in
                      if size.width > 0 && size.height > 0 {
                          DispatchQueue.main.async {
                              self.remainingTimeSize = size
                          }
                      }
                  }
                .rotationEffect(OrientationUtils.rotationAngleForOrientation(deviceOrientation))
                 // Apply both dx and dy from the precise offset calculation
                 .offset(x: LayoutUtils.calculateLeftAlignedOffsetForRotation(size: remainingTimeSize, orientation: deviceOrientation).width, 
                         y: LayoutUtils.calculateLeftAlignedOffsetForRotation(size: remainingTimeSize, orientation: deviceOrientation).height)
                 .animation(.easeInOut(duration: 0.5), value: remainingTimeSize)
                 .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                Spacer()
            }
            .offset(y: screenHeight * UIConstants.recordingTimeHeight) // Keep outer vertical offset
        }
        .padding(.horizontal, UIScreen.main.bounds.width * UIConstants.horizontalPadding)
        .padding(.top, UIScreen.main.bounds.height * UIConstants.videoInfoTopSpacing)
    }

    // calculateAudioWaveformOffset() remains the same
    private func calculateAudioWaveformOffset() -> CGFloat {
        return LayoutUtils.calculateAudioWaveformOffset(screenHeight: screenHeight, screenWidth: screenWidth, isLandscape: isLandscape)
    }
    
    // Add isLandscape computed property
    private var isLandscape: Bool {
        return OrientationUtils.isLandscape(deviceOrientation)
    }
}

// 竖屏录制信息视图 - 重构布局
struct PortraitRecordingInfoView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    let deviceOrientation: UIDeviceOrientation
    let screenWidth = UIScreen.main.bounds.width // Needed for padding calculations

    var body: some View {
        // Define constants for clarity and calculation
        let horizontalPaddingValue = screenWidth * UIConstants.horizontalPadding // 3% horizontal padding
        let topPaddingValue = screenHeight * UIConstants.videoInfoTopSpacing // 1.25% top spacing
        let recordingTimeHeightValue = screenHeight * UIConstants.recordingTimeHeight // 2.5% height

        // Total height calculation for container
        let remainingSpaceViewHeight = screenHeight * 0.03 // Matches frame height used below
        let audioWaveformApproxHeight = screenHeight * 0.07875
        let lowerElementsMaxHeight = max(remainingSpaceViewHeight, audioWaveformApproxHeight)
        let containerHeight = recordingTimeHeightValue + lowerElementsMaxHeight + topPaddingValue + 10 // Include top padding in total height

        // Main container - does NOT use padding at this level
        ZStack(alignment: .top) {
            // Empty container with fixed height
            Color.clear
                .frame(maxWidth: .infinity)
                .frame(height: containerHeight)
            
            // 1. Recording Time - positioned with exact 1.25% top margin
            Text(viewModel.formattedRecordingTime)
                .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .medium))
                .foregroundColor(.white)
                .frame(height: recordingTimeHeightValue)
                .padding(.horizontal, screenWidth * 0.02) // Internal padding
                .background(withAnimation(.spring(response: AnimationConstants.quickDuration, 
                                                dampingFraction: AnimationConstants.quickDampingFraction)) {
                    viewModel.getRecordingBackgroundColor()
                })
                .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius))
                .frame(maxWidth: .infinity, alignment: .center) // Center horizontally
                .offset(y: topPaddingValue) // Exact 1.25% top margin
            
            // 2. Audio Waveform - at top-right with proper margins
            HStack {
                Spacer() // Push to right
                AudioWaveformView(screenHeight: screenHeight,
                                 leftLevel: 0.7,  // 使用测试值
                                 rightLevel: 0.5) // 使用测试值
            }
            .offset(y: topPaddingValue + recordingTimeHeightValue) // Position below recording time
            
            // 3. Remaining Space - at top-left with proper margins (无旋转效果)
            HStack {
                // The actual view for remaining space
                HStack(spacing: 4) {
                    Image(systemName: "movieclapper.fill")
                        .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .ultraLight))
                        .foregroundColor(.white)
                    Text("2小时30分")
                        .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .medium))
                        .foregroundColor(.white)
                }
                .frame(height: remainingSpaceViewHeight) // Set frame height
                .padding(.horizontal, screenWidth * 0.02) // Internal padding
                .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius))
                
                Spacer() // Push to left
            }
            .offset(y: topPaddingValue + recordingTimeHeightValue) // Position below recording time
        }
        .padding(.horizontal, horizontalPaddingValue) // Apply 3% horizontal padding to entire container
    }
}

// 通用的录制标签组件
struct RecordingLabel: View {
    let text: String
    let textColor: Color
    let backgroundColor: Color
    let fontSize: CGFloat
    let horizontalPadding: CGFloat
    let verticalPadding: CGFloat?
    let height: CGFloat?
    
    init(
        text: String,
        textColor: Color = .white,
        backgroundColor: Color = Color.black.opacity(UIConstants.topBarBackgroundOpacity),
        fontSize: CGFloat? = nil,
        horizontalPadding: CGFloat? = nil,
        verticalPadding: CGFloat? = nil,
        height: CGFloat? = nil
    ) {
        self.text = text
        self.textColor = textColor
        self.backgroundColor = backgroundColor
        self.fontSize = fontSize ?? (UIScreen.main.bounds.height * UIConstants.parameterFontSize)
        self.horizontalPadding = horizontalPadding ?? (UIScreen.main.bounds.width * 0.02)
        self.verticalPadding = verticalPadding
        self.height = height
    }
    
    var body: some View {
        Text(text)
            .font(.custom("SF Mono", size: fontSize))
            .foregroundColor(textColor)
            .padding(.horizontal, horizontalPadding)
            .if(verticalPadding != nil) { view in
                view.padding(.vertical, verticalPadding!)
            }
            .if(height != nil) { view in
                view.frame(height: height!)
            }
            .background(backgroundColor)
            .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius))
    }
}

// 添加if修饰器扩展
extension View {
    @ViewBuilder
    func `if`<Transform: View>(_ condition: Bool, transform: (Self) -> Transform) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// 统一的录制信息视图 - 同时支持横屏和竖屏
struct RecordingInfoView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    let deviceOrientation: UIDeviceOrientation
    let screenWidth = UIScreen.main.bounds.width
    
    // 测量视图大小的状态变量
    @State private var remainingTimeSize: CGSize = .zero
    @State private var recordingTimeSize: CGSize = .zero
    
    // 判断是否为横屏
    private var isLandscape: Bool {
        return OrientationUtils.isLandscape(deviceOrientation)
    }
    
    // 音频波形偏移量计算
    private func calculateAudioWaveformOffset() -> CGFloat {
        return LayoutUtils.calculateAudioWaveformOffset(screenHeight: screenHeight, screenWidth: screenWidth, isLandscape: isLandscape)
    }
    
    // 精确的剩余时间偏移量计算(横屏模式使用)
    private func getPreciseRemainingTimeOffset() -> CGSize {
        guard remainingTimeSize != .zero else { return .zero }
        let H = remainingTimeSize.height
        let W = remainingTimeSize.width
        var dx: CGFloat = 0
        var dy: CGFloat = 0
        
        if isLandscape {
            // 横屏时的偏移量计算
            dx = (H - W) / 2
            dy = (W - H) / 2
        }
        return CGSize(width: dx, height: dy)
    }
    
    // 精确的录制时间偏移量计算(横屏模式使用)
    private func getPreciseRecordingTimeOffset() -> CGSize {
        guard recordingTimeSize != .zero else { return .zero }
        let H = recordingTimeSize.height
        let W = recordingTimeSize.width
        var dx: CGFloat = 0
        var dy: CGFloat = 0
        
        if isLandscape {
            // 横屏时的偏移量计算
            dx = -(H - W) / 2
            dy = (W - H) / 2
        }
        return CGSize(width: dx, height: dy)
    }
    
    // 顶部内边距
    private var topPaddingValue: CGFloat {
        return screenHeight * UIConstants.videoInfoTopSpacing // 1.25% top spacing
    }
    
    // 录制时间高度
    private var recordingTimeHeightValue: CGFloat {
        return screenHeight * UIConstants.recordingTimeHeight // 2.5% height
    }
    
    var body: some View {
        let horizontalPaddingValue = screenWidth * UIConstants.horizontalPadding
        
        ZStack(alignment: .top) {
            // 音频波形 - 视图一致性实现
            AudioWaveformView(screenHeight: screenHeight, leftLevel: 0.7, rightLevel: 0.5)
                .rotationEffect(isLandscape ? OrientationUtils.rotationAngleForOrientation(deviceOrientation) : .zero)
                .offset(
                    x: 0,
                    y: isLandscape ? calculateAudioWaveformOffset() : (topPaddingValue + recordingTimeHeightValue)
                )
                .frame(maxWidth: .infinity, alignment: isLandscape ? .center : .trailing)
            
            // 录制时间 - 横屏版本
            VStack(spacing: screenHeight * 0.005) {
                // 使用通用RecordingLabel组件显示录制时间
                RecordingLabel(
                    text: viewModel.formattedRecordingTime,
                    backgroundColor: viewModel.getRecordingBackgroundColor(),
                    height: recordingTimeHeightValue
                )
                .background(
                    GeometryReader { proxy in
                        Color.clear.preference(key: SizePreferenceKey.self, value: proxy.size)
                    }
                )
                .onPreferenceChange(SizePreferenceKey.self) { size in
                    if size.width > 0 && size.height > 0 {
                        DispatchQueue.main.async {
                            self.recordingTimeSize = size
                        }
                    }
                }
                
                // 使用通用RecordingLabel组件显示暂停状态
                if viewModel.isRecordingPaused {
                    RecordingLabel(
                        text: "已暂停",
                        textColor: .white,
                        backgroundColor: UIConstants.recordingColor,
                        fontSize: screenHeight * UIConstants.pausedLabelFontSize,
                        horizontalPadding: screenWidth * 0.01,
                        height: recordingTimeHeightValue * 0.8
                    )
                    .transition(.opacity.animation(.easeInOut(duration: 0.25)))
                }
            }
            .rotationEffect(isLandscape ? OrientationUtils.rotationAngleForOrientation(deviceOrientation) : .zero)
            .offset(
                x: isLandscape ? getPreciseRecordingTimeOffset().width + (viewModel.isRecordingPaused ? getLandscapePausedExtraOffset().width : 0) : 0,
                y: isLandscape ? 
                    (screenHeight * UIConstants.recordingTimeHeight + getPreciseRecordingTimeOffset().height + (viewModel.isRecordingPaused ? getLandscapePausedExtraOffset().height : 0))
                    : topPaddingValue
            )
            .frame(maxWidth: .infinity, alignment: isLandscape ? .trailing : .center)
            .animation(.easeInOut(duration: 0.5), value: recordingTimeSize)
            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
            .animation(.easeInOut(duration: 0.25), value: viewModel.isRecordingPaused)
            
            // 剩余空间显示 - 视图一致性实现
            HStack(spacing: 4) {
                Image(systemName: "movieclapper.fill")
                    .font(.system(size: screenHeight * UIConstants.parameterFontSize, weight: .ultraLight))
                    .foregroundColor(.white)
                Text("2小时30分")
                    .font(.custom("SF Mono", size: screenHeight * UIConstants.parameterFontSize))
                    .foregroundColor(.white)
            }
            .padding(.horizontal, screenWidth * 0.02)
            .frame(height: screenHeight * 0.03)
            .background(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
            .clipShape(RoundedRectangle(cornerRadius: UIConstants.videoInfoCornerRadius))
            .background(
                GeometryReader { proxy in
                    Color.clear.preference(key: SizePreferenceKey.self, value: proxy.size)
                }
            )
            .onPreferenceChange(SizePreferenceKey.self) { size in
                if size.width > 0 && size.height > 0 {
                    DispatchQueue.main.async {
                        self.remainingTimeSize = size
                    }
                }
            }
            .rotationEffect(isLandscape ? OrientationUtils.rotationAngleForOrientation(deviceOrientation) : .zero)
            .offset(
                x: isLandscape ? getPreciseRemainingTimeOffset().width : 0,
                y: isLandscape ? (screenHeight * UIConstants.recordingTimeHeight + getPreciseRemainingTimeOffset().height) : (topPaddingValue + recordingTimeHeightValue)
            )
            .frame(maxWidth: .infinity, alignment: .leading)
            .animation(.easeInOut(duration: 0.5), value: remainingTimeSize)
            .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
        }
        .padding(.horizontal, horizontalPaddingValue)
        .padding(.top, isLandscape ? (screenHeight * UIConstants.videoInfoTopSpacing) : 0)
        .animation(.easeInOut(duration: 0.5), value: isLandscape)
    }
    
    // 新增：计算横屏暂停状态下的额外水平偏移量
    private func getLandscapePausedExtraOffset() -> CGSize {
        guard isLandscape && viewModel.isRecordingPaused else {
            return .zero // 仅在横屏且暂停时需要计算
        }
        
        // 计算VStack高度差 = 间距 + 已暂停标签高度
        let pauseLabelHeight = recordingTimeHeightValue * 0.8
        let spacing = screenHeight * 0.005
        let heightDifference = spacing + pauseLabelHeight
        
        var extraOffsetX: CGFloat = -heightDifference / 2 // 水平方向两个横屏都使用负偏移
        var extraOffsetY: CGFloat = 0 // 垂直方向偏移量
        
        // 计算垂直偏移量，抵消VStack高度增加的一半
        // 由于VStack变高，视觉中心下移，所以需要往上移动一部分
        if deviceOrientation == .landscapeRight || deviceOrientation == .landscapeLeft {
            extraOffsetY = -heightDifference / 2
        }
        
        return CGSize(width: extraOffsetX, height: extraOffsetY) // 返回水平和垂直偏移
    }
}

