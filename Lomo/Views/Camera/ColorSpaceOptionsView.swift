import SwiftUI

struct ColorSpaceOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 色彩空间选项数据
    private let colorSpaceOptions: [(title: String, value: ColorSpaceMode)] = [
        ("SDR", .sdr),           // 标准动态范围 (Rec.709)
        ("HLG", .hlg),           // 混合对数伽马
        ("Dolby Vision", .dolbyVision),   // 杜比视界
        ("Log", .appleLog)       // Apple ProRes Log
    ]
    
    // Pro色彩空间选项
    private let proColorSpaceOptions: [ColorSpaceMode] = [
        .dolbyVision,   // 杜比视界为Pro选项
        .appleLog       // Log为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: colorSpaceOptions,
            selectedValue: viewModel.state.colorSpaceMode,
            onSelect: viewModel.selectColorSpaceMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isColorSpaceOptionsVisible,
            proOptions: proColorSpaceOptions,
            position: .top,
            isProUser: viewModel.isProUser  // 传递Pro用户状态
        )
    }
} 