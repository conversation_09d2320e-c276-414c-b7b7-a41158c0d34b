import SwiftUI

// 翻转模式选项视图
struct FlipOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 翻转模式选项配置
    private let flipOptions: [(title: String, value: FlipMode)] = [
        ("关闭", .off),
        ("水平", .horizontal),
        ("垂直", .vertical),
        ("双向", .both)
    ]
    
    var body: some View {
        OptionGroup(
            options: flipOptions,
            selectedValue: viewModel.state.flipMode,
            onSelect: viewModel.selectFlipMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isFlipOptionsVisible,
            position: .bottom
        )
    }
} 