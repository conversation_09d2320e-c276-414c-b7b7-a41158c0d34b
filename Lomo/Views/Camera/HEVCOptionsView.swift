import SwiftUI

/// 视频编码选项视图
/// 用于选择不同的视频编码格式，包括HEVC、H.264和各种ProRes格式
struct VideoEncodingOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    /// 编码格式选项列表
    /// 包含所有支持的视频编码格式及其对应的模式
    private let encodingOptions: [(title: String, value: VideoEncodingMode)] = [
        ("HEVC", .hevc),
        ("H.264", .h264),
        ("ProRes 422 Proxy", .proResProxy),
        ("ProRes 422 LT", .proResLT),
        ("ProRes 422", .proRes),
        ("ProRes 422 HQ", .proResHQ)
    ]
    
    // Pro编码选项
    private let proEncodingOptions: [VideoEncodingMode] = [
        .proResProxy,    // ProRes 422 Proxy为Pro选项
        .proResLT,       // ProRes 422 LT为Pro选项
        .proRes,         // ProRes 422为Pro选项
        .proResHQ        // ProRes 422 HQ为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: encodingOptions,
            selectedValue: viewModel.state.videoEncodingMode,
            onSelect: viewModel.selectVideoEncodingMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isVideoEncodingOptionsVisible,
            proOptions: proEncodingOptions,
            position: .top,
            isProUser: viewModel.isProUser
        )
    }
} 