import SwiftUI

struct FrameRateOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 帧率选项数据
    private let frameRateOptions: [(title: String, value: FrameRateMode)] = [
        ("30fps", .fps30),     // 标准帧率
        ("60fps", .fps60),     // 流畅帧率
        ("25fps", .fps25),     // PAL 制式标准
        ("50fps", .fps50),     // PAL 制式高帧率
        ("24fps", .fps24),     // 电影帧率
        ("48fps", .fps48),     // 高帧率电影
        ("120fps", .fps120),   // 慢动作
        ("240fps", .fps240)    // 超慢动作
    ]
    
    // Pro帧率选项
    private let proFrameRateOptions: [FrameRateMode] = [
        .fps24,     // 24fps为Pro选项
        .fps48,     // 48fps为Pro选项
        .fps50,     // 50fps为Pro选项
        .fps60,     // 60fps为Pro选项
        .fps120,    // 120fps为Pro选项
        .fps240     // 240fps为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: frameRateOptions,
            selectedValue: viewModel.state.frameRateMode,
            onSelect: viewModel.selectFrameRateMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isFrameRateOptionsVisible,
            proOptions: proFrameRateOptions,
            position: .top,
            isProUser: viewModel.isProUser
        )
    }
} 