import SwiftUI

struct PhotoFormatOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 照片格式选项数据
    private let photoFormatOptions: [(title: String, value: PhotoFormatMode)] = [
        ("JPG", .jpg),           // JPEG/JPG 通用格式
        ("HEIF", .heif),         // 高效图像文件格式
        ("PNG", .png),           // PNG 无损格式
        ("TIFF", .tiff),         // TIFF 专业格式
        ("RAW", .raw),           // RAW 原始格式
        ("ProRAW", .proRaw)      // Apple ProRAW 格式
    ]
    
    // Pro照片格式选项
    private let proPhotoFormatOptions: [PhotoFormatMode] = [
        .tiff,      // TIFF为Pro选项
        .raw,       // RAW为Pro选项
        .proRaw     // ProRAW为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: photoFormatOptions,
            selectedValue: viewModel.state.photoFormatMode,
            onSelect: viewModel.selectPhotoFormatMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isPhotoFormatOptionsVisible,
            proOptions: proPhotoFormatOptions,
            position: .top,  // 设置为顶部位置
            isProUser: viewModel.isProUser  // 传递Pro用户状态
        )
    }
} 