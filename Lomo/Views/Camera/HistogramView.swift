import SwiftUI

/// 统一的直方图视图，包含条件显示和绘制逻辑
struct HistogramView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    let screenWidth = UIScreen.main.bounds.width
    let alwaysVisible: Bool // 新增参数，控制是否无视 isHistogramEnabled
    let deviceOrientation: UIDeviceOrientation // 新增设备方向参数

    // 添加初始化器，让 alwaysVisible 可以不传（默认为 false）
    init(viewModel: CameraViewModel, screenHeight: CGFloat, alwaysVisible: Bool = false, deviceOrientation: UIDeviceOrientation = .portrait) {
        self.viewModel = viewModel
        self.screenHeight = screenHeight
        self.alwaysVisible = alwaysVisible
        self.deviceOrientation = deviceOrientation
    }

    var body: some View {
        // 只有在需要显示时才构建视图内容
        if alwaysVisible || viewModel.isHistogramEnabled {
            // 将绘制和布局代码整合到这里
            HStack { // 改用 HStack 并靠左对齐
                // 实际绘制直方图内容的逻辑
                Rectangle()
                    .fill(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
                    .overlay(
                        Group {
                            // 确保 viewModel.getHistogramData() 返回的是用于绘制的数据
                            if let data = viewModel.getHistogramData() { 
                                GeometryReader { geometry in
                                    Path { path in
                                        let width = geometry.size.width
                                        let height = geometry.size.height
                                        // 确保bin的数量不超过数据长度或256
                                        let binCount = min(data.count, 256)
                                        if binCount == 0 { return } // 如果没有数据，不绘制
                                        let binWidth = width / CGFloat(binCount)
                                        
                                        // 找到最大值以便归一化
                                        let maxValue = data.max() ?? 1
                                        if maxValue == 0 { return } // 避免除以零
                                        
                                        for index in 0..<binCount {
                                            let value = data[index]
                                            let x = CGFloat(index) * binWidth
                                            let normalizedValue = CGFloat(value) / CGFloat(maxValue)
                                            let y = height * (1 - normalizedValue)
                                            
                                            if index == 0 {
                                                path.move(to: CGPoint(x: x, y: height))
                                                path.addLine(to: CGPoint(x: x, y: y))
                                            } else {
                                                path.addLine(to: CGPoint(x: x, y: y))
                                            }
                                        }
                                        
                                        // 闭合路径
                                        let lastX = CGFloat(binCount - 1) * binWidth
                                        path.addLine(to: CGPoint(x: lastX, y: height))
                                        path.closeSubpath()
                                    }
                                    .fill(LinearGradient(
                                        gradient: Gradient(colors: [Color.blue.opacity(0.5), Color.purple.opacity(UIConstants.effectOpacity)]),
                                        startPoint: .top,
                                        endPoint: .bottom
                                    ))
                                }
                            } else {
                                Text("无数据") // 简化提示
                                    .font(.system(size: 10))
                                    .foregroundColor(.white)
                            }
                        }
                    )
                    .clipShape(RoundedRectangle(cornerRadius: UIConstants.optionButtonCornerRadius))
                    .frame(width: screenWidth * 0.12, height: screenHeight * 0.04)
                    .rotationEffect(rotationAngleForOrientation(deviceOrientation))
                    .offset(LayoutUtils.calculateHistogramOffsetForRotation(screenHeight: screenHeight, screenWidth: screenWidth, orientation: deviceOrientation))
                    .animation(AnimationService.standardRotationAnimation, value: deviceOrientation)
                
                Spacer() // 添加 Spacer 让直方图靠左
            }
        }
        // 如果不满足条件，这里什么也不返回，视图自然就不显示了
    }
}

// 添加旋转角度计算函数
private func rotationAngleForOrientation(_ orientation: UIDeviceOrientation) -> Angle {
    switch orientation {
    case .landscapeLeft:
        return .degrees(90) // Home button on right
    case .landscapeRight:
        return .degrees(-90) // Home button on left
    case .portrait:
        return .degrees(0)
    default: // Includes portraitUpsideDown, faceUp, faceDown, unknown
        return .degrees(0) // Default to no rotation for other states
    }
} 