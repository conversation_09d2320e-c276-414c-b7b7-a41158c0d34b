import SwiftUI

struct PhotoRatioOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 照片比例选项数据
    private let photoRatioOptions: [(title: String, value: PhotoRatioMode)] = [
        ("16:9", .ratio169),       // 宽屏比例
        ("3:2", .ratio32),         // 专业相机比例
        ("4:3", .ratio43),         // 标准比例
        ("1:1", .ratio11),         // 方形比例
        ("6:7", .ratio67),         // 竖版比例
        ("2:1", .ratio21),         // 宽幅比例
        ("1.85:1", .ratio185),     // 电影比例
        ("2.39:1", .ratio239),     // 影院比例
        ("1.66:1", .ratio166),     // 欧洲宽银幕
        ("XPAN", .ratioXPAN)       // 哈苏XPAN
    ]
    
    var body: some View {
        OptionGroup(
            options: photoRatioOptions,
            selectedValue: viewModel.state.photoRatioMode,
            onSelect: viewModel.selectPhotoRatioMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isPhotoRatioOptionsVisible,
            position: .top  // 设置为顶部位置
        )
    }
} 