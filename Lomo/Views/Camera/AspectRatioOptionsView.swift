import SwiftUI

struct AspectRatioOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 宽高比选项数据
    private let aspectRatioOptions: [(title: String, value: AspectRatioMode)] = [
        ("16:9", .ratio169),
        ("4:3", .ratio43),
        ("1:1", .ratio11),
        ("1.85:1", .ratio185),
        ("2:1", .ratio20),
        ("2.39:1", .ratio239),
        ("1.66:1", .ratio166)
    ]
    
    var body: some View {
        OptionGroup(
            options: aspectRatioOptions,
            selectedValue: viewModel.state.aspectRatioMode,
            onSelect: viewModel.selectAspectRatioMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isAspectRatioOptionsVisible,
            position: .top  // 设置为顶部位置
        )
    }
} 