import SwiftUI

/// 时间戳视图组件，显示当前时间的视图叠加层
struct TimestampView: View {
    /// 选中的时间戳样式
    var style: String
    
    /// 选中的时间戳颜色
    var colorName: String
    
    /// 时间戳的尺寸
    var size: CGFloat
    
    /// 控制月、日、年之间的间距
    var characterSpacing: CGFloat
    
    /// 控制引号和年份之间的间距
    var quoteYearSpacing: CGFloat
    
    /// 初始化时间戳视图
    /// - Parameters:
    ///   - style: 时间戳样式
    ///   - colorName: 时间戳颜色名称
    ///   - size: 字体大小
    ///   - characterSpacing: 字符间距，默认为 2
    ///   - quoteYearSpacing: 引号和年之间的间距，默认与 characterSpacing 相同
    init(style: String, colorName: String = "orange", size: CGFloat = 24, characterSpacing: CGFloat = 2, quoteYearSpacing: CGFloat? = nil) {
        self.style = style
        self.colorName = colorName
        self.size = size
        self.characterSpacing = characterSpacing
        self.quoteYearSpacing = quoteYearSpacing ?? characterSpacing // 如果未指定，使用与 characterSpacing 相同的值
    }
    
    var body: some View {
        // 获取格式化的日期组件
        let components = FontUtils.getFormattedDateComponents()
        
        // 使用嵌套 HStack 以允许不同的间距
        HStack(spacing: characterSpacing) {
            Text(components.month)
            Text(components.day)
            
            // 引号和年份的嵌套 HStack，使用单独的间距
            HStack(spacing: quoteYearSpacing) {
                Text("'")
                Text(components.year)
            }
            .padding(.leading, 0) // 移除引号年份组合前的额外内边距
        }
        .font(getFontForStyle()) // 应用字体到整个 HStack
        .foregroundColor(FontUtils.getColorByName(colorName)) // 应用颜色
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(Color.black.opacity(UIConstants.topBarBackgroundOpacity))
        )
    }
    
    /// 获取与样式匹配的字体
    private func getFontForStyle() -> Font {
        switch style {
        case "digital_orange":
            return Font.custom(FontUtils.lcdDotFontName, size: size)
        case "digital_red":
            return Font.custom(FontUtils.digital7FontName, size: size)
        case "digital_cyber":
            return Font.custom(FontUtils.cyberFontName, size: size)
        default:
            return Font.system(size: size, weight: .medium)
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        // 示例：分别设置不同的字符间距和引号年份间距
        TimestampView(style: "digital_orange", colorName: "orange", size: 24, characterSpacing: 10, quoteYearSpacing: 4)
        TimestampView(style: "digital_red", colorName: "red", size: 24, characterSpacing: 4, quoteYearSpacing: 0)
        TimestampView(style: "digital_cyber", colorName: "blue", size: 24, characterSpacing: 10, quoteYearSpacing: 4)
    }
    .padding()
    .background(.black)
} 