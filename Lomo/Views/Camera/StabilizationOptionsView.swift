import SwiftUI

// 防抖模式选项视图
struct StabilizationOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 防抖模式选项配置
    private let stabilizationOptions: [(title: String, value: StabilizationMode)] = [
        ("关闭", .off),
        ("标准", .standard),
        ("专业", .pro),
        ("影院", .cinematic)
    ]
    
    // Pro防抖选项
    private let proStabilizationOptions: [StabilizationMode] = [
        .cinematic  // 影院级防抖为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: stabilizationOptions,
            selectedValue: viewModel.state.stabilizationMode,
            onSelect: viewModel.selectStabilizationMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isStabilizationOptionsVisible,
            proOptions: proStabilizationOptions,
            position: .bottom,
            isProUser: viewModel.isProUser  // 传递Pro用户状态
        )
    }
} 