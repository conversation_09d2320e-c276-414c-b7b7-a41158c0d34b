import Foundation
import SwiftUI

/// 曝光刻度段结构
struct ExposureSegment {
    let startValue: Double    // 起始曝光值
    let endValue: Double      // 结束曝光值
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// 曝光刻度盘工具类
enum ExposureDialUtils {
    /// 曝光角度分配算法
    static func calculateAngleDistribution() -> [ExposureSegment] {
        var segments: [ExposureSegment] = []
        
        // 配置参数
        let startAngleRange = 45.0   // 起始角度
        let endAngleRange = 135.0    // 结束角度
        let totalRange = endAngleRange - startAngleRange  // 总角度范围90度
        let centerAngle = 90.0  // 中心角度（0 EV位置）
        let mainTickValues = [3.0, 2.0, 1.0, 0.0, -1.0, -2.0, -3.0]  // 主刻度值，从3到-3
        let subTickStep = 0.3  // 副刻度步进值
        
        // 计算每个EV对应的角度范围
        let anglePerEV = totalRange / 6.0  // 6是从3到-3的范围，每EV 15度
        
        // 创建刻度段
        for i in 0..<(mainTickValues.count - 1) {
            let startValue = mainTickValues[i]
            let endValue = mainTickValues[i + 1]
            
            // 计算角度 - 线性映射从[3,-3]到[45,135]
            let startAngle = centerAngle - (startValue * anglePerEV)  // 注意这里改为减号，因为值的顺序反了
            let endAngle = centerAngle - (endValue * anglePerEV)
            
            // 计算副刻度
            var subTickAngles: [Double] = []
            let subTickCount = Int(abs(endValue - startValue) / subTickStep) - 1  // 使用abs确保正确的副刻度数量
            if subTickCount > 0 {
                let angleStep = (endAngle - startAngle) / Double(subTickCount + 1)
                for j in 1...subTickCount {
                    let tickAngle = startAngle + angleStep * Double(j)
                    subTickAngles.append(tickAngle)
                }
            }
            
            // 创建段
            segments.append(ExposureSegment(
                startValue: startValue,
                endValue: endValue,
                startAngle: startAngle,
                endAngle: endAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前曝光值
    static func calculateValue(angle: Double) -> Double {
        let segments = calculateAngleDistribution()
        
        // 当前角度（考虑旋转）
        let currentAngle = 90 + angle
        
        // 遍历每个段找到对应的曝光值
        for segment in segments {
            if currentAngle >= segment.startAngle && currentAngle <= segment.endAngle {
                // 计算在这个区间内的插值
                let progress = (currentAngle - segment.startAngle) / (segment.endAngle - segment.startAngle)
                let value = segment.startValue + (segment.endValue - segment.startValue) * progress
                return value
            }
        }
        
        // 如果超出范围，限制在-3到3之间
        if currentAngle > 90 {
            return 3.0  // 改为3.0，因为现在大角度对应正值
        } else {
            return -3.0  // 改为-3.0，因为现在小角度对应负值
        }
    }
    
    /// 格式化曝光值
    static func formatValue(_ value: Double, forMainTick: Bool = false) -> String {
        // 如果是0，不显示正负号
        if abs(value) < 0.05 {
            return "0"
        }
        // 如果是整数，不显示小数
        if abs(value - round(value)) < 0.05 {
            return String(format: "%+.0f", round(value))
        }
        // 其他值显示正负号和一位小数
        return String(format: "%+.1f", value)
    }
} 