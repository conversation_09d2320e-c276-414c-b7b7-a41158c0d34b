import Foundation
import SwiftUI
import AVFoundation

/// 焦点刻度盘配置
struct FocusDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前焦距值（米）
    let currentValue: Double
    
    /// 相机设备
    let device: AVCaptureDevice?
    
    // MARK: - Initialization
    
    /// 初始化焦点刻度盘配置
    /// - Parameters:
    ///   - currentValue: 当前焦距值，默认1米
    ///   - device: 相机设备，用于获取支持的焦距范围
    init(currentValue: Double = 1.0, device: AVCaptureDevice? = nil) {
        self.currentValue = currentValue
        self.device = device
    }
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .focus }
    
    /// 最小焦距值（米）
    var minValue: Double {
        let range = FocusDialUtils.getDeviceSupportedFocusRange(device: device)
        return range.min
    }
    
    /// 最大焦距值（米）
    var maxValue: Double {
        let range = FocusDialUtils.getDeviceSupportedFocusRange(device: device)
        return range.max
    }
    
    /// 步进值
    var step: Double { 0.1 }
    
    /// 单位显示
    var unit: String { "m" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { true }
    
    /// 主刻度数量
    var mainTickCount: Int {
        let segments = FocusDialUtils.calculateFocusDistribution(minDistance: minValue, maxDistance: maxValue)
        return segments.count + 1 // 段数 + 1 = 刻度数
    }
    
    /// 副刻度数量，每个主刻度之间4个副刻度
    var subTickCount: Int { 4 }
    
    /// 焦点不需要镜头信息
    var lenses: [String] { [] }
    
    /// 焦点不需要显示焦距
    var dialLenses: [String] { [] }
    
    // MARK: - Methods
    
    /// 格式化显示值
    /// - Parameter value: 需要格式化的值（米）
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        FocusDialUtils.formatFocusValue(value)
    }
} 