import Foundation
import SwiftUI
/// 色温刻度段结构
struct TemperatureSegment {
    let startValue: Double    // 起始色温值
    let endValue: Double      // 结束色温值
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// 色温刻度盘工具类
enum TemperatureDialUtils {
    /// 计算色温刻度分布
    /// - Returns: 色温刻度段数组
    static func calculateTemperatureDistribution() -> [TemperatureSegment] {
        var segments: [TemperatureSegment] = []
        
        // 配置参数
        let centerTemp = 5600.0  // 中心点温度
        let minTemp = 2000.0     // 最小温度
        let maxTemp = 10000.0    // 最大温度
        let leftAngle = 81.0     // 左侧最大角度
        let rightAngle = 99.0    // 右侧最大角度
        let subTickStep = 100.0  // 副刻度步进值（100K）
        
        // 主刻度值
        let mainTickValues = [2000.0, 3000.0, 4000.0, 5000.0, 5600.0, 6000.0, 7000.0, 8000.0, 9000.0, 10000.0]
        
        // 创建刻度段
        for i in 0..<(mainTickValues.count - 1) {
            let startValue = mainTickValues[i]
            let endValue = mainTickValues[i + 1]
            
            // 计算角度
            let startAngle: Double
            let endAngle: Double
            
            if startValue <= centerTemp {
                // 左侧区间 (2000K-5600K)
                let leftProgress = (centerTemp - startValue) / (centerTemp - minTemp)
                startAngle = leftAngle * leftProgress
                
                if endValue <= centerTemp {
                    let endProgress = (centerTemp - endValue) / (centerTemp - minTemp)
                    endAngle = leftAngle * endProgress
                } else {
                    endAngle = 0 // 中心点
                }
            } else {
                // 右侧区间 (5600K-10000K)
                let startProgress = (startValue - centerTemp) / (maxTemp - centerTemp)
                startAngle = -rightAngle * startProgress
                
                let endProgress = (endValue - centerTemp) / (maxTemp - centerTemp)
                endAngle = -rightAngle * endProgress
            }
            
            // 计算副刻度
            var subTickAngles: [Double] = []
            let subTickCount = Int((endValue - startValue) / subTickStep) - 1
            if subTickCount > 0 {
                let angleStep = (endAngle - startAngle) / Double(subTickCount + 1)
                for j in 1...subTickCount {
                    let tickAngle = startAngle + angleStep * Double(j)
                    subTickAngles.append(tickAngle)
                }
            }
            
            segments.append(TemperatureSegment(
                startValue: startValue,
                endValue: endValue,
                startAngle: startAngle,
                endAngle: endAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前色温值
    /// - Parameter angle: 旋转角度
    /// - Returns: 色温值
    static func calculateTemperatureValue(angle: Double) -> Double {
        let centerTemp = 5600.0  // 中心点温度
        let minTemp = 2000.0     // 最小温度
        let maxTemp = 10000.0    // 最大温度
        let leftAngle = 81.0     // 左侧最大角度
        let rightAngle = 99.0    // 右侧最大角度
        
        if angle >= 0 {
            // 左侧区间 (2000K-5600K)
            let progress = angle / leftAngle
            return centerTemp - (progress * (centerTemp - minTemp))
        } else {
            // 右侧区间 (5600K-10000K)
            let progress = -angle / rightAngle
            return centerTemp + (progress * (maxTemp - centerTemp))
        }
    }
    
    /// 格式化色温显示值
    /// - Parameter value: 色温值
    /// - Returns: 格式化后的字符串
    static func formatTemperatureValue(_ value: Double) -> String {
        return String(format: "%.0fK", value)
    }
    
    /// 计算色温刻度的角度分布
    /// - Returns: 色温刻度段数组
    static func calculateTemperatureAngleDistribution() -> [TemperatureSegment] {
        var segments: [TemperatureSegment] = []
        
        // 色温范围：2000K - 10000K
        let minTemp = 2000.0
        let maxTemp = 10000.0
        let step = 1000.0 // 每1000K一个主刻度
        let subStep = 100.0 // 每100K一个副刻度
        
        // 计算每个主刻度段
        for temp in stride(from: minTemp, to: maxTemp, by: step) {
            let nextTemp = min(temp + step, maxTemp)
            
            // 计算主刻度的角度
            let startProgress = (temp - minTemp) / (maxTemp - minTemp)
            let endProgress = (nextTemp - minTemp) / (maxTemp - minTemp)
            
            let segStartAngle = 171.0 - startProgress * 180.0
            let segEndAngle = 171.0 - endProgress * 180.0
            
            // 计算副刻度的角度
            var subTickAngles: [Double] = []
            let subCount = Int(step / subStep) - 1
            if subCount > 0 {
                let angleStep = (segStartAngle - segEndAngle) / Double(subCount + 1)
                for i in 1...subCount {
                    subTickAngles.append(segStartAngle - angleStep * Double(i))
                }
            }
            
            segments.append(TemperatureSegment(
                startValue: temp,
                endValue: nextTemp,
                startAngle: segStartAngle,
                endAngle: segEndAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
} 