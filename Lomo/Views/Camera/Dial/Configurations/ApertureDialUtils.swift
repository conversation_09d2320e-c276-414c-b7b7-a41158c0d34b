import Foundation
import AVFoundation

/// 光圈刻度段结构
struct ApertureSegment {
    let value: Double      // 光圈值
    let angle: Double      // 角度
}

/// 光圈刻度盘工具类
enum ApertureDialUtils {
    /// 获取设备光圈值
    static func getDeviceAperture(device: AVCaptureDevice?) -> Double {
        return Double(device?.lensAperture ?? 1.8) // 默认值f/1.8
    }
    
    /// 格式化光圈值
    static func formatValue(_ value: Double) -> String {
        return String(format: "f/%.1f", value)
    }
    
    /// 计算刻度分布(只有一个主刻度在指示器位置)
    static func calculateDistribution(aperture: Double) -> [ApertureSegment] {
        // 只需要一个刻度段,角度固定在90度(指示器位置)
        return [ApertureSegment(
            value: aperture,
            angle: 90.0
        )]
    }
} 