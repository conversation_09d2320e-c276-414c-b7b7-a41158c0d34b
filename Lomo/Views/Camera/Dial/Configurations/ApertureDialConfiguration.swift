import Foundation
import SwiftUI
import AVFoundation

/// 光圈刻度盘配置
struct ApertureDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 相机设备
    let device: AVCaptureDevice?
    
    // MARK: - Initialization
    
    /// 初始化光圈刻度盘配置
    /// - Parameters:
    ///   - device: 相机设备，用于获取实际光圈值
    init(device: AVCaptureDevice? = nil) {
        self.device = device
    }
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .aperture }
    
    /// 最小光圈值
    var minValue: Double { 1.8 }
    
    /// 最大光圈值
    var maxValue: Double { 1.8 }
    
    /// 当前值
    var currentValue: Double { 
        Double(device?.lensAperture ?? 1.8)
    }
    
    /// 步进值（固定光圈不需要步进）
    var step: Double { 0.0 }
    
    /// 单位显示
    var unit: String { "" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { false }
    
    /// 主刻度数量（只有一个刻度）
    var mainTickCount: Int { 1 }
    
    /// 副刻度数量（没有副刻度）
    var subTickCount: Int { 0 }
    
    /// 光圈不需要镜头信息
    var lenses: [String] { [] }
    
    /// 光圈不需要显示焦距
    var dialLenses: [String] { [] }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "f/1.8"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        return String(format: "f/%.1f", value)
    }
} 