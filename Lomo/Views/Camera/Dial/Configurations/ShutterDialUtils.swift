import Foundation
import AVFoundation

/// 快门刻度段结构
struct ShutterSegment {
    let startValue: Double    // 起始快门速度（秒）
    let endValue: Double      // 结束快门速度（秒）
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// 快门刻度盘工具类
enum ShutterDialUtils {
    /// 标准快门速度数组（秒）
    static let standardShutterSpeeds: [Double] = [
        1.0/8000.0,  // 1/8000
        1.0/4000.0,  // 1/4000
        1.0/2000.0,  // 1/2000
        1.0/1000.0,  // 1/1000
        1.0/500.0,   // 1/500
        1.0/250.0,   // 1/250
        1.0/125.0,   // 1/125
        1.0/60.0,    // 1/60 - 中心值
        1.0/30.0,    // 1/30
        1.0/15.0,    // 1/15
        1.0/8.0,     // 1/8
        1.0/4.0,     // 1/4
        1.0/2.0,     // 1/2
        1.0,         // 1"
        2.0,         // 2"
        4.0,         // 4"
        8.0          // 8"
    ]
    
    /// 默认快门值（秒）
    static let defaultShutterValue: Double = 1.0/60.0
    
    /// 主刻度线之间的固定角度
    static let angleBetweenMainTicks: Double = 15.0
    
    /// 计算快门刻度分布
    /// - Returns: 快门刻度段数组
    static func calculateShutterDistribution() -> [ShutterSegment] {
        var segments: [ShutterSegment] = []
        
        // 配置参数
        let centerValue = 1.0/60.0  // 中心点快门速度
        let minShutter = 1.0/8000.0 // 最小快门速度
        let maxShutter = 8.0        // 最大快门速度
        let leftAngle = 105.0       // 左侧最大角度
        let rightAngle = 135.0      // 右侧最大角度
        let subTickStep = 1.0/3.0   // 副刻度步进值(1/3档)
        
        // 主刻度值
        let mainTickValues = standardShutterSpeeds
        
        // 创建刻度段
        for i in 0..<(mainTickValues.count - 1) {
            let startValue = mainTickValues[i]
            let endValue = mainTickValues[i + 1]
            
            // 计算角度
            let startAngle: Double
            let endAngle: Double
            
            if startValue <= centerValue {
                // 左侧区间 (1/8000-1/60)
                let leftProgress = (log2(centerValue) - log2(startValue)) / (log2(centerValue) - log2(minShutter))
                startAngle = leftAngle * leftProgress
                
                if endValue <= centerValue {
                    let endProgress = (log2(centerValue) - log2(endValue)) / (log2(centerValue) - log2(minShutter))
                    endAngle = leftAngle * endProgress
                } else {
                    endAngle = 0 // 中心点
                }
            } else {
                // 右侧区间 (1/60-8")
                let startProgress = (log2(startValue) - log2(centerValue)) / (log2(maxShutter) - log2(centerValue))
                startAngle = -rightAngle * startProgress
                
                let endProgress = (log2(endValue) - log2(centerValue)) / (log2(maxShutter) - log2(centerValue))
                endAngle = -rightAngle * endProgress
            }
            
            // 计算副刻度
            var subTickAngles: [Double] = []
            
            // 计算1/3档和2/3档的快门速度
            let oneThirdShutter = startValue * pow(2, 1.0/3.0)
            let twoThirdShutter = startValue * pow(2, 2.0/3.0)
            
            if startValue <= centerValue {
                // 左侧区间 (1/8000-1/60)
                // 计算1/3档对应的角度
                let oneThirdProgress = (log2(centerValue) - log2(oneThirdShutter)) / (log2(centerValue) - log2(minShutter))
                let oneThirdAngle = leftAngle * oneThirdProgress
                subTickAngles.append(oneThirdAngle)
                
                // 计算2/3档对应的角度
                let twoThirdProgress = (log2(centerValue) - log2(twoThirdShutter)) / (log2(centerValue) - log2(minShutter))
                let twoThirdAngle = leftAngle * twoThirdProgress
                subTickAngles.append(twoThirdAngle)
            } else {
                // 右侧区间 (1/60-8")
                // 计算1/3档对应的角度
                let oneThirdProgress = (log2(oneThirdShutter) - log2(centerValue)) / (log2(maxShutter) - log2(centerValue))
                let oneThirdAngle = -rightAngle * oneThirdProgress
                subTickAngles.append(oneThirdAngle)
                
                // 计算2/3档对应的角度
                let twoThirdProgress = (log2(twoThirdShutter) - log2(centerValue)) / (log2(maxShutter) - log2(centerValue))
                let twoThirdAngle = -rightAngle * twoThirdProgress
                subTickAngles.append(twoThirdAngle)
            }
            
            segments.append(ShutterSegment(
                startValue: startValue,
                endValue: endValue,
                startAngle: startAngle,
                endAngle: endAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前快门速度
    /// - Parameter angle: 旋转角度
    /// - Returns: 快门速度（秒）
    static func calculateShutterSpeed(angle: Double) -> Double {
        let centerValue = 1.0/60.0  // 中心点快门速度
        let minShutter = 1.0/8000.0 // 最小快门速度
        let maxShutter = 8.0        // 最大快门速度
        let leftAngle = 105.0       // 左侧最大角度
        let rightAngle = 135.0      // 右侧最大角度
        
        if angle >= 0 {
            // 左侧区间 (1/8000-1/60)
            let progress = angle / leftAngle
            let logShutter = log2(centerValue) - progress * (log2(centerValue) - log2(minShutter))
            return pow(2, logShutter)
        } else {
            // 右侧区间 (1/60-8")
            let progress = -angle / rightAngle
            let logShutter = log2(centerValue) + progress * (log2(maxShutter) - log2(centerValue))
            return pow(2, logShutter)
        }
    }
    
    /// 格式化快门值显示
    /// - Parameter value: 快门速度（秒）
    /// - Returns: 格式化后的字符串
    static func formatShutterValue(_ value: Double) -> String {
        if value >= 1.0 {
            return String(format: "%.0f\"", value)
        } else {
            let denominator = Int(round(1.0 / value))
            return "1/\(denominator)"
        }
    }
} 