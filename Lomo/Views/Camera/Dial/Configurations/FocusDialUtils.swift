import Foundation
import AVFoundation

/// 焦点刻度段结构
struct FocusSegment {
    let startValue: Double    // 起始焦距值（米）
    let endValue: Double      // 结束焦距值（米）
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// 焦点刻度盘工具类
enum FocusDialUtils {
    /// 标准焦距值数组（米）
    static let standardFocusDistances: [Double] = [0, 0.1, 0.2, 0.4, 0.8, 1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 64.0, 100.0, 200.0, 500.0, 1000.0, Double.infinity]
    
    /// 主刻度线之间的固定角度
    static let angleBetweenMainTicks: Double = 15.0
    
    /// 获取设备支持的焦距范围
    /// - Parameter device: 相机设备
    /// - Returns: (min: Double, max: Double) 最小和最大焦距值（米）
    static func getDeviceSupportedFocusRange(device: AVCaptureDevice?) -> (min: Double, max: Double) {
        guard let device = device else {
            return (min: 0.0, max: 100.0) // 默认范围改为从0开始
        }
        
        // 获取设备支持的最小焦距
        let minDistance = Double(device.minimumFocusDistance)
        
        // 如果设备不支持自动对焦,返回默认范围
        if !device.isFocusModeSupported(.autoFocus) || minDistance <= 0 {
            return (min: 0.0, max: 100.0)
        }
        
        return (min: max(0.0, minDistance), max: 100.0)
    }
    
    /// 计算焦距刻度分布
    /// - Parameters:
    ///   - minDistance: 设备支持的最小焦距（米）
    ///   - maxDistance: 设备支持的最大焦距（米）
    /// - Returns: 焦距刻度段数组
    static func calculateFocusDistribution(minDistance: Double = 0.0, maxDistance: Double = 100.0) -> [FocusSegment] {
        var segments: [FocusSegment] = []
        
        // 根据设备支持的范围筛选有效的焦距值
        let validDistances = standardFocusDistances.filter { 
            if $0.isInfinite {
                return true // 始终包含无限远刻度
            }
            return $0 >= 0.0 && $0 <= maxDistance  // 修改为从0开始
        }
        
        // 确保至少有两个值形成一个段
        let distances = validDistances.count < 2 ? [0.0, maxDistance, Double.infinity] : validDistances
        
        // 找到1.0m的位置
        let oneXIndex = distances.firstIndex(of: 1.0) ?? 5 // 默认位置调整为5
        
        // 计算角度分布
        var distanceAngles: [Double: Double] = [:]
        
        // 1.0m固定在0度（指示器位置）
        distanceAngles[1.0] = 0.0
        
        // 计算1.0m右边的角度（较大焦距，每段15度）
        var currentAngle = 0.0
        for i in (oneXIndex + 1)..<distances.count {
            currentAngle -= angleBetweenMainTicks
            distanceAngles[distances[i]] = currentAngle
        }
        
        // 计算1.0m左边的角度（较小焦距，每段15度）
        currentAngle = 0.0
        for i in (0..<oneXIndex).reversed() {
            currentAngle += angleBetweenMainTicks
            distanceAngles[distances[i]] = currentAngle
        }
        
        // 创建刻度段
        for i in 0..<(distances.count - 1) {
            let startValue = distances[i]
            let endValue = distances[i + 1]
            let startAngle = distanceAngles[startValue] ?? 0.0
            let endAngle = distanceAngles[endValue] ?? 0.0
            
            // 计算副刻度
            var subTickAngles: [Double] = []
            
            // 根据焦距范围动态调整副刻度数量
            let subTickCount: Int
            if startValue < 1.0 {
                // 近焦段(0.1m-1m)使用更多的副刻度
                subTickCount = 8
            } else if startValue >= 1.0 && startValue < 10.0 {
                // 中焦段(1m-10m)使用标准副刻度
                subTickCount = 4
            } else if startValue >= 10.0 && startValue < 100.0 {
                // 远焦段(10m-100m)使用较少副刻度
                subTickCount = 2
            } else {
                // 超远焦段(100m-∞)使用2个副刻度
                subTickCount = 2
            }
            
            if subTickCount > 0 {
                // 计算副刻度的角度
                let angleStep = angleBetweenMainTicks / Double(subTickCount + 1)
                for j in 1...subTickCount {
                    let subTickAngle = startAngle - angleStep * Double(j)
                    subTickAngles.append(subTickAngle)
                }
            }
            
            segments.append(FocusSegment(
                startValue: startValue,
                endValue: endValue,
                startAngle: startAngle,
                endAngle: endAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前焦距值
    /// - Parameters:
    ///   - angle: 旋转角度
    ///   - minDistance: 设备支持的最小焦距（米）
    ///   - maxDistance: 设备支持的最大焦距（米）
    /// - Returns: 焦距值（米）
    static func calculateFocusDistance(angle: Double, minDistance: Double = 0.0, maxDistance: Double = 100.0) -> Double {
        // 获取刻度分布
        let segments = calculateFocusDistribution(minDistance: minDistance, maxDistance: maxDistance)
        
        // 当前角度就是旋转角度
        let currentAngle = angle
        
        // 先检查是否在主刻度位置（±0.5度）
        for segment in segments {
            // 检查起始刻度
            if abs(currentAngle - segment.startAngle) <= 0.5 {
                return segment.startValue
            }
            // 检查结束刻度（最后一个段的结束值）
            if abs(currentAngle - segment.endAngle) <= 0.5 {
                return segment.endValue
            }
        }
        
        // 如果不在主刻度位置，找到对应的段并插值
        for segment in segments {
            if currentAngle >= segment.endAngle && currentAngle <= segment.startAngle {
                // 修改：调整progress的计算方式，确保值的正确顺序
                let progress = 1.0 - (currentAngle - segment.endAngle) / (segment.startAngle - segment.endAngle)
                
                // 特殊处理0值的情况
                if segment.startValue == 0 {
                    // 线性插值，而不是对数插值
                    return segment.endValue * (1.0 - progress)
                }
                
                // 使用对数插值计算焦距值
                let logStart = log2(segment.startValue)
                let logEnd = segment.endValue.isInfinite ? log2(2000.0) : log2(segment.endValue)
                let logFocus = logStart + progress * (logEnd - logStart)
                
                // 如果结束值是无限远，在接近结束角度时返回无限远
                if segment.endValue.isInfinite {
                    if progress > 0.95 {
                        return Double.infinity
                    }
                    // 否则返回插值计算的值
                    return pow(2, logFocus)
                }
                
                return pow(2, logFocus)
            }
        }
        
        // 如果角度超出范围，返回最近的有效值
        if currentAngle > (segments.first?.startAngle ?? 0.0) {
            return segments.first?.startValue ?? minDistance
        } else {
            return Double.infinity
        }
    }
    
    /// 根据焦距值计算角度
    /// - Parameters:
    ///   - distance: 焦距值（米）
    ///   - minDistance: 设备支持的最小焦距（米）
    ///   - maxDistance: 设备支持的最大焦距（米）
    /// - Returns: 旋转角度
    static func calculateAngleForDistance(_ distance: Double, minDistance: Double = 0.0, maxDistance: Double = 100.0) -> Double {
        // 获取刻度分布
        let segments = calculateFocusDistribution(minDistance: minDistance, maxDistance: maxDistance)
        
        // 处理无穷大
        if distance.isInfinite {
            return segments.last?.endAngle ?? -120.0
        }
        
        // 处理0值
        if distance == 0 {
            return segments.first?.startAngle ?? 75.0
        }
        
        // 找到对应的段
        for segment in segments {
            if distance >= segment.startValue && distance <= segment.endValue {
                // 特殊处理包含0的段
                if segment.startValue == 0 {
                    // 使用线性插值
                    let progress = distance / segment.endValue
                    return segment.startAngle - progress * (segment.startAngle - segment.endAngle)
                }
                
                // 计算在这个段内的插值
                let progress: Double
                if segment.endValue.isInfinite {
                    // 100米以上到无穷大的特殊处理
                    let logStart = log2(segment.startValue)
                    let logEnd = log2(2000.0) // 使用相同的参考值
                    let logDistance = log2(distance)
                    progress = (logDistance - logStart) / (logEnd - logStart)
                } else {
                    // 正常段的处理
                    let logStart = log2(segment.startValue)
                    let logEnd = log2(segment.endValue)
                    let logDistance = log2(distance)
                    progress = (logDistance - logStart) / (logEnd - logStart)
                }
                
                // 计算角度
                return segment.startAngle - progress * (segment.startAngle - segment.endAngle)
            }
        }
        
        // 如果没找到对应的段，返回最近的有效角度
        if distance <= minDistance {
            return segments.first?.startAngle ?? 75.0
        } else {
            return segments.last?.endAngle ?? -120.0
        }
    }
    
    /// 格式化焦距值显示
    /// - Parameter value: 焦距值（米）
    /// - Returns: 格式化后的字符串
    static func formatFocusValue(_ value: Double) -> String {
        if value.isInfinite {
            return "∞"
        } else if value.truncatingRemainder(dividingBy: 1) == 0 {
            // 整数值不显示小数点
            return String(format: "%.0f", value)
        } else {
            // 小数值保留一位小数
            return String(format: "%.1f", value)
        }
    }
} 