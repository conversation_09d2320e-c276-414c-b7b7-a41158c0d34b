import Foundation
import SwiftUI

/// 曝光刻度盘配置
struct ExposureDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前曝光值
    let currentValue: Double
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .exposure }
    
    /// 最小曝光补偿值
    var minValue: Double { -3.0 }
    
    /// 最大曝光补偿值
    var maxValue: Double { 3.0 }
    
    /// 步进值(1/3 EV)
    var step: Double { 0.3 }
    
    /// 单位显示
    var unit: String { "EV" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { false }
    
    /// 主刻度数量(-3,-2,-1,0,1,2,3)
    var mainTickCount: Int { 7 }
    
    /// 副刻度数量，每个主刻度之间2个副刻度
    var subTickCount: Int { 2 }
    
    /// 曝光不需要镜头信息
    var lenses: [String] { [] }
    
    /// 曝光不需要显示焦距
    var dialLenses: [String] { [] }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "+1.0" 或 "-2.0"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        return String(format: "%+.1f", value)
    }
} 