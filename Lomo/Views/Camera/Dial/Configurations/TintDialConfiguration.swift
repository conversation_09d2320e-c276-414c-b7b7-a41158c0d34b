import Foundation
import SwiftUI

/// 色调刻度盘配置
struct TintDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前色调值
    let currentValue: Double
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .tint }
    
    /// 最小色调值 (-100)
    var minValue: Double { -100.0 }
    
    /// 最大色调值 (+100)
    var maxValue: Double { 100.0 }
    
    /// 步进值(5)
    var step: Double { 5.0 }
    
    /// 单位显示（空）
    var unit: String { "" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { false }
    
    /// 主刻度数量(-100,-80,-60,-40,-20,0,20,40,60,80,100)
    var mainTickCount: Int { 11 }
    
    /// 副刻度数量，每个主刻度之间3个副刻度(每5一个)
    var subTickCount: Int { 3 }
    
    /// 色调不需要镜头信息
    var lenses: [String] { [] }
    
    /// 色调不需要显示焦距
    var dialLenses: [String] { [] }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "+60" 或 "-40"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        // 如果是0，不显示正负号
        if abs(value) < 0.5 {
            return "0"
        }
        // 显示整数值和正负号
        return String(format: "%+.0f", value)
    }
} 