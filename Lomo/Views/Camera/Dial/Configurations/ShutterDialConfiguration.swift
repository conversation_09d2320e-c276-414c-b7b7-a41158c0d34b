import Foundation
import SwiftUI

/// 快门刻度盘配置
struct ShutterDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前快门速度（秒）
    var currentValue: Double
    
    // MARK: - Initialization
    
    /// 初始化快门刻度盘配置
    /// - Parameters:
    ///   - currentValue: 当前快门速度，默认1/125秒
    init(currentValue: Double = 1.0/125.0) {
        self.currentValue = currentValue
    }
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode = .shutter
    
    /// 最小快门速度（1/8000秒）
    var minValue: Double = 1.0/8000.0
    
    /// 最大快门速度（8秒）
    var maxValue: Double = 8.0
    
    /// 步进值(1/3档)
    var step: Double { 1.0/3.0 }
    
    /// 单位显示（空）
    var unit: String { "" }
    
    /// 是否使用对数刻度（快门速度通常是2的幂次方，如1/1000, 1/500, 1/250等）
    var isLogarithmic: Bool { true }
    
    /// 主刻度数量(1/8000, 1/4000, 1/2000, 1/1000, 1/500, 1/250, 1/125, 1/60, 1/30, 1/15, 1/8, 1/4, 1/2, 1", 2", 4", 8")
    var mainTickCount: Int = 17
    
    /// 副刻度数量，每个主刻度之间2个副刻度(1/3档)
    var subTickCount: Int = 2
    
    /// 快门不需要镜头信息
    var lenses: [String] { [] }
    
    /// 快门不需要显示焦距
    var dialLenses: [String] { [] }
    
    /// 标题
    var title: String { "快门" }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "1/1000", "1/500", "2""
    /// - Parameter value: 需要格式化的值（秒）
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        if value >= 1.0 {
            return String(format: "%.0f\"", value)
        } else {
            let denominator = Int(round(1.0 / value))
            return "1/\(denominator)"
        }
    }
    
    /// 获取颜色
    /// - Parameter value: 需要获取颜色的值
    /// - Returns: 颜色
    func getColor(for value: Double) -> Color {
        return .white
    }
    
    /// 获取渐变色
    /// - Parameter value: 需要获取渐变色的值
    /// - Returns: 渐变色
    func getGradientColor(for value: Double) -> Color {
        return .white
    }
} 