import Foundation
import SwiftUI

/// 变焦刻度盘配置
struct ZoomDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 可用镜头列表，如 ["0.5", "1", "2"]
    let lenses: [String]
    
    /// 当前变焦倍数
    let currentZoom: CGFloat
    
    /// 是否是视频模式
    let isVideoMode: Bool
    
    /// 是否有长焦镜头
    let hasTelephotos: Bool
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .zoom }
    
    /// 最小变焦值，固定为 0.5
    var minValue: Double { 0.5 }
    
    /// 最大变焦值
    var maxValue: Double { 
        return 10.0  // 统一为 10x
    }
    
    /// 当前值
    var currentValue: Double { 
        Double(currentZoom) 
    }
    
    /// 步进值，每次变化 0.1
    var step: Double { 0.1 }
    
    /// 单位显示
    var unit: String { "x" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { false }
    
    /// 主刻度数量，等于显示的焦距数量
    var mainTickCount: Int { 
        dialLenses.count 
    }
    
    /// 副刻度数量，固定为 20 个
    var subTickCount: Int { 20 }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "1.0x"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        return String(format: "%.1fx", value)
    }
    
    /// 获取刻度盘显示的焦距组合
    private func getDialLenses() -> [String] {
        // 根据硬件镜头组合判断
        switch lenses {
        case ["1"]:  // 只有主摄
            return isVideoMode ? ["1", "2", "3"] : ["1", "2", "5"]
            
        case ["0.5", "1"]:  // 超广角 + 主摄
            return isVideoMode ? ["0.5", "1", "2", "3"] : ["0.5", "1", "2", "5"]
            
        case ["0.5", "1", "2"]:  // 超广角 + 主摄 + 2X长焦
            return isVideoMode ? ["0.5", "1", "2", "3", "6"] : ["0.5", "1", "2", "3", "6", "10"]
            
        case ["0.5", "1", "2.5"]:  // 超广角 + 主摄 + 2.5X长焦
            return isVideoMode ? ["0.5", "1", "2.5", "5"] : ["0.5", "1", "2.5", "5", "10"]
            
        case ["0.5", "1", "3"]:  // 超广角 + 主摄 + 3X长焦
            return isVideoMode ? ["0.5", "1", "2", "3", "6"] : ["0.5", "1", "2", "3", "6", "10"]
            
        case ["0.5", "1", "5"]:  // 超广角 + 主摄 + 5X长焦
            return isVideoMode ? ["0.5", "1", "2", "5", "10"] : ["0.5", "1", "2", "5", "10"]
            
        default:
            return ["1"]  // 默认至少显示主摄
        }
    }
    
    /// 刻度盘显示的焦距列表
    var dialLenses: [String] {
        getDialLenses()
    }
} 