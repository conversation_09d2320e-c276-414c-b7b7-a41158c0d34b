import Foundation
import SwiftUI

/// 色调刻度段结构
struct TintSegment {
    let startValue: Double    // 起始色调值
    let endValue: Double      // 结束色调值
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// 色调刻度盘工具类
enum TintDialUtils {
    /// 根据旋转角度计算当前色调值
    static func calculateValue(angle: Double) -> Double {
        // 色调范围：-100 到 +100
        let minTint = -100.0
        let maxTint = 100.0
        
        // 角度范围：-90 到 90（旋转角度）
        // 对应刻度角度：180 到 0（刻度角度）
        
        // 将旋转角度转换为刻度角度
        let scaleAngle = 90.0 - angle
        
        // 线性映射从刻度角度到色调值
        let progress = (scaleAngle - 0.0) / (180.0 - 0.0)
        let tintValue = minTint + progress * (maxTint - minTint)
        
        // 限制在有效范围内
        return max(minTint, min(maxTint, tintValue))
    }
    
    /// 格式化色调值
    static func formatValue(_ value: Double) -> String {
        // 如果是0，不显示正负号
        if abs(value) < 0.5 {
            return "0"
        }
        // 显示整数值和正负号
        return String(format: "%+.0f", value)
    }
    
    /// 计算色调刻度的角度分布
    static func calculateDistribution() -> [TintSegment] {
        var segments: [TintSegment] = []
        
        // 色调范围：-100 到 +100
        let minTint = -100.0
        let maxTint = 100.0
        let step = 20.0  // 每20一个主刻度
        let subStep = 5.0  // 每5一个副刻度
        
        // 计算每个主刻度段
        for tint in stride(from: minTint, to: maxTint, by: step) {
            let nextTint = min(tint + step, maxTint)
            
            // 计算主刻度的角度 - 线性映射从[-100,+100]到[0,180]，中心点在90度
            let startProgress = (tint - minTint) / (maxTint - minTint)
            let endProgress = (nextTint - minTint) / (maxTint - minTint)
            
            // 新的角度计算方式，以90度为中心
            let segStartAngle = 180.0 - startProgress * 180.0
            let segEndAngle = 180.0 - endProgress * 180.0
            
            // 计算副刻度的角度
            var subTickAngles: [Double] = []
            let subCount = Int(step / subStep) - 1
            if subCount > 0 {
                let angleStep = (segStartAngle - segEndAngle) / Double(subCount + 1)
                for i in 1...subCount {
                    subTickAngles.append(segStartAngle - angleStep * Double(i))
                }
            }
            
            segments.append(TintSegment(
                startValue: tint,
                endValue: nextTint,
                startAngle: segStartAngle,
                endAngle: segEndAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
} 