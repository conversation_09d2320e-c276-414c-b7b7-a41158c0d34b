import Foundation
import SwiftUI
import AVFoundation

/// 变焦相关工具类
enum ZoomUtils {
    /// 格式化变焦倍率显示
    /// - Parameter value: 变焦倍率值
    /// - Returns: 格式化后的字符串，整数不显示小数点
    static func formatZoomFactor(_ value: Double) -> String {
        return value.truncatingRemainder(dividingBy: 1) == 0 ? 
            String(format: "%.0f", value) : 
            String(format: "%.1f", value)
    }
    
    /// 角度分配算法
    /// - Parameter levels: 变焦级别数组
    /// - Returns: 角度段数组
    static func calculateAngleDistribution(levels: [ZoomLevel]) -> [AngleSegment] {
        let firstSegment = 20.0  // 第一段固定20度
        let q = 0.8  // 公比
        
        var segments: [AngleSegment] = []
        guard levels.count >= 2 else { return segments }
        
        // 1. 将变焦值转换为数值
        let zoomFactors = levels.map { Double($0.value.replacingOccurrences(of: "x", with: "")) ?? 0.0 }
        
        // 2. 计算角度
        var integerAngles: [Double] = []
        
        // 找到1x的位置
        if let oneXIndex = zoomFactors.firstIndex(of: 1.0) {
            // 从1x位置开始计算
            var currentAngle = 90.0  // 1x固定在90度
            integerAngles = Array(repeating: 0.0, count: levels.count)
            integerAngles[oneXIndex] = 90.0
            
            // 计算1x右边的角度
            var segmentIndex = 0
            for i in (oneXIndex + 1)..<zoomFactors.count {
                let segmentAngle = firstSegment * pow(q, Double(segmentIndex))
                currentAngle -= segmentAngle
                integerAngles[i] = currentAngle
                segmentIndex += 1
            }
            
            // 计算1x左边的角度
            currentAngle = 90.0
            segmentIndex = 0
            for i in (0..<oneXIndex).reversed() {
                let segmentAngle = firstSegment * pow(q, Double(segmentIndex))
                currentAngle += segmentAngle
                integerAngles[i] = currentAngle
                segmentIndex += 1
            }
        }
        
        // 3. 创建角度段
        for i in 0..<(levels.count - 1) {
            let startValue = zoomFactors[i]
            let endValue = zoomFactors[i + 1]
            
            // 计算这个区间需要多少个副刻度（每0.1一个刻度）
            let subTickCount = Int((endValue - startValue) * 10) - 1
            
            // 计算副刻度的角度
            var subTickAngles: [Double] = []
            if subTickCount > 0 {
                let segmentAngle = integerAngles[i] - integerAngles[i + 1]
                let tickInterval = segmentAngle / Double(subTickCount + 1)
                for j in 1...subTickCount {
                    let tickAngle = integerAngles[i] - tickInterval * Double(j)
                    subTickAngles.append(tickAngle)
                }
            }
            
            segments.append(
                AngleSegment(
                    startLens: levels[i].value,
                    endLens: levels[i + 1].value,
                    startAngle: integerAngles[i],
                    endAngle: integerAngles[i + 1],
                    subTickAngles: subTickAngles
                )
            )
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前变焦值
    /// - Parameters:
    ///   - angle: 旋转角度
    ///   - calculationLevels: 计算用的变焦级别数组
    /// - Returns: 计算得到的变焦值
    static func calculateZoomValue(angle: Double, calculationLevels: [ZoomLevel]) -> Double {
        // 获取角度分布
        let segments = calculateAngleDistribution(levels: calculationLevels)
        
        // 当前角度（考虑旋转）
        let currentAngle = 90 + angle
        
        // 直接根据角度找到对应的区间
        for i in 0..<segments.count {
            let segment = segments[i]
            let startValue = Double(segment.startLens.replacingOccurrences(of: "x", with: "")) ?? 1.0
            let endValue = Double(segment.endLens.replacingOccurrences(of: "x", with: "")) ?? 1.0
            
            // 直接判断角度是否在这个区间的刻度范围内
            if currentAngle >= segment.endAngle && currentAngle <= segment.startAngle {
                // 计算在这个区间内的插值
                let progress = (currentAngle - segment.endAngle) / (segment.startAngle - segment.endAngle)
                let value = startValue + (endValue - startValue) * (1 - progress)
                return value
            }
        }
        
        return 1.0 // 默认返回1x
    }
} 