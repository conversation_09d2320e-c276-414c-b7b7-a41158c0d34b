import Foundation
import AVFoundation

/// ISO刻度段结构
struct ISOSegment {
    let startValue: Double    // 起始ISO值
    let endValue: Double      // 结束ISO值
    let startAngle: Double    // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 副刻度角度列表
}

/// ISO刻度盘工具类
enum ISODialUtils {
    /// 标准ISO值数组
    static let standardISOValues: [Double] = [50, 100, 200, 400, 800, 1600, 3200, 6400, 12800]
    
    /// 主刻度线之间的固定角度
    static let angleBetweenMainTicks: Double = 15.0
    
    /// 计算ISO刻度分布
    /// - Parameters:
    ///   - minISO: 设备支持的最小ISO值
    ///   - maxISO: 设备支持的最大ISO值
    /// - Returns: ISO刻度段数组
    static func calculateISODistribution(minISO: Double = 50.0, maxISO: Double = 12800.0) -> [ISOSegment] {
        var segments: [ISOSegment] = []
        
        // 根据设备支持的范围筛选有效的ISO值
        let validISOValues = standardISOValues.filter { $0 >= minISO && $0 <= maxISO }
        
        // 确保至少有两个值形成一个段
        let isoValues: [Double]
        if validISOValues.count < 2 {
            // 如果没有足够的标准值在范围内，创建自定义值
            isoValues = [minISO, maxISO]
        } else {
            // 确保包含最小和最大值
            var values = validISOValues
            if !values.contains(minISO) && minISO < values.first! { values.insert(minISO, at: 0) }
            if !values.contains(maxISO) && maxISO > values.last! { values.append(maxISO) }
            isoValues = values.sorted()
        }
        
        // 修改：动态计算总角度，每个主刻度之间固定15度
        let totalAngle = (Double(isoValues.count) - 1) * angleBetweenMainTicks
        print("📏 ISO刻度盘总角度: \(totalAngle)度，共\(isoValues.count)个主刻度")
        
        // 修改：直接使用等间距角度分配，但反转顺序（最小ISO在左侧/大角度，最大ISO在右侧/小角度）
        var isoAngles: [Double: Double] = [:]
        for (index, iso) in isoValues.enumerated() {
            // 反转映射：最小ISO对应最大角度，最大ISO对应0度
            let angle = totalAngle - (Double(index) * angleBetweenMainTicks)
            isoAngles[iso] = angle
        }
        
        // 创建刻度段
        for i in 0..<(isoValues.count - 1) {
            let startValue = isoValues[i]
            let endValue = isoValues[i + 1]
            let startAngle = isoAngles[startValue] ?? 0
            let endAngle = isoAngles[endValue] ?? 0
            
            // 计算副刻度（1/3档和2/3档）
            var subTickAngles: [Double] = []
            
            // 计算1/3档和2/3档的ISO值
            let oneThirdISO = startValue * pow(2, 1/3)
            let twoThirdISO = startValue * pow(2, 2/3)
            
            // 只有在有效范围内才添加副刻度
            if oneThirdISO < endValue {
                // 计算1/3档对应的角度 - 线性插值
                let oneThirdProgress = (log2(oneThirdISO) - log2(startValue)) / (log2(endValue) - log2(startValue))
                let oneThirdAngle = startAngle - oneThirdProgress * (startAngle - endAngle)
                subTickAngles.append(oneThirdAngle)
                
                // 只有当第二个副刻度也在有效范围内时才添加
                if twoThirdISO < endValue {
                    // 计算2/3档对应的角度 - 线性插值
                    let twoThirdProgress = (log2(twoThirdISO) - log2(startValue)) / (log2(endValue) - log2(startValue))
                    let twoThirdAngle = startAngle - twoThirdProgress * (startAngle - endAngle)
                    subTickAngles.append(twoThirdAngle)
                }
            }
            
            segments.append(ISOSegment(
                startValue: startValue,
                endValue: endValue,
                startAngle: startAngle,
                endAngle: endAngle,
                subTickAngles: subTickAngles
            ))
        }
        
        return segments
    }
    
    /// 根据旋转角度计算当前ISO值
    /// - Parameters:
    ///   - angle: 旋转角度
    ///   - minISO: 设备支持的最小ISO值
    ///   - maxISO: 设备支持的最大ISO值
    /// - Returns: ISO值
    static func calculateISOValue(angle: Double, minISO: Double = 50.0, maxISO: Double = 12800.0) -> Double {
        // 获取有效的ISO值
        let validISOValues = standardISOValues.filter { $0 >= minISO && $0 <= maxISO }
        
        // 确保至少有两个值形成一个段
        let isoValues: [Double]
        if validISOValues.count < 2 {
            isoValues = [minISO, maxISO]
        } else {
            var values = validISOValues
            if !values.contains(minISO) && minISO < values.first! { values.insert(minISO, at: 0) }
            if !values.contains(maxISO) && maxISO > values.last! { values.append(maxISO) }
            isoValues = values.sorted()
        }
        
        // 修改：动态计算总角度
        let totalAngle = (Double(isoValues.count) - 1) * angleBetweenMainTicks
        
        // 限制角度在有效范围内
        let clampedAngle = max(0.0, min(totalAngle, angle))
        
        // 找到角度所在的刻度段
        let segments = calculateISODistribution(minISO: minISO, maxISO: maxISO)
        
        for segment in segments {
            if clampedAngle <= segment.startAngle && clampedAngle >= segment.endAngle {
                // 在段内进行插值
                let progress = (segment.startAngle - clampedAngle) / (segment.startAngle - segment.endAngle)
                
                // 使用对数插值计算ISO值
                let logStart = log2(segment.startValue)
                let logEnd = log2(segment.endValue)
                let logISO = logStart + progress * (logEnd - logStart)
                
                return pow(2, logISO)
            }
        }
        
        // 如果角度位于最小ISO值（最大角度）之前
        if clampedAngle > segments.first?.startAngle ?? totalAngle {
            return minISO
        }
        
        // 如果角度位于最大ISO值（0度）之后
        if clampedAngle < segments.last?.endAngle ?? 0 {
            return maxISO
        }
        
        // 默认返回最小ISO
        return minISO
    }
    
    /// 获取设备支持的ISO范围
    /// - Parameter device: 相机设备
    /// - Returns: (最小ISO, 最大ISO)
    static func getDeviceSupportedISORange(device: AVCaptureDevice?) -> (min: Double, max: Double) {
        guard let device = device else {
            return (50.0, 12800.0) // 默认范围
        }
        
        return (Double(device.activeFormat.minISO), Double(device.activeFormat.maxISO))
    }
} 