import Foundation
import SwiftUI

/// 色温刻度盘配置
struct TemperatureDialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前色温值
    let currentValue: Double
    
    // MARK: - Style Properties
    
    /// 背景颜色
    var backgroundColor: Color { .black.opacity(UIConstants.dialBackgroundOpacity) }
    
    /// 指示器颜色
    var indicatorColor: Color { .yellow }
    
    /// 刻度线颜色
    var tickColor: Color { .white }
    
    /// 文本颜色
    var textColor: Color { .white }
    
    /// 总角度范围（180度）
    let totalAngleRange: Double = 180.0
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .temperature }
    
    /// 最小色温值 (2000K)
    var minValue: Double { 2000.0 }
    
    /// 最大色温值 (10000K)
    var maxValue: Double { 10000.0 }
    
    /// 步进值(100K)
    var step: Double { 100.0 }
    
    /// 单位显示
    var unit: String { "K" }
    
    /// 是否使用对数刻度
    var isLogarithmic: Bool { false }
    
    /// 主刻度数量(2000,3000,4000,5000,6000,7000,8000,9000,10000)
    var mainTickCount: Int { 9 }
    
    /// 副刻度数量，每个主刻度之间9个副刻度(每100K一个)
    var subTickCount: Int { 9 }
    
    /// 色温不需要镜头信息
    var lenses: [String] { [] }
    
    /// 色温不需要显示焦距
    var dialLenses: [String] { [] }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "5500K"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        return String(format: "%.0fK", value)
    }
} 