import Foundation
import SwiftUI

/// ISO刻度盘配置
struct ISODialConfiguration: DialConfiguration {
    // MARK: - Properties
    
    /// 当前ISO值
    var currentValue: Double
    
    // MARK: - Style Properties
    
    /// 背景颜色
    var backgroundColor: Color { .black.opacity(UIConstants.dialBackgroundOpacity) }
    
    /// 指示器颜色
    var indicatorColor: Color { .white }
    
    /// 刻度线颜色
    var tickColor: Color { .white }
    
    /// 文本颜色
    var textColor: Color { .white }
    
    // MARK: - DialConfiguration
    
    /// 刻度盘模式
    var mode: DialMode { .iso }
    
    /// 最小ISO值 (通常为50或100)
    var minValue: Double { 50 }
    
    /// 最大ISO值 (通常为12800)
    var maxValue: Double { 12800 }
    
    /// 步进值(1/3档)
    var step: Double { 50 }
    
    /// 单位显示（空）
    var unit: String { "" }
    
    /// 是否使用对数刻度（ISO值通常是2的幂次方，如100, 200, 400等）
    var isLogarithmic: Bool { true }
    
    /// 主刻度数量(50, 100, 200, 400, 800, 1600, 3200, 6400, 12800)
    var mainTickCount: Int { 9 }
    
    /// 副刻度数量，每个主刻度之间2个副刻度(1/3档)
    var subTickCount: Int { 2 }
    
    /// ISO不需要镜头信息
    var lenses: [String] { [] }
    
    /// ISO不需要显示焦距
    var dialLenses: [String] { [] }
    
    /// 标题
    var title: String { "ISO" }
    
    // MARK: - Methods
    
    /// 格式化显示值，如 "100", "800"
    /// - Parameter value: 需要格式化的值
    /// - Returns: 格式化后的字符串
    func formatValue(_ value: Double) -> String {
        return String(format: "%.0f", value)
    }
    
    /// 获取颜色
    /// - Parameter value: 需要获取颜色的值
    /// - Returns: 颜色
    func getColor(for value: Double) -> Color {
        return .white
    }
    
    /// 获取渐变色
    /// - Parameter value: 需要获取渐变色的值
    /// - Returns: 渐变色
    func getGradientColor(for value: Double) -> Color {
        return .white
    }
} 