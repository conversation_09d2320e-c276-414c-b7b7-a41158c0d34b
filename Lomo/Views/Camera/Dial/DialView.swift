import SwiftUI
import AVFoundation

// 角度段结构
struct AngleSegment {
    let startLens: String      // 起始焦距值
    let endLens: String        // 结束焦距值
    let startAngle: Double     // 起始角度
    let endAngle: Double      // 结束角度
    let subTickAngles: [Double] // 小刻度的角度列表
}

// 变焦类型
enum ZoomType {
    case optical  // 光学变焦
    case digital  // 数码变焦
}

// 单个变焦刻度的配置
struct ZoomLevel {
    let value: String  // 变焦倍数，如 "0.5", "1", "2"
    let type: ZoomType
}

// 变焦配置
struct ZoomConfiguration {
    let displayLevels: [ZoomLevel]  // 显示用的刻度值
    let calculationLevels: [ZoomLevel]  // 计算用的完整序列
    
    // 根据可用镜头创建变焦配置
    init(availableLenses: Set<String>, mode: CameraMode) {
        // 将字符串转换为浮点数进行比较
        let lenses = availableLenses.sorted()
        
        // 1. 设置显示用的刻度值
        var display: [ZoomLevel] = []
        
        switch lenses {
        case ["1"]:  // 只有主摄
            display = [
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2", type: .digital),
                ZoomLevel(value: "5", type: .digital)
            ]
            
        case ["0.5", "1"]:  // 超广角 + 主摄
            display = [
                ZoomLevel(value: "0.5", type: .optical),
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2", type: .digital),
                ZoomLevel(value: "5", type: .digital),
                ZoomLevel(value: "10", type: .digital)
            ]
            
        case ["0.5", "1", "2"]:  // 超广角 + 主摄 + 2X长焦
            display = [
                ZoomLevel(value: "0.5", type: .optical),
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2", type: .optical),
                ZoomLevel(value: "5", type: .digital),
                ZoomLevel(value: "10", type: .digital)
            ]
            
        case ["0.5", "1", "2.5"]:  // 超广角 + 主摄 + 2.5X长焦
            display = [
                ZoomLevel(value: "0.5", type: .optical),
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2.5", type: .optical),
                ZoomLevel(value: "5", type: .digital),
                ZoomLevel(value: "10", type: .digital)
            ]
            
        case ["0.5", "1", "3"]:  // 超广角 + 主摄 + 3X长焦
            display = [
                ZoomLevel(value: "0.5", type: .optical),
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2", type: .digital),
                ZoomLevel(value: "3", type: .optical),
                ZoomLevel(value: "6", type: .digital),
                ZoomLevel(value: "10", type: .digital)
            ]
            
        case ["0.5", "1", "5"]:  // 超广角 + 主摄 + 5X长焦
            display = [
                ZoomLevel(value: "0.5", type: .optical),
                ZoomLevel(value: "1", type: .optical),
                ZoomLevel(value: "2", type: .digital),
                ZoomLevel(value: "5", type: .optical),
                ZoomLevel(value: "10", type: .digital)
            ]
            
        default:
            display = [ZoomLevel(value: "1", type: .optical)]
        }
        
        // 2. 从 display 中获取最大变焦值
        let maxZoom = Double(display.last?.value.replacingOccurrences(of: "x", with: "") ?? "10") ?? 10.0
        
        // 3. 创建计算用的完整序列
        var calculation: [ZoomLevel] = []
        
        // 添加0.5（如果有）
        if lenses.contains("0.5") {
            calculation.append(ZoomLevel(value: "0.5", type: .optical))
        }
        
        // 添加1x到实际最大值的所有整数倍率
        for zoom in 1...Int(maxZoom) {
            let value = String(zoom)
            let type: ZoomType = lenses.contains(value) ? .optical : .digital
            calculation.append(ZoomLevel(value: value, type: type))
        }
        
        self.displayLevels = display
        self.calculationLevels = calculation
    }
}

// 相机模式
enum CameraMode {
    case photo
    case video
    case focus
}

// UIConstants 扩展
extension UIConstants {
    static let zoomHStackWidth: CGFloat = 0.10  // HStack的固定宽度（屏幕宽度的10%）
    static let zoomHStackHeight: CGFloat = 0.04 // HStack的固定高度（屏幕高度的4%）
}

// 通用遮罩视图
struct DialMaskView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let config: MaskUtils.MaskConfiguration
    let rotationAngle: Double
    
    var body: some View {
        MaskUtils.createDialMaskPath(dialWidth: dialWidth, dialHeight: dialHeight, config: config)
            .fill(Color.black)
            .blendMode(.destinationOut)
            .rotationEffect(.degrees(-rotationAngle), anchor: .bottom)
    }
}

// 通用刻度盘视图
struct DialView<Config: DialConfiguration>: View {
    let config: Config
    @Binding var value: Double
    let mode: CameraMode
    let availableLenses: Set<String>
    let device: AVCaptureDevice?
    
    // 样式配置
    let backgroundColor: Color
    let indicatorColor: Color
    let tickColor: Color
    let textColor: Color
    
    // 获取当前模式下的显示刻度配置
    private var displayLevels: [ZoomLevel] {
        if config.mode == .exposure {
            // 生成曝光值的刻度
            return (-3...3).map { value in
                ZoomLevel(value: String(format: "%.1f", Double(value)), type: .optical)
            }
        }
        let configuration = getZoomConfiguration(for: availableLenses, mode: mode)
        return configuration.displayLevels
    }
    
    // 获取计算用的完整序列
    private var calculationLevels: [ZoomLevel] {
        if config.mode == .exposure {
            // 生成曝光值的计算序列
            return (-3...3).map { value in
                ZoomLevel(value: String(format: "%.1f", Double(value)), type: .optical)
            }
        }
        let configuration = getZoomConfiguration(for: availableLenses, mode: mode)
        return configuration.calculationLevels
    }
    
    // 回调函数
    let onValueChanged: ((Double) -> Void)?
    let onTouchBegan: (() -> Void)?
    let onTouchEnded: (() -> Void)?
    
    // 修改：保存上次的角度
    @State private var rotationAngle: Double
    @State private var previousAngle: Double = 90
    @State private var isTouching: Bool = false
    @State private var startLocation: CGPoint = .zero
    @State private var startRotationAngle: Double = 0
    
    // 新增：记录是否是从按钮切换来的
    private let isFromButtonSelection: Bool
    
    // 添加动画管理器
    @StateObject private var animationManager = AnimationManager.shared
    
    // 新增：添加 viewModel 参数
    private let viewModel: CameraViewModel
    
    init(config: Config,
         value: Binding<Double>,
         mode: CameraMode,
         availableLenses: Set<String>,
         device: AVCaptureDevice? = nil,
         backgroundColor: Color = .black.opacity(UIConstants.dialBackgroundOpacity),
         indicatorColor: Color = .yellow,
         tickColor: Color = .white,
         textColor: Color = .white,
         onValueChanged: ((Double) -> Void)? = nil,
         onTouchBegan: (() -> Void)? = nil,
         onTouchEnded: (() -> Void)? = nil,
         isFromButtonSelection: Bool = false,
         viewModel: CameraViewModel) {
        self.config = config
        self._value = value
        self.mode = mode
        self.availableLenses = availableLenses
        self.device = device
        self.backgroundColor = backgroundColor
        self.indicatorColor = indicatorColor
        self.tickColor = tickColor
        self.textColor = textColor
        self.onValueChanged = onValueChanged
        self.onTouchBegan = onTouchBegan
        self.onTouchEnded = onTouchEnded
        self.isFromButtonSelection = isFromButtonSelection
        self.viewModel = viewModel
        
        // 根据不同模式使用不同的存储key
        let storageKey: String
        switch config.mode {
        case .exposure:
            storageKey = "LastExposureDialRotationAngle"
        case .temperature:
            storageKey = "LastTemperatureDialRotationAngle"
        case .tint:
            storageKey = "LastTintDialRotationAngle"
        case .iso:
            storageKey = "LastISODialRotationAngle"
        case .shutter:
            storageKey = "LastShutterDialRotationAngle"
        case .focus:
            storageKey = "LastFocusDialRotationAngle"
        default:
            storageKey = "LastZoomDialRotationAngle"
        }
        
        // 获取保存的角度值
        let savedAngle = UserDefaults.standard.double(forKey: storageKey)
        
        // 判断是否是ISO刻度盘且没有保存过角度值（savedAngle为0）
        if config.mode == .iso && savedAngle == 0 {
            // 为ISO刻度盘设置默认角度，使其指向最小ISO值（左侧）
            let isoValues = ISODialUtils.standardISOValues.filter { 
                $0 >= viewModel.state.deviceMinISO && $0 <= viewModel.state.deviceMaxISO 
            }
            let defaultAngle = Double(max(isoValues.count, 2) - 1) * ISODialUtils.angleBetweenMainTicks
            self._rotationAngle = State(initialValue: defaultAngle)
        } else {
            // 使用保存的角度值
            self._rotationAngle = State(initialValue: savedAngle)
        }
    }
    
    private let dialHeight: CGFloat = UIScreen.main.bounds.width * UIConstants.zoomDialHeight
    private let dialWidth: CGFloat = UIScreen.main.bounds.width
    
    // 根据旋转角度计算当前值
    private func calculateCurrentValue(angle: Double) -> Double {
        if config.mode == .exposure {
            return calculateExposureValue(angle: angle)
        } else if config.mode == .temperature {
            // 使用 TemperatureDialUtils 中已有的计算方法
            return TemperatureDialUtils.calculateTemperatureValue(angle: angle)
        } else if config.mode == .tint {
            // 使用色调计算方法
            return calculateTintValue(angle: angle)
        } else if config.mode == .iso {
            // 修改：使用新的ISO值计算逻辑
            let minISO = viewModel.state.deviceMinISO
            let maxISO = viewModel.state.deviceMaxISO
            return ISODialUtils.calculateISOValue(angle: angle, minISO: minISO, maxISO: maxISO)
        } else if config.mode == .focus {
            // 使用 FocusDialUtils 中的计算方法
            return FocusDialUtils.calculateFocusDistance(angle: angle)
        }
        
        // 修改：使用ZoomUtils中的函数计算变焦值
        return ZoomUtils.calculateZoomValue(angle: angle, calculationLevels: calculationLevels)
    }
    
    var body: some View {
        ZStack {
            // 1. 背景层
            Path { path in
                path.addArc(
                    center: CGPoint(x: dialWidth/2, y: dialHeight),
                    radius: dialHeight,
                    startAngle: .degrees(360),
                    endAngle: .degrees(0),
                    clockwise: false
                )
            }
            .fill(backgroundColor)
            .zIndex(0)  // 最底层
            
            // 2. 刻度线层
            TickMarksView(
                mainTickCount: config.mainTickCount,
                subTickCount: config.subTickCount,
                dialWidth: dialWidth,
                dialHeight: dialHeight,
                tickColor: tickColor,
                displayLevels: displayLevels,
                calculationLevels: calculationLevels,
                device: device,
                currentAngle: 90 - rotationAngle,  // 修复计算方式
                rotationAngle: rotationAngle,
                dialMode: config.mode,  // 传递刻度盘模式
                exposureValue: config.mode == .exposure ? value : nil,  // 传递曝光值
                cameraState: viewModel.state  // 传递相机状态
            )
            .rotationEffect(.degrees(rotationAngle), anchor: .bottom)
            .zIndex(1)
            
            // 3. 指示器层
            IndicatorView(
                dialWidth: dialWidth,
                dialHeight: dialHeight,
                indicatorColor: indicatorColor,
                currentValue: ZoomUtils.formatZoomFactor(calculateCurrentValue(angle: rotationAngle)),
                dialMode: config.mode,
                exposureValue: config.mode == .exposure ? value : nil,
                rotationAngle: rotationAngle,
                minISO: viewModel.state.deviceMinISO,
                maxISO: viewModel.state.deviceMaxISO,
                cameraState: viewModel.state,
                cameraViewModel: viewModel
            )
            .zIndex(2)  // 最上层
        }
        .frame(width: dialWidth, height: dialHeight)
        .scaleEffect(animationManager.dialScale, anchor: .bottom)
        .opacity(animationManager.dialOpacity)
        .offset(y: UIScreen.main.bounds.height * 0.10)  // 添加通用偏移
        .gesture(
            DragGesture(minimumDistance: 0)
                .onChanged { gesture in
                    if !isTouching {
                        startLocation = gesture.location
                        startRotationAngle = rotationAngle
                        isTouching = true
                        onTouchBegan?()
                    }
                    
                    // 计算水平方向的移动距离
                    let translation = startLocation.x - gesture.location.x
                    
                    // 转换为角度变化
                    let deltaAngle = translation * 0.5
                    
                    // 计算新的旋转角度
                    let newRotationAngle = startRotationAngle + deltaAngle
                    
                    // 根据不同的刻度盘模式设置角度限制
                    switch config.mode {
                    case .exposure:
                        // 曝光刻度盘的角度限制
                        if newRotationAngle >= 45 {
                            // 到达 +3EV 位置
                            rotationAngle = 45
                        } else if newRotationAngle <= -45 {
                            // 到达 -3EV 位置
                            rotationAngle = -45
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新曝光值和角度
                        let exposureValue = calculateExposureValue(angle: rotationAngle)
                        value = exposureValue
                        onValueChanged?(exposureValue)
                        viewModel.updateExposureDialRotation(rotationAngle)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .temperature:
                        // 温度刻度盘的角度限制
                        if newRotationAngle >= 81 {
                            // 到达最低色温位置 (2000K)
                            rotationAngle = 81
                        } else if newRotationAngle <= -99 {
                            // 到达最高色温位置 (10000K)
                            rotationAngle = -99
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新温度值
                        let temperatureValue = TemperatureDialUtils.calculateTemperatureValue(angle: rotationAngle)
                        value = temperatureValue
                        onValueChanged?(temperatureValue)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .tint:
                        // 色调刻度盘的角度限制
                        if newRotationAngle >= 90 {
                            // 到达 -100 位置
                            rotationAngle = 90
                        } else if newRotationAngle <= -90 {
                            // 到达 +100 位置
                            rotationAngle = -90
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新色调值
                        let tintValue = calculateTintValue(angle: rotationAngle)
                        value = tintValue
                        onValueChanged?(tintValue)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .iso:
                        // ISO刻度盘的角度限制
                        // 修改：动态计算ISO刻度盘的最大角度
                        let isoValues = ISODialUtils.standardISOValues.filter { 
                            $0 >= viewModel.state.deviceMinISO && $0 <= viewModel.state.deviceMaxISO 
                        }
                        let maxISOAngle = Double(max(isoValues.count, 2) - 1) * ISODialUtils.angleBetweenMainTicks
                        
                        if newRotationAngle >= maxISOAngle {
                            // 到达最小ISO位置（左侧）
                            rotationAngle = maxISOAngle
                        } else if newRotationAngle <= 0 {
                            // 到达最大ISO位置（右侧）
                            rotationAngle = 0
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新ISO值
                        let minISO = viewModel.state.deviceMinISO
                        let maxISO = viewModel.state.deviceMaxISO
                        let isoValue = ISODialUtils.calculateISOValue(angle: rotationAngle, minISO: minISO, maxISO: maxISO)
                        value = isoValue
                        onValueChanged?(isoValue)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .shutter:
                        // 快门刻度盘的角度限制
                        if newRotationAngle >= 105 {
                            // 到达最高快门速度位置 (1/8000秒)
                            rotationAngle = 105
                        } else if newRotationAngle <= -135 {
                            // 到达最低快门速度位置 (8秒)
                            rotationAngle = -135
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新快门值
                        let shutterValue = ShutterDialUtils.calculateShutterSpeed(angle: rotationAngle)
                        print("📸 快门刻度盘 - 角度: \(rotationAngle), 计算的快门值: \(shutterValue), 格式化显示: \(ShutterDialUtils.formatShutterValue(shutterValue))")
                        value = shutterValue
                        onValueChanged?(shutterValue)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .focus:
                        // 焦点刻度盘的角度限制
                        if newRotationAngle >= 75 {
                            // 到达最大焦距位置（左侧）
                            rotationAngle = 75
                        } else if newRotationAngle <= -120 {
                            // 到达最小焦距位置（右侧）
                            rotationAngle = -120
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                        // 更新焦距值
                        let focusValue = FocusDialUtils.calculateFocusDistance(angle: rotationAngle)
                        value = focusValue
                        onValueChanged?(focusValue)
                        return // 直接返回,不执行后面的变焦区间检查
                        
                    case .zoom:
                        // 变焦刻度盘的逻辑
                        // 获取角度分布
                        let segments = ZoomUtils.calculateAngleDistribution(levels: calculationLevels)
                        // 获取最后一段的结束角度
                        let maxZoomAngle = -(90 - (segments.last?.endAngle ?? 90.0))
                        
                        // 在边界处停止旋转
                        if (newRotationAngle >= 20) {
                            // 到达0.5x位置
                            rotationAngle = 20
                        } else if (newRotationAngle <= maxZoomAngle) {
                            // 到达最大倍率位置
                            rotationAngle = maxZoomAngle
                        } else {
                            rotationAngle = newRotationAngle
                        }
                        
                    default:
                        break
                    }
                    
                    // 更新value - 使用格式化后的显示值
                    let calculatedValue = calculateCurrentValue(angle: rotationAngle)
                    let formattedValue = Double(ZoomUtils.formatZoomFactor(calculatedValue)) ?? calculatedValue
                    value = formattedValue
                    onValueChanged?(formattedValue)
                }
                .onEnded { _ in
                    isTouching = false
                    onTouchEnded?()
                    // 根据不同模式使用不同的存储key
                    let storageKey: String
                    switch config.mode {
                    case .exposure:
                        storageKey = "LastExposureDialRotationAngle"
                    case .temperature:
                        storageKey = "LastTemperatureDialRotationAngle"
                    case .tint:
                        storageKey = "LastTintDialRotationAngle"
                    case .iso:
                        storageKey = "LastISODialRotationAngle"
                    case .shutter:
                        storageKey = "LastShutterDialRotationAngle"
                    case .focus:
                        storageKey = "LastFocusDialRotationAngle"
                    default:
                        storageKey = "LastZoomDialRotationAngle"
                    }
                    UserDefaults.standard.set(rotationAngle, forKey: storageKey)
                }
        )
        .onAppear {
            // 显示动画
            animationManager.showDialAnimation(from: .zero)
        }
        .onDisappear {
            // 重置动画状态
            animationManager.resetDialAnimation()
        }
    }
    
    private func getProgressAngle() -> CGFloat {
        let progress = (value - config.minValue) / (config.maxValue - config.minValue)
        return CGFloat(progress) * 180
    }
    
    private func isMainTick(progress: CGFloat, mainTickCount: Int) -> Bool {
        let mainTicks = (0..<mainTickCount).map { CGFloat($0) / CGFloat(mainTickCount - 1) }
        return mainTicks.contains(progress)
    }
    
    // 新增：通过变焦倍率设置旋转角度
    func setZoomFactor(_ targetZoom: Double, animated: Bool = true) {
        // 获取角度分布
        let segments = ZoomUtils.calculateAngleDistribution(levels: calculationLevels)
        
        // 遍历每个区间找到目标角度
        for segment in segments {
            let startValue = Double(segment.startLens.replacingOccurrences(of: "x", with: "")) ?? 1.0
            let endValue = Double(segment.endLens.replacingOccurrences(of: "x", with: "")) ?? 1.0
            
            // 检查目标变焦值是否在当前区间
            if targetZoom >= endValue && targetZoom <= startValue {
                // 计算目标角度
                let progress = (targetZoom - endValue) / (startValue - endValue)
                let targetAngle = segment.endAngle + (segment.startAngle - segment.endAngle) * (1 - progress)
                
                // 设置旋转角度
                if animated {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        rotationAngle = -(90 - targetAngle)
                    }
                } else {
                    rotationAngle = -(90 - targetAngle)
                }
                
                // 更新value
                value = targetZoom
                onValueChanged?(targetZoom)
                
                // 保存当前角度
                let storageKey: String
                switch config.mode {
                case .exposure:
                    storageKey = "LastExposureDialRotationAngle"
                case .temperature:
                    storageKey = "LastTemperatureDialRotationAngle"
                case .tint:
                    storageKey = "LastTintDialRotationAngle"
                case .iso:
                    storageKey = "LastISODialRotationAngle"
                case .shutter:
                    storageKey = "LastShutterDialRotationAngle"
                case .focus:
                    storageKey = "LastFocusDialRotationAngle"
                default:
                    storageKey = "LastZoomDialRotationAngle"
                }
                UserDefaults.standard.set(rotationAngle, forKey: storageKey)
                break
            }
        }
    }
    
    /// 获取当前值对应的角度
    private func getAngleForValue() -> Double {
        if config.mode == .zoom {
            // 变焦刻度盘的角度计算
            return 0 // 暂时返回默认值，需要具体实现
        } else if config.mode == .exposure {
            // 曝光刻度盘的角度计算
            return 0 // 暂时返回默认值，需要具体实现
        } else if config.mode == .temperature {
            // 色温刻度盘的角度计算
            return 0 // 暂时返回默认值，需要具体实现
        } else if config.mode == .tint {
            // 色调刻度盘的角度计算
            return 0 // 暂时返回默认值，需要具体实现
        } else if config.mode == .iso {
            // 修改：使用新的角度计算逻辑
            if let isoConfig = config as? ISODialConfiguration {
                // 使用设备支持的ISO范围
                let minISO = viewModel.state.deviceMinISO
                let maxISO = viewModel.state.deviceMaxISO
                
                // ISO值归一化
                let logMin = log2(minISO)
                let logMax = log2(maxISO)
                let logISO = log2(isoConfig.currentValue)
                let normalizedValue = (logISO - logMin) / (logMax - logMin)
                
                // 映射到0-180度
                return normalizedValue * 180.0
            }
        }
        
        return 0
    }
}

// 刻度线视图 - 添加设备参数
private struct TickMarksView: View {
    private let totalAngleRange: Double = 90.0
    
    let mainTickCount: Int
    let subTickCount: Int
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let displayLevels: [ZoomLevel]  // 显示用的刻度
    let calculationLevels: [ZoomLevel]  // 计算用的刻度
    let device: AVCaptureDevice?  // 改为可选类型
    let currentAngle: Double  // 新增：当前角度
    let rotationAngle: Double  // 添加rotationAngle参数
    let dialMode: DialMode  // 添加刻度盘模式
    let exposureValue: Double?  // 添加曝光值
    let cameraState: CameraState?  // 新增：相机状态
    
    var body: some View {
        ZStack {
            // 主内容
            ZStack {
                if dialMode == .exposure {
                    // 曝光模式下的刻度线
                    ExposureTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        exposureValue: exposureValue ?? 0.0
                    )
                } else if dialMode == .temperature {
                    // 色温模式下的刻度线
                    TemperatureTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        temperatureValue: exposureValue ?? 0.0
                    )
                } else if dialMode == .tint {
                    // 色调模式下的刻度线
                    TintTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        tintValue: exposureValue ?? 0.0
                    )
                } else if dialMode == .iso {
                    // ISO模式下的刻度线
                    ISOTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        isoValue: exposureValue ?? 100.0,
                        minISO: cameraState?.deviceMinISO ?? 50.0,  // 使用设备支持的最小ISO
                        maxISO: cameraState?.deviceMaxISO ?? 12800.0  // 使用设备支持的最大ISO
                    )
                } else if dialMode == .shutter {
                    // 快门模式下的刻度线
                    ShutterTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        shutterValue: exposureValue ?? 1.0/125.0
                    )
                } else if dialMode == .focus {
                    // 焦点模式下的刻度线
                    let segments = FocusDialUtils.calculateFocusDistribution()
                    
                    ZStack {
                        // 主刻度线
                        ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                            Group {
                                // 起始刻度 - 只显示64米及以下的值，以及最后一个无穷远
                                if segment.startValue <= 64.0 || segment.startValue.isInfinite {
                                    MainTickView(
                                        index: i,
                                        dialWidth: dialWidth,
                                        dialHeight: dialHeight,
                                        tickColor: tickColor,
                                        lens: FocusDialUtils.formatFocusValue(segment.startValue),
                                        angle: segment.startAngle + 90,
                                        device: nil,
                                        isHighlighted: abs(segment.startAngle + 90 - currentAngle) <= 0.5,
                                        zoomType: .optical,
                                        rotationAngle: rotationAngle,
                                        displayLevels: [],
                                        dialMode: .focus,
                                        exposureValue: nil
                                    )
                                }
                                
                                // 结束刻度 - 只显示无穷远
                                if i == segments.count - 1 && segment.endValue.isInfinite {
                                    MainTickView(
                                        index: i + 1,
                                        dialWidth: dialWidth,
                                        dialHeight: dialHeight,
                                        tickColor: tickColor,
                                        lens: FocusDialUtils.formatFocusValue(segment.endValue),
                                        angle: segment.endAngle + 90,
                                        device: nil,
                                        isHighlighted: abs(segment.endAngle + 90 - currentAngle) <= 0.5,
                                        zoomType: .optical,
                                        rotationAngle: rotationAngle,
                                        displayLevels: [],
                                        dialMode: .focus,
                                        exposureValue: nil
                                    )
                                }
                            }
                            
                            // 副刻度 - 包括100米的主刻度位置
                            if segment.startValue <= 64.0 || (segment.startValue >= 64.0 && segment.startValue <= 1000.0) {
                                ForEach(segment.subTickAngles, id: \.self) { angle in
                                    SubTickView(
                                        dialWidth: dialWidth,
                                        dialHeight: dialHeight,
                                        tickColor: tickColor,
                                        angle: angle + 90
                                    )
                                }
                                
                                // 如果是100米的位置，添加一个副刻度线
                                if segment.startValue == 100.0 {
                                    SubTickView(
                                        dialWidth: dialWidth,
                                        dialHeight: dialHeight,
                                        tickColor: tickColor,
                                        angle: segment.startAngle + 90
                                    )
                                }
                            }
                        }
                    }
                } else if dialMode == .aperture {
                    // 光圈模式下的刻度线
                    ApertureTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        device: device
                    )
                } else {
                    // 变焦模式下的刻度线
                    ZoomTickMarksView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        currentAngle: currentAngle,
                        rotationAngle: rotationAngle,
                        displayLevels: displayLevels,
                        calculationLevels: calculationLevels,
                        device: device
                    )
                }
            }
            
            // 使用新的遮罩视图
            DialMaskView(
                dialWidth: dialWidth,
                dialHeight: dialHeight,
                config: MaskUtils.MaskConfiguration.standard,
                rotationAngle: rotationAngle
            )
        }
        .compositingGroup()
    }
}

// 新增：曝光刻度线视图
private struct ExposureTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let exposureValue: Double
    
    var body: some View {
        let segments = calculateExposureAngleDistribution()
        
        ForEach(segments.indices, id: \.self) { i in
            let segment = segments[i]
            
            // 主刻度线
            Group {
                // 起始刻度
                MainTickView(
                    index: i,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    lens: String(format: "%.1f", segment.startValue),
                    angle: segment.startAngle,
                    device: nil,
                    isHighlighted: isAngleHighlighted(segment.startAngle),
                    zoomType: .optical,
                    rotationAngle: rotationAngle,
                    displayLevels: [],
                    dialMode: .exposure,
                    exposureValue: exposureValue
                )
                
                // 结束刻度（最后一个段的结束刻度）
                if i == segments.count - 1 {
                    MainTickView(
                        index: i + 1,
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        lens: String(format: "%.1f", segment.endValue),
                        angle: segment.endAngle,
                        device: nil,
                        isHighlighted: isAngleHighlighted(segment.endAngle),
                        zoomType: .optical,
                        rotationAngle: rotationAngle,
                        displayLevels: [],
                        dialMode: .exposure,
                        exposureValue: exposureValue
                    )
                }
            }
            
            // 副刻度线
            ForEach(segment.subTickAngles, id: \.self) { angle in
                SubTickView(
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    angle: angle
                )
            }
        }
    }
    
    // 判断角度是否需要高亮
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        let tolerance: Double = 0.5
        return abs(angle - (180 - currentAngle)) <= tolerance
    }
}

// 主刻度视图 - 修改设备参数类型
private struct MainTickView: View {
    let index: Int
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let lens: String
    let angle: Double
    let device: AVCaptureDevice?
    let isHighlighted: Bool
    let zoomType: ZoomType
    let rotationAngle: Double  // 添加旋转角度参数
    let displayLevels: [ZoomLevel]  // 添加 displayLevels 参数
    let dialMode: DialMode  // 添加刻度盘模式
    let exposureValue: Double?  // 添加曝光值
    
    // 获取实际焦距
    private var focalLength: String {
        if dialMode == .exposure {
            // 修改：使用公共格式化函数
            return formatExposureValue(Double(lens) ?? 0.0, forMainTick: true)
        }
        // 获取焦距，如果没有设备则使用默认值
        let focalLength: Float
        if let device = device {
            focalLength = CameraLensManager.shared.getFocalLengthForZoom(lens, device: device)
        } else {
            // 使用默认值
            let zoomFactor = Float(lens.replacingOccurrences(of: "x", with: "")) ?? 1.0
            focalLength = zoomFactor == 0.5 ? 13.0 : 26.0 * zoomFactor
        }
        return String(format: "%.0fmm", focalLength)
    }
    
    // 计算位置
    private func calculatePoint(radius: CGFloat, angle: Double) -> CGPoint {
        let radian = angle * .pi / 180.0
        let x = dialWidth/2 + radius * cos(radian)
        let y = dialHeight - radius * sin(radian)
        return CGPoint(x: x, y: y)
    }
    
    // 计算 VStack 的总高度
    private var stackHeight: CGFloat {
        let mainTextSize = UIScreen.main.bounds.width * UIConstants.dialTextSize
        let subTextSize = mainTextSize * 0.8
        let spacing: CGFloat = 2
        return mainTextSize + subTextSize + spacing
    }
    
    // 计算 VStack 的位置
    private var stackPosition: CGPoint {
        return calculatePoint(
            radius: dialHeight - (UIScreen.main.bounds.height * UIConstants.dialEdgeDistance),
            angle: angle
        )
    }
    
    // 修改角度差计算
    private var diffAngle: Double {
        abs(angle - 90 - rotationAngle)  // 计算与指示器的角度差，考虑旋转角度
    }
    
    // 修改阈值计算
    private var maxDiffAngle: Double {
        // 曝光模式使用固定阈值
        if dialMode == .exposure {
            return 10.0  // 改为10度
        }
        
        // 色温模式使用固定阈值
        if dialMode == .temperature {
            return 20.0  // 改为20度
        }
        
        // 色调模式使用固定阈值
        if dialMode == .tint {
            return 10.0  // 设置为10度
        }
        
        // ISO模式使用动态阈值
        if dialMode == .iso {
            // 根据旋转角度计算动态阈值
            // rotationAngle范围：-90度(12800) 到 90度(50)
            // 总范围180度
            let totalAngle = 180.0
            let minThreshold = 10.0  // 右侧最小阈值(低ISO值)
            let maxThreshold = 15.0  // 左侧最大阈值(高ISO值)
            
            // 将旋转角度映射到0-1范围
            // -90度映射到1（左侧，高ISO值）
            // 90度映射到0（右侧，低ISO值）
            let progress = (-rotationAngle + 90.0) / totalAngle
            
            // 线性插值计算当前阈值
            return minThreshold + (maxThreshold - minThreshold) * progress
        }
        
        // 快门模式使用动态阈值
        if dialMode == .shutter {
            // 根据旋转角度计算动态阈值
            // rotationAngle范围：-135度(8秒) 到 105度(1/8000秒)
            // 总范围240度
            let totalAngle = 240.0
            let minThreshold = 10.0  // 右侧最小阈值(简单数值如2",4",8")
            let maxThreshold = 20.0  // 左侧最大阈值(复杂数值如1/4000,1/8000)
            
            // 将旋转角度映射到0-1范围
            // -135度映射到0（右侧，简单数值）
            // 105度映射到1（左侧，复杂数值）
            let progress = (rotationAngle + 135.0) / totalAngle
            
            // 线性插值计算当前阈值
            return minThreshold + (maxThreshold - minThreshold) * progress
        }
        
        // 变焦模式保持原有逻辑
        if angle >= 90 {  // 0.5x-1x 区间
            return 12.0
        } else if angle == 90 {  // 1x 位置
            return 10.0
        } else {  // 1x 往右的区间
            let baseThreshold = 10.0
            // 找到当前刻度在 displayLevels 中的位置
            if let currentIndex = displayLevels.firstIndex(where: { $0.value + "x" == lens }) {
                // 只有在1x右边的刻度才需要递减阈值
                if let oneXIndex = displayLevels.firstIndex(where: { $0.value == "1" }) {
                    if currentIndex > oneXIndex {
                        // 计算与1x的段数
                        let segmentsFromOne = Double(currentIndex - oneXIndex)
                        return baseThreshold * pow(0.8, segmentsFromOne)
                    }
                }
            }
            return baseThreshold
        }
    }
    
    // 修改映射函数
    private var ratio: Double {
        min(1, diffAngle / maxDiffAngle)  // 映射到0-1范围
    }
    
    var body: some View {
        ZStack {
            // 刻度线
            Path { path in
                let offset: CGFloat = UIScreen.main.bounds.height * UIConstants.dialTickOffset
                let mainTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialMainTickLength
                
                let startPoint = calculatePoint(radius: dialHeight - offset, angle: angle)
                let endPoint = calculatePoint(radius: dialHeight - offset - mainTickLength, angle: angle)
                
                path.move(to: startPoint)
                path.addLine(to: endPoint)
            }
            .stroke(tickColor, lineWidth: UIConstants.dialTickWidth)
            
            // VStack 文本
            VStack(spacing: UIConstants.zoomDisplayVerticalSpacing) {
                if dialMode == .exposure {
                    // 曝光值显示
                    Text(focalLength)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)  // 使用平方函数
                        .opacity(ratio)  // 保持线性变化
                } else if dialMode == .temperature {
                    // 色温值显示
                    Text("\(lens)K")
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.temperatureDisplayFontSize, weight: UIConstants.temperatureDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(ratio)
                } else if dialMode == .tint {
                    // 色调值显示
                    Text(lens)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.tintDisplayFontSize, weight: UIConstants.tintDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(ratio)
                } else if dialMode == .iso {
                    // ISO值显示
                    let isoValue = Double(lens) ?? 0.0
                    let isHighISO = isoValue >= 6400
                    Text(lens)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(isHighISO ? 0 : ratio)
                } else if dialMode == .shutter {
                    // 快门值显示
                    let shutterValue = Double(lens.replacingOccurrences(of: "1/", with: "").replacingOccurrences(of: "\"", with: "")) ?? 0.0
                    let isHighSpeed = shutterValue >= 1000 || (1.0/shutterValue) >= 1000
                    Text(lens)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.shutterDisplayFontSize, weight: UIConstants.shutterDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(isHighSpeed ? 0 : ratio)
                } else if dialMode == .focus {
                    // 焦点值显示
                    Text(lens)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.focusDisplayFontSize, weight: UIConstants.focusDisplayWeight))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(ratio)
                } else if dialMode == .aperture {
                    // 光圈值显示
                    Text(lens)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.dialTextSize))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .scaleEffect(ratio * ratio * 1)
                        .opacity(ratio)
                } else {
                    // 变焦倍率显示
                    HStack(alignment: .lastTextBaseline, spacing: UIConstants.zoomDisplaySpacing) {
                        Text(lens.replacingOccurrences(of: "x", with: ""))
                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                        Image(systemName: "multiply")
                            .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayMultiplierSize, weight: UIConstants.zoomDisplayMultiplierWeight))
                    }
                    .frame(
                        width: UIScreen.main.bounds.width * UIConstants.zoomHStackWidth,
                        height: UIScreen.main.bounds.height * UIConstants.zoomHStackHeight
                    )
                    .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                    .scaleEffect(ratio * ratio * 1)  // 使用平方函数
                    .opacity(ratio)  // 保持线性变化
                    
                    // 焦距值显示
                    Text(focalLength)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.dialTextSize, weight: .medium))
                        .foregroundColor(isHighlighted ? UIConstants.dialIndicatorColor : tickColor)
                        .opacity(isHighlighted ? 1.0 : (zoomType == .optical ? 0.25 : 0))
                }
            }
            .rotationEffect(.degrees(-angle + 90))
            .position(stackPosition)
        }
    }
}

// 副刻度视图
private struct SubTickView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let angle: Double  // 直接接收计算好的角度
    
    var body: some View {
        let radian = angle * Double.pi / 180
        
        Path { path in
            let offset: CGFloat = UIScreen.main.bounds.height * UIConstants.dialTickOffset
            let subTickLength: CGFloat = UIScreen.main.bounds.height * UIConstants.dialSubTickLength
            
            let startX = dialWidth/2 + cos(radian) * (dialHeight - offset)
            let startY = dialHeight - sin(radian) * (dialHeight - offset)
            let endX = dialWidth/2 + cos(radian) * (dialHeight - offset - subTickLength)
            let endY = dialHeight - sin(radian) * (dialHeight - offset - subTickLength)
            
            path.move(to: CGPoint(x: startX, y: startY))
            path.addLine(to: CGPoint(x: endX, y: endY))
        }
        .stroke(tickColor.opacity(UIConstants.dialSubTickOpacity), lineWidth: UIConstants.dialTickWidth)  // 使用常量设置透明度
    }
}

// 指示器视图
private struct IndicatorView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let indicatorColor: Color
    let currentValue: String
    let dialMode: DialMode
    let exposureValue: Double?
    let rotationAngle: Double
    // 新增：设备支持的ISO范围
    let minISO: Double?
    let maxISO: Double?
    // 新增：相机状态和视图模型
    let cameraState: CameraState?
    let cameraViewModel: CameraViewModel
    
    private var formattedValue: String {
        if dialMode == .exposure {
            return formatExposureValue(exposureValue ?? 0.0)
        } else if dialMode == .temperature {
            let temperature = TemperatureDialUtils.calculateTemperatureValue(angle: rotationAngle)
            return String(format: "%.0fK", temperature)
        } else if dialMode == .tint {
            // 色调值显示
            let tintValue = calculateTintValue(angle: rotationAngle)
            // 如果是0，不显示正负号
            if abs(tintValue) < 0.5 {
                return "0"
            }
            // 显示整数值和正负号
            return String(format: "%+.0f", tintValue)
        } else if dialMode == .iso {
            // ISO值显示
            // 使用设备支持的ISO范围
            let deviceMinISO = minISO ?? 50.0
            let deviceMaxISO = maxISO ?? 12800.0
            let isoValue = ISODialUtils.calculateISOValue(
                angle: rotationAngle, 
                minISO: deviceMinISO, 
                maxISO: deviceMaxISO
            )
            // 显示ISO值
            return String(format: "%.0f", isoValue)
        } else if dialMode == .shutter {
            // 快门值显示
            let shutterValue = ShutterDialUtils.calculateShutterSpeed(angle: rotationAngle)
            return ShutterDialUtils.formatShutterValue(shutterValue)
        } else if dialMode == .focus {
            // 焦点值显示
            let focusValue = FocusDialUtils.calculateFocusDistance(angle: rotationAngle)
            return FocusDialUtils.formatFocusValue(focusValue)
        } else if dialMode == .aperture {
            // 光圈值显示
            if let device = cameraViewModel.getCurrentDevice() {
                return ApertureDialUtils.formatValue(Double(device.lensAperture))
            }
            return ApertureDialUtils.formatValue(1.8) // 默认值
        }
        // 其他情况（变焦等）
        if let value = Double(currentValue.replacingOccurrences(of: "x", with: "")) {
            return ZoomUtils.formatZoomFactor(value)
        }
        return currentValue.replacingOccurrences(of: "x", with: "")
    }
    
    // 获取数值的位置
    private var valuePosition: CGPoint {
        let x = dialWidth/2 + cos(90 * .pi / 180) * (dialHeight - (UIScreen.main.bounds.height * UIConstants.dialEdgeDistance))
        let y = dialHeight - sin(90 * .pi / 180) * (dialHeight - (UIScreen.main.bounds.height * UIConstants.dialEdgeDistance))
        return CGPoint(x: x, y: y)
    }
    
    // 获取按钮的位置（在数值下方）
    private var buttonPosition: CGPoint {
        let valuePos = valuePosition
        return CGPoint(x: valuePos.x, y: valuePos.y + UIScreen.main.bounds.height * 0.075)
    }
    
    // 确定当前模式是否需要显示 A 按钮
    private var shouldShowAutoButton: Bool {
        switch dialMode {
        case .exposure, .temperature, .tint, .iso, .shutter, .focus:
            return true
        default:
            return false
        }
    }
    
    var body: some View {
        ZStack {
            // 三角形指示器
            Path { path in
                let width: CGFloat = UIScreen.main.bounds.width * 0.015
                let height: CGFloat = UIScreen.main.bounds.height * 0.015
                let offset: CGFloat = UIScreen.main.bounds.height * 0.025
                
                let radian = 90 * Double.pi / 180
                let x = dialWidth/2 + cos(radian) * (dialHeight - offset)
                let y = dialHeight - sin(radian) * (dialHeight - offset)
                
                let topPoint = CGPoint(x: x, y: y)
                let leftPoint = CGPoint(x: x - width/2, y: y - height)
                let rightPoint = CGPoint(x: x + width/2, y: y - height)
                
                path.move(to: topPoint)
                path.addLine(to: leftPoint)
                path.addLine(to: rightPoint)
                path.closeSubpath()
            }
            .fill(indicatorColor)
            
            // 数值显示 - 保持原始位置不变
            if dialMode == .exposure {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .temperature {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.temperatureDisplayFontSize, weight: UIConstants.temperatureDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .tint {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.tintDisplayFontSize, weight: UIConstants.tintDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .iso {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.isoDisplayFontSize, weight: UIConstants.isoDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .shutter {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.shutterDisplayFontSize, weight: UIConstants.shutterDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .focus {
                Text(formattedValue)
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.focusDisplayFontSize, weight: UIConstants.focusDisplayWeight))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else if dialMode == .aperture {
                Text(ApertureDialUtils.formatValue(Double(cameraViewModel.getCurrentDevice()?.lensAperture ?? 1.8)))
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.dialTextSize))
                    .foregroundColor(indicatorColor)
                    .position(valuePosition)
            } else {
                HStack(alignment: .lastTextBaseline, spacing: UIConstants.zoomDisplaySpacing) {
                    Text(formattedValue)
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                    Image(systemName: "multiply")
                        .font(.system(size: UIScreen.main.bounds.height * UIConstants.zoomDisplayMultiplierSize, weight: UIConstants.zoomDisplayMultiplierWeight))
                }
                .frame(
                    width: UIScreen.main.bounds.width * UIConstants.zoomHStackWidth,
                    height: UIScreen.main.bounds.height * UIConstants.zoomHStackHeight
                )
                .foregroundColor(indicatorColor)
                .position(valuePosition)
                
                Text("\(getFocalLength())mm")
                    .font(.system(size: UIScreen.main.bounds.height * UIConstants.dialTextSize, weight: .medium))
                    .foregroundColor(indicatorColor)
                    .opacity(0)
                    .position(
                        x: valuePosition.x,
                        y: valuePosition.y + UIScreen.main.bounds.height * 0.02
                    )
            }
            
            // A 按钮 - 单独定位
            if shouldShowAutoButton {
                Button(action: {
                    resetToAutoValue()
                }) {
                    Image(systemName: "a.circle.fill")
                        .resizable()
                        .scaledToFit()
                        .frame(width: UIScreen.main.bounds.height * UIConstants.functionIconSize,
                               height: UIScreen.main.bounds.height * UIConstants.functionIconSize)
                        .foregroundColor(.white)
                }
                .position(buttonPosition)
            }
        }
    }
    
    // 获取焦距值
    private func getFocalLength() -> String {
        let zoomFactor = Double(currentValue.replacingOccurrences(of: "x", with: "")) ?? 1.0
        let focalLength = zoomFactor == 0.5 ? 13.0 : 26.0 * zoomFactor
        return String(format: "%.0f", focalLength)
    }
    
    // 重置当前参数到自动值
    private func resetToAutoValue() {
        withAnimation(AnimationConstants.standardSpring) {
            switch dialMode {
            case .exposure:
                cameraViewModel.updateExposure(0.0)
            case .temperature:
                cameraViewModel.updateTemperature(5600.0)
            case .tint:
                cameraViewModel.updateTint(0.0)
            case .iso:
                cameraViewModel.updateISO(100.0)
            case .shutter:
                cameraViewModel.updateShutter(ShutterDialUtils.defaultShutterValue)
            case .focus:
                cameraViewModel.updateFocus(0.5)
            default:
                break
            }
        }
    }
}

// 角度分配算法
private func calculateAngleDistribution(levels: [ZoomLevel]) -> [AngleSegment] {
    let firstSegment = 20.0  // 第一段固定20度
    let q = 0.8  // 公比
    
    var segments: [AngleSegment] = []
    guard levels.count >= 2 else { return segments }
    
    // 1. 将变焦值转换为数值
    let zoomFactors = levels.map { Double($0.value.replacingOccurrences(of: "x", with: "")) ?? 0.0 }
    
    // 2. 计算角度
    var integerAngles: [Double] = []
    
    // 找到1x的位置
    if let oneXIndex = zoomFactors.firstIndex(of: 1.0) {
        // 从1x位置开始计算
        var currentAngle = 90.0  // 1x固定在90度
        integerAngles = Array(repeating: 0.0, count: levels.count)
        integerAngles[oneXIndex] = 90.0
        
        // 计算1x右边的角度
        var segmentIndex = 0
        for i in (oneXIndex + 1)..<zoomFactors.count {
            let segmentAngle = firstSegment * pow(q, Double(segmentIndex))
            currentAngle -= segmentAngle
            integerAngles[i] = currentAngle
            segmentIndex += 1
        }
        
        // 计算1x左边的角度
        currentAngle = 90.0
        segmentIndex = 0
        for i in (0..<oneXIndex).reversed() {
            let segmentAngle = firstSegment * pow(q, Double(segmentIndex))
            currentAngle += segmentAngle
            integerAngles[i] = currentAngle
            segmentIndex += 1
        }
    }
    
    // 3. 创建角度段
    for i in 0..<(levels.count - 1) {
        let startValue = zoomFactors[i]
        let endValue = zoomFactors[i + 1]
        
        // 计算这个区间需要多少个副刻度（每0.1一个刻度）
        let subTickCount = Int((endValue - startValue) * 10) - 1
        
        // 计算副刻度的角度
        var subTickAngles: [Double] = []
        if subTickCount > 0 {
            let segmentAngle = integerAngles[i] - integerAngles[i + 1]
            let tickInterval = segmentAngle / Double(subTickCount + 1)
            for j in 1...subTickCount {
                let tickAngle = integerAngles[i] - tickInterval * Double(j)
                subTickAngles.append(tickAngle)
            }
        }
        
        segments.append(
            AngleSegment(
                startLens: levels[i].value,
                endLens: levels[i + 1].value,
                startAngle: integerAngles[i],
                endAngle: integerAngles[i + 1],
                subTickAngles: subTickAngles
            )
        )
    }
    
    return segments
}

// 修改获取配置的函数
private func getZoomConfiguration(for availableLenses: Set<String>, mode: CameraMode) -> ZoomConfiguration {
    return ZoomConfiguration(availableLenses: availableLenses, mode: mode)
}

// 曝光角度分配算法
private func calculateExposureAngleDistribution() -> [ExposureSegment] {
    return ExposureDialUtils.calculateAngleDistribution()
}

// 根据旋转角度计算当前曝光值
private func calculateExposureValue(angle: Double) -> Double {
    return ExposureDialUtils.calculateValue(angle: angle)
}

// 添加格式化工具函数
private func formatExposureValue(_ value: Double, forMainTick: Bool = false) -> String {
    return ExposureDialUtils.formatValue(value, forMainTick: forMainTick)
}

// 根据旋转角度计算当前色调值
private func calculateTintValue(angle: Double) -> Double {
    return TintDialUtils.calculateValue(angle: angle)
}

// 添加色温刻度线视图
private struct TemperatureTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let temperatureValue: Double
    
    var body: some View {
        let segments = TemperatureDialUtils.calculateTemperatureAngleDistribution()
        
        ZStack {
            // 主刻度线
            ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                Group {
                    // 起始刻度
                    MainTickView(
                        index: i,
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        lens: String(format: "%.0f", segment.startValue),
                        angle: segment.startAngle,
                        device: nil,
                        isHighlighted: isAngleHighlighted(segment.startAngle),
                        zoomType: .optical,
                        rotationAngle: rotationAngle,
                        displayLevels: [],
                        dialMode: .temperature,
                        exposureValue: nil
                    )
                    
                    // 结束刻度（最后一个段的结束刻度）
                    if i == segments.count - 1 {
                        MainTickView(
                            index: i + 1,
                            dialWidth: dialWidth,
                            dialHeight: dialHeight,
                            tickColor: tickColor,
                            lens: String(format: "%.0f", segment.endValue),
                            angle: segment.endAngle,
                            device: nil,
                            isHighlighted: isAngleHighlighted(segment.endAngle),
                            zoomType: .optical,
                            rotationAngle: rotationAngle,
                            displayLevels: [],
                            dialMode: .temperature,
                            exposureValue: nil
                        )
                    }
                }
            }
            
            // 副刻度线
            ForEach(segments.indices, id: \.self) { i in
                ForEach(segments[i].subTickAngles, id: \.self) { angle in
                    SubTickView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        angle: angle
                    )
                }
            }
        }
    }
    
    // 判断角度是否需要高亮
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        let tolerance: Double = 0.5
        return abs(angle - (180 - currentAngle)) <= tolerance
    }
}

// 添加色调刻度线视图
private struct TintTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let tintValue: Double
    
    var body: some View {
        let segments = TintDialUtils.calculateDistribution()
        
        ZStack {
            // 主刻度线
            ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                Group {
                    // 起始刻度
                    MainTickView(
                        index: i,
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        lens: String(format: "%.0f", segment.startValue),
                        angle: segment.startAngle,
                        device: nil,
                        isHighlighted: isAngleHighlighted(segment.startAngle),
                        zoomType: .optical,
                        rotationAngle: rotationAngle,
                        displayLevels: [],
                        dialMode: .tint,
                        exposureValue: nil
                    )
                    
                    // 结束刻度（最后一个段的结束刻度）
                    if i == segments.count - 1 {
                        MainTickView(
                            index: i + 1,
                            dialWidth: dialWidth,
                            dialHeight: dialHeight,
                            tickColor: tickColor,
                            lens: String(format: "%.0f", segment.endValue),
                            angle: segment.endAngle,
                            device: nil,
                            isHighlighted: isAngleHighlighted(segment.endAngle),
                            zoomType: .optical,
                            rotationAngle: rotationAngle,
                            displayLevels: [],
                            dialMode: .tint,
                            exposureValue: nil
                        )
                    }
                }
            }
            
            // 副刻度线
            ForEach(segments.indices, id: \.self) { i in
                ForEach(segments[i].subTickAngles, id: \.self) { angle in
                    SubTickView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        angle: angle
                    )
                }
            }
        }
    }
    
    // 判断角度是否需要高亮
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        let tolerance: Double = 0.5
        return abs(angle - (180 - currentAngle)) <= tolerance
    }
    
    // 计算色调刻度的角度分布
    private func calculateTintAngleDistribution() -> [TintSegment] {
        return TintDialUtils.calculateDistribution()
    }
}

// 添加ISO刻度线视图
private struct ISOTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let isoValue: Double
    let minISO: Double  // 设备支持的最小ISO
    let maxISO: Double  // 设备支持的最大ISO
    
    var body: some View {
        let segments = ISODialUtils.calculateISODistribution(minISO: minISO, maxISO: maxISO)
        
        ZStack {
            // 主刻度线
            ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                Group {
                    // 起始刻度
                    MainTickView(
                        index: i,
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        lens: String(format: "%.0f", segment.startValue),
                        angle: segment.startAngle + 90, // 修改：调整角度映射，0度对应底部中心
                        device: nil,
                        isHighlighted: isAngleHighlighted(segment.startAngle + 90),
                        zoomType: .optical,
                        rotationAngle: rotationAngle,
                        displayLevels: [],
                        dialMode: .iso,
                        exposureValue: nil
                    )
                    
                    // 结束刻度（最后一个段的结束刻度）
                    if i == segments.count - 1 {
                        MainTickView(
                            index: i + 1,
                            dialWidth: dialWidth,
                            dialHeight: dialHeight,
                            tickColor: tickColor,
                            lens: String(format: "%.0f", segment.endValue),
                            angle: segment.endAngle + 90, // 修改：调整角度映射，0度对应底部中心
                            device: nil,
                            isHighlighted: isAngleHighlighted(segment.endAngle + 90),
                            zoomType: .optical,
                            rotationAngle: rotationAngle,
                            displayLevels: [],
                            dialMode: .iso,
                            exposureValue: nil
                        )
                    }
                }
                
                // 副刻度（1/3档和2/3档）
                ForEach(segment.subTickAngles.indices, id: \.self) { j in
                    let subAngle = segment.subTickAngles[j]
                    SubTickView(
                        dialWidth: dialWidth,
                        dialHeight: dialHeight,
                        tickColor: tickColor,
                        angle: subAngle + 90 // 修改：调整角度映射，0度对应底部中心
                    )
                }
            }
        }
    }
    
    /// 判断角度是否需要高亮显示
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        // 考虑角度范围为0-180度，转换为相对视图的坐标系
        let range = 5.0
        return abs(angle - currentAngle) < range
    }
}
// 快门刻度线视图
private struct ShutterTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let shutterValue: Double
    
    var body: some View {
        let segments = ShutterDialUtils.calculateShutterDistribution()
        
        ZStack {
            // 主刻度线
            ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                ShutterMainTickGroup(
                    index: i,
                    segment: segment,
                    isLastSegment: i == segments.count - 1,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    currentAngle: currentAngle,
                    rotationAngle: rotationAngle
                )
                
                // 副刻度
                ShutterSubTickGroup(
                    segment: segment,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    currentAngle: currentAngle
                )
            }
        }
    }
}

// 快门主刻度组视图
private struct ShutterMainTickGroup: View {
    let index: Int
    let segment: ShutterSegment
    let isLastSegment: Bool
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    
    var body: some View {
        Group {
            // 起始刻度
            MainTickView(
                index: index,
                dialWidth: dialWidth,
                dialHeight: dialHeight,
                tickColor: tickColor,
                lens: ShutterDialUtils.formatShutterValue(segment.startValue),
                angle: segment.startAngle + 90,
                device: nil,
                isHighlighted: false,  // 移除高亮效果
                zoomType: .optical,
                rotationAngle: rotationAngle,
                displayLevels: [],
                dialMode: .shutter,
                exposureValue: nil
            )
            
            // 结束刻度（最后一个段的结束刻度）
            if isLastSegment {
                MainTickView(
                    index: index + 1,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    lens: ShutterDialUtils.formatShutterValue(segment.endValue),
                    angle: segment.endAngle + 90,
                    device: nil,
                    isHighlighted: false,  // 移除高亮效果
                    zoomType: .optical,
                    rotationAngle: rotationAngle,
                    displayLevels: [],
                    dialMode: .shutter,
                    exposureValue: nil
                )
            }
        }
    }
    
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        let diffAngle = abs(angle - currentAngle)
        return diffAngle < 10.0
    }
}

// 快门副刻度组视图
private struct ShutterSubTickGroup: View {
    let segment: ShutterSegment
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    
    var body: some View {
        ForEach(segment.subTickAngles.indices, id: \.self) { j in
            SubTickView(
                dialWidth: dialWidth,
                dialHeight: dialHeight,
                tickColor: tickColor,
                angle: segment.subTickAngles[j] + 90
            )
        }
    }
}

// 添加变焦刻度线视图
private struct ZoomTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let displayLevels: [ZoomLevel]  // 显示用的刻度
    let calculationLevels: [ZoomLevel]  // 计算用的刻度
    let device: AVCaptureDevice?  // 相机设备
    
    var body: some View {
        ZStack {
            // 主刻度
            ForEach(0..<displayLevels.count, id: \.self) { i in
                MainTickView(
                    index: i,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    lens: displayLevels[i].value,
                    angle: getAngleForZoom(displayLevels[i].value),
                    device: device,
                    isHighlighted: isAngleHighlighted(getAngleForZoom(displayLevels[i].value)),
                    zoomType: displayLevels[i].type,
                    rotationAngle: rotationAngle,
                    displayLevels: displayLevels,
                    dialMode: .zoom,
                    exposureValue: nil
                )
            }
            
            // 副刻度
            ForEach(0..<(displayLevels.count - 1), id: \.self) { i in
                let startValue = Double(displayLevels[i].value.replacingOccurrences(of: "x", with: "")) ?? 0.0
                let endValue = Double(displayLevels[i + 1].value.replacingOccurrences(of: "x", with: "")) ?? 0.0
                let subTickCount = Int((endValue - startValue) * 10) - 1
                
                if subTickCount > 0 {
                    let startAngle = getAngleForZoom(displayLevels[i].value)
                    let endAngle = getAngleForZoom(displayLevels[i + 1].value)
                    let segmentAngle = startAngle - endAngle
                    let tickInterval = segmentAngle / Double(subTickCount + 1)
                    
                    ForEach(1...subTickCount, id: \.self) { j in
                        let angle = startAngle - tickInterval * Double(j)
                        SubTickView(
                            dialWidth: dialWidth,
                            dialHeight: dialHeight,
                            tickColor: tickColor,
                            angle: angle
                        )
                    }
                }
            }
        }
    }
    
    // 新增：判断角度是否需要高亮
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        // 允许0.5度的误差范围
        let tolerance: Double = 0.5
        return abs(angle - (180 - currentAngle)) <= tolerance
    }
    
    // 根据变焦值获取对应的角度
    private func getAngleForZoom(_ zoom: String) -> Double {
        if let index = calculationLevels.firstIndex(where: { $0.value == zoom }) {
            return integerAngles[index]
        }
        return 90.0  // 默认返回90度
    }
    
    // 获取整数刻度角度
    private var integerAngles: [Double] {
        var angles: [Double] = []
        
        // 1x固定在90度
        angles.append(90.0)
        
        // 计算1x右边的刻度角度
        var currentAngle = 90.0
        let baseSegment = 20.0  // 基准距离
        
        // 使用计算用的完整序列来计算角度
        for i in 1...(calculationLevels.count - 1) {
            let segmentAngle = baseSegment * pow(0.8, Double(i - 1))
            currentAngle -= segmentAngle
            angles.append(currentAngle)
        }
        
        // 如果有0.5x，在开头添加对应角度
        if calculationLevels.first?.value == "0.5" {
            var leftAngle = 90.0
            leftAngle += baseSegment
            angles.insert(leftAngle, at: 0)
        }
        
        return angles
    }
}

// 添加光圈刻度线视图
private struct ApertureTickMarksView: View {
    let dialWidth: CGFloat
    let dialHeight: CGFloat
    let tickColor: Color
    let currentAngle: Double
    let rotationAngle: Double
    let device: AVCaptureDevice?
    
    var body: some View {
        let segments = ApertureDialUtils.calculateDistribution(aperture: Double(device?.lensAperture ?? 1.8))
        
        ZStack {
            // 主刻度线
            ForEach(Array(segments.enumerated()), id: \.offset) { i, segment in
                MainTickView(
                    index: i,
                    dialWidth: dialWidth,
                    dialHeight: dialHeight,
                    tickColor: tickColor,
                    lens: ApertureDialUtils.formatValue(segment.value),
                    angle: segment.angle,
                    device: device,
                    isHighlighted: isAngleHighlighted(segment.angle),
                    zoomType: .optical,
                    rotationAngle: rotationAngle,
                    displayLevels: [],
                    dialMode: .aperture,
                    exposureValue: nil
                )
            }
        }
    }
    
    // 判断角度是否需要高亮
    private func isAngleHighlighted(_ angle: Double) -> Bool {
        let tolerance: Double = 0.5
        return abs(angle - (180 - currentAngle)) <= tolerance
    }
}

