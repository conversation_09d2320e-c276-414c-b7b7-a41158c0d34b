import SwiftUI

struct ResolutionOptionsView: View {
    @ObservedObject var viewModel: CameraViewModel
    let screenHeight: CGFloat
    
    // 分辨率选项数据
    private let resolutionOptions: [(title: String, value: ResolutionMode)] = [
        ("4K", .res4K),
        ("4K DCI", .res4KDCI),
        ("2K DCI", .res2KDCI),
        ("1080p", .res1080p),
        ("720p", .res720p)
    ]
    
    // Pro分辨率选项
    private let proResolutionOptions: [ResolutionMode] = [
        .res4KDCI,  // 4K DCI为Pro选项
        .res2KDCI   // 2K DCI也设为Pro选项
    ]
    
    var body: some View {
        OptionGroupWithPro(
            options: resolutionOptions,
            selectedValue: viewModel.state.resolutionMode,
            onSelect: viewModel.selectResolutionMode,
            screenHeight: screenHeight,
            isVisible: viewModel.state.isResolutionOptionsVisible,
            proOptions: proResolutionOptions,
            position: .top,  // 设置为顶部位置
            isProUser: viewModel.isProUser  // 传递Pro用户状态
        )
    }
} 