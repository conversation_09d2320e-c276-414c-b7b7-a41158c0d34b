<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 923.08 282.55">
  <defs>
    <style>
      .cls-1 {
        clip-path: url(#clippath);
      }

      .cls-2 {
        fill: none;
      }

      .cls-2, .cls-3, .cls-4, .cls-5, .cls-6 {
        stroke-width: 0px;
      }

      .cls-3 {
        fill: #109dd9;
      }

      .cls-7 {
        clip-path: url(#clippath-1);
      }

      .cls-8 {
        clip-path: url(#clippath-4);
      }

      .cls-9 {
        clip-path: url(#clippath-3);
      }

      .cls-10 {
        clip-path: url(#clippath-2);
      }

      .cls-4 {
        fill: #000;
      }

      .cls-5 {
        fill: #e8e8ea;
      }

      .cls-6 {
        fill: #075daa;
      }
    </style>
    <clipPath id="clippath">
      <rect class="cls-2" y="0" width="923.08" height="282.55"/>
    </clipPath>
    <clipPath id="clippath-1">
      <rect class="cls-2" y="0" width="923.08" height="282.55"/>
    </clipPath>
    <clipPath id="clippath-2">
      <rect class="cls-2" y="0" width="923.08" height="282.55"/>
    </clipPath>
    <clipPath id="clippath-3">
      <rect class="cls-2" y="0" width="923.08" height="282.55"/>
    </clipPath>
    <clipPath id="clippath-4">
      <rect class="cls-2" y="0" width="923.08" height="282.55"/>
    </clipPath>
  </defs>
  <g id="g35861">
    <g class="cls-1">
      <path id="path35851" class="cls-4" d="M335.23,106.55h-49.09c-22.75,0-33.52-3.59-33.52-11.97v-38.31c0-7.18,10.78-11.97,33.52-11.97h49.09c22.75,0,33.52,3.59,33.52,11.97v38.31c0,8.38-10.78,11.97-33.52,11.97M335.23,124.51c40.71,0,61.06-10.78,61.06-31.13v-35.92c0-21.55-20.35-31.13-61.06-31.13h-49.09c-40.71,0-61.06,10.78-61.06,31.13v35.92c0,21.55,20.35,31.13,61.06,31.13h49.09Z"/>
    </g>
    <g class="cls-7">
      <path id="path35853" class="cls-4" d="M862.02,106.55h-49.09c-22.75,0-33.52-3.59-33.52-11.97v-38.31c0-7.18,10.78-11.97,33.52-11.97h49.09c22.75,0,33.52,3.59,33.52,11.97v38.31c0,8.38-10.78,11.97-33.52,11.97M862.02,124.51c40.71,0,61.06-10.78,61.06-31.13v-35.92c0-21.55-20.35-31.13-61.06-31.13h-49.09c-40.71,0-61.06,10.78-61.06,31.13v35.92c0,21.55,20.35,31.13,61.06,31.13h49.09Z"/>
    </g>
    <g class="cls-10">
      <path id="path35855" class="cls-4" d="M197.55,93.39v-25.14c0-4.79-3.59-7.18-10.78-7.18h-59.86c-5.99,0-9.58,4.79-9.58,9.58s4.79,9.58,9.58,9.58h40.71v13.17c0,3.59-4.79,8.38-14.37,9.58-7.18,2.39-13.17,2.39-19.16,2.39h-65.85c-26.34,0-39.51-4.79-39.51-13.17V31.13c0-8.38,15.56-11.97,46.69-11.97h17.96c7.18,0,10.78-3.59,10.78-9.58,1.2-5.99-2.39-9.58-9.58-9.58h-26.34c-15.56,0-29.93,1.2-43.1,5.99C9.58,10.78,0,19.16,0,31.13v62.26c0,11.97,8.38,20.35,23.94,25.14,11.97,3.59,26.34,5.99,44.3,5.99h65.85c15.56,0,29.93-2.39,40.71-5.99,14.37-5.99,22.75-14.37,22.75-25.14"/>
    </g>
    <g class="cls-9">
      <path id="path35857" class="cls-4" d="M596.23,49.09c0,9.58-13.17,13.17-38.31,13.17h-102.96V17.96h102.96c25.14,0,38.31,2.39,38.31,13.17v17.96ZM599.82,4.79C587.85,2.39,574.68,0,557.92,0h-123.32C429.81,0,426.22,3.59,426.22,8.38v108.95c0,5.99,3.59,8.38,10.78,8.38h8.38c7.18,0,10.78-2.39,10.78-8.38v-37.11h102.96c17.96,0,31.13-2.39,43.1-5.99,15.56-5.99,23.94-14.37,23.94-27.54v-19.16c-1.2-9.58-9.58-16.76-26.34-22.75"/>
    </g>
    <g class="cls-8">
      <path id="path35859" class="cls-4" d="M721.94,26.34c-25.14,0-35.92,0-51.48,5.99-14.37,5.99-20.35,15.56-20.35,25.14v59.86c0,4.79,3.59,8.38,9.58,8.38h8.38c5.99,0,9.58-2.39,9.58-8.38v-59.86c0-8.38,10.78-11.97,32.33-11.97h11.97c7.18,0,10.78-3.59,10.78-9.58-1.2-5.99-4.79-9.58-10.78-9.58"/>
    </g>
  </g>
  <path id="rect35863" class="cls-3" d="M0,155.64h199.94v126.91H0v-126.91Z"/>
  <path id="rect35865" class="cls-3" d="M240.65,155.64h199.94v126.91h-199.94v-126.91Z"/>
  <path id="rect35867" class="cls-6" d="M481.29,155.64h199.94v126.91h-199.94v-126.91Z"/>
  <path id="rect35869" class="cls-5" d="M721.94,155.64h199.94v126.91h-199.94v-126.91Z"/>
</svg>