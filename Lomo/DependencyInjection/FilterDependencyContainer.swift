// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 滤镜应用模块的依赖注入容器 - MVVM-S架构重构版
/// 管理滤镜模块的所有依赖关系，使用Actor模式和重构后的ViewModel
class FilterDependencyContainer {

    // MARK: - 单例
    static let shared = FilterDependencyContainer()

    // MARK: - 依赖实例缓存
    private var _filterService: FilterServiceActor?
    private var _storageService: StorageService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [FilterDependencyContainer] MVVM-S架构容器初始化完成")
    }

    // MARK: - 核心服务提供

    /// 获取存储服务
    var storageService: StorageService {
        if let service = _storageService {
            return service
        }
        let service = StorageService(modelContainer: modelContainer)
        _storageService = service
        return service
    }

    /// 获取滤镜服务 (主要服务)
    var filterService: FilterServiceActor {
        if let service = _filterService {
            return service
        }
        // 创建正确的渲染服务实现
        let renderingService: RenderingServiceImpl
        do {
            renderingService = try RenderingServiceImpl()
        } catch {
            fatalError("渲染服务初始化失败: \(error)")
        }
        
        let service = FilterServiceActor(
            renderingService: renderingService,
            storageService: storageService,
            modelContainer: modelContainer
        )
        _filterService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }
        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建重构后的滤镜ViewModel
    /// - Returns: 配置好依赖注入的FilterViewModelRefactored实例
    @MainActor func createFilterViewModel() -> FilterViewModelRefactored {
        let viewModel = FilterViewModelRefactored(
            filterService: filterService
        )
        print("🎨 [FilterDependencyContainer] 创建FilterViewModelRefactored")
        return viewModel
    }

    /// 创建滤镜View (使用重构后的ViewModel)
    /// - Returns: 配置好依赖的FilterView实例
    @MainActor
    func createFilterView() -> FilterView {
        let viewModel = createFilterViewModel()
        let view = FilterView(filterViewModel: viewModel)
        print("🎨 [FilterDependencyContainer] 创建FilterView (使用重构后的ViewModel)")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化Actor服务）
    func warmUp() {
        print("🔥 [FilterDependencyContainer] 开始预热MVVM-S架构依赖...")

        // 预初始化核心服务
        _ = storageService
        _ = filterService

        print("🔥 [FilterDependencyContainer] MVVM-S架构依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _filterService = nil
        _storageService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [FilterDependencyContainer] MVVM-S架构资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension FilterDependencyContainer {

    /// 便捷方法：直接获取配置好的重构后FilterViewModel
    @MainActor static func filterViewModel() -> FilterViewModelRefactored {
        return shared.createFilterViewModel()
    }

    /// 便捷方法：直接获取配置好的FilterView (使用重构后的ViewModel)
    @MainActor
    static func filterView() -> FilterView {
        return shared.createFilterView()
    }
    
    /// 便捷方法：获取滤镜服务Actor
    static func filterService() -> FilterServiceActor {
        return shared.filterService
    }
    
    /// 便捷方法：获取存储服务
    static func storageService() -> StorageService {
        return shared.storageService
    }
}
