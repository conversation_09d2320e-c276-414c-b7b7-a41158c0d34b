// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 特效模块依赖注入容器 - MVVM-S架构
/// 管理特效模块的所有依赖关系，实现真正的依赖注入而非单例包装
class EffectsDependencyContainer {

    // MARK: - 单例（临时保留，用于向后兼容）
    static let shared = EffectsDependencyContainer()

    // MARK: - 核心依赖
    private let modelContainer: ModelContainer
    private let metalEngine: MetalSpecialEffectsEngine?

    // MARK: - 服务实例 (懒加载)
    private var _storageService: StorageService?
    private var _lightLeakService: LightLeakService?
    private var _grainService: GrainService?
    private var _scratchService: ScratchService?
    private var _effectsService: EffectsService?

    // MARK: - 初始化

    /// 依赖注入初始化（推荐使用）
    /// - Parameter modelContainer: SwiftData模型容器
    init(modelContainer: ModelContainer) {
        self.modelContainer = modelContainer
        
        // 初始化Metal引擎
        do {
            self.metalEngine = try MetalSpecialEffectsEngine()
            print("✅ [EffectsDependencyContainer] Metal引擎初始化成功")
        } catch {
            print("❌ [EffectsDependencyContainer] Metal引擎初始化失败: \(error)")
            self.metalEngine = nil
        }
        
        print("🎨 [EffectsDependencyContainer] 依赖注入初始化完成")
    }

    /// 单例初始化（向后兼容，临时保留）
    private convenience init() {
        // 临时使用SharedService，但这违反了依赖注入原则
        // TODO: 在所有调用方都更新为依赖注入后，删除此方法
        self.init(modelContainer: SharedService.shared.container)
        print("⚠️ [EffectsDependencyContainer] 使用单例初始化（不推荐）")
    }

    // MARK: - 服务工厂方法

    /// 获取存储服务
    var storageService: StorageServiceProtocol {
        if let service = _storageService {
            return service
        }
        
        let service = StorageService(modelContainer: modelContainer)
        _storageService = service
        print("🗄️ [EffectsDependencyContainer] 创建StorageService")
        return service
    }

    /// 获取漏光服务
    var lightLeakService: LightLeakServiceProtocol {
        if let service = _lightLeakService {
            return service
        }
        
        let service = LightLeakService(metalEngine: metalEngine)
        _lightLeakService = service
        print("🎨 [EffectsDependencyContainer] 创建LightLeakService")
        return service
    }

    /// 获取颗粒服务
    var grainService: GrainServiceProtocol {
        if let service = _grainService {
            return service
        }
        
        let service = GrainService(metalEngine: metalEngine)
        _grainService = service
        print("🎨 [EffectsDependencyContainer] 创建GrainService")
        return service
    }

    /// 获取划痕服务
    var scratchService: ScratchServiceProtocol {
        if let service = _scratchService {
            return service
        }
        
        let service = ScratchService(metalEngine: metalEngine)
        _scratchService = service
        print("🎨 [EffectsDependencyContainer] 创建ScratchService")
        return service
    }

    /// 获取特效服务
    var effectsService: EffectsServiceProtocol {
        if let service = _effectsService {
            return service
        }
        
        let service = EffectsService(
            lightLeakService: lightLeakService,
            grainService: grainService,
            scratchService: scratchService,
            storageService: storageService
        )
        _effectsService = service
        print("🎨 [EffectsDependencyContainer] 创建EffectsService")
        return service
    }

    // MARK: - ViewModel工厂方法

    /// 创建特效ViewModel
    /// - Returns: 配置好依赖的EffectsViewModel实例
    @MainActor
    func createEffectsViewModel() -> EffectsViewModel {
        let viewModel = EffectsViewModel(effectsService: effectsService)
        print("🎨 [EffectsDependencyContainer] 创建EffectsViewModel")
        return viewModel
    }

    /// 创建特效View
    /// - Returns: 配置好依赖的EffectsView实例
    @MainActor func createEffectsView() -> EffectsView {
        let effectsViewModel = createEffectsViewModel()
        let view = EffectsView(effectsViewModel: effectsViewModel)
        print("🎨 [EffectsDependencyContainer] 创建EffectsView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() async {
        print("🔥 [EffectsDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = storageService
        _ = lightLeakService
        _ = grainService
        _ = scratchService
        _ = effectsService

        print("🔥 [EffectsDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _storageService = nil
        _lightLeakService = nil
        _grainService = nil
        _scratchService = nil
        _effectsService = nil
        
        print("🧹 [EffectsDependencyContainer] 资源清理完成")
    }

    // MARK: - 诊断方法

    /// 获取依赖状态信息
    func getDependencyStatus() -> [String: Bool] {
        return [
            "ModelContainer": modelContainer != nil,
            "MetalEngine": metalEngine != nil,
            "StorageService": _storageService != nil,
            "LightLeakService": _lightLeakService != nil,
            "GrainService": _grainService != nil,
            "ScratchService": _scratchService != nil,
            "EffectsService": _effectsService != nil
        ]
    }

    /// 验证所有依赖是否正常
    func validateDependencies() -> Bool {
        let status = getDependencyStatus()
        let hasModelContainer = status["ModelContainer"] == true
        let hasMetalEngine = status["MetalEngine"] == true
        
        if !hasModelContainer {
            print("❌ [EffectsDependencyContainer] ModelContainer不可用")
            return false
        }
        
        if !hasMetalEngine {
            print("⚠️ [EffectsDependencyContainer] MetalEngine不可用，特效功能受限")
        }
        
        return true
    }
}

// MARK: - 扩展：便捷访问方法

extension EffectsDependencyContainer {

    /// 便捷方法：直接获取配置好的EffectsViewModel
    @MainActor static func effectsViewModel() -> EffectsViewModel {
        return shared.createEffectsViewModel()
    }

    /// 便捷方法：直接获取配置好的EffectsView
    @MainActor static func effectsView() -> EffectsView {
        return shared.createEffectsView()
    }

    /// 便捷方法：获取依赖状态
    static func dependencyStatus() -> [String: Bool] {
        return shared.getDependencyStatus()
    }

    /// 便捷方法：验证依赖
    static func validateDependencies() -> Bool {
        return shared.validateDependencies()
    }
}
