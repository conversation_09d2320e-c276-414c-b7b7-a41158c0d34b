// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine

/// 订阅模块依赖注入容器 - MVVM-S架构
/// 管理订阅模块的所有依赖关系，确保单一职责和可维护性
class SubscriptionDependencyContainer {
    
    // MARK: - 单例
    static let shared = SubscriptionDependencyContainer()
    
    // MARK: - 依赖实例
    private var _subscriptionService: SubscriptionService?
    
    // MARK: - 初始化
    private init() {
        print("💳 [SubscriptionDependencyContainer] 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 获取订阅服务
    var subscriptionService: SubscriptionServiceProtocol {
        if let service = _subscriptionService {
            return service
        }
        
        let service = SubscriptionService()
        _subscriptionService = service
        print("💳 [SubscriptionDependencyContainer] 创建SubscriptionService")
        return service
    }
    
    // MARK: - ViewModel工厂方法
    
    /// 创建订阅ViewModel
    /// - Returns: 配置好依赖的SubscriptionViewModel实例
    @MainActor
    func createSubscriptionViewModel() -> SubscriptionViewModel {
        let viewModel = SubscriptionViewModel(subscriptionService: subscriptionService)
        print("💳 [SubscriptionDependencyContainer] 创建SubscriptionViewModel")
        return viewModel
    }
    
    /// 创建订阅View
    /// - Returns: 配置好依赖的SubscriptionView实例
    @MainActor func createSubscriptionView() -> SubscriptionView {
        let viewModel = createSubscriptionViewModel()
        let view = SubscriptionView(viewModel: viewModel)
        print("💳 [SubscriptionDependencyContainer] 创建SubscriptionView")
        return view
    }
    
    // MARK: - 生命周期管理
    
    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [SubscriptionDependencyContainer] 开始预热依赖...")
        
        // 预初始化核心服务
        _ = subscriptionService
        
        print("🔥 [SubscriptionDependencyContainer] 依赖预热完成")
    }
    
    /// 清理资源
    func cleanup() {
        _subscriptionService = nil
        
        print("🧹 [SubscriptionDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension SubscriptionDependencyContainer {
    
    /// 便捷方法：直接获取配置好的SubscriptionViewModel
    @MainActor static func subscriptionViewModel() -> SubscriptionViewModel {
        return shared.createSubscriptionViewModel()
    }
    
    /// 便捷方法：直接获取配置好的SubscriptionView
    @MainActor static func subscriptionView() -> SubscriptionView {
        return shared.createSubscriptionView()
    }
}
