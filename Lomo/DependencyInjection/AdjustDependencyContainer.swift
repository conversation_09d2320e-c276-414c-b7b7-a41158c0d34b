// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 调节模块的依赖注入容器 - MVVM-S架构重构版
/// 管理调节模块的所有依赖关系，使用Actor模式和重构后的ViewModel
class AdjustDependencyContainer {

    // MARK: - 单例
    static let shared = AdjustDependencyContainer()

    // MARK: - 依赖实例缓存
    private var _adjustService: AdjustServiceActor?
    private var _filterService: FilterServiceActor?
    private var _storageService: StorageService?
    private var _modelContainer: ModelContainer?
    private var _renderingService: RenderingServiceImpl?
    private var _curveService: CurveServiceImpl?
    private var _hslService: HSLServiceImpl?

    // MARK: - 初始化
    private init() {
        print("🎨 [AdjustDependencyContainer] MVVM-S架构容器初始化完成")
    }

    // MARK: - 核心服务提供

    /// 获取存储服务
    var storageService: StorageService {
        if let service = _storageService {
            return service
        }
        let service = StorageService(modelContainer: modelContainer)
        _storageService = service
        return service
    }

    /// 获取渲染服务
    var renderingService: RenderingServiceImpl {
        if let service = _renderingService {
            return service
        }
        do {
            let service = try RenderingServiceImpl()
            _renderingService = service
            return service
        } catch {
            fatalError("渲染服务初始化失败: \(error)")
        }
    }
    
    /// 获取曲线服务
    var curveService: CurveServiceImpl {
        if let service = _curveService {
            return service
        }
        let service = CurveServiceImpl()
        _curveService = service
        return service
    }
    
    /// 获取HSL服务
    var hslService: HSLServiceImpl {
        if let service = _hslService {
            return service
        }
        let service = HSLServiceImpl()
        _hslService = service
        return service
    }

    /// 获取滤镜服务
    var filterService: FilterServiceActor {
        if let service = _filterService {
            return service
        }
        // 使用完整的服务实现
        let service = FilterServiceActor(
            renderingService: renderingService,
            storageService: storageService
        )
        _filterService = service
        return service
    }

    /// 获取调节服务 (主要服务)
    var adjustService: AdjustServiceActor {
        if let service = _adjustService {
            return service
        }
        // 使用完整的服务实现
        let service = AdjustServiceActor(
            filterService: filterService,
            curveService: curveService,
            hslService: hslService,
            storageService: storageService,
            modelContainer: modelContainer
        )
        _adjustService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }
        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建重构后的调节ViewModel
    /// - Returns: 配置好依赖注入的AdjustViewModelRefactored实例
    @MainActor
    func createAdjustViewModel() -> AdjustViewModelRefactored {
        let viewModel = AdjustViewModelRefactored(
            adjustService: adjustService,
            curveService: curveService,
            hslService: hslService
        )
        print("🎨 [AdjustDependencyContainer] 创建AdjustViewModelRefactored")
        return viewModel
    }

    /// 创建调节View (使用重构后的ViewModel)
    /// - Returns: 配置好依赖的AdjustView实例
    @MainActor
    func createAdjustView() -> AdjustView {
        let viewModel = createAdjustViewModel()
        let view = AdjustView(adjustViewModel: viewModel)
        print("🎨 [AdjustDependencyContainer] 创建AdjustView (使用重构后的ViewModel)")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化Actor服务）
    func warmUp() {
        print("🔥 [AdjustDependencyContainer] 开始预热MVVM-S架构依赖...")

        // 预初始化核心服务
        _ = storageService
        _ = renderingService
        _ = curveService
        _ = hslService
        _ = filterService
        _ = adjustService

        print("🔥 [AdjustDependencyContainer] MVVM-S架构依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _adjustService = nil
        _filterService = nil
        _storageService = nil
        _renderingService = nil
        _curveService = nil
        _hslService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [AdjustDependencyContainer] MVVM-S架构资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension AdjustDependencyContainer {

    /// 便捷方法：直接获取配置好的重构后AdjustViewModel
    @MainActor
    static func adjustViewModel() -> AdjustViewModelRefactored {
        return shared.createAdjustViewModel()
    }

    /// 便捷方法：直接获取配置好的AdjustView (使用重构后的ViewModel)
    @MainActor
    static func adjustView() -> AdjustView {
        return shared.createAdjustView()
    }
    
    /// 便捷方法：获取调节服务Actor
    static func adjustService() -> AdjustServiceActor {
        return shared.adjustService
    }
    
    /// 便捷方法：获取滤镜服务Actor
    static func filterService() -> FilterServiceActor {
        return shared.filterService
    }
}
