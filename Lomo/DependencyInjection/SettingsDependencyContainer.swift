import Foundation
import SwiftData

/// 设置模块依赖注入容器 - MVVM-S架构
/// 管理设置模块的所有依赖关系，确保单一职责和可测试性
class SettingsDependencyContainer {

    // MARK: - 单例
    static let shared = SettingsDependencyContainer()

    // MARK: - 依赖实例
    private var _settingsService: SettingsService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [SettingsDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取设置服务
    var settingsService: SettingsService {
        if let service = _settingsService {
            return service
        }

        let service = SettingsService()
        _settingsService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建设置ViewModel
    /// - Returns: 配置好依赖的SettingsViewModel实例
    func createSettingsViewModel() -> SettingsViewModel {
        let viewModel = SettingsViewModel(settingsService: settingsService)
        print("🎨 [SettingsDependencyContainer] 创建SettingsViewModel")
        return viewModel
    }

    /// 创建设置View
    /// - Returns: 配置好依赖的SettingsView实例
    func createSettingsView() -> SettingsView {
        let viewModel = createSettingsViewModel()
        let view = SettingsView(viewModel: viewModel)
        print("🎨 [SettingsDependencyContainer] 创建SettingsView")
        return view
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [SettingsDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = settingsService

        print("🔥 [SettingsDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _settingsService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [SettingsDependencyContainer] 资源清理完成")
    }
}

// MARK: - 扩展：便捷访问方法

extension SettingsDependencyContainer {

    /// 便捷方法：直接获取配置好的SettingsViewModel
    static func settingsViewModel() -> SettingsViewModel {
        return shared.createSettingsViewModel()
    }

    /// 便捷方法：直接获取配置好的SettingsView
    static func settingsView() -> SettingsView {
        return shared.createSettingsView()
    }
}