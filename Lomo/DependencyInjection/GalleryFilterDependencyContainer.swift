// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData
import SwiftUI

/// 相册滤镜模块依赖注入容器 - MVVM-S架构
/// 管理相册滤镜模块的所有依赖关系，确保单一职责和可测试性
class GalleryFilterDependencyContainer {

    // MARK: - 单例
    static let shared = GalleryFilterDependencyContainer()

    // MARK: - 依赖实例
    private var _galleryFilterService: GalleryFilterService?
    private var _modelContainer: ModelContainer?

    // MARK: - 初始化
    private init() {
        print("🎨 [GalleryFilterDependencyContainer] 初始化完成")
    }

    // MARK: - 公共接口

    /// 获取相册滤镜服务
    var galleryFilterService: GalleryFilterService {
        if let service = _galleryFilterService {
            return service
        }

        let service = GalleryFilterService()
        _galleryFilterService = service
        return service
    }

    /// 获取模型容器
    var modelContainer: ModelContainer {
        if let container = _modelContainer {
            return container
        }

        // 使用SharedService的模型容器
        let container = SharedService.shared.container
        _modelContainer = container
        return container
    }

    // MARK: - ViewModel工厂方法

    /// 创建相册滤镜视图模型
    /// - Returns: 配置完整的GalleryFilterViewModel实例
    func createGalleryFilterViewModel() -> GalleryFilterViewModel {
        return GalleryFilterViewModel(filterService: galleryFilterService)
    }

    /// 创建相册滤镜视图
    /// - Returns: 配置完整的GalleryFilterView实例
    func createGalleryFilterView() -> GalleryFilterView {
        let viewModel = createGalleryFilterViewModel()
        return GalleryFilterView(viewModel: viewModel)
    }

    // MARK: - 生命周期管理

    /// 预热依赖（提前初始化）
    func warmUp() {
        print("🔥 [GalleryFilterDependencyContainer] 开始预热依赖...")

        // 预初始化核心服务
        _ = galleryFilterService

        print("🔥 [GalleryFilterDependencyContainer] 依赖预热完成")
    }

    /// 清理资源
    func cleanup() {
        _galleryFilterService = nil
        // 注意：不清理modelContainer，因为它是共享的

        print("🧹 [GalleryFilterDependencyContainer] 资源清理完成")
    }
}

// MARK: - 便捷访问方法

extension GalleryFilterDependencyContainer {
    
    /// 便捷方法：创建相册滤镜视图模型
    /// - Returns: 配置完整的GalleryFilterViewModel实例
    static func galleryFilterViewModel() -> GalleryFilterViewModel {
        return shared.createGalleryFilterViewModel()
    }
    
    /// 便捷方法：创建相册滤镜视图
    /// - Returns: 配置完整的GalleryFilterView实例
    static func galleryFilterView() -> GalleryFilterView {
        return shared.createGalleryFilterView()
    }
}
