# 📚 Lomo项目资源索引 - 避免重复造轮子指南

## 🎯 使用说明

**在创建任何新文件前，请先查阅此索引确认功能是否已存在！**

本文档列出了项目中所有可复用的资源，包括常量、工具类、组件等，帮助开发者快速找到所需功能，避免重复创建。

---

## 📐 常量定义文件

### 1. CameraConstants.swift
**位置**: `Lomo/Utils/Constants/CameraConstants.swift`  
**功能**: 项目通用常量定义中心

#### 包含内容：
- **AnimationConstants枚举** - 所有动画相关常量
  - `standardSpring` - 标准弹簧动画
  - `standardEaseInOut` - 标准缓动动画
  - `duration` - 标准动画时长
  - `quickDuration` - 快速动画时长

- **UIConstants枚举** - 所有UI相关常量
  - **拨盘常量**: `dialMainTickLength`, `dialSubTickLength`, `dialTickWidth`, `dialIndicatorColor`
  - **字体大小**: `standardFontSize`, `parameterFontSize`, `tabFontSize`
  - **颜色**: `recordingColor`, `logoSelectedColor`
  - **尺寸**: `shutterButtonSize`, `lensButtonBaseSize`, `filterPreviewHeight`
  - **间距**: `horizontalPadding`, `verticalSpacing`, `bottomPadding`
  - **透明度**: `lightOpacity`, `mediumOpacity`, `highOpacity`
  - **圆角**: `commonCornerRadius`, `largeCornerRadius`, `smallCornerRadius`

- **CameraDefaults枚举** - 相机参数默认值
- **CameraThresholds枚举** - 相机参数阈值

#### 使用示例：
```swift
// 动画
withAnimation(AnimationConstants.standardSpring) { ... }

// UI常量
.frame(height: UIScreen.main.bounds.height * UIConstants.dialMainTickLength)
.foregroundColor(UIConstants.dialIndicatorColor)
```

### 2. WatermarkConstants.swift
**位置**: `Lomo/Utils/Constants/WatermarkConstants.swift`  
**功能**: 水印相关常量定义

#### 包含内容：
- 各种水印样式的尺寸、间距、颜色常量
- 水印布局参数
- 字体大小配置

---

## 🛠️ 工具类文件

### 1. MaskUtils.swift
**位置**: `Lomo/Utils/MaskUtils.swift`  
**功能**: 遮罩工具类

#### 包含功能：
- `createDialMaskPath()` - 为圆形拨盘创建遮罩路径
- `createScaleRulerMaskPath()` - 为直线刻度尺创建遮罩路径
- `MaskConfiguration` - 遮罩配置结构体

#### 使用示例：
```swift
MaskUtils.createScaleRulerMaskPath(centerX: geometry.size.width / 2, config: .scaleRuler)
```

### 2. LayoutUtils.swift
**位置**: `Lomo/Utils/LayoutUtils.swift`  
**功能**: 布局计算工具

### 3. FontUtils.swift
**位置**: `Lomo/Utils/FontUtils.swift`  
**功能**: 字体处理工具

### 4. AnimationUtils.swift
**位置**: `Lomo/Utils/AnimationUtils.swift`  
**功能**: 动画工具类

---

## 🎨 通用UI组件

### 1. NavigationTopBar.swift
**位置**: `Lomo/Views/Shared/NavigationTopBar.swift`  
**功能**: 通用顶部导航栏组件

#### 使用场景：
- 滤镜分类导航
- 编辑模式导航
- 设置页面导航

### 2. BottomTabBar.swift
**位置**: `Lomo/Views/Shared/BottomTabBar.swift`  
**功能**: 底部标签栏组件

### 3. OptionButton.swift
**位置**: `Lomo/Views/Components/OptionButton.swift`  
**功能**: 可复用的选项按钮组件

### 4. CustomSlider.swift
**位置**: `Lomo/Views/Components/CustomSlider.swift`  
**功能**: 自定义滑块组件

### 5. PreviewScrollView.swift
**位置**: `Lomo/Views/Components/PreviewScrollView.swift`  
**功能**: 预览滚动视图组件

---

## 🔍 快速查找指南

### 需要UI常量时：
```bash
# 1. 先检查是否已存在
grep -r "static let.*Color\|static let.*CGFloat" Lomo/Utils/Constants/CameraConstants.swift

# 2. 查看具体常量
grep -A 5 -B 5 "你需要的关键词" Lomo/Utils/Constants/CameraConstants.swift
```

### 需要动画常量时：
```bash
# 检查AnimationConstants枚举
grep -A 10 "enum AnimationConstants" Lomo/Utils/Constants/CameraConstants.swift
```

### 需要工具类时：
```bash
# 查看所有工具类
find Lomo/Utils -name "*Utils.swift" -type f

# 查看具体工具类功能
grep -A 5 "func.*(" Lomo/Utils/MaskUtils.swift
```

### 需要UI组件时：
```bash
# 查看通用组件
find Lomo/Views/Components -name "*.swift" -type f
find Lomo/Views/Shared -name "*.swift" -type f
```

---

## ⚠️ 重复造轮子警示案例

### 案例1: UIConstants重复创建
**错误**: 在Crop模块重构时，创建了新的UIConstants.swift文件  
**正确**: 使用现有的CameraConstants.swift中的UIConstants枚举  
**教训**: 创建前必须先搜索现有资源

### 案例2: 动画常量重复定义
**错误**: 创建AnimationConstants.swift文件  
**正确**: 使用CameraConstants.swift中已有的AnimationConstants枚举  
**教训**: 相同功能的常量应该集中管理

---

## 📋 创建新文件前的检查清单

### ✅ 必须完成的检查项：

1. **[ ] 搜索同名文件**
   ```bash
   find . -name "*你要创建的文件名*" -type f
   ```

2. **[ ] 搜索相似功能文件**
   ```bash
   find . -name "*关键词*" -type f
   ```

3. **[ ] 检查常量定义**
   ```bash
   grep -r "你需要的常量名" Lomo/Utils/Constants/
   ```

4. **[ ] 检查工具类**
   ```bash
   find Lomo/Utils -name "*Utils.swift" -type f
   ```

5. **[ ] 检查通用组件**
   ```bash
   find Lomo/Views/Components -name "*.swift" -type f
   find Lomo/Views/Shared -name "*.swift" -type f
   ```

6. **[ ] 查阅本索引文档**
   确认所需功能确实不存在

### ❌ 如果发现已存在相同功能：
- **停止创建新文件**
- **使用现有资源**
- **如需扩展，在现有文件中添加**
- **更新本索引文档**

---

## 🔄 索引维护

### 添加新资源时：
1. 在对应文件中添加详细注释
2. 更新本索引文档
3. 在相关模块文档中添加引用

### 发现重复资源时：
1. 立即合并重复资源
2. 更新所有引用
3. 删除重复文件
4. 更新索引文档

---

## 🛠️ 开发工具脚本

### Git 管理工具
| 脚本名称 | 功能描述 | 使用方式 | 位置 |
|---------|----------|----------|------|
| **smart_git_commit.sh** | 智能 Git 提交检查 | `./Lomo/Scripts/smart_git_commit.sh` | `Lomo/Scripts/` |
| **setup_git_hooks.sh** | Git Hooks 设置 | `./Lomo/Scripts/setup_git_hooks.sh` | `Lomo/Scripts/` |
| **setup_lonicera_git.sh** | LoniceraLab Git 环境一键设置 | `./Lomo/Scripts/setup_lonicera_git.sh` | `Lomo/Scripts/` |
| **quick_commit.sh** | 快速提交脚本 | `./Lomo/Scripts/quick_commit.sh arch "消息"` | `Lomo/Scripts/` |

### Xcode 缓存管理工具
| 脚本名称 | 功能描述 | 使用方式 | 位置 |
|---------|----------|----------|------|
| **smart_xcode_cache_cleaner.sh** | 智能 Xcode 缓存清理 | `./Lomo/Scripts/smart_xcode_cache_cleaner.sh` | `Lomo/Scripts/` |
| **setup_smart_cleaner_automation.sh** | 自动化清理设置 | `./Lomo/Scripts/setup_smart_cleaner_automation.sh` | `Lomo/Scripts/` |
| **clean_xcode_cache.sh** | 传统 Xcode 缓存清理 | `./Lomo/Scripts/clean_xcode_cache.sh` | `Lomo/Scripts/` |

### 项目质量工具
| 脚本名称 | 功能描述 | 使用方式 | 位置 |
|---------|----------|----------|------|
| **smart_duplication_check.sh** | 智能重复文件检查 | `./Lomo/Scripts/smart_duplication_check.sh UIConstants.swift "常量定义"` | `Lomo/Scripts/` |

### 模块重构测试脚本
| 脚本名称 | 功能描述 | 使用方式 | 位置 |
|---------|----------|----------|------|
| **test_gallery_mvvm_refactor.sh** | Gallery模块重构测试 | `./Lomo/Scripts/test_gallery_mvvm_refactor.sh` | `Lomo/Scripts/` |
| **test_crop_mvvm_refactor.sh** | Crop模块重构测试 | `./Lomo/Scripts/test_crop_mvvm_refactor.sh` | `Lomo/Scripts/` |
| **test_subscription_mvvm_refactor.sh** | Subscription模块重构测试 | `./Lomo/Scripts/test_subscription_mvvm_refactor.sh` | `Lomo/Scripts/` |

---

**记住：花5分钟查找现有资源，胜过花1小时重复造轮子！**