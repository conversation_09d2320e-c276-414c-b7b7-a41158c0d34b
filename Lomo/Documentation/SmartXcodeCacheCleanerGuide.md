# 🧠 智能 Xcode 缓存清理器使用指南

## 📋 概述

智能 Xcode 缓存清理器是一个基于多维度分析的自动化工具，它能够智能判断何时需要清理 Xcode 缓存，避免不必要的清理操作，提升开发效率。

## 🎯 设计理念

### 传统清理方式的问题
- ❌ **盲目定期清理**: 不管是否需要，每周都清理
- ❌ **浪费时间**: 清理后需要重新索引，耗时较长
- ❌ **过度清理**: 清理不必要的缓存，影响构建速度
- ❌ **被动响应**: 只有出现问题才清理，为时已晚

### 智能清理的优势
- ✅ **按需清理**: 只在真正需要时才执行清理
- ✅ **多维分析**: 综合考虑大小、空间、时间、错误等因素
- ✅ **主动预防**: 在问题发生前就进行预防性清理
- ✅ **自动化**: 可集成到开发工作流中，无需手动干预

## 🔍 智能分析维度

### 1. 缓存大小分析
```bash
# 检查 DerivedData 总大小
默认阈值: 5GB
触发条件: DerivedData 目录超过 5GB
清理策略: 优先清理项目特定缓存
```

### 2. 磁盘空间分析
```bash
# 检查磁盘剩余空间
默认阈值: 10GB
触发条件: 磁盘剩余空间少于 10GB
清理策略: 清理所有过期缓存 + 系统缓存
```

### 3. 缓存年龄分析
```bash
# 检查缓存最后修改时间
默认阈值: 7天
触发条件: 缓存超过 7 天未使用
清理策略: 清理过期的项目缓存
```

### 4. 构建错误检测
```bash
# 检查构建异常指标
检测项目:
- 损坏的构建数据库 (build.db)
- 异常终止的锁定文件 (*.lock)
- 过多的临时文件 (*.tmp, *.temp)
- 最近的构建失败记录
```

### 5. 系统健康检查
```bash
# 综合系统状态评估
考虑因素:
- 内存使用情况
- CPU 负载状态
- I/O 性能指标
- 网络连接状态
```

## 🛠️ 使用方法

### 基础用法

#### 1. 智能自动模式（推荐）
```bash
./Lomo/Scripts/smart_xcode_cache_cleaner.sh
# 或
./Lomo/Scripts/smart_xcode_cache_cleaner.sh auto
```
**功能**: 智能分析系统状态，如果需要清理会询问用户确认

#### 2. 仅检查模式
```bash
./Lomo/Scripts/smart_xcode_cache_cleaner.sh check
```
**功能**: 只分析状态，显示是否需要清理，不执行实际清理

#### 3. 强制清理模式
```bash
./Lomo/Scripts/smart_xcode_cache_cleaner.sh force
```
**功能**: 跳过分析，直接执行全面清理

#### 4. 静默自动模式
```bash
./Lomo/Scripts/smart_xcode_cache_cleaner.sh silent
```
**功能**: 静默执行，适用于定时任务和自动化脚本

### 高级用法

#### 1. 集成到 Git 工作流
```bash
# 设置 Git hooks
./Lomo/Scripts/setup_smart_cleaner_automation.sh
# 选择选项 4: 设置 Git hooks 集成
```

**效果**: 每次提交前自动检查是否需要清理缓存

#### 2. 集成到 Xcode 构建流程
```bash
# 设置构建集成
./Lomo/Scripts/setup_smart_cleaner_automation.sh
# 选择选项 5: 设置 Xcode 构建集成
```

**效果**: 构建前自动检查并清理缓存，确保构建环境最佳

#### 3. 设置定时自动检查
```bash
# 设置定时任务
./Lomo/Scripts/setup_smart_cleaner_automation.sh
# 选择选项 1-3: 设置不同频率的定时检查
```

**效果**: 系统自动定期检查，无需手动干预

#### 4. 设置 Shell 别名
```bash
# 设置便捷别名
./Lomo/Scripts/setup_smart_cleaner_automation.sh
# 选择选项 6: 设置 Shell 别名
```

**效果**: 可以使用 `xclean`、`xcheck`、`xforce` 等简短命令

## 📊 输出解读

### 分析报告示例
```bash
🧠 智能 Xcode 缓存清理器启动...

📊 正在分析系统状态...
   DerivedData 大小: 3GB
   磁盘剩余空间: 15GB
   项目缓存年龄: 5天
🚨 发现损坏的构建数据库
⚠️ 发现构建锁定文件（可能异常终止）

🎯 智能分析结果: 建议清理缓存
📋 清理原因:
   • 检测到构建错误或异常
   • 缓存接近过期 (5天 > 7天的70%)
```

### 状态指标说明

| 指标 | 含义 | 正常范围 | 需要关注 |
|------|------|----------|----------|
| **DerivedData 大小** | 构建缓存总大小 | < 3GB | > 5GB |
| **磁盘剩余空间** | 可用存储空间 | > 20GB | < 10GB |
| **项目缓存年龄** | 缓存最后使用时间 | < 3天 | > 7天 |
| **构建错误指标** | 异常文件数量 | 0个 | > 0个 |

## 🤖 自动化配置

### 1. LaunchAgent 定时任务
```xml
<!-- 每12小时检查一次 -->
<key>StartInterval</key>
<integer>43200</integer>
```

**优势**:
- 系统级定时任务，开机自动启动
- 静默运行，不干扰正常工作
- 日志记录，便于问题追踪

### 2. Git Hooks 集成
```bash
# pre-commit hook
if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
    echo "💡 提示: 检测到 Xcode 缓存可能需要清理"
fi
```

**优势**:
- 代码提交前自动检查
- 及时发现潜在问题
- 不影响提交流程

### 3. Xcode 构建集成
```bash
# 构建前自动检查
if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
    "$SMART_CLEANER" silent
fi
```

**优势**:
- 构建前确保环境最佳
- 减少构建失败概率
- 自动化程度最高

## 📈 性能优化

### 清理策略优化

#### 1. 分级清理
```bash
轻度清理: 仅清理项目特定缓存
中度清理: 清理过期缓存 + 项目缓存
重度清理: 全面清理 + 系统缓存
```

#### 2. 智能保留
```bash
保留策略:
- 保留最近使用的缓存
- 保留小于1GB的缓存
- 保留正在使用的缓存
```

#### 3. 并行处理
```bash
优化措施:
- 并行删除多个缓存目录
- 异步检查文件状态
- 批量处理小文件
```

### 检测性能优化

#### 1. 缓存检测结果
```bash
# 避免重复检查
检测结果缓存: 5分钟
文件状态缓存: 1分钟
系统信息缓存: 10分钟
```

#### 2. 快速检测模式
```bash
# 优先检查关键指标
快速检查: 大小 + 错误指标
完整检查: 所有维度分析
```

## 🔧 配置参数

### 可调整参数
```bash
# 在脚本顶部修改这些参数
MAX_DERIVED_DATA_SIZE_GB=5    # DerivedData 最大大小
MIN_FREE_SPACE_GB=10          # 最小剩余空间
MAX_CACHE_AGE_DAYS=7          # 缓存最大年龄
```

### 环境变量配置
```bash
# 设置环境变量覆盖默认配置
export SMART_CLEANER_MAX_SIZE=8
export SMART_CLEANER_MIN_SPACE=15
export SMART_CLEANER_MAX_AGE=10
```

## 📊 使用统计

### 统计信息
```bash
📈 使用统计:
   总清理次数: 15
   上次清理: 2025-01-01 10:30
   当前 DerivedData 大小: 2GB
   当前磁盘剩余空间: 25GB
```

### 日志记录
```bash
# 日志文件位置
清理日志: ~/Library/Logs/xcode_smart_cleaner.log
错误日志: ~/Library/Logs/xcode_smart_cleaner_error.log
使用记录: ~/.xcode_smart_clean_log
```

## 🚨 故障排除

### 常见问题

#### 1. 权限问题
```bash
# 解决方案
chmod +x Lomo/Scripts/smart_xcode_cache_cleaner.sh
sudo chown -R $(whoami) ~/Library/Developer/Xcode/DerivedData
```

#### 2. 路径问题
```bash
# 确保脚本路径正确
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
```

#### 3. 检测不准确
```bash
# 手动校准参数
MAX_DERIVED_DATA_SIZE_GB=3  # 降低阈值
MIN_FREE_SPACE_GB=15        # 提高阈值
```

### 调试模式
```bash
# 启用详细输出
export DEBUG=1
./Lomo/Scripts/smart_xcode_cache_cleaner.sh check
```

## 🎯 最佳实践

### 开发团队使用
1. **统一配置**: 团队使用相同的阈值参数
2. **定期检查**: 每个开发者设置自动化检查
3. **经验分享**: 分享清理效果和优化建议
4. **监控统计**: 定期查看使用统计，优化参数

### 个人开发使用
1. **按需调整**: 根据个人习惯调整参数
2. **集成工作流**: 选择合适的自动化方式
3. **定期维护**: 查看日志，清理无用文件
4. **性能监控**: 关注清理效果和系统性能

## 🔗 相关资源

- [Xcode Build System Guide](https://developer.apple.com/documentation/xcode/build-system)
- [macOS LaunchAgent Documentation](https://developer.apple.com/library/archive/documentation/MacOSX/Conceptual/BPSystemStartup/Chapters/CreatingLaunchdJobs.html)
- [Git Hooks Documentation](https://git-scm.com/book/en/v2/Customizing-Git-Git-Hooks)

---

**记住**: 智能清理器的目标是让您专注于开发，而不是手动管理缓存！🚀