import Foundation
import SwiftUI
import Combine
import Photos

/// 共享标签页视图模型
class SharedTabViewModel: ObservableObject {
    // MARK: - 发布属性
    @Published var selectedTab: TabBarItem = .gallery
    @Published var isVisible: Bool = false
    @Published var selectedTabIndex: Int = 0  // 添加当前选中的标签索引
    @Published var isSelectionMode: Bool = false  // 添加选择模式状态变量
    
    // 用于获取当前选中的照片数量
    @Published var selectedPhotosCount: Int = 0
    
    // 新增：标志位，指示进入相册时是否直接开启选择模式
    @Published var shouldEnterGallerySelectionMode: Bool = false
    
    // 新增: 用于存储从 GalleryViewModel 同步过来的选中资源
    @Published var currentGallerySelectedAssets: [PHAsset] = []

    // 新增：记住用户从哪个页面发起的照片选择，用于选择完成后返回
    @Published var previousTabBeforePhotoSelection: TabBarItem? = nil

    // 新增：记住编辑页面的子页面类别
    @Published var previousEditCategoryBeforePhotoSelection: WatermarkCategory? = nil
    
    // MARK: - 裁切/构图相关状态
    @Published var cropScaleDragOffset: CGFloat = 0  // 刻度尺拖动偏移量
    @Published var lastDragOffset: CGFloat = 0       // 上次拖动结束时的偏移量
    @Published var dragOffset: CGFloat = 0           // 当前拖动偏移量
    @Published var startDragOffset: CGFloat = 0      // 触摸开始时的偏移量
    @Published var isTouching: Bool = false          // 是否正在触摸
    @Published var rotationAngle: Double = 0         // 旋转角度
    @Published var selectedRatio: String = "original" // 选中的宽高比
    
    // 触摸位置
    var startLocation: CGPoint = .zero
    
    // 刻度尺触摸回调
    var onScaleTouchBegan: (() -> Void)?
    var onScaleTouchEnded: (() -> Void)?
    
    // MARK: - 常量
    private let screenHeight = UIScreen.main.bounds.height
    private let screenWidth = UIScreen.main.bounds.width
    
    // 存储服务
    private let storageService = UserDefaultsService.shared
    
    // MARK: - 初始化方法
    init() {
        // 初始化代码 - 设置刻度尺为默认居中位置，不从UserDefaults加载
        cropScaleDragOffset = 0.0
        lastDragOffset = 0.0
        dragOffset = 0.0
    }
    
    // 加载保存的状态
    func loadSavedState() {
        // 从UserDefaultsService加载上次保存的刻度尺偏移量，如果没有则默认为0（居中）
        cropScaleDragOffset = storageService.getCropScaleOffset()
    }
    
    // 重置裁切刻度尺到中心位置
    func resetCropScaleToCenter() {
        cropScaleDragOffset = 0.0
        lastDragOffset = 0.0
        dragOffset = 0.0
        storageService.saveCropScaleOffset(0.0)
    }
    
    // MARK: - 公共方法
    
    /// 显示共享标签页视图
    func show(initialTab: TabBarItem = .gallery) {
        selectedTab = initialTab
        withAnimation(.easeInOut(duration: AnimationConstants.duration)) {
            isVisible = true
        }
    }
    
    /// 隐藏共享标签页视图
    func hide() {
        withAnimation(.easeInOut(duration: AnimationConstants.duration)) {
            isVisible = false
        }
    }
    
    /// 切换标签页
    func switchTab(to tab: TabBarItem) {
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedTab = tab
        }
    }
    
    /// 切换标签索引
    func switchTabIndex(to index: Int) {
        withAnimation(.easeInOut(duration: 0.2)) {
            selectedTabIndex = index
        }
    }
    
    // MARK: - 裁切/构图相关方法
    
    /// 更新刻度尺拖动偏移量
    /// - Parameter offset: 新的偏移量
    func updateCropScaleOffset(_ offset: CGFloat) {
        // 限制偏移量范围在 ±屏幕宽度的46%
        let limitedOffset = min(max(offset, -screenWidth * 0.46), screenWidth * 0.46)
        cropScaleDragOffset = limitedOffset
        dragOffset = limitedOffset
        
        // 根据偏移量更新旋转角度 (左侧+45度到右侧-45度)
        let angleRange: Double = 90.0  // 总角度范围(+45到-45)
        let maxOffset: CGFloat = screenWidth * 0.46 * 2  // 总偏移范围
        rotationAngle = -Double(cropScaleDragOffset / maxOffset) * angleRange  // 使用负号翻转映射关系
    }
} 