// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine
import CoreGraphics

/// 调节模块的视图模型 - MVVM-S架构重构版
/// 消除单例依赖，实施真正的依赖注入
@MainActor
class AdjustViewModelRefactored: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let adjustService: AdjustServiceProtocol
    private let curveService: CurveServiceProtocol
    private let hslService: HSLServiceProtocol
    
    // MARK: - 状态管理 (集中在ViewModel)
    
    /// 视图状态
    @Published private(set) var state: ViewState<FilterParameters> = .idle
    
    /// 当前参数
    @Published var currentParameters = FilterParameters()
    
    // MARK: - 曲线状态属性
    
    /// 当前曲线控制点
    @Published var curvePoints: [CurveChannel: [CGPoint]] = [
        .rgb: CurveProcessor.createDefaultPoints(),
        .red: CurveProcessor.createDefaultPoints(),
        .green: CurveProcessor.createDefaultPoints(),
        .blue: CurveProcessor.createDefaultPoints()
    ]
    
    /// 曲线强度 (0.0-1.0)
    @Published var curveIntensity: Float = 1.0
    
    /// 是否启用曲线调整
    @Published var isEnabled: Bool = false
    
    /// 当前渲染质量
    @Published var renderQuality: CurveProcessor.CurveQuality = .standard
    
    /// 当前选中的预设
    @Published var currentPreset: CurveProcessor.CurvePreset? = nil
    
    /// 当前选中的通道
    @Published var selectedChannel: CurveChannel = .rgb {
        didSet {
            if selectedChannel != oldValue {
                resetDragState()
                print("🎨 通道已切换到: \(selectedChannel.displayName)")
            }
        }
    }
    
    /// 当前拖拽的点索引
    @Published var draggedPointIndex: Int? = nil
    
    // MARK: - HSL状态属性
    
    /// 当前选中的颜色选项 (0-7)
    @Published var selectedColorIndex: Int = 0
    
    /// 当前HSL参数 (用于UI绑定)
    @Published var currentHSLParameters: (hue: Float, saturation: Float, luminance: Float) = (0, 0, 0)
    
    // MARK: - 色调分离状态属性
    
    /// 当前选中的色调选项
    @Published var selectedToneOption: String = "阴影"
    
    /// 色环控制点的UI偏移量
    @Published var toneHueOffset: CGSize = .zero
    
    /// 是否正在按压控制点
    @Published var isPressing: Bool = false
    
    // MARK: - 调节控制状态属性
    
    /// 当前选中的调节参数类型
    @Published var selectedParameter: String = "exposure"
    
    // MARK: - 校准相关属性
    
    /// 当前选中的校准颜色索引 (0=红, 1=绿, 2=蓝)
    @Published var selectedCalibrationColorIndex: Int = 0
    
    // MARK: - 私有属性
    
    private var cancellables = Set<AnyCancellable>()
    private let debouncer = Debouncer(delay: 0.3)
    
    // MARK: - 初始化
    
    init(
        adjustService: AdjustServiceProtocol,
        curveService: CurveServiceProtocol,
        hslService: HSLServiceProtocol
    ) {
        self.adjustService = adjustService
        self.curveService = curveService
        self.hslService = hslService
        
        setupBindings()
        loadInitialState()
        
        print("🎨 AdjustViewModelRefactored 初始化完成")
    }
    
    // MARK: - 私有方法
    
    private func setupBindings() {
        // 监听参数变化并防抖保存
        $currentParameters
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] parameters in
                Task { [weak self] in
                    await self?.saveParametersDebounced(parameters)
                }
            }
            .store(in: &cancellables)
        
        // 监听曲线点变化
        $curvePoints
            .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main)
            .sink { [weak self] points in
                Task { [weak self] in
                    await self?.updateCurvePointsAsync(points)
                }
            }
            .store(in: &cancellables)
        
        print("✅ AdjustViewModelRefactored 绑定设置完成")
    }
    
    private func loadInitialState() {
        Task {
            do {
                state = .loading
                
                // 加载当前参数
                let parameters = await adjustService.getCurrentParameters()
                currentParameters = parameters
                
                // 加载曲线状态
                let rgbPoints = await curveService.getCurrentCurvePoints(for: .rgb)
                let redPoints = await curveService.getCurrentCurvePoints(for: .red)
                let greenPoints = await curveService.getCurrentCurvePoints(for: .green)
                let bluePoints = await curveService.getCurrentCurvePoints(for: .blue)
                
                curvePoints = [
                    .rgb: rgbPoints,
                    .red: redPoints,
                    .green: greenPoints,
                    .blue: bluePoints
                ]
                
                curveIntensity = await curveService.getCurrentCurveIntensity()
                isEnabled = await curveService.isCurveEnabled()
                renderQuality = await curveService.getCurrentRenderQuality()
                currentPreset = await curveService.getCurrentPreset()
                
                // 加载HSL状态
                selectedColorIndex = await hslService.getCurrentColorRangeIndex()
                currentHSLParameters = await hslService.getCurrentHSLParameters()
                
                state = .loaded(parameters)
                
                print("✅ AdjustViewModelRefactored 初始状态加载完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ AdjustViewModelRefactored 初始状态加载失败: \(error)")
            }
        }
    }
    
    private func saveParametersDebounced(_ parameters: FilterParameters) async {
        do {
            // 这里可以添加参数保存逻辑
            print("💾 参数自动保存: \(parameters)")
        } catch {
            print("❌ 参数保存失败: \(error)")
        }
    }
    
    private func updateCurvePointsAsync(_ points: [CurveChannel: [CGPoint]]) async {
        do {
            for (channel, channelPoints) in points {
                try await curveService.updateCurvePoints(channelPoints, for: channel)
            }
        } catch {
            print("❌ 曲线点更新失败: \(error)")
        }
    }
    
    // MARK: - 参数更新方法
    
    /// 更新单个参数
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) {
        // 立即更新本地状态
        currentParameters[keyPath: keyPath] = value
        
        // 异步保存到服务
        Task {
            do {
                try await adjustService.updateParameter(keyPath, value: value)
                print("✅ 参数更新成功: \(keyPath)")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 参数更新失败: \(error)")
            }
        }
    }
    
    /// 批量更新参数
    func batchUpdateParameters(_ updates: @escaping () -> Void) {
        // 立即更新本地状态
        updates()
        
        // 异步保存到服务
        Task {
            do {
                try await adjustService.batchUpdateParameters(updates)
                print("✅ 批量参数更新成功")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 批量参数更新失败: \(error)")
            }
        }
    }
    
    /// 重置所有参数
    func resetAllParameters() {
        Task {
            do {
                state = .loading
                
                try await adjustService.resetAllParameters()
                
                // 重新加载状态
                let parameters = await adjustService.getCurrentParameters()
                currentParameters = parameters
                
                // 重置曲线状态
                try await curveService.resetAllCurves()
                curvePoints = [
                    .rgb: CurveProcessor.createDefaultPoints(),
                    .red: CurveProcessor.createDefaultPoints(),
                    .green: CurveProcessor.createDefaultPoints(),
                    .blue: CurveProcessor.createDefaultPoints()
                ]
                curveIntensity = 1.0
                isEnabled = false
                currentPreset = nil
                selectedChannel = .rgb
                resetDragState()
                
                // 重置HSL状态
                try await hslService.resetAllHSLColors()
                selectedColorIndex = 0
                
                state = .loaded(parameters)
                
                print("✅ 所有参数重置完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 参数重置失败: \(error)")
            }
        }
    }
    
    // MARK: - 曲线管理方法
    
    /// 切换到指定通道
    func switchToChannel(_ channel: CurveChannel) {
        selectedChannel = channel
    }
    
    /// 重置拖拽相关状态
    func resetDragState() {
        draggedPointIndex = nil
    }
    
    /// 获取当前通道的曲线点
    func getCurrentCurvePoints() -> [CGPoint] {
        return curvePoints[selectedChannel] ?? CurveProcessor.createDefaultPoints()
    }
    
    /// 更新当前通道的曲线点
    func updateCurrentChannelCurvePoints(_ points: [CGPoint]) {
        curvePoints[selectedChannel] = points
    }
    
    /// 更新指定点的位置
    func updatePointPosition(index: Int, normalizedPoint: CGPoint) {
        guard var points = curvePoints[selectedChannel], index < points.count else {
            print("❌ 更新点位置失败：通道 \(selectedChannel.displayName)，索引 \(index)")
            return
        }
        
        // 设置拖拽状态
        draggedPointIndex = index
        
        // 边界约束
        let constrainedPoint = CGPoint(
            x: max(0.0, min(1.0, normalizedPoint.x)),
            y: max(0.0, min(1.0, normalizedPoint.y))
        )
        
        // 更新点位置
        points[index] = constrainedPoint
        curvePoints[selectedChannel] = points
        
        print("🎨 更新通道 \(selectedChannel.displayName) 点 \(index) 位置")
    }
    
    /// 结束点位置更新
    func endPointPositionUpdate() {
        draggedPointIndex = nil
    }
    
    /// 添加曲线点
    @discardableResult
    func addPoint(at normalizedPoint: CGPoint) -> Int {
        guard var points = curvePoints[selectedChannel] else { return -1 }
        
        let constrainedPoint = CGPoint(
            x: max(0.0, min(1.0, normalizedPoint.x)),
            y: max(0.0, min(1.0, normalizedPoint.y))
        )
        
        points.append(constrainedPoint)
        points.sort { $0.x < $1.x }
        
        let newIndex = points.firstIndex(where: { $0.x == constrainedPoint.x && $0.y == constrainedPoint.y }) ?? -1
        curvePoints[selectedChannel] = points
        
        return newIndex
    }
    
    /// 移除曲线点
    func removePoint(at index: Int) {
        guard var points = curvePoints[selectedChannel] else { return }
        guard index > 0 && index < points.count - 1 else { return }
        
        points.remove(at: index)
        curvePoints[selectedChannel] = points
    }
    
    /// 重置所有曲线状态到默认值
    func resetAllCurves() {
        Task {
            do {
                try await curveService.resetAllCurves()
                
                // 更新本地状态
                curvePoints = [
                    .rgb: CurveProcessor.createDefaultPoints(),
                    .red: CurveProcessor.createDefaultPoints(),
                    .green: CurveProcessor.createDefaultPoints(),
                    .blue: CurveProcessor.createDefaultPoints()
                ]
                curveIntensity = 1.0
                isEnabled = true
                currentPreset = nil
                selectedChannel = .rgb
                resetDragState()
                
                print("✅ 所有曲线状态已重置")
            } catch {
                print("❌ 曲线重置失败: \(error)")
            }
        }
    }
    
    /// 重置当前通道的曲线
    func resetCurrentChannel() {
        Task {
            do {
                try await curveService.resetCurrentChannel()
                curvePoints[selectedChannel] = CurveProcessor.createDefaultPoints()
                print("✅ 当前通道曲线已重置")
            } catch {
                print("❌ 当前通道曲线重置失败: \(error)")
            }
        }
    }
    
    /// 应用曲线预设
    func applyPreset(_ preset: CurveProcessor.CurvePreset, to channel: CurveChannel, intensity: Float = 1.0) {
        Task {
            do {
                try await curveService.applyPreset(preset, to: channel, intensity: intensity)
                
                // 更新本地状态
                curvePoints[channel] = preset.points
                currentPreset = preset
                curveIntensity = intensity
                isEnabled = true
                
                print("✅ 预设 '\(preset.rawValue)' 已应用到通道 \(channel.displayName)")
            } catch {
                print("❌ 预设应用失败: \(error)")
            }
        }
    }
    
    /// 重置指定通道
    func resetChannel(_ channel: CurveChannel) {
        Task {
            do {
                try await curveService.resetChannel(channel)
                curvePoints[channel] = CurveProcessor.createDefaultPoints()
                currentPreset = nil
                print("✅ 通道 \(channel.displayName) 已重置")
            } catch {
                print("❌ 通道重置失败: \(error)")
            }
        }
    }
    
    // MARK: - HSL管理方法
    
    /// 获取当前HSL参数
    func getCurrentHSLParameters() async -> (hue: Float, saturation: Float, luminance: Float) {
        return await hslService.getCurrentHSLParameters()
    }
    
    /// 更新HSL色相
    func updateHSLHue(_ hue: Float, for colorIndex: Int? = nil) {
        Task {
            do {
                try await hslService.updateHSLHue(hue, for: colorIndex)
                currentHSLParameters = await hslService.getCurrentHSLParameters()
                print("✅ HSL色相更新成功")
            } catch {
                print("❌ HSL色相更新失败: \(error)")
            }
        }
    }
    
    /// 更新HSL饱和度
    func updateHSLSaturation(_ saturation: Float, for colorIndex: Int? = nil) {
        Task {
            do {
                try await hslService.updateHSLSaturation(saturation, for: colorIndex)
                currentHSLParameters = await hslService.getCurrentHSLParameters()
                print("✅ HSL饱和度更新成功")
            } catch {
                print("❌ HSL饱和度更新失败: \(error)")
            }
        }
    }
    
    /// 更新HSL明度
    func updateHSLLuminance(_ luminance: Float, for colorIndex: Int? = nil) {
        Task {
            do {
                try await hslService.updateHSLLuminance(luminance, for: colorIndex)
                currentHSLParameters = await hslService.getCurrentHSLParameters()
                print("✅ HSL明度更新成功")
            } catch {
                print("❌ HSL明度更新失败: \(error)")
            }
        }
    }
    
    /// 切换HSL颜色范围
    func switchHSLColorRange(to index: Int) {
        selectedColorIndex = index
        Task {
            do {
                try await hslService.switchHSLColorRange(to: index)
                currentHSLParameters = await hslService.getCurrentHSLParameters()
                print("✅ HSL颜色范围切换成功")
            } catch {
                print("❌ HSL颜色范围切换失败: \(error)")
            }
        }
    }
    
    /// 重置当前选中颜色的HSL参数
    func resetCurrentHSLColor() {
        Task {
            do {
                try await hslService.resetCurrentHSLColor()
                print("✅ 当前HSL颜色重置成功")
            } catch {
                print("❌ 当前HSL颜色重置失败: \(error)")
            }
        }
    }
    
    /// 重置所有HSL颜色参数
    func resetAllHSLColors() {
        Task {
            do {
                try await hslService.resetAllHSLColors()
                selectedColorIndex = 0
                print("✅ 所有HSL颜色重置成功")
            } catch {
                print("❌ 所有HSL颜色重置失败: \(error)")
            }
        }
    }
    
    // MARK: - 校准相关方法
    
    /// 选择校准颜色
    func selectCalibrationColor(_ index: Int) {
        selectedCalibrationColorIndex = index
        print("🎨 [校准] 选择颜色: \(["红", "绿", "蓝"][index])")
    }
    
    /// 获取当前选中颜色的色相值
    func getCurrentCalibrationHue() -> Float {
        switch selectedCalibrationColorIndex {
        case 0: return currentParameters.redHue
        case 1: return currentParameters.greenHue
        case 2: return currentParameters.blueHue
        default: return 0.0
        }
    }
    
    /// 获取当前选中颜色的饱和度值
    func getCurrentCalibrationSaturation() -> Float {
        switch selectedCalibrationColorIndex {
        case 0: return currentParameters.redSaturation
        case 1: return currentParameters.greenSaturation
        case 2: return currentParameters.blueSaturation
        default: return 0.0
        }
    }
    
    /// 更新当前选中颜色的色相
    func updateCalibrationHue(_ value: Float) {
        switch selectedCalibrationColorIndex {
        case 0: updateParameter(\.redHue, value: value)
        case 1: updateParameter(\.greenHue, value: value)
        case 2: updateParameter(\.blueHue, value: value)
        default: break
        }
    }
    
    /// 更新当前选中颜色的饱和度
    func updateCalibrationSaturation(_ value: Float) {
        switch selectedCalibrationColorIndex {
        case 0: updateParameter(\.redSaturation, value: value)
        case 1: updateParameter(\.greenSaturation, value: value)
        case 2: updateParameter(\.blueSaturation, value: value)
        default: break
        }
    }
    
    /// 重置所有校准颜色
    func resetAllCalibrationColors() {
        batchUpdateParameters {
            self.currentParameters.redHue = 0.0
            self.currentParameters.greenHue = 0.0
            self.currentParameters.blueHue = 0.0
            self.currentParameters.redSaturation = 0.0
            self.currentParameters.greenSaturation = 0.0
            self.currentParameters.blueSaturation = 0.0
        }
        print("🔄 [校准] 所有颜色已重置")
    }
    
    // MARK: - 色调分离管理方法
    
    /// 切换色调选项
    func switchToToneOption(_ option: String) {
        selectedToneOption = option
    }
    
    /// 更新色调参数
    func updateToneHue(_ normalizedAngle: Double, saturation: Double) {
        // 根据选中的色调选项更新对应参数
        if selectedToneOption == "阴影" {
            updateParameter(\.shadowHue, value: Float(normalizedAngle))
            updateParameter(\.shadowSaturation, value: Float(saturation))
        } else {
            updateParameter(\.highlightHue, value: Float(normalizedAngle))
            updateParameter(\.highlightSaturation, value: Float(saturation))
        }
    }
    
    /// 重置当前选中的色调
    func resetCurrentTone() {
        if selectedToneOption == "阴影" {
            batchUpdateParameters {
                self.currentParameters.shadowHue = 0.0
                self.currentParameters.shadowSaturation = 0.0
            }
        } else {
            batchUpdateParameters {
                self.currentParameters.highlightHue = 0.0
                self.currentParameters.highlightSaturation = 0.0
            }
        }
    }
    
    /// 重置所有色调分离参数
    func resetAllTones() {
        batchUpdateParameters {
            self.currentParameters.shadowHue = 0.0
            self.currentParameters.shadowSaturation = 0.0
            self.currentParameters.highlightHue = 0.0
            self.currentParameters.highlightSaturation = 0.0
            self.currentParameters.splitToningBalance = 0.0
        }
    }
    
    /// 更新平衡参数
    func updateBalance(_ value: Float) {
        updateParameter(\.splitToningBalance, value: value)
    }
    
    // MARK: - 计算属性
    
    /// RGB曲线是否启用
    var rgbCurveEnabled: Bool {
        !isLinearCurve(curvePoints[.rgb] ?? [])
    }
    
    /// 分离通道曲线是否启用
    var channelCurvesEnabled: Bool {
        [CurveChannel.red, .green, .blue].contains { channel in
            !isLinearCurve(curvePoints[channel] ?? [])
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 检查曲线是否为线性
    private func isLinearCurve(_ points: [CGPoint]) -> Bool {
        guard points.count == 2 else { return false }
        let first = points[0]
        let last = points[1]
        return abs(first.x - 0.0) < 0.01 && abs(first.y - 0.0) < 0.01 &&
               abs(last.x - 1.0) < 0.01 && abs(last.y - 1.0) < 0.01
    }
}

// MARK: - 共享类型已在 Lomo/Models/Shared/ 中定义
// ViewState 和 AppError 类型定义已移至共享文件

// MARK: - Debouncer 工具类已移至 Lomo/Utils/Debouncer.swift