import Foundation
import SwiftUI
import Combine

/// 相纸视图模型 - MVVM-S架构
/// View和Service之间的桥梁，负责UI状态管理和业务逻辑协调
class PaperViewModel: ObservableObject {

    // MARK: - Published Properties（UI状态管理）

    /// 当前全局活跃的相纸类型和预设索引
    @Published var activePaperType: String = ""
    @Published var activePaperPresetIndex: Int = -1

    /// 是否正在加载
    @Published var isLoading = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 是否显示错误提示
    @Published var showError = false

    // MARK: - 依赖注入
    private let paperService: PaperServiceProtocol
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化
    init(paperService: PaperServiceProtocol) {
        self.paperService = paperService
        print("🎨 [PaperViewModel] 初始化完成")

        setupErrorHandling()
        loadSavedSettings()
    }

    // MARK: - 预设类型枚举
    enum PresetType: String {
        case polaroid = "polaroid"
        case film = "film"
        case vintage = "vintage"
        case fashion = "fashion"
        case ins = "ins"
    }

    // MARK: - 公共方法（UI交互）

    /// 通用的预设选择方法，处理所有类型的预设
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    func selectPreset(type: PresetType, index: Int) {
        guard !isLoading else { return }

        isLoading = true
        let typeString = type.rawValue

        Task {
            do {
                // 如果点击已选中的预设，则取消选择
                if activePaperType == typeString && activePaperPresetIndex == index {
                    // 取消选择
                    try await paperService.updateSetting(\PaperModel.activeFilterType, value: "")
                    try await paperService.updateSetting(\PaperModel.activePresetIndex, value: -1)

                    await MainActor.run {
                        self.activePaperType = ""
                        self.activePaperPresetIndex = -1
                    }
                } else {
                    // 更新对应的预设索引
                    switch type {
                    case .polaroid:
                        try await paperService.updateSetting(\PaperModel.selectedPolaroidPreset, value: index)
                    case .film:
                        try await paperService.updateSetting(\PaperModel.selectedFilmPreset, value: index)
                    case .vintage:
                        try await paperService.updateSetting(\PaperModel.selectedVintagePreset, value: index)
                    case .fashion:
                        try await paperService.updateSetting(\PaperModel.selectedFashionPreset, value: index)
                    case .ins:
                        try await paperService.updateSetting(\PaperModel.selectedINSPreset, value: index)
                    }

                    // 更新全局活跃状态
                    try await paperService.updateSetting(\PaperModel.activeFilterType, value: typeString)
                    try await paperService.updateSetting(\PaperModel.activePresetIndex, value: index)

                    await MainActor.run {
                        self.activePaperType = typeString
                        self.activePaperPresetIndex = index
                    }
                }

                await MainActor.run {
                    self.isLoading = false
                }

                print("🎨 [PaperViewModel] 预设选择完成: \(typeString) - \(index)")

            } catch {
                await MainActor.run {
                    self.handleError(error)
                }
            }
        }
    }

    /// 选择宝丽来预设
    /// - Parameter index: 预设索引
    func selectPolaroidPreset(_ index: Int) {
        selectPreset(type: .polaroid, index: index)
    }

    /// 选择胶片预设
    /// - Parameter index: 预设索引
    func selectFilmPreset(_ index: Int) {
        selectPreset(type: .film, index: index)
    }

    /// 选择复古预设
    /// - Parameter index: 预设索引
    func selectVintagePreset(_ index: Int) {
        selectPreset(type: .vintage, index: index)
    }

    /// 选择时尚预设
    /// - Parameter index: 预设索引
    func selectFashionPreset(_ index: Int) {
        selectPreset(type: .fashion, index: index)
    }

    /// 选择INS风预设
    /// - Parameter index: 预设索引
    func selectINSPreset(_ index: Int) {
        selectPreset(type: .ins, index: index)
    }

    /// 添加到最近使用的预设
    /// - Parameter preset: 预设名称
    func addToRecentPresets(_ preset: String) {
        Task {
            do {
                try await paperService.addToRecentPresets(preset)
                print("🎨 [PaperViewModel] 添加到最近使用: \(preset)")
            } catch {
                await MainActor.run {
                    self.handleError(error)
                }
            }
        }
    }

    /// 切换预设收藏状态
    /// - Parameter preset: 预设名称
    func toggleFavorite(_ preset: String) {
        Task {
            do {
                try await paperService.toggleFavorite(preset)
                print("🎨 [PaperViewModel] 切换收藏状态: \(preset)")
            } catch {
                await MainActor.run {
                    self.handleError(error)
                }
            }
        }
    }

    // MARK: - 私有方法

    /// 加载保存的设置
    private func loadSavedSettings() {
        Task {
            do {
                let settings = try await paperService.getSettings()

                await MainActor.run {
                    self.activePaperType = settings.activeFilterType
                    self.activePaperPresetIndex = settings.activePresetIndex
                }

            } catch {
                await MainActor.run {
                    self.handleError(error)
                }
            }
        }
    }

    /// 处理错误
    private func handleError(_ error: Error) {
        isLoading = false
        errorMessage = error.localizedDescription
        showError = true

        print("❌ [PaperViewModel] 错误: \(error.localizedDescription)")
    }

    /// 设置错误处理
    private func setupErrorHandling() {
        $errorMessage
            .compactMap { $0 }
            .sink { [weak self] _ in
                self?.showError = true
            }
            .store(in: &cancellables)
    }
}
