// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine
import UIKit

/// 滤镜应用模块的视图模型 - MVVM-S架构重构版
/// 消除单例依赖，实施真正的依赖注入
@MainActor
class FilterViewModelRefactored: ObservableObject {
    
    // MARK: - 依赖注入
    
    private let filterService: FilterServiceProtocol
    
    // MARK: - 状态管理 (集中在ViewModel)
    
    /// 视图状态
    @Published private(set) var state: ViewState<FilterPreset> = .idle
    
    /// 当前滤镜参数
    @Published var currentParameters = FilterParameters()
    
    /// 当前选中的预设类型
    @Published var selectedPresetType: FilterPresetType? = nil
    
    /// 当前选中的预设索引
    @Published var selectedPresetIndex: Int = -1
    
    /// 当前选中的预设
    @Published var selectedPreset: FilterPreset? = nil
    
    /// 是否有活跃的滤镜效果
    @Published var hasActiveFilter: Bool = false
    
    /// 原始图像
    @Published var originalImage: UIImage? = nil
    
    /// 处理后的图像
    @Published var processedImage: UIImage? = nil
    
    /// 当前使用的LUT文件路径
    @Published var currentLUTPath: String? = nil
    
    /// LUT强度 (0.0-1.0)
    @Published var lutIntensity: Float = 1.0
    
    /// 是否启用LUT
    @Published var isLUTEnabled: Bool = false
    
    /// 预设强度 (0.0-1.0)
    @Published var presetIntensity: Float = 1.0
    
    /// 是否正在处理图像
    @Published var isProcessing: Bool = false
    
    // MARK: - 私有属性
    
    private var cancellables = Set<AnyCancellable>()
    private let debouncer = Debouncer(delay: 0.3)
    
    // MARK: - 初始化
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
        
        setupBindings()
        loadInitialState()
        
        print("🎨 FilterViewModelRefactored 初始化完成")
    }
    
    // MARK: - 私有方法
    
    private func setupBindings() {
        // 监听参数变化并防抖保存
        $currentParameters
            .debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] parameters in
                Task { [weak self] in
                    await self?.saveParametersDebounced(parameters)
                }
            }
            .store(in: &cancellables)
        
        // 监听LUT强度变化
        $lutIntensity
            .debounce(for: .milliseconds(200), scheduler: DispatchQueue.main)
            .sink { [weak self] intensity in
                Task { [weak self] in
                    await self?.updateLUTIntensityAsync(intensity)
                }
            }
            .store(in: &cancellables)
        
        // 监听预设强度变化
        $presetIntensity
            .debounce(for: .milliseconds(200), scheduler: DispatchQueue.main)
            .sink { [weak self] intensity in
                Task { [weak self] in
                    await self?.updatePresetIntensityAsync(intensity)
                }
            }
            .store(in: &cancellables)
        
        print("✅ FilterViewModelRefactored 绑定设置完成")
    }
    
    private func loadInitialState() {
        Task {
            do {
                state = .loading
                
                // 加载当前参数
                let parameters = await filterService.getCurrentParameters()
                currentParameters = parameters
                
                // 加载滤镜状态
                hasActiveFilter = await filterService.hasActiveFilter()
                
                state = .idle
                
                print("✅ FilterViewModelRefactored 初始状态加载完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ FilterViewModelRefactored 初始状态加载失败: \(error)")
            }
        }
    }
    
    private func saveParametersDebounced(_ parameters: FilterParameters) async {
        do {
            // 这里可以添加参数保存逻辑
            print("💾 滤镜参数自动保存: \(parameters)")
        } catch {
            print("❌ 滤镜参数保存失败: \(error)")
        }
    }
    
    private func updateLUTIntensityAsync(_ intensity: Float) async {
        do {
            try await filterService.updateLUTIntensity(intensity)
        } catch {
            print("❌ LUT强度更新失败: \(error)")
        }
    }
    
    private func updatePresetIntensityAsync(_ intensity: Float) async {
        do {
            try await filterService.updatePresetIntensity(intensity)
        } catch {
            print("❌ 预设强度更新失败: \(error)")
        }
    }
    
    // MARK: - 预设操作方法
    
    /// 应用预设
    func applyPreset(type: FilterPresetType, index: Int) {
        Task {
            do {
                state = .loading
                
                try await filterService.applyPreset(type: type, index: index)
                
                // 更新本地状态
                selectedPresetType = type
                selectedPresetIndex = index
                hasActiveFilter = true
                
                // 获取应用后的参数
                currentParameters = await filterService.getCurrentParameters()
                
                // 检查是否有预设对象
                let presets = await filterService.getPresets(for: type)
                if index < presets.count {
                    selectedPreset = presets[index]
                    state = .loaded(presets[index])
                } else {
                    selectedPreset = nil
                    state = .idle
                }
                
                print("✅ 预设应用成功: \(type.rawValue) - \(index)")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 预设应用失败: \(error)")
            }
        }
    }
    
    /// 清除当前预设
    func clearPreset() {
        Task {
            do {
                state = .loading
                
                try await filterService.clearPreset()
                
                // 更新本地状态
                selectedPresetType = nil
                selectedPresetIndex = -1
                selectedPreset = nil
                hasActiveFilter = false
                
                // 重置参数
                currentParameters = await filterService.getCurrentParameters()
                
                // 清除LUT状态
                currentLUTPath = nil
                isLUTEnabled = false
                
                state = .idle
                
                print("✅ 预设清除成功")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 预设清除失败: \(error)")
            }
        }
    }
    
    /// 检查是否选中了指定预设
    func isPresetSelected(type: FilterPresetType, index: Int) -> Bool {
        return selectedPresetType == type && selectedPresetIndex == index
    }
    
    /// 获取指定类型的所有预设
    func getPresets(for type: FilterPresetType) -> [FilterPreset] {
        // 这里可以缓存预设数据，避免频繁异步调用
        return []
    }
    
    // MARK: - 参数操作方法
    
    /// 更新单个参数
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) {
        // 立即更新本地状态
        currentParameters[keyPath: keyPath] = value
        hasActiveFilter = true
        
        // 异步保存到服务
        Task {
            do {
                try await filterService.updateParameter(keyPath, value: value)
                print("✅ 滤镜参数更新成功: \(keyPath)")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 滤镜参数更新失败: \(error)")
            }
        }
    }
    
    /// 批量更新参数
    func batchUpdateParameters(_ updates: @escaping () -> Void) {
        // 立即更新本地状态
        updates()
        hasActiveFilter = true
        
        // 异步保存到服务
        Task {
            do {
                try await filterService.batchUpdateParameters(updates)
                print("✅ 批量滤镜参数更新成功")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 批量滤镜参数更新失败: \(error)")
            }
        }
    }
    
    /// 重置所有参数
    func resetParameters() {
        Task {
            do {
                state = .loading
                
                try await filterService.resetParameters()
                
                // 重新加载状态
                currentParameters = await filterService.getCurrentParameters()
                hasActiveFilter = await filterService.hasActiveFilter()
                
                // 重置本地状态
                selectedPresetType = nil
                selectedPresetIndex = -1
                selectedPreset = nil
                currentLUTPath = nil
                isLUTEnabled = false
                presetIntensity = 1.0
                lutIntensity = 1.0
                
                state = .idle
                
                print("✅ 滤镜参数重置完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 滤镜参数重置失败: \(error)")
            }
        }
    }
    
    // MARK: - LUT操作方法
    
    /// 应用LUT文件
    func applyLUT(lutPath: String, intensity: Float = 1.0) {
        Task {
            do {
                try await filterService.applyLUT(lutPath: lutPath, intensity: intensity)
                
                // 更新本地状态
                currentLUTPath = lutPath
                lutIntensity = intensity
                isLUTEnabled = true
                hasActiveFilter = true
                
                print("✅ LUT应用成功: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
            } catch {
                state = .error(AppError.from(error))
                print("❌ LUT应用失败: \(error)")
            }
        }
    }
    
    /// 更新LUT强度
    func updateLUTIntensity(_ intensity: Float) {
        // 立即更新本地状态
        lutIntensity = max(0.0, min(1.0, intensity))
        
        // 异步调用已通过绑定处理
        print("🔧 LUT强度更新: \(Int(lutIntensity * 100))%")
    }
    
    /// 更新预设强度
    func updatePresetIntensity(_ intensity: Float) {
        // 立即更新本地状态
        presetIntensity = max(0.0, min(1.0, intensity))
        
        // 异步调用已通过绑定处理
        print("🔧 预设强度更新: \(Int(presetIntensity * 100))%")
    }
    
    /// 切换LUT启用状态
    func toggleLUT() {
        Task {
            do {
                try await filterService.toggleLUT()
                
                // 更新本地状态
                isLUTEnabled.toggle()
                hasActiveFilter = await filterService.hasActiveFilter()
                
                print(isLUTEnabled ? "✅ LUT已启用" : "❌ LUT已禁用")
            } catch {
                state = .error(AppError.from(error))
                print("❌ LUT切换失败: \(error)")
            }
        }
    }
    
    /// 清除LUT
    func clearLUT() {
        Task {
            do {
                try await filterService.clearLUT()
                
                // 更新本地状态
                currentLUTPath = nil
                isLUTEnabled = false
                hasActiveFilter = await filterService.hasActiveFilter()
                
                print("🗑️ LUT已清除")
            } catch {
                state = .error(AppError.from(error))
                print("❌ LUT清除失败: \(error)")
            }
        }
    }
    
    // MARK: - 图像处理方法
    
    /// 设置原始图像
    func setOriginalImage(_ image: UIImage) {
        Task {
            do {
                try await filterService.setOriginalImage(image)
                
                // 更新本地状态
                originalImage = image
                
                print("✅ 原始图像设置成功 - 尺寸: \(image.size)")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 原始图像设置失败: \(error)")
            }
        }
    }
    
    /// 获取当前显示的图像
    func getCurrentDisplayImage() -> UIImage? {
        // 返回本地缓存的图像，避免频繁异步调用
        return processedImage ?? originalImage
    }
    
    /// 获取实时预览图像
    func getRealtimePreview() {
        Task {
            do {
                let previewImage = await filterService.getRealtimePreview()
                processedImage = previewImage
                print("✅ 实时预览更新完成")
            } catch {
                print("❌ 实时预览获取失败: \(error)")
            }
        }
    }
    
    /// 获取最终输出图像
    func getFinalOutputImage(highQuality: Bool = true) {
        Task {
            do {
                isProcessing = true
                let outputImage = await filterService.getFinalOutputImage(highQuality: highQuality)
                processedImage = outputImage
                isProcessing = false
                print("✅ 最终输出图像生成完成")
            } catch {
                isProcessing = false
                print("❌ 最终输出图像生成失败: \(error)")
            }
        }
    }
    
    // MARK: - 状态查询方法
    
    /// 检查是否有未保存的更改
    func hasUnsavedChanges() -> Bool {
        return hasActiveFilter || isLUTEnabled
    }
    
    /// 刷新滤镜状态
    func refreshFilterState() {
        Task {
            do {
                // 重新加载所有状态
                currentParameters = await filterService.getCurrentParameters()
                hasActiveFilter = await filterService.hasActiveFilter()
                
                print("✅ 滤镜状态刷新完成")
            } catch {
                print("❌ 滤镜状态刷新失败: \(error)")
            }
        }
    }
    
    // MARK: - 设置管理方法
    
    /// 保存当前设置
    func saveCurrentSettings() {
        Task {
            do {
                let settings = await filterService.getSettings()
                try await filterService.saveSettings(settings)
                print("✅ 滤镜设置保存成功")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 滤镜设置保存失败: \(error)")
            }
        }
    }
    
    /// 重置到默认设置
    func resetToDefaults() {
        Task {
            do {
                state = .loading
                
                try await filterService.resetToDefaults()
                
                // 重新加载状态
                await loadInitialState()
                
                print("✅ 重置到默认设置完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ 重置到默认设置失败: \(error)")
            }
        }
    }
    
    // MARK: - 便利方法
    
    /// 检查指定预设是否可用
    func isPresetAvailable(type: FilterPresetType, index: Int) -> Bool {
        // 这里可以添加预设可用性检查逻辑
        return true
    }
    
    /// 获取当前滤镜效果的描述
    func getCurrentFilterDescription() -> String {
        if let preset = selectedPreset {
            return preset.name
        } else if hasActiveFilter {
            return "自定义调整"
        } else {
            return "无滤镜"
        }
    }
    
    /// 获取当前LUT效果的描述
    func getCurrentLUTDescription() -> String {
        if isLUTEnabled, let lutPath = currentLUTPath {
            let fileName = URL(fileURLWithPath: lutPath).lastPathComponent
            return "\(fileName) (\(Int(lutIntensity * 100))%)"
        } else {
            return "无LUT"
        }
    }
    
    /// 检查是否可以应用更多效果
    func canApplyMoreEffects() -> Bool {
        // 这里可以添加效果限制检查逻辑
        return true
    }
    
    /// 获取处理进度
    func getProcessingProgress() -> Float {
        return isProcessing ? 0.5 : (hasActiveFilter ? 1.0 : 0.0)
    }
}