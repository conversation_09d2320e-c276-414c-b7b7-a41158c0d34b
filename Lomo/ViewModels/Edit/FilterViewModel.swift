import Foundation
import SwiftUI
import Combine

/// 滤镜应用模块的视图模型 - MVVM-S架构
/// 复制自：FilterStateManager.swift (@Published属性)
class FilterViewModel: ObservableObject {

    // MARK: - 依赖注入
    private let filterService = FilterService.shared

    // MARK: - 滤镜状态属性 (从FilterStateManager.swift完整复制)

    /// 当前滤镜参数
    @Published var currentParameters = FilterParameters()

    /// 当前选中的预设类型
    @Published var selectedPresetType: FilterPresetType? = nil

    /// 当前选中的预设索引
    @Published var selectedPresetIndex: Int = -1

    /// 当前选中的预设
    @Published var selectedPreset: FilterPreset? = nil

    /// 是否有活跃的滤镜效果
    @Published var hasActiveFilter: Bool = false

    /// 原始图像
    @Published var originalImage: UIImage? = nil

    /// 处理后的图像
    @Published var processedImage: UIImage? = nil

    /// 当前使用的LUT文件路径
    @Published var currentLUTPath: String? = nil

    /// LUT强度 (0.0-1.0)
    @Published var lutIntensity: Float = 1.0

    /// 是否启用LUT
    @Published var isLUTEnabled: Bool = false

    /// 预设强度 (0.0-1.0) - 控制预设参数的整体强度
    @Published var presetIntensity: Float = 1.0

    /// 是否正在处理图像
    @Published var isProcessing: Bool = false

    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化
    init() {
        setupBindings()
        print("🎨 FilterViewModel 初始化完成")
    }

    // MARK: - 绑定设置
    private func setupBindings() {
        // 绑定FilterService的状态到ViewModel
        filterService.$currentParameters
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentParameters, on: self)
            .store(in: &cancellables)

        filterService.$selectedPresetType
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedPresetType, on: self)
            .store(in: &cancellables)

        filterService.$selectedPresetIndex
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedPresetIndex, on: self)
            .store(in: &cancellables)

        filterService.$selectedPreset
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedPreset, on: self)
            .store(in: &cancellables)

        filterService.$hasActiveFilter
            .receive(on: DispatchQueue.main)
            .assign(to: \.hasActiveFilter, on: self)
            .store(in: &cancellables)

        filterService.$originalImage
            .receive(on: DispatchQueue.main)
            .assign(to: \.originalImage, on: self)
            .store(in: &cancellables)

        filterService.$processedImage
            .receive(on: DispatchQueue.main)
            .assign(to: \.processedImage, on: self)
            .store(in: &cancellables)

        filterService.$currentLUTPath
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentLUTPath, on: self)
            .store(in: &cancellables)

        filterService.$lutIntensity
            .receive(on: DispatchQueue.main)
            .assign(to: \.lutIntensity, on: self)
            .store(in: &cancellables)

        filterService.$isLUTEnabled
            .receive(on: DispatchQueue.main)
            .assign(to: \.isLUTEnabled, on: self)
            .store(in: &cancellables)

        filterService.$presetIntensity
            .receive(on: DispatchQueue.main)
            .assign(to: \.presetIntensity, on: self)
            .store(in: &cancellables)

        filterService.$isProcessing
            .receive(on: DispatchQueue.main)
            .assign(to: \.isProcessing, on: self)
            .store(in: &cancellables)

        print("✅ FilterViewModel 绑定设置完成")
    }

    // MARK: - 公共方法 (委托给FilterService)

    /// 应用预设
    func applyPreset(type: FilterPresetType, index: Int) {
        filterService.applyPreset(type: type, index: index)
    }

    /// 清除当前预设
    func clearPreset() {
        filterService.clearPreset()
    }

    /// 检查是否选中了指定预设
    func isPresetSelected(type: FilterPresetType, index: Int) -> Bool {
        return filterService.isPresetSelected(type: type, index: index)
    }

    /// 更新LUT强度
    func updateLUTIntensity(_ intensity: Float) {
        filterService.updateLUTIntensity(intensity)
    }

    /// 更新预设强度
    func updatePresetIntensity(_ intensity: Float) {
        filterService.updatePresetIntensity(intensity)
    }

    /// 应用LUT文件
    func applyLUT(lutPath: String, intensity: Float = 1.0) {
        filterService.applyLUT(lutPath: lutPath, intensity: intensity)
    }

    /// 调整LUT强度
    func adjustLUTIntensity(_ intensity: Float) {
        filterService.adjustLUTIntensity(intensity)
    }

    /// 切换LUT启用状态
    func toggleLUT() {
        filterService.toggleLUT()
    }

    /// 清除LUT
    func clearLUT() {
        filterService.clearLUT()
    }

    /// 设置原始图像
    func setOriginalImage(_ image: UIImage) {
        filterService.setOriginalImage(image)
    }

    /// 获取当前显示的图像
    func getCurrentDisplayImage() -> UIImage? {
        return filterService.getCurrentDisplayImage()
    }

    /// 获取实时预览图像
    func getRealtimePreview() -> UIImage? {
        return filterService.getRealtimePreview()
    }

    /// 获取最终输出图像
    func getFinalOutputImage(highQuality: Bool = true) -> UIImage? {
        return filterService.getFinalOutputImage(highQuality: highQuality)
    }

    /// 获取指定类型的所有预设
    func getPresets(for type: FilterPresetType) -> [FilterPreset] {
        return filterService.getPresets(for: type)
    }

    /// 检查是否有未保存的更改
    func hasUnsavedChanges() -> Bool {
        return filterService.hasUnsavedChanges()
    }
}
