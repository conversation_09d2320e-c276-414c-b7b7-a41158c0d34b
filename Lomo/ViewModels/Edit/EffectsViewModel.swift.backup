// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine

/// 特效视图模型 - MVVM-S架构
/// 管理特效模块的UI状态和用户交互，通过依赖注入与Service层通信
@MainActor
class EffectsViewModel: ObservableObject {
    
    // MARK: - 状态管理
    @Published private(set) var state: ViewState<EffectsModel> = .idle
    @Published var effectsSettings = EffectsModel()
    @Published private(set) var isProcessing = false
    @Published private(set) var processingProgress: Double = 0.0
    
    // MARK: - 预设数据
    @Published var availableLightLeakPresets: [LightLeakPreset] = []
    @Published var availableGrainPresets: [GrainPreset] = []
    @Published var availableScratchPresets: [ScratchPreset] = []
    
    // MARK: - 依赖注入
    private let effectsService: EffectsServiceProtocol
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - 初始化
    init(effectsService: EffectsServiceProtocol) {
        self.effectsService = effectsService
        print("🎨 [EffectsViewModel] 初始化完成 - 使用MVVM-S架构")
        
        setupBindings()
        loadInitialData()
    }
    
    // MARK: - 数据加载
    
    func loadInitialData() {
        Task {
            await loadSettings()
            await loadPresets()
        }
    }
    
    private func loadSettings() async {
        state = .loading
        
        do {
            let settings = await effectsService.getEffectsSettings()
            effectsSettings = settings
            state = .loaded(settings)
            print("✅ [EffectsViewModel] 设置加载成功")
        } catch {
            state = .error(AppError.from(error))
            print("❌ [EffectsViewModel] 设置加载失败: \(error)")
        }
    }
    
    private func loadPresets() async {
        do {
            availableLightLeakPresets = await effectsService.getAvailableLightLeakPresets()
            availableGrainPresets = await effectsService.getAvailableGrainPresets()
            availableScratchPresets = await effectsService.getAvailableScratchPresets()
            print("✅ [EffectsViewModel] 预设数据加载完成")
        } catch {
            print("❌ [EffectsViewModel] 预设数据加载失败: \(error)")
        }
    }
    
    // MARK: - 漏光效果
    
    func updateLightLeakIntensity(_ intensity: Double) {
        effectsSettings.leakIntensity = intensity
        
        Task {
            await effectsService.updateLightLeakIntensity(intensity)
        }
    }
    
    func selectLightLeakPreset(_ preset: LightLeakPreset?) {
        effectsSettings.selectedLeakPreset = preset?.id ?? ""
        effectsSettings.isLeakEnabled = preset != nil
        
        Task {
            await effectsService.selectLightLeakPreset(preset)
        }
    }
    
    func toggleLightLeakEnabled() {
        effectsSettings.isLeakEnabled.toggle()
        
        Task {
            await effectsService.toggleLightLeakEnabled()
        }
    }
    
    // MARK: - 颗粒效果
    
    func updateGrainIntensity(_ intensity: Double) {
        effectsSettings.grainIntensity = intensity
        
        Task {
            await effectsService.updateGrainIntensity(intensity)
        }
    }
    
    func selectGrainPreset(_ preset: GrainPreset?) {
        effectsSettings.selectedGrainPreset = preset?.id ?? ""
        effectsSettings.isGrainEnabled = preset != nil
        
        Task {
            await effectsService.selectGrainPreset(preset)
        }
    }
    
    func toggleGrainEnabled() {
        effectsSettings.isGrainEnabled.toggle()
        
        Task {
            await effectsService.toggleGrainEnabled()
        }
    }
    
    // MARK: - 划痕效果
    
    func updateScratchIntensity(_ intensity: Double) {
        effectsSettings.scratchIntensity = intensity
        
        Task {
            await effectsService.updateScratchIntensity(intensity)
        }
    }
    
    func selectScratchPreset(_ preset: ScratchPreset?) {
        effectsSettings.selectedScratchPreset = preset?.id ?? ""
        effectsSettings.isScratchEnabled = preset != nil
        
        Task {
            await effectsService.selectScratchPreset(preset)
        }
    }
    
    func toggleScratchEnabled() {
        effectsSettings.isScratchEnabled.toggle()
        
        Task {
            await effectsService.toggleScratchEnabled()
        }
    }
    
    // MARK: - 时间戳效果
    
    func updateTimeStyle(_ style: String) {
        effectsSettings.selectedTimeStyle = style
        effectsSettings.isTimeEnabled = !style.isEmpty
        
        Task {
            await effectsService.updateTimeStyle(style)
        }
    }
    
    func updateTimeColor(_ color: String) {
        effectsSettings.selectedTimeColor = color
        
        Task {
            await effectsService.updateTimeColor(color)
        }
    }
    
    func updateTimePosition(_ position: String) {
        effectsSettings.selectedTimePosition = position
        
        Task {
            await effectsService.updateTimePosition(position)
        }
    }
    
    func toggleTimeEnabled() {
        effectsSettings.isTimeEnabled.toggle()
        
        Task {
            await effectsService.toggleTimeEnabled()
        }
    }
    
    // MARK: - 图像处理
    
    func applyAllEffectsToImage(_ image: UIImage) async -> UIImage {
        isProcessing = true
        processingProgress = 0.0
        
        defer {
            isProcessing = false
            processingProgress = 1.0
        }
        
        do {
            let result = try await effectsService.applyAllEffectsToImage(image)
            
            // 监控处理进度
            await monitorProcessingProgress()
            
            print("✅ [EffectsViewModel] 所有特效应用完成")
            return result
        } catch {
            state = .error(AppError.from(error))
            print("❌ [EffectsViewModel] 特效应用失败: \(error)")
            return image
        }
    }
    
    func applySingleEffectToImage(_ image: UIImage, effectType: EffectType) async -> UIImage {
        isProcessing = true
        processingProgress = 0.0
        
        defer {
            isProcessing = false
            processingProgress = 1.0
        }
        
        do {
            let result = try await effectsService.applySingleEffectToImage(image, effectType: effectType)
            print("✅ [EffectsViewModel] \(effectType.displayName)效果应用完成")
            return result
        } catch {
            state = .error(AppError.from(error))
            print("❌ [EffectsViewModel] \(effectType.displayName)效果应用失败: \(error)")
            return image
        }
    }
    
    // MARK: - 设置管理
    
    func resetAllSettings() {
        Task {
            do {
                try await effectsService.resetAllSettings()
                await loadSettings()
                print("✅ [EffectsViewModel] 重置所有设置完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ [EffectsViewModel] 重置设置失败: \(error)")
            }
        }
    }
    
    func saveCurrentSettings() {
        Task {
            do {
                try await effectsService.saveEffectsSettings(effectsSettings)
                print("✅ [EffectsViewModel] 设置保存完成")
            } catch {
                state = .error(AppError.from(error))
                print("❌ [EffectsViewModel] 设置保存失败: \(error)")
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置数据绑定
    private func setupBindings() {
        // 监听设置变化，自动保存
        $effectsSettings
            .debounce(for: .milliseconds(500), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] settings in
                Task { @MainActor in
                    await self?.autoSaveSettings(settings)
                }
            }
            .store(in: &cancellables)
    }
    
    /// 自动保存设置
    private func autoSaveSettings(_ settings: EffectsModel) async {
        do {
            try await effectsService.saveEffectsSettings(settings)
        } catch {
            print("⚠️ [EffectsViewModel] 自动保存失败: \(error)")
        }
    }
    
    /// 监控处理进度
    private func monitorProcessingProgress() async {
        while isProcessing {
            let progress = await effectsService.getProcessingProgress()
            processingProgress = progress
            
            if progress >= 1.0 {
                break
            }
            
            try? await Task.sleep(nanoseconds: 100_000_000) // 0.1秒
        }
    }
    
    // MARK: - 便利方法
    
    /// 获取选中的漏光预设
    var selectedLightLeakPreset: LightLeakPreset? {
        guard !effectsSettings.selectedLeakPreset.isEmpty else { return nil }
        return availableLightLeakPresets.first { $0.id == effectsSettings.selectedLeakPreset }
    }
    
    /// 获取选中的颗粒预设
    var selectedGrainPreset: GrainPreset? {
        guard !effectsSettings.selectedGrainPreset.isEmpty else { return nil }
        return availableGrainPresets.first { $0.id == effectsSettings.selectedGrainPreset }
    }
    
    /// 获取选中的划痕预设
    var selectedScratchPreset: ScratchPreset? {
        guard !effectsSettings.selectedScratchPreset.isEmpty else { return nil }
        return availableScratchPresets.first { $0.id == effectsSettings.selectedScratchPreset }
    }
    
    /// 检查是否有任何效果启用
    var hasAnyEffectEnabled: Bool {
        return effectsSettings.isTimeEnabled ||
               effectsSettings.isLeakEnabled ||
               effectsSettings.isGrainEnabled ||
               effectsSettings.isScratchEnabled
    }
    
    /// 获取启用的效果数量
    var enabledEffectsCount: Int {
        var count = 0
        if effectsSettings.isTimeEnabled { count += 1 }
        if effectsSettings.isLeakEnabled { count += 1 }
        if effectsSettings.isGrainEnabled { count += 1 }
        if effectsSettings.isScratchEnabled { count += 1 }
        return count
    }
    
    // MARK: - 便利属性（用于View绑定）
    
    /// 颗粒参数（兼容性属性）
    var grainParameters: GrainParameters {
        var params = GrainParameters()
        params.intensity = effectsSettings.grainIntensity
        params.selectedPreset = selectedGrainPreset
        params.isEnabled = effectsSettings.isGrainEnabled
        return params
    }
    
    /// 划痕参数（兼容性属性）
    var scratchParameters: ScratchParameters {
        var params = ScratchParameters()
        params.intensity = effectsSettings.scratchIntensity
        params.selectedPreset = selectedScratchPreset
        params.isEnabled = effectsSettings.isScratchEnabled
        return params
    }
    
    /// 漏光参数（兼容性属性）
    var lightLeakParameters: LightLeakParameters {
        var params = LightLeakParameters()
        params.intensity = effectsSettings.leakIntensity
        params.selectedPreset = selectedLightLeakPreset
        params.isEnabled = effectsSettings.isLeakEnabled
        return params
    }
    
    /// 设置初始化方法（兼容性方法）
    func setupEffects() {
        // 确保预设数据已加载
        if availableLightLeakPresets.isEmpty || availableGrainPresets.isEmpty || availableScratchPresets.isEmpty {
            Task {
                await loadPresets()
            }
        }
    }
}

// MARK: - ViewState 定义
enum ViewState<T>: Equatable where T: Equatable {
    case idle
    case loading
    case loaded(T)
    case error(AppError)
    
    var isLoading: Bool {
        if case .loading = self { return true }
        return false
    }
    
    var error: AppError? {
        if case .error(let error) = self { return error }
        return nil
    }
    
    var data: T? {
        if case .loaded(let data) = self { return data }
        return nil
    }
}

// MARK: - AppError 定义
struct AppError: LocalizedError, Equatable {
    let message: String
    
    var errorDescription: String? {
        return message
    }
    
    static func from(_ error: Error) -> AppError {
        return AppError(message: error.localizedDescription)
    }
}