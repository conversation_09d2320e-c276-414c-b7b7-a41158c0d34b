// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine

/// 裁切视图模型 - MVVM-S架构
class CropViewModel: ObservableObject {

    // MARK: - Published Properties（UI状态管理）

    /// 刻度尺拖动偏移量
    @Published var cropScaleDragOffset: CGFloat = 0

    /// 上次拖动结束时的偏移量
    @Published var lastDragOffset: CGFloat = 0

    /// 当前拖动偏移量
    @Published var dragOffset: CGFloat = 0

    /// 触摸开始时的偏移量
    @Published var startDragOffset: CGFloat = 0

    /// 是否正在触摸
    @Published var isTouching: Bool = false

    /// 旋转角度
    @Published var rotationAngle: Double = 0

    /// 选中的宽高比
    @Published var selectedRatio: String = "original"
    
    /// 错误状态
    @Published var errorMessage: String?
    
    /// 是否正在加载
    @Published var isLoading: Bool = false

    // MARK: - 非Published属性

    /// 触摸位置
    var startLocation: CGPoint = .zero

    /// 刻度尺触摸回调
    var onScaleTouchBegan: (() -> Void)?
    var onScaleTouchEnded: (() -> Void)?

    // MARK: - 常量
    private let screenHeight = UIScreen.main.bounds.height
    private let screenWidth = UIScreen.main.bounds.width

    // MARK: - 依赖注入
    private let cropService: CropServiceProtocol
    private let storageService = UserDefaultsService.shared
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化
    init(cropService: CropServiceProtocol) {
        self.cropService = cropService

        // 初始化代码 - 设置刻度尺为默认居中位置
        cropScaleDragOffset = 0.0
        lastDragOffset = 0.0
        dragOffset = 0.0

        print("🎨 [CropViewModel] 初始化完成")

        loadSavedSettings()
    }

    // MARK: - 公共方法

    /// 加载保存的状态
    func loadSavedState() {
        // 从UserDefaultsService加载上次保存的刻度尺偏移量，如果没有则默认为0（居中）
        cropScaleDragOffset = storageService.getCropScaleOffset()
    }

    /// 重置裁切刻度尺到中心位置
    func resetCropScaleToCenter() {
        cropScaleDragOffset = 0.0
        lastDragOffset = 0.0
        dragOffset = 0.0
        storageService.saveCropScaleOffset(0.0)

        // 同时重置旋转角度和比例
        rotationAngle = 0
        selectedRatio = "original"

        // 保存到服务
        cropService.updateSetting(\CropModel.cropScaleDragOffset, value: 0.0)
        cropService.updateSetting(\CropModel.lastDragOffset, value: 0.0)
        cropService.updateSetting(\CropModel.rotationAngle, value: 0.0)
        cropService.updateSetting(\CropModel.selectedRatio, value: "original")
    }

    /// 更新刻度尺拖动偏移量
    /// - Parameter offset: 新的偏移量
    func updateCropScaleOffset(_ offset: CGFloat) {
        do {
            // 限制偏移量范围在 ±屏幕宽度的46%
            let limitedOffset = min(max(offset, -screenWidth * 0.46), screenWidth * 0.46)
            cropScaleDragOffset = limitedOffset
            dragOffset = limitedOffset

            // 根据偏移量更新旋转角度 (左侧+45度到右侧-45度)
            let angleRange: Double = 90.0  // 总角度范围(+45到-45)
            let maxOffset: CGFloat = screenWidth * 0.46 * 2  // 总偏移范围
            rotationAngle = -Double(cropScaleDragOffset / maxOffset) * angleRange  // 使用负号翻转映射关系

            // 保存到服务
            cropService.updateSetting(\CropModel.cropScaleDragOffset, value: Double(limitedOffset))
            cropService.updateSetting(\CropModel.rotationAngle, value: rotationAngle)
            
            // 清除错误状态
            errorMessage = nil
        } catch {
            errorMessage = "更新旋转角度失败: \(error.localizedDescription)"
        }
    }

    /// 更新选中的比例
    /// - Parameter ratio: 新的比例
    func updateSelectedRatio(_ ratio: String) {
        do {
            selectedRatio = ratio
            cropService.updateSetting(\CropModel.selectedRatio, value: ratio)
            
            // 清除错误状态
            errorMessage = nil
        } catch {
            errorMessage = "更新裁切比例失败: \(error.localizedDescription)"
        }
    }

    // MARK: - 私有方法

    /// 加载保存的设置
    private func loadSavedSettings() {
        do {
            isLoading = true
            errorMessage = nil
            
            let settings = cropService.getSettings()

            cropScaleDragOffset = CGFloat(settings.cropScaleDragOffset)
            lastDragOffset = CGFloat(settings.lastDragOffset)
            rotationAngle = settings.rotationAngle
            selectedRatio = settings.selectedRatio

            // 同步dragOffset
            dragOffset = cropScaleDragOffset
            
            isLoading = false
        } catch {
            isLoading = false
            errorMessage = "加载裁切设置失败: \(error.localizedDescription)"
        }
    }
}
