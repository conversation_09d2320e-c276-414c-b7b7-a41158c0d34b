import Foundation
import SwiftUI
import Combine
import CoreGraphics

/// 调节模块的视图模型 - MVVM-S架构
/// 复制自：CurveManager.swift + HSLManager.swift + SplitToningManager.swift + AdjustControlView.swift (@Published属性)
class AdjustViewModel: ObservableObject {

    // MARK: - 依赖注入
    private let adjustService = AdjustService.shared

    // MARK: - 曲线状态属性 (从CurveManager.swift完整复制)

    /// 当前曲线控制点 - 四通道类型安全管理
    @Published var curvePoints: [CurveChannel: [CGPoint]] = [
        .rgb: CurveProcessor.createDefaultPoints(),
        .red: CurveProcessor.createDefaultPoints(),
        .green: CurveProcessor.createDefaultPoints(),
        .blue: CurveProcessor.createDefaultPoints()
    ]

    /// 曲线强度 (0.0-1.0)
    @Published var curveIntensity: Float = 1.0

    /// 是否启用曲线调整
    @Published var isEnabled: Bool = false

    /// 当前渲染质量
    @Published var renderQuality: CurveProcessor.CurveQuality = .standard

    /// RGB曲线是否启用（计算属性）
    var rgbCurveEnabled: Bool {
        !isLinearCurve(curvePoints[.rgb] ?? [])
    }

    /// 分离通道曲线是否启用（计算属性）
    var channelCurvesEnabled: Bool {
        [CurveChannel.red, .green, .blue].contains { channel in
            !isLinearCurve(curvePoints[channel] ?? [])
        }
    }

    /// 当前选中的预设
    @Published var currentPreset: CurveProcessor.CurvePreset? = nil

    /// 当前选中的通道
    @Published var selectedChannel: CurveChannel = .rgb {
        didSet {
            if selectedChannel != oldValue {
                // 通道切换时重置拖拽状态
                resetDragState()
                print("🎨 通道已切换到: \(selectedChannel.displayName)")
            }
        }
    }

    /// 当前拖拽的点索引
    @Published var draggedPointIndex: Int? = nil

    // MARK: - HSL状态属性 (从HSLManager.swift完整复制)

    /// 当前选中的颜色选项 (0-7: 红、橙、黄、绿、青、蓝、紫、洋红)
    @Published var selectedColorIndex: Int = 0

    // MARK: - 色调分离状态属性 (从SplitToningManager.swift完整复制)

    /// 当前选中的色调选项
    @Published var selectedToneOption: String = "阴影"

    /// 色环控制点的UI偏移量
    @Published var toneHueOffset: CGSize = .zero

    /// 是否正在按压控制点
    @Published var isPressing: Bool = false

    /// 色调分离性能监控器
    private lazy var toneSplitPerformanceMonitor = PerformanceService.toneSplitMonitor()

    // MARK: - 调节控制状态属性 (从AdjustControlView.swift完整复制)

    /// 当前选中的调节参数类型
    @Published var selectedParameter: String = "exposure"

    // MARK: - 私有属性
    private var cancellables = Set<AnyCancellable>()

    // MARK: - 初始化
    init() {
        setupBindings()
        print("🎨 AdjustViewModel 初始化完成")
    }

    // MARK: - 绑定设置
    private func setupBindings() {
        // 绑定AdjustService的曲线状态到ViewModel
        adjustService.$curvePoints
            .receive(on: DispatchQueue.main)
            .assign(to: \.curvePoints, on: self)
            .store(in: &cancellables)

        adjustService.$curveIntensity
            .receive(on: DispatchQueue.main)
            .assign(to: \.curveIntensity, on: self)
            .store(in: &cancellables)

        adjustService.$isEnabled
            .receive(on: DispatchQueue.main)
            .assign(to: \.isEnabled, on: self)
            .store(in: &cancellables)

        adjustService.$renderQuality
            .receive(on: DispatchQueue.main)
            .assign(to: \.renderQuality, on: self)
            .store(in: &cancellables)

        adjustService.$currentPreset
            .receive(on: DispatchQueue.main)
            .assign(to: \.currentPreset, on: self)
            .store(in: &cancellables)

        adjustService.$selectedChannel
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedChannel, on: self)
            .store(in: &cancellables)

        adjustService.$draggedPointIndex
            .receive(on: DispatchQueue.main)
            .assign(to: \.draggedPointIndex, on: self)
            .store(in: &cancellables)

        adjustService.$selectedHSLColorIndex
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedColorIndex, on: self)
            .store(in: &cancellables)

        adjustService.$selectedToneOption
            .receive(on: DispatchQueue.main)
            .assign(to: \.selectedToneOption, on: self)
            .store(in: &cancellables)

        adjustService.$toneHueOffset
            .receive(on: DispatchQueue.main)
            .assign(to: \.toneHueOffset, on: self)
            .store(in: &cancellables)

        adjustService.$isPressing
            .receive(on: DispatchQueue.main)
            .assign(to: \.isPressing, on: self)
            .store(in: &cancellables)

        print("✅ AdjustViewModel 绑定设置完成")
    }

    // MARK: - 曲线管理公共方法 (委托给AdjustService)

    /// 切换到指定通道
    func switchToChannel(_ channel: CurveChannel) {
        adjustService.switchToChannel(channel)
    }

    /// 重置拖拽相关状态
    func resetDragState() {
        adjustService.resetDragState()
    }

    /// 获取当前通道的曲线点
    func getCurrentCurvePoints() -> [CGPoint] {
        return adjustService.getCurrentCurvePoints()
    }

    /// 更新当前通道的曲线点
    func updateCurrentChannelCurvePoints(_ points: [CGPoint]) {
        adjustService.updateCurrentChannelCurvePoints(points)
    }

    /// 更新指定点的位置（使用归一化坐标）
    func updatePointPosition(index: Int, normalizedPoint: CGPoint) {
        adjustService.updatePointPosition(index: index, normalizedPoint: normalizedPoint)
    }

    /// 结束点位置更新（拖拽结束时调用）
    func endPointPositionUpdate() {
        adjustService.endPointPositionUpdate()
    }

    /// 添加曲线点（使用归一化坐标）
    @discardableResult
    func addPoint(at normalizedPoint: CGPoint) -> Int {
        return adjustService.addPoint(at: normalizedPoint)
    }

    /// 移除曲线点
    func removePoint(at index: Int) {
        adjustService.removePoint(at: index)
    }

    /// 重置所有曲线状态到默认值
    func resetAllCurves() {
        adjustService.resetAllCurves()
    }

    /// 重置当前通道的曲线
    func resetCurrentChannel() {
        adjustService.resetCurrentChannel()
    }

    /// 更新曲线控制点
    func updateCurvePoints(_ points: [CGPoint], for channel: CurveChannel) {
        adjustService.updateCurvePoints(points, for: channel)
    }

    /// 应用曲线预设
    func applyPreset(_ preset: CurveProcessor.CurvePreset, to channel: CurveChannel, intensity: Float = 1.0) {
        adjustService.applyPreset(preset, to: channel, intensity: intensity)
    }

    /// 重置指定通道
    func resetChannel(_ channel: CurveChannel) {
        adjustService.resetChannel(channel)
    }

    /// 获取当前性能统计
    func getPerformanceStats() -> (averageTime: TimeInterval, updateCount: Int, lastDuration: TimeInterval) {
        return adjustService.getPerformanceStats()
    }

    /// 获取当前LUT数据
    func getCurrentLUTs() -> [CurveChannel: [Float]] {
        return adjustService.getCurrentLUTs()
    }

    /// 检查是否有活跃的曲线
    func hasActiveCurves() -> Bool {
        return adjustService.hasActiveCurves()
    }

    // MARK: - 校准相关属性和方法 (从CalibrationManager.swift完整复制)

    /// 当前选中的校准颜色索引 (0=红, 1=绿, 2=蓝)
    @Published var selectedCalibrationColorIndex: Int = 0

    /// 选择校准颜色
    func selectCalibrationColor(_ index: Int) {
        selectedCalibrationColorIndex = index
        print("🎨 [校准] 选择颜色: \(["红", "绿", "蓝"][index])")
    }

    /// 获取当前选中颜色的色相值
    func getCurrentCalibrationHue() -> Float {
        switch selectedCalibrationColorIndex {
        case 0: return adjustService.getCurrentParametersCopy().redHue
        case 1: return adjustService.getCurrentParametersCopy().greenHue
        case 2: return adjustService.getCurrentParametersCopy().blueHue
        default: return 0.0
        }
    }

    /// 获取当前选中颜色的饱和度值
    func getCurrentCalibrationSaturation() -> Float {
        switch selectedCalibrationColorIndex {
        case 0: return adjustService.getCurrentParametersCopy().redSaturation
        case 1: return adjustService.getCurrentParametersCopy().greenSaturation
        case 2: return adjustService.getCurrentParametersCopy().blueSaturation
        default: return 0.0
        }
    }

    /// 更新当前选中颜色的色相
    func updateCalibrationHue(_ value: Float) {
        switch selectedCalibrationColorIndex {
        case 0: adjustService.updateParameter(\.redHue, value: value)
        case 1: adjustService.updateParameter(\.greenHue, value: value)
        case 2: adjustService.updateParameter(\.blueHue, value: value)
        default: break
        }
    }

    /// 更新当前选中颜色的饱和度
    func updateCalibrationSaturation(_ value: Float) {
        switch selectedCalibrationColorIndex {
        case 0: adjustService.updateParameter(\.redSaturation, value: value)
        case 1: adjustService.updateParameter(\.greenSaturation, value: value)
        case 2: adjustService.updateParameter(\.blueSaturation, value: value)
        default: break
        }
    }

    /// 重置所有校准颜色
    func resetAllCalibrationColors() {
        adjustService.updateParameter(\.redHue, value: 0.0)
        adjustService.updateParameter(\.greenHue, value: 0.0)
        adjustService.updateParameter(\.blueHue, value: 0.0)
        adjustService.updateParameter(\.redSaturation, value: 0.0)
        adjustService.updateParameter(\.greenSaturation, value: 0.0)
        adjustService.updateParameter(\.blueSaturation, value: 0.0)
        print("🔄 [校准] 所有颜色已重置")
    }

    /// 验证并修正所有控制点的边界
    func validateAndFixBoundaries() {
        adjustService.validateAndFixBoundaries()
    }

    /// 验证状态一致性
    func validateStateConsistency() -> Bool {
        // 检查曲线点是否与Service同步
        for channel in CurveChannel.allCases {
            let viewModelPoints = curvePoints[channel] ?? []
            let servicePoints = adjustService.curvePoints[channel] ?? []
            if viewModelPoints != servicePoints {
                return false
            }
        }
        return true
    }

    /// 强制同步状态
    func forceSyncState() {
        // 强制从Service同步所有状态
        curvePoints = adjustService.curvePoints
        selectedChannel = adjustService.selectedChannel
        draggedPointIndex = adjustService.draggedPointIndex
        curveIntensity = adjustService.curveIntensity
        isEnabled = adjustService.isEnabled
        renderQuality = adjustService.renderQuality
        currentPreset = adjustService.currentPreset
        print("🔄 强制同步状态完成")
    }

    // MARK: - HSL管理公共方法 (委托给AdjustService)

    /// 更新HSL色相
    func updateHSLHue(_ hue: Float, for colorIndex: Int? = nil) {
        adjustService.updateHSLHue(hue, for: colorIndex)
    }

    /// 更新HSL饱和度
    func updateHSLSaturation(_ saturation: Float, for colorIndex: Int? = nil) {
        adjustService.updateHSLSaturation(saturation, for: colorIndex)
    }

    /// 更新HSL明度
    func updateHSLLuminance(_ luminance: Float, for colorIndex: Int? = nil) {
        adjustService.updateHSLLuminance(luminance, for: colorIndex)
    }

    /// 切换HSL颜色范围
    func switchHSLColorRange(to index: Int) {
        adjustService.switchHSLColorRange(to: index)
    }

    /// 重置当前选中颜色的HSL参数
    func resetCurrentHSLColor() {
        adjustService.resetCurrentHSLColor()
    }

    /// 重置所有HSL颜色参数
    func resetAllHSLColors() {
        adjustService.resetAllHSLColors()
    }

    /// 检查是否有活跃的HSL调整
    func hasActiveHSLAdjustments() -> Bool {
        return adjustService.hasActiveHSLAdjustments()
    }

    /// 获取当前选中颜色的HSL参数 - 从备份复制
    func getCurrentHSLParameters() -> (hue: Float, saturation: Float, luminance: Float) {
        return adjustService.getCurrentHSLParameters()
    }

    /// 获取所有HSL参数 - 从备份复制
    func getAllHSLParameters() -> (hueValues: [Float], saturationValues: [Float], luminanceValues: [Float]) {
        return adjustService.getAllHSLParameters()
    }

    /// 打印HSL当前状态
    func printHSLCurrentState() {
        adjustService.printHSLCurrentState()
    }

    // MARK: - 色调分离管理公共方法 (委托给AdjustService)

    /// 切换色调选项
    func switchToToneOption(_ option: String) {
        adjustService.switchToToneOption(option)
    }

    /// 更新色调参数
    func updateToneHue(_ normalizedAngle: Double, saturation: Double) {
        // 性能监控：检查更新频率
        guard toneSplitPerformanceMonitor.recordUpdate() else {
            // 更新太频繁，跳过本次更新以保持性能
            return
        }

        adjustService.updateToneHue(normalizedAngle, saturation: saturation)
    }

    /// 重置当前选中的色调（双击重置）
    func resetCurrentTone() {
        adjustService.resetCurrentTone()
    }

    /// 重置所有色调分离参数（重置按钮）
    func resetAllTones() {
        adjustService.resetAllTones()
    }

    /// 更新平衡参数
    func updateBalance(_ value: Float) {
        adjustService.updateBalance(value)
    }

    /// 获取当前色调分离状态
    func getCurrentSplitToningState() -> (shadowHue: Float, shadowSaturation: Float, highlightHue: Float, highlightSaturation: Float, balance: Float) {
        return adjustService.getCurrentSplitToningState()
    }

    /// 检查是否有活跃的色调分离调整
    func hasActiveSplitToningAdjustments() -> Bool {
        return adjustService.hasActiveSplitToningAdjustments()
    }

    /// 打印色调分离当前状态
    func printSplitToningCurrentState() {
        adjustService.printSplitToningCurrentState()
    }

    // MARK: - 调节设置管理公共方法 (委托给AdjustService)

    /// 获取调节设置
    func getSettings() -> AdjustSettings {
        return adjustService.getSettings()
    }

    /// 保存设置
    func saveSettings(_ settings: AdjustSettings) {
        adjustService.saveSettings(settings)
    }

    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<AdjustSettings, T>, value: T) {
        adjustService.updateSetting(keyPath, value: value)
    }

    /// 重置所有参数
    func resetAllParameters() {
        adjustService.resetAllParameters()
    }

    /// 重置所有设置为默认值
    func resetToDefaults() {
        adjustService.resetToDefaults()
    }

    // MARK: - 参数更新方法

    /// 更新单个参数
    /// - Parameters:
    ///   - keyPath: 参数路径
    ///   - value: 新值
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T) {
        adjustService.updateParameter(keyPath, value: value)
    }

    /// 批量更新参数
    func batchUpdateParameters(_ updates: () -> Void) {
        adjustService.batchUpdateParameters(updates)
    }

    /// 获取当前参数的副本
    func getCurrentParametersCopy() -> FilterParameters {
        return adjustService.getCurrentParametersCopy()
    }

    // MARK: - 私有辅助方法

    /// 检查曲线是否为线性
    private func isLinearCurve(_ points: [CGPoint]) -> Bool {
        guard points.count == 2 else { return false }
        let first = points[0]
        let last = points[1]
        return abs(first.x - 0.0) < 0.01 && abs(first.y - 0.0) < 0.01 &&
               abs(last.x - 1.0) < 0.01 && abs(last.y - 1.0) < 0.01
    }
}
