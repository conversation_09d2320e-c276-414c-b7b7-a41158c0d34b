import Foundation
import SwiftUI
import Combine
import UIKit

// MARK: - 水印ViewModel
/// 水印视图模型，负责管理水印相关的状态
/// **重要：这是一个渐进式重构的初始版本**
/// **当前阶段：仅定义基础属性，不改变WatermarkControlView的任何逻辑**
class WatermarkViewModel: ObservableObject {

    // MARK: - 依赖注入
    private let watermarkService: WatermarkServiceProtocol
    private let watermarkManagerService: WatermarkManagerServiceProtocol?

    // MARK: - UI引用（第二阶段新增）
    /// 预览容器的弱引用，用于水印应用
    private weak var previewContainer: UIView?

    // MARK: - 基础状态属性（从WatermarkControlView迁移的核心状态）

    /// 选中的水印类型
    /// 对应：WatermarkControlView中的 @State private var selectedWatermarkType: String = "none"
    @Published var selectedWatermarkType: String = "none"

    /// 选中的水印分类
    /// 对应：WatermarkControlView中的 @State private var selectedWatermarkCategory: String = "经典"
    @Published var selectedWatermarkCategory: String = "经典"

    /// 过滤后的水印索引列表
    /// 对应：WatermarkControlView中的 @State private var filteredWatermarkIndices: [Int] = Array(0...18)
    @Published var filteredWatermarkIndices: [Int] = Array(0...25)

    // MARK: - 颜色选择状态（从WatermarkControlView迁移）

    /// 选中的边框颜色索引
    /// 对应：WatermarkControlView中的 @State private var selectedBorderColorIndex: Int = 0
    @Published var selectedBorderColorIndex: Int = 0 // 0 for white, 1 for dark gray

    /// 选中的字体颜色索引
    /// 对应：WatermarkControlView中的 @State private var selectedFontColorIndex: Int = 1
    @Published var selectedFontColorIndex: Int = 1 // 0 for white, 1 for black (默认黑色)

    /// 选中的Logo颜色索引
    /// 对应：WatermarkControlView中的 @State private var selectedLogoColorIndex: Int = 2
    @Published var selectedLogoColorIndex: Int = 2 // 0表示不使用颜色调整, 1为白色, 2为黑色 (默认黑色)

    // MARK: - 字体设置状态（从WatermarkControlView迁移）

    /// 选中的中文字体名称
    /// 对应：WatermarkControlView中的 @State private var selectedFontName: String = "PingFang-SC"
    @Published var selectedFontName: String = "PingFang-SC"

    /// 选中的英文字体名称
    /// 对应：WatermarkControlView中的 @State private var selectedEnglishFontName: String = ""
    @Published var selectedEnglishFontName: String = ""

    /// 选中的中文字体粗细
    /// 对应：WatermarkControlView中的 @State private var selectedFontWeight: String = "常规"
    @Published var selectedFontWeight: String = "常规"

    /// 选中的署名字体粗细
    /// 对应：WatermarkControlView中的 @State private var selectedSignatureFontWeight: String = "常规"
    @Published var selectedSignatureFontWeight: String = "常规"

    /// 选中的英文字体粗细
    /// 对应：WatermarkControlView中的 @State private var selectedEnglishFontWeight: String = "Regular"
    @Published var selectedEnglishFontWeight: String = "Regular"

    /// 署名字体大小滑块值
    /// 对应：WatermarkControlView中的 @State private var signatureFontSizeSliderValue: Double = 1.0
    @Published var signatureFontSizeSliderValue: Double = 1.0

    /// 文字字体大小滑块值
    /// 对应：WatermarkControlView中的 @State private var textFontSizeSliderValue: Double = 1.0
    @Published var textFontSizeSliderValue: Double = 1.0

    // MARK: - 滑块状态（从WatermarkControlView迁移）

    /// 边框粗细滑块值
    /// 对应：WatermarkControlView中的 @State private var borderThicknessSliderValue: Double = 0.2
    @Published var borderThicknessSliderValue: Double = 0.2

    /// 宽边框粗细滑块值
    /// 对应：WatermarkControlView中的 @State private var wideBorderThicknessSliderValue: Double = 0.5
    @Published var wideBorderThicknessSliderValue: Double = 0.5

    /// 上下边框粗细滑块值
    /// 对应：WatermarkControlView中的 @State private var topBottomBorderThicknessSliderValue: Double = 0.2
    @Published var topBottomBorderThicknessSliderValue: Double = 0.2

    /// 左右边框粗细滑块值
    /// 对应：WatermarkControlView中的 @State private var leftRightBorderThicknessSliderValue: Double = 0.2
    @Published var leftRightBorderThicknessSliderValue: Double = 0.2

    /// 偏好选项缩放比例滑块值
    /// 对应：WatermarkControlView中的 @State private var preferenceScaleFactorSliderValue: Double = 1.0
    @Published var preferenceScaleFactorSliderValue: Double = 1.0

    /// Logo大小滑块值
    /// 对应：WatermarkControlView中的 @State private var logoSizeSliderValue: Double = 1.0
    @Published var logoSizeSliderValue: Double = 1.0

    /// 当前水印设置
    /// 这将替代直接调用WatermarkSettingsManager.shared.getSettings()
    @Published var currentSettings: WatermarkSettings = WatermarkSettings()

    // MARK: - UI状态
    /// 加载状态
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String = ""

    /// 是否显示错误
    @Published var showError: Bool = false

    // MARK: - 初始化
    /// 初始化ViewModel
    /// - Parameters:
    ///   - watermarkService: 水印服务（依赖注入）
    ///   - watermarkManagerService: 水印管理器服务（可选，第三阶段新增）
    init(watermarkService: WatermarkServiceProtocol,
         watermarkManagerService: WatermarkManagerServiceProtocol? = nil) {
        self.watermarkService = watermarkService
        self.watermarkManagerService = watermarkManagerService
        print("🏭 [WatermarkViewModel] 初始化ViewModel（第三阶段更新）")
        print("   - WatermarkService: ✅")
        print("   - WatermarkManagerService: \(watermarkManagerService != nil ? "✅" : "❌（使用兼容模式）")")

        // 加载初始数据
        loadInitialData()
    }

    // MARK: - 第三阶段：水印管理器操作方法

    /// 设置水印管理器（使用新的依赖注入服务）
    /// - Parameter container: 预览容器
    func setupWatermarkManager(with container: UIView) {
        if let managerService = watermarkManagerService {
            managerService.setupWatermarkManager(with: container)
        } else {
            print("⚠️ [WatermarkViewModel] WatermarkManagerService未注入，无法设置管理器")
        }
    }

    /// 检查水印管理器是否可用
    /// - Returns: 是否可用
    func isWatermarkManagerAvailable() -> Bool {
        if let managerService = watermarkManagerService {
            return managerService.isManagerAvailable()
        } else {
            print("⚠️ [WatermarkViewModel] WatermarkManagerService未注入")
            return false
        }
    }

    /// 应用水印样式（使用新的依赖注入服务）
    /// - Parameter style: 水印样式
    /// - Returns: 是否成功
    func applyWatermarkStyleDirect(_ style: WatermarkStyle?) -> Bool {
        if let managerService = watermarkManagerService {
            return managerService.applyWatermarkStyle(style)
        } else {
            print("⚠️ [WatermarkViewModel] WatermarkManagerService未注入，无法应用样式")
            return false
        }
    }

    /// 移除当前水印（使用新的依赖注入服务）
    /// - Returns: 是否成功
    func removeCurrentWatermarkDirect() -> Bool {
        if let managerService = watermarkManagerService {
            return managerService.removeCurrentWatermark()
        } else {
            print("⚠️ [WatermarkViewModel] WatermarkManagerService未注入，无法移除水印")
            return false
        }
    }

    // MARK: - 数据加载
    /// 加载初始数据
    private func loadInitialData() {
        print("📖 [WatermarkViewModel] 加载初始数据")

        // 从Service获取当前设置
        currentSettings = watermarkService.getSettings()

        // 设置初始的水印类型
        selectedWatermarkType = currentSettings.activeWatermarkStyleType

        // 初始化颜色选择状态
        initializeColorStates()

        // 初始化字体设置状态
        initializeFontStates()

        // 初始化滑块状态
        initializeSliderStates()

        print("✅ [WatermarkViewModel] 初始数据加载完成，当前水印类型: \(selectedWatermarkType)")
    }

    /// 设置预览容器引用
    /// - Parameter container: 预览容器视图
    func setPreviewContainer(_ container: UIView) {
        print("🔗 [WatermarkViewModel] setPreviewContainer() - 设置预览容器")
        self.previewContainer = container
        print("🔗 [WatermarkViewModel] 预览容器已设置，尺寸: \(container.bounds)")
    }

    /// 初始化颜色选择状态
    /// 根据当前设置的颜色值推断对应的索引
    private func initializeColorStates() {
        print("🎨 [WatermarkViewModel] 初始化颜色选择状态")

        // 初始化边框颜色索引 - 使用更精确的颜色匹配
        selectedBorderColorIndex = matchBorderColorIndex()

        // 初始化字体颜色索引
        if currentSettings.fontColorRed > 0.5 {
            selectedFontColorIndex = 0 // 白色
        } else {
            selectedFontColorIndex = 1 // 黑色
        }

        // 初始化Logo颜色索引
        let logoRed = currentSettings.logoColorRed
        let logoGreen = currentSettings.logoColorGreen
        let logoBlue = currentSettings.logoColorBlue

        if abs(logoRed - 0.5) < 0.1 && abs(logoGreen - 0.5) < 0.1 && abs(logoBlue - 0.5) < 0.1 {
            selectedLogoColorIndex = 0 // 不使用颜色调整
        } else if logoRed > 0.5 {
            selectedLogoColorIndex = 1 // 白色
        } else {
            selectedLogoColorIndex = 2 // 黑色
        }

        print("🎨 [WatermarkViewModel] 颜色状态初始化完成 - 边框:\(selectedBorderColorIndex), 字体:\(selectedFontColorIndex), Logo:\(selectedLogoColorIndex)")
    }

    /// 匹配边框颜色索引
    /// 根据当前边框颜色设置找到最匹配的颜色索引
    private func matchBorderColorIndex() -> Int {
        // 定义颜色映射（与UI中的availableBorderColors对应）
        let colorMappings: [(red: Double, green: Double, blue: Double, index: Int)] = [
            (1.0, 1.0, 1.0, 0),      // 白色
            (0.098, 0.102, 0.106, 1), // 黑色 #191a1b
            (0.325, 0.325, 0.325, 2), // 灰色 #535353
            (0.0, 0.0, 0.0, 3),       // 纯黑色
            (0.969, 0.576, 0.161, 4), // 橙色 #F79329
            (0.902, 0.322, 0.310, 5), // 红色 #e6524f
            (0.984, 0.765, 0.200, 6), // 黄色 #fbc333
            (0.455, 0.757, 0.365, 7), // 绿色 #74c15d
            (0.145, 0.678, 0.886, 8), // 蓝色 #25ade2
            (0.655, 0.337, 0.631, 9)  // 紫色 #a756a1
        ]

        let currentRed = currentSettings.borderColorRed
        let currentGreen = currentSettings.borderColorGreen
        let currentBlue = currentSettings.borderColorBlue

        var bestMatch = 0
        var minDistance = Double.infinity

        for mapping in colorMappings {
            let distance = sqrt(
                pow(currentRed - mapping.red, 2) +
                pow(currentGreen - mapping.green, 2) +
                pow(currentBlue - mapping.blue, 2)
            )

            if distance < minDistance {
                minDistance = distance
                bestMatch = mapping.index
            }
        }

        return bestMatch
    }

    /// 初始化字体设置状态
    /// 根据当前设置初始化所有字体相关状态
    private func initializeFontStates() {
        print("🔤 [WatermarkViewModel] 初始化字体设置状态")

        // 初始化字体选择
        selectedFontName = currentSettings.selectedFontName
        selectedEnglishFontName = currentSettings.selectedEnglishFontName

        // 初始化字体粗细
        if !currentSettings.selectedFontWeight.isEmpty {
            selectedFontWeight = currentSettings.selectedFontWeight
        } else {
            selectedFontWeight = getDefaultFontWeight(for: selectedFontName)
        }

        if !currentSettings.selectedSignatureFontWeight.isEmpty {
            selectedSignatureFontWeight = currentSettings.selectedSignatureFontWeight
        } else {
            selectedSignatureFontWeight = getDefaultFontWeight(for: selectedFontName)
        }

        if !currentSettings.selectedEnglishFontWeight.isEmpty {
            selectedEnglishFontWeight = currentSettings.selectedEnglishFontWeight
        } else {
            selectedEnglishFontWeight = getDefaultEnglishFontWeight(for: selectedEnglishFontName)
        }

        // 初始化字体大小
        signatureFontSizeSliderValue = currentSettings.signatureFontSizeMultiplier
        textFontSizeSliderValue = currentSettings.textFontSizeMultiplier

        print("🔤 [WatermarkViewModel] 字体状态初始化完成 - 中文:\(selectedFontName), 英文:\(selectedEnglishFontName)")
    }

    /// 获取字体的默认粗细
    private func getDefaultFontWeight(for fontName: String) -> String {
        switch fontName {
        case "HarmonyOS_Sans_SC", "PingFang-SC", "SourceHanSansSC", "HONORSansCN":
            return "Regular"
        case "Makinas-Flat", "Makinas-Square":
            return "常规"
        default:
            return "Regular"
        }
    }

    /// 获取英文字体的默认粗细
    private func getDefaultEnglishFontWeight(for fontName: String) -> String {
        switch fontName {
        case "FuturaPT":
            return "Book"
        case "TYPOGRAPH PRO":
            return "Light"
        case "ADELE":
            return "Light"
        case "Syntax":
            return "Roman"
        default:
            return "Regular"
        }
    }

    /// 初始化滑块状态
    /// 根据当前设置初始化所有滑块相关状态
    private func initializeSliderStates() {
        print("🎚️ [WatermarkViewModel] 初始化滑块状态")

        // 初始化边框粗细滑块
        borderThicknessSliderValue = currentSettings.borderThicknessMultiplier

        // 初始化宽边框粗细滑块
        wideBorderThicknessSliderValue = currentSettings.wideBorderThicknessMultiplier

        // 初始化上下边框粗细滑块
        topBottomBorderThicknessSliderValue = currentSettings.topBottomBorderThicknessMultiplier

        // 初始化左右边框粗细滑块
        leftRightBorderThicknessSliderValue = currentSettings.leftRightBorderThicknessMultiplier

        // 初始化偏好选项缩放比例滑块
        preferenceScaleFactorSliderValue = currentSettings.preferenceScaleFactor

        // 初始化Logo大小滑块
        logoSizeSliderValue = currentSettings.logoSizeMultiplier

        print("🎚️ [WatermarkViewModel] 滑块状态初始化完成 - 边框:\(borderThicknessSliderValue), Logo:\(logoSizeSliderValue)")
    }

    // MARK: - 基础方法（暂时为空实现，为未来迁移准备）

    /// 刷新当前设置
    /// **注意：当前版本仅从Service重新获取设置，不改变现有逻辑**
    func refreshCurrentSettings() {
        print("🔄 [WatermarkViewModel] 刷新当前设置")
        currentSettings = watermarkService.getSettings()
    }

    /// 选择水印分类
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameter category: 分类名称
    func selectCategory(_ category: String) {
        print("📂 [WatermarkViewModel] 选择分类: \(category)")
        selectedWatermarkCategory = category
        // TODO: 未来版本将在这里实现过滤逻辑
    }

    /// 选择水印
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameter type: 水印类型
    func selectWatermark(_ type: String) {
        print("🎨 [WatermarkViewModel] 选择水印: \(type)")
        selectedWatermarkType = type
        // TODO: 未来版本将在这里实现应用逻辑
    }

    /// 选择水印（通过索引）
    /// **注意：当前版本仅更新状态，实际逻辑仍在WatermarkControlView中**
    /// - Parameters:
    ///   - index: 水印索引
    ///   - container: 容器视图
    func selectWatermark(at index: Int, container: UIView) {
        print("🎨 [WatermarkViewModel] 选择水印索引: \(index)")

        guard index < filteredWatermarkIndices.count else {
            print("❌ [WatermarkViewModel] 索引超出范围")
            return
        }

        // 获取实际的水印类型（这里需要实现索引到类型的映射）
        let actualIndex = filteredWatermarkIndices[index]
        let watermarkType = getWatermarkTypeForIndex(actualIndex)

        selectedWatermarkType = watermarkType
        // TODO: 未来版本将在这里实现应用逻辑
    }

    /// 更新过滤的水印列表
    /// **注意：当前版本使用简化逻辑，实际逻辑仍在WatermarkControlView中**
    func updateFilteredWatermarks() {
        print("🔄 [WatermarkViewModel] 更新过滤水印列表")
        // TODO: 实现实际的过滤逻辑
        // 暂时使用默认的索引列表
        filteredWatermarkIndices = Array(0...25)
    }

    /// 获取水印类型对应的索引
    /// **注意：这是一个简化版本，实际逻辑在WatermarkControlView中**
    /// - Parameter index: 水印索引
    /// - Returns: 水印类型字符串
    private func getWatermarkTypeForIndex(_ index: Int) -> String {
        // 简化的映射逻辑，实际应该从WatermarkControlView中迁移
        switch index {
        case 0: return "none"
        case 1: return "custom1"
        case 2: return "custom2"
        case 3: return "custom3"
        case 4: return "custom4"
        case 5: return "custom5"
        default: return "custom\(index)"
        }
    }

    /// 更新水印文字
    /// **注意：当前版本仅调用Service，不改变现有UI逻辑**
    /// - Parameter text: 新的文字内容
    func updateWatermarkText(_ text: String) {
        print("✏️ [WatermarkViewModel] 更新水印文字: \(text)")
        watermarkService.updateSetting(\.watermarkText, value: text)
        refreshCurrentSettings()
    }

    /// 切换水印文字启用状态
    /// **注意：当前版本仅调用Service，不改变现有UI逻辑**
    /// - Parameter enabled: 是否启用
    func toggleWatermarkText(_ enabled: Bool) {
        print("🔘 [WatermarkViewModel] 切换水印文字启用: \(enabled)")
        watermarkService.updateSetting(\.isWatermarkTextEnabled, value: enabled)
        refreshCurrentSettings()
    }
}

// MARK: - 扩展方法（为未来迁移准备）
extension WatermarkViewModel {

    /// 获取可见的选项配置
    /// **注意：当前版本返回空数组，保持现有逻辑不变**
    /// - Parameter watermarkType: 水印类型
    /// - Returns: 选项配置数组
    func getVisibleOptions(for watermarkType: String) -> [WatermarkOptionConfig] {
        // 暂时返回空数组，保持现有逻辑不变
        return []
    }

    /// 更新位置选项
    /// - Parameter value: 位置选项值
    func updatePositionOption(_ value: String) {
        watermarkService.updateSetting(\.positionOption, value: value)
        refreshCurrentSettings()
    }

    /// 更新设置的通用方法
    /// - Parameters:
    ///   - keyPath: 设置的键路径
    ///   - value: 新值
    func updateSetting<T>(_ keyPath: WritableKeyPath<WatermarkSettings, T>, value: T) {
        watermarkService.updateSetting(keyPath, value: value)
        refreshCurrentSettings()
    }

    /// 如果容器可用则应用水印
    func applyWatermarkIfContainerAvailable() {
        if let container = previewContainer {
            applyWatermark(to: container)
        }
    }

    /// 应用水印到容器
    /// ✅ 第三阶段重构完成：使用依赖注入的WatermarkManagerService
    /// - Parameter container: 容器视图
    func applyWatermark(to container: UIView) {
        print("🎨 [WatermarkViewModel] applyWatermark() - 使用依赖注入服务")
        setupWatermarkManager(with: container)
    }

    // MARK: - 业务逻辑方法（渐进式迁移）

    /// 应用水印样式
    /// **第二阶段更新：使用WatermarkService的新方法**
    /// - Parameter watermarkType: 水印类型
    func applyWatermarkStyle(for watermarkType: String) {
        print("🎨 [WatermarkViewModel] applyWatermarkStyle(for: \(watermarkType)) - 使用Service方法")

        // 验证预览容器
        guard let container = previewContainer else {
            print("❌ [WatermarkViewModel] 预览容器未设置，无法应用水印")
            return
        }

        // 使用Service的新方法应用水印
        let result = watermarkService.applyWatermark(watermarkType, to: container)

        if result.success {
            // 更新ViewModel状态
            selectedWatermarkType = watermarkType
            print("✅ [WatermarkViewModel] 水印应用成功: \(result.message)")
        } else {
            print("❌ [WatermarkViewModel] 水印应用失败: \(result.message)")
            if let error = result.error {
                print("❌ [WatermarkViewModel] 错误详情: \(error.localizedDescription)")
            }
        }
    }

    /// 移除当前水印
    /// **第二阶段更新：使用WatermarkService的新方法**
    func removeWatermark() {
        print("🎨 [WatermarkViewModel] removeWatermark() - 使用Service方法")

        // 验证预览容器
        guard let container = previewContainer else {
            print("❌ [WatermarkViewModel] 预览容器未设置，无法移除水印")
            // 仍然更新状态，保持一致性
            selectedWatermarkType = "none"
            return
        }

        // 使用Service的新方法移除水印
        let result = watermarkService.removeWatermark(from: container)

        if result.success {
            // 更新ViewModel状态
            selectedWatermarkType = "none"
            print("✅ [WatermarkViewModel] 水印移除成功: \(result.message)")
        } else {
            print("❌ [WatermarkViewModel] 水印移除失败: \(result.message)")
            if let error = result.error {
                print("❌ [WatermarkViewModel] 错误详情: \(error.localizedDescription)")
            }
            // 即使失败也更新状态，避免UI不一致
            selectedWatermarkType = "none"
        }
    }

    // MARK: - 新增便利方法（第二阶段）

    /// 获取可用的水印类型信息
    /// - Returns: 水印类型信息数组
    func getAvailableWatermarkTypes() -> [WatermarkTypeInfo] {
        print("📋 [WatermarkViewModel] getAvailableWatermarkTypes() - 获取可用水印类型")
        return watermarkService.getAvailableWatermarkTypes()
    }

    /// 获取指定分类的水印类型
    /// - Parameter category: 分类名称
    /// - Returns: 该分类下的水印类型数组
    func getWatermarkTypes(for category: String) -> [String] {
        print("📂 [WatermarkViewModel] getWatermarkTypes(for: \(category))")
        return watermarkService.getWatermarkTypesByCategory(category)
    }

    /// 获取所有水印分类信息
    /// - Returns: 分类信息数组
    func getAllCategories() -> [WatermarkCategoryInfo] {
        print("📋 [WatermarkViewModel] getAllCategories() - 获取所有分类")
        return watermarkService.getAllWatermarkCategories()
    }

    /// 验证当前设置
    /// - Returns: 验证结果
    func validateCurrentSettings() -> ValidationResult {
        print("🔍 [WatermarkViewModel] validateCurrentSettings() - 验证当前设置")
        let settings = watermarkService.getSettings()
        return watermarkService.validateSettings(settings)
    }

    /// 检查当前是否有水印应用
    /// - Returns: 是否有水印
    func hasWatermarkApplied() -> Bool {
        guard let container = previewContainer else {
            print("⚠️ [WatermarkViewModel] 预览容器未设置，无法检查水印状态")
            return false
        }
        return watermarkService.isWatermarkApplied(to: container)
    }

    /// 批量更新设置
    /// - Parameter closure: 更新闭包
    func batchUpdateSettings(using closure: (inout WatermarkSettings) -> Void) {
        print("🔄 [WatermarkViewModel] batchUpdateSettings() - 批量更新设置")
        watermarkService.batchUpdateSettings(using: closure)

        // 重新加载状态以保持同步
        loadInitialData()
    }

    /// 更新水印类型
    /// **新增方法：统一管理水印类型变更**
    /// - Parameter newType: 新的水印类型
    func updateWatermarkType(_ newType: String) {
        print("🎨 [WatermarkViewModel] updateWatermarkType(\(newType))")

        // 更新状态
        selectedWatermarkType = newType

        // 持久化到设置
        watermarkService.updateSetting(\.activeWatermarkStyleType, value: newType)

        // 应用新的水印样式
        if newType == "none" {
            removeWatermark()
        } else {
            applyWatermarkStyle(for: newType)
        }
    }

    // MARK: - 步骤1.1：PreviewScrollView水印选择方法

    /// 选择水印（通过索引）- 完整复制PreviewScrollView.onItemTap逻辑
    /// **步骤1.2：为PreviewScrollView的onItemTap提供ViewModel方法**
    /// - Parameters:
    ///   - index: 过滤后的索引
    ///   - filteredIndices: 过滤后的水印索引数组
    /// - Returns: 需要更新的宽边框粗细滑块值（如果需要更新的话）
    func selectWatermarkAtIndex(_ index: Int,
                               filteredIndices: [Int]) -> Double? {
        print("🎨 [WatermarkViewModel] selectWatermarkAtIndex: \(index)")

        // 将过滤后的索引转换为实际的水印索引
        let actualIndex = filteredIndices[index]

        // 检查Manager是否可用
        guard isWatermarkManagerAvailable() else {
            print("❌ WatermarkManager 不可用")
            return nil
        }

        var styleTypeToApply: String? = nil

        // 完整复制索引到类型的映射逻辑
        switch actualIndex {
        case 0: // 不启用水印
            print("选择：不启用水印")
            styleTypeToApply = "none"
        case 1: // 四周白色边框
            print("选择：预览项1 - 白色边框水印")
            styleTypeToApply = "border_2percent"
        case 2: // 拍立得风格
            print("选择：预览项2 - 拍立得风格水印")
            styleTypeToApply = "polaroid"
        case 3: // 胶片风格
            print("选择：预览项3 - 胶片风格水印")
            styleTypeToApply = "film"
        case 4: // 自定义水印4
            print("选择：预览项4 - 自定义水印4")
            styleTypeToApply = "custom4"
        case 5: // 自定义水印5
            print("选择：预览项5 - 左侧宽边框水印")
            styleTypeToApply = "custom5"
        case 6: // 自定义水印6
            print("选择：预览项6 - 上下宽边框水印")
            styleTypeToApply = "custom6"
        case 7: // 自定义水印7
            print("选择：预览项7 - 自定义水印7")
            styleTypeToApply = "custom7"
        case 8: // 自定义水印8
            print("选择：预览项8 - 自定义水印8")
            styleTypeToApply = "custom8"
        case 9: // 自定义水印9
            print("选择：预览项9 - 自定义水印9")
            styleTypeToApply = "custom9"
        case 10: // 自定义水印10
            print("选择：预览项10 - 右侧宽边框水印")
            styleTypeToApply = "custom10"
        case 11: // 自定义水印11
            print("选择：预览项11 - 自定义水印11")
            styleTypeToApply = "custom11"
        case 12: // 自定义水印12
            print("选择：预览项12 - 自定义水印12")
            styleTypeToApply = "custom12"
        case 13: // 自定义水印13
            print("选择：预览项13 - 自定义水印13")
            styleTypeToApply = "custom13"
        case 14: // 自定义水印14
            print("选择：预览项14 - 自定义水印14")
            styleTypeToApply = "custom14"
        case 15: // 自定义水印15
            print("选择：预览项15 - 自定义水印15")
            styleTypeToApply = "custom15"
        case 16: // 自定义水印16
            print("选择：预览项16 - 自定义水印16")
            styleTypeToApply = "custom16"
        case 17: // 自定义水印17
            print("选择：预览项17 - 自定义水印17")
            styleTypeToApply = "custom17"
        case 18: // 自定义水印18
            print("选择：预览项18 - 自定义水印18")
            styleTypeToApply = "custom18"
        case 19: // 自定义水印19
            print("选择：预览项19 - 自定义水印19")
            styleTypeToApply = "custom19"
        case 20: // 自定义水印20
            print("选择：预览项20 - 自定义水印20")
            styleTypeToApply = "custom20"
        case 21: // 自定义水印21
            print("选择：预览项21 - 自定义水印21")
            styleTypeToApply = "custom21"
        case 22: // 自定义水印22
            print("选择：预览项22 - 自定义水印22")
            styleTypeToApply = "custom22"
        case 23: // 自定义水印23
            print("选择：预览项23 - 拼图水印")
            styleTypeToApply = "custom23"
        case 24: // 自定义水印24
            print("选择：预览项24 - 拼图水印")
            styleTypeToApply = "custom24"
        case 25: // 自定义水印25
            print("选择：预览项25 - 自定义水印25")
            styleTypeToApply = "custom25"
        default:
            print("选择：预览项 \(actualIndex) - 行为未定义，暂不应用样式。")
            break
        }

        // 更新状态变量以反映当前选择
        var updatedWideBorderThickness: Double? = nil

        if let newType = styleTypeToApply {
            selectedWatermarkType = newType

            // 完整复制业务逻辑处理
            updatedWideBorderThickness = handleWatermarkTypeSelection(newType: newType)
        } else if actualIndex == 0 {
            selectedWatermarkType = "none"
        }

        // 持久化选择的水印类型
        watermarkService.updateSetting(\.activeWatermarkStyleType, value: selectedWatermarkType)

        // 如果切换到需要互斥逻辑的水印类型，则主动应用一次互斥规则
        if shouldApplyMutualExclusionForType(selectedWatermarkType) {
            applyMutualExclusionForSettings()
        }

        // 获取水印设置
        let settings = watermarkService.getSettings()

        // 使用工厂创建并应用样式
        if let style = WatermarkStyleFactory.createWatermarkStyle(type: selectedWatermarkType, settings: settings) {
            print("✅ 应用水印样式: \(selectedWatermarkType)")
            applyWatermarkStyleDirect(style)
        } else {
            print("ℹ️ 移除当前水印")
            removeCurrentWatermarkDirect()
        }

        return updatedWideBorderThickness
    }

    // MARK: - 辅助方法（从WatermarkControlView复制）

    /// 处理水印类型选择的业务逻辑
    /// **完整复制WatermarkControlView中的业务逻辑**
    /// - Parameter newType: 新的水印类型
    /// - Returns: 需要更新的宽边框粗细滑块值（如果需要更新的话）
    private func handleWatermarkTypeSelection(newType: String) -> Double? {
        // 检查水印13/14类型时是否需要关闭描述选项
        if newType == "custom13" || newType == "custom14" {
            let settings = watermarkService.getSettings()
            let hasLogo = !settings.selectedLogo.isEmpty
            let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
            let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
            let hasPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF"

            // 统计已启用的常规元素数量
            var enabledCount = 0
            if hasLogo { enabledCount += 1 }
            if hasSignature { enabledCount += 1 }
            if hasText || hasPreference { enabledCount += 1 } // 文字和偏好算一个元素

            // 如果常规元素少于3个，自动关闭描述选项
            if enabledCount < 3 && settings.isWatermarkDescriptionEnabled {
                watermarkService.updateSetting(\.isWatermarkDescriptionEnabled, value: false)
            }
        }

        // 根据水印类型动态设置宽边框粗细的默认值
        var updatedWideBorderThickness: Double? = nil
        if ["polaroid", "custom4", "custom5", "custom9", "custom10", "custom11", "custom12", "custom13", "custom14", "custom15", "custom16", "custom19", "custom25"].contains(newType) {
            let defaultSliderValue = WideBorderThicknessUtils.getDefaultSliderValue(for: newType)
            updatedWideBorderThickness = defaultSliderValue
            // 同时更新到设置中
            watermarkService.updateSetting(\.wideBorderThicknessMultiplier, value: defaultSliderValue)
        }

        // 处理自定义水印10、水印16和水印25的偏好选项限制
        if newType == "custom10" || newType == "custom16" || newType == "custom25" {
            // 获取当前设置
            let settings = watermarkService.getSettings()
            // 检查偏好选项
            if !settings.selectedPreferences.isEmpty {
                // 检查所有已选择的偏好是否有不支持的选项
                let unsupportedOptions = settings.selectedPreferences.filter { prefOption in
                    return prefOption != "OFF" && prefOption != "参数"
                }

                // 如果有不支持的选项
                if !unsupportedOptions.isEmpty {
                    // 检查是否包含参数选项
                    if settings.selectedPreferences.contains("参数") {
                        // 只保留参数选项
                        watermarkService.updateSetting(\.selectedPreferences, value: ["参数"])
                        watermarkService.updateSetting(\.preferenceOption, value: "参数")
                    } else {
                        // 重置为OFF
                        watermarkService.updateSetting(\.selectedPreferences, value: [])
                        watermarkService.updateSetting(\.preferenceOption, value: "OFF")
                    }
                    // 发送通知刷新偏好UI
                    NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
                }
            }

            // 自定义水印10不再执行互斥逻辑，可以同时显示文字和参数
        }

        return updatedWideBorderThickness
    }

    /// 检查特定水印类型是否需要应用互斥逻辑
    /// **完整复制WatermarkControlView.shouldApplyMutualExclusionForType**
    private func shouldApplyMutualExclusionForType(_ type: String) -> Bool {
        // 显式排除custom10和custom16，允许它们同时显示文字和偏好
        if type == "custom10" || type == "custom16" {
            return false
        }
        return type == "custom4" ||
               type == "film" ||
               type == "custom7" ||
               type == "custom8" ||
               type == "custom9" ||
               type == "custom5" ||
               type == "custom11" ||
               type == "custom12" ||
               type == "custom13" ||
               type == "custom14" ||
               type == "border_2percent" ||
               type == "custom18" ||
               type == "custom19" || // 添加自定义水印19
               type == "custom20" || // 添加自定义水印20
               type == "custom21" || // 添加自定义水印21
               type == "custom22"    // 添加自定义水印22
    }

    /// 主动应用水印互斥逻辑到当前设置
    /// **完整复制WatermarkControlView.applyMutualExclusionForSettings**
    private func applyMutualExclusionForSettings() {
        var settings = watermarkService.getSettings() // 获取可变副本

        let hasLogo = !settings.selectedLogo.isEmpty
        let hasText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        let hasPreference = settings.preferenceOption != "OFF" || !settings.selectedPreferences.isEmpty
        let hasSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty

        // 对于所有互斥逻辑的水印，确保文字和偏好只能有一个开启
            if hasText && hasPreference {
                // 默认关闭偏好，保留文字（可以根据产品需求调整哪个优先）
            print("WatermarkViewModel: 应用切换互斥 - 文字和偏好都开启，关闭偏好")
                watermarkService.updateSetting(\.preferenceOption, value: "OFF")
                watermarkService.updateSetting(\.selectedPreferences, value: []) // 同时清空新格式的偏好数组
                NotificationCenter.default.post(name: Notification.Name("RefreshWatermarkPreferenceUI"), object: nil)
        }

        // 移除这部分特殊逻辑，因为我们现在通过UI配置控制描述选项的可见性

        // 重新获取最新的settings，因为可能已被修改
        settings = watermarkService.getSettings()
        // 确保在应用新类型之前，设置是符合互斥规则的
        // （这一步主要是为了确保 WatermarkManager接收到的是正确的settings，尽管UI可能已通过通知更新）
    }
}