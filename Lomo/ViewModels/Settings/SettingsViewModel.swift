import Foundation
import SwiftUI
import Combine

/// 设置功能的视图模型，处理设置相关的所有状态和操作
class SettingsViewModel: ObservableObject {
    // MARK: - 发布属性
    
    // 相机设置
    @Published var isSaveOriginalEnabled: Bool = true
    @Published var copyrightSignature: String = ""
    @Published var isLocationRecordingEnabled: Bool = false
    @Published var isCustomRatioEnabled: Bool = false
    @Published var photoRatio: String = "4:3"
    @Published var videoRatio: String = "16:9"
    @Published var focusPeakingColor: String = "黄色"
    @Published var isVolumeButtonShutterEnabled: Bool = true
    @Published var shutterSound: String = "快门声1"
    @Published var isFocusSoundEnabled: Bool = true
    @Published var isResolutionSettingsEnabled: Bool = false
    @Published var proRAWResolution: String = "48MP"
    @Published var heicResolution: String = "12MP"
    @Published var videoBitrate: String = "自动"
    @Published var audioSource: String = "自动"
    
    // 偏好设置
    @Published var language: String = "跟随系统"
    @Published var deviceOrientation: String = "自动旋转"
    
    // 应用信息
    @Published var appVersion: String = "1.0.0"
    
    // 用户状态
    @Published var isProUser: Bool = false  // 默认为非Pro用户
    
    // UI状态
    @Published var showProView: Bool = false  // 控制LOMO Pro页面的显示
    
    // MARK: - 依赖注入
    private let settingsService: SettingsService
    
    // MARK: - 初始化方法
    
    /// 依赖注入初始化方法
    /// - Parameter settingsService: 设置服务
    init(settingsService: SettingsService) {
        self.settingsService = settingsService
        
        // 初始化代码 - 从SwiftData加载设置
        loadSavedSettings()
        
        print("🎨 [SettingsViewModel] 初始化完成")
        
        // 订阅Pro用户状态变化
        NotificationCenter.default.addObserver(
            forName: Notification.Name("ProUserStatusChanged"),
            object: nil,
            queue: .main
        ) { [weak self] notification in
            if let isProUser = notification.userInfo?["isProUser"] as? Bool {
                self?.isProUser = isProUser
            }
        }
    }
    
    /// 便捷初始化方法 - 使用依赖注入容器
    convenience init() {
        self.init(settingsService: SettingsDependencyContainer.shared.settingsService)
    }
    
    // MARK: - 设置加载和保存
    
    /// 加载保存的设置
    private func loadSavedSettings() {
        // 从SwiftData加载设置
        let settings = settingsService.getSettings()
        
        // 更新所有发布属性
        isSaveOriginalEnabled = settings.isSaveOriginalEnabled
        copyrightSignature = settings.copyrightSignature
        isLocationRecordingEnabled = settings.isLocationRecordingEnabled
        isCustomRatioEnabled = settings.isCustomRatioEnabled
        photoRatio = settings.photoRatio
        videoRatio = settings.videoRatio
        focusPeakingColor = settings.focusPeakingColor
        isVolumeButtonShutterEnabled = settings.isVolumeButtonShutterEnabled
        shutterSound = settings.shutterSound
        isFocusSoundEnabled = settings.isFocusSoundEnabled
        isResolutionSettingsEnabled = settings.isResolutionSettingsEnabled
        proRAWResolution = settings.proRAWResolution
        heicResolution = settings.heicResolution
        videoBitrate = settings.videoBitrate
        audioSource = settings.audioSource
        language = settings.language
        deviceOrientation = settings.deviceOrientation
    }
    
    /// 保存当前设置
    private func saveCurrentSettings() {
        // 创建设置对象
        let settings = AppSettings(
            isSaveOriginalEnabled: isSaveOriginalEnabled,
            copyrightSignature: copyrightSignature,
            isLocationRecordingEnabled: isLocationRecordingEnabled,
            isCustomRatioEnabled: isCustomRatioEnabled,
            photoRatio: photoRatio,
            videoRatio: videoRatio,
            focusPeakingColor: focusPeakingColor,
            isVolumeButtonShutterEnabled: isVolumeButtonShutterEnabled,
            shutterSound: shutterSound,
            isFocusSoundEnabled: isFocusSoundEnabled,
            isResolutionSettingsEnabled: isResolutionSettingsEnabled,
            proRAWResolution: proRAWResolution,
            heicResolution: heicResolution,
            videoBitrate: videoBitrate,
            audioSource: audioSource,
            language: language,
            deviceOrientation: deviceOrientation
        )
        
        // 保存设置
        settingsService.saveSettings(settings)
    }
    
    // MARK: - 公共方法
    
    /// 切换设置开关状态
    /// - Parameter setting: 设置名称
    func toggleSetting(_ setting: SettingKey) {
        switch setting {
        case .saveOriginal:
            isSaveOriginalEnabled.toggle()
            settingsService.updateSetting(\.isSaveOriginalEnabled, value: isSaveOriginalEnabled)
        case .locationRecording:
            isLocationRecordingEnabled.toggle()
            settingsService.updateSetting(\.isLocationRecordingEnabled, value: isLocationRecordingEnabled)
        case .customRatio:
            isCustomRatioEnabled.toggle()
            settingsService.updateSetting(\.isCustomRatioEnabled, value: isCustomRatioEnabled)
        case .volumeButtonShutter:
            isVolumeButtonShutterEnabled.toggle()
            settingsService.updateSetting(\.isVolumeButtonShutterEnabled, value: isVolumeButtonShutterEnabled)
        case .shutterSound:
            print("快门声不再是布尔类型，应该使用updateSetting方法")
        case .focusSound:
            isFocusSoundEnabled.toggle()
            settingsService.updateSetting(\.isFocusSoundEnabled, value: isFocusSoundEnabled)
        case .resolutionSettings:
            isResolutionSettingsEnabled.toggle()
            settingsService.updateSetting(\.isResolutionSettingsEnabled, value: isResolutionSettingsEnabled)
        // 添加其他不是布尔类型的case，它们不能使用toggle()
        case .copyright, .photoRatio, .videoRatio, .focusPeakingColor,
             .proRAWResolution, .heicResolution, .videoBitrate, .audioSource,
             .language, .deviceOrientation:
            // 这些设置项不是布尔值，无法执行toggle操作
            print("无法切换非布尔类型设置: \(setting)")
        }
    }
    
    /// 更新设置值
    /// - Parameters:
    ///   - setting: 设置名称
    ///   - value: 新的值
    func updateSetting(_ setting: SettingKey, value: String) {
        switch setting {
        case .copyright:
            copyrightSignature = value
            settingsService.updateSetting(\.copyrightSignature, value: value)
        case .photoRatio:
            photoRatio = value
            settingsService.updateSetting(\.photoRatio, value: value)
        case .videoRatio:
            videoRatio = value
            settingsService.updateSetting(\.videoRatio, value: value)
        case .focusPeakingColor:
            focusPeakingColor = value
            settingsService.updateSetting(\.focusPeakingColor, value: value)
        case .shutterSound:
            shutterSound = value
            settingsService.updateSetting(\.shutterSound, value: value)
        case .proRAWResolution:
            proRAWResolution = value
            settingsService.updateSetting(\.proRAWResolution, value: value)
        case .heicResolution:
            heicResolution = value
            settingsService.updateSetting(\.heicResolution, value: value)
        case .videoBitrate:
            videoBitrate = value
            settingsService.updateSetting(\.videoBitrate, value: value)
        case .audioSource:
            audioSource = value
            settingsService.updateSetting(\.audioSource, value: value)
        case .language:
            language = value
            settingsService.updateSetting(\.language, value: value)
        case .deviceOrientation:
            deviceOrientation = value
            settingsService.updateSetting(\.deviceOrientation, value: value)
        // 添加布尔类型的设置项
        case .saveOriginal, .locationRecording, .customRatio, 
             .volumeButtonShutter, .shutterSound, .focusSound, .resolutionSettings:
            // 这些设置项是布尔值，不应该通过此方法更新
            print("布尔类型设置应使用toggleSetting方法: \(setting)")
        }
    }
    
    /// 重置所有设置为默认值
    func resetAllSettings() {
        // 重置所有设置
        settingsService.resetToDefaults()
        // 重新加载默认设置
        loadSavedSettings()
    }
    
    /// 发送反馈邮件
    func sendFeedback(email: String = "", content: String) {
        // 保持原有功能不变
        print("收到反馈，邮箱：\(email)，内容：\(content)")
        
        if !email.isEmpty {
            let emailBody = "反馈内容：\n\(content)"
            let encodedSubject = "Lomo相机反馈".addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
            let encodedBody = emailBody.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
            
            if let url = URL(string: "mailto:<EMAIL>?subject=\(encodedSubject)&body=\(encodedBody)") {
                UIApplication.shared.open(url)
            }
        }
    }
    
    /// 显示服务条款
    func showTermsOfService() {
        // 保持原有功能不变
    }
    
    /// 显示隐私政策
    func showPrivacyPolicy() {
        // 保持原有功能不变
    }
    
    /// 给应用评分
    func rateApp() {
        // 保持原有功能不变
    }
}

// MARK: - 设置键枚举

/// 设置键枚举，用于标识不同设置项
enum SettingKey {
    // 相机设置
    case saveOriginal
    case copyright
    case locationRecording
    case customRatio
    case photoRatio
    case videoRatio
    case focusPeakingColor
    case volumeButtonShutter
    case shutterSound
    case focusSound
    case resolutionSettings
    case proRAWResolution
    case heicResolution
    case videoBitrate
    case audioSource
    
    // 偏好设置
    case language
    case deviceOrientation
} 