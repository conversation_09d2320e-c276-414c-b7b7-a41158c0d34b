import Foundation
import AVFoundation

// 相机状态
struct CameraState {
    // 镜头相关
    var availableLenses: [String] = ["0.5", "1", "2"]  // 固定镜头列表
    
    // 闪光灯状态
    enum FlashMode {
        case off
        case on
        case auto
    }
    var flashMode: FlashMode = .off  // 闪光灯模式
    
    var deviceType: CameraType = .single   // 设备类型
    var currentZoomFactor: CGFloat = 1.0   // 当前变焦倍数
    var maxZoomFactor: CGFloat = 1.0       // 最大变焦倍数
    var minZoomFactor: CGFloat = 1.0       // 最小变焦倍数
    var isZoomSupported: Bool = false      // 是否支持变焦
    var selectedLensIndex: Int = 1         // 当前选中的镜头索引
    var isTouching: Bool = false           // 是否正在触摸刻度盘
    var isAutoFocusEnabled: Bool = true    // 是否启用自动对焦
    
    // Live Photo 相关
    var isLivePhotoEnabled: Bool = false   // 是否启用 Live Photo
    
    // 曝光相关
    var exposureValue: Double = 0.0        // 当前曝光值
    var previousExposureValue: Double = 0.0  // 上一次的曝光值
    var exposureDialRotationAngle: Double = 0.0  // 曝光刻度盘旋转角度
    
    // 色温相关
    var temperatureValue: Double = 5600.0   // 当前色温值（默认5600K）
    var previousTemperatureValue: Double = 5600.0  // 上一次的色温值
    var temperatureDialRotationAngle: Double = 0.0  // 温度刻度盘旋转角度
    
    // 色调相关
    var tintValue: Double = 0.0   // 当前色调值（默认0）
    var previousTintValue: Double = 0.0  // 上一次的色调值
    var tintDialRotationAngle: Double = 0.0  // 色调刻度盘旋转角度
    
    // ISO相关
    var isoValue: Double = 50.0   // 当前ISO值（默认为最小值50）
    var previousISOValue: Double = 50.0  // 上一次的ISO值
    var isoDialRotationAngle: Double = 0.0  // ISO刻度盘旋转角度
    
    // 新增：设备支持的ISO范围
    var deviceMinISO: Double = 50.0  // 设备支持的最小ISO值
    var deviceMaxISO: Double = 12800.0  // 设备支持的最大ISO值
    
    // 刻度盘状态
    var activeDialMode: DialMode = .none   // 当前激活的刻度盘模式
    var isDialVisible: Bool = false        // 通用刻度盘可见性状态
    var isTemperatureDialVisible: Bool = false  // 温度刻度盘可见性状态
    var previousRightButtonState: Bool = false  // 保存显示刻度盘前的右侧按钮展开状态
    
    // 焦距选择器按钮状态
    var isLeftButtonExpanded: Bool = false  // 左侧按钮是否展开
    var isRightButtonExpanded: Bool = false // 右侧按钮是否展开
    var isAMMode: Bool = false             // A/M 模式状态（false 显示 A，true 显示 M）
    
    // 左侧功能按钮状态
    var isGridEnabled: Bool = false        // 参考线
    var isHistogramEnabled: Bool = false   // 直方图
    var isLevelEnabled: Bool = false       // 水平仪
    var isFlipped: Bool = false           // 翻转
    var isPeakingEnabled: Bool = false    // 峰值对焦
    var isHDREnabled: Bool = false        // HDR（照片模式）
    var isZebraEnabled: Bool = false      // 斑马线（视频模式）
    
    // 模式相关
    var isVideoMode: Bool = false          // 是否为视频模式
    var isRecording: Bool = false          // 是否正在录制
    var recordingTime: TimeInterval = 0    // 录制时间
    
    // 参数面板相关
    var isParameterExpanded: Bool = false  // 参数面板是否展开
    var sideButtonsOpacity: Double = 1     // 侧边按钮透明度
    var closeButtonOpacity: Double = 0     // 关闭按钮透明度
    
    // 相机设置
    var isFlashOn: Bool = false           // 闪光灯状态
    var currentPosition: AVCaptureDevice.Position = .back  // 当前相机位置
    
    // 会话状态
    // var session = AVCaptureSession()    // 相机会话 - 已移除，由SessionManager管理
    
    // 相机参数设置
    var settings = CameraSettings()      // 相机参数设置
    
    // 当前参数
    var currentParameters: Any {
        isVideoMode ? settings.videoParameters : settings.photoParameters
    }
    
    // 音频相关
    var audioLevels: AudioLevels = AudioLevels()
    var leftLevel: Float = 0.0 // 左声道峰值电平 (0.0 - 1.0)
    var rightLevel: Float = 0.0 // 右声道峰值电平 (0.0 - 1.0)
    
    // 快门相关
    var deviceMinShutter: Double = 1.0/8000.0  // 设备支持的最小快门速度（秒）
    var deviceMaxShutter: Double = 8.0  // 设备支持的最大快门速度（秒）
    var shutterValue: Double = 1.0/60.0  // 当前快门速度（秒）
    var previousShutterValue: Double = 1.0/60.0  // 上一次的快门速度（秒）
    
    // 对焦相关
    var focusDistance: Double = 1.0  // 当前对焦距离
    var focusDialRotationAngle: Double = 0.0  // 焦点刻度盘旋转角度
    
    // 光圈相关
    var apertureValue: Double = 1.8  // 当前光圈值（默认f/1.8）
    
    // 防抖相关
    var isStabilizationEnabled: Bool = false  // 是否启用防抖
    var stabilizationMode: StabilizationMode = .standard  // 当前防抖模式
    var isStabilizationOptionsVisible: Bool = false  // 是否显示防抖选项
    
    // 翻转相关状态
    var flipMode: FlipMode = .off
    var isFlipOptionsVisible: Bool = false
    
    // 视频编码相关状态
    var videoEncodingMode: VideoEncodingMode = .hevc
    var isVideoEncodingOptionsVisible: Bool = false
    
    // 视频宽高比相关状态
    var aspectRatioMode: AspectRatioMode = .ratio169
    var isAspectRatioOptionsVisible: Bool = false
    
    // 视频分辨率相关状态
    var resolutionMode: ResolutionMode = .res1080p
    var isResolutionOptionsVisible: Bool = false
    
    // 视频帧率相关状态
    var frameRateMode: FrameRateMode = .fps30
    var isFrameRateOptionsVisible: Bool = false
    
    // 视频色彩空间相关状态
    var colorSpaceMode: ColorSpaceMode = .sdr
    var isColorSpaceOptionsVisible: Bool = false
    
    // 照片文件格式相关状态
    var photoFormatMode: PhotoFormatMode = .jpg
    var isPhotoFormatOptionsVisible: Bool = false
    
    // 照片比例相关状态
    var photoRatioMode: PhotoRatioMode = .ratio43
    var isPhotoRatioOptionsVisible: Bool = false
    
    // 照片模式相关状态
    var photoMode: PhotoMode = .auto
    var isPhotoModeOptionsVisible: Bool = false
    
    // 定时器相关状态
    var timerMode: TimerMode = .off
    var isTimerOptionsVisible: Bool = false
}

// 音频电平数据结构
struct AudioLevels {
    var leftChannel: [Float] = Array(repeating: 0, count: 16)  // 左声道16个电平值
    var rightChannel: [Float] = Array(repeating: 0, count: 16) // 右声道16个电平值
}

// 翻转模式
enum FlipMode: String, Hashable {
    case off = "off"        // 关闭
    case horizontal = "horizontal" // 水平
    case vertical = "vertical"   // 垂直
    case both = "both"      // 双向
}

// 视频编码模式
enum VideoEncodingMode: String {
    case hevc = "HEVC"         // HEVC/H.265
    case h264 = "H.264"        // H.264
    case proResHQ = "ProRes 422 HQ"    // ProRes 422 HQ
    case proRes = "ProRes 422"      // ProRes 422
    case proResLT = "ProRes 422 LT"    // ProRes 422 LT
    case proResProxy = "ProRes 422 Proxy" // ProRes 422 Proxy
}

// 视频宽高比模式
enum AspectRatioMode: String {
    case ratio169 = "16:9"    // 16:9
    case ratio43 = "4:3"     // 4:3
    case ratio11 = "1:1"     // 1:1
    case ratio185 = "1.85:1"    // 1.85:1
    case ratio20 = "2:1"     // 2:1
    case ratio239 = "2.39:1"    // 2.39:1
    case ratio166 = "1.66:1"    // 1.66:1
}

// 视频分辨率模式
enum ResolutionMode: String {
    case res4K = "4K"      // 4K (3840x2160)
    case res4KDCI = "4K DCI"   // 4K DCI (4096x2160)
    case res2KDCI = "2K DCI"   // 2K DCI (2048x1080)
    case res1080p = "1080p"   // 1080p (1920x1080)
    case res720p = "720p"    // 720p (1280x720)
}

// 视频帧率模式
enum FrameRateMode: String {
    case fps30 = "30fps"     // 30fps
    case fps60 = "60fps"     // 60fps
    case fps25 = "25fps"     // 25fps
    case fps50 = "50fps"     // 50fps
    case fps24 = "24fps"     // 24fps
    case fps48 = "48fps"     // 48fps
    case fps120 = "120fps"    // 120fps
    case fps240 = "240fps"    // 240fps
}

// 视频色彩空间模式
enum ColorSpaceMode: String {
    case sdr = "SDR"           // SDR (Rec.709)
    case hlg = "HLG"           // HLG
    case dolbyVision = "Dolby Vision"   // Dolby Vision
    case appleLog = "Log"      // Apple ProRes Log
}

// 照片文件格式模式
enum PhotoFormatMode: String {
    case jpg = "JPG"       // JPEG/JPG 格式
    case heif = "HEIF"      // HEIF/HEIC 格式
    case png = "PNG"       // PNG 格式
    case tiff = "TIFF"      // TIFF 格式
    case raw = "RAW"       // RAW 格式
    case proRaw = "ProRAW"    // Apple ProRAW 格式
}

// 照片比例模式
enum PhotoRatioMode: String {
    case ratio43 = "4:3"      // 4:3 标准比例
    case ratio32 = "3:2"      // 3:2 专业相机比例
    case ratio169 = "16:9"     // 16:9 宽屏比例
    case ratio11 = "1:1"      // 1:1 方形比例
    case ratio67 = "6:7"      // 6:7 竖版比例
    case ratio21 = "2:1"      // 2:1 宽幅比例
    case ratio185 = "1.85:1"     // 1.85:1 电影比例
    case ratio239 = "2.39:1"     // 2.39:1 影院比例
    case ratio166 = "1.66:1"     // 1.66:1 欧洲宽银幕
    case ratioXPAN = "XPAN"    // 65:24 哈苏XPAN
}

// 照片模式
enum PhotoMode: Hashable {
    case auto        // 自动模式
    case portrait    // 人像模式
    case macro       // 微距模式
    case night       // 夜景模式
    case timelapse   // 延时摄影
    case lightTrail  // 光绘模式
    case pano        // 全景模式
}

// 定时器模式
enum TimerMode: String, Hashable {
    case off = "off"       // 关闭
    case three = "three"     // 3秒
    case five = "five"      // 5秒
    case ten = "ten"       // 10秒
    case fifteen = "fifteen"   // 15秒
    case thirty = "thirty"    // 30秒
} 