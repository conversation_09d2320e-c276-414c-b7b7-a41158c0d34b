import Foundation
import SwiftUI
import Combine

/// UI状态管理器
/// 负责管理相机界面的UI状态和动画效果
class UIStateManager: ObservableObject {
    // MARK: - Published Properties
    @Published var isRotating: Bool = false {
        didSet {
            rotatingPublisher.send(isRotating)
        }
    }
    @Published var rotationAngle: Double = 0 {
        didSet {
            rotationAnglePublisher.send(rotationAngle)
        }
    }
    @Published var isFilterPressed: Bool = false
    @Published var isEditPressed: Bool = false
    @Published var isLockPressed: Bool = false
    
    // 发布者，用于通知属性变化
    let rotatingPublisher = PassthroughSubject<Bool, Never>()
    let rotationAnglePublisher = PassthroughSubject<Double, Never>()
    
    // MARK: - 依赖注入
    private weak var cameraViewModel: CameraViewModel?
    private var animationViewModel: CameraAnimationViewModel
    
    // MARK: - 初始化
    init(cameraViewModel: CameraViewModel? = nil, animationViewModel: CameraAnimationViewModel) {
        self.cameraViewModel = cameraViewModel
        self.animationViewModel = animationViewModel
    }
    
    // MARK: - 公共方法
    
    /// 设置相机视图模型引用
    /// - Parameter viewModel: 相机视图模型
    func setCameraViewModel(_ viewModel: CameraViewModel) {
        self.cameraViewModel = viewModel
    }
    
    /// 执行相机切换动画
    /// - Parameters:
    ///   - completion: 动画完成后的回调
    func animateCameraSwitch(completion: @escaping () -> Void = {}) {
        // 更新UI
        DispatchQueue.main.async {
            withAnimation(.spring(response: AnimationConstants.duration, 
                                  dampingFraction: AnimationConstants.dampingFraction)) {
                self.isRotating = true
                self.rotationAngle += 180
            }
        }
        
        // 0.5秒后重置旋转状态并执行回调
        DispatchQueue.main.asyncAfter(deadline: .now() + UIConstants.cameraRotationResetDelay) {
            self.isRotating = false
            completion()
        }
    }
    
    /// 更新按钮按下状态
    /// - Parameters:
    ///   - button: 按钮类型
    ///   - isPressed: 是否按下
    func updateButtonPressState(button: ButtonType, isPressed: Bool) {
        switch button {
        case .filter:
            isFilterPressed = isPressed
        case .edit:
            isEditPressed = isPressed
        case .lock:
            isLockPressed = isPressed
        }
    }
    
    /// 在主线程上执行带动画的UI更新
    /// - Parameters:
    ///   - animation: 动画设置，默认使用标准弹簧动画
    ///   - action: 要执行的更新操作
    func executeWithAnimation(_ animation: Animation = .spring(response: AnimationConstants.duration, 
                                                            dampingFraction: AnimationConstants.dampingFraction),
                             _ action: @escaping () -> Void) {
        DispatchQueue.main.async {
            withAnimation(animation) {
                action()
            }
        }
    }
    
    /// 获取标准弹簧动画
    /// - Returns: 配置好的弹簧动画
    func getSpringAnimation() -> Animation {
        return animationViewModel.getSpringAnimation()
    }
    
    /// 获取快速弹簧动画
    /// - Returns: 配置好的快速弹簧动画
    func getQuickSpringAnimation() -> Animation {
        return animationViewModel.getQuickSpringAnimation()
    }
    
    /// 更新旋转状态
    /// - Parameter isRotating: 是否正在旋转
    func updateRotatingState(_ isRotating: Bool) {
        self.isRotating = isRotating
    }
    
    // MARK: - 枚举
    
    /// 按钮类型
    enum ButtonType {
        case filter
        case edit
        case lock
    }
} 