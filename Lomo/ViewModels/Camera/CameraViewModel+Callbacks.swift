import Foundation
import AVFoundation
import Combine
import UIKit
import SwiftUI

// MARK: - CameraViewModel 回调扩展
extension CameraViewModel {
    // MARK: - 相机服务回调
    
    /// 设置相机服务相关的所有回调
    /// 包括：设备类型、ISO范围、变焦因子、可用镜头、旋转状态、闪光灯状态、相机位置等
    internal func setupCameraServiceCallbacks() {
        // 设备类型变更回调
        self.cameraService.onDeviceTypeChanged = { [weak self] deviceType in
            self?.executeOnMain {
                self?.state.deviceType = deviceType
            }
        }
        
        // ISO范围变更回调
        self.cameraService.onISORangeChanged = { [weak self] minISO, maxISO in
            self?.executeOnMain {
                self?.state.deviceMinISO = Double(minISO)
                self?.state.deviceMaxISO = Double(maxISO)
                self?.state.isoValue = Double(minISO)
                self?.state.previousISOValue = Double(minISO)
                print("📱 ISO范围更新: \(minISO)-\(maxISO)")
            }
        }
        
        // 变焦因子变更回调
        self.cameraService.onZoomFactorsChanged = { [weak self] max, min, current, isSupported in
            self?.executeOnMain {
                self?.state.maxZoomFactor = max
                self?.state.minZoomFactor = min
                self?.state.currentZoomFactor = current
                self?.state.isZoomSupported = isSupported
            }
        }
        
        // 可用镜头变更回调
        self.cameraService.onAvailableLensesChanged = { [weak self] lenses in
            self?.executeOnMain {
                self?.state.availableLenses = lenses
                self?.zoomService.updateAvailableLenses(lenses)
            }
        }
        
        // 旋转状态变更回调
        self.cameraService.onRotatingStateChanged = { [weak self] isRotating in
            self?.executeOnMain {
                // 更新UIStateManager中的旋转状态
                self?.uiStateManager.updateRotatingState(isRotating)
            }
        }
        
        // 闪光灯状态变更回调
        self.cameraService.onFlashStateChanged = { [weak self] isFlashOn in
            self?.executeOnMain {
                self?.state.isFlashOn = isFlashOn
            }
        }
        
        // 相机位置变更回调
        self.cameraService.onPositionChanged = { [weak self] position in
            self?.executeOnMain {
                self?.state.currentPosition = position
            }
        }
        
        // 变焦因子变更回调
        self.cameraService.onZoomFactorChanged = { [weak self] factor in
            self?.executeOnMain {
                self?.state.currentZoomFactor = factor
            }
        }
        
        // 相机设置完成回调
        self.cameraService.onCameraSetupComplete = { [weak self] in
            self?.executeOnMain {
                if let minISO = self?.state.deviceMinISO {
                    print("📱 相机初始化完成，设置初始ISO: \(minISO)")
                    self?.setISOValue(minISO)
                }
            }
        }
        
        // 快门范围变更回调
        self.cameraService.onShutterRangeChanged = { [weak self] minShutter, maxShutter in
            self?.executeOnMain {
                self?.state.deviceMinShutter = minShutter
                self?.state.deviceMaxShutter = maxShutter
                self?.state.shutterValue = 1.0/60.0
                self?.state.previousShutterValue = 1.0/60.0
                print("📱 快门范围更新: \(minShutter)-\(maxShutter)")
            }
        }
    }
    
    // MARK: - 曝光服务回调
    
    /// 设置曝光服务相关的所有回调
    /// 包括：曝光值、ISO值、快门速度的变更
    internal func setupExposureServiceCallbacks() {
        // 曝光值变更回调
        self.exposureService.onExposureChanged = { [weak self] value in
            self?.executeOnMain {
                self?.state.exposureValue = value
            }
        }
        
        // ISO值变更回调
        self.exposureService.onISOChanged = { [weak self] value in
            self?.executeOnMain {
                self?.state.isoValue = value
            }
        }
        
        // 快门速度变更回调
        self.exposureService.onShutterSpeedChanged = { [weak self] value in
            self?.executeOnMain {
                self?.state.shutterValue = value
            }
        }
    }
    
    // MARK: - 变焦服务回调
    
    /// 设置变焦服务相关的所有回调
    /// 包括：变焦因子、选中镜头索引的变更
    internal func setupZoomServiceCallbacks() {
        // 变焦因子变更回调
        self.zoomService.onZoomFactorChanged = { [weak self] factor in
            self?.executeOnMain {
                print("📱 变焦因子更新: \(factor)")
                self?.state.currentZoomFactor = factor
            }
        }
        
        // 选中镜头索引变更回调
        self.zoomService.onSelectedLensIndexChanged = { [weak self] index in
            self?.executeOnMain {
                print("📱 镜头选择更新: \(index)")
                self?.state.selectedLensIndex = index
            }
        }
    }
    
    // MARK: - UI控制服务回调
    
    /// 设置UI控制服务相关的所有回调
    /// 包括：刻度盘可见性、刻度盘模式、触摸状态、按钮状态等
    internal func setupUIControlServiceCallbacks() {
        // 刻度盘可见性变更回调
        self.uiControlService.onDialVisibilityChanged = { [weak self] isVisible in
            self?.executeOnMain {
                self?.state.isDialVisible = isVisible
            }
        }
        
        // 刻度盘模式变更回调
        self.uiControlService.onDialModeChanged = { [weak self] mode in
            self?.executeOnMain {
                self?.state.activeDialMode = mode ?? .none
            }
        }
        
        // 触摸状态变更回调
        self.uiControlService.onTouchStateChanged = { [weak self] isTouching in
            self?.executeOnMain {
                self?.state.isTouching = isTouching
            }
        }
        
        // 左侧按钮状态变更回调
        self.buttonLogic.onLeftButtonStateChanged = { [weak self] isExpanded in
            self?.executeWithAnimation {  // 使用默认动画，与右侧A/M按钮保持一致
                print("📱 3×3按钮状态更新: \(isExpanded)")
                self?.state.isLeftButtonExpanded = isExpanded
            }
        }
        
        // 右侧按钮状态变更回调
        self.buttonLogic.onRightButtonStateChanged = { [weak self] isExpanded, isAMMode in
            self?.executeWithAnimation {
                print("📱 A/M按钮状态更新: expanded=\(isExpanded), AMMode=\(isAMMode)")
                self?.state.isRightButtonExpanded = isExpanded
                self?.state.isAMMode = isAMMode
            }
        }
        
        // 参数面板状态变更回调
        self.buttonLogic.onParameterStateChanged = { [weak self] isExpanded in
            self?.executeWithAnimation(.spring(response: AnimationConstants.quickDuration, 
                                            dampingFraction: AnimationConstants.quickDampingFraction)) {
                print("📱 参数面板状态更新: \(isExpanded)")
                if !isExpanded {
                    self?.state.isParameterExpanded = false
                    self?.state.closeButtonOpacity = 0
                    self?.state.sideButtonsOpacity = 1
                }
            }
        }
    }
    
    // MARK: - 录制服务回调
    
    /// 设置录制服务相关的所有回调
    /// 包括：录制状态变更
    internal func setupRecordingServiceCallbacks() {
        // 录制状态变更回调
        self.recordingService.onRecordingStateChanged = { [weak self] isRecording in
            self?.executeOnMain {
                self?.state.isRecording = isRecording
                // 录制状态变化时重置暂停状态
                self?.isRecordingPaused = false
                
                if isRecording {
                    self?.buttonLogic.startRecording { time in
                        self?.executeOnMain {
                            self?.state.recordingTime = time
                        }
                    }
                    self?.startAudioLevelMonitoring()
                } else {
                    self?.buttonLogic.stopRecording { time in
                        self?.executeOnMain {
                            self?.state.recordingTime = time
                        }
                    }
                    self?.stopAudioLevelMonitoring()
                    // 重置暂停状态
                    self?.isRecordingPaused = false
                }
            }
        }
        
        // 添加暂停状态变更回调
        self.recordingService.onRecordingPauseStateChanged = { [weak self] isPaused in
            self?.executeOnMain {
                self?.isRecordingPaused = isPaused
                // 当暂停状态改变时，确保时间显示也跟着更新
                if isPaused {
                    // 暂停时停止buttonLogic的计时器更新
                    self?.buttonLogic.pauseRecordingTimer()
                } else {
                    // 继续时恢复buttonLogic的计时器更新
                    if self?.state.isRecording == true {
                        self?.buttonLogic.resumeRecordingTimer { time in
                            self?.executeOnMain {
                                self?.state.recordingTime = time
                            }
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 辅助方法
extension CameraViewModel {
    /// 在主线程上执行UI更新
    /// - Parameter action: 要执行的更新操作
    internal func executeOnMain(_ action: @escaping () -> Void) {
        if Thread.isMainThread {
            action()
        } else {
            DispatchQueue.main.async(execute: action)
        }
    }
    
    /// 在主线程上执行带动画的UI更新
    /// - Parameters:
    ///   - animation: 动画设置，默认使用标准弹簧动画
    ///   - action: 要执行的更新操作
    internal func executeWithAnimation(_ animation: Animation = .spring(response: AnimationConstants.duration, 
                                                            dampingFraction: AnimationConstants.dampingFraction),
                            _ action: @escaping () -> Void) {
        uiStateManager.executeWithAnimation(animation, action)
    }
} 