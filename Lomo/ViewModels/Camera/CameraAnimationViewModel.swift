import Foundation
import SwiftUI
import Combine

/// 相机动画视图模型
/// 负责处理相机界面所有动画相关逻辑
class CameraAnimationViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isRotating: Bool = false
    @Published var rotationAngle: Double = 0
    
    // MARK: - 依赖注入
    private var state: CameraState
    
    // MARK: - 初始化
    init(state: CameraState) {
        self.state = state
    }
    
    // MARK: - 公共方法
    
    /// 执行相机切换动画
    /// - Parameter completion: 动画完成后的回调
    func animateCameraSwitch(completion: @escaping () -> Void) {
        // 更新UI
        DispatchQueue.main.async {
            withAnimation(.spring(response: AnimationConstants.duration, 
                                  dampingFraction: AnimationConstants.dampingFraction)) {
                self.isRotating = true
                self.rotationAngle += 180
            }
        }
        
        // 0.5秒后重置旋转状态并执行回调
        DispatchQueue.main.asyncAfter(deadline: .now() + UIConstants.cameraRotationResetDelay) {
            self.isRotating = false
            completion()
        }
    }
    
    /// 获取标准弹簧动画
    /// - Parameters:
    ///   - response: 响应时间
    ///   - dampingFraction: 阻尼系数
    /// - Returns: 配置好的弹簧动画
    func getSpringAnimation(response: Double = AnimationConstants.duration, 
                           dampingFraction: Double = AnimationConstants.dampingFraction) -> Animation {
        return .spring(response: response, dampingFraction: dampingFraction)
    }
    
    /// 获取快速弹簧动画
    /// - Returns: 配置好的快速弹簧动画
    func getQuickSpringAnimation() -> Animation {
        return .spring(response: AnimationConstants.quickDuration, 
                     dampingFraction: AnimationConstants.quickDampingFraction)
    }
    
    /// 执行参数面板展开动画
    /// - Parameter action: 要执行的动作
    func animateParameterExpansion(action: @escaping () -> Void) {
        withAnimation(AnimationConstants.standardSpring) {
            action()
        }
    }
    
    /// 执行主题元素过渡动画
    /// - Parameter action: 要执行的动作
    func animateTransition(action: @escaping () -> Void) {
        withAnimation(.spring(response: AnimationConstants.duration, 
                             dampingFraction: AnimationConstants.dampingFraction)) {
            action()
        }
    }
} 