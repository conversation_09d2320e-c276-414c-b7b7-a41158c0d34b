import Foundation
import SwiftUI
import Combine

/// 相机设置视图模型
/// 负责处理相机所有设置相关的状态和逻辑
class CameraSettingsViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var isGridEnabled: Bool = false
    @Published var isHistogramEnabled: Bool = false
    @Published var isLevelEnabled: Bool = false
    @Published var isFlipped: Bool = false
    @Published var isPeakingEnabled: Bool = false
    @Published var isTimerEnabled: Bool = false
    @Published var isHDREnabled: Bool = false
    @Published var isZebraEnabled: Bool = false
    @Published var isStabilizationEnabled: Bool = false
    
    // 用户状态 - 是否是Pro用户
    @Published var isProUser: Bool = false
    
    // MARK: - 依赖
    
    private var state: CameraState
    private let buttonLogic: ButtonControlLogic
    private let cameraBasicManager = CameraBasicManager.shared
    private let settingsStorageService = CameraSettingsStorageService.shared
    
    // MARK: - 初始化
    init(
        state: CameraState, 
        buttonLogic: ButtonControlLogic = ButtonControlLogic()
    ) {
        self.state = state
        self.buttonLogic = buttonLogic
        
        // 从持久化存储加载设置
        loadSavedSettings()
        
        // 监听Pro用户状态变化通知
        let notificationService = NotificationService.shared
        notificationService.addObserver(
            self,
            selector: #selector(handleProUserStatusChanged),
            name: Notification.Name("ProUserStatusChanged"),
            object: nil
        )
    }
    
    deinit {
        // 移除通知观察者
        NotificationService.shared.removeObserver(self)
    }
    
    // MARK: - 设置加载和保存
    
    /// 从持久化存储加载设置
    private func loadSavedSettings() {
        let savedSettings = cameraBasicManager.getSettings()
        
        // 加载3x3按钮状态设置
        isGridEnabled = savedSettings.isGridEnabled
        isHistogramEnabled = savedSettings.isHistogramEnabled
        isLevelEnabled = savedSettings.isLevelEnabled
        isPeakingEnabled = savedSettings.isPeakingEnabled
        isFlipped = savedSettings.isFlipped
        isZebraEnabled = savedSettings.isZebraEnabled
        isStabilizationEnabled = savedSettings.isStabilizationEnabled
        isHDREnabled = savedSettings.isHDREnabled
    }
    
    // MARK: - 设置控制方法
    
    // 参考线切换
    func toggleGrid() {
        buttonLogic.toggleGrid(currentState: isGridEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isGridEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateGridEnabled(newState)
        }
    }
    
    // 峰值对焦切换
    func togglePeaking() {
        buttonLogic.togglePeaking(currentState: isPeakingEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isPeakingEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updatePeakingEnabled(newState)
        }
    }
    
    // 直方图切换
    func toggleHistogram() {
        buttonLogic.toggleHistogram(currentState: isHistogramEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isHistogramEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateHistogramEnabled(newState)
        }
    }
    
    // 水平仪切换
    func toggleLevel() {
        buttonLogic.toggleLevel(currentState: isLevelEnabled) { [weak self] newState in
            guard let self = self else { return }
            self.isLevelEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateLevelEnabled(newState)
        }
    }
    
    // 翻转切换
    func toggleFlip() {
        buttonLogic.toggleFlip(currentState: isFlipped) { [weak self] newState in
            guard let self = self else { return }
            self.isFlipped = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateFlippedEnabled(newState)
        }
    }
    
    // HDR切换（照片模式）
    func toggleHDR() {
        // 检查格式兼容性
        checkHDRCompatibility()
        
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            isHDREnabled.toggle()  // 切换状态
            
            // 使用新的存储服务
            settingsStorageService.updateHDREnabled(isHDREnabled)
            
            // 如果开启了HDR而Live Photo也是开启状态，则自动关闭Live Photo
            if isHDREnabled && state.isLivePhotoEnabled {
                state.isLivePhotoEnabled = false
            }
            
            buttonLogic.toggleHDR(currentState: isHDREnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
                guard let self = self else { return }
                self.isHDREnabled = newState
                
                // 使用新的存储服务
                self.settingsStorageService.updateHDREnabled(newState)
            }
        }
    }
    
    // 斑马线切换（视频模式）
    func toggleZebra() {
        buttonLogic.toggleZebra(currentState: isZebraEnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
            guard let self = self else { return }
            self.isZebraEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateZebraEnabled(newState)
        }
    }
    
    // 防抖切换
    func toggleStabilization() {
        buttonLogic.toggleStabilization(currentState: isStabilizationEnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
            guard let self = self else { return }
            self.isStabilizationEnabled = newState
            
            // 使用新的存储服务
            self.settingsStorageService.updateStabilizationEnabled(newState)
        }
    }
    
    // MARK: - 辅助方法
    
    /// 处理Pro用户状态变化通知
    @objc private func handleProUserStatusChanged(_ notification: Notification) {
        if let isProUser = notification.userInfo?["isProUser"] as? Bool {
            DispatchQueue.main.async {
                self.isProUser = isProUser
            }
        }
    }
    
    /// 检查HDR与选择的照片格式的兼容性
    private func checkHDRCompatibility() {
        // JPG、HEIF和TIFF格式支持HDR
        let isHDRCompatible = [PhotoFormatMode.jpg, .heif, .tiff].contains(state.photoFormatMode)
        
        // 如果当前格式不支持HDR但准备开启HDR
        if !isHDRCompatible && !isHDREnabled {
            // 自动切换到支持的格式
            if [.raw, .proRaw].contains(state.photoFormatMode) {
                state.photoFormatMode = .tiff
            } else if state.photoFormatMode == .png {
                state.photoFormatMode = .jpg
            }
        }
    }
} 