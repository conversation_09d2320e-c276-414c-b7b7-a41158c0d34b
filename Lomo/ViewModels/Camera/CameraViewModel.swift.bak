import Foundation
import AVFoundation
import Combine
import UIKit
import SwiftUI

class CameraViewModel: NSObject, ObservableObject {
    // MARK: - Published State
    @Published var state = CameraState()
    @Published var isFilterPressed: Bool = false
    @Published var isEditPressed: Bool = false
    @Published var isLockPressed: Bool = false
    @Published var isLocked: Bool = false           // 锁定状态
    
    // 用户状态 - 是否是Pro用户
    @Published var isProUser: Bool = false  // 默认为非Pro用户
    
    // MARK: - 视图模型
    
    /// 特效视图模型
    @Published var effectsVM = EffectsViewModel()
    
    /// 动画视图模型
    @Published var animationVM = CameraAnimationViewModel()
    
    /// 设置视图模型
    @Published var settingsVM = CameraSettingsViewModel()
    
    /// 相机控制视图模型
    @Published var cameraControlVM: CameraControlViewModel!
    
    /// 曝光控制视图模型
    @Published var exposureControlVM: ExposureControlViewModel!
    
    // MARK: - Published UI States
    // 这些属性已经在settingsVM中定义，删除它们
    // @Published private(set) var isGridEnabled: Bool = false
    // @Published private(set) var isHistogramEnabled: Bool = false
    // @Published private(set) var isLevelEnabled: Bool = false
    // @Published private(set) var isFlipped: Bool = false
    // @Published private(set) var isPeakingEnabled: Bool = false
    // @Published private(set) var isTimerEnabled: Bool = false
    // @Published private(set) var isHDREnabled: Bool = false
    // @Published private(set) var isZebraEnabled: Bool = false
    // @Published private(set) var isStabilizationEnabled: Bool = false
    
    // MARK: - 服务依赖
    internal var cameraService: CameraServiceProtocol
    internal var exposureService: ExposureServiceProtocol
    internal var zoomService: ZoomServiceProtocol
    internal var uiControlService: UIControlServiceProtocol
    internal var recordingService: RecordingServiceProtocol
    internal let buttonLogic = ButtonControlLogic()
    internal let animationService: CameraAnimationService
    internal let eventHandler: CameraEventHandler
    
    // 添加相机基础设置管理器
    private let cameraBasicManager = CameraBasicManager.shared
    
    // 添加存储服务
    private let storageService = UserDefaultsService.shared
    
    // MARK: - State Properties
    @Published var effectsState = CameraState()
    
    // MARK: - Computed Properties
    var currentOptions: [String] {
        buttonLogic.currentOptions
    }
    
    // 获取选项类型
    func getOptionType(_ option: String) -> ButtonControlLogic.ParameterOptionType {
        buttonLogic.getOptionType(option)
    }
    
    // MARK: - 初始化
    init(
        cameraService: CameraServiceProtocol? = nil,
        exposureService: ExposureServiceProtocol? = nil,
        zoomService: ZoomServiceProtocol? = nil,
        uiControlService: UIControlServiceProtocol? = nil,
        recordingService: RecordingServiceProtocol? = nil
    ) {
        // 使用提供的服务或创建默认服务
        let services = ServiceFactory.createServices(session: CameraState().session)
        
        self.cameraService = cameraService ?? services.cameraService
        self.exposureService = exposureService ?? services.exposureService
        self.zoomService = zoomService ?? services.zoomService
        self.uiControlService = uiControlService ?? services.uiControlService
        self.recordingService = recordingService ?? services.recordingService
        
        // 初始化辅助组件
        self.eventHandler = CameraEventHandler(viewModel: nil)
        self.animationService = CameraAnimationService(viewModel: nil)
        
        // 清除上次保存的刻度盘角度
        storageService.clearAllDialRotationAngles()
        
        super.init()
        
        // 初始化相机控制视图模型
        self.cameraControlVM = CameraControlViewModel(
            state: state,
            cameraService: self.cameraService,
            recordingService: self.recordingService,
            animationVM: self.animationVM,
            buttonLogic: self.buttonLogic,
            settingsVM: self.settingsVM,
            eventHandler: self.eventHandler,
            cameraBasicManager: self.cameraBasicManager
        )
        
        // 初始化曝光控制视图模型
        self.exposureControlVM = ExposureControlViewModel(
            state: state,
            cameraService: self.cameraService,
            exposureService: self.exposureService,
            uiControlService: self.uiControlService,
            buttonLogic: self.buttonLogic
        )
        
        // 设置依赖关系
        self.eventHandler.viewModel = self
        self.animationService.viewModel = self
        
        // 获取DialController并设置ViewModel
        if let dialController = (self.uiControlService as? UIControlService)?.dialController {
            dialController.setViewModel(self)
        }
        
        // 从持久化存储加载基本设置
        loadSavedBasicSettings()
        
        // 设置回调
        setupCallbacks()
        
        // 初始化相机
        self.cameraService.setup()
        
        // 监听Pro用户状态变化通知
        let notificationService = NotificationService.shared
        notificationService.addObserver(
            self,
            selector: #selector(handleProUserStatusChanged),
            name: Notification.Name("ProUserStatusChanged"),
            object: nil
        )
    }
    
    // 处理Pro用户状态变化通知
    @objc private func handleProUserStatusChanged(_ notification: Notification) {
        if let isProUser = notification.userInfo?["isProUser"] as? Bool {
            DispatchQueue.main.async {
                self.isProUser = isProUser
            }
        }
    }
    
    deinit {
        // 移除通知观察者
        NotificationService.shared.removeObserver(self)
    }
    
    // MARK: - 特效代理方法
    
    /// 选择漏光预设
    func selectLightLeakPreset(_ preset: LightLeakPreset?) {
        effectsVM.selectLightLeakPreset(preset)
    }
    
    /// 更新漏光强度
    func updateLightLeakIntensity(_ intensity: Double) {
        effectsVM.updateLightLeakIntensity(intensity)
    }
    
    /// 更新漏光混合模式
    func updateLightLeakBlendMode(_ blendMode: LightLeakBlendMode) {
        effectsVM.updateLightLeakBlendMode(blendMode)
    }
    
    /// 切换漏光效果
    func toggleLightLeakEnabled() {
        effectsVM.toggleLightLeakEnabled()
    }
    
    /// 选择颗粒预设
    func selectGrainPreset(_ preset: GrainPreset?) {
        effectsVM.selectGrainPreset(preset)
    }
    
    /// 更新颗粒强度
    func updateGrainIntensity(_ intensity: Double) {
        effectsVM.updateGrainIntensity(intensity)
    }
    
    /// 切换颗粒效果
    func toggleGrainEnabled() {
        effectsVM.toggleGrainEnabled()
    }
    
    /// 选择划痕预设
    func selectScratchPreset(_ preset: ScratchPreset?) {
        effectsVM.selectScratchPreset(preset)
    }
    
    /// 更新划痕强度
    func updateScratchIntensity(_ intensity: Double) {
        effectsVM.updateScratchIntensity(intensity)
    }
    
    /// 切换划痕效果
    func toggleScratchEnabled() {
        effectsVM.toggleScratchEnabled()
    }
    
    /// 应用所有特效到图像
    func applyAllEffectsToImage(_ image: UIImage) -> UIImage {
        return effectsVM.applyAllEffectsToImage(image)
    }
    
    // MARK: - 持久化方法
    
    /// 从持久化存储加载基本设置
    private func loadSavedBasicSettings() {
        let savedSettings = cameraBasicManager.getSettings()
        
        // 加载基础设置
        state.isVideoMode = savedSettings.isVideoMode
        
        // 相机位置默认为后置摄像头（不从持久化存储加载，与定时器保持一致的实现）
        state.currentPosition = .back
        
        // 加载视频模式状态栏设置
        if let encodingMode = VideoEncodingMode(rawValue: savedSettings.videoEncodingMode) {
            state.videoEncodingMode = encodingMode
        }
        if let aspectRatioMode = AspectRatioMode(rawValue: savedSettings.videoAspectRatioMode) {
            state.aspectRatioMode = aspectRatioMode
        }
        if let resolutionMode = ResolutionMode(rawValue: savedSettings.videoResolutionMode) {
            state.resolutionMode = resolutionMode
        }
        if let frameRateMode = FrameRateMode(rawValue: savedSettings.videoFrameRateMode) {
            state.frameRateMode = frameRateMode
        }
        if let colorSpaceMode = ColorSpaceMode(rawValue: savedSettings.videoColorSpaceMode) {
            state.colorSpaceMode = colorSpaceMode
        }
        
        // 加载照片模式状态栏设置
        if let photoFormatMode = PhotoFormatMode(rawValue: savedSettings.photoFormatMode) {
            state.photoFormatMode = photoFormatMode
        }
        if let photoRatioMode = PhotoRatioMode(rawValue: savedSettings.photoRatioMode) {
            state.photoRatioMode = photoRatioMode
        }
        
        // 加载照片模式
        switch savedSettings.photoMode {
        case "auto":
            state.photoMode = .auto
        case "portrait":
            state.photoMode = .portrait
        case "macro":
            state.photoMode = .macro
        case "night":
            state.photoMode = .night
        case "timelapse":
            state.photoMode = .timelapse
        case "lightTrail":
            state.photoMode = .lightTrail
        case "pano":
            state.photoMode = .pano
        default:
            state.photoMode = .auto
        }
        
        // 加载3x3按钮状态设置
        // isGridEnabled = savedSettings.isGridEnabled
        // isHistogramEnabled = savedSettings.isHistogramEnabled
        
        // 加载水平仪、峰值对焦和翻转功能设置
        // isLevelEnabled = savedSettings.isLevelEnabled
        // isPeakingEnabled = savedSettings.isPeakingEnabled
        // isFlipped = savedSettings.isFlipped
        
        // 加载翻转模式
        if let flipMode = FlipMode(rawValue: savedSettings.flipMode) {
            state.flipMode = flipMode
        }
        
        // 加载斑马线和防抖功能设置
        // isZebraEnabled = savedSettings.isZebraEnabled
        // isStabilizationEnabled = savedSettings.isStabilizationEnabled
        
        // 加载防抖模式
        if let stabilizationMode = StabilizationMode(rawValue: savedSettings.stabilizationMode) {
            state.stabilizationMode = stabilizationMode
        }
        
        // 加载HDR状态（状态栏功能，非3x3按钮）
        // isHDREnabled = savedSettings.isHDREnabled
        
        // 同步buttonLogic状态
        buttonLogic.setVideoMode(state.isVideoMode)
    }
    
    /// 保存当前的相机基本设置
    private func saveBasicSettings() {
        var settings = cameraBasicManager.getSettings()
        settings.isVideoMode = state.isVideoMode
        cameraBasicManager.saveSettings(settings)
    }
    
    // MARK: - 回调设置
    
    /// 设置所有回调
    internal func setupCallbacks() {
        setupCameraServiceCallbacks()
        setupExposureServiceCallbacks()
        setupZoomServiceCallbacks()
        setupUIControlServiceCallbacks()
        setupRecordingServiceCallbacks()
    }
    
    // MARK: - Audio Processing Methods
    
    internal func setupAudioProcessing() {
        // 音频处理初始化，以后实现
    }
    
    internal func startAudioLevelMonitoring() {
        // 开始音频电平监控，以后实现
    }
    
    internal func stopAudioLevelMonitoring() {
        // 停止音频电平监控，以后实现
    }
    
    internal func updateAudioLevels() {
        // 更新音频电平数据，以后实现
    }
    
    // MARK: - Public Methods
    
    func toggleFlash() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            switch state.flashMode {
            case .off:
                state.flashMode = .on
            case .on:
                state.flashMode = .auto
            case .auto:
                state.flashMode = .off
            }
        }
        // 这里可以添加实际的相机闪光灯设置代码
        print("📸 闪光灯状态: \(state.flashMode)")
    }
    
    func switchCamera() {
        // 如果正在录制或处理中，不允许切换
        if state.isRecording {
            return
        }
        
        // 切换摄像头
        let newPosition: AVCaptureDevice.Position = state.currentPosition == .back ? .front : .back
        
        // 执行相机切换动画
        animationVM.animateCameraSwitch {
            // 动画完成后的回调，可以放置任何需要在动画完成后执行的代码
        }
        
        // 执行相机切换，不传参数
        cameraService.switchCamera()
        
        // 更新currentPosition，因为switchCamera方法不接受参数
        state.currentPosition = newPosition
    }
    
    func selectLens(_ lens: String) {
        zoomService.selectLens(lens)  // 修改为使用zoomService
    }
    
    func capturePhoto() {
        cameraService.capturePhoto()
    }
    
    func getCurrentDevice() -> AVCaptureDevice? {
        return cameraService.getCurrentDevice()
    }
    
    // MARK: - UI State Methods
    
    func toggleVideoMode() {
        state.isVideoMode.toggle()
        
        // 保存到持久化存储
        cameraBasicManager.updateSetting(\.isVideoMode, value: state.isVideoMode)
        
        // 更新UI和相机设置
        buttonLogic.setVideoMode(state.isVideoMode)  // 使用setVideoMode代替不存在的resetOptions
        state.isDialVisible = false  // 隐藏刻度盘
        state.isParameterExpanded = false  // 折叠参数面板
        
        // 如果在录制中，停止录制
        if state.isRecording {
            toggleRecording()  // 使用toggleRecording代替不存在的stopRecording
        }
    }
    
    func toggleLivePhoto() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isLivePhotoEnabled.toggle()
            
            // 如果开启了Live Photo而HDR也是开启状态，则自动关闭HDR
            if state.isLivePhotoEnabled && settingsVM.isHDREnabled {
                settingsVM.isHDREnabled = false
            }
        }
        // 这里可以添加实际的相机Live Photo设置代码
        print("📸 Live Photo状态: \(state.isLivePhotoEnabled ? "开启" : "关闭")")
    }
    
    func toggleRecording() {
        Task {
            do {
                try await recordingService.toggleRecording()
            } catch {
                print("Recording toggle failed: \(error)")
            }
        }
    }
    
    // MARK: - 参数面板控制
    
    // 切换参数面板显示状态
    func toggleParameter() {
        if state.isParameterExpanded {
            collapseParameter()
        } else {
            expandParameter()
        }
    }
    
    // 展开参数面板
    func expandParameter() {
        // 如果正在录制，不允许展开
        if state.isRecording {
            return
        }
        
        state.isParameterExpanded = true
        state.closeButtonOpacity = 1
        
        // 延迟隐藏侧边按钮
        DispatchQueue.main.asyncAfter(deadline: .now() + AnimationConstants.quickDuration) {
            self.state.sideButtonsOpacity = 0
        }
        
        // 使用通用计时器方法启动参数面板的计时器
        buttonLogic.startOptionPanelTimer(type: .parameter) { [weak self] in
            guard let self = self else { return }
            self.state.isParameterExpanded = false
            self.state.closeButtonOpacity = 0
            self.state.sideButtonsOpacity = 1
        }
    }
    
    // 收起参数面板
    func collapseParameter() {
        state.isParameterExpanded = false
        state.closeButtonOpacity = 0
        state.sideButtonsOpacity = 1
        
        // 取消计时器
        buttonLogic.cancelOptionPanelTimer(type: .parameter)
    }
    
    // MARK: - Timer Reset Methods
    
    func resetParameterTimer() {
        print("⏰ 参数面板：准备重置计时器，当前展开状态=\(state.isParameterExpanded)")
        if state.isParameterExpanded {
            buttonLogic.resetOptionPanelTimer(type: .parameter, isVisible: true) { [weak self] in
                guard let self = self else { return }
                self.state.isParameterExpanded = false
                self.state.closeButtonOpacity = 0
                self.state.sideButtonsOpacity = 1
            }
        } else {
            print("⏰ 参数面板：面板未展开，跳过重置")
        }
    }
    
    func resetLeftButtonTimer() {
        print("⏰ 3×3按钮：准备重置计时器，当前展开状态=\(state.isLeftButtonExpanded)")
        if state.isLeftButtonExpanded {
            buttonLogic.resetOptionPanelTimer(type: .leftButton, isVisible: true) { [weak self] in
                guard let self = self else { return }
                self.state.isLeftButtonExpanded = false
            }
        } else {
            print("⏰ 3×3按钮：按钮未展开，跳过重置")
        }
    }
    
    func resetRightButtonTimer() {
        print("⏰ A/M按钮：准备重置计时器，当前展开状态=\(state.isRightButtonExpanded)")
        if state.isRightButtonExpanded {
            buttonLogic.resetOptionPanelTimer(type: .rightButton, isVisible: true) { [weak self] in
                guard let self = self else { return }
                self.state.isRightButtonExpanded = false
                self.state.isAMMode = false
            }
        } else {
            print("⏰ A/M按钮：按钮未展开，跳过重置")
        }
    }
    
    // MARK: - Tint Dial Methods
    
    func showTintDial() {
        exposureControlVM.showTintDial()
    }
    
    // MARK: - ISO Dial Methods
    
    func showISODial() {
        exposureControlVM.showISODial()
    }
    
    // 设置ISO值
    func setISOValue(_ value: Double) {
        exposureControlVM.setISOValue(value)
    }
    
    func updateISO(_ value: Double) {
        exposureControlVM.updateISO(value)
    }
    
    // MARK: - Zoom Dial Methods
    
    func showZoomDial() {
        zoomService.showZoomDial()
    }
    
    func hideZoomDial() {
        zoomService.hideZoomDial()
    }
    
    func updateZoom(_ zoomFactor: CGFloat) {
        zoomService.updateZoom(zoomFactor)
    }
    
    func selectLensAtIndex(_ index: Int) {
        zoomService.selectLensAtIndex(index)
    }
    
    // MARK: - Touch State Methods
    
    func handleTouchBegan() {
        uiControlService.handleTouchBegan()
    }
    
    func handleTouchEnded() {
        uiControlService.handleTouchEnded()
    }
    
    // MARK: - 焦距选择器按钮控制
    
    // 左侧按钮（3×3按钮）切换
    func toggleLeftButton() {
        state.isLeftButtonExpanded.toggle()
        
        // 调用buttonLogic来管理计时器
        buttonLogic.toggleLeftButton(isExpanded: state.isLeftButtonExpanded)
    }
    
    // 右侧按钮（A/M按钮）切换
    func toggleRightButton() {
        state.isRightButtonExpanded.toggle()
        // 不再自动切换A/M模式
        
        buttonLogic.toggleRightButton(isExpanded: state.isRightButtonExpanded, isAMMode: state.isAMMode)
    }
    
    // MARK: - 左侧功能按钮控制
    
    // 参考线切换
    func toggleGrid() {
        settingsVM.toggleGrid()
    }
    
    // 峰值对焦切换
    func togglePeaking() {
        settingsVM.togglePeaking()
    }
    
    // 直方图切换
    func toggleHistogram() {
        settingsVM.toggleHistogram()
    }
    
    // 水平仪切换
    func toggleLevel() {
        settingsVM.toggleLevel()
    }
    
    // 翻转切换
    func toggleFlip() {
        buttonLogic.toggleFlip(currentState: state.flipMode != .off) { [weak self] newState in
            self?.state.flipMode = newState ? .horizontal : .off
            
            // 添加持久化存储
            self?.cameraBasicManager.updateSetting(\.flipMode, value: newState ? FlipMode.horizontal.rawValue : FlipMode.off.rawValue)
        }
    }
    
    // HDR切换（照片模式）
    func toggleHDR() {
        // 如果当前HDR是关闭状态，并且用户要开启HDR
        if !settingsVM.isHDREnabled {
            // 检查当前照片格式是否支持HDR
            let isHDRCompatible = [.jpg, .heif, .tiff].contains(state.photoFormatMode)
            
            // 如果当前格式不支持HDR但HDR已开启，则关闭HDR
            if !isHDRCompatible {
                // RAW和ProRAW格式自动切换到TIFF
                if [.raw, .proRaw].contains(state.photoFormatMode) {
                    state.photoFormatMode = .tiff
                    // 保存到持久化存储
                    cameraBasicManager.updateSetting(\.photoFormatMode, value: PhotoFormatMode.tiff.rawValue)
                } 
                // PNG格式自动切换到JPG
                else if state.photoFormatMode == .png {
                    state.photoFormatMode = .jpg
                    // 保存到持久化存储
                    cameraBasicManager.updateSetting(\.photoFormatMode, value: PhotoFormatMode.jpg.rawValue)
                }
            }
        }
        
        settingsVM.toggleHDR()
        
        // 如果开启了HDR而Live Photo也是开启状态，则自动关闭Live Photo
        if settingsVM.isHDREnabled && state.isLivePhotoEnabled {
            state.isLivePhotoEnabled = false
        }
    }
    
    // 斑马线切换（视频模式）
    func toggleZebra() {
        settingsVM.toggleZebra()
    }
    
    // 防抖切换
    func toggleStabilization() {
        // 不直接切换isStabilizationEnabled状态
        // 而是只负责显示/隐藏选项面板
        
        buttonLogic.toggleStabilization(currentState: settingsVM.isStabilizationEnabled, isVideoMode: state.isVideoMode) { [weak self] _ in
            guard let self = self else { return }
            
            if state.isStabilizationOptionsVisible {
                // 如果当前是显示状态，就隐藏
                state.isStabilizationOptionsVisible = false
            } else {
                // 如果当前是隐藏状态，先隐藏其他选项，再显示当前选项
                hideAllBottomOptions()
                state.isStabilizationOptionsVisible = true
                
                // 启动自动隐藏计时器
                buttonLogic.startOptionPanelTimer(type: .bottomOptionPanel) { [weak self] in
                    self?.state.isStabilizationOptionsVisible = false
                }
            }
            
            // 不在这里更改isStabilizationEnabled状态
            // 状态只在选择具体模式时更改
        }
    }
    
    // 选择防抖模式
    func selectStabilizationMode(_ mode: StabilizationMode) {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.stabilizationMode = mode
            
            // 关键逻辑：只有在模式不是"off"时才启用防抖
            settingsVM.isStabilizationEnabled = (mode != .off)
            
            // 添加持久化存储
            cameraBasicManager.updateSetting(\.stabilizationMode, value: mode.rawValue)
            cameraBasicManager.updateSetting(\.isStabilizationEnabled, value: settingsVM.isStabilizationEnabled)
            
            // 选择后自动隐藏选项面板
            state.isStabilizationOptionsVisible = false
            
            // 重置左侧按钮计时器
            resetLeftButtonTimer()
        }
        // 这里可以添加实际的相机防抖模式设置代码
        print("📸 防抖模式: \(mode)")
    }
    
    // 定时器切换（照片模式）
    func toggleTimer() {
        buttonLogic.toggleTimer(currentState: settingsVM.isTimerEnabled, isVideoMode: state.isVideoMode) { [weak self] newState in
            self?.settingsVM.isTimerEnabled = newState
            
            // 如果关闭定时器，将定时器模式设为off
            if !newState {
                self?.state.timerMode = .off
            }
        }
    }
    
    // MARK: - Recording Timer Methods
    
    // 恢复原始实现：调用 ButtonControlLogic 中的格式化方法
    internal func formattedTime(_ time: TimeInterval) -> String {
        // 直接调用 buttonLogic 的方法，它会处理 HH:MM:SS 格式
        buttonLogic.formattedTime(time)
    }

    // 计算属性 formattedRecordingTime 保持不变，它会调用上面恢复后的 formattedTime
    var formattedRecordingTime: String {
        formattedTime(state.recordingTime)
    }
    
    // MARK: - Animation Helpers
    
    private func animate(_ action: @escaping () -> Void) {
        buttonLogic.animate(action)
    }
    
    // MARK: - Exposure Dial Methods
    
    // MARK: - 曝光相关方法代理
    // 注意: 这些方法是为了实现ExposureControlViewModel的重构而添加的
    // 所有曝光相关的方法都会将调用转发到exposureControlVM，以实现功能模块化
    // 同时保持与原有代码的兼容性，未来可以考虑逐步将UI层调用直接改为使用exposureControlVM

    func showExposureDial() {
        exposureControlVM.showExposureDial()
    }
    
    func updateExposure(_ value: Double) {
        exposureControlVM.updateExposure(value)
    }
    
    func updateExposureDialRotation(_ angle: Double) {
        exposureControlVM.updateExposureDialRotation(angle)
    }
    
    func showTemperatureDial() {
        exposureControlVM.showTemperatureDial()
    }
    
    func updateTemperature(_ value: Double) {
        exposureControlVM.updateTemperature(value)
    }
    
    func updateTemperatureDialRotation(_ angle: Double) {
        exposureControlVM.updateTemperatureDialRotation(angle)
    }
    
    func showShutterDial() {
        exposureControlVM.showShutterDial()
    }
    
    func updateShutter(_ value: Double) {
        exposureControlVM.updateShutter(value)
    }
    
    // MARK: - 曝光相关计时器方法代理
    
    func showShutterDialWithTimer() {
        exposureControlVM.showShutterDialWithTimer()
    }
    
    func showISODialWithTimer() {
        exposureControlVM.showISODialWithTimer()
    }
    
    func showExposureDialWithTimer() {
        exposureControlVM.showExposureDialWithTimer()
    }
    
    func showFocusDialWithTimer() {
        exposureControlVM.showFocusDialWithTimer()
    }
    
    func showTemperatureDialWithTimer() {
        exposureControlVM.showTemperatureDialWithTimer()
    }
    
    func showTintDialWithTimer() {
        exposureControlVM.showTintDialWithTimer()
    }
    
    // MARK: - Public Interface Methods
    
    // 处理参数图标点击
    func handleParameterIconTap(option: String) {
        eventHandler.handleIconButtonTap(option: option)
    }

    // 格式化镜头值
    func formatLensValue(lens: String, isSelected: Bool, currentZoomFactor: CGFloat) -> String {
        eventHandler.formatLensValue(lens: lens, isSelected: isSelected, currentZoomFactor: currentZoomFactor)
    }
    
    // 是否应该显示预览按钮
    func shouldShowPreviewButton() -> Bool {
        eventHandler.shouldShowPreviewButton()
    }
    
    // 是否应该显示滤镜按钮
    func shouldShowFilterButton() -> Bool {
        eventHandler.shouldShowFilterButton()
    }
    
    // 是否应该显示左侧按钮
    func shouldShowLeftButton() -> Bool {
        eventHandler.shouldShowLeftButton()
    }
    
    // 是否应该显示右侧按钮
    func shouldShowRightButton() -> Bool {
        eventHandler.shouldShowRightButton()
    }
    
    // 是否应该显示视频模式控制
    func shouldShowVideoModeControls() -> Bool {
        eventHandler.shouldShowVideoModeControls()
    }
    
    // 是否应该显示相机切换按钮
    func shouldShowCameraSwitchButton() -> Bool {
        eventHandler.shouldShowCameraSwitchButton()
    }
    
    // 获取录制背景颜色
    func getRecordingBackgroundColor() -> Color {
        eventHandler.getRecordingBackgroundColor()
    }
    
    // 处理触摸开始
    func handleTouchBeganForDial() {
        exposureControlVM.handleTouchBeganForDial()
    }
    
    // 处理触摸结束
    func handleTouchEndedForDial() {
        exposureControlVM.handleTouchEndedForDial()
    }

    // MARK: - Histogram Methods

    /// 获取直方图数据
    /// - Returns: 直方图数据数组，包含256个元素，表示RGB通道的直方图
    func getHistogramData() -> [UInt8]? {
        // 这里我们返回一个模拟的直方图数据
        // 在实际应用中，应该从相机或图像中生成真实的直方图数据
        var mockHistogramData = [UInt8](repeating: 0, count: 256)
        
        // 生成随机的直方图数据供测试
        for i in 0..<256 {
            // 创建一个类似钟形曲线的分布
            let value = min(255, max(0, Int(128 * exp(-pow(Double(i - 128) / 50, 2)))))
            mockHistogramData[i] = UInt8(value)
        }
        
        return mockHistogramData
    }

    // 选择定时器模式
    func selectTimerMode(_ mode: TimerMode) {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.timerMode = mode
            // 设置定时器状态
            settingsVM.isTimerEnabled = (mode != .off)
            
            // 选择后自动隐藏选项面板
            state.isTimerOptionsVisible = false
            
            // 重置左侧按钮计时器
            resetLeftButtonTimer()
        }
    }

    // MARK: - 格式化属性（为符合MVVM添加）
    
    // 格式化后的曝光值
    var formattedExposureValue: String {
        return ExposureDialUtils.formatValue(state.exposureValue)
    }
    
    // 格式化后的对焦距离
    var formattedFocusValue: String {
        return FocusDialUtils.formatFocusValue(state.focusDistance)
    }
    
    // 格式化后的色温值
    var formattedTemperatureValue: String {
        return String(format: "%.0fK", state.temperatureValue)
    }
    
    // 格式化后的色调值
    var formattedTintValue: String {
        return TintDialUtils.formatValue(state.tintValue)
    }
    
    // 格式化后的ISO值
    var formattedISOValue: String {
        return String(format: "%.0f", state.isoValue)
    }

    // MARK: - Computed UI Logic Properties

    /// 是否应该显示镜头选择器 (复制自 CameraView lensButtons)
    var shouldShowLensSelector: Bool {
        // 复制过来的逻辑
        !state.isLeftButtonExpanded && !state.isRightButtonExpanded
    }

    /// 是否可以显示顶栏次要控件（如切换相机按钮） (复制自 TopBarView 条件)
    var canShowSecondaryTopBarControls: Bool {
        // 复制过来的逻辑
        !state.isParameterExpanded && !state.isRecording
    }

    /// 是否正在录制视频（视频模式且正在录制） (复制自快门按钮条件)
    var isVideoRecording: Bool {
        // 复制过来的逻辑
        state.isVideoMode && state.isRecording
    }

    /// 是否可以编辑或使用其他录制时禁用的功能 (复制自编辑按钮条件)
    var canEditOrModify: Bool {
        // 复制过来的逻辑
        !state.isRecording
    }

    // 可以继续在这里添加其他封装复杂条件的计算属性
    // var shouldShowCameraSwitch: Bool { ... }

    // 隐藏所有底部选项面板
    private func hideAllBottomOptions() {
        state.isPhotoModeOptionsVisible = false
        state.isFlipOptionsVisible = false
        state.isStabilizationOptionsVisible = false
        state.isTimerOptionsVisible = false
    }

    func updateShutter(_ value: Double) {
        exposureControlVM.updateShutter(value)
    }

    func showFocusDial() {
        exposureControlVM.showFocusDial()
    }

    func updateFocus(_ value: Double) {
        exposureControlVM.updateFocus(value)
    }

    func updateFocusDialRotation(_ angle: Double) {
        exposureControlVM.updateFocusDialRotation(angle)
    }

    func updateTint(_ value: Double) {
        exposureControlVM.updateTint(value)
    }
}

extension CameraViewModel {
    func updateUIView(_ uiView: UIView, context: UIViewRepresentableContext<CameraPreviewView>) {
        if let previewLayer = uiView.layer.sublayers?.first as? AVCaptureVideoPreviewLayer {
            previewLayer.frame = uiView.bounds
        }
    }
} 
