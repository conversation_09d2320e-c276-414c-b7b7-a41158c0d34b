import Foundation
import AVFoundation
import Combine
import UIKit
import SwiftUI

class CameraControlViewModel: ObservableObject {
    // MARK: - Published State
    @Published var state: CameraState
    
    // MARK: - 服务依赖
    internal var cameraService: CameraServiceProtocol
    internal var recordingService: RecordingServiceProtocol
    internal var animationVM: CameraAnimationViewModel
    internal var buttonLogic: ButtonControlLogic
    internal var cameraBasicManager: CameraBasicManager
    internal var settingsVM: CameraSettingsViewModel
    internal var eventHandler: CameraEventHandler
    internal var settingsStorageService: CameraSettingsStorageService
    
    // MARK: - 初始化
    init(
        state: CameraState,
        cameraService: CameraServiceProtocol,
        recordingService: RecordingServiceProtocol,
        animationVM: CameraAnimationViewModel,
        buttonLogic: ButtonControlLogic,
        settingsVM: CameraSettingsViewModel,
        eventHandler: CameraEventHandler,
        cameraBasicManager: CameraBasicManager = CameraBasicManager.shared,
        settingsStorageService: CameraSettingsStorageService = CameraSettingsStorageService.shared
    ) {
        self.state = state
        self.cameraService = cameraService
        self.recordingService = recordingService
        self.animationVM = animationVM
        self.buttonLogic = buttonLogic
        self.settingsVM = settingsVM
        self.eventHandler = eventHandler
        self.cameraBasicManager = cameraBasicManager
        self.settingsStorageService = settingsStorageService
    }
    
    // MARK: - Public Methods
    
    func toggleFlash() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            switch state.flashMode {
            case .off:
                state.flashMode = .on
            case .on:
                state.flashMode = .auto
            case .auto:
                state.flashMode = .off
            }
        }
        // 这里可以添加实际的相机闪光灯设置代码
        print("📸 闪光灯状态: \(state.flashMode)")
    }
    
    func switchCamera() {
        // 如果正在录制或处理中，不允许切换
        if state.isRecording {
            return
        }
        
        // 切换摄像头
        let newPosition: AVCaptureDevice.Position = state.currentPosition == .back ? .front : .back
        
        // 执行相机切换动画
        animationVM.animateCameraSwitch {
            // 动画完成后的回调，可以放置任何需要在动画完成后执行的代码
        }
        
        // 执行相机切换，不传参数
        cameraService.switchCamera()
        
        // 更新currentPosition，因为switchCamera方法不接受参数
        state.currentPosition = newPosition
    }
    
    func capturePhoto() {
        cameraService.capturePhoto()
    }
    
    func getCurrentDevice() -> AVCaptureDevice? {
        cameraService.getCurrentDevice()
    }
    
    // MARK: - UI State Methods
    
    func toggleVideoMode() {
        state.isVideoMode.toggle()
        
        // 保存到持久化存储
        settingsStorageService.updateVideoMode(state.isVideoMode)
        
        // 更新UI和相机设置
        buttonLogic.setVideoMode(state.isVideoMode)  // 使用setVideoMode代替不存在的resetOptions
        state.isDialVisible = false  // 隐藏刻度盘
        state.isParameterExpanded = false  // 折叠参数面板
        
        // 如果在录制中，停止录制
        if state.isRecording {
            toggleRecording()  // 使用toggleRecording代替不存在的stopRecording
        }
    }
    
    func toggleLivePhoto() {
        withAnimation(.spring(response: AnimationConstants.quickDuration, dampingFraction: AnimationConstants.quickDampingFraction)) {
            state.isLivePhotoEnabled.toggle()
            
            // 如果开启了Live Photo而HDR也是开启状态，则自动关闭HDR
            if state.isLivePhotoEnabled && settingsVM.isHDREnabled {
                settingsVM.isHDREnabled = false
            }
        }
        // 这里可以添加实际的相机Live Photo设置代码
        print("📸 Live Photo状态: \(state.isLivePhotoEnabled ? "开启" : "关闭")")
    }
    
    func toggleRecording() {
        Task {
            do {
                try await recordingService.toggleRecording()
            } catch {
                print("Recording toggle failed: \(error)")
            }
        }
    }
    
    func getParameterFlashIcon() -> String {
        switch state.flashMode {
        case .on:
            return "bolt.fill"
        case .auto:
            return "bolt.badge.a.fill"
        case .off:
            return "bolt.slash.fill"
        }
    }
    
    func handleShutterButtonPress() {
        // 如果参数面板展开，先收起
        if state.isParameterExpanded {
            withAnimation(getQuickSpringAnimation()) {
                eventHandler.collapseParameter()
            }
        }
        
        // 根据模式处理拍摄/录制
        if state.isVideoMode {
            eventHandler.toggleRecording()
        } else {
            eventHandler.capturePhoto()
        }
    }
    
    // MARK: - 辅助方法
    
    /// 获取快速弹簧动画
    /// - Returns: 配置好的快速弹簧动画
    func getQuickSpringAnimation() -> Animation {
        return .spring(response: AnimationConstants.quickDuration, 
                     dampingFraction: AnimationConstants.quickDampingFraction)
    }
} 