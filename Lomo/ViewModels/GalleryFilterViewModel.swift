// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI
import Combine

/// 相册滤镜视图模型，处理滤镜显示和交互逻辑 - MVVM-S架构
class GalleryFilterViewModel: ObservableObject {
    /// 滤镜服务
    private let filterService: GalleryFilterServiceProtocol
    
    /// 当前选中的滤镜类别
    @Published var selectedCategory: FilterCategory = .film
    
    /// 当前类别下的滤镜列表
    @Published var filters: [Filter] = []
    
    /// 是否有选中的滤镜
    @Published var hasSelectedFilter: Bool = false
    
    /// 当前选中的滤镜
    @Published var selectedFilter: Filter?
    
    // MARK: - 核心展示状态（移除了预设相关状态，专注于滤镜展示功能）
    
    /// 初始化方法
    /// - Parameter filterService: 滤镜服务
    init(filterService: GalleryFilterServiceProtocol = GalleryFilterDependencyContainer.shared.galleryFilterService) {
        self.filterService = filterService
        
        // 初始加载胶片类别的滤镜
        loadFilters(for: .film)
    }
    
    /// 根据类别加载滤镜
    /// - Parameter category: 滤镜类别
    func loadFilters(for category: FilterCategory) {
        selectedCategory = category
        
        // 根据不同类别加载滤镜
        switch category {
        case .favorites:
            // 加载收藏的滤镜
            filters = filterService.getFavoriteFilters()
        case .film, .polaroid, .nature, .fresh, .vintage, .blackAndWhite, .custom:
            // 加载指定类型的滤镜
            if let type = category.filterType {
                filters = filterService.getFilters(byType: type)
            }
        }
    }
    
    /// 选择滤镜
    /// - Parameter filter: 要选择的滤镜
    func selectFilter(_ filter: Filter) {
        selectedFilter = filter
        hasSelectedFilter = true
    }
    
    /// 取消选择滤镜
    func deselectFilter() {
        selectedFilter = nil
        hasSelectedFilter = false
    }
    
    /// 切换滤镜收藏状态
    /// - Parameter filterId: 滤镜ID
    func toggleFavorite(filterId: String) {
        let isFavorite = filterService.toggleFavorite(filterId: filterId)
        
        // 如果当前在收藏类别中，需要更新列表
        if selectedCategory == .favorites {
            loadFilters(for: .favorites)
        }
        
        // 如果选中的滤镜被切换了收藏状态，更新选中滤镜的状态
        if let selectedFilter = selectedFilter, selectedFilter.id == filterId {
            var updatedFilter = selectedFilter
            updatedFilter.isFavorite = isFavorite
            self.selectedFilter = updatedFilter
        }
    }
    
    // MARK: - 核心展示功能（专注于滤镜展示，移除了预设相关功能）
    // 注意：预设相关功能属于Edit模块的应用层，不应在展示层实现
}