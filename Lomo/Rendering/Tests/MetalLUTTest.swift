import Foundation
import UIKit
import Metal

/// Metal LUT处理器测试
class MetalLUTTest {
    
    /// 测试Metal LUT处理器初始化
    static func testInitialization() {
        print("🧪 [MetalLUTTest] 开始测试Metal LUT处理器初始化...")
        
        do {
            let metalLUT = try MetalLUTProcessor()
            print("✅ [MetalLUTTest] Metal LUT处理器初始化成功")
            
            let cacheInfo = metalLUT.getCacheInfo()
            print("📊 [MetalLUTTest] 缓存信息: \(cacheInfo)")
            
        } catch {
            print("❌ [MetalLUTTest] Metal LUT处理器初始化失败: \(error)")
        }
    }
    
    /// 测试Metal设备支持
    static func testMetalSupport() {
        print("🧪 [MetalLUTTest] 检查Metal设备支持...")
        
        if let device = MTLCreateSystemDefaultDevice() {
            print("✅ [MetalLUTTest] Metal设备可用: \(device.name)")
            print("📱 [MetalLUTTest] 支持的功能集: \(device.supportsFeatureSet(.iOS_GPUFamily1_v1))")
            
            // 检查3D纹理支持
            let descriptor = MTLTextureDescriptor()
            descriptor.textureType = .type3D
            descriptor.pixelFormat = .rgba8Unorm
            descriptor.width = 64
            descriptor.height = 64
            descriptor.depth = 64
            descriptor.usage = [.shaderRead]
            
            if let texture = device.makeTexture(descriptor: descriptor) {
                print("✅ [MetalLUTTest] 3D纹理支持: 64x64x64")
                print("📏 [MetalLUTTest] 纹理尺寸: \(texture.width)x\(texture.height)x\(texture.depth)")
            } else {
                print("❌ [MetalLUTTest] 3D纹理不支持")
            }
            
        } else {
            print("❌ [MetalLUTTest] Metal设备不可用")
        }
    }
    
    /// 测试Metal着色器编译
    static func testShaderCompilation() {
        print("🧪 [MetalLUTTest] 测试Metal着色器编译...")
        
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("❌ [MetalLUTTest] Metal设备不可用")
            return
        }
        
        guard let library = device.makeDefaultLibrary() else {
            print("❌ [MetalLUTTest] 无法加载Metal库")
            return
        }
        
        // 测试各种LUT着色器函数
        let shaderFunctions = [
            "apply_3d_lut_filter",
            "apply_2d_lut_filter", 
            "apply_cube_lut_filter",
            "apply_high_quality_lut_filter",
            "apply_realtime_lut_filter",
            "apply_simple_lut_filter"
        ]
        
        for functionName in shaderFunctions {
            if let function = library.makeFunction(name: functionName) {
                print("✅ [MetalLUTTest] 着色器函数可用: \(functionName)")
                
                // 尝试创建计算管线状态
                do {
                    let pipelineState = try device.makeComputePipelineState(function: function)
                    print("✅ [MetalLUTTest] 管线状态创建成功: \(functionName)")
                } catch {
                    print("❌ [MetalLUTTest] 管线状态创建失败: \(functionName) - \(error)")
                }
            } else {
                print("❌ [MetalLUTTest] 着色器函数不可用: \(functionName)")
            }
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        TestUtils.printTestStart("MetalLUTTest")

        testMetalSupport()
        TestUtils.printTestGroup("着色器编译")

        testShaderCompilation()
        TestUtils.printTestGroup("初始化测试")

        testInitialization()

        TestUtils.printTestEnd("MetalLUTTest")
    }
}
