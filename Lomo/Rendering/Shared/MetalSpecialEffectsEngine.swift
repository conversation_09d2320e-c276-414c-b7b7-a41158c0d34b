import Foundation
import UIKit
import Metal
import MetalKit

/// Metal特殊效果引擎 - 处理漏光、颗粒、划痕等效果
class MetalSpecialEffectsEngine {
    
    // MARK: - 属性
    
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    
    // 管线状态缓存
    private var pipelineStates: [String: MTLComputePipelineState] = [:]
    
    // 纹理缓存
    private var textureCache: [String: MTLTexture] = [:]
    
    // MARK: - 初始化
    
    init() throws {
        print("🔍 [MetalSpecialEffectsEngine] 开始初始化...")

        guard let device = MTLCreateSystemDefaultDevice() else {
            print("❌ [MetalSpecialEffectsEngine] Metal设备不支持")
            throw MetalSpecialEffectsError.deviceNotSupported
        }
        print("✅ [MetalSpecialEffectsEngine] Metal设备创建成功: \(device.name)")

        self.device = device

        guard let commandQueue = device.makeCommandQueue() else {
            print("❌ [MetalSpecialEffectsEngine] 命令队列创建失败")
            throw MetalSpecialEffectsError.commandQueueCreationFailed
        }
        print("✅ [MetalSpecialEffectsEngine] 命令队列创建成功")

        self.commandQueue = commandQueue

        guard let library = device.makeDefaultLibrary() else {
            print("❌ [MetalSpecialEffectsEngine] 默认库未找到")
            throw MetalSpecialEffectsError.libraryNotFound
        }
        print("✅ [MetalSpecialEffectsEngine] 默认库加载成功")

        self.library = library

        print("🎨 [MetalSpecialEffectsEngine] 初始化完成")
    }
    
    // MARK: - 漏光效果
    
    /// 应用漏光效果
    /// - Parameters:
    ///   - image: 输入图像
    ///   - leakImage: 漏光纹理图像
    ///   - parameters: 漏光参数
    /// - Returns: 处理后的图像
    func applyLightLeak(to image: UIImage, 
                       leakImage: UIImage, 
                       parameters: LightLeakParameters) throws -> UIImage {
        
        print("🎨 [MetalSpecialEffectsEngine] 开始应用漏光效果")
        
        // 创建纹理
        let inputTexture = try createTexture(from: image)
        let leakTexture = try createTexture(from: leakImage)
        let outputTexture = try createOutputTexture(size: image.size)
        
        // 创建参数
        var effectParams = SpecialEffectsParameters(
            intensity: Float(parameters.intensity),
            position: SIMD2<Float>(Float(parameters.positionOffset.x), Float(parameters.positionOffset.y)),
            rotation: Float(parameters.rotation),
            scale: 1.0, // LightLeakParameters没有scale属性，使用默认值
            contrast: 1.0,
            size: 1.0,
            density: 1.0,
            padding: 0.0
        )
        
        // 执行着色器
        try executeShader(
            functionName: "apply_light_leak_effect",
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            additionalTextures: [leakTexture],
            parameters: &effectParams
        )
        
        return try createUIImage(from: outputTexture)
    }
    
    // MARK: - 胶片颗粒效果
    
    /// 应用胶片颗粒效果
    /// - Parameters:
    ///   - image: 输入图像
    ///   - parameters: 颗粒参数
    /// - Returns: 处理后的图像
    func applyGrain(to image: UIImage, 
                   parameters: GrainParameters) throws -> UIImage {
        
        print("🎨 [MetalSpecialEffectsEngine] 开始应用颗粒效果")
        
        guard let preset = parameters.selectedPreset else {
            return image
        }
        
        // 创建纹理
        let inputTexture = try createTexture(from: image)
        let outputTexture = try createOutputTexture(size: image.size)
        
        // 创建参数
        var effectParams = SpecialEffectsParameters(
            intensity: Float(parameters.intensity),
            position: SIMD2<Float>(0, 0),
            rotation: 0,
            scale: 1.0,
            contrast: Float(preset.contrast),
            size: Float(preset.size),
            density: 1.0,
            padding: 0.0
        )
        
        // 执行着色器
        try executeShader(
            functionName: "apply_grain_effect",
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            parameters: &effectParams
        )
        
        return try createUIImage(from: outputTexture)
    }
    
    // MARK: - 划痕效果
    
    /// 应用划痕效果
    /// - Parameters:
    ///   - image: 输入图像
    ///   - parameters: 划痕参数
    /// - Returns: 处理后的图像
    func applyScratch(to image: UIImage, 
                     parameters: ScratchParameters) throws -> UIImage {
        
        print("🎨 [MetalSpecialEffectsEngine] 开始应用划痕效果")
        
        guard let preset = parameters.selectedPreset else {
            return image
        }
        
        // 创建纹理
        let inputTexture = try createTexture(from: image)
        let outputTexture = try createOutputTexture(size: image.size)
        
        // 创建参数
        var effectParams = SpecialEffectsParameters(
            intensity: Float(parameters.intensity),
            position: SIMD2<Float>(0, 0),
            rotation: 0,
            scale: 1.0,
            contrast: 1.0,
            size: Float(preset.width),
            density: Float(preset.density),
            padding: 0.0
        )
        
        // 执行着色器
        try executeShader(
            functionName: "apply_scratch_effect",
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            parameters: &effectParams
        )
        
        return try createUIImage(from: outputTexture)
    }
    
    // MARK: - 高斯模糊效果
    
    /// 应用高斯模糊效果
    /// - Parameters:
    ///   - image: 输入图像
    ///   - radius: 模糊半径
    /// - Returns: 处理后的图像
    func applyGaussianBlur(to image: UIImage, radius: Float) throws -> UIImage {

        print("🎨 [MetalSpecialEffectsEngine] 开始应用高斯模糊")
        print("   - 输入图像尺寸: \(image.size)")
        print("   - 模糊半径: \(radius)")

        // 创建纹理
        print("🔍 [MetalSpecialEffectsEngine] 创建输入纹理...")
        let inputTexture = try createTexture(from: image)
        print("✅ [MetalSpecialEffectsEngine] 输入纹理创建成功: \(inputTexture.width)x\(inputTexture.height)")

        print("🔍 [MetalSpecialEffectsEngine] 创建输出纹理...")
        let outputTexture = try createOutputTexture(size: image.size)
        print("✅ [MetalSpecialEffectsEngine] 输出纹理创建成功: \(outputTexture.width)x\(outputTexture.height)")

        // 执行着色器
        print("🔍 [MetalSpecialEffectsEngine] 执行模糊着色器...")
        try executeBlurShader(
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            radius: radius
        )
        print("✅ [MetalSpecialEffectsEngine] 模糊着色器执行成功")

        print("🔍 [MetalSpecialEffectsEngine] 创建输出图像...")
        let result = try createUIImage(from: outputTexture)
        print("✅ [MetalSpecialEffectsEngine] 高斯模糊完成，输出图像尺寸: \(result.size)")
        return result
    }
    
    // MARK: - 溶解过渡效果
    
    /// 应用溶解过渡效果
    /// - Parameters:
    ///   - fromImage: 起始图像
    ///   - toImage: 目标图像
    ///   - progress: 过渡进度 (0.0-1.0)
    /// - Returns: 过渡图像
    func applyDissolveTransition(from fromImage: UIImage, 
                                to toImage: UIImage, 
                                progress: Float) throws -> UIImage {
        
        print("🎨 [MetalSpecialEffectsEngine] 开始应用溶解过渡")
        
        // 创建纹理
        let fromTexture = try createTexture(from: fromImage)
        let toTexture = try createTexture(from: toImage)
        let outputTexture = try createOutputTexture(size: fromImage.size)
        
        // 执行着色器
        try executeDissolveShader(
            fromTexture: fromTexture,
            toTexture: toTexture,
            outputTexture: outputTexture,
            progress: progress
        )
        
        return try createUIImage(from: outputTexture)
    }
    
    // MARK: - 私有方法
    
    /// 执行通用着色器
    private func executeShader(functionName: String,
                              inputTexture: MTLTexture,
                              outputTexture: MTLTexture,
                              additionalTextures: [MTLTexture] = [],
                              parameters: UnsafeMutableRawPointer) throws {
        
        let pipelineState = try getOrCreatePipelineState(functionName: functionName)
        
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalSpecialEffectsError.commandBufferCreationFailed
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(inputTexture, index: 0)
        encoder.setTexture(outputTexture, index: 1)
        
        // 设置额外纹理
        for (index, texture) in additionalTextures.enumerated() {
            encoder.setTexture(texture, index: 2 + index)
        }
        
        // 设置参数
        encoder.setBytes(parameters, length: MemoryLayout<SpecialEffectsParameters>.size, index: 0)
        
        // 计算线程组
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (inputTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            throw MetalSpecialEffectsError.processingFailed
        }
    }
    
    /// 执行模糊着色器
    private func executeBlurShader(inputTexture: MTLTexture,
                                  outputTexture: MTLTexture,
                                  radius: Float) throws {

        print("🔍 [MetalSpecialEffectsEngine] 获取模糊着色器管线状态...")
        let pipelineState = try getOrCreatePipelineState(functionName: "apply_gaussian_blur")
        print("✅ [MetalSpecialEffectsEngine] 模糊着色器管线状态获取成功")

        print("🔍 [MetalSpecialEffectsEngine] 创建命令缓冲区和编码器...")
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            print("❌ [MetalSpecialEffectsEngine] 命令缓冲区或编码器创建失败")
            throw MetalSpecialEffectsError.commandBufferCreationFailed
        }
        print("✅ [MetalSpecialEffectsEngine] 命令缓冲区和编码器创建成功")
        
        print("🔍 [MetalSpecialEffectsEngine] 设置着色器参数...")
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(inputTexture, index: 0)
        encoder.setTexture(outputTexture, index: 1)

        var blurRadius = radius
        encoder.setBytes(&blurRadius, length: MemoryLayout<Float>.size, index: 0)
        print("✅ [MetalSpecialEffectsEngine] 着色器参数设置完成")

        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (inputTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        print("🔍 [MetalSpecialEffectsEngine] 线程组配置: \(threadgroupCount.width)x\(threadgroupCount.height)")

        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        print("✅ [MetalSpecialEffectsEngine] 着色器调度完成")

        print("🔍 [MetalSpecialEffectsEngine] 提交命令缓冲区...")
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        print("✅ [MetalSpecialEffectsEngine] 命令缓冲区执行完成")

        if let error = commandBuffer.error {
            print("❌ [MetalSpecialEffectsEngine] 命令缓冲区执行错误: \(error)")
            throw MetalSpecialEffectsError.processingFailed
        }
    }
    
    /// 执行溶解着色器
    private func executeDissolveShader(fromTexture: MTLTexture,
                                      toTexture: MTLTexture,
                                      outputTexture: MTLTexture,
                                      progress: Float) throws {
        
        let pipelineState = try getOrCreatePipelineState(functionName: "apply_dissolve_transition")
        
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalSpecialEffectsError.commandBufferCreationFailed
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(fromTexture, index: 0)
        encoder.setTexture(toTexture, index: 1)
        encoder.setTexture(outputTexture, index: 2)
        
        var transitionProgress = progress
        encoder.setBytes(&transitionProgress, length: MemoryLayout<Float>.size, index: 0)
        
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (fromTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (fromTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            throw MetalSpecialEffectsError.processingFailed
        }
    }
    
    /// 获取或创建管线状态
    private func getOrCreatePipelineState(functionName: String) throws -> MTLComputePipelineState {
        if let cached = pipelineStates[functionName] {
            print("✅ [MetalSpecialEffectsEngine] 使用缓存的管线状态: \(functionName)")
            return cached
        }

        print("🔍 [MetalSpecialEffectsEngine] 创建新的管线状态: \(functionName)")
        guard let function = library.makeFunction(name: functionName) else {
            print("❌ [MetalSpecialEffectsEngine] 着色器函数未找到: \(functionName)")
            throw MetalSpecialEffectsError.functionNotFound(functionName)
        }
        print("✅ [MetalSpecialEffectsEngine] 着色器函数找到: \(functionName)")

        let pipelineState = try device.makeComputePipelineState(function: function)
        pipelineStates[functionName] = pipelineState
        print("✅ [MetalSpecialEffectsEngine] 管线状态创建成功: \(functionName)")

        return pipelineState
    }
    
    /// 创建纹理
    private func createTexture(from image: UIImage) throws -> MTLTexture {
        guard let cgImage = image.cgImage else {
            throw MetalSpecialEffectsError.invalidImage
        }
        
        let textureLoader = MTKTextureLoader(device: device)
        let options: [MTKTextureLoader.Option: Any] = [
            .textureUsage: MTLTextureUsage.shaderRead.rawValue,
            .textureStorageMode: MTLStorageMode.private.rawValue
        ]
        
        return try textureLoader.newTexture(cgImage: cgImage, options: options)
    }
    
    /// 创建输出纹理
    private func createOutputTexture(size: CGSize) throws -> MTLTexture {
        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba8Unorm,
            width: Int(size.width),
            height: Int(size.height),
            mipmapped: false
        )
        descriptor.usage = [.shaderWrite, .shaderRead]
        descriptor.storageMode = .private
        
        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalSpecialEffectsError.textureCreationFailed
        }
        
        return texture
    }
    
    /// 创建UIImage
    private func createUIImage(from texture: MTLTexture) throws -> UIImage {
        let ciImage = CIImage(mtlTexture: texture, options: [
            .colorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        guard let outputCIImage = ciImage else {
            throw MetalSpecialEffectsError.textureConversionFailed
        }
        
        let context = CIContext(mtlDevice: device)
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent) else {
            throw MetalSpecialEffectsError.cgImageCreationFailed
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    // MARK: - 缓存管理
    
    /// 清理缓存
    func clearCache() {
        pipelineStates.removeAll()
        textureCache.removeAll()
        print("🗑️ [MetalSpecialEffectsEngine] 缓存已清理")
    }
}

// MARK: - 数据结构

/// 特殊效果参数结构体 (与Metal着色器对应)
struct SpecialEffectsParameters {
    var intensity: Float
    var position: SIMD2<Float>
    var rotation: Float
    var scale: Float
    var contrast: Float
    var size: Float
    var density: Float
    var padding: Float
}

/// Metal特殊效果错误
enum MetalSpecialEffectsError: Error {
    case deviceNotSupported
    case commandQueueCreationFailed
    case libraryNotFound
    case functionNotFound(String)
    case textureCreationFailed
    case commandBufferCreationFailed
    case invalidImage
    case textureConversionFailed
    case cgImageCreationFailed
    case processingFailed
}
