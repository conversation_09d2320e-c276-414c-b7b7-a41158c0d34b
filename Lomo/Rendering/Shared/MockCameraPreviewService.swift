import SwiftUI
import AVFoundation
import Combine

// MARK: - 模拟相机预览服务
/// 此服务用于在模拟器环境中提供类似真机的相机预览效果
/// 通过使用静态图像或视频资源来模拟相机捕获画面
class MockCameraPreviewService: CameraPreviewServiceProtocol {
    let session: AVCaptureSession
    
    init() {
        self.session = AVCaptureSession()
    }
    
    // 根据比例字符串计算宽高比
    private func calculateAspectRatio(_ ratioString: String, isPortrait: Bool = true) -> CGFloat {
        // 计算出的宽高比应该是宽度/高度，但考虑到设备通常是竖屏方向
        // 注意：这里的宽高比是基于传感器的物理尺寸（通常是横屏方向）
        // 但由于手机通常为竖屏状态，需要对不同比例进行调整
        var aspectRatio: CGFloat
        
        // 解析传入的比例字符串
        switch ratioString {
        case "4:3":
            aspectRatio = 4.0 / 3.0  // 横屏状态下的比例
        case "3:2":
            aspectRatio = 3.0 / 2.0
        case "16:9":
            aspectRatio = 16.0 / 9.0
        case "1:1":
            aspectRatio = 1.0
        case "6:7":
            aspectRatio = 6.0 / 7.0
        case "2:1":
            aspectRatio = 2.0 / 1.0
        case "1.85:1":
            aspectRatio = 1.85
        case "2.39:1":
            aspectRatio = 2.39
        case "1.66:1":
            aspectRatio = 1.66
        case "XPAN":
            aspectRatio = 65.0 / 24.0
        default:
            aspectRatio = 4.0 / 3.0  // 默认为4:3
        }
        
        // 如果是竖屏显示，需要返回宽高比的倒数
        return isPortrait ? 1.0 / aspectRatio : aspectRatio;
    }
    
    // 根据容器尺寸和宽高比计算预览尺寸
    private func calculatePreviewSize(containerSize: CGRect, aspectRatio: CGFloat) -> CGRect {
        let containerWidth = containerSize.width
        let containerHeight = containerSize.height
        let containerAspectRatio = containerWidth / containerHeight
        
        var previewWidth: CGFloat
        var previewHeight: CGFloat
        
        if aspectRatio > containerAspectRatio {
            // 预览比例更宽，宽度填满容器
            previewWidth = containerWidth
            previewHeight = containerWidth / aspectRatio
        } else {
            // 预览比例更高，高度填满容器
            previewHeight = containerHeight
            previewWidth = containerHeight * aspectRatio
        }
        
        // 计算居中位置
        let x = (containerWidth - previewWidth) / 2
        let y = (containerHeight - previewHeight) / 2
        
        return CGRect(x: x, y: y, width: previewWidth, height: previewHeight)
    }
    
    // 获取当前相机宽高比设置
    private func getCurrentAspectRatioString() -> String {
        // 尝试从UserDefaults获取设置
        let defaults = UserDefaults.standard
        if let settings = defaults.object(forKey: "CameraBasicSettings") as? [String: Any],
           let photoRatioMode = settings["photoRatioMode"] as? String {
            return photoRatioMode
        }
        
        // 如果无法获取，返回默认值16:9
        return "16:9"
    }
    
    func configurePreviewLayer(for view: UIView, customFrame: CGRect? = nil) {
        // 清除之前的视图
        view.subviews.forEach { $0.removeFromSuperview() }
        
        // 使用完整的屏幕边界作为容器
        let containerFrame = UIScreen.main.bounds
        
        // 创建一个黑色背景的容器视图
        let containerView = UIView(frame: containerFrame)
        containerView.backgroundColor = .black
        containerView.tag = 100 // 添加标签便于后续查找
        view.addSubview(containerView)
        
        // 获取当前相机宽高比设置
        let aspectRatioString = getCurrentAspectRatioString()
        let aspectRatio = calculateAspectRatio(aspectRatioString)
        
        // 使用自定义frame或计算出具有正确宽高比的frame
        let previewFrame: CGRect
        if let customFrame = customFrame {
            // 如果有自定义frame，在其中居中显示正确宽高比的内容
            previewFrame = calculatePreviewSize(containerSize: customFrame, aspectRatio: aspectRatio)
        } else {
            // 如果没有自定义frame，使用全屏并计算正确宽高比
            previewFrame = calculatePreviewSize(containerSize: containerFrame, aspectRatio: aspectRatio)
        }
        
        // 创建预览视图
        let previewView = UIView(frame: previewFrame)
        previewView.tag = 101 // 添加标签
        containerView.addSubview(previewView)
        
        // 创建渐变层
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = previewView.bounds
        gradientLayer.colors = [
            UIColor.systemPink.cgColor,
            UIColor.purple.cgColor,
            UIColor.systemBlue.cgColor
        ]
        gradientLayer.locations = [0.0, 0.5, 1.0]
        gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
        gradientLayer.endPoint = CGPoint(x: 1.0, y: 1.0)
        
        // 添加渐变层
        previewView.layer.addSublayer(gradientLayer)
    }
    
    // 符合协议的方法
    func updatePreviewLayer(for view: UIView, customFrame: CGRect?) {
        // 调用扩展版本的实现，使用默认动画参数
        updatePreviewLayer(for: view, customFrame: customFrame, animationDuration: AnimationConstants.duration, animationCurve: UIView.AnimationOptions.curveEaseInOut.rawValue)
    }
    
    // 扩展版本的方法，提供额外的动画参数
    func updatePreviewLayer(for view: UIView, customFrame: CGRect? = nil, animationDuration: TimeInterval = AnimationConstants.duration, animationCurve: UInt = UIView.AnimationOptions.curveEaseInOut.rawValue) {
        // 获取容器视图
        guard let containerView = view.viewWithTag(100) else {
            // 如果容器视图不存在，重新创建
            configurePreviewLayer(for: view, customFrame: customFrame)
            return
        }
        
        // 使用完整的屏幕边界
        let containerFrame = UIScreen.main.bounds
        
        // 使用传入的动画参数更新容器视图
        UIView.animate(withDuration: animationDuration, delay: 0, options: UIView.AnimationOptions(rawValue: animationCurve)) {
            containerView.frame = containerFrame
        }
        
        // 获取当前相机宽高比设置
        let aspectRatioString = getCurrentAspectRatioString()
        let aspectRatio = calculateAspectRatio(aspectRatioString)
        
        // 使用自定义frame或计算出具有正确宽高比的frame
        let previewFrame: CGRect
        if let customFrame = customFrame {
            // 如果有自定义frame，在其中居中显示正确宽高比的内容
            previewFrame = calculatePreviewSize(containerSize: customFrame, aspectRatio: aspectRatio)
        } else {
            // 如果没有自定义frame，使用全屏并计算正确宽高比
            previewFrame = calculatePreviewSize(containerSize: containerFrame, aspectRatio: aspectRatio)
        }
        
        // 更新预览视图尺寸
        if let previewView = containerView.viewWithTag(101) {
            UIView.animate(withDuration: animationDuration, delay: 0, options: UIView.AnimationOptions(rawValue: animationCurve)) {
                previewView.frame = previewFrame
                
                // 更新渐变层尺寸
                if let gradientLayer = previewView.layer.sublayers?.first as? CAGradientLayer {
                    CATransaction.begin()
                    CATransaction.setAnimationDuration(animationDuration)
                    
                    // 设置CATransaction的动画定时函数
                    let mediaTimingFunction: CAMediaTimingFunction
                    switch UIView.AnimationOptions(rawValue: animationCurve) {
                    case .curveEaseIn:
                        mediaTimingFunction = CAMediaTimingFunction(name: .easeIn)
                    case .curveEaseOut:
                        mediaTimingFunction = CAMediaTimingFunction(name: .easeOut)
                    case .curveEaseInOut:
                        mediaTimingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                    case .curveLinear:
                        mediaTimingFunction = CAMediaTimingFunction(name: .linear)
                    default:
                        mediaTimingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
                    }
                    CATransaction.setAnimationTimingFunction(mediaTimingFunction)
                    
                    gradientLayer.frame = previewView.bounds
                    CATransaction.commit()
                }
            }
        } else {
            // 如果预览视图不存在，重新创建
            let previewView = UIView(frame: previewFrame)
            previewView.tag = 101
            containerView.addSubview(previewView)
            
            // 创建渐变层
            let gradientLayer = CAGradientLayer()
            gradientLayer.frame = previewView.bounds
            gradientLayer.colors = [
                UIColor.systemPink.cgColor,
                UIColor.purple.cgColor,
                UIColor.systemBlue.cgColor
            ]
            gradientLayer.locations = [0.0, 0.5, 1.0]
            gradientLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
            gradientLayer.endPoint = CGPoint(x: 1.0, y: 1.0)
            
            // 添加渐变层
            previewView.layer.addSublayer(gradientLayer)
        }
    }
    
    func updatePreviewLayerPosition(for view: UIView, dragOffset: CGFloat) {
        // 获取容器视图
        guard let containerView = view.viewWithTag(100) else { return }
        
        // 更新容器视图位置
        var frame = containerView.frame
            frame.origin.y = dragOffset
        containerView.frame = frame
    }
} 