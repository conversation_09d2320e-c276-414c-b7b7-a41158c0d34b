import SwiftUI
import AVFoundation

class CameraPreviewService: CameraPreviewServiceProtocol {
    let session: AVCaptureSession
    
    init(session: AVCaptureSession) {
        self.session = session
    }
    
    func configurePreviewLayer(for view: UIView, customFrame: CGRect? = nil) {
        let previewLayer = AVCaptureVideoPreviewLayer(session: session)
        
        // 设置背景色为黑色
        view.backgroundColor = .black
        previewLayer.backgroundColor = UIColor.black.cgColor
        
        // 设置预览层 - 使用自定义尺寸或屏幕bounds
        let frame = customFrame ?? UIScreen.main.bounds
        previewLayer.frame = frame
        print("📱 预览层初始化尺寸: \(previewLayer.frame)")
        previewLayer.videoGravity = .resizeAspectFill
        
        // 添加自动布局支持
        previewLayer.masksToBounds = true
        
        // 设置视图的自动布局
        view.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        
        view.layer.addSublayer(previewLayer)
    }
    
    func updatePreviewLayer(for view: UIView, customFrame: CGRect? = nil) {
        // 调用带动画参数的方法，使用默认动画值
        updatePreviewLayer(for: view, customFrame: customFrame, animationDuration: 0.25, animationCurve: UIView.AnimationOptions.curveEaseInOut.rawValue)
    }
    
    func updatePreviewLayer(for view: UIView, customFrame: CGRect?, animationDuration: TimeInterval, animationCurve: UInt) {
        guard let previewLayer = view.layer.sublayers?.first as? AVCaptureVideoPreviewLayer else { return }
        
        let oldFrame = previewLayer.frame
        let newFrame = customFrame ?? UIScreen.main.bounds
        
        // 如果有动画参数且时长大于0，则使用动画
        if animationDuration > 0 {
            // 创建CABasicAnimation为Layer添加隐式动画
            let animationTiming = CAMediaTimingFunction(name: Self.getTimingFunctionName(from: animationCurve))
            
            // 对UIView进行动画
            UIView.animate(withDuration: animationDuration, delay: 0, options: UIView.AnimationOptions(rawValue: animationCurve), animations: {
                // 更新预览层frame
                previewLayer.frame = newFrame
            })
            
            // 为确保CALayer动画同步，设置动画参数
            CATransaction.begin()
            CATransaction.setAnimationDuration(animationDuration)
            CATransaction.setAnimationTimingFunction(animationTiming)
            CATransaction.commit()
        } else {
            // 如果没有动画，直接更新
            previewLayer.frame = newFrame
        }
        
        print("📱 预览层更新 - 旧尺寸: \(oldFrame), 新尺寸: \(previewLayer.frame), 动画时长: \(animationDuration)")
    }
    
    // 根据下拉偏移量更新预览层位置
    func updatePreviewLayerPosition(for view: UIView, dragOffset: CGFloat) {
        if let previewLayer = view.layer.sublayers?.first as? AVCaptureVideoPreviewLayer {
            // 创建新的frame，保持大小但更新位置
            var newFrame = previewLayer.frame
            newFrame.origin.y = dragOffset
            previewLayer.frame = newFrame
            
            print("📱 预览层位置更新 - 偏移量: \(dragOffset), 新位置: \(newFrame.origin)")
        }
    }
    
    // 将UIView.AnimationOptions转换为CAMediaTimingFunction
    private static func getTimingFunctionName(from curve: UInt) -> CAMediaTimingFunctionName {
        switch curve {
        case UIView.AnimationOptions.curveEaseIn.rawValue:
            return .easeIn
        case UIView.AnimationOptions.curveEaseOut.rawValue:
            return .easeOut
        case UIView.AnimationOptions.curveEaseInOut.rawValue:
            return .default
        case UIView.AnimationOptions.curveLinear.rawValue:
            return .linear
        default:
            return .default
        }
    }
} 