import UIKit
import SwiftUI
import Metal

/// 图像渲染服务 - 提供图像渲染和过渡动画相关功能
class ImageRenderingService {
    // MARK: - 单例
    static let shared = ImageRenderingService()
    
    // MARK: - 初始化
    private init() {}
    
    // MARK: - 常量
    /// 图像容器视图的标签
    private let imageHostViewTag = 123
    
    // MARK: - 属性
    private var dissolveContexts: [UIView: DissolveContext] = [:]
    private lazy var metalEngine: MetalSpecialEffectsEngine? = {
        do {
            return try MetalSpecialEffectsEngine()
        } catch {
            print("❌ [ImageRenderingService] Metal引擎初始化失败: \(error)")
            return nil
        }
    }()
    
    // MARK: - 溶解上下文
    private class DissolveContext {
        let startTime: CFTimeInterval
        let duration: TimeInterval
        let oldImage: UIImage?
        let newImage: UIImage
        var transitionImageView: UIImageView
        var displayLink: CADisplayLink
        let completion: () -> Void
        
        init(startTime: CFTimeInterval, duration: TimeInterval, oldImage: UIImage?, newImage: UIImage, transitionImageView: UIImageView, displayLink: CADisplayLink, completion: @escaping () -> Void) {
            self.startTime = startTime
            self.duration = duration
            self.oldImage = oldImage
            self.newImage = newImage
            self.transitionImageView = transitionImageView
            self.displayLink = displayLink
            self.completion = completion
        }
    }
    
    // MARK: - 图像渲染方法
    
    /// 在指定容器中显示图片
    /// - Parameters:
    ///   - image: 要显示的图片
    ///   - parentView: 父容器视图
    ///   - animationDuration: 动画持续时间
    ///   - animationCurve: 动画曲线
    ///   - previewProvider: 水印预览提供者
    func displayImage(_ image: UIImage, in parentView: UIView, animationDuration: TimeInterval = AnimationConstants.duration, animationCurve: UInt = UIView.AnimationOptions.curveEaseInOut.rawValue, previewProvider: Any? = nil) {
        // 使用标准动画时长 AnimationConstants.duration (0.5秒)
        
        // 计算图像的最终位置和大小
        let imageSize = image.size
        let viewBounds = parentView.bounds
        var imageDisplayRect = CGRect.zero
        
        if viewBounds.width / viewBounds.height > imageSize.width / imageSize.height {
            imageDisplayRect.size.height = viewBounds.height
            imageDisplayRect.size.width = (imageSize.width / imageSize.height) * viewBounds.height
        } else {
            imageDisplayRect.size.width = viewBounds.width
            imageDisplayRect.size.height = (imageSize.height / imageSize.width) * viewBounds.width
        }
        
        imageDisplayRect.origin.x = (viewBounds.width - imageDisplayRect.width) / 2
        imageDisplayRect.origin.y = (viewBounds.height - imageDisplayRect.height) / 2
        
        // 查找当前存在的imageHostView（如果有）
        let existingImageHostView = parentView.viewWithTag(imageHostViewTag)
        
        // 判断是否是首次渲染（从相机模式切换到照片模式）
        let isFirstTimeRendering = existingImageHostView == nil && parentView.subviews.count == 0
        
        // 获取当前显示的图像(如果有)
        var oldImage: UIImage? = nil
        if let existingHostView = existingImageHostView,
           let imageView = existingHostView.subviews.first as? UIImageView {
            oldImage = imageView.image
        }
        
        // 如果有旧图像，使用高级溶解效果
        if let oldImage = oldImage {
            // 创建并配置用于显示渐变效果的imageHostView
            let newImageHostView = UIView(frame: imageDisplayRect)
            newImageHostView.tag = imageHostViewTag
            newImageHostView.clipsToBounds = true
            newImageHostView.backgroundColor = .clear
            
            // 如果提供了预览提供者，设置容器
            if let provider = previewProvider as? WatermarkPreviewProvider {
                provider.setPreviewContainer(parentView)
            }
            
            // 应用高级胶片溶解效果
            applyFilmDissolveEffect(from: oldImage, to: image, in: newImageHostView, duration: animationDuration, completion: {
                // 溶解完成后移除旧视图
                existingImageHostView?.removeFromSuperview()
            })
            
            // 添加新视图
            parentView.addSubview(newImageHostView)
        } else {
            // 首次渲染或没有旧图像，使用简单淡入效果
            
            // 创建新的imageHostView，直接使用计算好的最终位置和大小
            let newImageHostView = UIView(frame: imageDisplayRect)
            newImageHostView.tag = imageHostViewTag
            newImageHostView.clipsToBounds = true
            newImageHostView.backgroundColor = .clear
            
            // 创建并配置imageView
            let imageView = UIImageView(image: image)
            imageView.contentMode = .scaleAspectFit
            imageView.backgroundColor = .clear
            imageView.frame = newImageHostView.bounds
            
            // 将imageView添加到新的host view
            newImageHostView.addSubview(imageView)
            
            // 设置初始透明度 - 从相机切换时使用0
            newImageHostView.alpha = 0
            
            // 将新视图添加到父视图
            parentView.addSubview(newImageHostView)
            
            // 如果提供了预览提供者，设置容器
            if let provider = previewProvider as? WatermarkPreviewProvider {
                provider.setPreviewContainer(parentView)
            }
            
            // 首次渲染直接淡入新视图
            UIView.animate(withDuration: animationDuration, delay: 0, options: UIView.AnimationOptions(rawValue: animationCurve), animations: {
                newImageHostView.alpha = 1
            })
            
            // 如果有旧视图，移除它
            existingImageHostView?.removeFromSuperview()
        }
    }
    
    /// 应用高级胶片溶解效果
    /// - Parameters:
    ///   - fromImage: 起始图像
    ///   - toImage: 目标图像
    ///   - containerView: 容器视图
    ///   - duration: 动画持续时间
    ///   - completion: 完成回调
    private func applyFilmDissolveEffect(from fromImage: UIImage, to toImage: UIImage, in containerView: UIView, duration: TimeInterval, completion: @escaping () -> Void) {
        // 创建一个承载过渡效果的图像视图
        let transitionImageView = UIImageView(frame: containerView.bounds)
        transitionImageView.contentMode = .scaleAspectFit
        containerView.addSubview(transitionImageView)
        
        // 设置初始图像
        transitionImageView.image = fromImage
        
        // 创建DisplayLink来驱动动画
        let displayLink = CADisplayLink(target: self, selector: #selector(updateDissolveTransition))
        displayLink.add(to: .current, forMode: .common)
        
        // 存储溶解上下文
        let context = DissolveContext(
            startTime: CACurrentMediaTime(),
            duration: duration,
            oldImage: fromImage,
            newImage: toImage,
            transitionImageView: transitionImageView,
            displayLink: displayLink,
            completion: {
                // 动画完成后，显示最终图像
                let finalImageView = UIImageView(image: toImage)
                finalImageView.contentMode = .scaleAspectFit
                finalImageView.frame = containerView.bounds
                
                // 替换过渡图像视图
                transitionImageView.removeFromSuperview()
                containerView.addSubview(finalImageView)
                
                // 执行完成回调
                completion()
            }
        )
        
        dissolveContexts[containerView] = context
    }
    
    /// 更新溶解过渡效果
    @objc private func updateDissolveTransition(displayLink: CADisplayLink) {
        // 遍历所有活动的溶解上下文
        for (containerView, context) in dissolveContexts {
            if context.displayLink === displayLink {
                // 计算当前进度 (0-1)
                let elapsedTime = CACurrentMediaTime() - context.startTime
                let progress = min(1.0, elapsedTime / context.duration)
                
                // 如果有旧图像和新图像，创建溶解效果
                if let oldImage = context.oldImage, let transitionImage = createFilmDissolveImage(
                    from: oldImage,
                    to: context.newImage,
                    progress: CGFloat(progress)) {
                    
                    // 更新过渡图像视图
                    context.transitionImageView.image = transitionImage
                }
                
                // 如果进度完成，结束动画
                if progress >= 1.0 {
                    displayLink.invalidate()
                    dissolveContexts.removeValue(forKey: containerView)
                    context.completion()
                }
            }
        }
    }
    
    /// 创建胶片溶解效果图像 - 使用Metal实现
    /// - Parameters:
    ///   - fromImage: 起始图像
    ///   - toImage: 目标图像
    ///   - progress: 进度 (0-1)
    /// - Returns: 混合后的图像
    private func createFilmDissolveImage(from fromImage: UIImage, to toImage: UIImage, progress: CGFloat) -> UIImage? {
        guard let metalEngine = self.metalEngine else {
            print("❌ [ImageRenderingService] Metal引擎不可用")
            return nil
        }

        do {
            // 使用Metal溶解过渡效果
            let result = try metalEngine.applyDissolveTransition(
                from: fromImage,
                to: toImage,
                progress: Float(progress)
            )

            print("✅ [ImageRenderingService] Metal溶解过渡成功")
            return result
        } catch {
            print("❌ [ImageRenderingService] Metal溶解过渡失败: \(error)")
            return nil
        }
    }
} 