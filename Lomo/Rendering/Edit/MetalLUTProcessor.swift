import Foundation
import UIKit
import Metal
import MetalKit

/// Metal LUT处理器 - 完全基于Metal着色器的高性能LUT处理
class MetalLUTProcessor {
    
    // MARK: - Metal资源
    
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    
    // 管线状态缓存
    private var pipelineStates: [String: MTLComputePipelineState] = [:]
    
    // LUT纹理缓存
    private var lutTextureCache: [String: MTLTexture] = [:]
    
    // MARK: - 初始化
    
    init() throws {
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw MetalLUTError.deviceNotSupported
        }
        
        self.device = device
        
        guard let commandQueue = device.makeCommandQueue() else {
            throw MetalLUTError.commandQueueCreationFailed
        }
        
        self.commandQueue = commandQueue
        
        guard let library = device.makeDefaultLibrary() else {
            throw MetalLUTError.libraryNotFound
        }
        
        self.library = library
        
        print("🎨 [MetalLUTProcessor] 初始化完成 - 完全Metal实现")
    }
    
    // MARK: - 公共接口
    
    /// 处理图像 - 应用LUT效果
    /// - Parameters:
    ///   - image: 输入图像
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度 (0.0-1.0)
    ///   - quality: 处理质量
    /// - Returns: 处理后的图像
    func processImage(_ image: UIImage,
                     lutPath: String,
                     intensity: Float = 1.0,
                     quality: LUTQuality = .standard) throws -> UIImage {
        
        print("🎨 [MetalLUTProcessor] 开始处理 - LUT: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
        
        // 创建输入纹理
        let inputTexture = try createTexture(from: image)
        
        // 加载LUT纹理
        let lutTexture = try loadLUTTexture(from: lutPath)
        
        // 创建输出纹理
        let outputTexture = try createOutputTexture(size: image.size)
        
        // 应用LUT效果
        try applyLUT(input: inputTexture, 
                    output: outputTexture, 
                    lut: lutTexture, 
                    intensity: intensity,
                    quality: quality)
        
        // 转换为UIImage
        let resultImage = try createUIImage(from: outputTexture)
        
        print("✅ [MetalLUTProcessor] 处理完成")
        return resultImage
    }
    
    /// 实时预览处理 (优化性能)
    /// - Parameters:
    ///   - image: 输入图像
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度
    /// - Returns: 预览图像
    func processForPreview(_ image: UIImage, 
                          lutPath: String, 
                          intensity: Float = 1.0) throws -> UIImage {
        
        return try processImage(image,
                               lutPath: lutPath,
                               intensity: intensity,
                               quality: .realtime)
    }
    
    /// 高质量输出处理
    /// - Parameters:
    ///   - image: 输入图像
    ///   - lutPath: LUT文件路径
    ///   - intensity: LUT强度
    /// - Returns: 高质量图像
    func processForOutput(_ image: UIImage, 
                         lutPath: String, 
                         intensity: Float = 1.0) throws -> UIImage {
        
        return try processImage(image, 
                               lutPath: lutPath, 
                               intensity: intensity, 
                               quality: .highQuality)
    }
    
    // MARK: - 私有方法
    
    /// 应用LUT效果
    private func applyLUT(input: MTLTexture, 
                         output: MTLTexture, 
                         lut: MTLTexture, 
                         intensity: Float,
                         quality: LUTQuality) throws {
        
        // 选择合适的着色器
        let functionName = quality.shaderFunctionName
        let pipelineState = try getOrCreatePipelineState(functionName: functionName)
        
        // 创建命令缓冲区
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalLUTError.commandBufferCreationFailed
        }
        
        // 设置管线状态
        encoder.setComputePipelineState(pipelineState)
        
        // 设置纹理
        encoder.setTexture(input, index: 0)
        encoder.setTexture(output, index: 1)
        encoder.setTexture(lut, index: 2)
        
        // 设置参数
        var params = LUTParameters(
            intensity: intensity,
            lutSize: Float(lut.width), // 假设是立方体LUT
            padding1: 0.0,
            padding2: 0.0
        )
        encoder.setBytes(&params, length: MemoryLayout<LUTParameters>.size, index: 0)
        
        // 计算线程组
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (input.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (input.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        // 提交命令
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            print("❌ [MetalLUTProcessor] 处理失败: \(error)")
            throw MetalLUTError.processingFailed
        }
    }
    
    /// 加载LUT纹理
    private func loadLUTTexture(from lutPath: String) throws -> MTLTexture {
        // 检查缓存
        if let cachedTexture = lutTextureCache[lutPath] {
            return cachedTexture
        }
        
        // 解析LUT文件
        let lutData = try parseLUTFile(lutPath)

        // 创建真正的3D纹理
        let texture = try create3DTexture(from: lutData)
        
        // 缓存纹理
        lutTextureCache[lutPath] = texture
        
        print("✅ [MetalLUTProcessor] LUT纹理已缓存: \(URL(fileURLWithPath: lutPath).lastPathComponent)")
        
        return texture
    }
    
    /// 解析LUT文件
    private func parseLUTFile(_ lutPath: String) throws -> LUTData {
        let fileExtension = URL(fileURLWithPath: lutPath).pathExtension.lowercased()
        
        switch fileExtension {
        case "cube":
            return try parseCubeLUT(lutPath)
        case "3dl":
            return try parse3DLLUT(lutPath)
        default:
            throw MetalLUTError.unsupportedLUTFormat
        }
    }
    
    /// 解析CUBE格式LUT
    private func parseCubeLUT(_ lutPath: String) throws -> LUTData {
        let content = try String(contentsOfFile: lutPath, encoding: .utf8)
        let lines = content.components(separatedBy: .newlines)
        
        var lutSize: Int = 64 // 默认尺寸
        var lutData: [Float] = []
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespaces)
            
            // 跳过注释和空行
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("#") {
                continue
            }
            
            // 解析LUT_3D_SIZE
            if trimmedLine.hasPrefix("LUT_3D_SIZE") {
                let components = trimmedLine.components(separatedBy: .whitespaces)
                if components.count >= 2, let size = Int(components[1]) {
                    lutSize = size
                }
                continue
            }
            
            // 解析颜色数据
            let components = trimmedLine.components(separatedBy: .whitespaces)
            if components.count >= 3 {
                if let r = Float(components[0]),
                   let g = Float(components[1]),
                   let b = Float(components[2]) {
                    lutData.append(r)
                    lutData.append(g)
                    lutData.append(b)
                    lutData.append(1.0) // Alpha通道
                }
            }
        }
        
        return LUTData(size: lutSize, data: lutData, format: .cube)
    }
    
    /// 解析3DL格式LUT
    private func parse3DLLUT(_ lutPath: String) throws -> LUTData {
        // 3DL格式解析实现
        // 这里简化实现，实际需要根据3DL格式规范解析
        throw MetalLUTError.unsupportedLUTFormat
    }
    
    /// 创建真正的3D纹理 - 专业级实现
    private func create3DTexture(from lutData: LUTData) throws -> MTLTexture {
        let descriptor = MTLTextureDescriptor()
        descriptor.textureType = .type3D
        descriptor.pixelFormat = .rgba8Unorm  // 使用8位格式提高兼容性
        descriptor.width = lutData.size
        descriptor.height = lutData.size
        descriptor.depth = lutData.size
        descriptor.usage = [.shaderRead]
        descriptor.storageMode = .shared  // 使用shared存储模式

        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalLUTError.textureCreationFailed
        }

        // 将浮点数据转换为8位数据
        var pixelData: [UInt8] = []
        for i in stride(from: 0, to: lutData.data.count, by: 4) {
            if i + 3 < lutData.data.count {
                pixelData.append(UInt8(clamp(lutData.data[i] * 255.0, min: 0.0, max: 255.0)))     // R
                pixelData.append(UInt8(clamp(lutData.data[i + 1] * 255.0, min: 0.0, max: 255.0))) // G
                pixelData.append(UInt8(clamp(lutData.data[i + 2] * 255.0, min: 0.0, max: 255.0))) // B
                pixelData.append(255) // A
            }
        }

        // 上传数据到3D纹理
        let region = MTLRegion(origin: MTLOrigin(x: 0, y: 0, z: 0),
                              size: MTLSize(width: lutData.size, height: lutData.size, depth: lutData.size))

        let bytesPerRow = lutData.size * 4  // 4字节每像素 (RGBA8)
        let bytesPerImage = bytesPerRow * lutData.size

        texture.replace(region: region,
                       mipmapLevel: 0,
                       slice: 0,
                       withBytes: pixelData,
                       bytesPerRow: bytesPerRow,
                       bytesPerImage: bytesPerImage)

        print("✅ [MetalLUTProcessor] 创建3D LUT纹理: \(lutData.size)x\(lutData.size)x\(lutData.size)")

        return texture
    }
    
    /// 创建输入纹理
    private func createTexture(from image: UIImage) throws -> MTLTexture {
        guard let cgImage = image.cgImage else {
            throw MetalLUTError.invalidImage
        }
        
        let textureLoader = MTKTextureLoader(device: device)
        let options: [MTKTextureLoader.Option: Any] = [
            .textureUsage: MTLTextureUsage.shaderRead.rawValue,
            .textureStorageMode: MTLStorageMode.private.rawValue
        ]
        
        return try textureLoader.newTexture(cgImage: cgImage, options: options)
    }
    
    /// 创建输出纹理
    private func createOutputTexture(size: CGSize) throws -> MTLTexture {
        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba8Unorm,
            width: Int(size.width),
            height: Int(size.height),
            mipmapped: false
        )
        descriptor.usage = [.shaderWrite, .shaderRead]
        descriptor.storageMode = .private
        
        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalLUTError.textureCreationFailed
        }
        
        return texture
    }

    /// 创建2D纹理 (简化实现)
    private func create2DTexture(from lutData: LUTData) throws -> MTLTexture {
        // 创建扁平化的2D LUT纹理 - 简化版本
        let lutSize = lutData.size
        let textureWidth = lutSize * lutSize
        let textureHeight = lutSize

        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba8Unorm,  // 使用8位格式简化
            width: textureWidth,
            height: textureHeight,
            mipmapped: false
        )
        descriptor.usage = [.shaderRead]
        descriptor.storageMode = .shared

        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalLUTError.textureCreationFailed
        }

        // 简化的数据转换
        var pixelData: [UInt8] = []
        for i in stride(from: 0, to: lutData.data.count, by: 4) {
            if i + 3 < lutData.data.count {
                pixelData.append(UInt8(clamp(lutData.data[i] * 255.0, min: 0.0, max: 255.0)))     // R
                pixelData.append(UInt8(clamp(lutData.data[i + 1] * 255.0, min: 0.0, max: 255.0))) // G
                pixelData.append(UInt8(clamp(lutData.data[i + 2] * 255.0, min: 0.0, max: 255.0))) // B
                pixelData.append(255) // A
            }
        }

        // 上传数据
        let region = MTLRegion(origin: MTLOrigin(x: 0, y: 0, z: 0),
                              size: MTLSize(width: textureWidth, height: textureHeight, depth: 1))

        let bytesPerRow = textureWidth * 4

        texture.replace(region: region,
                       mipmapLevel: 0,
                       withBytes: pixelData,
                       bytesPerRow: bytesPerRow)

        return texture
    }

    /// 创建UIImage
    private func createUIImage(from texture: MTLTexture) throws -> UIImage {
        let ciImage = CIImage(mtlTexture: texture, options: [
            .colorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        guard let outputCIImage = ciImage else {
            throw MetalLUTError.textureConversionFailed
        }
        
        let context = CIContext(mtlDevice: device)
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent) else {
            throw MetalLUTError.cgImageCreationFailed
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    /// 获取或创建管线状态
    private func getOrCreatePipelineState(functionName: String) throws -> MTLComputePipelineState {
        if let cached = pipelineStates[functionName] {
            return cached
        }
        
        guard let function = library.makeFunction(name: functionName) else {
            throw MetalLUTError.functionNotFound(functionName)
        }
        
        let pipelineState = try device.makeComputePipelineState(function: function)
        pipelineStates[functionName] = pipelineState
        
        return pipelineState
    }

    /// 辅助函数：限制值在范围内
    private func clamp(_ value: Float, min: Float, max: Float) -> Float {
        return Swift.max(min, Swift.min(max, value))
    }

    // MARK: - 缓存管理
    
    /// 清理LUT缓存
    func clearCache() {
        lutTextureCache.removeAll()
        pipelineStates.removeAll()
        print("🗑️ [MetalLUTProcessor] 缓存已清理")
    }
    
    /// 获取缓存信息
    func getCacheInfo() -> [String: Any] {
        return [
            "cachedLUTs": lutTextureCache.count,
            "cachedPipelines": pipelineStates.count,
            "lutPaths": Array(lutTextureCache.keys)
        ]
    }
}

// MARK: - 数据结构

/// LUT参数结构体 (与Metal着色器对应)
struct LUTParameters {
    var intensity: Float
    var lutSize: Float
    var padding1: Float
    var padding2: Float
}

/// LUT数据
struct LUTData {
    let size: Int
    let data: [Float]
    let format: LUTFormat
}

/// LUT格式
enum LUTFormat {
    case cube
    case threeDL
}

/// LUT处理质量
enum LUTQuality {
    case simple        // 简化版本 (最兼容)
    case realtime      // 实时预览 (最快)
    case standard      // 标准质量
    case highQuality   // 高质量 (最慢)

    var shaderFunctionName: String {
        switch self {
        case .simple:
            return "apply_simple_lut_filter"
        case .realtime:
            return "apply_realtime_lut_filter"
        case .standard:
            return "apply_3d_lut_filter"
        case .highQuality:
            return "apply_high_quality_lut_filter"
        }
    }
}

/// Metal LUT错误
enum MetalLUTError: Error {
    case deviceNotSupported
    case commandQueueCreationFailed
    case libraryNotFound
    case functionNotFound(String)
    case textureCreationFailed
    case commandBufferCreationFailed
    case invalidImage
    case textureConversionFailed
    case cgImageCreationFailed
    case processingFailed
    case unsupportedLUTFormat
}
