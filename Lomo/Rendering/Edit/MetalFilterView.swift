import SwiftUI
import Metal
import UIKit

/// Metal滤镜视图 - 替换RealTimeFilterView
struct MetalFilterView: View {
    @ObservedObject private var renderer = MetalFilterRenderer.shared
    let frame: CGRect
    var previewProvider: WatermarkPreviewProvider? = nil

    init(frame: CGRect, previewProvider: WatermarkPreviewProvider? = nil) {
        self.frame = frame
        self.previewProvider = previewProvider
        print("📱 [DEBUG] MetalFilterView.init() - frame: \(frame)")
        print("📱 [DEBUG] MetalFilterView.init() - renderer.currentOutputImage: \(MetalFilterRenderer.shared.currentOutputImage != nil ? "有图像" : "nil")")
    }

    var body: some View {
        print("📱 [DEBUG] MetalFilterView.body 渲染 - renderer.currentOutputImage: \(renderer.currentOutputImage != nil ? "有图像" : "nil")")
        return MetalCompatibleView(renderer: renderer, frame: frame, previewProvider: previewProvider)
            .frame(width: frame.width, height: frame.height)
    }
}

/// Metal兼容的UIViewRepresentable
struct MetalCompatibleView: UIViewRepresentable {

    @ObservedObject var renderer: MetalFilterRenderer
    let frame: CGRect
    var previewProvider: WatermarkPreviewProvider? = nil

    func makeUIView(context: Context) -> UIView {
        let containerView = UIView(frame: frame)
        containerView.backgroundColor = .black
        containerView.clipsToBounds = true

        // 设置预览容器，用于水印功能
        if let provider = previewProvider {
            print("📱 [DEBUG] MetalCompatibleView.makeUIView() - 设置预览容器")
            provider.setPreviewContainer(containerView)
        }

        return containerView
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        print("📱 [DEBUG] MetalCompatibleView.updateUIView() 开始")
        
        guard let outputImage = renderer.currentOutputImage else {
            print("📱 [DEBUG] renderer.currentOutputImage 为 nil，清空视图")
            uiView.subviews.forEach { $0.removeFromSuperview() }
            return
        }
        
        print("📱 [DEBUG] renderer.currentOutputImage 存在，开始创建视图")
        print("📱 [DEBUG] 输出图像尺寸: \(outputImage.size)")
        
        // 使用传入的frame而不是uiView.bounds，避免初始化时bounds为零的问题
        let viewBounds = CGRect(origin: .zero, size: CGSize(width: frame.width, height: frame.height))
        var imageDisplayRect = CGRect.zero
        
        print("📱 [DEBUG] 尺寸计算 - imageSize: \(outputImage.size), viewBounds: \(viewBounds)")
        
        // 检查viewBounds是否有效
        guard viewBounds.width > 0 && viewBounds.height > 0 else {
            print("📱 [DEBUG] viewBounds无效，跳过渲染")
            return
        }
        
        // 计算图像显示区域 (保持宽高比)
        let imageSize = outputImage.size
        if viewBounds.width / viewBounds.height > imageSize.width / imageSize.height {
            imageDisplayRect.size.height = viewBounds.height
            imageDisplayRect.size.width = (imageSize.width / imageSize.height) * viewBounds.height
        } else {
            imageDisplayRect.size.width = viewBounds.width
            imageDisplayRect.size.height = (imageSize.height / imageSize.width) * viewBounds.width
        }
        
        imageDisplayRect.origin.x = (viewBounds.width - imageDisplayRect.width) / 2
        imageDisplayRect.origin.y = (viewBounds.height - imageDisplayRect.height) / 2
        
        // 清除现有内容
        uiView.subviews.forEach { $0.removeFromSuperview() }
        print("📱 [DEBUG] 已清除现有子视图")
        
        // 创建imageHostView，使用与ImageRenderingService相同的tag
        let imageHostView = UIView(frame: imageDisplayRect)
        imageHostView.tag = 123  // 与ImageRenderingService保持一致
        imageHostView.clipsToBounds = true
        imageHostView.backgroundColor = .clear
        
        // 创建imageView
        let imageView = UIImageView(image: outputImage)
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        imageView.frame = imageHostView.bounds
        
        // 组装视图层次
        imageHostView.addSubview(imageView)
        uiView.addSubview(imageHostView)
        
        print("📱 [DEBUG] 视图创建完成 - imageDisplayRect: \(imageDisplayRect)")
        print("📱 [DEBUG] MetalCompatibleView.updateUIView() 完成")
    }
}

// MARK: - Metal性能监控视图

/// Metal性能监控视图 (调试用)
struct MetalPerformanceView: View {
    @ObservedObject private var renderer = MetalFilterRenderer.shared
    @State private var renderTime: Double = 0.0
    @State private var fps: Double = 0.0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Metal渲染性能")
                .font(.caption)
                .foregroundColor(.white)
            
            Text("渲染时间: \(String(format: "%.2f", renderTime))ms")
                .font(.caption2)
                .foregroundColor(.green)
            
            Text("FPS: \(String(format: "%.1f", fps))")
                .font(.caption2)
                .foregroundColor(.green)
        }
        .padding(8)
        .background(Color.black.opacity(0.7))
        .cornerRadius(8)
    }
}

// MARK: - Metal调试信息视图

/// Metal调试信息视图
struct MetalDebugView: View {
    @ObservedObject private var renderer = MetalFilterRenderer.shared
    @State private var imageFormatInfo: String = "未检测"

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Metal调试信息")
                .font(.caption)
                .foregroundColor(.white)

            Text("设备: \(getDeviceName())")
                .font(.caption2)
                .foregroundColor(.yellow)

            Text("输出图像: \(renderer.currentOutputImage != nil ? "✅" : "❌")")
                .font(.caption2)
                .foregroundColor(renderer.currentOutputImage != nil ? .green : .red)

            if let image = renderer.currentOutputImage {
                Text("尺寸: \(Int(image.size.width))×\(Int(image.size.height))")
                    .font(.caption2)
                    .foregroundColor(.cyan)

                Text("格式: \(imageFormatInfo)")
                    .font(.caption2)
                    .foregroundColor(.orange)
                    .onAppear {
                        updateImageFormatInfo(image)
                    }
                    .onChange(of: renderer.currentOutputImage) { _ in
                        if let newImage = renderer.currentOutputImage {
                            updateImageFormatInfo(newImage)
                        }
                    }
            }
        }
        .padding(8)
        .background(Color.black.opacity(0.7))
        .cornerRadius(8)
    }

    private func getDeviceName() -> String {
        return renderer.getDeviceName()
    }

    private func updateImageFormatInfo(_ image: UIImage) {
        let colorSpaceInfo = ImageFormatProcessor.shared.analyzeColorSpace(image)
        imageFormatInfo = "\(colorSpaceInfo.format.displayName) \(colorSpaceInfo.bitDepth)位"
    }
}

// MARK: - 预览支持

struct MetalFilterView_Previews: PreviewProvider {
    static var previews: some View {
        MetalFilterView(frame: CGRect(x: 0, y: 0, width: 400, height: 300))
            .background(Color.black)
    }
}
