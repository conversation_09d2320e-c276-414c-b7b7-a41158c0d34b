import Foundation
import Metal
import MetalKit
import CoreImage
import UIKit

/// Metal滤镜引擎 - 专业级GPU滤镜处理
class MetalFilterEngine {
    
    // MARK: - Metal核心组件
    let device: MTLDevice
    let commandQueue: MTLCommandQueue
    let library: MTLLibrary
    
    // MARK: - 纹理管理
    private var inputTexture: MTLTexture?
    private var outputTexture: MTLTexture?
    private var intermediateTextures: [MTLTexture] = []

    // 第3步添加：曲线LUT纹理支持
    private var curveLUTTexture: MTLTexture?
    private var curveLUTSampler: MTLSamplerState?

    // 多通道曲线LUT纹理支持
    private var rgbLUTTexture: MTLTexture?
    private var redLUTTexture: MTLTexture?
    private var greenLUTTexture: MTLTexture?
    private var blueLUTTexture: MTLTexture?
    private var isMultiChannelMode: Bool = false

    // MARK: - 渲染管线状态
    private var pipelineStates: [String: MTLComputePipelineState] = [:]
    
    // MARK: - 初始化
    init() throws {
        // 获取Metal设备
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw MetalFilterError.deviceNotSupported
        }
        self.device = device
        
        // 创建命令队列
        guard let commandQueue = device.makeCommandQueue() else {
            throw MetalFilterError.commandQueueCreationFailed
        }
        self.commandQueue = commandQueue
        
        // 加载着色器库
        guard let library = device.makeDefaultLibrary() else {
            throw MetalFilterError.libraryNotFound
        }
        self.library = library

        // 列出所有可用的函数
        print("🎨 MetalFilterEngine: 可用的着色器函数:")
        let functionNames = library.functionNames.sorted()
        for functionName in functionNames {
            print("   - \(functionName)")
        }
        print("🎨 MetalFilterEngine: 总共 \(functionNames.count) 个函数")

        // 检查我们需要的函数是否存在
        let requiredFunctions = ["lightroom_filter", "vsco_filter", "comprehensive_filter"]
        for funcName in requiredFunctions {
            if library.makeFunction(name: funcName) != nil {
                print("✅ 找到函数: \(funcName)")
            } else {
                print("❌ 缺少函数: \(funcName)")
            }
        }

        // 第3步添加：创建曲线LUT采样器
        let samplerDescriptor = MTLSamplerDescriptor()
        samplerDescriptor.minFilter = .linear
        samplerDescriptor.magFilter = .linear
        samplerDescriptor.sAddressMode = .clampToEdge
        samplerDescriptor.normalizedCoordinates = true
        self.curveLUTSampler = device.makeSamplerState(descriptor: samplerDescriptor)

        print("🎨 MetalFilterEngine: 初始化完成")
        print("   设备: \(device.name)")
        print("   支持Metal版本: \(device.supportsFamily(.common3) ? "3.0+" : "2.0")")
        print("   曲线LUT采样器: \(curveLUTSampler != nil ? "✅" : "❌")")
    }
    
    // MARK: - 纹理管理
    
    /// 设置输入图像
    func setInputImage(_ image: UIImage) throws {
        let textureLoader = MTKTextureLoader(device: device)
        
        // 配置纹理加载选项 - 明确指定sRGB色彩空间
        let options: [MTKTextureLoader.Option: Any] = [
            .textureUsage: MTLTextureUsage.shaderRead.rawValue,
            .textureStorageMode: MTLStorageMode.private.rawValue,
            .origin: MTKTextureLoader.Origin.bottomLeft,
            .SRGB: true  // 明确指定为sRGB色彩空间，避免色彩空间混乱
        ]
        
        // 从UIImage创建纹理
        guard let cgImage = image.cgImage else {
            throw MetalFilterError.invalidImage
        }
        
        self.inputTexture = try textureLoader.newTexture(cgImage: cgImage, options: options)

        // 创建输出纹理
        try createOutputTexture(width: Int(image.size.width), height: Int(image.size.height))

        print("🎨 MetalFilterEngine: 输入纹理已设置 - 尺寸: \(image.size)")
        print("🎨 MetalFilterEngine: 输入纹理格式: \(inputTexture?.pixelFormat.rawValue ?? 0)")
        print("🎨 MetalFilterEngine: 输出纹理格式: \(outputTexture?.pixelFormat.rawValue ?? 0)")
        print("🎨 MetalFilterEngine: CGImage色彩空间: \(cgImage.colorSpace?.name as String? ?? "nil")")
    }
    
    /// 创建输出纹理
    private func createOutputTexture(width: Int, height: Int) throws {
        // 使用与输入纹理相同的格式
        let pixelFormat = inputTexture?.pixelFormat ?? .rgba8Unorm

        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: pixelFormat,
            width: width,
            height: height,
            mipmapped: false
        )
        descriptor.usage = [.shaderWrite, .shaderRead]
        descriptor.storageMode = .private

        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalFilterError.textureCreationFailed
        }

        print("🎨 MetalFilterEngine: 创建输出纹理 - 格式: \(pixelFormat.rawValue)")
        self.outputTexture = texture
    }
    
    /// 创建中间纹理
    func createIntermediateTexture(width: Int, height: Int) throws -> MTLTexture {
        let descriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba8Unorm,
            width: width,
            height: height,
            mipmapped: false
        )
        descriptor.usage = [.shaderWrite, .shaderRead]
        descriptor.storageMode = .private
        
        guard let texture = device.makeTexture(descriptor: descriptor) else {
            throw MetalFilterError.textureCreationFailed
        }
        
        return texture
    }
    
    // MARK: - 管线状态管理
    
    /// 创建计算管线状态
    func createComputePipelineState(functionName: String) throws -> MTLComputePipelineState {
        // 检查缓存
        if let cachedState = pipelineStates[functionName] {
            return cachedState
        }
        
        // 获取着色器函数，如果找不到则尝试备用函数
        var function: MTLFunction?

        // 首先尝试请求的函数
        function = library.makeFunction(name: functionName)

        // 如果找不到，尝试备用函数
        if function == nil {
            print("⚠️ 找不到函数 '\(functionName)'，尝试备用函数...")

            // 尝试备用函数名
            let fallbackFunctions = ["comprehensive_filter", "basic_filter", "simple_filter"]
            for fallbackName in fallbackFunctions {
                if let fallbackFunction = library.makeFunction(name: fallbackName) {
                    print("✅ 使用备用函数: \(fallbackName)")
                    function = fallbackFunction
                    break
                }
            }
        }

        guard let finalFunction = function else {
            print("❌ 无法找到任何可用的着色器函数")
            throw MetalFilterError.functionNotFound(functionName)
        }
        
        // 创建管线状态
        do {
            let pipelineState = try device.makeComputePipelineState(function: finalFunction)

            // 缓存管线状态
            pipelineStates[functionName] = pipelineState

            print("🎨 MetalFilterEngine: 创建管线状态成功 - \(functionName)")
            print("🎨 MetalFilterEngine: 线程执行宽度: \(pipelineState.threadExecutionWidth)")
            print("🎨 MetalFilterEngine: 最大线程数: \(pipelineState.maxTotalThreadsPerThreadgroup)")

            return pipelineState
        } catch {
            print("❌ MetalFilterEngine: 创建管线状态失败 - \(functionName): \(error)")
            throw error
        }
    }
    
    // MARK: - 渲染执行
    
    /// 执行滤镜渲染
    func executeFilter(
        functionName: String,
        parameters: [String: Any] = [:],
        inputTexture: MTLTexture? = nil,
        outputTexture: MTLTexture? = nil
    ) throws -> MTLTexture {

        print("🎨 MetalFilterEngine: executeFilter() 开始 - 函数名: \(functionName)")

        let input = inputTexture ?? self.inputTexture
        let output = outputTexture ?? self.outputTexture

        guard let inputTex = input, let outputTex = output else {
            print("❌ MetalFilterEngine: 纹理未设置")
            throw MetalFilterError.textureNotSet
        }

        print("🎨 MetalFilterEngine: 输入纹理: \(inputTex.width)x\(inputTex.height), 输出纹理: \(outputTex.width)x\(outputTex.height)")
        
        // 获取管线状态
        let pipelineState = try createComputePipelineState(functionName: functionName)
        
        // 创建命令缓冲区
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            throw MetalFilterError.commandBufferCreationFailed
        }
        
        // 创建计算编码器
        guard let computeEncoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalFilterError.computeEncoderCreationFailed
        }
        
        // 设置管线状态
        computeEncoder.setComputePipelineState(pipelineState)
        
        // 设置纹理
        computeEncoder.setTexture(inputTex, index: 0)
        computeEncoder.setTexture(outputTex, index: 1)
        
        // 设置参数
        try setShaderParameters(encoder: computeEncoder, parameters: parameters)
        
        // 计算线程组大小
        let threadgroupSize = MTLSize(
            width: min(pipelineState.threadExecutionWidth, inputTex.width),
            height: min(pipelineState.maxTotalThreadsPerThreadgroup / pipelineState.threadExecutionWidth, inputTex.height),
            depth: 1
        )

        let threadgroupCount = MTLSize(
            width: (inputTex.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTex.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )

        print("🎨 MetalFilterEngine: 线程组大小: \(threadgroupSize.width)x\(threadgroupSize.height)")
        print("🎨 MetalFilterEngine: 线程组数量: \(threadgroupCount.width)x\(threadgroupCount.height)")

        // 分发线程
        computeEncoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        print("🎨 MetalFilterEngine: 线程已分发")
        
        // 结束编码
        computeEncoder.endEncoding()
        
        // 提交命令
        print("🎨 MetalFilterEngine: 提交Metal命令")
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()

        if let error = commandBuffer.error {
            print("❌ MetalFilterEngine: 命令执行失败 - \(error)")
        } else {
            print("🎨 MetalFilterEngine: 命令执行成功")
        }

        return outputTex
    }

    /// 第3步添加：执行带曲线的滤镜渲染
    func executeFilterWithCurves(
        parameters: [String: Any] = [:],
        inputTexture: MTLTexture? = nil,
        outputTexture: MTLTexture? = nil
    ) throws -> MTLTexture {

        print("🎨 MetalFilterEngine: executeFilterWithCurves() 开始")

        let input = inputTexture ?? self.inputTexture
        let output = outputTexture ?? self.outputTexture

        guard let inputTex = input, let outputTex = output else {
            print("❌ MetalFilterEngine: 纹理未设置")
            throw MetalFilterError.textureNotSet
        }

        // 选择着色器函数名
        let functionName: String
        if hasActiveCurve() {
            if isMultiChannelMode {
                functionName = "comprehensive_filter_with_multichannel_curves"
            } else {
                functionName = "comprehensive_filter_with_curves"
            }
        } else {
            functionName = "comprehensive_filter"
        }
        print("🎨 MetalFilterEngine: 使用着色器: \(functionName)")

        // 获取管线状态
        let pipelineState = try createComputePipelineState(functionName: functionName)

        // 创建命令缓冲区
        guard let commandBuffer = commandQueue.makeCommandBuffer() else {
            throw MetalFilterError.commandBufferCreationFailed
        }

        // 创建计算编码器
        guard let computeEncoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalFilterError.computeEncoderCreationFailed
        }

        // 设置管线状态
        computeEncoder.setComputePipelineState(pipelineState)

        // 设置纹理
        computeEncoder.setTexture(inputTex, index: 0)
        computeEncoder.setTexture(outputTex, index: 1)

        // 设置曲线LUT纹理和采样器
        if hasActiveCurve(), let sampler = curveLUTSampler {
            if isMultiChannelMode {
                // 多通道模式：设置所有通道的LUT纹理
                if let rgbTex = rgbLUTTexture { computeEncoder.setTexture(rgbTex, index: 2) }
                if let redTex = redLUTTexture { computeEncoder.setTexture(redTex, index: 3) }
                if let greenTex = greenLUTTexture { computeEncoder.setTexture(greenTex, index: 4) }
                if let blueTex = blueLUTTexture { computeEncoder.setTexture(blueTex, index: 5) }
                computeEncoder.setSamplerState(sampler, index: 0)
                print("✅ 多通道曲线LUT纹理和采样器已设置")
            } else if let curveLUT = curveLUTTexture {
                // 单通道模式：只设置RGB LUT
                computeEncoder.setTexture(curveLUT, index: 2)
                computeEncoder.setSamplerState(sampler, index: 0)
                print("✅ RGB曲线LUT纹理和采样器已设置")
            }
        }

        // 设置参数
        try setShaderParameters(encoder: computeEncoder, parameters: parameters)

        // 计算线程组大小
        let threadgroupSize = MTLSize(
            width: min(pipelineState.threadExecutionWidth, inputTex.width),
            height: min(pipelineState.maxTotalThreadsPerThreadgroup / pipelineState.threadExecutionWidth, inputTex.height),
            depth: 1
        )

        let threadgroupCount = MTLSize(
            width: (inputTex.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTex.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )

        // 分发计算
        computeEncoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        computeEncoder.endEncoding()

        // 提交命令
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()

        if let error = commandBuffer.error {
            print("❌ MetalFilterEngine: 命令执行失败: \(error)")
            throw MetalFilterError.executionFailed
        }

        print("✅ MetalFilterEngine: executeFilterWithCurves() 完成")
        return outputTex
    }

    // MARK: - 第3步添加：曲线LUT管理

    /// 更新曲线LUT纹理
    func updateCurveLUT(_ lut: [Float]) {
        guard !lut.isEmpty else {
            curveLUTTexture = nil
            return
        }

        // 创建1D纹理描述符
        let textureDescriptor = MTLTextureDescriptor()
        textureDescriptor.textureType = .type1D
        textureDescriptor.pixelFormat = .r32Float
        textureDescriptor.width = lut.count
        textureDescriptor.usage = [.shaderRead]

        // 创建纹理
        guard let texture = device.makeTexture(descriptor: textureDescriptor) else {
            print("❌ 无法创建曲线LUT纹理")
            return
        }

        // 上传LUT数据
        texture.replace(region: MTLRegionMake1D(0, lut.count),
                       mipmapLevel: 0,
                       withBytes: lut,
                       bytesPerRow: lut.count * MemoryLayout<Float>.size)

        self.curveLUTTexture = texture
        print("✅ 曲线LUT纹理已更新，大小: \(lut.count)")
    }

    /// 更新多通道曲线LUT纹理
    func updateMultiChannelCurveLUTs(rgbLUT: [Float], redLUT: [Float], greenLUT: [Float], blueLUT: [Float]) {
        // 清除单通道模式
        curveLUTTexture = nil
        isMultiChannelMode = true

        // 创建所有通道的LUT纹理
        rgbLUTTexture = createLUTTexture(from: rgbLUT, name: "RGB")
        redLUTTexture = createLUTTexture(from: redLUT, name: "Red")
        greenLUTTexture = createLUTTexture(from: greenLUT, name: "Green")
        blueLUTTexture = createLUTTexture(from: blueLUT, name: "Blue")

        print("✅ 多通道曲线LUT纹理已更新")
        print("   RGB: \(rgbLUT.count), Red: \(redLUT.count), Green: \(greenLUT.count), Blue: \(blueLUT.count)")
    }

    /// 创建单个LUT纹理
    private func createLUTTexture(from lut: [Float], name: String) -> MTLTexture? {
        guard !lut.isEmpty else { return nil }

        let textureDescriptor = MTLTextureDescriptor()
        textureDescriptor.textureType = .type1D
        textureDescriptor.pixelFormat = .r32Float
        textureDescriptor.width = lut.count
        textureDescriptor.usage = [.shaderRead]

        guard let texture = device.makeTexture(descriptor: textureDescriptor) else {
            print("❌ 无法创建\(name)LUT纹理")
            return nil
        }

        texture.replace(region: MTLRegionMake1D(0, lut.count),
                       mipmapLevel: 0,
                       withBytes: lut,
                       bytesPerRow: lut.count * MemoryLayout<Float>.size)

        return texture
    }

    /// 检查是否有活跃的曲线
    func hasActiveCurve() -> Bool {
        return curveLUTTexture != nil || isMultiChannelMode
    }

    /// 检查是否为多通道模式
    func isMultiChannelCurveMode() -> Bool {
        return isMultiChannelMode
    }

    /// 设置着色器参数
    private func setShaderParameters(encoder: MTLComputeCommandEncoder, parameters: [String: Any]) throws {
        var bufferIndex = 0

        for (key, value) in parameters {
            switch value {
            case let floatValue as Float:
                var param = floatValue
                let buffer = device.makeBuffer(bytes: &param, length: MemoryLayout<Float>.size, options: [])
                encoder.setBuffer(buffer, offset: 0, index: bufferIndex)
                bufferIndex += 1

            case let float2Value as SIMD2<Float>:
                var param = float2Value
                let buffer = device.makeBuffer(bytes: &param, length: MemoryLayout<SIMD2<Float>>.size, options: [])
                encoder.setBuffer(buffer, offset: 0, index: bufferIndex)
                bufferIndex += 1

            case let float3Value as SIMD3<Float>:
                var param = float3Value
                let buffer = device.makeBuffer(bytes: &param, length: MemoryLayout<SIMD3<Float>>.size, options: [])
                encoder.setBuffer(buffer, offset: 0, index: bufferIndex)
                bufferIndex += 1

            case let float4Value as SIMD4<Float>:
                var param = float4Value
                let buffer = device.makeBuffer(bytes: &param, length: MemoryLayout<SIMD4<Float>>.size, options: [])
                encoder.setBuffer(buffer, offset: 0, index: bufferIndex)
                bufferIndex += 1

            case let structValue as MetalFilterParameters:
                var param = structValue
                let buffer = device.makeBuffer(bytes: &param, length: MemoryLayout<MetalFilterParameters>.size, options: [])
                encoder.setBuffer(buffer, offset: 0, index: bufferIndex)
                bufferIndex += 1

            default:
                print("⚠️ MetalFilterEngine: 不支持的参数类型 - \(key): \(type(of: value))")
            }
        }
    }
    
    // MARK: - 输出转换
    
    /// 将Metal纹理转换为UIImage
    func textureToUIImage(_ texture: MTLTexture) throws -> UIImage {
        // 创建Core Image上下文，明确指定sRGB色彩空间
        let ciContext = CIContext(mtlDevice: device, options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])

        // 从Metal纹理创建CIImage，明确指定色彩空间
        let ciImage = CIImage(mtlTexture: texture, options: [
            .colorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])

        guard let outputCIImage = ciImage else {
            print("❌ MetalFilterEngine: CIImage创建失败")
            throw MetalFilterError.textureConversionFailed
        }

        print("🎨 MetalFilterEngine: CIImage创建成功 - extent: \(outputCIImage.extent)")

        // 转换为CGImage，明确指定sRGB色彩空间
        guard let cgImage = ciContext.createCGImage(outputCIImage, from: outputCIImage.extent, format: .RGBA8, colorSpace: CGColorSpace(name: CGColorSpace.sRGB)) else {
            print("❌ MetalFilterEngine: CGImage创建失败")
            throw MetalFilterError.cgImageCreationFailed
        }

        print("🎨 MetalFilterEngine: CGImage创建成功 - 尺寸: \(cgImage.width)x\(cgImage.height)")
        print("🎨 MetalFilterEngine: CGImage色彩空间: \(cgImage.colorSpace?.name as String? ?? "nil")")

        let resultImage = UIImage(cgImage: cgImage)
        print("🎨 MetalFilterEngine: UIImage创建成功 - 尺寸: \(resultImage.size)")

        return resultImage
    }
    
    /// 获取当前输出纹理
    func getCurrentOutputTexture() -> MTLTexture? {
        return outputTexture
    }
}

// MARK: - Metal参数结构体

/// Metal着色器参数结构体 - 与BasicFilterParameters对应
struct MetalFilterParameters {
    var exposure: Float = 0.0
    var contrast: Float = 0.0
    var brightness: Float = 0.0
    var saturation: Float = 0.0
    var vibrance: Float = 0.0
    var temperature: Float = 0.0
    var tint: Float = 0.0
    var highlights: Float = 0.0
    var shadows: Float = 0.0
    var whites: Float = 0.0
    var blacks: Float = 0.0
    var clarity: Float = 0.0
    var clarity2: Float = 0.0
    var dehaze: Float = 0.0
    var gamma: Float = 1.0
    var fadeEffect: Float = 0.0     // 褪色效果强度 (0.0 到 1.0)
    var monoEffect: Float = 0.0     // 黑白效果强度 (0.0 到 1.0)
    var isLinearInput: Float = 0.0  // 是否为线性输入 (0.0 = sRGB, 1.0 = Linear)
    var highlightProtectionIntensity: Float = 0.0  // 高光保护强度 (0.0 到 1.0)
    var highlightProtectionMode: Int32 = 0         // 高光保护模式 (0=标准, 1=胶片, 2=线性)
    var curveIntensity: Float = 0.0                // 曲线强度 (0.0 到 1.0) - 第3步添加

    // MARK: - HSL选择性调整参数
    var hue: Float = 0.0                   // 色相调整 (-180.0 到 +180.0 度)
    var hslSaturation: Float = 0.0         // HSL饱和度调整 (-1.0 到 +1.0)
    var hslLuminance: Float = 0.0          // HSL明度调整 (-1.0 到 +1.0)
    var selectedHSLColorIndex: Int32 = 0   // 选中的颜色范围索引 (0-7)
    var hslColorRangeSoftness: Float = 0.3 // 颜色范围过渡柔和度 (0.0 到 1.0)
    var hslColorRangePrecision: Float = 1.0 // 颜色范围检测精度 (0.5 到 2.0)

    // MARK: - 色调分离参数
    var highlightHue: Float = 0.0          // 高光色相 (0.0 到 360.0 度)
    var highlightSaturation: Float = 0.0   // 高光饱和度 (0.0 到 1.0)
    var shadowHue: Float = 0.0             // 阴影色相 (0.0 到 360.0 度)
    var shadowSaturation: Float = 0.0      // 阴影饱和度 (0.0 到 1.0)
    var splitToningBalance: Float = 0.0    // 色调分离平衡 (-1.0 到 1.0)

    // MARK: - 细节调整参数
    var sharpness: Float = 0.0             // 锐化强度 (-1.0 到 1.0)
    var vignetteIntensity: Float = 0.0     // 暗角强度 (-1.0 到 1.0)
    var vignetteRadius: Float = 1.0        // 暗角半径 (0.0 到 2.0)
    var noiseReduction: Float = 0.0        // 降噪强度 (0.0 到 1.0)
}

// MARK: - 错误定义

enum MetalFilterError: Error, LocalizedError {
    case deviceNotSupported
    case commandQueueCreationFailed
    case libraryNotFound
    case functionNotFound(String)
    case invalidImage
    case textureCreationFailed
    case textureNotSet
    case commandBufferCreationFailed
    case computeEncoderCreationFailed
    case textureConversionFailed
    case cgImageCreationFailed
    case executionFailed  // 第3步添加：执行失败错误
    
    var errorDescription: String? {
        switch self {
        case .deviceNotSupported:
            return "Metal设备不支持"
        case .commandQueueCreationFailed:
            return "命令队列创建失败"
        case .libraryNotFound:
            return "着色器库未找到"
        case .functionNotFound(let name):
            return "着色器函数未找到: \(name)"
        case .invalidImage:
            return "无效的图像"
        case .textureCreationFailed:
            return "纹理创建失败"
        case .textureNotSet:
            return "纹理未设置"
        case .commandBufferCreationFailed:
            return "命令缓冲区创建失败"
        case .computeEncoderCreationFailed:
            return "计算编码器创建失败"
        case .textureConversionFailed:
            return "纹理转换失败"
        case .cgImageCreationFailed:
            return "CGImage创建失败"
        case .executionFailed:
            return "Metal命令执行失败"  // 第3步添加
        }
    }
}
