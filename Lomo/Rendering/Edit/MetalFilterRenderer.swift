import Foundation
import Metal
import UIKit
import SwiftUI



/// Metal滤镜渲染器 - 替换RealTimeFilterRenderer
class MetalFilterRenderer: ObservableObject {
    
    // MARK: - 单例
    static let shared = MetalFilterRenderer()
    
    // MARK: - Metal引擎
    private let metalEngine: MetalFilterEngine
    
    // MARK: - 当前状态
    @Published var currentOutputImage: UIImage?
    private var baseImage: UIImage?
    
    // MARK: - 滤镜参数
    private var currentParameters = FilterParameters()
    private var currentRenderingMode: RenderingMode = .lightroom
    
    // MARK: - 初始化
    private init() {
        do {
            self.metalEngine = try MetalFilterEngine()
            print("🎨 MetalFilterRenderer: 初始化完成")
        } catch {
            print("❌ MetalFilterRenderer初始化失败: \(error)")
            fatalError("MetalFilterRenderer初始化失败: \(error)")
        }
    }
    
    // MARK: - 公共接口
    
    /// 设置基础图像
    func setBaseImage(_ image: UIImage) {
        print("🎨 [DEBUG] MetalFilterRenderer.setBaseImage() 开始 - 图像尺寸: \(image.size)")

        // 智能预处理 - 确保不同格式都能正确处理滤镜
        let processedImage = ImageFormatProcessor.shared.preprocessImageForFiltering(image)
        print("🎨 [DEBUG] MetalFilterRenderer.setBaseImage() - 预处理前图像尺寸: \(image.size), 预处理后图像尺寸: \(processedImage.size)")
        print("🎨 [DEBUG] MetalFilterRenderer.setBaseImage() - 图像是否发生变化: \(image !== processedImage)")

        // 保存原始图像引用
        self.baseImage = processedImage

        do {
            try metalEngine.setInputImage(processedImage)
            renderCurrentEffect()
            print("🎨 [DEBUG] MetalFilterRenderer.setBaseImage() 完成 - currentOutputImage: \(currentOutputImage != nil ? "有图像" : "nil")")
        } catch {
            print("❌ MetalFilterRenderer: 设置图像失败 - \(error)")
            currentOutputImage = processedImage // 回退到处理后的图像
        }
    }
    
    /// 更新滤镜参数
    func updateParameters(_ parameters: FilterParameters) {
        print("🎨🎨🎨 [MetalFilterRenderer] updateParameters() 开始")
        print("🎨 [MetalFilterRenderer] 基础参数: 曝光=\(parameters.exposure), 对比度=\(parameters.contrast), 饱和度=\(parameters.saturation)")
        print("🎨🎨🎨 [MetalFilterRenderer] HSL参数检查:")
        print("🎨 [MetalFilterRenderer] - 色相=\(parameters.hue)°, HSL饱和度=\(parameters.hslSaturation), HSL明度=\(parameters.hslLuminance)")
        print("🎨 [MetalFilterRenderer] - 选中颜色索引=\(parameters.selectedHSLColorIndex), 柔和度=\(parameters.hslColorRangeSoftness)")

        let hasHSLChanges = parameters.hue != 0.0 || parameters.hslSaturation != 0.0 || parameters.hslLuminance != 0.0
        print("🎨 [MetalFilterRenderer] - 是否有HSL变化: \(hasHSLChanges)")

        self.currentParameters = parameters
        renderCurrentEffect()

        print("🎨🎨🎨 [MetalFilterRenderer] updateParameters() 完成")
    }
    
    /// 设置渲染模式
    func setRenderingMode(_ mode: RenderingMode) {
        print("🎨 [DEBUG] MetalFilterRenderer.setRenderingMode() - 切换到: \(mode.displayName)")
        print("🎨 [DEBUG] 着色器函数: \(mode.shaderFunctionName)")
        currentRenderingMode = mode
        renderCurrentEffect()
    }

    /// 获取当前渲染模式
    func getCurrentRenderingMode() -> RenderingMode {
        return currentRenderingMode
    }

    /// 获取Metal设备名称
    func getDeviceName() -> String {
        return metalEngine.device.name
    }

    /// 重新加载Metal着色器库 - 用于调试
    func reloadShaderLibrary() {
        print("🔄 重新加载Metal着色器库...")
        do {
            // 重新创建MetalFilterEngine
            let newEngine = try MetalFilterEngine()
            // 这里我们不能直接替换，因为metalEngine是let常量
            // 但可以用于调试检查
            print("🔄 新的Metal引擎创建成功")
        } catch {
            print("❌ 重新加载失败: \(error)")
        }
    }

    /// 应用LUT（暂时保留接口兼容性）
    func applyLUT(lutPath: String?, intensity: Float = 1.0) {
        print("🎨 [DEBUG] MetalFilterRenderer.applyLUT() - LUT功能将在后续版本实现")
        // TODO: 实现LUT支持
    }
    
    // MARK: - 渲染核心
    
    /// 渲染当前效果
    private func renderCurrentEffect() {
        print("🎨🎨🎨 [MetalFilterRenderer] renderCurrentEffect() 开始")

        guard let baseImage = baseImage else {
            print("🎨 [DEBUG] baseImage 为 nil，设置 currentOutputImage = nil")
            currentOutputImage = nil
            return
        }

        do {
            print("🎨 [DEBUG] 开始Metal滤镜处理 - 模式: \(currentRenderingMode.displayName)")
            print("🎨 [DEBUG] 着色器函数: \(currentRenderingMode.shaderFunctionName)")

            // 第3步添加：更新曲线LUT
            updateCurveLUTIfNeeded()

            // 根据当前模式和曲线状态选择渲染方法
            let outputTexture: MTLTexture
            if currentParameters.curveIntensity > 0.0 {
                if metalEngine.isMultiChannelCurveMode() {
                    print("🎨 [DEBUG] 使用多通道曲线渲染")
                } else {
                    print("🎨 [DEBUG] 使用RGB曲线渲染")
                }
                outputTexture = try metalEngine.executeFilterWithCurves(
                    parameters: createShaderParameters()
                )
            } else {
                print("🎨 [DEBUG] 使用标准渲染")
                let shaderName = currentRenderingMode.shaderFunctionName
                print("🎨 [DEBUG] 使用着色器: \(shaderName)")
                outputTexture = try metalEngine.executeFilter(
                    functionName: shaderName,
                    parameters: createShaderParameters()
                )
            }
            
            // 转换为UIImage
            let resultImage = try metalEngine.textureToUIImage(outputTexture)

            // 检查图像处理结果
            print("🎨🎨🎨 [MetalFilterRenderer] 图像处理结果检查:")
            print("🎨 [MetalFilterRenderer] - 原图尺寸: \(baseImage.size)")
            print("🎨 [MetalFilterRenderer] - 输出尺寸: \(resultImage.size)")

            // 如果有HSL调整，特别标注
            let hasHSLAdjustments = currentParameters.hue != 0.0 || currentParameters.hslSaturation != 0.0 || currentParameters.hslLuminance != 0.0
            if hasHSLAdjustments {
                print("🎨🎨🎨 [MetalFilterRenderer] ⚠️ 有HSL调整，检查是否生效！")
                print("🎨 [MetalFilterRenderer] HSL参数: 色相=\(currentParameters.hue)°, 饱和度=\(currentParameters.hslSaturation), 明度=\(currentParameters.hslLuminance)")
                print("🎨 [MetalFilterRenderer] 选中颜色: \(currentParameters.selectedHSLColorIndex)")
            }

            // 更新输出图像
            DispatchQueue.main.async {
                self.currentOutputImage = resultImage
                print("🎨 [DEBUG] MetalFilterRenderer.renderCurrentEffect() 完成 - 图像已更新")
            }
            
        } catch {
            print("❌ MetalFilterRenderer: 渲染失败 - \(error)")
            // 回退到原图
            DispatchQueue.main.async {
                self.currentOutputImage = baseImage
            }
        }
    }
    


    // MARK: - 第3步添加：曲线LUT管理

    /// 如果需要则更新曲线LUT - 支持多通道
    private func updateCurveLUTIfNeeded() {
        print("🎨 [DEBUG] 检查曲线LUT更新: 强度=\(currentParameters.curveIntensity), RGB启用=\(currentParameters.rgbCurveEnabled), 分离通道启用=\(currentParameters.channelCurvesEnabled)")

        guard currentParameters.curveIntensity > 0.0 else {
            // 曲线强度为0，清除所有LUT
            print("🎨 [DEBUG] 曲线强度为0，清除所有LUT - 这应该会让图像恢复原状")
            metalEngine.updateCurveLUT([])
            metalEngine.updateMultiChannelCurveLUTs(rgbLUT: [], redLUT: [], greenLUT: [], blueLUT: [])
            return
        }

        // 检查是否有分离通道曲线
        let hasChannelCurves = currentParameters.channelCurvesEnabled && (
            !currentParameters.redCurveLUT.isEmpty ||
            !currentParameters.greenCurveLUT.isEmpty ||
            !currentParameters.blueCurveLUT.isEmpty
        )

        if hasChannelCurves {
            // 使用多通道曲线模式
            print("🎨 [DEBUG] 更新多通道曲线LUT")
            let rgbLUT = currentParameters.rgbCurveLUT.isEmpty ?
                Array(0..<256).map { Float($0) / 255.0 } : currentParameters.rgbCurveLUT
            let redLUT = currentParameters.redCurveLUT.isEmpty ?
                Array(0..<256).map { Float($0) / 255.0 } : currentParameters.redCurveLUT
            let greenLUT = currentParameters.greenCurveLUT.isEmpty ?
                Array(0..<256).map { Float($0) / 255.0 } : currentParameters.greenCurveLUT
            let blueLUT = currentParameters.blueCurveLUT.isEmpty ?
                Array(0..<256).map { Float($0) / 255.0 } : currentParameters.blueCurveLUT

            metalEngine.updateMultiChannelCurveLUTs(
                rgbLUT: rgbLUT,
                redLUT: redLUT,
                greenLUT: greenLUT,
                blueLUT: blueLUT
            )
        } else if !currentParameters.rgbCurveLUT.isEmpty && currentParameters.rgbCurveEnabled {
            // 只使用RGB曲线
            print("🎨 [DEBUG] 更新RGB曲线LUT，大小: \(currentParameters.rgbCurveLUT.count)")
            metalEngine.updateCurveLUT(currentParameters.rgbCurveLUT)
        } else {
            // 使用线性LUT作为默认
            let linearLUT = Array(0..<256).map { Float($0) / 255.0 }
            print("🎨 [DEBUG] 使用线性LUT作为默认")
            metalEngine.updateCurveLUT(linearLUT)
        }
    }

    /// 创建着色器参数
    private func createShaderParameters() -> [String: Any] {
        // 创建Metal着色器参数结构体
        var metalParams = MetalFilterParameters()

        // 映射参数 (范围转换)
        metalParams.exposure = currentParameters.exposure // -2.0 to +2.0
        metalParams.contrast = currentParameters.contrast / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.brightness = currentParameters.brightness / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.saturation = currentParameters.saturation / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.vibrance = currentParameters.vibrance / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.temperature = currentParameters.temperature / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.tint = currentParameters.tint / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.highlights = currentParameters.highlights / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.shadows = currentParameters.shadows / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.whites = currentParameters.whites / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.blacks = currentParameters.blacks / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.clarity = currentParameters.clarity / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.clarity2 = currentParameters.clarity2 / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.dehaze = currentParameters.dehaze / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.gamma = currentParameters.gamma // 0.25 to 4.0
        metalParams.fadeEffect = currentParameters.fadeEffect / 100.0 // 0 to 100 -> 0.0 to 1.0
        metalParams.monoEffect = currentParameters.monoEffect / 100.0 // 0 to 100 -> 0.0 to 1.0
        metalParams.isLinearInput = currentParameters.isLinearImage ? 1.0 : 0.0 // 线性输入标志
        metalParams.highlightProtectionIntensity = currentParameters.highlightProtectionIntensity // 0.0 to 1.0
        metalParams.highlightProtectionMode = Int32(currentParameters.highlightProtectionMode) // 0, 1, 2
        metalParams.curveIntensity = currentParameters.curveIntensity // 第3步添加：曲线强度

        // HSL选择性调整参数映射 - 基于专业实现
        metalParams.hue = currentParameters.hue // -180.0 to +180.0 度
        metalParams.hslSaturation = 1.0 + currentParameters.hslSaturation / 100.0 // -100 to 100 -> 0.0 to 2.0 (1.0为原始值)
        metalParams.hslLuminance = currentParameters.hslLuminance // -100 to 100 直接传递
        metalParams.selectedHSLColorIndex = Int32(currentParameters.selectedHSLColorIndex) // 0-7
        metalParams.hslColorRangeSoftness = currentParameters.hslColorRangeSoftness // 0.0 to 1.0
        metalParams.hslColorRangePrecision = currentParameters.hslColorRangePrecision // 0.5 to 2.0

        // 色调分离参数映射
        metalParams.highlightHue = currentParameters.highlightHue // 0.0 to 360.0
        metalParams.highlightSaturation = currentParameters.highlightSaturation / 100.0 // 0 to 100 -> 0.0 to 1.0
        metalParams.shadowHue = currentParameters.shadowHue // 0.0 to 360.0
        metalParams.shadowSaturation = currentParameters.shadowSaturation / 100.0 // 0 to 100 -> 0.0 to 1.0
        metalParams.splitToningBalance = currentParameters.splitToningBalance / 100.0 // -100 to 100 -> -1.0 to 1.0

        // 细节调整参数映射 - 按照色调分离的相同模式
        metalParams.sharpness = currentParameters.sharpness / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.vignetteIntensity = currentParameters.vignetteIntensity / 100.0 // -100 to +100 -> -1.0 to +1.0
        metalParams.vignetteRadius = currentParameters.vignetteRadius // 0.0 to 2.0 直接传递
        metalParams.noiseReduction = currentParameters.noiseReduction / 100.0 // 0 to 100 -> 0.0 to 1.0

        print("🎨 [DEBUG] Metal参数详细:")
        print("🎨 [DEBUG] - 曝光=\(metalParams.exposure), 对比度=\(metalParams.contrast), 饱和度=\(metalParams.saturation)")
        print("🎨 [DEBUG] - 亮度=\(metalParams.brightness), 自然饱和度=\(metalParams.vibrance)")
        print("🎨 [DEBUG] - 色温=\(metalParams.temperature), 色调=\(metalParams.tint)")
        print("🎨 [DEBUG] - 高光=\(metalParams.highlights), 阴影=\(metalParams.shadows)")
        print("🎨 [DEBUG] - 白色=\(metalParams.whites), 黑色=\(metalParams.blacks)")
        print("🎨 [DEBUG] - 清晰度=\(metalParams.clarity), 去雾=\(metalParams.dehaze)")
        print("🎨 [DEBUG] - 褪色=\(metalParams.fadeEffect), 黑白=\(metalParams.monoEffect)")
        print("🎨 [DEBUG] - 伽马=\(metalParams.gamma), 线性输入=\(metalParams.isLinearInput)")
        print("🎨 [DEBUG] - 高光保护强度=\(metalParams.highlightProtectionIntensity), 模式=\(metalParams.highlightProtectionMode)")
        print("🎨 [DEBUG] - 曲线强度=\(metalParams.curveIntensity)") // 第3步添加
        print("🎨🎨🎨 [Metal渲染器] HSL参数传递:")
        print("🎨 [Metal渲染器] - 色相=\(metalParams.hue)°, 饱和度=\(metalParams.hslSaturation), 明度=\(metalParams.hslLuminance)")
        print("🎨 [Metal渲染器] - 选中颜色索引=\(metalParams.selectedHSLColorIndex), 柔和度=\(metalParams.hslColorRangeSoftness)")
        print("🎨🎨🎨 [Metal渲染器] 色调分离参数传递:")
        print("🎨 [Metal渲染器] - 高光: 色相=\(metalParams.highlightHue)°, 饱和度=\(metalParams.highlightSaturation)")
        print("🎨 [Metal渲染器] - 阴影: 色相=\(metalParams.shadowHue)°, 饱和度=\(metalParams.shadowSaturation)")
        print("🎨 [Metal渲染器] - 平衡=\(metalParams.splitToningBalance) (分界点: \(0.5 + metalParams.splitToningBalance * 0.3))")
        print("🎨🎨🎨 [Metal渲染器] 细节调整参数传递:")
        print("🎨 [Metal渲染器] - 锐化=\(metalParams.sharpness), 暗角强度=\(metalParams.vignetteIntensity)")
        print("🎨 [Metal渲染器] - 暗角半径=\(metalParams.vignetteRadius), 降噪=\(metalParams.noiseReduction)")

        // 检查是否有HSL效果
        let hasHSLEffect = metalParams.hue != 0.0 || metalParams.hslSaturation != 0.0 || metalParams.hslLuminance != 0.0
        print("🎨 [Metal渲染器] - 是否有HSL效果: \(hasHSLEffect)")

        // 检查是否有色调分离效果
        let hasSplitToning = metalParams.highlightHue != 0.0 || metalParams.highlightSaturation != 0.0 ||
                            metalParams.shadowHue != 0.0 || metalParams.shadowSaturation != 0.0 ||
                            metalParams.splitToningBalance != 0.0
        print("🎨 [Metal渲染器] - 是否有色调分离效果: \(hasSplitToning)")

        // 检查是否有细节调整效果
        let hasDetailEffect = metalParams.sharpness != 0.0 || metalParams.vignetteIntensity != 0.0 || metalParams.noiseReduction != 0.0
        print("🎨 [Metal渲染器] - 是否有细节调整效果: \(hasDetailEffect)")

        // 🔍 锐化参数调试信息
        print("🔍 [锐化调试] Swift参数: \(currentParameters.sharpness)")
        print("🔍 [锐化调试] Metal参数: \(metalParams.sharpness)")
        print("🔍 [锐化调试] 参数转换: \(currentParameters.sharpness) / 100.0 = \(metalParams.sharpness)")
        print("🔍 [锐化调试] 清晰度参数对比: Swift=\(currentParameters.clarity), Metal=\(metalParams.clarity)")
        print("🔍 [锐化调试] 锐化是否会被处理: \(metalParams.sharpness != 0.0 ? "是" : "否")")
        print("🔍 [锐化调试] 清晰度是否会被处理: \(metalParams.clarity != 0.0 ? "是" : "否")")

        return ["params": metalParams]
    }

    // MARK: - 兼容性接口
    

}

// MARK: - 扩展支持

extension MetalFilterRenderer {

    /// 导出高质量图像
    func exportHighQualityImage() -> UIImage? {
        guard let baseImage = baseImage else { return nil }
        
        do {
            // 使用原始分辨率进行高质量渲染
            try metalEngine.setInputImage(baseImage)
            
            let outputTexture = try metalEngine.executeFilter(
                functionName: "comprehensive_filter",
                parameters: createShaderParameters()
            )
            
            return try metalEngine.textureToUIImage(outputTexture)
        } catch {
            print("❌ MetalFilterRenderer: 高质量导出失败 - \(error)")
            return baseImage
        }
    }
    
    /// 重置所有滤镜
    func resetFilters() {
        currentParameters = FilterParameters()
        renderCurrentEffect()
    }
    
    /// 获取当前参数副本
    func getCurrentParameters() -> FilterParameters {
        return currentParameters
    }
}
