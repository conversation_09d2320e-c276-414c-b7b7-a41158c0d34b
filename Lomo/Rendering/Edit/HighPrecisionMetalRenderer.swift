import Foundation
import UIKit
import Metal
import MetalKit
import CoreImage

/// 高精度Metal渲染器 - 专门处理RAW数据和线性空间调整
class HighPrecisionMetalRenderer {
    
    // MARK: - 属性
    
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    private var pipelineStates: [String: MTLComputePipelineState] = [:]
    
    // 纹理管理
    private var inputTexture: MTLTexture?
    private var linearTexture: MTLTexture?      // 线性空间中间纹理
    private var outputTexture: MTLTexture?      // sRGB输出纹理
    
    // 当前数据
    private var currentParameters: FilterParameters = FilterParameters()
    private var isLinearInput: Bool = false
    
    // MARK: - 初始化
    
    init() throws {
        guard let device = MTLCreateSystemDefaultDevice() else {
            throw MetalError.deviceNotSupported
        }
        
        self.device = device
        
        guard let commandQueue = device.makeCommandQueue() else {
            throw MetalError.commandQueueCreationFailed
        }
        
        self.commandQueue = commandQueue
        
        guard let library = device.makeDefaultLibrary() else {
            throw MetalError.libraryNotFound
        }
        
        self.library = library
        
        print("🎨 [HighPrecisionMetalRenderer] 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 设置输入图像
    /// - Parameters:
    ///   - image: 输入图像
    ///   - isLinear: 是否为线性空间图像
    func setInputImage(_ image: UIImage, isLinear: Bool = false) throws {
        print("🎨 [HighPrecisionMetalRenderer] 设置输入图像 - 线性空间: \(isLinear)")
        
        self.isLinearInput = isLinear
        
        // 创建高精度纹理
        try createHighPrecisionTextures(from: image)
        
        print("🎨 [HighPrecisionMetalRenderer] 输入图像设置完成")
    }
    
    /// 处理图像并生成预览
    /// - Parameter parameters: 滤镜参数
    /// - Returns: sRGB预览图像
    func processForPreview(_ parameters: FilterParameters) throws -> UIImage {
        self.currentParameters = parameters
        
        print("🎨 [HighPrecisionMetalRenderer] 开始处理预览")
        
        // 第一步：在线性空间处理
        try processInLinearSpace()
        
        // 第二步：转换为sRGB用于预览
        try convertLinearToSRGBForPreview()
        
        // 第三步：生成UIImage
        return try generateUIImage(from: outputTexture!)
    }
    
    /// 处理图像并生成最终输出
    /// - Parameters:
    ///   - parameters: 滤镜参数
    ///   - highQuality: 是否使用高质量模式
    /// - Returns: 最终输出图像
    func processForFinalOutput(_ parameters: FilterParameters, highQuality: Bool = true) throws -> UIImage {
        self.currentParameters = parameters
        
        print("🎨 [HighPrecisionMetalRenderer] 开始处理最终输出 - 高质量: \(highQuality)")
        
        // 在线性空间处理
        try processInLinearSpace()
        
        // 高质量转换为sRGB
        try convertLinearToSRGBForOutput(highQuality: highQuality)
        
        // 生成高质量UIImage
        return try generateHighQualityUIImage(from: outputTexture!)
    }
    
    // MARK: - 私有方法
    
    /// 创建高精度纹理
    private func createHighPrecisionTextures(from image: UIImage) throws {
        let width = Int(image.size.width)
        let height = Int(image.size.height)
        
        // 输入纹理 - 使用16位浮点格式以保持精度
        let inputDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba16Float,  // 16位浮点，保持高精度
            width: width,
            height: height,
            mipmapped: false
        )
        inputDescriptor.usage = [.shaderRead, .shaderWrite]
        inputDescriptor.storageMode = .private
        
        guard let inputTex = device.makeTexture(descriptor: inputDescriptor) else {
            throw MetalError.textureCreationFailed
        }
        
        // 线性空间中间纹理 - 同样使用16位浮点
        let linearDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba16Float,
            width: width,
            height: height,
            mipmapped: false
        )
        linearDescriptor.usage = [.shaderRead, .shaderWrite]
        linearDescriptor.storageMode = .private
        
        guard let linearTex = device.makeTexture(descriptor: linearDescriptor) else {
            throw MetalError.textureCreationFailed
        }
        
        // 输出纹理 - sRGB格式
        let outputDescriptor = MTLTextureDescriptor.texture2DDescriptor(
            pixelFormat: .rgba8Unorm,
            width: width,
            height: height,
            mipmapped: false
        )
        outputDescriptor.usage = [.shaderRead, .shaderWrite]
        outputDescriptor.storageMode = .private
        
        guard let outputTex = device.makeTexture(descriptor: outputDescriptor) else {
            throw MetalError.textureCreationFailed
        }
        
        self.inputTexture = inputTex
        self.linearTexture = linearTex
        self.outputTexture = outputTex
        
        // 将UIImage数据复制到输入纹理
        try copyImageToTexture(image, to: inputTex)
        
        print("🎨 [HighPrecisionMetalRenderer] 高精度纹理创建完成")
        print("🎨 [HighPrecisionMetalRenderer] 输入格式: \(inputTex.pixelFormat)")
        print("🎨 [HighPrecisionMetalRenderer] 线性格式: \(linearTex.pixelFormat)")
        print("🎨 [HighPrecisionMetalRenderer] 输出格式: \(outputTex.pixelFormat)")
    }
    
    /// 在线性空间处理
    private func processInLinearSpace() throws {
        guard let inputTex = inputTexture,
              let linearTex = linearTexture else {
            throw MetalError.textureNotSet
        }
        
        print("🎨 [HighPrecisionMetalRenderer] 在线性空间处理")
        
        // 如果输入不是线性空间，先转换
        let sourceTexture: MTLTexture
        if !isLinearInput {
            // 先转换为线性空间
            try convertSRGBToLinear(from: inputTex, to: linearTex)
            sourceTexture = linearTex
        } else {
            sourceTexture = inputTex
        }
        
        // 在线性空间应用滤镜
        try applyLinearSpaceFilters(from: sourceTexture, to: linearTex)
    }
    
    /// 应用线性空间滤镜
    private func applyLinearSpaceFilters(from input: MTLTexture, to output: MTLTexture) throws {
        let pipelineState = try getOrCreatePipelineState(functionName: "linear_raw_filter")
        
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalError.commandBufferCreationFailed
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(input, index: 0)
        encoder.setTexture(output, index: 1)
        
        // 设置线性空间参数
        var linearParams = createLinearSpaceParameters()
        encoder.setBytes(&linearParams, length: MemoryLayout<LinearFilterParameters>.size, index: 0)
        
        // 计算线程组
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (input.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (input.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            print("❌ [HighPrecisionMetalRenderer] 线性空间处理失败: \(error)")
            throw MetalError.processingFailed
        }
        
        print("🎨 [HighPrecisionMetalRenderer] 线性空间处理完成")
    }
    
    /// 转换线性空间到sRGB（预览用）
    private func convertLinearToSRGBForPreview() throws {
        guard let linearTex = linearTexture,
              let outputTex = outputTexture else {
            throw MetalError.textureNotSet
        }
        
        try convertLinearToSRGB(from: linearTex, to: outputTex, highQuality: false)
    }
    
    /// 转换线性空间到sRGB（输出用）
    private func convertLinearToSRGBForOutput(highQuality: Bool) throws {
        guard let linearTex = linearTexture,
              let outputTex = outputTexture else {
            throw MetalError.textureNotSet
        }
        
        try convertLinearToSRGB(from: linearTex, to: outputTex, highQuality: highQuality)
    }
    
    /// 线性空间到sRGB转换
    private func convertLinearToSRGB(from input: MTLTexture, to output: MTLTexture, highQuality: Bool) throws {
        let functionName = highQuality ? "linear_to_srgb_converter" : "linear_to_srgb_converter"
        let pipelineState = try getOrCreatePipelineState(functionName: functionName)
        
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalError.commandBufferCreationFailed
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(input, index: 0)
        encoder.setTexture(output, index: 1)
        
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (input.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (input.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            print("❌ [HighPrecisionMetalRenderer] 线性到sRGB转换失败: \(error)")
            throw MetalError.processingFailed
        }
        
        print("🎨 [HighPrecisionMetalRenderer] 线性到sRGB转换完成")
    }
    
    /// sRGB到线性空间转换
    private func convertSRGBToLinear(from input: MTLTexture, to output: MTLTexture) throws {
        let pipelineState = try getOrCreatePipelineState(functionName: "srgb_to_linear_converter")
        
        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            throw MetalError.commandBufferCreationFailed
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(input, index: 0)
        encoder.setTexture(output, index: 1)
        
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (input.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (input.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )
        
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            print("❌ [HighPrecisionMetalRenderer] sRGB到线性转换失败: \(error)")
            throw MetalError.processingFailed
        }
        
        print("🎨 [HighPrecisionMetalRenderer] sRGB到线性转换完成")
    }
    
    /// 创建线性空间参数
    private func createLinearSpaceParameters() -> LinearFilterParameters {
        return LinearFilterParameters(
            exposure: currentParameters.exposure,
            contrast: currentParameters.contrast / 100.0,
            brightness: currentParameters.brightness / 100.0,
            saturation: currentParameters.saturation / 100.0,
            vibrance: currentParameters.vibrance / 100.0,
            temperature: currentParameters.temperature / 100.0,
            tint: currentParameters.tint / 100.0,
            highlights: currentParameters.highlights / 100.0,
            shadows: currentParameters.shadows / 100.0,
            whites: currentParameters.whites / 100.0,
            blacks: currentParameters.blacks / 100.0,
            clarity: currentParameters.clarity / 100.0,
            dehaze: currentParameters.dehaze / 100.0,
            gamma: currentParameters.gamma,
            isLinearInput: isLinearInput ? 1.0 : 0.0
        )
    }
    
    // MARK: - 辅助方法
    
    /// 获取或创建管线状态
    private func getOrCreatePipelineState(functionName: String) throws -> MTLComputePipelineState {
        if let cached = pipelineStates[functionName] {
            return cached
        }
        
        guard let function = library.makeFunction(name: functionName) else {
            throw MetalError.functionNotFound(functionName)
        }
        
        let pipelineState = try device.makeComputePipelineState(function: function)
        pipelineStates[functionName] = pipelineState
        
        return pipelineState
    }
    
    /// 复制图像到纹理
    private func copyImageToTexture(_ image: UIImage, to texture: MTLTexture) throws {
        // 使用Core Image进行高质量转换
        guard let cgImage = image.cgImage else {
            throw MetalError.invalidImage
        }
        
        let ciImage = CIImage(cgImage: cgImage)
        let context = CIContext(mtlDevice: device)
        
        try context.render(ciImage, to: texture, commandBuffer: nil, bounds: ciImage.extent, colorSpace: cgImage.colorSpace ?? CGColorSpace(name: CGColorSpace.sRGB)!)
    }
    
    /// 生成UIImage
    private func generateUIImage(from texture: MTLTexture) throws -> UIImage {
        let ciImage = CIImage(mtlTexture: texture, options: [
            .colorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        guard let outputCIImage = ciImage else {
            throw MetalError.textureConversionFailed
        }
        
        let context = CIContext(mtlDevice: device, options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent) else {
            throw MetalError.cgImageCreationFailed
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    /// 生成高质量UIImage
    private func generateHighQualityUIImage(from texture: MTLTexture) throws -> UIImage {
        let ciImage = CIImage(mtlTexture: texture, options: [
            .colorSpace: CGColorSpace(name: CGColorSpace.sRGB)!
        ])
        
        guard let outputCIImage = ciImage else {
            throw MetalError.textureConversionFailed
        }
        
        // 高质量上下文
        let context = CIContext(mtlDevice: device, options: [
            .workingColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .outputColorSpace: CGColorSpace(name: CGColorSpace.sRGB)!,
            .useSoftwareRenderer: false,
            .highQualityDownsample: true
        ])
        
        guard let cgImage = context.createCGImage(outputCIImage, from: outputCIImage.extent, format: .RGBA8, colorSpace: CGColorSpace(name: CGColorSpace.sRGB)) else {
            throw MetalError.cgImageCreationFailed
        }
        
        return UIImage(cgImage: cgImage)
    }
}

// MARK: - 线性空间参数结构体

struct LinearFilterParameters {
    var exposure: Float
    var contrast: Float
    var brightness: Float
    var saturation: Float
    var vibrance: Float
    var temperature: Float
    var tint: Float
    var highlights: Float
    var shadows: Float
    var whites: Float
    var blacks: Float
    var clarity: Float
    var dehaze: Float
    var gamma: Float
    var isLinearInput: Float
}

// MARK: - 错误定义

enum MetalError: Error {
    case deviceNotSupported
    case commandQueueCreationFailed
    case libraryNotFound
    case functionNotFound(String)
    case textureCreationFailed
    case textureNotSet
    case commandBufferCreationFailed
    case invalidImage
    case textureConversionFailed
    case cgImageCreationFailed
    case processingFailed
}
