//
//  LomoApp.swift
//  Lomo
//
//  Created by LH on 2025/2/15.
//

import SwiftUI
import SwiftData
import UIKit

// 添加AppDelegate来实现方向控制
class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        // 仅允许竖屏方向
        return .portrait
    }
}

@main
struct LomoApp: App {
    // 使用UIApplicationDelegate
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    
    // 共享数据容器
    private var sharedModelContainer: ModelContainer
    
    // 订阅管理器（通过依赖注入容器获取）
    @StateObject private var subscriptionManager = SubscriptionDependencyContainer.shared.subscriptionService as! SubscriptionService
    
    // 在 App 层创建 CameraViewModel
    @StateObject private var cameraViewModel: CameraViewModel
    
    init() {
        // 设置共享数据容器
        sharedModelContainer = SharedService.shared.container
        
        // 注册自定义字体
        FontUtils.registerCustomFonts()
        
        // 打印所有可用字体，用于调试
        for family in UIFont.familyNames.sorted() {
            let names = UIFont.fontNames(forFamilyName: family)
            print("字体家族: \(family) 字体: \(names)")
        }
        
        // !! 移除启动时重置调节设置的逻辑 !!
        // AdjustSettingsManager.shared.resetToDefaults()
        
        // --- 从 CameraView init() 复制过来的代码 开始 ---
        // 这里直接使用 ServiceFactory 创建所有服务，更符合依赖注入原则
        // 使用SessionManager代替直接创建session
        // 创建 CameraViewModel 实例
        _cameraViewModel = StateObject(wrappedValue: CameraViewModel())
        // --- 从 CameraView init() 复制过来的代码 结束 ---
        
        // 确保设备方向通知已开启 (这段逻辑留在 CameraViewModel 内部处理可能更合适，暂时保留在 ViewModel 的 init 中)
        // if !UIDevice.current.isGeneratingDeviceOrientationNotifications {
        //     UIDevice.current.beginGeneratingDeviceOrientationNotifications()
        // }
    }
    
    var body: some Scene {
        WindowGroup {
            ZStack {
                // 使用新的AppContainerView替代直接的CameraView
                AppContainerView(cameraViewModel: cameraViewModel)
                    .preferredColorScheme(.dark)
                    .fullScreenCover(isPresented: $subscriptionManager.showProView) {
                        SubscriptionDependencyContainer.subscriptionView()
                    }
            }
        }
        .modelContainer(sharedModelContainer)
    }
}
