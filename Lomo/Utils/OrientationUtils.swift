import SwiftUI
import UIKit

struct OrientationUtils {
    static func rotationAngleForOrientation(_ orientation: UIDeviceOrientation) -> <PERSON><PERSON> {
        switch orientation {
        case .landscapeLeft:
            return .degrees(90) // Home button on right
        case .landscapeRight:
            return .degrees(-90) // Home button on left
        case .portrait:
            return .degrees(0)
        default: // Includes portraitUpsideDown, faceUp, faceDown, unknown
            return .degrees(0) // Default to no rotation for other states
        }
    }
    
    // Copied from CameraView
    static func isLandscape(_ orientation: UIDeviceOrientation) -> Bool {
        return orientation == .landscapeLeft || orientation == .landscapeRight
    }
} 