import SwiftUI
import UIKit

/// 字体工具类，用于管理应用中使用的自定义字体
enum FontUtils {
    /// LCD点阵字体名称 - 用于第一个时间戳风格 (digital_orange)
    static let lcdDotFontName = "LCDDot"
    
    /// 数字7字体名称 - 用于第二个时间戳风格 (digital_red)
    static let digital7FontName = "Digital-7MonoItalic"
    
    /// Cyber字体名称 - 用于第三个时间戳风格 (digital_cyber)
    static let cyberFontName = "Cyber"
    
    /// 注册所有自定义字体
    static func registerCustomFonts() {
        // 使用统一的方法从Assets.xcassets中注册所有字体
        
        // 注册时间戳相关字体
        registerFontFromAssets(name: "lcddot")
        registerFontFromAssets(name: "digital-7.monoitalic")
        registerFontFromAssets(name: "Cyber")
        
        // 注册FuturaPT系列字体
        registerFontFromAssets(name: "FuturaPT-Light")
        registerFontFromAssets(name: "FuturaPT-LightObl")
        registerFontFromAssets(name: "FuturaPT-Book")
        registerFontFromAssets(name: "FuturaPT-BookObl")
        registerFontFromAssets(name: "FuturaPT-Medium")
        registerFontFromAssets(name: "FuturaPT-MediumObl")
        registerFontFromAssets(name: "FuturaPT-Demi")  // Demi对应Bold
        registerFontFromAssets(name: "FuturaPT-DemiObl") // DemiObl对应BoldObl
        
        // 注册TYPOGRAPH PRO系列字体
        registerFontFromAssets(name: "TYPOGRAPH PRO Light")
        registerFontFromAssets(name: "TYPOGRAPH PRO Light Italic")
        registerFontFromAssets(name: "TYPOGRAPH PRO Semi Bold")
        registerFontFromAssets(name: "TYPOGRAPH PRO Semi Bold Italic")
        registerFontFromAssets(name: "TYPOGRAPH PRO Ultra Light")
        registerFontFromAssets(name: "TYPOGRAPH PRO Ultra Light Italic")
        registerFontFromAssets(name: "TYPOGRAPH PRO Extra Bold")
        
        // 注册CourierPrimeCode系列字体
        registerFontFromAssets(name: "CourierPrimeCode-Regular")
        registerFontFromAssets(name: "CourierPrimeCode-Italic")
        
        // 注册Miamo系列字体
        registerFontFromAssets(name: "Miamo Thin")
        registerFontFromAssets(name: "Miamo Light")
        registerFontFromAssets(name: "Miamo Regular")
        registerFontFromAssets(name: "Miamo Medium")
        registerFontFromAssets(name: "Miamo SemiBold")
        registerFontFromAssets(name: "Miamo Bold")
        
        // 注册ADELE字体
        registerFontFromAssets(name: "ADELE-Light")
        
        // 注册Quantico系列字体
        registerFontFromAssets(name: "Quantico-Regular")
        registerFontFromAssets(name: "Quantico-Bold")
        registerFontFromAssets(name: "Quantico-Italic")
        registerFontFromAssets(name: "Quantico-BoldItalic")
        
        // 注册Syntax系列字体
        registerFontFromAssets(name: "Syntax-Roman")
        registerFontFromAssets(name: "Syntax-Bold")
        registerFontFromAssets(name: "Syntax-Italic")
        registerFontFromAssets(name: "Syntax-Black")
        registerFontFromAssets(name: "Syntax-UltraBlack")
        
        // 注册Makinas系列字体
        registerFontFromAssets(name: "Makinas-4-Flat")
        registerFontFromAssets(name: "Makinas-4-Square")
        
        // 注册鸿蒙字体系列
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Thin")
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Light")
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Regular")
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Medium")
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Bold")
        registerFontFromAssets(name: "HarmonyOS_Sans_SC_Black")
        
        // 注册荣耀字体系列
        registerFontFromAssets(name: "HONORSansCN-Thin")
        registerFontFromAssets(name: "HONORSansCN-ExtraLight")
        registerFontFromAssets(name: "HONORSansCN-Light")
        registerFontFromAssets(name: "HONORSansCN-Regular")
        registerFontFromAssets(name: "HONORSansCN-Medium")
        registerFontFromAssets(name: "HONORSansCN-DemiBold")
        registerFontFromAssets(name: "HONORSansCN-Bold")
        registerFontFromAssets(name: "HONORSansCN-ExtraBold")
        registerFontFromAssets(name: "HONORSansCN-Heavy")
        
        // 注册思源黑体系列
        registerFontFromAssets(name: "SourceHanSansSC-ExtraLight")
        registerFontFromAssets(name: "SourceHanSansSC-Light")
        registerFontFromAssets(name: "SourceHanSansSC-Normal")
        registerFontFromAssets(name: "SourceHanSansSC-Regular")
        registerFontFromAssets(name: "SourceHanSansSC-Medium")
        registerFontFromAssets(name: "SourceHanSansSC-Bold")
        registerFontFromAssets(name: "SourceHanSansSC-Heavy")
        
        // 注册苹方字体系列
        registerFontFromAssets(name: "PingFang-SC-Ultralight")
        registerFontFromAssets(name: "PingFang-SC-Thin")
        registerFontFromAssets(name: "PingFang-SC-Light")
        registerFontFromAssets(name: "PingFang-SC-Regular")
        registerFontFromAssets(name: "PingFang-SC-Medium")
        registerFontFromAssets(name: "PingFang-SC-Semibold")
        
        // 可以在此处添加其他需要注册的字体，全部使用registerFontFromAssets方法
        
        // 打印所有可用字体，用于调试
        #if DEBUG
        for family in UIFont.familyNames.sorted() {
            let names = UIFont.fontNames(forFamilyName: family)
            print("字体家族: \(family) 字体: \(names)")
        }
        #endif
    }
    
    /// 根据时间戳样式获取对应的字体
    /// - Parameters:
    ///   - style: 时间戳样式标识符
    ///   - size: 字体大小
    /// - Returns: 对应的SwiftUI字体
    static func fontForTimeStyle(_ style: String, size: CGFloat) -> Font {
        switch style {
        case "digital_orange":
            return Font.custom(lcdDotFontName, size: size)
        case "digital_red":
            return Font.custom(digital7FontName, size: size)
        case "digital_cyber":
            return Font.custom(cyberFontName, size: size)
        default:
            // 默认返回系统字体作为后备
            return Font.system(size: size, weight: .medium)
        }
    }
    
    /// 从Assets.xcassets中注册字体
    /// - Parameter name: 字体文件名（与Assets中的数据集名称一致）
    private static func registerFontFromAssets(name: String) {
        guard let asset = NSDataAsset(name: name) else {
            print("⚠️ 无法从Assets.xcassets加载字体数据: \(name)")
            return
        }
        
        guard let dataProvider = CGDataProvider(data: asset.data as CFData),
              let cgFont = CGFont(dataProvider) else {
            print("⚠️ 无法创建CGFont: \(name)")
            return
        }
        
        var error: Unmanaged<CFError>?
        if !CTFontManagerRegisterGraphicsFont(cgFont, &error) {
            print("⚠️ 无法注册Assets中的字体: \(name)")
            if let errorDescription = error?.takeRetainedValue() {
                print("错误信息: \(errorDescription)")
            }
        } else {
            print("✅ 成功从Assets注册字体: \(name)")
        }
    }
    
    /// 获取当前格式化的时间日期字符串
    /// - Parameter style: 时间戳样式
    /// - Returns: 根据样式格式化的时间字符串
    static func getCurrentTimeString(style: String = "") -> String {
        let dateFormatter = DateFormatter()
        
        // 根据不同的样式使用不同的日期格式
        if style == "digital_red" {
            // 第二个选项 (digital_red) 使用无空格格式
            dateFormatter.dateFormat = "Mdd''yy"
        } else if style == "digital_orange" {
            // 第一个选项 (digital_orange) 使用更大间距格式
            dateFormatter.dateFormat = "M   dd   ''   yy"
        } else {
            // 第三个选项 (digital_cyber) 使用标准空格格式
            dateFormatter.dateFormat = "M dd '' yy"
        }
        
        return dateFormatter.string(from: Date())
    }
    
    /// 获取格式化的日期组件（月、日、年）
    /// - Returns: 包含格式化后的月、日、年字符串的元组 (month: String, day: String, year: String)
    static func getFormattedDateComponents() -> (month: String, day: String, year: String) {
        let calendar = Calendar.current
        let date = Date()
        let components = calendar.dateComponents([.month, .day, .year], from: date)

        let month = components.month ?? 1 // Default to 1 if nil
        let day = components.day ?? 1   // Default to 1 if nil
        let year = (components.year ?? 0) % 100 // Get last two digits, default to 00 if nil

        // Format components: Month as is, Day with two digits, Year with two digits prefixed by '
        let monthString = "\(month)"
        let dayString = String(format: "%02d", day)
        let yearString = String(format: "%02d", year)

        return (month: monthString, day: dayString, year: yearString)
    }
    
    /// 根据颜色名称获取对应的颜色对象
    /// - Parameter colorName: 颜色名称
    /// - Returns: 对应的SwiftUI Color对象
    static func getColorByName(_ colorName: String) -> Color {
        switch colorName {
        case "red":
            return .red
        case "orange":
            return .orange
        case "yellow":
            return .yellow
        case "blue":
            return .blue
        case "purple":
            return .purple
        case "cyan":
            return .cyan
        case "magenta":
            return Color(red: 255/255, green: 41/255, blue: 117/255) // 霓虹粉红色 (RGB: 255,41,117)
        default:
            return .white
        }
    }
} 