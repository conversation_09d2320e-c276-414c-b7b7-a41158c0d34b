import SwiftUI
import UIKit

struct LayoutUtils {
    
    // Copied from LandscapeRecordingInfoView / RecordingInfoView - getPreciseRemainingTimeOffset
    static func calculateLeftAlignedOffsetForRotation(size: CGSize, orientation: UIDeviceOrientation) -> CGSize {
        guard size != .zero else { return .zero }
        let H = size.height
        let W = size.width
        var dx: CGFloat = 0
        var dy: CGFloat = 0

        switch orientation {
        case .landscapeLeft, .landscapeRight:
            // Horizontal offset needed to align left edge correctly after rotation
            dx = (H - W) / 2
            // Vertical offset needed to adjust vertical center after rotation
            dy = (W - H) / 2
        default:
            dx = 0
            dy = 0
        }
        return CGSize(width: dx, height: dy)
    }

    // Copied from LandscapeRecordingInfoView / RecordingInfoView - getPreciseRecordingTimeOffset
    static func calculateRightAlignedOffsetForRotation(size: CGSize, orientation: UIDeviceOrientation) -> CGSize {
        guard size != .zero else { return .zero }
        let H = size.height
        let W = size.width
        var dx: CGFloat = 0
        var dy: CGFloat = 0

        switch orientation {
        case .landscapeLeft, .landscapeRight:
            // Horizontal offset needed to align right edge correctly after rotation
            dx = -(H - W) / 2
            // Vertical offset needed to adjust vertical center after rotation
            dy = (W - H) / 2
        default:
            dx = 0
            dy = 0
        }
        return CGSize(width: dx, height: dy)
    }

    // 从CameraView复制的音频波形偏移计算
    static func calculateAudioWaveformOffset(screenHeight: CGFloat, screenWidth: CGFloat, isLandscape: Bool) -> CGFloat {
        let waveformHeight = screenHeight * UIConstants.waveformHeight
        let waveformWidth = screenWidth * UIConstants.waveformWidth
        
        if isLandscape {
            return -((waveformHeight / 2) - (waveformWidth / 2))
        }
        
        return 0
    }

    // 从 HistogramView 复制的偏移计算
    static func calculateHistogramOffsetForRotation(screenHeight: CGFloat, screenWidth: CGFloat, orientation: UIDeviceOrientation) -> CGSize {
        // 恢复使用 HistogramView 中原始的硬编码值
        let histogramWidth = screenWidth * 0.12
        let histogramHeight = screenHeight * 0.04

        switch orientation {
        case .landscapeLeft, .landscapeRight:
            // 横屏模式下使用相同的偏移量，确保位置一致
            let offsetValue = (histogramHeight - histogramWidth) / 2
            return CGSize(width: offsetValue, height: offsetValue)
        default:
            return CGSize.zero
        }
    }
} 