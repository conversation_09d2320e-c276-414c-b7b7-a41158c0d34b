import Foundation
import Metal
import MetalKit
import UIKit

/// Metal曲线验证器 - 用于测试第1步基础功能
/// 验证Metal着色器编译和基础功能是否正确工作
class MetalCurveValidator {
    
    // MARK: - Metal资源
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    
    // MARK: - 验证结果
    struct ValidationResult {
        let isSuccess: Bool
        let message: String
        let details: [String]
        let executionTime: TimeInterval
    }
    
    // MARK: - 初始化
    init?() {
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("❌ Metal设备不可用")
            return nil
        }
        self.device = device
        
        guard let commandQueue = device.makeCommandQueue() else {
            print("❌ 无法创建Metal命令队列")
            return nil
        }
        self.commandQueue = commandQueue
        
        guard let library = device.makeDefaultLibrary() else {
            print("❌ 无法加载Metal库")
            return nil
        }
        self.library = library
        
        print("✅ MetalCurveValidator 初始化成功")
    }
    
    // MARK: - 主要验证方法
    
    /// 验证所有基础功能
    func validateAllBasicFunctions() -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        var details: [String] = []
        var allSuccess = true
        
        print("🧪 开始验证Metal曲线基础功能...")
        
        // 1. 验证着色器编译
        let compileResult = validateShaderCompilation()
        details.append("着色器编译: \(compileResult.isSuccess ? "✅" : "❌") \(compileResult.message)")
        if !compileResult.isSuccess {
            allSuccess = false
        }
        
        // 2. 验证LUT采样
        if compileResult.isSuccess {
            let lutResult = validateLUTSampling()
            details.append("LUT采样: \(lutResult.isSuccess ? "✅" : "❌") \(lutResult.message)")
            if !lutResult.isSuccess {
                allSuccess = false
            }
        }
        
        // 3. 验证基础曲线应用
        if allSuccess {
            let curveResult = validateBasicCurveApplication()
            details.append("基础曲线应用: \(curveResult.isSuccess ? "✅" : "❌") \(curveResult.message)")
            if !curveResult.isSuccess {
                allSuccess = false
            }
        }
        
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        
        let finalMessage = allSuccess ? 
            "🎉 所有基础功能验证通过！" : 
            "⚠️ 部分功能验证失败，请检查详细信息"
        
        return ValidationResult(
            isSuccess: allSuccess,
            message: finalMessage,
            details: details,
            executionTime: executionTime
        )
    }
    
    // MARK: - 具体验证方法
    
    /// 验证着色器编译
    private func validateShaderCompilation() -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        var details: [String] = []
        
        // 检查基础曲线内核
        guard let basicCurveFunction = library.makeFunction(name: "apply_curve_basic") else {
            return ValidationResult(
                isSuccess: false,
                message: "无法找到apply_curve_basic函数",
                details: ["请检查CurveShaders.metal文件是否正确添加到项目中"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("apply_curve_basic函数加载成功")
        
        // 检查LUT测试内核
        guard let lutTestFunction = library.makeFunction(name: "test_lut_sampling") else {
            return ValidationResult(
                isSuccess: false,
                message: "无法找到test_lut_sampling函数",
                details: details + ["LUT测试函数缺失"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("test_lut_sampling函数加载成功")
        
        // 尝试创建计算管线状态
        do {
            let _ = try device.makeComputePipelineState(function: basicCurveFunction)
            details.append("基础曲线管线状态创建成功")
            
            let _ = try device.makeComputePipelineState(function: lutTestFunction)
            details.append("LUT测试管线状态创建成功")
            
        } catch {
            return ValidationResult(
                isSuccess: false,
                message: "管线状态创建失败: \(error.localizedDescription)",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        
        return ValidationResult(
            isSuccess: true,
            message: "所有着色器编译成功",
            details: details,
            executionTime: CFAbsoluteTimeGetCurrent() - startTime
        )
    }
    
    /// 验证LUT采样功能
    private func validateLUTSampling() -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        var details: [String] = []
        
        // 创建测试LUT - 简单的S曲线
        let testLUT = createTestSCurveLUT()
        details.append("测试S曲线LUT创建完成，256个值")
        
        // 创建LUT缓冲区
        guard let lutBuffer = device.makeBuffer(
            bytes: testLUT,
            length: testLUT.count * MemoryLayout<Float>.size,
            options: []
        ) else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建LUT缓冲区",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("LUT缓冲区创建成功，大小: \(testLUT.count * 4)字节")
        
        // 创建测试纹理
        let textureDescriptor = MTLTextureDescriptor()
        textureDescriptor.pixelFormat = .rgba8Unorm
        textureDescriptor.width = 256
        textureDescriptor.height = 1
        textureDescriptor.usage = [.shaderWrite, .shaderRead]
        
        guard let testTexture = device.makeTexture(descriptor: textureDescriptor) else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建测试纹理",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("测试纹理创建成功，尺寸: 256x1")
        
        // 执行LUT采样测试
        guard let function = library.makeFunction(name: "test_lut_sampling"),
              let pipelineState = try? device.makeComputePipelineState(function: function),
              let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建LUT测试执行环境",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(testTexture, index: 0)
        encoder.setBuffer(lutBuffer, offset: 0, index: 0)
        
        let threadgroupSize = MTLSize(width: 16, height: 1, depth: 1)
        let threadgroupCount = MTLSize(width: (256 + 15) / 16, height: 1, depth: 1)
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            return ValidationResult(
                isSuccess: false,
                message: "LUT采样测试执行失败: \(error.localizedDescription)",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        
        details.append("LUT采样测试执行成功")
        
        return ValidationResult(
            isSuccess: true,
            message: "LUT采样功能验证通过",
            details: details,
            executionTime: CFAbsoluteTimeGetCurrent() - startTime
        )
    }
    
    /// 验证基础曲线应用
    private func validateBasicCurveApplication() -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        var details: [String] = []
        
        // 创建测试图像 - 简单的渐变
        let testImage = createTestGradientImage()
        details.append("测试渐变图像创建完成，尺寸: 256x256")
        
        // 转换为Metal纹理
        guard let inputTexture = createMetalTexture(from: testImage) else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建输入纹理",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("输入纹理创建成功")
        
        // 创建输出纹理
        let descriptor = MTLTextureDescriptor()
        descriptor.pixelFormat = inputTexture.pixelFormat
        descriptor.width = inputTexture.width
        descriptor.height = inputTexture.height
        descriptor.usage = [.shaderWrite, .shaderRead]
        
        guard let outputTexture = device.makeTexture(descriptor: descriptor) else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建输出纹理",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("输出纹理创建成功")
        
        // 创建测试参数和LUT
        let testParams = createTestCurveParameters()
        let testLUT = createTestSCurveLUT()
        
        guard let paramsBuffer = device.makeBuffer(
            bytes: [testParams],
            length: MemoryLayout<BasicCurveParameters>.size,
            options: []
        ),
        let lutBuffer = device.makeBuffer(
            bytes: testLUT,
            length: testLUT.count * MemoryLayout<Float>.size,
            options: []
        ) else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建参数缓冲区",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        details.append("参数和LUT缓冲区创建成功")
        
        // 执行曲线应用测试
        guard let function = library.makeFunction(name: "apply_curve_basic"),
              let pipelineState = try? device.makeComputePipelineState(function: function),
              let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return ValidationResult(
                isSuccess: false,
                message: "无法创建曲线应用执行环境",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        
        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(inputTexture, index: 0)
        encoder.setTexture(outputTexture, index: 1)
        encoder.setBuffer(paramsBuffer, offset: 0, index: 0)
        encoder.setBuffer(lutBuffer, offset: 0, index: 1)
        
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (inputTexture.width + 15) / 16,
            height: (inputTexture.height + 15) / 16,
            depth: 1
        )
        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()
        
        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()
        
        if let error = commandBuffer.error {
            return ValidationResult(
                isSuccess: false,
                message: "曲线应用测试执行失败: \(error.localizedDescription)",
                details: details,
                executionTime: CFAbsoluteTimeGetCurrent() - startTime
            )
        }
        
        details.append("基础曲线应用测试执行成功")
        
        return ValidationResult(
            isSuccess: true,
            message: "基础曲线应用功能验证通过",
            details: details,
            executionTime: CFAbsoluteTimeGetCurrent() - startTime
        )
    }

    // MARK: - 辅助方法

    /// 创建测试S曲线LUT
    private func createTestSCurveLUT() -> [Float] {
        var lut: [Float] = []
        lut.reserveCapacity(256)

        for i in 0..<256 {
            let x = Float(i) / 255.0
            // 简单的S曲线公式: y = 3x² - 2x³
            let y = 3.0 * x * x - 2.0 * x * x * x
            lut.append(y)
        }

        return lut
    }

    /// 创建测试渐变图像
    private func createTestGradientImage() -> UIImage {
        let size = CGSize(width: 256, height: 256)
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            let cgContext = context.cgContext

            // 创建水平渐变
            for x in 0..<256 {
                let gray = CGFloat(x) / 255.0
                cgContext.setFillColor(red: gray, green: gray, blue: gray, alpha: 1.0)
                cgContext.fill(CGRect(x: x, y: 0, width: 1, height: 256))
            }
        }
    }

    /// 创建Metal纹理从UIImage
    private func createMetalTexture(from image: UIImage) -> MTLTexture? {
        guard let cgImage = image.cgImage else { return nil }

        let textureLoader = MTKTextureLoader(device: device)
        do {
            return try textureLoader.newTexture(cgImage: cgImage, options: [.SRGB: false])
        } catch {
            print("❌ 纹理创建失败: \(error)")
            return nil
        }
    }

    /// 创建测试曲线参数
    private func createTestCurveParameters() -> BasicCurveParameters {
        return BasicCurveParameters(
            intensity: 1.0,
            padding1: 0.0,
            padding2: 0.0,
            padding3: 0.0
        )
    }
}

// MARK: - BasicCurveParameters结构体定义

/// 与Metal着色器中基础版本相同的参数结构体
struct BasicCurveParameters {
    let intensity: Float
    let padding1: Float
    let padding2: Float
    let padding3: Float
}
