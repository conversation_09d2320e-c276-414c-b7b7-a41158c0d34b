import Foundation

/// 简化的验证运行器 - 避免复杂的字符串操作
/// 提供最基本的验证功能测试
class SimpleValidationRunner {
    
    /// 运行基础验证测试
    static func runBasicValidation() {
        print("开始Metal曲线基础功能验证...")
        print("时间: \(Date())")
        print("")
        
        // 创建验证器
        guard let validator = MetalCurveValidator() else {
            print("❌ 验证器初始化失败")
            return
        }
        
        // 运行验证
        let result = validator.validateAllBasicFunctions()
        
        // 显示结果
        print("验证结果:")
        print("状态: \(result.isSuccess ? "✅ 成功" : "❌ 失败")")
        print("消息: \(result.message)")
        print("执行时间: \(String(format: "%.3f", result.executionTime))秒")
        
        print("\n详细信息:")
        for (index, detail) in result.details.enumerated() {
            print("  \(index + 1). \(detail)")
        }
        
        // 给出建议
        if result.isSuccess {
            print("\n🎉 验证成功！可以进入第2步开发")
        } else {
            print("\n⚠️ 验证失败，请检查上述问题")
        }
        
        print("\n验证完成")
    }
    
    /// 快速编译检查
    static func quickCheck() {
        print("⚡ 快速编译检查...")
        
        guard let validator = MetalCurveValidator() else {
            print("❌ 验证器不可用")
            return
        }
        
        let result = validator.validateAllBasicFunctions()
        
        if result.isSuccess {
            print("✅ 编译检查通过")
        } else {
            print("❌ 编译检查失败: \(result.message)")
        }
    }
    
    /// 性能测试
    static func performanceTest() {
        print("⏱ 性能测试...")
        
        guard let validator = MetalCurveValidator() else {
            print("❌ 验证器不可用")
            return
        }
        
        var totalTime: TimeInterval = 0
        let iterations = 3
        
        for i in 1...iterations {
            print("  第\(i)次测试...")
            let result = validator.validateAllBasicFunctions()
            totalTime += result.executionTime
            
            if !result.isSuccess {
                print("❌ 第\(i)次测试失败")
                return
            }
        }
        
        let averageTime = totalTime / Double(iterations)
        print("✅ 性能测试完成")
        print("📊 平均执行时间: \(String(format: "%.3f", averageTime))秒")
        
        if averageTime < 0.1 {
            print("🎯 性能评估: 优秀")
        } else if averageTime < 0.5 {
            print("🎯 性能评估: 良好")
        } else {
            print("🎯 性能评估: 需要优化")
        }
    }
}

// MARK: - 使用示例

/*
在ViewController中使用：

class ViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 运行基础验证
        SimpleValidationRunner.runBasicValidation()
    }
}

在AppDelegate中使用：

func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    
    #if DEBUG
    // 开发阶段快速检查
    SimpleValidationRunner.quickCheck()
    #endif
    
    return true
}

单独测试性能：
SimpleValidationRunner.performanceTest()
*/
