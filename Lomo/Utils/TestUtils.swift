import Foundation

/// 测试工具类
class TestUtils {
    
    /// 创建分隔线
    /// - Parameter character: 分隔字符
    /// - Parameter count: 重复次数
    /// - Returns: 分隔线字符串
    static func separator(_ character: String = "=", count: Int = 50) -> String {
        return String(repeating: character, count: count)
    }
    
    /// 创建短分隔线
    /// - Returns: 短分隔线字符串
    static func shortSeparator() -> String {
        return separator("-", count: 30)
    }
    
    /// 创建长分隔线
    /// - Returns: 长分隔线字符串
    static func longSeparator() -> String {
        return separator("=", count: 60)
    }
    
    /// 打印测试开始
    /// - Parameter testName: 测试名称
    static func printTestStart(_ testName: String) {
        print("🚀 [\(testName)] 开始测试...")
        print(longSeparator())
    }
    
    /// 打印测试结束
    /// - Parameter testName: 测试名称
    static func printTestEnd(_ testName: String) {
        print("🎉 [\(testName)] 测试完成")
        print(longSeparator())
    }
    
    /// 打印测试分组
    /// - Parameter groupName: 分组名称
    static func printTestGroup(_ groupName: String) {
        print(shortSeparator())
        print("🧪 测试分组: \(groupName)")
        print(shortSeparator())
    }
    
    /// 打印成功消息
    /// - Parameter message: 消息内容
    static func printSuccess(_ message: String) {
        print("✅ \(message)")
    }
    
    /// 打印失败消息
    /// - Parameter message: 消息内容
    static func printFailure(_ message: String) {
        print("❌ \(message)")
    }
    
    /// 打印警告消息
    /// - Parameter message: 消息内容
    static func printWarning(_ message: String) {
        print("⚠️ \(message)")
    }
    
    /// 打印信息消息
    /// - Parameter message: 消息内容
    static func printInfo(_ message: String) {
        print("ℹ️ \(message)")
    }
}
