import Foundation
import UIKit

/// 字体线粗细工具类 - 用于根据字体和粗细获取对应的分隔线宽度
class FontLineThicknessUtils {
    
    /// 获取指定字体和字体粗细对应的分隔线宽度
    /// - Parameters:
    ///   - fontName: 字体名称，如"PingFang-SC"
    ///   - fontWeight: 字体粗细名称，如"Regular"
    /// - Returns: 分隔线宽度，单位为点(points)
    static func getLineThickness(fontName: String, fontWeight: String) -> CGFloat {
        // 根据字体名称选择相应的映射表
        switch fontName {
        case "PingFang-SC":
            return getPingFangSCLineThickness(fontWeight: fontWeight)
        case "HarmonyOS_Sans_SC":
            return getHarmonyOSLineThickness(fontWeight: fontWeight)
        case "SourceHanSansSC":
            return getSourceHanSansSCLineThickness(fontWeight: fontWeight)
        case "HONORSansCN":
            return getHONORSansCNLineThickness(fontWeight: fontWeight)
        case "FuturaPT":
            return getFuturaPTLineThickness(fontWeight: fontWeight)
        case "TYPOGRAPH PRO":
            return getTypographProLineThickness(fontWeight: fontWeight)
        case "CourierPrimeCode":
            return getCourierPrimeCodeLineThickness(fontWeight: fontWeight)
        case "Miamo":
            return getMiamoLineThickness(fontWeight: fontWeight)
        case "ADELE":
            return getAdeleLineThickness(fontWeight: fontWeight)
        case "Quantico":
            return getQuanticoLineThickness(fontWeight: fontWeight)
        case "Syntax":
            return getSyntaxLineThickness(fontWeight: fontWeight)
        case "Makinas-Square", "Makinas-Flat":
            return getMakinasLineThickness(fontWeight: fontWeight)
        default:
            // 其他字体使用默认映射
            return getDefaultLineThickness(fontWeight: fontWeight)
        }
    }
    
    /// 获取PingFang SC字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getPingFangSCLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Ultralight":
            return 1.0
        case "Thin":
            return 1.25
        case "Light":
            return 1.25
        case "Regular":
            return 1.5
        case "Medium":
            return 1.5
        case "Semibold":
            return 2.0
        default:
            return 1.5  // 默认为Regular粗细
        }
    }
    
    /// 获取HarmonyOS_Sans_SC字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getHarmonyOSLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Thin":
            return 0.5
        case "Light":
            return 0.75
        case "Regular":
            return 1.0
        case "Medium":
            return 1.25
        case "Bold":
            return 1.5
        case "Black":
            return 2.0
        default:
            return 1.0  // 默认为Regular粗细
        }
    }
    
    /// 获取SourceHanSansSC字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getSourceHanSansSCLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "ExtraLight":
            return 0.5
        case "Light":
            return 0.75
        case "Normal":
            return 1.0
        case "Regular":
            return 1.25
        case "Medium":
            return 1.5
        case "Bold":
            return 2.0
        case "Heavy":
            return 2.0
        default:
            return 1.25  // 默认为Regular粗细
        }
    }
    
    /// 获取HONORSansCN字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getHONORSansCNLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Thin":
            return 0.5
        case "ExtraLight":
            return 0.75
        case "Light":
            return 1.0
        case "Regular":
            return 1.25
        case "Medium":
            return 1.25
        case "DemiBold", "SemiBold":  // 支持两种写法
            return 1.5
        case "Bold":
            return 1.5
        case "ExtraBold":
            return 2.0
        case "Heavy":
            return 2.0
        default:
            return 1.25  // 默认为Regular粗细
        }
    }
    
    /// 获取FuturaPT字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getFuturaPTLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Light":
            return 0.5
        case "Book":
            return 0.75
        case "Medium":
            return 1.0
        case "Demi":
            return 1.25
        case "Light Obl":
            return 0.5
        case "Book Obl":
            return 0.75
        case "Medium Obl":
            return 1.0
        case "Demi Obl":
            return 1.5
        default:
            return 1.0  // 默认为Medium粗细
        }
    }
    
    /// 获取TYPOGRAPH PRO字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getTypographProLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Ultra Light":
            return 0.5
        case "Light":
            return 0.5
        case "Semi Bold":
            return 1.25
        case "Extra Bold":
            return 2.0
        case "Ultra Light Italic":
            return 0.5
        case "Light Italic":
            return 0.5
        case "Semi Bold Italic":
            return 1.25
        default:
            return 1.25  // 默认为Semi Bold粗细
        }
    }
    
    /// 获取CourierPrimeCode字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getCourierPrimeCodeLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Regular":
            return 0.75
        case "Italic":
            return 0.75
        default:
            return 0.75  // 默认为Regular粗细
        }
    }
    
    /// 获取Miamo字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getMiamoLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Thin":
            return 0.5
        case "Light":
            return 0.75
        case "Regular":
            return 1.0
        case "Medium":
            return 1.25
        case "SemiBold":
            return 1.5
        case "Bold":
            return 2.0
        default:
            return 1.0  // 默认为Regular粗细
        }
    }
    
    /// 获取ADELE字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getAdeleLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Light":
            return 0.5
        default:
            return 0.5  // 只有Light粗细
        }
    }
    
    /// 获取Quantico字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getQuanticoLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Regular":
            return 0.75
        case "Bold":
            return 1.5
        case "Italic":
            return 0.75
        case "Bold Italic":
            return 1.5
        default:
            return 0.75  // 默认为Regular粗细
        }
    }
    
    /// 获取Syntax字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getSyntaxLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Roman":
            return 0.75
        case "Bold":
            return 1.25
        case "Italic":
            return 0.75
        case "Black":
            return 1.5
        case "UltraBlack":
            return 2.0
        default:
            return 0.75  // 默认为Roman粗细
        }
    }
    
    /// 获取Makinas系列字体各粗细对应的分隔线宽度（根据用户定义）
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getMakinasLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "Regular":
            return 0.75
        default:
            return 0.75  // 只有Regular粗细
        }
    }
    
    /// 获取默认字体粗细对应的分隔线宽度
    /// - Parameter fontWeight: 字体粗细名称
    /// - Returns: 分隔线宽度
    private static func getDefaultLineThickness(fontWeight: String) -> CGFloat {
        switch fontWeight {
        case "UltraLight", "Ultralight", "Extra Light", "ExtraLight", "Ultra Light":
            return 0.5
        case "Thin":
            return 0.75
        case "Light":
            return 0.75
        case "Regular", "Normal", "Book", "Roman", "Text":
            return 1.0
        case "Medium":
            return 1.25
        case "Semibold", "SemiBold", "DemiBold", "Demi Bold", "Semi Bold":
            return 1.5
        case "Bold":
            return 1.5
        case "Heavy", "ExtraBold", "Extra Bold", "Black":
            return 2.0
        default:
            return 1.0  // 默认等同于Regular粗细
        }
    }
    
    /// 根据UIFont字体获取建议的分隔线宽度
    /// - Parameter font: UIFont对象
    /// - Returns: 建议的分隔线宽度
    static func getLineThicknessForFont(_ font: UIFont) -> CGFloat {
        // 提取字体名称
        let fontName = font.fontName
        
        // 尝试从字体名称中提取字体族和粗细
        // 例如从"PingFangSC-Medium"中提取"PingFangSC"和"Medium"
        let components = fontName.split(separator: "-")
        if components.count > 1 {
            let familyPrefix = String(components[0])
            let weightString = String(components[1])
            
            // 根据字体族名称选择对应的处理方法
            if familyPrefix.contains("PingFang") {
                return getPingFangSCLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("HarmonyOS") {
                return getHarmonyOSLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("SourceHanSans") {
                return getSourceHanSansSCLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("HONORSansCN") {
                return getHONORSansCNLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("FuturaPT") {
                return getFuturaPTLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("TYPOGRAPH") {
                return getTypographProLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("CourierPrimeCode") {
                return getCourierPrimeCodeLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("Miamo") {
                return getMiamoLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("ADELE") {
                return getAdeleLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("Quantico") {
                return getQuanticoLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("Syntax") {
                return getSyntaxLineThickness(fontWeight: weightString)
            } else if familyPrefix.contains("Makinas") {
                return getMakinasLineThickness(fontWeight: weightString)
            }
        }
        
        // 如果无法从字体名称中提取粗细信息，则使用默认值
        return 1.0
    }
} 