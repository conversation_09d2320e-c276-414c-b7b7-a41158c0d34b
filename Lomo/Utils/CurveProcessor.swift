import Foundation
import CoreGraphics
import Accelerate

/// 曲线处理工具类 - 专业级LUT生成系统
/// 负责将UI控制点转换为高精度256级浮点LUT，支持Catmull-Rom样条插值
class CurveProcessor {
    
    // MARK: - 常量定义
    
    /// LUT分辨率 - 256级精度
    static let lutResolution = 256
    
    /// 最大控制点数量
    static let maxControlPoints = 16
    
    /// 插值精度 - 用于高质量曲线生成
    static let interpolationPrecision = 1024
    
    /// 边界容差 - 用于控制点验证
    static let boundaryTolerance: CGFloat = 0.001
    
    // MARK: - 曲线质量枚举
    
    enum CurveQuality {
        case realtime       // 实时预览 - 线性插值
        case standard       // 标准质量 - Catmull-Rom样条
        case highQuality    // 高质量 - Catmull-Rom + 超采样
        
        var description: String {
            switch self {
            case .realtime: return "实时预览"
            case .standard: return "标准质量"
            case .highQuality: return "高质量"
            }
        }
        
        var interpolationSteps: Int {
            switch self {
            case .realtime: return lutResolution
            case .standard: return lutResolution * 2
            case .highQuality: return interpolationPrecision
            }
        }
    }
    
    // MARK: - 主要LUT生成方法
    
    /// 从控制点生成高质量曲线查找表
    /// - Parameters:
    ///   - points: 控制点数组，已按X坐标排序
    ///   - quality: 生成质量级别
    /// - Returns: 256个浮点值的查找表，范围0.0-1.0
    static func generateLUT(from points: [CGPoint], quality: CurveQuality = .standard) -> [Float] {
        guard points.count >= 2 else {
            return createLinearLUT()
        }
        
        // 验证和预处理控制点
        let processedPoints = preprocessControlPoints(points)
        
        switch quality {
        case .realtime:
            return generateRealtimeLUT(from: processedPoints)
        case .standard:
            return generateStandardLUT(from: processedPoints)
        case .highQuality:
            return generateHighQualityLUT(from: processedPoints)
        }
    }
    
    /// 批量生成所有通道的LUT
    /// - Parameters:
    ///   - curvePoints: 四通道控制点字典
    ///   - quality: 生成质量
    /// - Returns: 包含所有通道LUT的字典
    static func generateAllChannelLUTs(from curvePoints: [CurveChannel: [CGPoint]], quality: CurveQuality = .standard) -> [CurveChannel: [Float]] {
        var luts: [CurveChannel: [Float]] = [:]

        for channel in CurveChannel.allCases {
            luts[channel] = generateLUT(from: curvePoints[channel] ?? createDefaultPoints(), quality: quality)
        }

        return luts
    }
    
    // MARK: - 控制点预处理
    
    /// 预处理控制点 - 确保有效性和优化性能
    /// - Parameter points: 原始控制点
    /// - Returns: 处理后的控制点
    private static func preprocessControlPoints(_ points: [CGPoint]) -> [CGPoint] {
        var processedPoints = points.sorted { $0.x < $1.x }
        
        // 确保端点存在
        if processedPoints.first?.x != 0.0 {
            let firstY = processedPoints.first?.y ?? 0.0
            processedPoints.insert(CGPoint(x: 0.0, y: firstY), at: 0)
        }
        if processedPoints.last?.x != 1.0 {
            let lastY = processedPoints.last?.y ?? 1.0
            processedPoints.append(CGPoint(x: 1.0, y: lastY))
        }
        
        // 限制控制点数量
        if processedPoints.count > maxControlPoints {
            processedPoints = Array(processedPoints.prefix(maxControlPoints))
        }
        
        // 确保Y坐标在有效范围内
        processedPoints = processedPoints.map { point in
            CGPoint(x: point.x, y: max(0.0, min(1.0, point.y)))
        }
        
        // 移除重复的X坐标点
        processedPoints = removeDuplicateXPoints(processedPoints)
        
        return processedPoints
    }
    
    /// 移除重复的X坐标点，保留Y值更合理的点
    private static func removeDuplicateXPoints(_ points: [CGPoint]) -> [CGPoint] {
        var uniquePoints: [CGPoint] = []
        var lastX: CGFloat = -1.0
        
        for point in points {
            if abs(point.x - lastX) > boundaryTolerance {
                uniquePoints.append(point)
                lastX = point.x
            } else {
                // 如果X坐标重复，保留Y值更接近线性的点
                if let lastIndex = uniquePoints.indices.last {
                    let linearY = point.x // 线性情况下 y = x
                    let currentDistance = abs(point.y - linearY)
                    let lastDistance = abs(uniquePoints[lastIndex].y - linearY)
                    
                    if currentDistance < lastDistance {
                        uniquePoints[lastIndex] = point
                    }
                }
            }
        }
        
        return uniquePoints
    }
    
    // MARK: - 不同质量的LUT生成
    
    /// 实时质量LUT生成 - 优化性能
    private static func generateRealtimeLUT(from points: [CGPoint]) -> [Float] {
        var lut: [Float] = []
        lut.reserveCapacity(lutResolution)
        
        for i in 0..<lutResolution {
            let x = Float(i) / Float(lutResolution - 1)
            let y = evaluateCurveLinear(at: CGFloat(x), points: points)
            lut.append(Float(y))
        }
        
        return lut
    }
    
    /// 标准质量LUT生成 - 平衡性能和质量
    private static func generateStandardLUT(from points: [CGPoint]) -> [Float] {
        var lut: [Float] = []
        lut.reserveCapacity(lutResolution)
        
        for i in 0..<lutResolution {
            let x = Float(i) / Float(lutResolution - 1)
            let y = evaluateCurveCatmullRom(at: CGFloat(x), points: points)
            lut.append(Float(y))
        }
        
        return lut
    }
    
    /// 高质量LUT生成 - 最佳质量
    private static func generateHighQualityLUT(from points: [CGPoint]) -> [Float] {
        // 首先生成高精度中间数据
        var highPrecisionLUT: [Float] = []
        highPrecisionLUT.reserveCapacity(interpolationPrecision)
        
        for i in 0..<interpolationPrecision {
            let x = Float(i) / Float(interpolationPrecision - 1)
            let y = evaluateCurveCatmullRom(at: CGFloat(x), points: points)
            highPrecisionLUT.append(Float(y))
        }
        
        // 使用高质量重采样到目标分辨率
        return resampleLUT(highPrecisionLUT, to: lutResolution)
    }
    
    // MARK: - 工具方法
    
    /// 创建线性LUT
    static func createLinearLUT() -> [Float] {
        return Array(0..<lutResolution).map { Float($0) / Float(lutResolution - 1) }
    }
    
    /// 创建默认控制点
    static func createDefaultPoints() -> [CGPoint] {
        return [CGPoint(x: 0, y: 0), CGPoint(x: 1, y: 1)]
    }
    
    /// 验证控制点有效性
    static func validatePoints(_ points: [CGPoint]) -> Bool {
        guard points.count >= 2 && points.count <= maxControlPoints else { return false }
        
        // 检查排序
        for i in 1..<points.count {
            if points[i].x <= points[i-1].x { return false }
        }
        
        // 检查边界
        guard let firstPoint = points.first, let lastPoint = points.last else { return false }
        return firstPoint.x <= boundaryTolerance && lastPoint.x >= (1.0 - boundaryTolerance)
    }
    
    /// 计算LUT的统计信息
    static func analyzeLUT(_ lut: [Float]) -> (min: Float, max: Float, mean: Float, variance: Float) {
        guard !lut.isEmpty else { return (0, 0, 0, 0) }

        let min = lut.min() ?? 0
        let max = lut.max() ?? 1
        let mean = lut.reduce(0, +) / Float(lut.count)
        let variance = lut.map { pow($0 - mean, 2) }.reduce(0, +) / Float(lut.count)

        return (min, max, mean, variance)
    }

    // MARK: - 曲线评估算法

    /// 线性插值评估 - 用于实时模式
    private static func evaluateCurveLinear(at x: CGFloat, points: [CGPoint]) -> CGFloat {
        if x <= points.first?.x ?? 0.0 {
            return points.first?.y ?? 0.0
        }
        if x >= points.last?.x ?? 1.0 {
            return points.last?.y ?? 1.0
        }

        for i in 0..<points.count - 1 {
            let p1 = points[i]
            let p2 = points[i + 1]

            if x >= p1.x && x <= p2.x {
                let t = (x - p1.x) / (p2.x - p1.x)
                return p1.y + t * (p2.y - p1.y)
            }
        }

        return CGFloat(x) // 默认线性
    }

    /// Catmull-Rom样条插值 - 专业级平滑曲线
    private static func evaluateCurveCatmullRom(at x: CGFloat, points: [CGPoint]) -> CGFloat {
        if x <= points.first?.x ?? 0.0 {
            return points.first?.y ?? 0.0
        }
        if x >= points.last?.x ?? 1.0 {
            return points.last?.y ?? 1.0
        }

        // 找到X位置所在的曲线段
        for i in 0..<points.count - 1 {
            let p1 = points[i]
            let p2 = points[i + 1]

            if x >= p1.x && x <= p2.x {
                return catmullRomInterpolation(x: x, points: points, segmentIndex: i)
            }
        }

        return CGFloat(x)
    }

    /// Catmull-Rom样条插值实现
    private static func catmullRomInterpolation(x: CGFloat, points: [CGPoint], segmentIndex: Int) -> CGFloat {
        let p1 = points[segmentIndex]
        let p2 = points[segmentIndex + 1]

        // 获取四个控制点
        let p0: CGPoint
        if segmentIndex > 0 {
            p0 = points[segmentIndex - 1]
        } else {
            // 虚拟前导点 - 保持切线连续性
            let dx = p2.x - p1.x
            let dy = p2.y - p1.y
            p0 = CGPoint(x: p1.x - dx * 0.3, y: p1.y - dy * 0.3)
        }

        let p3: CGPoint
        if segmentIndex + 2 < points.count {
            p3 = points[segmentIndex + 2]
        } else {
            // 虚拟后续点 - 保持切线连续性
            let dx = p2.x - p1.x
            let dy = p2.y - p1.y
            p3 = CGPoint(x: p2.x + dx * 0.3, y: p2.y + dy * 0.3)
        }

        // 计算参数t
        let t = (x - p1.x) / (p2.x - p1.x)

        // Catmull-Rom样条公式
        let t2 = t * t
        let t3 = t2 * t

        let a0 = -0.5 * p0.y + 1.5 * p1.y - 1.5 * p2.y + 0.5 * p3.y
        let a1 = p0.y - 2.5 * p1.y + 2.0 * p2.y - 0.5 * p3.y
        let a2 = -0.5 * p0.y + 0.5 * p2.y
        let a3 = p1.y

        let result = a0 * t3 + a1 * t2 + a2 * t + a3

        // 确保结果在有效范围内
        return max(0.0, min(1.0, result))
    }

    // MARK: - 高质量重采样

    /// 使用Lanczos重采样算法进行高质量LUT重采样
    private static func resampleLUT(_ sourceLUT: [Float], to targetSize: Int) -> [Float] {
        var targetLUT: [Float] = []
        targetLUT.reserveCapacity(targetSize)

        let scale = Float(sourceLUT.count - 1) / Float(targetSize - 1)

        for i in 0..<targetSize {
            let sourceIndex = Float(i) * scale
            let value = lanczosInterpolation(sourceLUT, at: sourceIndex)
            targetLUT.append(value)
        }

        return targetLUT
    }

    /// Lanczos插值 - 高质量重采样
    private static func lanczosInterpolation(_ data: [Float], at index: Float) -> Float {
        let a: Float = 3.0 // Lanczos参数
        let x = index
        let baseIndex = Int(floor(x))

        var sum: Float = 0.0
        var weightSum: Float = 0.0

        for i in (baseIndex - Int(a) + 1)...(baseIndex + Int(a)) {
            if i >= 0 && i < data.count {
                let distance = x - Float(i)
                let weight = lanczosKernel(distance, a: a)
                sum += data[i] * weight
                weightSum += weight
            }
        }

        return weightSum > 0 ? sum / weightSum : 0.0
    }

    /// Lanczos核函数
    private static func lanczosKernel(_ x: Float, a: Float) -> Float {
        if abs(x) >= a { return 0.0 }
        if abs(x) < 0.001 { return 1.0 }

        let piX = Float.pi * x
        let piXOverA = piX / a

        return (sin(piX) / piX) * (sin(piXOverA) / piXOverA)
    }
}
