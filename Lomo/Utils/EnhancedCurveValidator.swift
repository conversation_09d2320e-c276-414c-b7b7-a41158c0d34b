import Foundation
import UIKit

/// 增强曲线验证器 - 第2步功能验证
/// 验证硬件采样器、高质量插值、sRGB色彩空间处理等增强功能
class EnhancedCurveValidator {
    
    private let renderer: EnhancedMetalCurveRenderer?
    
    init() {
        self.renderer = EnhancedMetalCurveRenderer()
        if renderer == nil {
            print("❌ EnhancedMetalCurveRenderer 初始化失败")
        }
    }
    
    // MARK: - 验证结果结构
    
    struct ValidationResult {
        let isSuccess: Bool
        let message: String
        let details: [String]
        let executionTime: TimeInterval
        let performanceMetrics: PerformanceMetrics?
    }
    
    struct PerformanceMetrics {
        let realtimeRenderTime: TimeInterval
        let standardRenderTime: TimeInterval
        let highQualityRenderTime: TimeInterval
        let memoryUsage: Int
    }
    
    // MARK: - 主要验证方法
    
    /// 验证所有第2步增强功能
    func validateAllEnhancedFeatures() -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        var details: [String] = []
        var allSuccess = true
        
        print("🧪 开始验证第2步增强功能...")
        
        guard let renderer = self.renderer else {
            return ValidationResult(
                isSuccess: false,
                message: "渲染器不可用",
                details: ["EnhancedMetalCurveRenderer初始化失败"],
                executionTime: 0,
                performanceMetrics: nil
            )
        }
        
        // 1. 验证硬件采样器支持
        let hardwareResult = validateHardwareSampler(renderer)
        details.append("硬件采样器: \(hardwareResult.isSuccess ? "✅" : "❌") \(hardwareResult.message)")
        if !hardwareResult.isSuccess { allSuccess = false }
        
        // 2. 验证高质量插值
        let qualityResult = validateHighQualityInterpolation(renderer)
        details.append("高质量插值: \(qualityResult.isSuccess ? "✅" : "❌") \(qualityResult.message)")
        if !qualityResult.isSuccess { allSuccess = false }
        
        // 3. 验证sRGB色彩空间处理
        let srgbResult = validateSRGBColorSpace(renderer)
        details.append("sRGB色彩空间: \(srgbResult.isSuccess ? "✅" : "❌") \(srgbResult.message)")
        if !srgbResult.isSuccess { allSuccess = false }
        
        // 4. 验证多通道曲线
        let multiChannelResult = validateMultiChannelCurves(renderer)
        details.append("多通道曲线: \(multiChannelResult.isSuccess ? "✅" : "❌") \(multiChannelResult.message)")
        if !multiChannelResult.isSuccess { allSuccess = false }
        
        // 5. 性能基准测试
        var performanceMetrics: PerformanceMetrics? = nil
        if allSuccess {
            performanceMetrics = runPerformanceBenchmark(renderer)
            if let metrics = performanceMetrics {
                details.append("性能测试: ✅ 实时:\(String(format: "%.1f", metrics.realtimeRenderTime*1000))ms, 标准:\(String(format: "%.1f", metrics.standardRenderTime*1000))ms, 高质量:\(String(format: "%.1f", metrics.highQualityRenderTime*1000))ms")
            }
        }
        
        let executionTime = CFAbsoluteTimeGetCurrent() - startTime
        let finalMessage = allSuccess ? 
            "🎉 所有第2步增强功能验证通过！" : 
            "⚠️ 部分增强功能验证失败"
        
        return ValidationResult(
            isSuccess: allSuccess,
            message: finalMessage,
            details: details,
            executionTime: executionTime,
            performanceMetrics: performanceMetrics
        )
    }
    
    // MARK: - 具体验证方法
    
    private func validateHardwareSampler(_ renderer: EnhancedMetalCurveRenderer) -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 创建测试S曲线LUT
        let testLUT = createTestSCurveLUT()
        
        // 创建测试图像
        let testImage = createTestGradientImage(size: CGSize(width: 256, height: 256))
        
        // 测试硬件采样器渲染
        guard let result = renderer.applyCurves(
            to: testImage,
            rgbLUT: testLUT,
            quality: .standard,
            colorSpace: .srgb,
            intensity: 1.0
        ) else {
            return ValidationResult(
                isSuccess: false,
                message: "硬件采样器渲染失败",
                details: ["无法使用硬件采样器进行曲线渲染"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        }
        
        // 验证结果图像
        if result.size.width > 0 && result.size.height > 0 {
            return ValidationResult(
                isSuccess: true,
                message: "硬件采样器功能正常",
                details: ["成功使用硬件采样器渲染图像"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        } else {
            return ValidationResult(
                isSuccess: false,
                message: "硬件采样器输出无效",
                details: ["渲染结果图像无效"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        }
    }
    
    private func validateHighQualityInterpolation(_ renderer: EnhancedMetalCurveRenderer) -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let testLUT = createTestSCurveLUT()
        let testImage = createTestGradientImage(size: CGSize(width: 128, height: 128))
        
        // 测试不同质量模式
        let qualities: [EnhancedMetalCurveRenderer.RenderQuality] = [.realtime, .standard, .highQuality]
        var results: [UIImage] = []
        
        for quality in qualities {
            guard let result = renderer.applyCurves(
                to: testImage,
                rgbLUT: testLUT,
                quality: quality,
                colorSpace: .srgb,
                intensity: 1.0
            ) else {
                return ValidationResult(
                    isSuccess: false,
                    message: "质量模式 \(quality.description) 渲染失败",
                    details: ["无法使用 \(quality.description) 模式渲染"],
                    executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                    performanceMetrics: nil
                )
            }
            results.append(result)
        }
        
        return ValidationResult(
            isSuccess: true,
            message: "所有质量模式渲染成功",
            details: ["实时、标准、高质量模式均正常工作"],
            executionTime: CFAbsoluteTimeGetCurrent() - startTime,
            performanceMetrics: nil
        )
    }
    
    private func validateSRGBColorSpace(_ renderer: EnhancedMetalCurveRenderer) -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let testLUT = createTestSCurveLUT()
        let testImage = createTestColorImage()
        
        // 测试线性和sRGB色彩空间
        guard let linearResult = renderer.applyCurves(
            to: testImage,
            rgbLUT: testLUT,
            quality: .standard,
            colorSpace: .linear,
            intensity: 1.0
        ),
        let srgbResult = renderer.applyCurves(
            to: testImage,
            rgbLUT: testLUT,
            quality: .standard,
            colorSpace: .srgb,
            intensity: 1.0
        ) else {
            return ValidationResult(
                isSuccess: false,
                message: "sRGB色彩空间处理失败",
                details: ["无法在不同色彩空间中渲染"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        }
        
        // 验证两种色彩空间产生不同结果
        if linearResult.size == srgbResult.size {
            return ValidationResult(
                isSuccess: true,
                message: "sRGB色彩空间处理正常",
                details: ["线性和sRGB色彩空间均正常工作"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        } else {
            return ValidationResult(
                isSuccess: false,
                message: "色彩空间处理异常",
                details: ["不同色彩空间产生了异常结果"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        }
    }
    
    private func validateMultiChannelCurves(_ renderer: EnhancedMetalCurveRenderer) -> ValidationResult {
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 创建不同的通道LUT
        let rgbLUT = createTestSCurveLUT()
        let redLUT = createTestBrightLUT()
        let greenLUT = createTestLinearLUT()
        let blueLUT = createTestDarkLUT()
        
        let testImage = createTestColorImage()
        
        guard let result = renderer.applyMultiChannelCurves(
            to: testImage,
            rgbLUT: rgbLUT,
            redLUT: redLUT,
            greenLUT: greenLUT,
            blueLUT: blueLUT,
            rgbIntensity: 0.8,
            redIntensity: 0.6,
            greenIntensity: 0.4,
            blueIntensity: 0.7,
            colorSpace: .srgb
        ) else {
            return ValidationResult(
                isSuccess: false,
                message: "多通道曲线渲染失败",
                details: ["无法执行多通道曲线处理"],
                executionTime: CFAbsoluteTimeGetCurrent() - startTime,
                performanceMetrics: nil
            )
        }
        
        return ValidationResult(
            isSuccess: true,
            message: "多通道曲线功能正常",
            details: ["RGB + 红绿蓝分离通道处理成功"],
            executionTime: CFAbsoluteTimeGetCurrent() - startTime,
            performanceMetrics: nil
        )
    }
    
    private func runPerformanceBenchmark(_ renderer: EnhancedMetalCurveRenderer) -> PerformanceMetrics? {
        let testLUT = createTestSCurveLUT()
        let testImage = createTestGradientImage(size: CGSize(width: 512, height: 512))
        
        // 测试实时模式性能
        let realtimeStart = CFAbsoluteTimeGetCurrent()
        _ = renderer.applyCurves(to: testImage, rgbLUT: testLUT, quality: .realtime)
        let realtimeTime = CFAbsoluteTimeGetCurrent() - realtimeStart
        
        // 测试标准模式性能
        let standardStart = CFAbsoluteTimeGetCurrent()
        _ = renderer.applyCurves(to: testImage, rgbLUT: testLUT, quality: .standard)
        let standardTime = CFAbsoluteTimeGetCurrent() - standardStart
        
        // 测试高质量模式性能
        let highQualityStart = CFAbsoluteTimeGetCurrent()
        _ = renderer.applyCurves(to: testImage, rgbLUT: testLUT, quality: .highQuality)
        let highQualityTime = CFAbsoluteTimeGetCurrent() - highQualityStart
        
        return PerformanceMetrics(
            realtimeRenderTime: realtimeTime,
            standardRenderTime: standardTime,
            highQualityRenderTime: highQualityTime,
            memoryUsage: 0 // 简化实现，实际可以测量内存使用
        )
    }

    // MARK: - 辅助方法

    private func createTestSCurveLUT() -> [Float] {
        var lut: [Float] = []
        for i in 0..<256 {
            let x = Float(i) / 255.0
            let y = 3.0 * x * x - 2.0 * x * x * x // S曲线
            lut.append(y)
        }
        return lut
    }

    private func createTestLinearLUT() -> [Float] {
        return Array(0..<256).map { Float($0) / 255.0 }
    }

    private func createTestBrightLUT() -> [Float] {
        var lut: [Float] = []
        for i in 0..<256 {
            let x = Float(i) / 255.0
            let y = min(1.0, x + 0.2) // 提亮
            lut.append(y)
        }
        return lut
    }

    private func createTestDarkLUT() -> [Float] {
        var lut: [Float] = []
        for i in 0..<256 {
            let x = Float(i) / 255.0
            let y = max(0.0, x - 0.2) // 压暗
            lut.append(y)
        }
        return lut
    }

    private func createTestGradientImage(size: CGSize) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let cgContext = context.cgContext
            for x in 0..<Int(size.width) {
                let gray = CGFloat(x) / size.width
                cgContext.setFillColor(red: gray, green: gray, blue: gray, alpha: 1.0)
                cgContext.fill(CGRect(x: x, y: 0, width: 1, height: Int(size.height)))
            }
        }
    }

    private func createTestColorImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let cgContext = context.cgContext

            // 红色区域
            cgContext.setFillColor(UIColor.red.cgColor)
            cgContext.fill(CGRect(x: 0, y: 0, width: 50, height: 50))

            // 绿色区域
            cgContext.setFillColor(UIColor.green.cgColor)
            cgContext.fill(CGRect(x: 50, y: 0, width: 50, height: 50))

            // 蓝色区域
            cgContext.setFillColor(UIColor.blue.cgColor)
            cgContext.fill(CGRect(x: 0, y: 50, width: 50, height: 50))

            // 白色区域
            cgContext.setFillColor(UIColor.white.cgColor)
            cgContext.fill(CGRect(x: 50, y: 50, width: 50, height: 50))
        }
    }
}

// MARK: - 简化的验证运行器

class EnhancedValidationRunner {

    /// 运行第2步增强功能验证
    static func validateStep2() {
        print("🚀 开始第2步Metal曲线增强功能验证")
        print("时间: \(Date())")
        print("")

        let validator = EnhancedCurveValidator()
        let result = validator.validateAllEnhancedFeatures()

        // 显示结果
        print("验证结果:")
        print("状态: \(result.isSuccess ? "✅ 成功" : "❌ 失败")")
        print("消息: \(result.message)")
        print("执行时间: \(String(format: "%.3f", result.executionTime))秒")

        print("\n详细信息:")
        for (index, detail) in result.details.enumerated() {
            print("  \(index + 1). \(detail)")
        }

        // 显示性能指标
        if let metrics = result.performanceMetrics {
            print("\n📊 性能指标:")
            print("  实时模式: \(String(format: "%.1f", metrics.realtimeRenderTime * 1000))ms")
            print("  标准模式: \(String(format: "%.1f", metrics.standardRenderTime * 1000))ms")
            print("  高质量模式: \(String(format: "%.1f", metrics.highQualityRenderTime * 1000))ms")

            // 性能评估
            if metrics.realtimeRenderTime < 0.016 { // 60fps
                print("  实时性能: 🟢 优秀 (支持60fps)")
            } else if metrics.realtimeRenderTime < 0.033 { // 30fps
                print("  实时性能: 🟡 良好 (支持30fps)")
            } else {
                print("  实时性能: 🔴 需要优化")
            }
        }

        if result.isSuccess {
            print("\n🎉 第2步增强功能验证成功！可以进入第3步")
        } else {
            print("\n⚠️ 第2步验证失败，请检查上述问题")
        }

        print("\n🏁 第2步验证完成")
    }

    /// 快速性能测试
    static func quickPerformanceTest() {
        print("⚡ 第2步快速性能测试...")

        let validator = EnhancedCurveValidator()
        let result = validator.validateAllEnhancedFeatures()

        if let metrics = result.performanceMetrics {
            print("📊 性能结果:")
            print("  实时: \(String(format: "%.1f", metrics.realtimeRenderTime * 1000))ms")
            print("  标准: \(String(format: "%.1f", metrics.standardRenderTime * 1000))ms")
            print("  高质量: \(String(format: "%.1f", metrics.highQualityRenderTime * 1000))ms")
        } else {
            print("❌ 性能测试失败")
        }
    }
}
