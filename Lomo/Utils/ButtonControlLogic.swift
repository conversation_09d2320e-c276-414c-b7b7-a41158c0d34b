import Foundation
import SwiftUI

// 按钮类型枚举
enum ButtonType {
    case grid
    case histogram
    case level
    case flip
    case peaking
    case timer
    case zebra
    case stabilization
}

// 按钮控制逻辑
class ButtonControlLogic {
    // 动画配置
    private let standardAnimation: Animation = .spring(
        response: AnimationConstants.duration,
        dampingFraction: AnimationConstants.dampingFraction
    )
    private let animationDuration: Double = AnimationConstants.duration
    
    // 视频模式状态
    private var isVideoMode: Bool = false
    
    // MARK: - 参数选项类型
    
    enum ParameterOptionType {
        case separator
        case icon
        case text
    }
    
    func getOptionType(_ option: String) -> ParameterOptionType {
        if option == "separator" {
            return .separator
        } else if option.contains("fill") || 
                 option.contains("livephoto") {
            return .icon
        } else {
            return .text
        }
    }
    
    // 录制时间
    private var recordingTime: TimeInterval = 0
    
    // 计时器管理器
    private let timerManager = TimerManager()
    
    // MARK: - TimerManager Access Methods
    
    /// 启动选项面板自动隐藏计时器
    func startOptionPanelTimer(type: TimerType, hideAction: @escaping () -> Void) {
        // 直接使用原始回调启动计时器，移除额外的重置逻辑
        // 这样选项面板可以独立消失，而不影响左侧按钮的展开状态
        timerManager.startOptionPanelTimer(type: type, hideAction: hideAction)
    }
    
    /// 取消选项面板自动隐藏计时器
    func cancelOptionPanelTimer(type: TimerType) {
        timerManager.cancelOptionPanelTimer(type: type)
    }
    
    /// 重置选项面板计时器
    func resetOptionPanelTimer(type: TimerType, isVisible: Bool, hideAction: @escaping () -> Void) {
        timerManager.resetOptionPanelTimer(type: type, isVisible: isVisible, hideAction: hideAction)
    }
    
    // 当前显示的选项
    var currentOptions: [String] {
        if isVideoMode {
            return [
                CameraDefaults.videoCodec,      // HEVC
                "separator",
                CameraDefaults.videoAspectRatio, // 16:9
                "separator",
                CameraDefaults.videoResolution,  // 4K
                "separator",
                "30fps",                        // 30fps
                "separator",
                CameraDefaults.videoColorSpace  // 使用默认的colorSpace值
            ]
        } else {
            return [
                CameraDefaults.photoFormat,     // JPG
                "separator",
                CameraDefaults.photoAspectRatio, // 4:3
                "separator",
                "livephoto",                    // 实况照片
                "separator",
                "bolt.fill",                    // 闪光灯
                "separator",
                "HDR"                          // 照片模式保持HDR文本
            ]
        }
    }
    
    // 设置视频模式
    func setVideoMode(_ isVideo: Bool) {
        isVideoMode = isVideo
    }
    
    // 回调
    var onParameterStateChanged: ((Bool) -> Void)? {
        get { timerManager.onParameterStateChanged }
        set { timerManager.onParameterStateChanged = newValue }
    }
    var onLeftButtonStateChanged: ((Bool) -> Void)? {
        get { timerManager.onLeftButtonStateChanged }
        set { timerManager.onLeftButtonStateChanged = newValue }
    }
    var onRightButtonStateChanged: ((Bool, Bool) -> Void)? {
        get { timerManager.onRightButtonStateChanged }
        set { timerManager.onRightButtonStateChanged = newValue }
    }
    var onButtonStateChanged: ((ButtonType, Bool) -> Void)?
    
    // 重置方法
    func resetParameterTimer(isExpanded: Bool) {
        timerManager.resetParameterTimer(isExpanded: isExpanded)
    }

    func resetLeftButtonTimer(isExpanded: Bool) {
        timerManager.resetLeftButtonTimer(isExpanded: isExpanded)
    }

    func resetRightButtonTimer(isExpanded: Bool) {
        timerManager.resetRightButtonTimer(isExpanded: isExpanded)
    }

    // 按钮切换逻辑
    func toggleLeftButton(isExpanded: Bool) {
        if isExpanded {
            // 启动左侧按钮自动隐藏计时器
            timerManager.startTimer(for: .leftButton) { [weak self] in
                self?.onLeftButtonStateChanged?(false)
            }
        } else {
            // 取消计时器
            timerManager.cancelTimer(for: .leftButton)
        }
    }

    func toggleRightButton(isExpanded: Bool, isAMMode: Bool) {
        if isExpanded {
            // 启动右侧按钮自动隐藏计时器
            timerManager.startTimer(for: .rightButton) { [weak self] in
                self?.onRightButtonStateChanged?(false, isAMMode)
            }
        } else {
            // 取消计时器
            timerManager.cancelTimer(for: .rightButton)
        }
    }

    // 功能按钮切换
    func toggleButton(_ type: ButtonType, isVideoMode: Bool = false) -> Bool {
        if shouldToggleButton(type, isVideoMode: isVideoMode) {
            onButtonStateChanged?(type, true)
            return true
        }
        return false
    }
    
    // 判断按钮是否可以切换状态
    func shouldToggleButton(_ type: ButtonType, isVideoMode: Bool = false) -> Bool {
        switch type {
        case .grid, .histogram, .level, .flip, .peaking:
            return true  // 这些按钮总是可以切换
        case .timer:
            return !isVideoMode  // 只在照片模式可用
        case .zebra:
            return isVideoMode   // 只在视频模式可用
        case .stabilization:
            return isVideoMode   // 只在视频模式可用
        }
    }
    
    // 动画辅助方法
    func animate(_ action: @escaping () -> Void) {
        withAnimation(standardAnimation) {
            action()
        }
    }
    
    // 参数面板展开和收起方法
    func expandParameter(isRecording: Bool, completion: @escaping (Bool, Double, Double) -> Void) {
        // 如果正在录制，则不允许展开
        guard !isRecording else {
            print("⚠️ 参数面板：录制中，禁止展开")
            return
        }
        
        print("📱 参数面板：开始展开")
        // 初始状态：展开，显示关闭按钮，显示侧边按钮
        completion(true, 1, 1)
        
        print("📱 参数面板：展开动画开始")
        // 延迟隐藏侧边按钮
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration) {
            completion(true, 1, 0)
            print("📱 参数面板：展开完成，状态=true")
        }
        
        // 启动自动隐藏计时器
        timerManager.startParameterHideTimer()
    }
    
    func collapseParameter(completion: @escaping (Bool, Double, Double) -> Void) {
        print("📱 参数面板：开始收起")
        completion(false, 0, 1)
        
        print("📱 参数面板：收起动画开始")
        // 取消自动隐藏计时器
        timerManager.cancelParameterHideTimer()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration) {
            print("📱 参数面板：收起完成，状态=false")
        }
    }
    
    // MARK: - Recording Timer Methods
    
    // 格式化时间显示
    func formattedTime(_ time: TimeInterval) -> String {
        let hours = Int(time) / 3600
        let minutes = Int(time) / 60 % 60
        let seconds = Int(time) % 60
        return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    // 开始计时
    private func startRecordingTimer(completion: @escaping (TimeInterval) -> Void) {
        recordingTime = 0
        completion(recordingTime)  // 重置时间
        timerManager.startRecordingTimer {
            self.recordingTime += 1
            completion(self.recordingTime)
        }
    }
    
    // 停止计时
    private func stopRecordingTimer(completion: @escaping (TimeInterval) -> Void) {
        timerManager.stopRecordingTimer()
        recordingTime = 0
        completion(0)  // 重置时间
    }
    
    // MARK: - Timer Control Methods
    
    func startRecording(completion: @escaping (TimeInterval) -> Void) {
        startRecordingTimer(completion: completion)
    }
    
    func pauseRecordingTimer() {
        timerManager.pauseRecordingTimer()
    }
    
    func resumeRecordingTimer(completion: @escaping (TimeInterval) -> Void) {
        timerManager.resumeRecordingTimer {
            self.recordingTime += 1
            completion(self.recordingTime)
        }
    }
    
    func stopRecording(completion: @escaping (TimeInterval) -> Void) {
        stopRecordingTimer(completion: completion)
    }

    // MARK: - 功能按钮控制

    // 参考线切换
    func toggleGrid(currentState: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.grid) {
            completion(!currentState)  // 切换状态
        }
    }

    // 峰值对焦切换
    func togglePeaking(currentState: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.peaking) {
            completion(!currentState)  // 切换状态
        }
    }

    // 直方图切换
    func toggleHistogram(currentState: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.histogram) {
            completion(!currentState)  // 切换状态
        }
    }

    // 水平仪切换
    func toggleLevel(currentState: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.level) {
            completion(!currentState)  // 切换状态
        }
    }

    // 翻转切换
    func toggleFlip(currentState: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.flip) {
            completion(!currentState)  // 切换状态
        }
    }

    // HDR切换（照片模式）
    func toggleHDR(currentState: Bool, isVideoMode: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.zebra, isVideoMode: isVideoMode) {
            let newState = !currentState
            completion(newState)  // 调用回调并传递新状态
        }
    }

    // 斑马线切换（视频模式）
    func toggleZebra(currentState: Bool, isVideoMode: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.zebra, isVideoMode: isVideoMode) {
            completion(!currentState)  // 切换状态
        }
    }

    // 防抖切换（视频模式）
    func toggleStabilization(currentState: Bool, isVideoMode: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.stabilization, isVideoMode: isVideoMode) {
            completion(!currentState)  // 切换状态
        }
    }

    // 定时器切换（照片模式）
    func toggleTimer(currentState: Bool, isVideoMode: Bool, completion: @escaping (Bool) -> Void) {
        if shouldToggleButton(.timer, isVideoMode: isVideoMode) {
            completion(!currentState)  // 切换状态
        }
    }

    // 参数面板相关方法
    
    /// 切换曝光参数面板
    func toggleExposure() {
        // 实现曝光参数面板的切换逻辑
        print("📱 切换曝光参数面板")
        
        // 如果需要展开参数面板，调用expandParameter方法
        // 如果已经展开，则需要根据当前显示的是否为曝光面板决定是折叠还是切换
        
        // TODO: 实现具体的切换逻辑
    }
    
    /// 切换对焦参数面板
    func toggleFocus() {
        // 实现对焦参数面板的切换逻辑
        print("📱 切换对焦参数面板")
        
        // TODO: 实现具体的切换逻辑
    }
    
    /// 切换白平衡参数面板
    func toggleWhiteBalance() {
        // 实现白平衡参数面板的切换逻辑
        print("📱 切换白平衡参数面板")
        
        // TODO: 实现具体的切换逻辑
    }
    
    /// 折叠当前参数面板
    func collapseCurrentParameter() {
        // 折叠当前展开的参数面板
        print("📱 折叠当前参数面板")
        
        // 取消自动隐藏计时器
        timerManager.cancelParameterHideTimer()
        
        // 通知状态变化
        onParameterStateChanged?(false)
    }
} 