import Foundation
import UIKit

/// 曲线验证测试套件 - 用于验证第1步基础功能
/// 提供简单的测试接口和结果展示
class CurveValidationTests {
    
    private let validator: MetalCurveValidator?
    
    init() {
        self.validator = MetalCurveValidator()
        if validator == nil {
            print("❌ MetalCurveValidator 初始化失败")
        }
    }
    
    // MARK: - 主要测试方法
    
    /// 运行所有基础验证测试
    func runAllBasicTests() {
        print("\n" + String(repeating: "=", count: 50))
        print("🧪 Metal曲线基础功能验证测试")
        print(String(repeating: "=", count: 50))
        
        guard let validator = self.validator else {
            print("❌ 验证器不可用，测试终止")
            return
        }
        
        let result = validator.validateAllBasicFunctions()
        
        // 显示测试结果
        displayTestResult(result)
        
        // 根据结果给出建议
        if result.isSuccess {
            showSuccessRecommendations()
        } else {
            showFailureRecommendations(result)
        }
    }
    
    /// 运行快速编译检查
    func runQuickCompileCheck() -> <PERSON><PERSON> {
        print("\n🔍 快速编译检查...")
        
        guard let validator = self.validator else {
            print("❌ 验证器不可用")
            return false
        }
        
        // 只检查着色器编译 - 使用完整验证的编译部分
        let result = validator.validateAllBasicFunctions()
        
        if result.isSuccess {
            print("✅ 着色器编译检查通过")
            return true
        } else {
            print("❌ 着色器编译检查失败: \(result.message)")
            return false
        }
    }
    
    // MARK: - 结果显示方法
    
    private func displayTestResult(_ result: MetalCurveValidator.ValidationResult) {
        print("\n📊 测试结果:")
        print("状态: \(result.isSuccess ? "✅ 成功" : "❌ 失败")")
        print("消息: \(result.message)")
        print("执行时间: \(String(format: "%.3f", result.executionTime))秒")
        
        print("\n📋 详细信息:")
        for (index, detail) in result.details.enumerated() {
            print("  \(index + 1). \(detail)")
        }
    }
    
    private func showSuccessRecommendations() {
        print("\n🎉 所有基础功能验证通过！")
        print("\n📝 下一步建议:")
        print("1. ✅ 第1步基础功能已验证完成")
        print("2. 🚀 可以安全进入第2步：增强LUT采样")
        print("3. 🔧 建议先进行简单的集成测试")
        print("4. 📊 可以开始性能基准测试")
        
        print("\n🎯 第2步预期功能:")
        print("- 硬件采样器支持")
        print("- 高质量插值选项")
        print("- GPU内存访问优化")
        print("- sRGB色彩空间处理")
    }
    
    private func showFailureRecommendations(_ result: MetalCurveValidator.ValidationResult) {
        print("\n⚠️ 验证失败，需要修复问题")
        print("\n🔧 故障排除建议:")
        
        if result.details.contains(where: { $0.contains("无法找到") && $0.contains("函数") }) {
            print("1. 📁 检查CurveShaders.metal文件是否正确添加到项目中")
            print("2. 🎯 确认Target Membership设置正确")
            print("3. 🔄 尝试Clean Build Folder后重新编译")
        }
        
        if result.details.contains(where: { $0.contains("管线状态创建失败") }) {
            print("1. 📝 检查Metal着色器语法错误")
            print("2. 🔍 查看Xcode的Metal编译错误信息")
            print("3. 📋 确认参数结构体内存对齐正确")
        }
        
        if result.details.contains(where: { $0.contains("缓冲区") }) {
            print("1. 💾 检查设备内存是否充足")
            print("2. 🔧 确认Metal设备支持所需功能")
            print("3. 📊 检查数据大小是否合理")
        }
        
        print("\n🛠 通用修复步骤:")
        print("1. 确保使用支持Metal的设备/模拟器")
        print("2. 检查iOS版本兼容性")
        print("3. 验证项目Metal设置")
        print("4. 查看Xcode控制台的详细错误信息")
    }
    
    // MARK: - 集成测试辅助
    
    /// 创建简单的集成测试
    func createSimpleIntegrationTest() -> UIImage? {
        print("\n🔗 创建简单集成测试...")
        
        guard let validator = self.validator else {
            print("❌ 验证器不可用")
            return nil
        }
        
        // 创建测试图像
        let testImage = createTestImage()
        print("✅ 测试图像创建完成")
        
        // 这里可以添加实际的图像处理测试
        // 目前只返回测试图像作为占位符
        return testImage
    }
    
    private func createTestImage() -> UIImage {
        let size = CGSize(width: 100, height: 100)
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let cgContext = context.cgContext
            
            // 创建简单的测试图案
            cgContext.setFillColor(UIColor.red.cgColor)
            cgContext.fill(CGRect(x: 0, y: 0, width: 50, height: 50))
            
            cgContext.setFillColor(UIColor.green.cgColor)
            cgContext.fill(CGRect(x: 50, y: 0, width: 50, height: 50))
            
            cgContext.setFillColor(UIColor.blue.cgColor)
            cgContext.fill(CGRect(x: 0, y: 50, width: 50, height: 50))
            
            cgContext.setFillColor(UIColor.white.cgColor)
            cgContext.fill(CGRect(x: 50, y: 50, width: 50, height: 50))
        }
    }
    
    // MARK: - 性能测试
    
    /// 简单的性能基准测试
    func runPerformanceBenchmark() {
        print("\n⏱ 运行性能基准测试...")
        
        guard let validator = self.validator else {
            print("❌ 验证器不可用")
            return
        }
        
        var totalTime: TimeInterval = 0
        let iterations = 5
        
        for i in 1...iterations {
            print("  第\(i)次测试...")
            let result = validator.validateAllBasicFunctions()
            totalTime += result.executionTime
            
            if !result.isSuccess {
                print("❌ 第\(i)次测试失败")
                return
            }
        }
        
        let averageTime = totalTime / Double(iterations)
        print("✅ 性能基准测试完成")
        print("📊 平均执行时间: \(String(format: "%.3f", averageTime))秒")
        print("🎯 性能评估: \(averageTime < 0.1 ? "优秀" : averageTime < 0.5 ? "良好" : "需要优化")")
    }
}

// MARK: - 扩展方法

extension CurveValidationTests {
    
    /// 生成验证报告
    func generateValidationReport() -> String {
        var report = """
        # Metal曲线基础功能验证报告
        
        ## 测试环境
        - 设备: \(UIDevice.current.model)
        - 系统: iOS \(UIDevice.current.systemVersion)
        - 时间: \(Date())
        
        ## 测试结果
        """
        
        guard let validator = self.validator else {
            report += "\n❌ 验证器初始化失败"
            return report
        }
        
        let result = validator.validateAllBasicFunctions()
        
        report += """
        
        - 总体状态: \(result.isSuccess ? "✅ 通过" : "❌ 失败")
        - 执行时间: \(String(format: "%.3f", result.executionTime))秒
        - 详细信息:
        """
        
        for detail in result.details {
            report += "\n  - \(detail)"
        }
        
        report += """
        
        ## 建议
        \(result.isSuccess ? "可以进入下一步开发阶段" : "需要修复发现的问题后重新测试")
        """
        
        return report
    }
}
