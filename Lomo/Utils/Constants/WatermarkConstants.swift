import UIKit
import SwiftUI // For Color, if ever needed directly for constants

// MARK: - Watermark Constants
// This file centralizes constants used across various watermark styles.
// Factors are typically percentages of screen height or container dimensions.

struct WatermarkConstants {

    // MARK: - Shared Colors
    /// 共享颜色常量，用于边框颜色和其他元素
    struct Colors {
        /// 白色边框
        static let borderWhite = UIColor.white
        
        /// 黑色边框 (#191a1b)
        static let borderBlack = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        
        /// 灰色边框 (#535353)
        static let borderGray = UIColor(red: 83/255, green: 83/255, blue: 83/255, alpha: 1.0) // #535353
        
        /// 黑色文本色
        static let textBlack = UIColor.black
    }

    // MARK: - General Factory Constants
    // Used by WatermarkStyleFactory for dynamic border width calculation
    struct Factory {
        static let minBorderWidthScreenHeightFactor: CGFloat = 0.0 // 最小边框宽度为屏幕高度的0%
        static let maxBorderWidthScreenHeightFactor: CGFloat = 0.05 // 最大边框宽度为屏幕高度的5%
    }

    // MARK: - Common Element Properties
    struct Common {
        // Baseline spacing for elements in stack views, often based on 0.5% of screen height
        static let elementBaselineSpacingScreenHeightFactor: CGFloat = 0.005
        // Default tint color for placeholder/error logo
        static let placeholderLogoTintColor: UIColor = .gray
        // Default font names (can be overridden by style-specific settings)
        static let defaultSystemFont: String = "" // Represents system default
        static let defaultHelveticaNeueFont: String = "HelveticaNeue"
        static let defaultPingFangSCSemiboldFont: String = "PingFangSC-Semibold"
        static let defaultPingFangSCRegularFont: String = "PingFangSC-Regular"
        static let defaultTimesNewRomanFont: String = "TimesNewRomanPSMT"
        static let defaultCourierNewFont: String = "CourierNewPSMT"
    }

    // MARK: - Polaroid Style (Style 2)
    struct Polaroid {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.09

        // MARK: - Shared Colors
        /// 共享颜色常量，用于边框颜色和其他元素
        struct Colors {
            /// 白色边框
            static let borderWhite = UIColor.white
            
            /// 黑色边框 (#191a1b)
            static let borderBlack = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
            
            /// 灰色边框 (#535353)
            static let borderGray = UIColor(red: 83/255, green: 83/255, blue: 83/255, alpha: 1.0) // #535353
            
            /// 黑色文本色
            static let textBlack = UIColor.black
        }

        // 基础底部边框高度(9%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.09
        
        // Logo大小：使用线性增长（50%增长）
        // 基础Logo大小（屏幕高度百分比）
        static let logoSingleElementBaseSize: CGFloat = 0.030    // 3.0%
        static let logoTwoElementsBaseSize: CGFloat = 0.025      // 2.5%
        static let logoThreeElementsBaseSize: CGFloat = 0.020    // 2.0%
        
        // Logo大小增长因子（每1%边框增长对应的Logo增长）
        // 边框从9%到18%（9%增长），Logo增长50%
        // 单个元素：从3.0%增长到4.5%，增长1.5%，增长因子 = 1.5% / 9% = 0.16667
        static let logoSingleElementGrowthFactor: CGFloat = 1.5 / 9.0
        
        // 两个元素：从2.5%增长到3.75%，增长1.25%，增长因子 = 1.25% / 9% = 0.13889
        static let logoTwoElementsGrowthFactor: CGFloat = 1.25 / 9.0
        
        // 三个元素：从2.0%增长到3.0%，增长1.0%，增长因子 = 1.0% / 9% = 0.11111
        static let logoThreeElementsGrowthFactor: CGFloat = 1.0 / 9.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015    // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125     // 1.25%
        static let fontThreeElementsBaseSize: CGFloat = 0.010    // 1.0%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 边框从9%到18%（9%增长），文字增长25%
        // 单个元素：从1.5%增长到1.875%，增长0.375%，增长因子 = 0.375% / 9% = 0.04167
        static let fontSingleElementGrowthFactor: CGFloat = 0.375 / 9.0
        
        // 两个元素：从1.25%增长到1.5625%，增长0.3125%，增长因子 = 0.3125% / 9% = 0.03472
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 9.0
        
        // 三个元素：从1.0%增长到1.25%，增长0.25%，增长因子 = 0.25% / 9% = 0.02778
        static let fontThreeElementsGrowthFactor: CGFloat = 0.25 / 9.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeSingleElementFactor: CGFloat = 0.030
        static let logoSizeTwoElementsFactor: CGFloat = 0.025
        static let logoSizeThreeElementsFactor: CGFloat = 0.020

        // Text & Preference Label font sizes (as a factor of screen height)
        static let fontSingleElementFactor: CGFloat = 0.0150
        static let fontTwoElementsFactor: CGFloat = 0.0125
        static let fontThreeElementsFactor: CGFloat = 0.0100
        
        // Label max width (as a factor of its container's width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9

        // Spacing for StackView in addWatermarkElementsToBottomContainer
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Layout constants for arrangeElementsLayout (factors of screen height or container height)
        static let layout2ElementsLogoTopPaddingScreenHeightFactor: CGFloat = 0.0175
        static let layout2ElementsLogoBottomPaddingScreenHeightFactor: CGFloat = 0.0125
        // Note: elementSpacing of 0.02 for 2-elements-with-logo case was declared but unused.

        static let layout2ElementsNoLogoBottomPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout2ElementsNoLogoTextPrefSpacingScreenHeightFactor: CGFloat = 0.0075

        static let layout3ElementsTopPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout3ElementsBottomPaddingScreenHeightFactor: CGFloat = 0.010
        static let layout3ElementsTextPrefSpacingScreenHeightFactor: CGFloat = 0.0035
        
        // 新增：居中布局的元素间距常量
        // 两个元素居中布局时的垂直间距
        static let centeredLayout2ElementsSpacing: CGFloat = 0.01  // 1%屏幕高度
        
        // 三个元素居中布局时的垂直间距
        static let centeredLayout3ElementsSpacing: CGFloat = 0.008  // 0.8%屏幕高度
        
        // 动态间距计算：基础间距和增长因子
        // 两个元素间距：从1%增长到1.25%（25%增长）
        static let spacing2ElementsBaseSize: CGFloat = 0.01        // 1%屏幕高度
        static let spacing2ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
        
        // 三个元素间距：从0.75%增长到1%（33.33%增长）
        static let spacing3ElementsBaseSize: CGFloat = 0.0075      // 0.75%屏幕高度
        static let spacing3ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
    }

    // MARK: - Film Style (Style 3)
    struct Film {
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let logoSizeScreenHeightFactor: CGFloat = 0.025
        static let fontSizeScreenHeightFactor: CGFloat = 0.010

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }

    // MARK: - Custom Style 4 (Based on Polaroid)
    struct Custom4 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }

    // MARK: - Custom Style 5
    struct Custom5 {
        // Previously UIConstants.watermarkBorder5LeftWidth
        static let leftBorderScreenHeightFactor: CGFloat = 0.25

        // Logo sizes (as a factor of screen height)
        static let logoSizeSingleElementFactor: CGFloat = 0.030
        static let logoSizeTwoElementsFactor: CGFloat = 0.0275
        static let logoSizeThreeElementsFactor: CGFloat = 0.025

        // Text & Preference Label font sizes (as a factor of screen height)
        static let fontSingleElementFactor: CGFloat = 0.02
        static let fontTwoElementsFactor: CGFloat = 0.0175
        static let fontThreeElementsFactor: CGFloat = 0.015
        
        // Label max width (assuming same as Polaroid/Custom4, factor of container width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9

        // Spacing for StackView in addWatermarkElementsToLeftContainer
        // Both cases (Logo+Text, and other with multiplier) result in 0.01 * screenHeight
        static let stackViewSpacingScreenHeightFactor: CGFloat = 0.01 

        // Layout constants for arrangeElementsLayout (factors of screen height)
        static let layout2ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.075 // 当署名和文字/偏好同时出现时为0.05
        static let layout3ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.05
        // 当两个元素都不是logo时，即署名和文字/偏好同时出现时的特殊间距
        static let layout2ElementsNoLogoVerticalSpacingScreenHeightFactor: CGFloat = 0.05
    }

    // MARK: - Border Style (Style 1)
    struct Border {
        // 文字大小 (屏幕高度的百分比)
        static let fontSizeScreenHeightFactor: CGFloat = 0.01  // 1%屏幕高度
        
        // 标签最大宽度 (容器宽度的百分比)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9
        
        // 底部填充 (容器高度的百分比)
        static let bottomPaddingContainerHeightFactor: CGFloat = 0.2  // 20%容器高度
    }
    
    // MARK: - Custom Style 6 (上下宽边框风格)
    struct Custom6 {
        // 上下边框尺寸 (屏幕高度的百分比)
        static let topBorderScreenHeightFactor: CGFloat = 0.08
        static let thinTopBorderScreenHeightFactor: CGFloat = 0.06 // 从5%改为6%
        static let bottomBorderScreenHeightFactor: CGFloat = 0.08
        
        // 基础边框高度和增长率配置
        static let baseTopBorderHeight: CGFloat = 0.06 // 基础上边框高度为6%
        static let baseBottomBorderHeight: CGFloat = 0.08 // 基础下边框高度为8%
        static let maxTopBorderGrowthFactor: CGFloat = 0.5 // 最大增长50%，即从6%到9%
        static let maxBottomBorderGrowthFactor: CGFloat = 0.5 // 最大增长50%，即从8%到12%
        
        // 动态元素大小配置
        static let baseLogoSizeFactor: CGFloat = 0.02 // 基础Logo尺寸为屏幕高度的2%
        static let logoGrowthFactor: CGFloat = 0.5 // Logo尺寸最大增长50%
        static let baseFontSizeFactor: CGFloat = 0.015 // 基础字体大小为屏幕高度的1.5%
        static let fontGrowthFactor: CGFloat = 0.25 // 字体大小最大增长25%
        
        // Logo尺寸 (屏幕高度的百分比)
        static let logoSizeSingleElementFactor: CGFloat = 0.030
        static let logoSizeTwoElementsFactor: CGFloat = 0.025
        static let logoSizeThreeElementsFactor: CGFloat = 0.020
        static let logoSizeFourElementsFactor: CGFloat = 0.018
        static let logoSizeWithSignatureFactor: CGFloat = 0.020 // 当与署名同时存在时的Logo尺寸
        
        // 文字和偏好标签字体大小 (屏幕高度的百分比)
        static let fontSingleElementFactor: CGFloat = 0.0150
        static let fontTwoElementsFactor: CGFloat = 0.0125
        static let fontThreeElementsFactor: CGFloat = 0.0100
        static let fontFourElementsFactor: CGFloat = 0.0090
        
        // 署名标签字体大小 (屏幕高度的百分比)
        static let signatureFontFactor: CGFloat = 0.0150 // 改为与fontSingleElementFactor相同
        
        // 标签最大宽度 (容器宽度的百分比)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9
        
        // 元素间距 (屏幕高度的百分比)
        static let elementsSpacingFactor: CGFloat = 0.01
        
        // 布局常量 (屏幕高度的百分比)
        static let layout2ElementsVerticalSpacingFactor: CGFloat = 0.03
        static let layout3ElementsVerticalSpacingFactor: CGFloat = 0.02
        static let layout4ElementsVerticalSpacingFactor: CGFloat = 0.015
        
        // 署名在底部边框中的位置 (距底部的百分比)
        static let signatureBottomOffsetFactor: CGFloat = 0.3
        
        // 动态间距计算：基础间距和增长因子
        // 两个元素间距：从0.75%增长到1%（33%增长）
        static let spacing2ElementsBaseSize: CGFloat = 0.0075      // 0.75%屏幕高度
        static let spacing2ElementsGrowthFactor: CGFloat = 0.25 / 4.0  // 增长0.25%，边框增长4%（从8%到12%）
    }
    
    // MARK: - Custom Style 7 (基于胶片风格)
    struct Custom7 {
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let logoSizeScreenHeightFactor: CGFloat = 0.025
        static let fontSizeScreenHeightFactor: CGFloat = 0.015

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }
    
    // MARK: - Custom Style 8 (基于胶片风格，署名替换Logo)
    struct Custom8 {
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let signatureFontSizeScreenHeightFactor: CGFloat = 0.010
        static let fontSizeScreenHeightFactor: CGFloat = 0.010

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }

    // MARK: - Custom Style 9 (基于自定义水印4，新增署名功能)
    struct Custom9 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        static let fontThreeElementsBaseSize: CGFloat = 0.010  // 1.0%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0
        
        // 三个元素：从1.0%增长到1.25%，边框从6%到9%（3%增长），字体增长0.25%
        // 增长因子 = 0.25% / 3% ≈ 0.0833
        static let fontThreeElementsGrowthFactor: CGFloat = 0.25 / 3.0
        
        // 署名大小：使用线性增长（25%增长，与文字相同）
        // 基础署名大小（屏幕高度百分比）
        static let signatureSingleElementBaseSize: CGFloat = 0.015  // 1.5%，与文字相同
        static let signatureTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%，与文字相同
        static let signatureThreeElementsBaseSize: CGFloat = 0.010  // 1.0%，与文字相同
        
        // 署名大小增长因子（每1%边框增长对应的字体增长）
        // 增长率与文字相同：25%
        static let signatureSingleElementGrowthFactor: CGFloat = 0.125  // 0.375% / 3% = 0.125
        static let signatureTwoElementsGrowthFactor: CGFloat = 0.104167 // 0.3125% / 3% ≈ 0.104167
        static let signatureThreeElementsGrowthFactor: CGFloat = 0.0833  // 0.25% / 3% ≈ 0.0833
        
        // 三元素布局的间距控制
        static let threeElementsHorizontalPaddingFactor: CGFloat = 0.008 // 水平边距为屏幕高度的0.8%
        static let threeElementsVerticalSpacingFactor: CGFloat = 0.003   // 垂直间距为屏幕高度的0.3%
        
        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor
        static let signatureFontFactor: CGFloat = 0.015  // 署名大小

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }
    
    // MARK: - Custom Style 10 (基于自定义水印5，宽边框在右侧)
    struct Custom10 {
        // 与水印5相似，但宽边框在右侧
        static let rightBorderScreenHeightFactor: CGFloat = 0.25

        // Logo sizes (as a factor of screen height)
        static let logoSizeSingleElementFactor: CGFloat = 0.030  // 1个元素：3.0%
        static let logoSizeTwoElementsFactor: CGFloat = 0.0275   // 2个元素：2.75%
        static let logoSizeThreeElementsFactor: CGFloat = 0.025   // 3个元素：2.5%

        // Text Label font sizes (as a factor of screen height) - 更新注释说明这仅适用于文字标签
        static let textFontSingleElementFactor: CGFloat = 0.02    // 文字标签1个元素：2.0%
        static let textFontTwoElementsFactor: CGFloat = 0.0175    // 文字标签2个元素：1.75%
        static let textFontThreeElementsFactor: CGFloat = 0.0175  // 文字标签3个元素：1.75%
        
        // Preference Label font sizes (as a factor of screen height) - 添加偏好标签专用的字体大小设置
        static let preferenceFontSingleElementFactor: CGFloat = 0.02    // 偏好标签1个元素：2.0% 
        static let preferenceFontTwoElementsFactor: CGFloat = 0.02      // 偏好标签2个元素：2.0%
        static let preferenceFontThreeElementsFactor: CGFloat = 0.02    // 偏好标签3个元素：2.0%
        
        // 为了向后兼容保留原有常量，但标记为弃用
        @available(*, deprecated, message: "Use textFontSingleElementFactor or preferenceFontSingleElementFactor instead")
        static let fontSingleElementFactor: CGFloat = 0.02
        @available(*, deprecated, message: "Use textFontTwoElementsFactor or preferenceFontTwoElementsFactor instead")
        static let fontTwoElementsFactor: CGFloat = 0.0175
        @available(*, deprecated, message: "Use textFontThreeElementsFactor or preferenceFontThreeElementsFactor instead")
        static let fontThreeElementsFactor: CGFloat = 0.0175
        
        // Label max width (factor of container width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9

        // Spacing for StackView in addWatermarkElementsToRightContainer
        static let stackViewSpacingScreenHeightFactor: CGFloat = 0.01 

        // 基础布局间距常量和非线性缩放系数
        // 当偏好大小在100%时的基础间距值（屏幕高度百分比）
        static let basePreferenceTextSpacingScreenHeightFactor: CGFloat = 0.04 // 基础偏好和文字间距：4%
        static let baseTextLogoSpacingScreenHeightFactor: CGFloat = 0.015 // 基础文字和Logo间距：1.5%
        static let baseLogoTextSpecialSpacingScreenHeightFactor: CGFloat = 0.03 // 基础Logo和文字间距：3%
        static let baseLayout2ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.05 // 基础两元素间距：5%
        static let baseLayout3ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.02 // 基础三元素间距：2%
        
        // 非线性缩放指数 - 值越大，缩放越剧烈
        // 例如：当偏好从100%缩小到75%时，间距从4%缩小到2%（而不是等比例的3%）
        static let spacingScalingExponent: CGFloat = 1.8 // 非线性缩放指数
        
        // 保留向后兼容的原有常量（弃用）
        @available(*, deprecated, message: "Use dynamic spacing calculation with basePreferenceTextSpacingScreenHeightFactor instead")
        static let layout2ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.05 // 改为5%
        @available(*, deprecated, message: "Use dynamic spacing calculation with baseLayout3ElementsVerticalSpacingScreenHeightFactor instead")
        static let layout3ElementsVerticalSpacingScreenHeightFactor: CGFloat = 0.02 // 改为不同间距
        @available(*, deprecated, message: "Use dynamic spacing calculation with basePreferenceTextSpacingScreenHeightFactor instead")
        static let preferenceTextSpacingScreenHeightFactor: CGFloat = 0.04 // 偏好和文字间距：改为4%
        @available(*, deprecated, message: "Use dynamic spacing calculation with baseTextLogoSpacingScreenHeightFactor instead")
        static let textLogoSpacingScreenHeightFactor: CGFloat = 0.015 // 文字和Logo间距：改为1.5%
        @available(*, deprecated, message: "Use dynamic spacing calculation with baseLogoTextSpecialSpacingScreenHeightFactor instead")
        static let logoTextSpecialSpacingScreenHeightFactor: CGFloat = 0.03 // Logo和文字间距：3%
    }
    
    // MARK: - Custom Style 11 (基于自定义水印4)
    struct Custom11 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }

    // MARK: - Custom Style 12 (基于自定义水印11)
    struct Custom12 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }
    
    // MARK: - Custom Style 13 (基于自定义水印12)
    struct Custom13 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }

    // MARK: - Custom Style 14 (基于自定义水印13)
    struct Custom14 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.06

        // 基础底部边框高度(6%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.06
        
        // Logo大小：保持比例关系（50%增长）
        // 当底部边框为6%时，Logo大小为2%，比例为 2/6 = 1/3
        static let logoSizeBaseBorderRatio: CGFloat = 1.0 / 3.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015  // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125   // 1.25%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 单个元素：从1.5%增长到1.875%，边框从6%到9%（3%增长），字体增长0.375%
        // 增长因子 = 0.375% / 3% = 0.125
        static let fontSingleElementGrowthFactor: CGFloat = 0.125
        
        // 两个元素：从1.25%增长到1.5625%，边框从6%到9%（3%增长），字体增长0.3125%
        // 增长因子 = 0.3125% / 3% ≈ 0.104167
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 3.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeScreenHeightFactor: CGFloat = 0.02
        static let fontSingleElementFactor: CGFloat = Polaroid.fontSingleElementFactor
        static let fontTwoElementsFactor: CGFloat = Polaroid.fontTwoElementsFactor

        // Label max width (same as Polaroid)
        static let labelMaxWidthContainerWidthFactor: CGFloat = Polaroid.labelMaxWidthContainerWidthFactor
        
        // Spacing for StackView (assuming similar to Polaroid, using the common baseline)
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // Layout constants for arrangeElementsLayout (factors of screen height)
        // For 2 elements (horizontal layout)
        static let horizontalPaddingLandscapeScreenHeightFactor: CGFloat = 0.02
        static let horizontalPaddingPortraitScreenHeightFactor: CGFloat = 0.01
        static let defaultHorizontalPaddingScreenHeightFactor: CGFloat = 0.01 // Fallback
        
        // 新增：基于屏幕宽度的内边距常量
        static let logoLeftPaddingScreenWidthFactor: CGFloat = 0.02 // Logo左内边距为屏幕宽度的2%
        static let textRightPaddingScreenWidthFactor: CGFloat = 0.01 // 文本右内边距为屏幕宽度的1%
    }

    // MARK: - Custom Style 15 (基于自定义水印2/拍立得水印)
    struct Custom15 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.09

        // 基础底部边框高度(9%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.09
        
        // Logo大小：使用线性增长（50%增长）
        // 基础Logo大小（屏幕高度百分比）
        static let logoSingleElementBaseSize: CGFloat = 0.030    // 3.0%
        static let logoTwoElementsBaseSize: CGFloat = 0.025      // 2.5%
        static let logoThreeElementsBaseSize: CGFloat = 0.020    // 2.0%
        
        // Logo大小增长因子（每1%边框增长对应的Logo增长）
        // 边框从9%到18%（9%增长），Logo增长50%
        // 单个元素：从3.0%增长到4.5%，增长1.5%，增长因子 = 1.5% / 9% = 0.16667
        static let logoSingleElementGrowthFactor: CGFloat = 1.5 / 9.0
        
        // 两个元素：从2.5%增长到3.75%，增长1.25%，增长因子 = 1.25% / 9% = 0.13889
        static let logoTwoElementsGrowthFactor: CGFloat = 1.25 / 9.0
        
        // 三个元素：从2.0%增长到3.0%，增长1.0%，增长因子 = 1.0% / 9% = 0.11111
        static let logoThreeElementsGrowthFactor: CGFloat = 1.0 / 9.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015    // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125     // 1.25%
        static let fontThreeElementsBaseSize: CGFloat = 0.010    // 1.0%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 边框从9%到18%（9%增长），文字增长25%
        // 单个元素：从1.5%增长到1.875%，增长0.375%，增长因子 = 0.375% / 9% = 0.04167
        static let fontSingleElementGrowthFactor: CGFloat = 0.375 / 9.0
        
        // 两个元素：从1.25%增长到1.5625%，增长0.3125%，增长因子 = 0.3125% / 9% = 0.03472
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 9.0
        
        // 三个元素：从1.0%增长到1.25%，增长0.25%，增长因子 = 0.25% / 9% = 0.02778
        static let fontThreeElementsGrowthFactor: CGFloat = 0.25 / 9.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeSingleElementFactor: CGFloat = 0.030
        static let logoSizeTwoElementsFactor: CGFloat = 0.025
        static let logoSizeThreeElementsFactor: CGFloat = 0.020

        // Text & Preference Label font sizes (as a factor of screen height)
        static let fontSingleElementFactor: CGFloat = 0.0150
        static let fontTwoElementsFactor: CGFloat = 0.0125
        static let fontThreeElementsFactor: CGFloat = 0.0100
        
        // Label max width (as a factor of its container's width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9

        // Spacing for StackView in addWatermarkElementsToBottomContainer
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Layout constants for arrangeElementsLayout (factors of screen height or container height)
        static let layout2ElementsLogoTopPaddingScreenHeightFactor: CGFloat = 0.0175
        static let layout2ElementsLogoBottomPaddingScreenHeightFactor: CGFloat = 0.0125
        // Note: elementSpacing of 0.02 for 2-elements-with-logo case was declared but unused.

        static let layout2ElementsNoLogoBottomPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout2ElementsNoLogoTextPrefSpacingScreenHeightFactor: CGFloat = 0.0075

        static let layout3ElementsTopPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout3ElementsBottomPaddingScreenHeightFactor: CGFloat = 0.010
        static let layout3ElementsTextPrefSpacingScreenHeightFactor: CGFloat = 0.0035
        
        // 居中布局的元素间距常量
        // 两个元素居中布局时的垂直间距
        static let centeredLayout2ElementsSpacing: CGFloat = 0.01  // 1%屏幕高度
        
        // 三个元素居中布局时的垂直间距
        static let centeredLayout3ElementsSpacing: CGFloat = 0.008  // 0.8%屏幕高度
        
        // 动态间距计算：基础间距和增长因子
        // 两个元素间距：从1%增长到1.25%（25%增长）
        static let spacing2ElementsBaseSize: CGFloat = 0.01        // 1%屏幕高度
        static let spacing2ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
        
        // 三个元素间距：从0.75%增长到1%（33.33%增长）
        static let spacing3ElementsBaseSize: CGFloat = 0.0075      // 0.75%屏幕高度
        static let spacing3ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
    }

    // MARK: - Custom Style 17 (基于自定义水印3/胶片风格)
    struct Custom17 {
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // 水平布局间距（屏幕宽度的百分比）
        static let horizontalSpacingScreenWidthFactor: CGFloat = 0.02 // 水平元素间距为屏幕宽度的2%
        static let verticalTextStackSpacingScreenHeightFactor: CGFloat = 0.005 // 文字堆栈垂直间距为屏幕高度的0.5%

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let logoSizeScreenHeightFactor: CGFloat = 0.025
        static let fontSizeScreenHeightFactor: CGFloat = 0.015

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }
    
    // MARK: - Custom Style 18 (基于自定义水印1，简约风格边框水印)
    struct Custom18 {
        // 边框宽度为屏幕高度的1%
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let logoSizeScreenHeightFactor: CGFloat = 0.025
        static let fontSizeScreenHeightFactor: CGFloat = 0.01

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }
    
    // MARK: - Custom Style 19 (大疆水印样式，基于自定义水印15)
    struct Custom19 {
        static let bottomBorderScreenHeightFactor: CGFloat = 0.09

        // 基础底部边框高度(9%)，用于计算元素大小的基准
        static let baseBorderHeight: CGFloat = 0.09
        
        // Logo大小：使用线性增长（50%增长）
        // 基础Logo大小（屏幕高度百分比）
        static let logoSingleElementBaseSize: CGFloat = 0.030    // 3.0%
        static let logoTwoElementsBaseSize: CGFloat = 0.025      // 2.5%
        static let logoThreeElementsBaseSize: CGFloat = 0.020    // 2.0%
        
        // Logo大小增长因子（每1%边框增长对应的Logo增长）
        // 边框从9%到18%（9%增长），Logo增长50%
        // 单个元素：从3.0%增长到4.5%，增长1.5%，增长因子 = 1.5% / 9% = 0.16667
        static let logoSingleElementGrowthFactor: CGFloat = 1.5 / 9.0
        
        // 两个元素：从2.5%增长到3.75%，增长1.25%，增长因子 = 1.25% / 9% = 0.13889
        static let logoTwoElementsGrowthFactor: CGFloat = 1.25 / 9.0
        
        // 三个元素：从2.0%增长到3.0%，增长1.0%，增长因子 = 1.0% / 9% = 0.11111
        static let logoThreeElementsGrowthFactor: CGFloat = 1.0 / 9.0
        
        // 文字大小：使用线性增长（25%增长）
        // 基础字体大小（屏幕高度百分比）
        static let fontSingleElementBaseSize: CGFloat = 0.015    // 1.5%
        static let fontTwoElementsBaseSize: CGFloat = 0.0125     // 1.25%
        static let fontThreeElementsBaseSize: CGFloat = 0.010    // 1.0%
        
        // 字体大小增长因子（每1%边框增长对应的字体增长）
        // 边框从9%到18%（9%增长），文字增长25%
        // 单个元素：从1.5%增长到1.875%，增长0.375%，增长因子 = 0.375% / 9% = 0.04167
        static let fontSingleElementGrowthFactor: CGFloat = 0.375 / 9.0
        
        // 两个元素：从1.25%增长到1.5625%，增长0.3125%，增长因子 = 0.3125% / 9% = 0.03472
        static let fontTwoElementsGrowthFactor: CGFloat = 0.3125 / 9.0
        
        // 三个元素：从1.0%增长到1.25%，增长0.25%，增长因子 = 0.25% / 9% = 0.02778
        static let fontThreeElementsGrowthFactor: CGFloat = 0.25 / 9.0

        // 保留原有的固定大小常量作为备用（向下兼容）
        static let logoSizeSingleElementFactor: CGFloat = 0.030
        static let logoSizeTwoElementsFactor: CGFloat = 0.025
        static let logoSizeThreeElementsFactor: CGFloat = 0.020

        // Text & Preference Label font sizes (as a factor of screen height)
        static let fontSingleElementFactor: CGFloat = 0.0150
        static let fontTwoElementsFactor: CGFloat = 0.0125
        static let fontThreeElementsFactor: CGFloat = 0.0100
        
        // Label max width (as a factor of its container's width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9

        // Spacing for StackView in addWatermarkElementsToBottomContainer
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor

        // Layout constants for arrangeElementsLayout (factors of screen height or container height)
        static let layout2ElementsLogoTopPaddingScreenHeightFactor: CGFloat = 0.0175
        static let layout2ElementsLogoBottomPaddingScreenHeightFactor: CGFloat = 0.0125
        // Note: elementSpacing of 0.02 for 2-elements-with-logo case was declared but unused.

        static let layout2ElementsNoLogoBottomPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout2ElementsNoLogoTextPrefSpacingScreenHeightFactor: CGFloat = 0.0075

        static let layout3ElementsTopPaddingScreenHeightFactor: CGFloat = 0.015
        static let layout3ElementsBottomPaddingScreenHeightFactor: CGFloat = 0.010
        static let layout3ElementsTextPrefSpacingScreenHeightFactor: CGFloat = 0.0035
        
        // 居中布局的元素间距常量
        // 两个元素居中布局时的垂直间距
        static let centeredLayout2ElementsSpacing: CGFloat = 0.01  // 1%屏幕高度
        
        // 三个元素居中布局时的垂直间距
        static let centeredLayout3ElementsSpacing: CGFloat = 0.008  // 0.8%屏幕高度
        
        // 动态间距计算：基础间距和增长因子
        // 两个元素间距：从1%增长到1.25%（25%增长）
        static let spacing2ElementsBaseSize: CGFloat = 0.01        // 1%屏幕高度
        static let spacing2ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
        
        // 三个元素间距：从0.75%增长到1%（33.33%增长）
        static let spacing3ElementsBaseSize: CGFloat = 0.0075      // 0.75%屏幕高度
        static let spacing3ElementsGrowthFactor: CGFloat = 0.25 / 9.0  // 增长0.25%，边框增长9%
    }
    
    // MARK: - Custom Style 22 (基于自定义水印17/胶片风格)
    struct Custom22 {
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor(red: 25/255, green: 26/255, blue: 27/255, alpha: 1.0) // #191a1b
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // Element Spacing in StackView
        // Both cases (Logo+Text, and other) use 0.005 * screenHeight
        static let stackViewSpacingFactor: CGFloat = Common.elementBaselineSpacingScreenHeightFactor
        
        // 水平布局间距（屏幕宽度的百分比）
        static let horizontalSpacingScreenWidthFactor: CGFloat = 0.02 // 水平元素间距为屏幕宽度的2%
        static let verticalTextStackSpacingScreenHeightFactor: CGFloat = 0.005 // 文字堆栈垂直间距为屏幕高度的0.5%

        // Fixed sizes for elements in this style (as a factor of screen height)
        static let logoSizeScreenHeightFactor: CGFloat = 0.025
        static let fontSizeScreenHeightFactor: CGFloat = 0.015

        // Layout: offset from bottom for non-centered elements
        static let elementsBottomOffsetScreenHeightFactor: CGFloat = 0.015
    }

    // MARK: - Custom Style 23 (基于自定义水印1，拼图水印)
    struct Custom23 {
        // 边框宽度为屏幕高度的1%
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor.white
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // 字体大小为屏幕高度的1%
        static let fontSizeScreenHeightFactor: CGFloat = 0.01
        
        // Label max width (as a factor of its container's width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9
        
        // 底部padding为容器高度的10%
        static let bottomPaddingContainerHeightFactor: CGFloat = 0.1
    }
    
    // MARK: - Custom Style 24 (基于自定义水印23，拼图水印)
    struct Custom24 {
        // 边框宽度为屏幕高度的1%
        static let fixedBorderScreenHeightFactor: CGFloat = 0.01
        static let borderColor: UIColor = UIColor.white
        static let defaultTextFontName: String = Common.defaultHelveticaNeueFont

        // 字体大小为屏幕高度的1%
        static let fontSizeScreenHeightFactor: CGFloat = 0.01
        
        // Label max width (as a factor of its container's width)
        static let labelMaxWidthContainerWidthFactor: CGFloat = 0.9
        
        // 底部padding为容器高度的10%
        static let bottomPaddingContainerHeightFactor: CGFloat = 0.1
    }
} 