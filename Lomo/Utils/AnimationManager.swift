import SwiftUI

/// 动画管理器 - 负责管理所有自定义动画效果
class AnimationManager: ObservableObject {
    // MARK: - 单例
    static let shared = AnimationManager()
    private init() {
        // 初始化时设置为完全透明和75%大小
        dialScale = 0.75
        dialOpacity = 0.0
    }
    
    // MARK: - 状态
    @Published var dialScale: CGFloat = 0.75  // 刻度盘缩放值
    @Published var dialOpacity: CGFloat = 0.0  // 刻度盘透明度
    
    // MARK: - 动画常量
    private struct Constants {
        // 从 AnimationConstants 复制
        static let duration: Double = 0.35
        static let dampingFraction: Double = 0.75
        static let quickDuration: Double = 0.25
        static let quickDampingFraction: Double = 0.75
        static let symbolBounceScale: Double = 0.8
        static let symbolBounceResponse: Double = 0.15
        static let symbolBounceDamping: Double = 0.75
    }
    
    private weak var viewModel: CameraViewModel?
    
    init(viewModel: CameraViewModel) {
        self.viewModel = viewModel
    }
    
    // MARK: - 刻度盘动画
    
    /// 刻度盘出现动画
    /// - Parameters:
    ///   - startPoint: 动画起始点
    ///   - completion: 动画完成回调
    func showDialAnimation(from startPoint: CGPoint, completion: (() -> Void)? = nil) {
        // 确保初始状态
        dialScale = 0.75  // 从75%开始
        dialOpacity = 0.0
        
        // 分别执行缩放和透明度动画
        withAnimation(.spring(response: 0.25, dampingFraction: 0.75)) {
            dialScale = 1.0  // 缩放到100%
        }
        
        withAnimation(.easeIn(duration: 0.2)) {
            dialOpacity = 1.0
        }
        
        completion?()
    }
    
    /// 刻度盘消失动画
    /// - Parameters:
    ///   - endPoint: 动画终点
    ///   - completion: 动画完成回调
    func hideDialAnimation(to endPoint: CGPoint, completion: (() -> Void)? = nil) {
        // 分别执行缩放和透明度动画
        withAnimation(.timingCurve(0.33, 0, 0.67, 1, duration: 0.25)) {  // 使用平滑的缓出曲线
            dialScale = 0.75  // 缩放到75%
        }
        
        withAnimation(.easeOut(duration: 0.2)) {
            dialOpacity = 0.0
        }
        
        completion?()
    }
    
    /// 重置动画状态
    func resetDialAnimation() {
        dialScale = 0.75  // 重置为75%
        dialOpacity = 0.0
    }
    
    // MARK: - 工具方法
    
    /// 获取动画时长
    /// - Parameter type: 动画类型
    /// - Returns: 动画持续时间
    func getAnimationDuration(for type: AnimationType) -> Double {
        switch type {
        case .dialShow, .dialHide:
            return 0.25
        }
    }
}

// MARK: - 枚举定义

/// 动画类型
enum AnimationType {
    case dialShow   // 刻度盘显示
    case dialHide   // 刻度盘隐藏
    // 后续可以添加更多动画类型
}

/// 动画曲线类型
enum AnimationCurve {
    case spring     // 弹性动画
    case easeOut    // 缓出动画
    case easeInOut  // 缓入缓出
    // 后续可以添加更多动画曲线
} 