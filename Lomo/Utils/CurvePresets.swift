import Foundation
import CoreGraphics

/// 曲线预设系统 - 提供专业级预设曲线
/// 包含常用的摄影和图像处理曲线预设
extension CurveProcessor {
    
    /// 曲线预设枚举
    enum CurvePreset: String, CaseIterable, Identifiable {
        case linear = "线性"
        case sCurve = "S曲线"
        case inverseCurve = "反转"
        case brightCurve = "提亮"
        case darkCurve = "压暗"
        case contrastCurve = "增强对比"
        case softCurve = "柔和"
        case dramaticCurve = "戏剧性"
        case vintageCurve = "复古"
        case filmCurve = "胶片"
        case portraitCurve = "人像"
        case landscapeCurve = "风景"
        case blackWhiteCurve = "黑白"
        case warmCurve = "暖调"
        case coolCurve = "冷调"
        
        var id: String { rawValue }
        
        /// 显示名称
        var displayName: String {
            return self.rawValue
        }        
        /// 获取预设的控制点
        var points: [CGPoint] {
            switch self {
            case .linear:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .sCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.25, y: 0.2),
                    CGPoint(x: 0.75, y: 0.8),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .inverseCurve:
                return [
                    CGPoint(x: 0, y: 1),
                    CGPoint(x: 1, y: 0)
                ]
                
            case .brightCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.5, y: 0.6),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .darkCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.5, y: 0.4),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .contrastCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.2, y: 0.1),
                    CGPoint(x: 0.8, y: 0.9),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .softCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.3, y: 0.35),
                    CGPoint(x: 0.7, y: 0.65),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .dramaticCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.15, y: 0.05),
                    CGPoint(x: 0.4, y: 0.3),
                    CGPoint(x: 0.6, y: 0.7),
                    CGPoint(x: 0.85, y: 0.95),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .vintageCurve:
                return [
                    CGPoint(x: 0, y: 0.1),
                    CGPoint(x: 0.3, y: 0.35),
                    CGPoint(x: 0.7, y: 0.75),
                    CGPoint(x: 1, y: 0.95)
                ]
                
            case .filmCurve:
                return [
                    CGPoint(x: 0, y: 0.05),
                    CGPoint(x: 0.25, y: 0.22),
                    CGPoint(x: 0.5, y: 0.5),
                    CGPoint(x: 0.75, y: 0.78),
                    CGPoint(x: 1, y: 0.95)
                ]
                
            case .portraitCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.2, y: 0.15),
                    CGPoint(x: 0.6, y: 0.65),
                    CGPoint(x: 0.9, y: 0.92),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .landscapeCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.1, y: 0.05),
                    CGPoint(x: 0.3, y: 0.25),
                    CGPoint(x: 0.7, y: 0.75),
                    CGPoint(x: 0.9, y: 0.95),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .blackWhiteCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.3, y: 0.2),
                    CGPoint(x: 0.7, y: 0.8),
                    CGPoint(x: 1, y: 1)
                ]
                
            case .warmCurve:
                return [
                    CGPoint(x: 0, y: 0.02),
                    CGPoint(x: 0.4, y: 0.45),
                    CGPoint(x: 0.8, y: 0.85),
                    CGPoint(x: 1, y: 0.98)
                ]
                
            case .coolCurve:
                return [
                    CGPoint(x: 0, y: 0),
                    CGPoint(x: 0.3, y: 0.28),
                    CGPoint(x: 0.7, y: 0.72),
                    CGPoint(x: 1, y: 1)
                ]
            }
        }
        
        /// 预设描述
        var description: String {
            switch self {
            case .linear: return "无调整的线性曲线"
            case .sCurve: return "经典S曲线，增强对比度"
            case .inverseCurve: return "反转曲线，负片效果"
            case .brightCurve: return "整体提亮曲线"
            case .darkCurve: return "整体压暗曲线"
            case .contrastCurve: return "强对比度曲线"
            case .softCurve: return "柔和对比度曲线"
            case .dramaticCurve: return "戏剧性高对比曲线"
            case .vintageCurve: return "复古胶片风格曲线"
            case .filmCurve: return "电影胶片风格曲线"
            case .portraitCurve: return "人像优化曲线"
            case .landscapeCurve: return "风景摄影曲线"
            case .blackWhiteCurve: return "黑白摄影曲线"
            case .warmCurve: return "暖色调曲线"
            case .coolCurve: return "冷色调曲线"
            }
        }
        
        /// 预设类别
        var category: PresetCategory {
            switch self {
            case .linear:
                return .basic
            case .sCurve, .contrastCurve, .softCurve:
                return .contrast
            case .brightCurve, .darkCurve:
                return .exposure
            case .dramaticCurve, .vintageCurve, .filmCurve:
                return .artistic
            case .portraitCurve, .landscapeCurve, .blackWhiteCurve:
                return .photography
            case .warmCurve, .coolCurve:
                return .color
            case .inverseCurve:
                return .special
            }
        }
        
        /// 预设强度建议
        var recommendedIntensity: Float {
            switch self {
            case .linear: return 1.0
            case .sCurve, .contrastCurve: return 0.8
            case .brightCurve, .darkCurve: return 0.7
            case .softCurve: return 0.9
            case .dramaticCurve: return 0.6
            case .vintageCurve, .filmCurve: return 0.75
            case .portraitCurve: return 0.85
            case .landscapeCurve: return 0.8
            case .blackWhiteCurve: return 0.9
            case .warmCurve, .coolCurve: return 0.7
            case .inverseCurve: return 1.0
            }
        }
    }
    
    /// 预设类别枚举
    enum PresetCategory: String, CaseIterable {
        case basic = "基础"
        case contrast = "对比度"
        case exposure = "曝光"
        case artistic = "艺术"
        case photography = "摄影"
        case color = "色彩"
        case special = "特殊"
        
        var presets: [CurvePreset] {
            return CurvePreset.allCases.filter { $0.category == self }
        }
    }
    
    // MARK: - 预设相关方法
    
    /// 从预设生成LUT
    static func generateLUT(from preset: CurvePreset, quality: CurveQuality = .standard) -> [Float] {
        return generateLUT(from: preset.points, quality: quality)
    }
    
    /// 获取所有预设的LUT
    static func generateAllPresetLUTs(quality: CurveQuality = .standard) -> [String: [Float]] {
        var presetLUTs: [String: [Float]] = [:]
        
        for preset in CurvePreset.allCases {
            presetLUTs[preset.rawValue] = generateLUT(from: preset, quality: quality)
        }
        
        return presetLUTs
    }
    
    /// 获取指定类别的预设LUT
    static func generateCategoryLUTs(for category: PresetCategory, quality: CurveQuality = .standard) -> [String: [Float]] {
        var categoryLUTs: [String: [Float]] = [:]
        
        for preset in category.presets {
            categoryLUTs[preset.rawValue] = generateLUT(from: preset, quality: quality)
        }
        
        return categoryLUTs
    }
    
    /// 查找最相似的预设
    static func findSimilarPreset(to points: [CGPoint]) -> CurvePreset? {
        let targetLUT = generateLUT(from: points, quality: .realtime)
        var bestMatch: CurvePreset?
        var bestScore: Float = Float.greatestFiniteMagnitude
        
        for preset in CurvePreset.allCases {
            let presetLUT = generateLUT(from: preset, quality: .realtime)
            let score = calculateLUTDifference(targetLUT, presetLUT)
            
            if score < bestScore {
                bestScore = score
                bestMatch = preset
            }
        }
        
        return bestMatch
    }
    
    /// 计算两个LUT之间的差异
    private static func calculateLUTDifference(_ lut1: [Float], _ lut2: [Float]) -> Float {
        guard lut1.count == lut2.count else { return Float.greatestFiniteMagnitude }
        
        var totalDifference: Float = 0.0
        for i in 0..<lut1.count {
            totalDifference += abs(lut1[i] - lut2[i])
        }
        
        return totalDifference / Float(lut1.count)
    }
}
