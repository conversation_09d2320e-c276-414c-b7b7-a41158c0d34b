import UIKit

/// 模拟图像生成器
/// 用于在模拟器环境中生成各种类型的模拟图像
class MockImageGenerator {
    
    /// 生成渐变图像
    /// - Parameters:
    ///   - size: 图像尺寸
    ///   - colors: 渐变颜色数组
    ///   - startPoint: 渐变起点
    ///   - endPoint: 渐变终点
    /// - Returns: 渐变图像
    static func generateGradientImage(
        size: CGSize,
        colors: [UIColor] = [.blue, .purple, .red],
        startPoint: CGPoint = CGPoint(x: 0, y: 0),
        endPoint: CGPoint = CGPoint(x: 1, y: 1)
    ) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            let cgColors = colors.map { $0.cgColor }
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let colorLocations: [CGFloat] = stride(from: 0.0, through: 1.0, by: 1.0 / CGFloat(colors.count - 1)).map { $0 }
            
            if let gradient = CGGradient(
                colorsSpace: colorSpace,
                colors: cgColors as CFArray,
                locations: colorLocations
            ) {
                let startPointConverted = CGPoint(x: startPoint.x * size.width, y: startPoint.y * size.height)
                let endPointConverted = CGPoint(x: endPoint.x * size.width, y: endPoint.y * size.height)
                
                context.cgContext.drawLinearGradient(
                    gradient,
                    start: startPointConverted,
                    end: endPointConverted,
                    options: []
                )
            }
        }
    }
    
    /// 生成网格图像
    /// - Parameters:
    ///   - size: 图像尺寸
    ///   - gridSize: 网格大小
    ///   - lineColor: 网格线颜色
    ///   - backgroundColor: 背景颜色
    /// - Returns: 网格图像
    static func generateGridImage(
        size: CGSize,
        gridSize: CGFloat = 50,
        lineColor: UIColor = .white,
        backgroundColor: UIColor = .black
    ) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 绘制背景
            backgroundColor.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 设置线条属性
            lineColor.setStroke()
            context.cgContext.setLineWidth(1.0)
            
            // 绘制水平线
            for y in stride(from: 0, to: size.height, by: gridSize) {
                context.cgContext.move(to: CGPoint(x: 0, y: y))
                context.cgContext.addLine(to: CGPoint(x: size.width, y: y))
            }
            
            // 绘制垂直线
            for x in stride(from: 0, to: size.width, by: gridSize) {
                context.cgContext.move(to: CGPoint(x: x, y: 0))
                context.cgContext.addLine(to: CGPoint(x: x, y: size.height))
            }
            
            context.cgContext.strokePath()
        }
    }
    
    /// 生成文本图像
    /// - Parameters:
    ///   - size: 图像尺寸
    ///   - text: 文本内容
    ///   - font: 字体
    ///   - textColor: 文本颜色
    ///   - backgroundColor: 背景颜色
    /// - Returns: 文本图像
    static func generateTextImage(
        size: CGSize,
        text: String,
        font: UIFont = .systemFont(ofSize: 24),
        textColor: UIColor = .white,
        backgroundColor: UIColor = .black
    ) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        
        return renderer.image { context in
            // 绘制背景
            backgroundColor.setFill()
            context.fill(CGRect(origin: .zero, size: size))
            
            // 绘制文本
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor,
                .paragraphStyle: paragraphStyle
            ]
            
            let textRect = CGRect(x: 0, y: (size.height - font.lineHeight) / 2, width: size.width, height: font.lineHeight)
            text.draw(in: textRect, withAttributes: attributes)
        }
    }
    
    /// 生成模拟相机图像
    /// - Parameters:
    ///   - size: 图像尺寸
    ///   - index: 图像索引
    /// - Returns: 模拟相机图像
    static func generateMockCameraImage(size: CGSize, index: Int) -> UIImage {
        switch index % 3 {
        case 0:
            // 渐变图像
            return generateGradientImage(
                size: size,
                colors: [.blue, .purple, .red],
                startPoint: CGPoint(x: 0, y: 0),
                endPoint: CGPoint(x: 1, y: 1)
            )
        case 1:
            // 网格图像
            return generateGridImage(
                size: size,
                gridSize: 50,
                lineColor: .white,
                backgroundColor: .darkGray
            )
        case 2:
            // 文本图像
            return generateTextImage(
                size: size,
                text: "模拟相机 #\(index + 1)",
                font: .systemFont(ofSize: 36, weight: .bold),
                textColor: .white,
                backgroundColor: .black
            )
        default:
            return UIImage()
        }
    }
    
    /// 生成一组模拟相机图像
    /// - Parameters:
    ///   - size: 图像尺寸
    ///   - count: 图像数量
    /// - Returns: 模拟相机图像数组
    static func generateMockCameraImages(size: CGSize, count: Int = 3) -> [UIImage] {
        return (0..<count).map { generateMockCameraImage(size: size, index: $0) }
    }
}