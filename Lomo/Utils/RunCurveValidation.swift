import Foundation

/// 曲线验证执行器 - 提供简单的接口来运行验证测试
/// 可以在开发过程中快速验证Metal曲线功能
class RunCurveValidation {
    
    /// 运行完整的第1步验证
    static func validateStep1() {
        print("🚀 开始第1步Metal曲线基础功能验证")
        print("时间: \(Date())")
        print("")
        
        let tests = CurveValidationTests()
        
        // 运行所有基础测试
        tests.runAllBasicTests()
        
        // 运行性能基准测试
        tests.runPerformanceBenchmark()
        
        // 生成验证报告
        let report = tests.generateValidationReport()
        print("\n" + String(repeating: "=", count: 60))
        print("📄 验证报告")
        print(String(repeating: "=", count: 60))
        print(report)
        
        print("\n🏁 第1步验证完成")
    }
    
    /// 快速验证 - 只检查编译
    static func quickValidation() {
        print("⚡ 快速验证 - 检查Metal着色器编译")
        
        let tests = CurveValidationTests()
        let success = tests.runQuickCompileCheck()
        
        if success {
            print("✅ 快速验证通过 - 可以继续开发")
        } else {
            print("❌ 快速验证失败 - 需要修复编译问题")
        }
    }
    
    /// 创建测试图像
    static func createTestImage() {
        print("🖼 创建集成测试图像")
        
        let tests = CurveValidationTests()
        if let testImage = tests.createSimpleIntegrationTest() {
            print("✅ 测试图像创建成功")
            // 这里可以保存图像或进行其他处理
        } else {
            print("❌ 测试图像创建失败")
        }
    }
}

// MARK: - 使用示例

/*
使用方法：

1. 完整验证第1步功能：
   RunCurveValidation.validateStep1()

2. 快速编译检查：
   RunCurveValidation.quickValidation()

3. 创建测试图像：
   RunCurveValidation.createTestImage()

在ViewController或其他地方调用：

class ViewController: UIViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 验证Metal曲线功能
        RunCurveValidation.validateStep1()
    }
}

或者在AppDelegate中：

func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
    
    // 开发阶段验证
    #if DEBUG
    RunCurveValidation.quickValidation()
    #endif
    
    return true
}
*/
