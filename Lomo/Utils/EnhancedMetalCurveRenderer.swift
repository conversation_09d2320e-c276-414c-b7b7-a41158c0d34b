import Foundation
import Metal
import MetalKit
import UIKit

/// 增强的Metal曲线渲染器 - 第2步实现
/// 支持硬件采样器、高质量插值、sRGB色彩空间处理
class EnhancedMetalCurveRenderer {
    
    // MARK: - Metal资源
    private let device: MTLDevice
    private let commandQueue: MTLCommandQueue
    private let library: MTLLibrary
    
    // MARK: - 管线状态缓存
    private var pipelineStates: [String: MTLComputePipelineState] = [:]
    private var lutTextures: [String: MTLTexture] = [:]
    private var lutSampler: MTLSamplerState
    
    // MARK: - 质量模式枚举
    enum RenderQuality: Int, CaseIterable {
        case realtime = 0      // 实时预览
        case standard = 1      // 标准质量
        case highQuality = 2   // 高质量
        
        var shaderName: String {
            switch self {
            case .realtime: return "apply_curve_realtime"
            case .standard: return "apply_curve_enhanced"
            case .highQuality: return "apply_multichannel_curves"
            }
        }
        
        var description: String {
            switch self {
            case .realtime: return "实时预览"
            case .standard: return "标准质量"
            case .highQuality: return "高质量"
            }
        }
    }
    
    // MARK: - 色彩空间模式
    enum ColorSpace: Int {
        case linear = 0    // 线性空间
        case srgb = 1      // sRGB空间
    }
    
    // MARK: - 初始化
    init?() {
        guard let device = MTLCreateSystemDefaultDevice() else {
            print("❌ Metal设备不可用")
            return nil
        }
        self.device = device
        
        guard let commandQueue = device.makeCommandQueue() else {
            print("❌ 无法创建Metal命令队列")
            return nil
        }
        self.commandQueue = commandQueue
        
        guard let library = device.makeDefaultLibrary() else {
            print("❌ 无法加载Metal库")
            return nil
        }
        self.library = library
        
        // 创建LUT采样器
        let samplerDescriptor = MTLSamplerDescriptor()
        samplerDescriptor.minFilter = .linear
        samplerDescriptor.magFilter = .linear
        samplerDescriptor.sAddressMode = .clampToEdge
        samplerDescriptor.normalizedCoordinates = true
        
        guard let sampler = device.makeSamplerState(descriptor: samplerDescriptor) else {
            print("❌ 无法创建LUT采样器")
            return nil
        }
        self.lutSampler = sampler
        
        // 预编译管线状态
        setupPipelineStates()
        
        print("✅ EnhancedMetalCurveRenderer 初始化成功")
    }
    
    // MARK: - 设置方法
    
    private func setupPipelineStates() {
        let shaderNames = [
            "apply_curve_basic",
            "apply_curve_enhanced", 
            "apply_curve_realtime",
            "apply_multichannel_curves",
            "test_hardware_sampler",
            "test_srgb_conversion"
        ]
        
        for shaderName in shaderNames {
            if let function = library.makeFunction(name: shaderName) {
                do {
                    let pipelineState = try device.makeComputePipelineState(function: function)
                    pipelineStates[shaderName] = pipelineState
                    print("✅ 管线状态创建成功: \(shaderName)")
                } catch {
                    print("❌ 管线状态创建失败: \(shaderName) - \(error)")
                }
            } else {
                print("❌ 无法找到着色器函数: \(shaderName)")
            }
        }
    }
    
    // MARK: - LUT纹理管理
    
    /// 创建1D LUT纹理
    func createLUTTexture(from lut: [Float], name: String) -> Bool {
        let textureDescriptor = MTLTextureDescriptor()
        textureDescriptor.textureType = .type1D
        textureDescriptor.pixelFormat = .r32Float
        textureDescriptor.width = lut.count
        textureDescriptor.usage = [.shaderRead]
        
        guard let texture = device.makeTexture(descriptor: textureDescriptor) else {
            print("❌ 无法创建LUT纹理: \(name)")
            return false
        }
        
        // 上传LUT数据
        texture.replace(region: MTLRegionMake1D(0, lut.count),
                       mipmapLevel: 0,
                       withBytes: lut,
                       bytesPerRow: lut.count * MemoryLayout<Float>.size)
        
        lutTextures[name] = texture
        print("✅ LUT纹理创建成功: \(name), 大小: \(lut.count)")
        return true
    }
    
    /// 更新LUT纹理
    func updateLUTTexture(with lut: [Float], name: String) -> Bool {
        guard let texture = lutTextures[name] else {
            return createLUTTexture(from: lut, name: name)
        }
        
        texture.replace(region: MTLRegionMake1D(0, lut.count),
                       mipmapLevel: 0,
                       withBytes: lut,
                       bytesPerRow: lut.count * MemoryLayout<Float>.size)
        
        return true
    }
    
    // MARK: - 主要渲染方法
    
    /// 应用曲线到图像 - 增强版本
    func applyCurves(to image: UIImage, 
                    rgbLUT: [Float],
                    quality: RenderQuality = .standard,
                    colorSpace: ColorSpace = .srgb,
                    intensity: Float = 1.0) -> UIImage? {
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        // 1. 创建输入纹理
        guard let inputTexture = createTexture(from: image) else {
            print("❌ 无法创建输入纹理")
            return nil
        }
        
        // 2. 创建输出纹理
        guard let outputTexture = createOutputTexture(like: inputTexture) else {
            print("❌ 无法创建输出纹理")
            return nil
        }
        
        // 3. 创建或更新LUT纹理
        if !updateLUTTexture(with: rgbLUT, name: "rgb") {
            print("❌ 无法更新RGB LUT纹理")
            return nil
        }
        
        // 4. 获取管线状态
        guard let pipelineState = pipelineStates[quality.shaderName] else {
            print("❌ 无法获取管线状态: \(quality.shaderName)")
            return nil
        }
        
        // 5. 创建参数
        let params = EnhancedCurveParameters(
            intensity: intensity,
            qualityMode: Float(quality.rawValue),
            colorSpace: Float(colorSpace.rawValue),
            useHardwareSampler: 1.0
        )
        
        // 6. 执行渲染
        guard let result = executeRender(
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            pipelineState: pipelineState,
            params: params,
            rgbLUT: rgbLUT
        ) else {
            print("❌ 渲染执行失败")
            return nil
        }
        
        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        print("✅ 曲线渲染完成，耗时: \(String(format: "%.2f", processingTime * 1000))ms")
        
        return result
    }
    
    /// 应用多通道曲线
    func applyMultiChannelCurves(to image: UIImage,
                                rgbLUT: [Float],
                                redLUT: [Float],
                                greenLUT: [Float],
                                blueLUT: [Float],
                                rgbIntensity: Float = 1.0,
                                redIntensity: Float = 1.0,
                                greenIntensity: Float = 1.0,
                                blueIntensity: Float = 1.0,
                                colorSpace: ColorSpace = .srgb) -> UIImage? {
        
        // 创建纹理
        guard let inputTexture = createTexture(from: image),
              let outputTexture = createOutputTexture(like: inputTexture) else {
            return nil
        }
        
        // 创建所有LUT纹理
        let lutNames = ["rgb", "red", "green", "blue"]
        let luts = [rgbLUT, redLUT, greenLUT, blueLUT]
        
        for (name, lut) in zip(lutNames, luts) {
            if !updateLUTTexture(with: lut, name: name) {
                print("❌ 无法更新LUT纹理: \(name)")
                return nil
            }
        }
        
        // 获取多通道管线状态
        guard let pipelineState = pipelineStates["apply_multichannel_curves"] else {
            print("❌ 无法获取多通道管线状态")
            return nil
        }
        
        // 创建多通道参数
        let params = EnhancedMultiChannelCurveParameters(
            rgbIntensity: rgbIntensity,
            redIntensity: redIntensity,
            greenIntensity: greenIntensity,
            blueIntensity: blueIntensity,
            qualityMode: Float(RenderQuality.highQuality.rawValue),
            colorSpace: Float(colorSpace.rawValue),
            useHardwareSampler: 1.0,
            padding: 0.0
        )
        
        return executeMultiChannelRender(
            inputTexture: inputTexture,
            outputTexture: outputTexture,
            pipelineState: pipelineState,
            params: params,
            luts: luts
        )
    }

    // MARK: - 私有渲染方法

    private func executeRender(inputTexture: MTLTexture,
                              outputTexture: MTLTexture,
                              pipelineState: MTLComputePipelineState,
                              params: EnhancedCurveParameters,
                              rgbLUT: [Float]) -> UIImage? {

        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return nil
        }

        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(inputTexture, index: 0)
        encoder.setTexture(outputTexture, index: 1)

        // 设置LUT纹理（如果可用）
        if let rgbTexture = lutTextures["rgb"] {
            encoder.setTexture(rgbTexture, index: 2)
        }

        // 设置参数
        var mutableParams = params
        encoder.setBytes(&mutableParams, length: MemoryLayout<EnhancedCurveParameters>.size, index: 0)
        encoder.setBytes(rgbLUT, length: rgbLUT.count * MemoryLayout<Float>.size, index: 1)

        // 设置采样器
        encoder.setSamplerState(lutSampler, index: 0)

        // 配置线程组
        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (inputTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )

        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()

        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()

        if let error = commandBuffer.error {
            print("❌ Metal命令执行失败: \(error)")
            return nil
        }

        return createImage(from: outputTexture)
    }

    private func executeMultiChannelRender(inputTexture: MTLTexture,
                                          outputTexture: MTLTexture,
                                          pipelineState: MTLComputePipelineState,
                                          params: EnhancedMultiChannelCurveParameters,
                                          luts: [[Float]]) -> UIImage? {

        guard let commandBuffer = commandQueue.makeCommandBuffer(),
              let encoder = commandBuffer.makeComputeCommandEncoder() else {
            return nil
        }

        encoder.setComputePipelineState(pipelineState)
        encoder.setTexture(inputTexture, index: 0)
        encoder.setTexture(outputTexture, index: 1)

        // 设置所有LUT纹理
        let lutNames = ["rgb", "red", "green", "blue"]
        for (index, name) in lutNames.enumerated() {
            if let texture = lutTextures[name] {
                encoder.setTexture(texture, index: 2 + index)
            }
        }

        // 设置参数
        var mutableParams = params
        encoder.setBytes(&mutableParams, length: MemoryLayout<EnhancedMultiChannelCurveParameters>.size, index: 0)

        // 设置所有LUT缓冲区
        for (index, lut) in luts.enumerated() {
            encoder.setBytes(lut, length: lut.count * MemoryLayout<Float>.size, index: 1 + index)
        }

        encoder.setSamplerState(lutSampler, index: 0)

        let threadgroupSize = MTLSize(width: 16, height: 16, depth: 1)
        let threadgroupCount = MTLSize(
            width: (inputTexture.width + threadgroupSize.width - 1) / threadgroupSize.width,
            height: (inputTexture.height + threadgroupSize.height - 1) / threadgroupSize.height,
            depth: 1
        )

        encoder.dispatchThreadgroups(threadgroupCount, threadsPerThreadgroup: threadgroupSize)
        encoder.endEncoding()

        commandBuffer.commit()
        commandBuffer.waitUntilCompleted()

        if let error = commandBuffer.error {
            print("❌ 多通道渲染失败: \(error)")
            return nil
        }

        return createImage(from: outputTexture)
    }

    // MARK: - 辅助方法

    private func createTexture(from image: UIImage) -> MTLTexture? {
        guard let cgImage = image.cgImage else { return nil }

        let textureLoader = MTKTextureLoader(device: device)
        do {
            return try textureLoader.newTexture(cgImage: cgImage, options: [.SRGB: false])
        } catch {
            print("❌ 输入纹理创建失败: \(error)")
            return nil
        }
    }

    private func createOutputTexture(like inputTexture: MTLTexture) -> MTLTexture? {
        let descriptor = MTLTextureDescriptor()
        descriptor.pixelFormat = inputTexture.pixelFormat
        descriptor.width = inputTexture.width
        descriptor.height = inputTexture.height
        descriptor.usage = [.shaderWrite, .shaderRead]

        return device.makeTexture(descriptor: descriptor)
    }

    private func createImage(from texture: MTLTexture) -> UIImage? {
        let width = texture.width
        let height = texture.height
        let bytesPerRow = width * 4
        var data = [UInt8](repeating: 0, count: width * height * 4)

        texture.getBytes(&data,
                        bytesPerRow: bytesPerRow,
                        from: MTLRegionMake2D(0, 0, width, height),
                        mipmapLevel: 0)

        guard let provider = CGDataProvider(data: Data(data) as CFData) else { return nil }

        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let bitmapInfo = CGBitmapInfo(rawValue: CGImageAlphaInfo.premultipliedLast.rawValue)

        guard let cgImage = CGImage(width: width, height: height,
                                   bitsPerComponent: 8,
                                   bitsPerPixel: 32,
                                   bytesPerRow: bytesPerRow,
                                   space: colorSpace,
                                   bitmapInfo: bitmapInfo,
                                   provider: provider,
                                   decode: nil,
                                   shouldInterpolate: true,
                                   intent: .defaultIntent) else { return nil }

        return UIImage(cgImage: cgImage)
    }
}

// MARK: - 参数结构体定义

/// 与Metal着色器对应的增强参数结构体
struct EnhancedCurveParameters {
    let intensity: Float
    let qualityMode: Float
    let colorSpace: Float
    let useHardwareSampler: Float
}

struct EnhancedMultiChannelCurveParameters {
    let rgbIntensity: Float
    let redIntensity: Float
    let greenIntensity: Float
    let blueIntensity: Float
    let qualityMode: Float
    let colorSpace: Float
    let useHardwareSampler: Float
    let padding: Float
}
