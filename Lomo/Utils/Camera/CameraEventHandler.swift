import SwiftUI
import AVFoundation

class CameraEventHandler {
    weak var viewModel: CameraViewModel?
    
    init(viewModel: CameraViewModel?) {
        self.viewModel = viewModel
    }
    
    // MARK: - 格式化和转换逻辑
    
    // 镜头值格式化
    func formatLensValue(lens: String, isSelected: Bool, currentZoomFactor: CGFloat) -> String {
        if isSelected {
            return ZoomUtils.formatZoomFactor(Double(currentZoomFactor))
        }
        // 非选中状态显示原始镜头值
        if let value = Double(lens.replacingOccurrences(of: "x", with: "")) {
            return ZoomUtils.formatZoomFactor(value)
        }
        return lens.replacingOccurrences(of: "x", with: "")
    }
    
    // 闪光灯图标获取
    func getFlashIcon() -> String {
        guard let viewModel = viewModel else { return "bolt.fill" }
        switch viewModel.state.flashMode {
        case .off, .on:
            return "bolt.fill"
        case .auto:
            return "bolt.badge.automatic.fill"
        }
    }
    
    // 图标颜色获取
    func getIconColor(for option: String) -> Color {
        guard let viewModel = viewModel else { return .white }
        switch option {
        case "livephoto":
            return viewModel.state.isLivePhotoEnabled ? UIConstants.dialIndicatorColor : .white
        case "bolt.fill":
            return viewModel.state.flashMode == .off ? .white : UIConstants.dialIndicatorColor
        case "HDR":
            return viewModel.isHDREnabled ? UIConstants.dialIndicatorColor : .white
        case "16:9":
            return viewModel.state.isAspectRatioOptionsVisible ? UIConstants.dialIndicatorColor : .white
        case "4K":
            return viewModel.state.isResolutionOptionsVisible ? UIConstants.dialIndicatorColor : .white
        case "30fps":
            return .white
        case "SDR":
            return viewModel.state.isColorSpaceOptionsVisible ? UIConstants.dialIndicatorColor : .white
        case "JPG":
            return viewModel.state.isPhotoFormatOptionsVisible ? UIConstants.dialIndicatorColor : .white
        case "4:3":
            return viewModel.state.isPhotoRatioOptionsVisible ? UIConstants.dialIndicatorColor : .white
        default:
            return .white
        }
    }
    
    // 时间格式化
    func formatTime(_ time: TimeInterval) -> String {
        guard let viewModel = viewModel else { return "00:00" }
        return viewModel.formattedTime(time)
    }
    
    // MARK: - 状态判断逻辑
    
    // 视频模式状态判断
    func isVideoModeAndRecording() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode && viewModel.state.isRecording
    }
    
    func isVideoModeAndNotRecording() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode && !viewModel.state.isRecording
    }
    
    // 显示状态判断
    func shouldShowParameterExpanded() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isParameterExpanded && !viewModel.state.isRecording
    }
    
    func shouldShowLeftButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isRightButtonExpanded && !viewModel.state.isRecording
    }
    
    func shouldShowRightButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isLeftButtonExpanded && !viewModel.state.isRecording
    }
    
    // 模式切换判断
    func shouldShowVideoModeControls() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode
    }
    
    func shouldShowPhotoModeControls() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isVideoMode
    }
    
    // 新增的状态判断方法
    func shouldShowCameraSwitchButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isParameterExpanded && !viewModel.state.isRecording
    }
    
    func shouldShowTimerButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isVideoMode
    }
    
    func shouldShowStabilizationButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode
    }
    
    func shouldShowPhotoModeButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isVideoMode
    }
    
    func shouldShowZebraButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return viewModel.state.isVideoMode
    }
    
    func shouldShowEditButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isRecording
    }
    
    func shouldShowFilterButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isRecording
    }
    
    func shouldShowPreviewButton() -> Bool {
        guard let viewModel = viewModel else { return false }
        return !viewModel.state.isRecording
    }
    
    func getRecordingBackgroundColor() -> Color {
        guard let viewModel = viewModel else { return .black.opacity(UIConstants.topBarBackgroundOpacity) }
        return viewModel.state.isVideoMode && viewModel.state.isRecording ? 
            UIConstants.recordingColor : 
            Color.black.opacity(UIConstants.topBarBackgroundOpacity)
    }
    
    // MARK: - 按钮点击处理
    func handleIconButtonTap(option: String) {
        guard let viewModel = viewModel else { return }
        
        withAnimation(AnimationConstants.standardSpring) {
            switch option {
            case "livephoto":
                viewModel.toggleLivePhoto()
            case "bolt.fill":
                viewModel.toggleFlash()
            case "HDR":
                viewModel.toggleHDR()
            case "HEVC":
                viewModel.toggleVideoEncodingOptions()
            case "16:9":
                viewModel.toggleAspectRatioOptions()
            case "4K":
                viewModel.toggleResolutionOptions()
            case "30fps":
                viewModel.toggleFrameRateOptions()
            case "SDR":
                viewModel.toggleColorSpaceOptions()
            case "JPG":
                viewModel.togglePhotoFormatOptions()
            case "4:3":
                viewModel.togglePhotoRatioOptions()
            default:
                break
            }
        }
    }
    
    // MARK: - 手势处理
    func handleTouchBegan() {
        guard let viewModel = viewModel else { return }
        viewModel.handleTouchBegan()
    }
    
    func handleTouchEnded() {
        guard let viewModel = viewModel else { return }
        viewModel.handleTouchEnded()
    }
    
    // MARK: - 长按手势处理
    func handleLongPress() {
        guard let viewModel = viewModel else { return }
        viewModel.showZoomDial()
    }
    
    // MARK: - 计时器管理
    
    // 参数计时器
    func resetParameterTimer() {
        guard let viewModel = viewModel else { return }
        viewModel.resetParameterTimer()
    }
    
    func expandParameter() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.expandParameter()
        }
    }
    
    func collapseParameter() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.collapseParameter()
        }
    }
    
    // 左侧按钮计时器
    func resetLeftButtonTimer() {
        guard let viewModel = viewModel else { return }
        viewModel.resetLeftButtonTimer()
    }
    
    func toggleLeftButton() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.toggleLeftButton()
        }
    }
    
    // 右侧按钮计时器
    func resetRightButtonTimer() {
        guard let viewModel = viewModel else { return }
        viewModel.resetRightButtonTimer()
    }
    
    func toggleRightButton() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.toggleRightButton()
        }
    }
    
    // 功能按钮操作
    func toggleGrid() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleGrid()
    }
    
    func toggleHistogram() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleHistogram()
    }
    
    func toggleLevel() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleLevel()
    }
    
    func togglePeaking() {
        guard let viewModel = viewModel else { return }
        viewModel.togglePeaking()
    }
    
    func toggleZebra() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleZebra()
    }
    
    func toggleStabilization() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleStabilization()
    }
    
    func toggleTimerOptions() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleTimerOptions()
    }
    
    func togglePhotoModeOptions() {
        guard let viewModel = viewModel else { return }
        viewModel.togglePhotoModeOptions()
    }
    
    func toggleFlipOptions() {
        guard let viewModel = viewModel else { return }
        viewModel.toggleFlipOptions()
    }
    
    // MARK: - 视频模式相关
    func toggleVideoMode() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.toggleVideoMode()
        }
    }

    func toggleRecording() {
        guard let viewModel = viewModel else { return }
        withAnimation(AnimationConstants.standardSpring) {
            viewModel.toggleRecording()
        }
    }

    func capturePhoto() {
        guard let viewModel = viewModel else { return }
        viewModel.capturePhoto()
    }

    // MARK: - 相机控制
    func switchCamera() {
        guard let viewModel = viewModel else { return }
        viewModel.switchCamera()
    }

    func showZoomDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showZoomDial()
    }

    // MARK: - 刻度盘相关
    func showApertureDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showApertureDial()
    }

    func showShutterDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showShutterDial()
    }

    func showISODial() {
        guard let viewModel = viewModel else { return }
        viewModel.showISODial()
    }

    func showExposureDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showExposureDial()
    }

    func showFocusDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showFocusDial()
    }

    func showTintDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showTintDial()
    }

    func showTemperatureDial() {
        guard let viewModel = viewModel else { return }
        viewModel.showTemperatureDial()
    }
} 