import Foundation
import AVFoundation

// MARK: - AVCaptureDevice.DiscoverySession 扩展
extension AVCaptureDevice.DiscoverySession {
    /// 创建用于发现相机设备的会话
    static var cameraDiscoverySession: AVCaptureDevice.DiscoverySession {
        AVCaptureDevice.DiscoverySession(
            deviceTypes: [
                .builtInWideAngleCamera,
                .builtInTelephotoCamera,
                .builtInUltraWideCamera
            ],
            mediaType: .video,
            position: .back
        )
    }
}

// MARK: - AVCaptureDevice 扩展
extension AVCaptureDevice {
    /// 获取指定位置的相机设备
    static func camera(position: AVCaptureDevice.Position) -> AVCaptureDevice? {
        AVCaptureDevice.default(.builtInWideAngleCamera,
                              for: .video,
                              position: position)
    }
    
    /// 获取设备的最大变焦倍数
    var maxZoomFactor: Float? {
        virtualDeviceSwitchOverVideoZoomFactors.last as? Float
    }
    
    /// 获取设备的对焦能力
    var focusCapabilities: [String: Bool] {
        [
            "pointOfInterestSupported": isFocusPointOfInterestSupported,
            "autoFocusSupported": isFocusModeSupported(.autoFocus),
            "continuousAutoFocusSupported": isFocusModeSupported(.continuousAutoFocus),
            "lockedFocusSupported": isFocusModeSupported(.locked)
        ]
    }
    
    /// 获取设备的曝光能力
    var exposureCapabilities: [String: Bool] {
        [
            "pointOfInterestSupported": isExposurePointOfInterestSupported,
            "autoExposureSupported": isExposureModeSupported(.autoExpose),
            "continuousAutoExposureSupported": isExposureModeSupported(.continuousAutoExposure),
            "lockedExposureSupported": isExposureModeSupported(.locked),
            "customExposureSupported": isExposureModeSupported(.custom)
        ]
    }
    
    /// 获取设备的视场角信息
    var fieldOfViewInfo: [String: Float] {
        var fovInfo: [String: Float] = [:]
        fovInfo["activeFormatFOV"] = activeFormat.videoFieldOfView
        let fovValues = formats.map { $0.videoFieldOfView }
        fovInfo["minFOV"] = fovValues.min() ?? 0
        fovInfo["maxFOV"] = fovValues.max() ?? 0
        return fovInfo
    }
    
    /// 获取设备支持的捕获格式
    var supportedFormats: [[String: Any]] {
        formats.map { format -> [String: Any] in
            let dimensions = CMVideoFormatDescriptionGetDimensions(format.formatDescription)
            let frameRates = format.videoSupportedFrameRateRanges.map { range -> [String: Float64] in
                [
                    "minFrameRate": range.minFrameRate,
                    "maxFrameRate": range.maxFrameRate
                ]
            }
            
            return [
                "width": dimensions.width,
                "height": dimensions.height,
                "frameRates": frameRates,
                "fieldOfView": format.videoFieldOfView,
                "isVideoStabilizationSupported": format.isVideoStabilizationModeSupported(.auto),
                "pixelFormat": CMFormatDescriptionGetMediaSubType(format.formatDescription)
            ]
        }
    }
    
    /// 根据变焦倍数获取镜头标识
    var lensIdentifier: String? {
        guard let maxZoom = maxZoomFactor else { return nil }
        switch maxZoom {
        case _ where maxZoom >= 5: return "5"
        case _ where maxZoom >= 3: return "3"
        case _ where maxZoom >= 2: return "2"
        default: return nil
        }
    }
}

// MARK: - AVCaptureVideoPreviewLayer 扩展
extension AVCaptureVideoPreviewLayer {
    /// 创建相机预览层
    static func preview(session: AVCaptureSession, frame: CGRect) -> AVCaptureVideoPreviewLayer {
        let layer = AVCaptureVideoPreviewLayer(session: session)
        layer.frame = frame
        layer.videoGravity = .resizeAspectFill
        return layer
    }
}
