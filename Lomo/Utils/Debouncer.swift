// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 防抖工具类 - 用于减少频繁调用
/// 在MVVM-S架构中用于优化性能
class Debouncer {
    private let delay: TimeInterval
    private var workItem: DispatchWorkItem?
    
    init(delay: TimeInterval) {
        self.delay = delay
    }
    
    /// 调度执行
    /// - Parameter action: 要执行的动作
    func schedule(_ action: @escaping () -> Void) {
        workItem?.cancel()
        workItem = DispatchWorkItem(block: action)
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem!)
    }
    
    /// 立即执行并取消之前的调度
    /// - Parameter action: 要执行的动作
    func executeImmediately(_ action: @escaping () -> Void) {
        workItem?.cancel()
        action()
    }
    
    /// 取消当前调度
    func cancel() {
        workItem?.cancel()
        workItem = nil
    }
    
    /// 检查是否有待执行的任务
    var hasPendingWork: Bool {
        return workItem != nil && !(workItem?.isCancelled ?? true)
    }
}