import Foundation
import CoreGraphics

/// 曲线边界管理器 - 专业级边界约束和验证系统
/// 确保曲线编辑器中的控制点和生成的曲线始终在有效范围内
class CurveBoundaryManager {
    
    // MARK: - 常量定义
    
    /// 边界容差 - 用于浮点数比较
    static let boundaryTolerance: CGFloat = 0.001
    
    /// 默认最小点间距
    static let defaultMinSpacing: CGFloat = 0.01
    
    /// 边界缓冲区大小
    static let boundaryBuffer: CGFloat = 0.02
    
    /// 最大曲率限制
    static let maxCurvature: CGFloat = 0.8
    
    /// 曲线采样精度
    static let curveSampleCount = 100
    
    // MARK: - 主要边界约束方法
    
    /// 应用实时边界约束（用于拖动过程中）
    /// - Parameters:
    ///   - point: 原始点位置
    ///   - index: 点的索引
    ///   - points: 当前所有控制点
    ///   - allowBuffer: 是否允许边界缓冲区
    /// - Returns: 约束后的点位置
    static func applyRealtimeBoundaryConstraints(
        to point: CGPoint,
        at index: Int,
        in points: [CGPoint],
        allowBuffer: Bool = true
    ) -> CGPoint {
        
        var constrainedPoint = point
        
        // 1. 基础边界约束
        constrainedPoint = applyBasicBoundaryConstraints(to: constrainedPoint, allowBuffer: allowBuffer)
        
        // 2. 相邻点约束
        constrainedPoint = applyAdjacentPointConstraints(
            to: constrainedPoint,
            at: index,
            in: points
        )
        
        // 3. 边界点特殊约束
        if isBoundaryPoint(at: index, in: points) {
            constrainedPoint = applyBoundaryPointConstraints(
                to: constrainedPoint,
                at: index,
                in: points
            )
        }
        
        return constrainedPoint
    }
    
    /// 应用完整边界验证（用于最终验证）
    /// - Parameter points: 控制点数组
    /// - Returns: 验证并修正后的控制点数组
    static func applyCompleteBoundaryValidation(to points: [CGPoint]) -> [CGPoint] {
        var validatedPoints = points
        
        // 1. 基础约束
        validatedPoints = validatedPoints.map { applyBasicBoundaryConstraints(to: $0, allowBuffer: false) }
        
        // 2. 排序
        validatedPoints.sort { $0.x < $1.x }
        
        // 3. 点间距约束
        validatedPoints = enforceMinimumSpacing(in: validatedPoints)
        
        // 4. 边界点修正
        validatedPoints = fixBoundaryPoints(in: validatedPoints)
        
        // 5. 曲线形状验证
        validatedPoints = validateCurveShape(points: validatedPoints)
        
        return validatedPoints
    }
    
    // MARK: - 基础约束方法
    
    /// 应用基础边界约束
    private static func applyBasicBoundaryConstraints(to point: CGPoint, allowBuffer: Bool) -> CGPoint {
        let buffer = allowBuffer ? boundaryBuffer : 0.0
        
        let minX = buffer
        let maxX = 1.0 - buffer
        let minY = buffer
        let maxY = 1.0 - buffer
        
        return CGPoint(
            x: max(minX, min(maxX, point.x)),
            y: max(minY, min(maxY, point.y))
        )
    }
    
    /// 应用相邻点约束
    private static func applyAdjacentPointConstraints(
        to point: CGPoint,
        at index: Int,
        in points: [CGPoint]
    ) -> CGPoint {
        guard points.count > 1 else { return point }
        
        let minSpacing = calculateDynamicMinSpacing(for: points.count)
        var constrainedX = point.x
        
        if index == 0 {
            // 第一个点
            let nextX = points.count > 1 ? points[1].x : 1.0
            constrainedX = max(0.0, min(nextX - minSpacing, constrainedX))
        } else if index == points.count - 1 {
            // 最后一个点
            let prevX = points[index - 1].x
            constrainedX = max(prevX + minSpacing, min(1.0, constrainedX))
        } else {
            // 中间点
            let prevX = points[index - 1].x
            let nextX = points[index + 1].x
            constrainedX = max(prevX + minSpacing, min(nextX - minSpacing, constrainedX))
        }
        
        return CGPoint(x: constrainedX, y: point.y)
    }
    
    /// 应用边界点约束
    private static func applyBoundaryPointConstraints(
        to point: CGPoint,
        at index: Int,
        in points: [CGPoint]
    ) -> CGPoint {
        let boundaryTolerance: CGFloat = 0.05
        
        if index == 0 {
            // 第一个点应该靠近左边界
            let constrainedX = max(0.0, min(boundaryTolerance, point.x))
            return CGPoint(x: constrainedX, y: point.y)
        } else if index == points.count - 1 {
            // 最后一个点应该靠近右边界
            let constrainedX = max(1.0 - boundaryTolerance, min(1.0, point.x))
            return CGPoint(x: constrainedX, y: point.y)
        }
        
        return point
    }
    
    // MARK: - 辅助方法
    
    /// 计算动态最小点间距
    private static func calculateDynamicMinSpacing(for pointCount: Int) -> CGFloat {
        let scaleFactor = max(1.0, CGFloat(pointCount) / 10.0)
        return defaultMinSpacing * scaleFactor
    }
    
    /// 检查是否为边界点
    private static func isBoundaryPoint(at index: Int, in points: [CGPoint]) -> Bool {
        return index == 0 || index == points.count - 1
    }
    
    /// 强制执行最小点间距
    private static func enforceMinimumSpacing(in points: [CGPoint]) -> [CGPoint] {
        guard points.count > 1 else { return points }
        
        var spacedPoints = points
        let minSpacing = calculateDynamicMinSpacing(for: points.count)
        
        for i in 1..<spacedPoints.count {
            let prevX = spacedPoints[i - 1].x
            let currentX = spacedPoints[i].x
            
            if currentX - prevX < minSpacing {
                spacedPoints[i].x = min(1.0, prevX + minSpacing)
            }
        }
        
        return spacedPoints
    }
    
    /// 修正边界点
    private static func fixBoundaryPoints(in points: [CGPoint]) -> [CGPoint] {
        guard points.count >= 2 else { return points }
        
        var fixedPoints = points
        
        // 确保第一个点在左边界
        if fixedPoints[0].x > 0.1 {
            fixedPoints[0].x = 0.0
        }
        
        // 确保最后一个点在右边界
        let lastIndex = fixedPoints.count - 1
        if fixedPoints[lastIndex].x < 0.9 {
            fixedPoints[lastIndex].x = 1.0
        }
        
        return fixedPoints
    }
    
    /// 验证曲线形状
    private static func validateCurveShape(points: [CGPoint]) -> [CGPoint] {
        guard points.count >= 3 else { return points }
        
        var validatedPoints = points
        
        // 检查每个中间点的曲率
        for i in 1..<(validatedPoints.count - 1) {
            let p1 = validatedPoints[i - 1]
            let p2 = validatedPoints[i]
            let p3 = validatedPoints[i + 1]
            
            let curvature = calculateCurvature(p1: p1, p2: p2, p3: p3)
            
            if curvature > maxCurvature {
                // 平滑化处理
                let smoothedY = (p1.y + p3.y) / 2.0
                let blendFactor: CGFloat = 0.5
                validatedPoints[i].y = p2.y * (1.0 - blendFactor) + smoothedY * blendFactor
            }
        }
        
        return validatedPoints
    }
    
    /// 计算三点曲率
    private static func calculateCurvature(p1: CGPoint, p2: CGPoint, p3: CGPoint) -> CGFloat {
        let dx1 = p2.x - p1.x
        let dy1 = p2.y - p1.y
        let dx2 = p3.x - p2.x
        let dy2 = p3.y - p2.y
        
        let angle1 = atan2(dy1, dx1)
        let angle2 = atan2(dy2, dx2)
        let angleDiff = abs(angle2 - angle1)
        
        return min(angleDiff, 2 * .pi - angleDiff) / .pi
    }
    
    // MARK: - 曲线验证方法
    
    /// 验证整条曲线是否在边界内
    /// - Parameter points: 控制点数组
    /// - Returns: 是否在边界内
    static func isCurveWithinBounds(points: [CGPoint]) -> Bool {
        // 1. 检查控制点
        for point in points {
            if point.x < 0.0 || point.x > 1.0 || point.y < 0.0 || point.y > 1.0 {
                return false
            }
        }
        
        // 2. 检查曲线插值点
        return validateCurveInterpolation(points: points)
    }
    
    /// 验证曲线插值是否在边界内
    private static func validateCurveInterpolation(points: [CGPoint]) -> Bool {
        guard points.count > 2 else { return true }
        
        for i in 0..<curveSampleCount {
            let t = CGFloat(i) / CGFloat(curveSampleCount - 1)
            let interpolatedY = interpolateCurveValue(at: t, points: points)
            
            if interpolatedY < 0.0 || interpolatedY > 1.0 {
                return false
            }
        }
        
        return true
    }
    
    /// 在指定X位置插值曲线Y值
    private static func interpolateCurveValue(at x: CGFloat, points: [CGPoint]) -> CGFloat {
        guard points.count >= 2 else { return x }
        
        // 简单线性插值
        for i in 0..<(points.count - 1) {
            let p1 = points[i]
            let p2 = points[i + 1]
            
            if x >= p1.x && x <= p2.x {
                let t = (x - p1.x) / (p2.x - p1.x)
                return p1.y + t * (p2.y - p1.y)
            }
        }
        
        // 边界情况
        if x <= points.first!.x {
            return points.first!.y
        } else {
            return points.last!.y
        }
    }
}
