import Foundation
import SwiftUI

/// 动画工具类，提供动画相关的辅助功能
class AnimationUtils {
    // 动画参数存储，所有组件共享
    static var sharedAnimationParameters: [String: Any] = [
        "duration": 0.5,
        "curve": UIView.AnimationOptions.curveEaseInOut.rawValue
    ]
    
    /// 从UIKit动画曲线转换为SwiftUI动画
    /// - Parameters:
    ///   - duration: 动画持续时间
    ///   - curve: UIKit的动画曲线选项
    /// - Returns: 对应的SwiftUI动画
    static func swiftUIAnimation(duration: TimeInterval, curve: UInt) -> Animation {
        let curveOption = UIView.AnimationOptions(rawValue: curve)
        
        switch curveOption {
        case .curveEaseIn:
            return .easeIn(duration: duration)
        case .curveEaseOut:
            return .easeOut(duration: duration)
        case .curveEaseInOut:
            return .easeInOut(duration: duration)
        case .curveLinear:
            return .linear(duration: duration)
        default:
            // 默认使用easeInOut
            return .easeInOut(duration: duration)
        }
    }
    
    /// 从UIKit动画曲线转换为CAMediaTimingFunction
    /// - Parameter curve: UIKit的动画曲线选项
    /// - Returns: 对应的CAMediaTimingFunction名称
    static func caMediaTimingFunction(from curve: UInt) -> CAMediaTimingFunctionName {
        switch curve {
        case UIView.AnimationOptions.curveEaseIn.rawValue:
            return .easeIn
        case UIView.AnimationOptions.curveEaseOut.rawValue:
            return .easeOut
        case UIView.AnimationOptions.curveEaseInOut.rawValue:
            return .default
        case UIView.AnimationOptions.curveLinear.rawValue:
            return .linear
        default:
            return .default
        }
    }
    
    /// 从键盘通知中提取动画参数
    /// - Parameter notification: 键盘通知
    /// - Returns: 包含动画持续时间和曲线的元组
    static func extractKeyboardAnimationParameters(from notification: Notification) -> (duration: TimeInterval, curve: UInt)? {
        guard let durationValue = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? NSNumber,
              let curveValue = notification.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? NSNumber else {
            return nil
        }
        
        let duration = durationValue.doubleValue
        let curve = curveValue.intValue
        let curveOption = UIView.AnimationOptions(rawValue: UInt(curve << 16))
        
        return (duration, curveOption.rawValue)
    }
    
    /// 更新共享动画参数并返回SwiftUI动画
    /// - Parameter notification: 键盘通知
    /// - Returns: 对应的SwiftUI动画
    static func updateSharedAnimationParameters(from notification: Notification) -> Animation {
        if let params = extractKeyboardAnimationParameters(from: notification) {
            sharedAnimationParameters["duration"] = params.duration
            sharedAnimationParameters["curve"] = params.curve
            
            return swiftUIAnimation(duration: params.duration, curve: params.curve)
        }
        
        // 如果提取失败，返回默认动画
        return .easeInOut(duration: 0.25)
    }
} 