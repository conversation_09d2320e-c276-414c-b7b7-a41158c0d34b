import Foundation
import CoreGraphics

/// 曲线控制点管理器 - 专业级控制点验证和操作
/// 负责控制点的验证、排序、边界处理和优化
class CurveControlPointManager {
    
    // MARK: - 常量定义
    
    /// 最小点间距 - 防止控制点过于密集
    static let minimumPointSpacing: CGFloat = 0.01
    
    /// 边界容差 - 用于边界检查
    static let boundaryTolerance: CGFloat = 0.001
    
    /// 最大控制点数量
    static let maxControlPoints = 16
    
    /// 最小控制点数量
    static let minControlPoints = 2
    
    // MARK: - 控制点验证
    
    /// 验证控制点数组的有效性
    /// - Parameter points: 控制点数组
    /// - Returns: 验证结果和错误信息
    static func validateControlPoints(_ points: [CGPoint]) -> (isValid: Bool, error: ValidationError?) {
        // 检查数量
        if points.count < minControlPoints {
            return (false, .tooFewPoints(points.count))
        }
        
        if points.count > maxControlPoints {
            return (false, .tooManyPoints(points.count))
        }
        
        // 检查排序
        for i in 1..<points.count {
            if points[i].x <= points[i-1].x {
                return (false, .invalidOrder(i))
            }
        }
        
        // 检查边界
        guard let firstPoint = points.first, let lastPoint = points.last else {
            return (false, .emptyArray)
        }
        
        if firstPoint.x > boundaryTolerance {
            return (false, .missingStartPoint(firstPoint.x))
        }
        
        if lastPoint.x < (1.0 - boundaryTolerance) {
            return (false, .missingEndPoint(lastPoint.x))
        }
        
        // 检查Y坐标范围
        for (index, point) in points.enumerated() {
            if point.y < 0.0 || point.y > 1.0 {
                return (false, .invalidYRange(index, point.y))
            }
        }
        
        // 检查点间距
        for i in 1..<points.count {
            let spacing = points[i].x - points[i-1].x
            if spacing < minimumPointSpacing {
                return (false, .pointsTooClose(i, spacing))
            }
        }
        
        return (true, nil)
    }
    
    /// 验证错误类型
    enum ValidationError: Error, CustomStringConvertible {
        case tooFewPoints(Int)
        case tooManyPoints(Int)
        case invalidOrder(Int)
        case emptyArray
        case missingStartPoint(CGFloat)
        case missingEndPoint(CGFloat)
        case invalidYRange(Int, CGFloat)
        case pointsTooClose(Int, CGFloat)
        
        var description: String {
            switch self {
            case .tooFewPoints(let count):
                return "控制点数量不足: \(count)，至少需要 \(minControlPoints) 个"
            case .tooManyPoints(let count):
                return "控制点数量过多: \(count)，最多支持 \(maxControlPoints) 个"
            case .invalidOrder(let index):
                return "控制点顺序错误，索引 \(index) 处的X坐标不递增"
            case .emptyArray:
                return "控制点数组为空"
            case .missingStartPoint(let x):
                return "缺少起始点，第一个点的X坐标为 \(x)，应该接近0"
            case .missingEndPoint(let x):
                return "缺少结束点，最后一个点的X坐标为 \(x)，应该接近1"
            case .invalidYRange(let index, let y):
                return "索引 \(index) 处的Y坐标 \(y) 超出范围 [0,1]"
            case .pointsTooClose(let index, let spacing):
                return "索引 \(index) 处的控制点间距过小: \(spacing)，最小间距为 \(minimumPointSpacing)"
            }
        }
    }
    
    // MARK: - 控制点处理
    
    /// 自动修复控制点数组
    /// - Parameter points: 原始控制点
    /// - Returns: 修复后的控制点
    static func autoFixControlPoints(_ points: [CGPoint]) -> [CGPoint] {
        guard !points.isEmpty else {
            return CurveProcessor.createDefaultPoints()
        }
        
        var fixedPoints = points
        
        // 1. 排序
        fixedPoints.sort { $0.x < $1.x }
        
        // 2. 限制数量
        if fixedPoints.count > maxControlPoints {
            fixedPoints = Array(fixedPoints.prefix(maxControlPoints))
        }
        
        // 3. 确保Y坐标在有效范围内
        fixedPoints = fixedPoints.map { point in
            CGPoint(x: point.x, y: max(0.0, min(1.0, point.y)))
        }
        
        // 4. 移除重复和过近的点
        fixedPoints = removeDuplicateAndClosePoints(fixedPoints)
        
        // 5. 确保边界点
        fixedPoints = ensureBoundaryPoints(fixedPoints)
        
        // 6. 最终验证，如果仍然无效则返回默认点
        let (isValid, _) = validateControlPoints(fixedPoints)
        if !isValid {
            return CurveProcessor.createDefaultPoints()
        }
        
        return fixedPoints
    }
    
    /// 移除重复和过近的控制点
    private static func removeDuplicateAndClosePoints(_ points: [CGPoint]) -> [CGPoint] {
        guard points.count > 1 else { return points }
        
        var filteredPoints: [CGPoint] = [points[0]]
        
        for i in 1..<points.count {
            let currentPoint = points[i]
            let lastPoint = filteredPoints.last!
            
            // 检查X坐标间距
            if currentPoint.x - lastPoint.x >= minimumPointSpacing {
                filteredPoints.append(currentPoint)
            } else {
                // 如果点太近，保留Y值更合理的点
                let linearY = currentPoint.x // 线性情况下 y = x
                let currentDistance = abs(currentPoint.y - linearY)
                let lastDistance = abs(lastPoint.y - linearY)
                
                if currentDistance < lastDistance {
                    filteredPoints[filteredPoints.count - 1] = currentPoint
                }
            }
        }
        
        return filteredPoints
    }
    
    /// 确保边界点存在
    private static func ensureBoundaryPoints(_ points: [CGPoint]) -> [CGPoint] {
        var processedPoints = points
        
        // 确保起始点
        if processedPoints.first?.x != 0.0 {
            let firstY = processedPoints.first?.y ?? 0.0
            processedPoints.insert(CGPoint(x: 0.0, y: firstY), at: 0)
        }
        
        // 确保结束点
        if processedPoints.last?.x != 1.0 {
            let lastY = processedPoints.last?.y ?? 1.0
            processedPoints.append(CGPoint(x: 1.0, y: lastY))
        }
        
        return processedPoints
    }
    
    // MARK: - 控制点操作
    
    /// 添加新的控制点
    /// - Parameters:
    ///   - points: 现有控制点
    ///   - newPoint: 新控制点
    /// - Returns: 添加后的控制点数组和新点的索引
    static func addControlPoint(to points: [CGPoint], newPoint: CGPoint) -> (points: [CGPoint], index: Int?) {
        guard points.count < maxControlPoints else {
            print("⚠️ 已达到最大控制点数量限制")
            return (points, nil)
        }
        
        let clampedPoint = CGPoint(x: max(0.0, min(1.0, newPoint.x)), 
                                  y: max(0.0, min(1.0, newPoint.y)))
        
        var newPoints = points
        var insertIndex = points.count
        
        // 找到插入位置
        for (index, point) in points.enumerated() {
            if clampedPoint.x < point.x {
                insertIndex = index
                break
            }
        }
        
        // 检查是否与现有点太近
        if insertIndex > 0 {
            let prevPoint = points[insertIndex - 1]
            if clampedPoint.x - prevPoint.x < minimumPointSpacing {
                print("⚠️ 新控制点与前一个点太近")
                return (points, nil)
            }
        }
        
        if insertIndex < points.count {
            let nextPoint = points[insertIndex]
            if nextPoint.x - clampedPoint.x < minimumPointSpacing {
                print("⚠️ 新控制点与后一个点太近")
                return (points, nil)
            }
        }
        
        newPoints.insert(clampedPoint, at: insertIndex)
        return (newPoints, insertIndex)
    }
    
    /// 移除控制点
    /// - Parameters:
    ///   - points: 现有控制点
    ///   - index: 要移除的点的索引
    /// - Returns: 移除后的控制点数组
    static func removeControlPoint(from points: [CGPoint], at index: Int) -> [CGPoint] {
        guard index >= 0 && index < points.count else {
            print("⚠️ 无效的控制点索引: \(index)")
            return points
        }
        
        guard points.count > minControlPoints else {
            print("⚠️ 无法移除控制点，已达到最小数量限制")
            return points
        }
        
        // 不允许移除边界点
        let point = points[index]
        if point.x <= boundaryTolerance || point.x >= (1.0 - boundaryTolerance) {
            print("⚠️ 无法移除边界控制点")
            return points
        }
        
        var newPoints = points
        newPoints.remove(at: index)
        return newPoints
    }
    
    /// 更新控制点位置
    /// - Parameters:
    ///   - points: 现有控制点
    ///   - index: 要更新的点的索引
    ///   - newPosition: 新位置
    /// - Returns: 更新后的控制点数组
    static func updateControlPoint(in points: [CGPoint], at index: Int, to newPosition: CGPoint) -> [CGPoint] {
        guard index >= 0 && index < points.count else {
            print("⚠️ 无效的控制点索引: \(index)")
            return points
        }
        
        var newPoints = points
        let clampedPosition = CGPoint(x: max(0.0, min(1.0, newPosition.x)), 
                                     y: max(0.0, min(1.0, newPosition.y)))
        
        // 检查X坐标约束
        let minX: CGFloat = index > 0 ? points[index - 1].x + minimumPointSpacing : 0.0
        let maxX: CGFloat = index < points.count - 1 ? points[index + 1].x - minimumPointSpacing : 1.0
        
        let constrainedX = max(minX, min(maxX, clampedPosition.x))
        let finalPosition = CGPoint(x: constrainedX, y: clampedPosition.y)
        
        newPoints[index] = finalPosition
        return newPoints
    }
    
    // MARK: - 工具方法
    
    /// 计算控制点的统计信息
    static func analyzeControlPoints(_ points: [CGPoint]) -> ControlPointStats {
        guard !points.isEmpty else {
            return ControlPointStats(count: 0, xRange: 0, yRange: 0, averageSpacing: 0, complexity: 0)
        }
        
        let count = points.count
        let xRange = (points.last?.x ?? 0) - (points.first?.x ?? 0)
        let yValues = points.map { $0.y }
        let yRange = (yValues.max() ?? 0) - (yValues.min() ?? 0)
        
        var totalSpacing: CGFloat = 0
        for i in 1..<points.count {
            totalSpacing += points[i].x - points[i-1].x
        }
        let averageSpacing = count > 1 ? totalSpacing / CGFloat(count - 1) : 0
        
        // 计算复杂度 - 基于Y值的变化
        var complexity: CGFloat = 0
        for i in 1..<points.count {
            complexity += abs(points[i].y - points[i-1].y)
        }
        
        return ControlPointStats(count: count, xRange: xRange, yRange: yRange, 
                               averageSpacing: averageSpacing, complexity: complexity)
    }
    
    /// 控制点统计信息
    struct ControlPointStats {
        let count: Int
        let xRange: CGFloat
        let yRange: CGFloat
        let averageSpacing: CGFloat
        let complexity: CGFloat
    }
}
