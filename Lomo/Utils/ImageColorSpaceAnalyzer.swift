import UIKit
import CoreImage
import ImageIO

/// 图像色彩空间分析器
/// 用于检测图像的色彩空间和是否为线性图像
class ImageColorSpaceAnalyzer {
    
    /// 检测图像是否为线性色彩空间
    /// - Parameter image: 输入图像
    /// - Returns: 如果是线性图像返回true，否则返回false
    static func isLinearImage(_ image: UIImage) -> Bool {
        // 方法1: 检查图像的色彩空间信息
        if let colorSpaceInfo = getImageColorSpaceInfo(image) {
            return colorSpaceInfo.isLinear
        }
        
        // 方法2: 通过像素分析检测
        return detectLinearByPixelAnalysis(image)
    }
    
    /// 获取图像的色彩空间信息
    /// - Parameter image: 输入图像
    /// - Returns: 色彩空间信息
    static func getImageColorSpaceInfo(_ image: UIImage) -> ColorSpaceInfo? {
        guard let cgImage = image.cgImage else { return nil }

        let colorSpace = cgImage.colorSpace
        let colorSpaceName = colorSpace?.name

        // 简化的线性色彩空间检测
        var isLinear = false

        if let name = colorSpaceName {
            let nameString = name as String
            let lowercasedName = nameString.lowercased()

            // 检查色彩空间名称中是否包含线性标识
            isLinear = lowercasedName.contains("linear") ||
                      lowercasedName.contains("rec2020") ||
                      lowercasedName.contains("rec.2020")
        }

        // 检查ICC配置文件
        var hasLinearProfile = false
        if let iccData = colorSpace?.copyICCData() {
            let iccString = String(data: iccData as Data, encoding: .utf8) ??
                           String(data: iccData as Data, encoding: .ascii) ?? ""
            let lowercasedICC = iccString.lowercased()
            hasLinearProfile = lowercasedICC.contains("linear") ||
                              lowercasedICC.contains("gamma 1.0") ||
                              lowercasedICC.contains("gamma=1.0")
        }

        return ColorSpaceInfo(
            name: colorSpaceName as String? ?? "Unknown",
            isLinear: isLinear || hasLinearProfile,
            model: colorSpace?.model ?? .unknown
        )
    }
    
    /// 通过像素分析检测是否为线性图像
    /// - Parameter image: 输入图像
    /// - Returns: 是否为线性图像
    private static func detectLinearByPixelAnalysis(_ image: UIImage) -> Bool {
        guard let cgImage = image.cgImage else { return false }
        
        // 创建小尺寸的采样图像以提高性能
        let sampleSize = CGSize(width: 100, height: 100)
        guard let resizedImage = resizeImage(cgImage, to: sampleSize) else { return false }
        
        // 分析像素分布
        let pixelData = getPixelData(from: resizedImage)
        return analyzePixelDistribution(pixelData)
    }
    
    /// 调整图像大小
    private static func resizeImage(_ cgImage: CGImage, to size: CGSize) -> CGImage? {
        let context = CGContext(
            data: nil,
            width: Int(size.width),
            height: Int(size.height),
            bitsPerComponent: 8,
            bytesPerRow: 0,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(origin: .zero, size: size))
        return context?.makeImage()
    }
    
    /// 获取像素数据
    private static func getPixelData(from cgImage: CGImage) -> [UInt8] {
        let width = cgImage.width
        let height = cgImage.height
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let totalBytes = height * bytesPerRow
        
        var pixelData = [UInt8](repeating: 0, count: totalBytes)
        
        let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: CGColorSpaceCreateDeviceRGB(),
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        )
        
        context?.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))
        
        return pixelData
    }
    
    /// 分析像素分布以检测线性特征
    private static func analyzePixelDistribution(_ pixelData: [UInt8]) -> Bool {
        guard pixelData.count >= 400 else { return false } // 至少100x100像素
        
        var histogram = [Int](repeating: 0, count: 256)
        var totalPixels = 0
        
        // 构建亮度直方图（每4个字节一个像素：RGBA）
        for i in stride(from: 0, to: pixelData.count, by: 4) {
            let r = Int(pixelData[i])
            let g = Int(pixelData[i + 1])
            let b = Int(pixelData[i + 2])
            
            // 计算亮度（使用标准权重）
            let luminance = Int(0.299 * Double(r) + 0.587 * Double(g) + 0.114 * Double(b))
            histogram[luminance] += 1
            totalPixels += 1
        }
        
        // 线性图像的特征：
        // 1. 暗部像素较多（线性编码在暗部有更多信息）
        // 2. 中间调分布相对平均
        // 3. 高光部分较少
        
        let darkPixels = histogram[0..<85].reduce(0, +)      // 0-84 (暗部)
        let midPixels = histogram[85..<170].reduce(0, +)     // 85-169 (中间调)
        let brightPixels = histogram[170..<256].reduce(0, +) // 170-255 (高光)
        
        let darkRatio = Double(darkPixels) / Double(totalPixels)
        let midRatio = Double(midPixels) / Double(totalPixels)
        let brightRatio = Double(brightPixels) / Double(totalPixels)
        
        // 线性图像通常有更多暗部信息
        // 这是一个启发式检测，可能需要根据实际情况调整
        return darkRatio > 0.4 && midRatio > 0.3 && brightRatio < 0.3
    }
    
    /// 从图像文件路径检测色彩空间
    /// - Parameter imagePath: 图像文件路径
    /// - Returns: 是否为线性图像
    static func isLinearImageFromPath(_ imagePath: String) -> Bool {
        guard let imageSource = CGImageSourceCreateWithURL(URL(fileURLWithPath: imagePath) as CFURL, nil),
              let imageProperties = CGImageSourceCopyPropertiesAtIndex(imageSource, 0, nil) as? [String: Any] else {
            return false
        }

        // 检查色彩空间信息
        if let colorModel = imageProperties[kCGImagePropertyColorModel as String] as? String {
            let lowercasedModel = colorModel.lowercased()
            if lowercasedModel.contains("linear") ||
               lowercasedModel.contains("rec2020") ||
               lowercasedModel.contains("rec.2020") {
                return true
            }
        }

        // 检查文件扩展名（RAW文件通常是线性的）
        let fileExtension = URL(fileURLWithPath: imagePath).pathExtension.lowercased()
        let rawExtensions = ["dng", "cr2", "cr3", "nef", "arw", "orf", "rw2", "pef", "srw", "raf"]
        if rawExtensions.contains(fileExtension) {
            return true
        }

        return false
    }
}

/// 色彩空间信息结构
struct ColorSpaceInfo {
    let name: String
    let isLinear: Bool
    let model: CGColorSpaceModel
    
    var description: String {
        return "ColorSpace: \(name), Linear: \(isLinear), Model: \(model)"
    }
}
