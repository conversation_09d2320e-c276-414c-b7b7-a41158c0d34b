import Foundation
import SwiftUI

// 计时器类型枚举
enum TimerType {
    case parameter // 参数面板
    case leftButton // 3×3按钮
    case rightButton // A/M按钮
    case recording
    case zoomDial
    case topOptionPanel    // 顶部选项面板（如分辨率、宽高比等）
    case bottomOptionPanel // 底部选项面板（如定时器、防抖等）
    
    // 获取对应的超时时间
    var timeout: TimeInterval {
        switch self {
        case .zoomDial:
            return UIConstants.dialAutoHideDuration
        case .topOptionPanel, .bottomOptionPanel:
            return 6.0  // 为选项面板设置6秒的超时时间
        default:
            return UIConstants.buttonAutoHideInterval  // 默认8秒
        }
    }
    
    // 获取计时器描述名称（用于日志）
    var description: String {
        switch self {
        case .parameter: return "参数面板"
        case .leftButton: return "3×3按钮"
        case .rightButton: return "A/M按钮"
        case .recording: return "录制计时器"
        case .zoomDial: return "刻度盘"
        case .topOptionPanel: return "顶部选项面板"
        case .bottomOptionPanel: return "底部选项面板"
        }
    }
}

// 计时器回调类型
typealias TimerCallback = () -> Void
typealias TimerCallbackWithBool = (Bool) -> Void
typealias TimerCallbackWithTwoBools = (Bool, Bool) -> Void

// 通用计时器项
class TimerItem {
    var timer: Timer?
    var callback: TimerCallback?
    var boolCallback: TimerCallbackWithBool?
    var twoBoolsCallback: TimerCallbackWithTwoBools?
    
    init() {}
    
    func invalidate() {
        timer?.invalidate()
        timer = nil
    }
    
    var isActive: Bool {
        return timer != nil
    }
}

// 计时器管理器
class TimerManager {
    // 通用计时器字典
    private var timers: [TimerType: TimerItem] = [:]
    
    // 为了向后兼容保留的回调
    var onParameterStateChanged: ((Bool) -> Void)? {
        get { return timers[.parameter]?.boolCallback }
        set { 
            if timers[.parameter] == nil {
                timers[.parameter] = TimerItem()
            }
            timers[.parameter]?.boolCallback = newValue 
        }
    }
    
    var onLeftButtonStateChanged: ((Bool) -> Void)? {
        get { return timers[.leftButton]?.boolCallback }
        set { 
            if timers[.leftButton] == nil {
                timers[.leftButton] = TimerItem()
            }
            timers[.leftButton]?.boolCallback = newValue 
        }
    }
    
    var onRightButtonStateChanged: ((Bool, Bool) -> Void)? {
        get { return timers[.rightButton]?.twoBoolsCallback }
        set { 
            if timers[.rightButton] == nil {
                timers[.rightButton] = TimerItem()
            }
            timers[.rightButton]?.twoBoolsCallback = newValue 
        }
    }
    
    var onZoomDialStateChanged: ((Bool) -> Void)? {
        get { return timers[.zoomDial]?.boolCallback }
        set { 
            if timers[.zoomDial] == nil {
                timers[.zoomDial] = TimerItem()
            }
            timers[.zoomDial]?.boolCallback = newValue 
        }
    }
    
    // MARK: - 通用计时器方法
    
    /// 启动自动隐藏计时器
    func startTimer(for type: TimerType, hideAction: @escaping () -> Void) {
        cancelTimer(for: type)
        
        print("  ⏰ \(type.description)：正在启动新计时器(\(type.timeout)秒)")
        
        if timers[type] == nil {
            timers[type] = TimerItem()
        }
        
        let timerItem = timers[type]!
        timerItem.callback = hideAction
        
        timerItem.timer = Timer.scheduledTimer(withTimeInterval: type.timeout, repeats: false) { [weak self, weak timerItem] _ in
            print("⏰ \(type.description)：计时器触发，准备隐藏")
            timerItem?.callback?()
            
            // 处理特殊的回调类型
            switch type {
            case .parameter:
                timerItem?.boolCallback?(false)
            case .leftButton:
                // 直接调用回调，与rightButton保持一致
                timerItem?.boolCallback?(false)
            case .rightButton:
                timerItem?.twoBoolsCallback?(false, false)
            case .zoomDial:
                DispatchQueue.main.async {
                    // 先执行消失动画
                    AnimationManager.shared.hideDialAnimation(to: .zero) {
                        // 动画完成后再改变状态
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.25) {
                            timerItem?.boolCallback?(false)
                        }
                    }
                }
            default:
                break
            }
            
            self?.cancelTimer(for: type)
        }
    }
    
    /// 取消自动隐藏计时器
    func cancelTimer(for type: TimerType) {
        if let timerItem = timers[type], timerItem.isActive {
            print("  ⏰ \(type.description)：正在取消旧计时器")
            timerItem.invalidate()
        }
    }
    
    /// 重置自动隐藏计时器
    func resetTimer(for type: TimerType, isVisible: Bool, hideAction: @escaping () -> Void) {
        print("⏰ \(type.description)：开始重置计时器 >>>>")
        if isVisible {
            startTimer(for: type, hideAction: hideAction)
            print("⏰ \(type.description)：计时器重置完成 <<<<")
        } else {
            print("⏰ \(type.description)：组件未显示，不重置计时器 <<<<")
        }
    }
    
    // MARK: - 向后兼容的方法
    
    // 参数面板计时器
    func startParameterHideTimer() {
        startTimer(for: .parameter) { }
    }
    
    func cancelParameterHideTimer() {
        cancelTimer(for: .parameter)
    }
    
    func resetParameterTimer(isExpanded: Bool) {
        resetTimer(for: .parameter, isVisible: isExpanded) { }
    }
    
    // 左侧按钮计时器
    func startLeftButtonHideTimer() {
        startTimer(for: .leftButton) { }
    }
    
    func cancelLeftButtonHideTimer() {
        cancelTimer(for: .leftButton)
    }
    
    func resetLeftButtonTimer(isExpanded: Bool) {
        // 确保使用.leftButton类型和8秒的超时时间
        resetTimer(for: .leftButton, isVisible: isExpanded) { }
    }
    
    // 右侧按钮计时器
    func startRightButtonHideTimer() {
        startTimer(for: .rightButton) { }
    }
    
    func cancelRightButtonHideTimer() {
        cancelTimer(for: .rightButton)
    }
    
    func pauseRightButtonHideTimer() {
        print("  ⏰ A/M按钮：暂停计时器")
        cancelTimer(for: .rightButton)
        // 这里不再立即收回参数面板，只是暂停计时器
    }
    
    func resumeRightButtonHideTimer() {
        print("  ⏰ A/M按钮：重新开始计时器")
        startRightButtonHideTimer()
    }
    
    func resetRightButtonTimer(isExpanded: Bool) {
        resetTimer(for: .rightButton, isVisible: isExpanded) { }
    }
    
    // 选项面板计时器方法
    func startOptionPanelTimer(type: TimerType, hideAction: @escaping () -> Void) {
        startTimer(for: type, hideAction: hideAction)
    }
    
    func cancelOptionPanelTimer(type: TimerType) {
        cancelTimer(for: type)
    }
    
    func resetOptionPanelTimer(type: TimerType, isVisible: Bool, hideAction: @escaping () -> Void) {
        resetTimer(for: type, isVisible: isVisible, hideAction: hideAction)
    }
    
    // 变焦刻度盘计时器
    func startZoomDialHideTimer(isTouching: Bool) {
        guard !isTouching else { 
            print("⚠️ 刻度盘：仍在触摸中，不启动定时器")
            return 
        }
        
        startTimer(for: .zoomDial) { }
    }
    
    func cancelZoomDialHideTimer() {
        cancelTimer(for: .zoomDial)
    }
    
    // 通用方法
    func handleButtonExpansion(isExpanded: Bool, startTimer: () -> Void, cancelTimer: () -> Void) {
        if isExpanded {
            startTimer()
        } else {
            cancelTimer()
        }
    }
    
    // MARK: - 录制计时器方法
    
    private var recordingTimer: Timer?
    private var recordingCallback: (() -> Void)?
    
    func startRecordingTimer(callback: @escaping () -> Void) {
        // 停止现有计时器
        stopRecordingTimer()
        
        recordingCallback = callback
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.recordingCallback?()
        }
    }
    
    func pauseRecordingTimer() {
        // 仅暂停计时器，保留回调
        recordingTimer?.invalidate()
        recordingTimer = nil
    }
    
    func resumeRecordingTimer(callback: @escaping () -> Void) {
        // 保存新的回调
        recordingCallback = callback
        
        // 创建新的计时器
        recordingTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.recordingCallback?()
        }
    }
    
    func stopRecordingTimer() {
        recordingTimer?.invalidate()
        recordingTimer = nil
        recordingCallback = nil
    }
    
    // MARK: - 系统事件处理
    
    // 当应用进入前台或收到用户交互时调用此方法，取消所有自动隐藏计时器
    func cancelAllAutoHideTimers() {
        for (type, _) in timers {
            cancelTimer(for: type)
        }
    }
    
    // 获取CameraViewModel实例，用于检查UI状态
    func getViewModel() -> CameraViewModel? {
        // 不使用shared属性，因为CameraViewModel不是单例
        // 返回nil，我们将在DialController中添加ViewModel引用
        return nil
    }
} 