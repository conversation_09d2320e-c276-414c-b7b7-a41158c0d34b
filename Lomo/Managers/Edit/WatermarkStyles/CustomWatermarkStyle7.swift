import UIKit

/// 自定义水印7 - 基于胶片风格
class Custom7WatermarkStyle: WatermarkStyle {
    /// 边框宽度
    private let borderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var custom7WatermarkWrapper: UIView? // 水印包装视图
    
    // --- 水印元素相关 ---
    private weak var watermarkContentContainer: UIView? // 水印内容容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    
    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, borderColor: UIColor) {
        self.borderWidth = borderWidth // from WatermarkConstants.Custom7.fixedBorderScreenHeightFactor * screenHeight
        self.borderColor = borderColor // from WatermarkConstants.Custom7.borderColor
    }
    
    /// 查找实际的内容视图
    private func findActualContentView(in previewContainer: UIView) -> UIView? {
        // 先尝试查找照片模式下的图片视图 (imageHostView, tag = 123)
        if let imageHostView = previewContainer.viewWithTag(123) {
            print("Custom7WatermarkStyle: 发现照片模式的 imageHostView (tag 123)")
            return imageHostView
        }
            
        // 然后尝试查找相机预览视图 (previewView, tag = 101, in containerView, tag = 100)
        if let containerView = previewContainer.viewWithTag(100), let previewView = containerView.viewWithTag(101) {
            print("Custom7WatermarkStyle: 发现相机预览视图 (tag 101 in tag 100)")
            return previewView
        }
        
        // 如果预览容器本身就是相机预览视图，直接返回
        if previewContainer.tag == 101 {
            print("Custom7WatermarkStyle: 容器本身是相机预览视图 (tag 101)")
            return previewContainer
        }
        
        // 尝试查找第一个 UIImageView (可能是照片视图)
        for subview in previewContainer.subviews {
            if let imageView = subview as? UIImageView {
                print("Custom7WatermarkStyle: 使用第一个UIImageView作为内容视图")
                return imageView
            }
        }
        
        // 退化方案：使用第一个子视图 (假设它包含了主要内容)
        if let firstSubview = previewContainer.subviews.first {
            print("⚠️ Custom7WatermarkStyle: 未找到特定的内容视图，尝试使用第一个子视图")
            return firstSubview
        }
        
        return nil
    }
    
    /// 应用胶片风格水印效果
    /// - Parameter previewContainer: 预览容器视图
    func apply(to previewContainer: UIView) {
        if custom7WatermarkWrapper != nil {
            remove(from: previewContainer) // 先移除旧的
        }
        
        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ Custom7WatermarkStyle: apply - 无法找到实际内容视图")
            return
        }
        
        // 获取水印设置
        self.watermarkSettings = WatermarkDependencyContainer.shared.watermarkService.getSettings()
        
        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }
        
        // --- 创建水印效果 ---
        let contentOriginalSize = contentView.bounds.size
        
        // 创建一个包装视图，大小与内容视图相同（不添加边框）
        let wrapperView = UIView(frame: CGRect(origin: .zero, size: contentOriginalSize))
        wrapperView.clipsToBounds = true
        
        // 保持内容视图的原始尺寸和位置
        contentView.transform = .identity
        contentView.frame = CGRect(x: 0, y: 0, width: contentOriginalSize.width, height: contentOriginalSize.height)
        
        // 添加内容视图到包装视图
        wrapperView.addSubview(contentView)
        
        // 创建水印内容容器
        let watermarkContainer = UIView(frame: wrapperView.bounds)
        watermarkContainer.backgroundColor = .clear // 透明背景
        watermarkContainer.isUserInteractionEnabled = false // 禁用用户交互
        wrapperView.addSubview(watermarkContainer)
        self.watermarkContentContainer = watermarkContainer
        
        // 添加水印元素到容器
        addWatermarkElements(to: watermarkContainer)
        
        // 保存包装视图引用
        self.custom7WatermarkWrapper = wrapperView
        
        // 添加包装视图到预览容器
        previewContainer.addSubview(wrapperView)
        
        // 计算并应用缩放以适应预览容器
        let containerSize = previewContainer.bounds.size
        guard wrapperView.bounds.width > 0, wrapperView.bounds.height > 0 else {
            print("❌ Custom7WatermarkStyle: wrapperView 尺寸为0")
            wrapperView.removeFromSuperview()
            return
        }
        
        let scaleX = containerSize.width / wrapperView.bounds.width
        let scaleY = containerSize.height / wrapperView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)
        
        wrapperView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapperView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ Custom7WatermarkStyle: 已应用自定义水印7，缩放比例: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到容器
    private func addWatermarkElements(to container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示的元素
        let showLogo = !settings.selectedLogo.isEmpty
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        let showPreference = settings.preferenceOption != "OFF" // 添加偏好检查
        
        // 如果没有任何元素需要显示，直接返回
        if !showLogo && !showText && !showPreference { return }
        
        // 获取位置设置
        let position = settings.positionOption // "左", "中", "右"
        
        // 获取字体设置
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        var fontName = WatermarkConstants.Custom7.defaultTextFontName // 使用常量
        switch settings.selectedFontName {
        case "黑体":
            fontName = WatermarkConstants.Common.defaultPingFangSCSemiboldFont
        case "苹方":
            fontName = WatermarkConstants.Common.defaultPingFangSCRegularFont
        case "Times":
            fontName = WatermarkConstants.Common.defaultTimesNewRomanFont
        case "Courier":
            fontName = WatermarkConstants.Common.defaultCourierNewFont
        case "Makinas-Flat":
            fontName = "Makinas-Flat"
        case "Makinas-Square":
            fontName = "Makinas-Square"
        default:
            fontName = WatermarkConstants.Custom7.defaultTextFontName // 确保默认
        }
        
        // 容器尺寸
        let containerWidth = container.bounds.width
        let containerHeight = container.bounds.height
        
        // 水平边距 - 使用屏幕宽度的3%
        let horizontalPadding = UIScreen.main.bounds.width * 0.03
        
        // 底部边距 - 使用屏幕高度的1.5%
        let bottomPadding = UIScreen.main.bounds.height * WatermarkConstants.Custom7.elementsBottomOffsetScreenHeightFactor
        
        // 元素之间的垂直间距
        let verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom7.stackViewSpacingFactor
        
        // 创建Logo（如果需要）
        var logoImageView: UIImageView?
        var logoFrame = CGRect.zero
        
        if showLogo {
            // 使用LogoCreator创建Logo
            let logoSize = UIScreen.main.bounds.height * WatermarkConstants.Custom7.logoSizeScreenHeightFactor
            logoImageView = LogoCreator.createLogo(
                with: settings,
                fixedSize: logoSize
            )
            
            // 保存引用
            self.logoView = logoImageView
            
            // 计算Logo的高度和宽度，已经在LogoCreator.createLogo中应用了logoSizeMultiplier
            let logoHeight = logoImageView?.bounds.height ?? (logoSize * CGFloat(settings.logoSizeMultiplier))
            let logoWidth = logoImageView?.bounds.width ?? logoHeight
            
            // 初始化框架尺寸（位置稍后设置）
            logoFrame = CGRect(x: 0, y: 0, width: logoWidth, height: logoHeight)
        }
        
        // 创建文字标签（如果需要）
        var textLabel: UILabel?
        var textFrame = CGRect.zero
        
        if showText {
            // 使用TextLabelCreator创建文本标签
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom7.fontSizeScreenHeightFactor
            textLabel = TextLabelCreator.createLabel(
                with: settings,
                fixedFontSize: fontSize
            )
            
            // 设置背景透明
            textLabel?.backgroundColor = .clear
            
            // 保存引用
            self.textLabel = textLabel
            
            // 计算文本尺寸
            if let label = textLabel {
                let maxWidth = containerWidth * 0.6  // 最大宽度为容器的60%
                let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
                
                // 初始化框架尺寸（位置稍后设置）
                textFrame = CGRect(x: 0, y: 0, width: size.width, height: size.height)
            }
        }
        
        // 创建偏好标签（如果需要）
        var preferenceLabel: UILabel?
        var preferenceFrame = CGRect.zero
        
        if showPreference {
            // 使用PreferenceLabelCreator创建偏好标签
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom7.fontSizeScreenHeightFactor
            preferenceLabel = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption,
                with: settings,
                enabledElementsCount: [showLogo, showText, showPreference].filter { $0 }.count,
                customFontSizeFactors: (
                    single: WatermarkConstants.Custom7.fontSizeScreenHeightFactor,
                    two: WatermarkConstants.Custom7.fontSizeScreenHeightFactor,
                    three: WatermarkConstants.Custom7.fontSizeScreenHeightFactor
                )
            )
            
            // 设置背景透明
            preferenceLabel?.backgroundColor = .clear
            
            // 保存引用
            self.preferenceLabel = preferenceLabel
            
            // 计算偏好标签尺寸
            if let label = preferenceLabel {
                let maxWidth = containerWidth * 0.6  // 最大宽度为容器的60%
                let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
                
                // 初始化框架尺寸（位置稍后设置）
                preferenceFrame = CGRect(x: 0, y: 0, width: size.width, height: size.height)
            }
        }
        
        // 根据位置设置元素位置
        switch position {
        case "左":
            // 左侧对齐
            
            if showLogo && showText {
                // Logo和文字都显示
                
                // Logo在左上
                if let logoView = logoImageView {
                    logoFrame.origin.x = horizontalPadding
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - textFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 文字在左下
                if let label = textLabel {
                    textFrame.origin.x = horizontalPadding
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .left
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showLogo && showPreference {
                // Logo和偏好都显示
                
                // Logo在左上
                if let logoView = logoImageView {
                    logoFrame.origin.x = horizontalPadding
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - preferenceFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 偏好在左下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = horizontalPadding
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .left
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showText && showPreference {
                // 文字和偏好都显示（文字在上，偏好在下）
                
                // 文字在左上
                if let label = textLabel {
                    textFrame.origin.x = horizontalPadding
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height - verticalSpacing - preferenceFrame.height
                    label.textAlignment = .left
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
                // 偏好在左下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = horizontalPadding
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .left
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showLogo {
                // 只有Logo
                if let logoView = logoImageView {
                    logoFrame.origin.x = horizontalPadding
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
            } else if showText {
                // 只有文字
                if let label = textLabel {
                    textFrame.origin.x = horizontalPadding
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .left
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showPreference {
                // 只有偏好
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = horizontalPadding
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .left
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
            }
            
        case "右":
            // 右侧对齐
            
            if showLogo && showText {
                // Logo和文字都显示
                
                // Logo在右上
                if let logoView = logoImageView {
                    logoFrame.origin.x = containerWidth - horizontalPadding - logoFrame.width
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - textFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 文字在右下
                if let label = textLabel {
                    textFrame.origin.x = containerWidth - horizontalPadding - textFrame.width
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .right
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showLogo && showPreference {
                // Logo和偏好都显示
                
                // Logo在右上
                if let logoView = logoImageView {
                    logoFrame.origin.x = containerWidth - horizontalPadding - logoFrame.width
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - preferenceFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 偏好在右下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = containerWidth - horizontalPadding - preferenceFrame.width
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .right
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showText && showPreference {
                // 文字和偏好都显示（文字在上，偏好在下）
                
                // 文字在右上
                if let label = textLabel {
                    textFrame.origin.x = containerWidth - horizontalPadding - textFrame.width
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height - verticalSpacing - preferenceFrame.height
                    label.textAlignment = .right
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
                // 偏好在右下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = containerWidth - horizontalPadding - preferenceFrame.width
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .right
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showLogo {
                // 只有Logo
                if let logoView = logoImageView {
                    logoFrame.origin.x = containerWidth - horizontalPadding - logoFrame.width
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
            } else if showText {
                // 只有文字
                if let label = textLabel {
                    textFrame.origin.x = containerWidth - horizontalPadding - textFrame.width
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .right
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showPreference {
                // 只有偏好
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = containerWidth - horizontalPadding - preferenceFrame.width
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .right
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
            }
            
        default: // "中"或其他值
            // 居中对齐
            
            if showLogo && showText {
                // Logo和文字都显示
                
                // Logo在中上
                if let logoView = logoImageView {
                    logoFrame.origin.x = (containerWidth - logoFrame.width) / 2
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - textFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 文字在中下
                if let label = textLabel {
                    textFrame.origin.x = (containerWidth - textFrame.width) / 2
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .center
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showLogo && showPreference {
                // Logo和偏好都显示
                
                // Logo在中上
                if let logoView = logoImageView {
                    logoFrame.origin.x = (containerWidth - logoFrame.width) / 2
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height - verticalSpacing - preferenceFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
                // 偏好在中下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = (containerWidth - preferenceFrame.width) / 2
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .center
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showText && showPreference {
                // 文字和偏好都显示（文字在上，偏好在下）
                
                // 文字在中上
                if let label = textLabel {
                    textFrame.origin.x = (containerWidth - textFrame.width) / 2
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height - verticalSpacing - preferenceFrame.height
                    label.textAlignment = .center
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
                // 偏好在中下
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = (containerWidth - preferenceFrame.width) / 2
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .center
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
                
            } else if showLogo {
                // 只有Logo
                if let logoView = logoImageView {
                    logoFrame.origin.x = (containerWidth - logoFrame.width) / 2
                    logoFrame.origin.y = containerHeight - bottomPadding - logoFrame.height
                    logoView.frame = logoFrame
                    container.addSubview(logoView)
                }
                
            } else if showText {
                // 只有文字
                if let label = textLabel {
                    textFrame.origin.x = (containerWidth - textFrame.width) / 2
                    textFrame.origin.y = containerHeight - bottomPadding - textFrame.height
                    label.textAlignment = .center
                    label.frame = textFrame
                    container.addSubview(label)
                }
                
            } else if showPreference {
                // 只有偏好
                if let label = preferenceLabel {
                    preferenceFrame.origin.x = (containerWidth - preferenceFrame.width) / 2
                    preferenceFrame.origin.y = containerHeight - bottomPadding - preferenceFrame.height
                    label.textAlignment = .center
                    label.frame = preferenceFrame
                    container.addSubview(label)
                }
            }
        }
        
        print("✅ Custom7WatermarkStyle: 已添加水印元素，位置: \(position)")
    }
    
    /// 移除自定义水印7效果
    /// - Parameter previewContainer: 预览容器视图
    func remove(from previewContainer: UIView) {
        guard let wrapperView = self.custom7WatermarkWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            // 清理水印视图
            self.custom7WatermarkWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        // 1. 从包装视图中移除内容视图
        contentView.removeFromSuperview()
        
        // 2. 重置内容视图状态
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        // 3. 插回原始父视图
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        // 4. 移除包装视图
        wrapperView.removeFromSuperview()
        
        // 5. 清理存储的状态
        cleanupStoredState()
        
        print("✅ Custom7WatermarkStyle: 已移除自定义水印7。")
    }

    /// 辅助方法：清理存储的状态
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.custom7WatermarkWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.watermarkContentContainer = nil
        self.watermarkSettings = nil
    }
} 