import UIKit

// MARK: - 自定义水印11
/// 自定义水印样式11 - 基于自定义水印4复制的水印
class CustomWatermarkStyle11: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom11FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义11框架视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    private var separatorLabel: UILabel? // "|"分隔符标签

    // 添加新属性存储分隔符视图
    private weak var separatorView: SeparatorLineView?

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat, 
         bottomBorderWidth: CGFloat, // From WatermarkConstants.Custom11.bottomBorderScreenHeightFactor * screenHeight
         sideBorderWidth: CGFloat, 
         borderColor: UIColor = WatermarkConstants.Colors.borderWhite) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle11: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印11到预览视图
    func apply(to previewContainer: UIView) {
        // 安全检查：确保容器有效
        guard previewContainer.bounds.width > 0, previewContainer.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle11: apply - 无效的容器尺寸，跳过应用水印")
            return
        }
        
        if scaledCustom11FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle11: apply - 无法找到实际内容视图。")
            return
        }
        
        // 确保内容视图尺寸有效
        guard contentView.bounds.width > 0, contentView.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle11: apply - 内容视图尺寸无效，跳过应用水印")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkDependencyContainer.shared.watermarkService.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom11Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let custom11Wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        custom11Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom11Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom11Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle11: 无法获取视图截图，使用默认背景")
                custom11Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            custom11Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom11Wrapper.addSubview(contentView)

        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom11Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle11")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了白色背景
        custom11Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom11FrameWrapper = custom11Wrapper
        previewContainer.addSubview(custom11Wrapper)

        // --- 将 wrapper (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom11Wrapper.bounds.width > 0, custom11Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle11: wrapper 尺寸为0。")
            custom11Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom11Wrapper.bounds.width
        let scaleY = containerSize.height / custom11Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom11Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom11Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle11: 已应用自定义水印11。最终缩放: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 安全检查：确保容器尺寸有效
        guard container.bounds.width > 0, container.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle11: 无效的容器尺寸，跳过添加水印元素")
            return
        }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        // 检查署名是否启用
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 署名启用且不为空
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showPreference, showSignature].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        // 创建Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建文字（如果需要）
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建偏好（如果需要）
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建署名（如果需要）
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要分隔符
        if showLogo && (showText || showPreference) && showSignature {
            createSeparator(container, settings: settings)
        }
        
        // 直接安排布局，不使用异步处理
        arrangeElementsLayout(container, enabledCount: enabledElementsCount, 
                              showLogo: showLogo, 
                              showText: showText,
                              showPreference: showPreference,
                              showSignature: showSignature)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印11使用动态大小 - 根据当前底部边框宽度等比例计算Logo大小
        // 公式：logoSize = bottomBorderWidth * logoSizeBaseBorderRatio
        let logoSize = self.bottomBorderWidth * WatermarkConstants.Custom11.logoSizeBaseBorderRatio
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印11 Logo动态大小: bottomBorderWidth=\(self.bottomBorderWidth), logoSize=\(logoSize)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印11使用线性增长 - 文字只有25%增长，而Logo有50%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom11.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom11.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom11.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom11.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom11.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom11.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印11 文字线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印11使用线性增长 - 偏好标签与文字使用相同的增长模式（25%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom11.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom11.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom11.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom11.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom11.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom11.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
        
        print("✅ 水印11 偏好线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建署名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印11使用线性增长 - 署名大小与文字相同
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom11.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom11.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom11.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom11.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom11.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        var fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        // 应用署名大小乘数
        fontSize *= CGFloat(settings.signatureFontSizeMultiplier)
        
        // 使用TextUtils创建署名标签
        let label = TextUtils.createSignatureLabel(
            with: settings,
            fontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom11.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: CGFloat.greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印11 署名线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount), 大小乘数=\(settings.signatureFontSizeMultiplier)")
    }
    
    /// 创建分隔符标签
    private func createSeparator(_ container: UIView, settings: WatermarkSettings) {
        // 获取分隔符颜色 - 使用统一的工具方法
        let separatorColor = WatermarkStyleUtils.getSpecialElementColor(with: settings, borderColor: self.borderColor)
        
        // 默认分隔符线宽
        var separatorLineWidth: CGFloat = 1.5
        
        // 获取字体名称和粗细
        let fontName = settings.selectedFontName
        let fontWeight = settings.selectedFontWeight
        
        // 尝试根据右侧文本的字体获取对应的分隔符线宽
        if let textLabel = self.textLabel, let font = textLabel.font {
            // 使用新的FontLineThicknessUtils类来获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThicknessForFont(font)
            print("✅ 水印11 分隔符: 使用字体[\(font.fontName)]的自定义映射获取线宽: \(separatorLineWidth)")
        } else if let preferenceLabel = self.preferenceLabel, let font = preferenceLabel.font {
            // 使用新的FontLineThicknessUtils类来获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThicknessForFont(font)
            print("✅ 水印11 分隔符: 使用偏好字体[\(font.fontName)]的自定义映射获取线宽: \(separatorLineWidth)")
        } else if !fontName.isEmpty && !fontWeight.isEmpty {
            // 如果没有可用的标签，但有字体名称和粗细信息，使用这些信息获取线宽
            separatorLineWidth = FontLineThicknessUtils.getLineThickness(fontName: fontName, fontWeight: fontWeight)
            print("✅ 水印11 分隔符: 使用设置中的字体[\(fontName)-\(fontWeight)]获取线宽: \(separatorLineWidth)")
            } else {
            // 默认使用常规粗细
            separatorLineWidth = 1.5
            print("✅ 水印11 分隔符: 无法获取字体信息，使用默认线宽1.5")
        }
        
        // 创建自定义分隔符视图，传入颜色和线宽
        let separatorView = SeparatorLineView(color: separatorColor, width: separatorLineWidth)
        
        // 设置初始大小 - 宽度为线宽，高度暂时先设置较小
        separatorView.frame = CGRect(x: 0, y: 0, width: separatorLineWidth, height: 20)
        
        container.addSubview(separatorView)
        self.separatorLabel = nil // 清除旧的标签引用
        
        // 使用新属性保存分隔符视图引用
        self.separatorView = separatorView
        
        print("✅ 水印11 分隔符: 使用自定义线条视图，使用统一颜色逻辑，线宽为\(separatorLineWidth)")
    }
    
    /// 自定义分隔线视图类
    private class SeparatorLineView: UIView {
        private let lineColor: UIColor
        private var _lineWidth: CGFloat = 1.5
        
        var lineWidth: CGFloat {
            return _lineWidth
        }
        
        init(color: UIColor, width: CGFloat = 1.5) {
            self.lineColor = color
            self._lineWidth = width
            super.init(frame: .zero)
            self.backgroundColor = .clear
        }
        
        func updateLineWidth(_ width: CGFloat) {
            self._lineWidth = width
            self.setNeedsDisplay()
        }
        
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        override func draw(_ rect: CGRect) {
            guard let context = UIGraphicsGetCurrentContext() else { return }
            
            // 设置线条宽度和颜色
            context.setLineWidth(min(rect.width, _lineWidth))
            context.setStrokeColor(lineColor.cgColor)
            
            // 绘制垂直线条 - 居中
            let centerX = rect.width / 2.0
            context.move(to: CGPoint(x: centerX, y: 0))
            context.addLine(to: CGPoint(x: centerX, y: rect.height))
            
            // 应用绘制
            context.strokePath()
        }
    }
    
    /// 根据元素数量和类型安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int, 
                                      showLogo: Bool, showText: Bool, 
                                      showPreference: Bool, showSignature: Bool) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 使用基于屏幕宽度的内边距常量
        let leftPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom11.logoLeftPaddingScreenWidthFactor
        let rightPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom11.textRightPaddingScreenWidthFactor
        
        switch enabledCount {
        case 1:
            // 只有一个元素：居中显示
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let textLabel = self.textLabel {
                // 文字垂直居中
                textLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let preferenceLabel = self.preferenceLabel {
                // 偏好垂直居中
                preferenceLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let signatureLabel = self.signatureLabel {
                // 署名垂直居中
                signatureLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
            
        case 2:
            if showLogo && showSignature {
                // Logo + 署名: Logo在左，署名在右
                placeElementLeft(self.logoView, padding: leftPadding, containerHeight: containerHeight)
                placeElementRight(self.signatureLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                print("✅ 水印11两元素布局: LOGO在左, 署名在右")
                
            } else if showLogo && (showText || showPreference) {
                // Logo + 文字/偏好: Logo在左，文字/偏好在右
                placeElementLeft(self.logoView, padding: leftPadding, containerHeight: containerHeight)
                if let textLabel = self.textLabel {
                    placeElementRight(textLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                    print("✅ 水印11两元素布局: LOGO在左, 文字在右")
                } else if let preferenceLabel = self.preferenceLabel {
                    placeElementRight(preferenceLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                    print("✅ 水印11两元素布局: LOGO在左, 偏好在右")
                }
                
            } else if showSignature && (showText || showPreference) {
                // 署名 + 文字/偏好: 署名在左，文字/偏好在右
                placeElementLeft(self.signatureLabel, padding: leftPadding, containerHeight: containerHeight)
                if let textLabel = self.textLabel {
                    placeElementRight(textLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                    print("✅ 水印11两元素布局: 署名在左, 文字在右")
                } else if let preferenceLabel = self.preferenceLabel {
                    placeElementRight(preferenceLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                    print("✅ 水印11两元素布局: 署名在左, 偏好在右")
                }
            }
            
        case 3:
            if showSignature && showLogo && (showText || showPreference) {
                // 署名 + Logo + 文字/偏好: 署名在左，Logo和文字/偏好在右，中间用 | 分隔
                
                // 1. 署名放在左侧
                placeElementLeft(self.signatureLabel, padding: leftPadding, containerHeight: containerHeight)
                
                // 2. 计算右侧区域总宽度
                let rightAreaWidth = containerWidth - leftPadding - rightPadding - (self.signatureLabel?.bounds.width ?? 0)
                let rightSideX = containerWidth - rightAreaWidth/2 - rightPadding
                
                // 3. 放置Logo、分隔符和文字/偏好
                if let logoView = self.logoView, let separator = self.separatorView {
                    // 计算右侧元素总宽度
                    let totalRightWidth = logoView.bounds.width + separator.bounds.width + 
                                         (showText ? (self.textLabel?.bounds.width ?? 0) : 
                                         (self.preferenceLabel?.bounds.width ?? 0))
                    
                    // 设置固定间距为1.5%屏幕宽度
                    let spacing = UIScreen.main.bounds.width * 0.015 // 固定间距
                    
                    // 右侧组合物整体靠右，与两元素布局保持一致
                    // 从右向左依次放置：文字/偏好、分隔符、Logo
                    
                    // 文字/偏好放在最右边
                    if let textLabel = self.textLabel {
                        placeElementRight(textLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                        
                        // 获取文字高度
                        let textHeight = textLabel.bounds.height
                        
                        // 估算文本是否为多行
                        let fontSize = textLabel.font.pointSize
                        let estimatedLineHeight = fontSize * 1.3 // 估算的单行高度（考虑行间距）
                        let estimatedLines = textHeight / estimatedLineHeight
                        
                        // 根据行数计算分隔符高度
                        let separatorHeight: CGFloat
                        if estimatedLines > 1.5 { // 若为双行文本
                            separatorHeight = textHeight * 0.8 // 设为文本高度的80%
                            print("✅ 水印11: 检测到多行文本，分隔符高度设为80%")
                        } else {
                            separatorHeight = textHeight // 单行文本保持一致
                        }
                        
                        // 获取分隔符线宽，用于动态调整间距
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽 (线宽越大，间距稍微加大)
                        let dynamicSpacing = UIScreen.main.bounds.width * (0.015 + separatorLineWidth * 0.001)
                        
                        // 分隔符放在文字左边
                        let separatorX = textLabel.center.x - textLabel.bounds.width/2 - dynamicSpacing - separator.bounds.width/2
                        
                        // 调整分隔符尺寸 - 宽度保持不变，高度根据文本行数调整
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: separatorHeight
                        )
                        
                        // 分隔符垂直居中
                        separator.center = CGPoint(x: separatorX, y: containerHeight/2)
                        
                        // Logo放在分隔符左边，同样使用动态间距
                        let logoX = separatorX - separator.bounds.width/2 - dynamicSpacing - logoView.bounds.width/2
                        logoView.center = CGPoint(x: logoX, y: containerHeight/2)
                        
                        print("✅ 水印11三元素布局: 署名在左, Logo和文字在右(有分隔符), 文本高度=\(textHeight), 分隔符高度=\(separatorHeight), 分隔符线宽=\(separatorLineWidth), 间距=\(dynamicSpacing)")
                    } else if let preferenceLabel = self.preferenceLabel {
                        placeElementRight(preferenceLabel, padding: rightPadding, containerHeight: containerHeight, containerWidth: containerWidth)
                        
                        // 获取偏好高度
                        let preferenceHeight = preferenceLabel.bounds.height
                        
                        // 估算文本是否为多行
                        let fontSize = preferenceLabel.font.pointSize
                        let estimatedLineHeight = fontSize * 1.3 // 估算的单行高度（考虑行间距）
                        let estimatedLines = preferenceHeight / estimatedLineHeight
                        
                        // 根据行数计算分隔符高度
                        let separatorHeight: CGFloat
                        if estimatedLines > 1.5 { // 若为双行文本
                            separatorHeight = preferenceHeight * 0.8 // 设为文本高度的80%
                            print("✅ 水印11: 检测到多行文本，分隔符高度设为80%")
                        } else {
                            separatorHeight = preferenceHeight // 单行文本保持一致
                        }
                        
                        // 获取分隔符线宽，用于动态调整间距
                        let separatorLineWidth = separator.lineWidth
                        
                        // 设置动态间距，考虑分隔符线宽 (线宽越大，间距稍微加大)
                        let dynamicSpacing = UIScreen.main.bounds.width * (0.015 + separatorLineWidth * 0.001)
                        
                        // 分隔符放在偏好左边
                        let separatorX = preferenceLabel.center.x - preferenceLabel.bounds.width/2 - dynamicSpacing - separator.bounds.width/2
                        
                        // 调整分隔符尺寸 - 宽度保持不变，高度根据文本行数调整
                        separator.frame = CGRect(
                            x: separator.frame.origin.x,
                            y: separator.frame.origin.y,
                            width: separator.bounds.width,
                            height: separatorHeight
                        )
                        
                        // 分隔符垂直居中
                        separator.center = CGPoint(x: separatorX, y: containerHeight/2)
                        
                        // Logo放在分隔符左边，同样使用动态间距
                        let logoX = separatorX - separator.bounds.width/2 - dynamicSpacing - logoView.bounds.width/2
                        logoView.center = CGPoint(x: logoX, y: containerHeight/2)
                        
                        print("✅ 水印11三元素布局: 署名在左, Logo和偏好在右(有分隔符), 偏好高度=\(preferenceHeight), 分隔符高度=\(separatorHeight), 分隔符线宽=\(separatorLineWidth), 间距=\(dynamicSpacing)")
                    }
                }
            }
            
        default:
            print("⚠️ 水印11: 不支持超过3个元素的布局，元素可能不可见")
            break
        }
    }
    
    /// 辅助方法：将元素放置在左侧
    private func placeElementLeft(_ element: UIView?, padding: CGFloat, containerHeight: CGFloat) {
        guard let element = element else { return }
        let x = padding + element.bounds.width / 2
        element.center = CGPoint(x: x, y: containerHeight / 2)
    }
    
    /// 辅助方法：将元素放置在右侧
    private func placeElementRight(_ element: UIView?, padding: CGFloat, containerHeight: CGFloat, containerWidth: CGFloat) {
        guard let element = element else { return }
        let x = containerWidth - padding - element.bounds.width / 2
        element.center = CGPoint(x: x, y: containerHeight / 2)
    }
    
    /// 移除自定义水印11效果
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.separatorView?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let wrapperView = self.scaledCustom11FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom11FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapperView.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle11: 已移除自定义水印11。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 辅助方法：清理存储的状态变量
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledCustom11FrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.signatureLabel = nil
        self.separatorView = nil
        self.bottomItemsContainer = nil
        self.watermarkSettings = nil
    }
} 