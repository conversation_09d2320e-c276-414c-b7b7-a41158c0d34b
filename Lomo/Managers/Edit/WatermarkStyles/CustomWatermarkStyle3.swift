import UIKit

// MARK: - 自定义水印3
/// 自定义水印样式3 - 电影胶片风格水印
class CustomWatermarkStyle3: WatermarkStyle {
    /// 边框宽度
    private let borderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var filmWatermarkWrapper: UIView? // 水印包装视图
    
    // --- 水印元素相关 ---
    private weak var watermarkContentContainer: UIView? // 水印内容容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    
    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, borderColor: UIColor) { // Factory provides these, possibly from constants already
        self.borderWidth = borderWidth // from WatermarkConstants.Film.fixedBorderScreenHeightFactor * screenHeight
        self.borderColor = borderColor // from WatermarkConstants.Film.borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        // 优先查找导入图片的imageHostView (tag 123)
        if let imageHost = container.viewWithTag(123) { 
            print("CustomWatermarkStyle3: 发现照片模式的 imageHostView (tag 123)")
            return imageHost
        }
        
        // 查找相机预览视图
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                print("CustomWatermarkStyle3: 发现相机预览视图 (tag 101 in tag 100)")
                return cameraPreview
            }
        }
        
        if container.tag == 101 {
            print("CustomWatermarkStyle3: 容器本身是相机预览视图 (tag 101)")
            return container
        }
        
        // 降级：查找第一个UIImageView
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            print("CustomWatermarkStyle3: 使用第一个UIImageView作为内容视图")
            return imageView
        }
        
        print("⚠️ CustomWatermarkStyle3: 未找到特定的内容视图，尝试使用第一个子视图")
        return container.subviews.first
    }
    
    /// 应用胶片风格水印到预览视图
    /// - Parameter previewContainer: 预览容器视图
    func apply(to previewContainer: UIView) {
        if filmWatermarkWrapper != nil {
            remove(from: previewContainer) // 先移除旧的
        }
        
        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle3: apply - 无法找到实际内容视图")
            return
        }
        
        // 获取水印设置
        self.watermarkSettings = WatermarkDependencyContainer.shared.watermarkService.getSettings()
        
        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }
        
        // --- 创建水印效果 ---
        let contentOriginalSize = contentView.bounds.size
        
        // 创建一个包装视图，大小与内容视图相同（不添加边框）
        let wrapperView = UIView(frame: CGRect(origin: .zero, size: contentOriginalSize))
        wrapperView.clipsToBounds = true
        
        // 保持内容视图的原始尺寸和位置
        contentView.transform = .identity
        contentView.frame = CGRect(x: 0, y: 0, width: contentOriginalSize.width, height: contentOriginalSize.height)
        
        // 添加内容视图到包装视图
        wrapperView.addSubview(contentView)
        
        // 创建水印内容容器
        let watermarkContainer = UIView(frame: wrapperView.bounds)
        watermarkContainer.backgroundColor = .clear // 透明背景
        watermarkContainer.isUserInteractionEnabled = false // 禁用用户交互
        wrapperView.addSubview(watermarkContainer)
        self.watermarkContentContainer = watermarkContainer
        
        // 添加水印元素到容器
        addWatermarkElements(to: watermarkContainer)
        
        // 保存包装视图引用
        self.filmWatermarkWrapper = wrapperView
        
        // 添加包装视图到预览容器
        previewContainer.addSubview(wrapperView)
        
        // 计算并应用缩放以适应预览容器
        let containerSize = previewContainer.bounds.size
        guard wrapperView.bounds.width > 0, wrapperView.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle3: wrapperView 尺寸为0")
            wrapperView.removeFromSuperview()
            return
        }
        
        let scaleX = containerSize.width / wrapperView.bounds.width
        let scaleY = containerSize.height / wrapperView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)
        
        wrapperView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapperView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle3: 已应用胶片风格水印，缩放比例: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到容器
    private func addWatermarkElements(to container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示的元素
        let showLogo = !settings.selectedLogo.isEmpty
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 如果没有任何元素需要显示，直接返回
        if !showLogo && !showText && !showPreference { return }
        
        // 获取位置设置
        let position = settings.positionOption // "中" 或 "下"
        let isCenter = position == "中"
        
        // 获取字体设置
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        var fontName = WatermarkConstants.Film.defaultTextFontName // 使用常量
        switch settings.selectedFontName {
        case "黑体":
            fontName = WatermarkConstants.Common.defaultPingFangSCSemiboldFont
        case "苹方":
            fontName = WatermarkConstants.Common.defaultPingFangSCRegularFont
        case "Times":
            fontName = WatermarkConstants.Common.defaultTimesNewRomanFont
        case "Courier":
            fontName = WatermarkConstants.Common.defaultCourierNewFont
        default:
            fontName = WatermarkConstants.Film.defaultTextFontName // 确保默认
        }
        
        // 计算位置
        let containerWidth = container.bounds.width
        let containerHeight = container.bounds.height
        
        // 创建内容视图
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.alignment = .center
        
        // 根据显示的元素设置不同的间距
        contentStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Film.stackViewSpacingFactor // 使用常量
        
        contentStackView.backgroundColor = .clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加Logo（如果需要）
        if showLogo {
            // 使用LogoCreator创建Logo，传入固定大小
            let logoSize = UIScreen.main.bounds.height * WatermarkConstants.Film.logoSizeScreenHeightFactor
            let logoImageView = LogoCreator.createLogo(
                with: settings,
                fixedSize: logoSize
            )
            
            // 设置约束
            logoImageView.translatesAutoresizingMaskIntoConstraints = false
            logoImageView.heightAnchor.constraint(equalToConstant: logoSize).isActive = true
            // 移除了宽度约束: logoImageView.widthAnchor.constraint(equalToConstant: logoSize).isActive = true
            
            contentStackView.addArrangedSubview(logoImageView)
            self.logoView = logoImageView
        }
        
        // 添加文字（如果需要）
        if showText {
            // 使用TextLabelCreator创建文本标签
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Film.fontSizeScreenHeightFactor
            let label = TextLabelCreator.createLabel(
                with: settings,
                fixedFontSize: fontSize
            )
            
            label.backgroundColor = .clear
            
            contentStackView.addArrangedSubview(label)
            self.textLabel = label
        }
        
        // 添加偏好（如果需要）
        if showPreference {
            // 使用PreferenceLabelCreator创建偏好标签
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Film.fontSizeScreenHeightFactor
            let preferenceLabel = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption,
                with: settings,
                enabledElementsCount: [showLogo, showText, showPreference].filter { $0 }.count,
                customFontSizeFactors: (
                    single: WatermarkConstants.Film.fontSizeScreenHeightFactor,
                    two: WatermarkConstants.Film.fontSizeScreenHeightFactor,
                    three: WatermarkConstants.Film.fontSizeScreenHeightFactor
                )
            )
            
            preferenceLabel.backgroundColor = .clear
            
            contentStackView.addArrangedSubview(preferenceLabel)
            self.preferenceLabel = preferenceLabel
        }
        
        // 设置位置约束
        if isCenter {
            // 居中显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                contentStackView.centerYAnchor.constraint(equalTo: container.centerYAnchor)
            ])
        } else {
            // 底部显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                // 将偏移量改为屏幕高度的1.5%
                contentStackView.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -UIScreen.main.bounds.height * WatermarkConstants.Film.elementsBottomOffsetScreenHeightFactor) // 使用常量
            ])
        }
    }
    
    /// 移除胶片风格水印效果
    /// - Parameter previewContainer: 预览容器视图
    func remove(from previewContainer: UIView) {
        guard let wrapperView = self.filmWatermarkWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.filmWatermarkWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }
        
        // 1. 从包装视图中移除内容视图
        contentView.removeFromSuperview()
        
        // 2. 恢复内容视图的原始状态
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        // 3. 将内容视图添加回原始父视图
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        // 4. 移除包装视图
        wrapperView.removeFromSuperview()
        
        // 5. 清理存储的状态
        cleanupStoredState()
        print("✅ CustomWatermarkStyle3: 已移除胶片风格水印")
    }
    
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.filmWatermarkWrapper = nil
        self.watermarkContentContainer = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.preferenceLabel = nil
        self.watermarkSettings = nil
    }
} 