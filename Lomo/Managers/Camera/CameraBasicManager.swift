import Foundation
import SwiftData
import SwiftUI

/// 相机基础设置管理器（拍摄模式、前后摄像头）
class CameraBasicManager {
    // 单例
    static let shared = CameraBasicManager()
    
    // 模型容器和上下文
    private var modelContainer: ModelContainer?
    private var modelContext: ModelContext?
    
    // 私有初始化方法
    private init() {
        setupModelContainer()
    }
    
    // 设置SwiftData模型容器
    private func setupModelContainer() {
        // 使用共享容器替代单独创建容器
        self.modelContainer = SharedService.shared.container
        if let container = self.modelContainer {
            self.modelContext = ModelContext(container)
            print(" CameraBasicManager: 已使用共享 ModelContainer 和 ModelContext。")
        } else {
            print(" CameraBasicManager: 获取共享 ModelContainer 失败！")
        }
    }
    
    // MARK: - 公共方法
    
    /// 获取设置
    func getSettings() -> CameraBasicSettings {
        guard let context = modelContext else {
            return CameraBasicSettings()
        }
        
        do {
            // 尝试获取现有设置
            let descriptor = FetchDescriptor<CameraBasicSettings>(predicate: #Predicate { $0.id == "camera_basic_settings" })
            let existingSettings = try context.fetch(descriptor)
            
            // 如果存在设置，返回第一个
            if let settings = existingSettings.first {
                return settings
            }
            
            // 如果不存在，创建新的设置并保存
            let newSettings = CameraBasicSettings()
            context.insert(newSettings)
            try context.save()
            return newSettings
        } catch {
            print("获取相机基础设置失败: \(error.localizedDescription)")
            // 发生错误时返回默认设置
            return CameraBasicSettings()
        }
    }
    
    /// 保存设置
    func saveSettings(_ settings: CameraBasicSettings) {
        guard let context = modelContext else {
            print("保存失败：模型上下文不可用")
            return
        }
        
        do {
            // 更新时间戳
            settings.updateTimestamp()
            // 保存上下文中的更改
            try context.save()
        } catch {
            print("保存相机基础设置失败: \(error.localizedDescription)")
        }
    }
    
    /// 更新特定设置
    func updateSetting<T>(_ keyPath: WritableKeyPath<CameraBasicSettings, T>, value: T) {
        var settings = getSettings()
        settings[keyPath: keyPath] = value
        saveSettings(settings)
    }
    
    /// 重置所有设置为默认值
    func resetToDefaults() {
        guard let context = modelContext else {
            return
        }
        
        do {
            // 获取所有现有设置
            let descriptor = FetchDescriptor<CameraBasicSettings>()
            let existingSettings = try context.fetch(descriptor)
            
            // 删除所有现有设置
            for settings in existingSettings {
                context.delete(settings)
            }
            
            // 创建新的默认设置
            let newSettings = CameraBasicSettings()
            context.insert(newSettings)
            
            // 保存更改
            try context.save()
        } catch {
            print("重置相机基础设置失败: \(error.localizedDescription)")
        }
    }
} 