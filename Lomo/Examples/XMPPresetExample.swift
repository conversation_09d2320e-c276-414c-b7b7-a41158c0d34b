import Foundation

/// XMP预设更新示例
/// 展示如何从XMP文件创建和更新滤镜预设
class XMPPresetExample {
    
    /// 解析用户提供的XMP内容
    static func parseUserXMP() {
        // 用户提供的XMP内容（第一部分）
        let userXMPContent = """
        <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 5.6-c140 79.160451, 2017/05/06-01:08:21        ">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description rdf:about=""
            xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/"
           crs:PresetType="Look"
           crs:Cluster=""
           crs:UUID="9A4C6AA010864A6187C34142306FB45D"
           crs:SupportsAmount="True"
           crs:SupportsColor="True"
           crs:SupportsMonochrome="False"
           crs:SupportsHighDynamicRange="True"
           crs:SupportsNormalDynamicRange="True"
           crs:SupportsSceneReferred="True"
           crs:SupportsOutputReferred="False"
           crs:CameraModelRestriction=""
           crs:Copyright=""
           crs:ContactInfo=""
           crs:Version="13.2"
           crs:ProcessVersion="11.0"
           crs:ToneMapStrength="2"
           crs:ConvertToGrayscale="False"
           crs:CameraProfile="Adobe Standard"
           crs:LookTable="E1095149FDB39D7A057BAB208837E2E1"
           crs:Table_E1095149FDB39D7A057BAB208837E2E1="vqf00hWjaEPdiX}e4wRI=Nf.W1'FxI1QTfjwhR.vQ*/Zv$fY?y3OhTf'ECkj2M%TcC3ph?hR`BT4DcHZ+tl^V:.@Q)p?`P@fq}zO7KH$yWLXKEe^9DNGF1Y*D6#mFPU?c?"
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """

        // 解析XMP参数
        let xmpParams = XMPParser.parseXMP(from: userXMPContent)

        // 打印解析结果
        print("📄 解析用户XMP文件...")
        XMPParser.printXMPParameters(xmpParams)

        // 分析XMP特征
        print("\n🔍 XMP文件分析:")
        print("  预设类型: Look (外观预设)")
        print("  版本: 13.2")
        print("  处理版本: 11.0")
        print("  色调映射强度: 2")
        print("  相机配置文件: Adobe Standard")
        print("  包含LUT表: 是")
        print("  支持彩色: 是")
        print("  支持HDR: 是")

        print("\n⚠️  注意: 此XMP包含LUT表数据，需要完整内容才能提取所有参数")
        print("请继续提供XMP文件的剩余部分...")
    }

    /// 示例：从XMP文件更新宝丽来预设
    static func updatePolaroidPresetFromXMP() {
        // 示例XMP内容（您可以替换为实际的XMP文件内容）
        let sampleXMPContent = """
        <?xml version="1.0" encoding="UTF-8"?>
        <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 7.0-c000 1.000000, 0000/00/00-00:00:00">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description rdf:about=""
            xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/">
           <crs:Exposure>+0.50</crs:Exposure>
           <crs:Contrast>+25</crs:Contrast>
           <crs:Highlights>-30</crs:Highlights>
           <crs:Shadows>+40</crs:Shadows>
           <crs:Whites>+10</crs:Whites>
           <crs:Blacks>-15</crs:Blacks>
           <crs:Brightness>+8</crs:Brightness>
           <crs:Temperature>+300</crs:Temperature>
           <crs:Tint>+12</crs:Tint>
           <crs:Saturation>-15</crs:Saturation>
           <crs:Vibrance>+20</crs:Vibrance>
           <crs:Clarity>-25</crs:Clarity>
           <crs:Sharpness>+15</crs:Sharpness>
           <crs:VignetteAmount>-35</crs:VignetteAmount>
           <crs:VignetteRadius>+80</crs:VignetteRadius>
           <crs:SplitToningShadowHue>220</crs:SplitToningShadowHue>
           <crs:SplitToningShadowSaturation>15</crs:SplitToningShadowSaturation>
           <crs:SplitToningHighlightHue>45</crs:SplitToningHighlightHue>
           <crs:SplitToningHighlightSaturation>25</crs:SplitToningHighlightSaturation>
           <crs:ParametricShadows>+10</crs:ParametricShadows>
           <crs:ParametricDarks>+5</crs:ParametricDarks>
           <crs:ParametricLights>-8</crs:ParametricLights>
           <crs:ParametricHighlights>-15</crs:ParametricHighlights>
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """
        
        // 使用XMP内容更新预设
        let success = FilterPresetManager.shared.updatePresetFromXMPContent(
            xmpContent: sampleXMPContent,
            type: .polaroid,
            index: 0,
            name: "XMP宝丽来风格"
        )
        
        if success {
            print("✅ 成功从XMP更新宝丽来预设")
        } else {
            print("❌ 从XMP更新预设失败")
        }
    }
    
    /// 示例：从XMP文件更新胶卷预设
    static func updateFilmPresetFromXMP() {
        // 胶卷风格的XMP示例
        let filmXMPContent = """
        <?xml version="1.0" encoding="UTF-8"?>
        <x:xmpmeta xmlns:x="adobe:ns:meta/" x:xmptk="Adobe XMP Core 7.0-c000 1.000000, 0000/00/00-00:00:00">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description rdf:about=""
            xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/">
           <crs:Exposure>+0.15</crs:Exposure>
           <crs:Contrast>+18</crs:Contrast>
           <crs:Highlights>-20</crs:Highlights>
           <crs:Shadows>+25</crs:Shadows>
           <crs:Temperature>+150</crs:Temperature>
           <crs:Tint>-5</crs:Tint>
           <crs:Saturation>+12</crs:Saturation>
           <crs:Vibrance>+30</crs:Vibrance>
           <crs:Clarity>+10</crs:Clarity>
           <crs:Sharpness>+25</crs:Sharpness>
           <crs:VignetteAmount>-20</crs:VignetteAmount>
           <crs:SplitToningShadowHue>200</crs:SplitToningShadowHue>
           <crs:SplitToningShadowSaturation>20</crs:SplitToningShadowSaturation>
           <crs:SplitToningHighlightHue>35</crs:SplitToningHighlightHue>
           <crs:SplitToningHighlightSaturation>30</crs:SplitToningHighlightSaturation>
           <crs:GrainAmount>25</crs:GrainAmount>
           <crs:GrainSize>30</crs:GrainSize>
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """
        
        let success = FilterPresetManager.shared.updatePresetFromXMPContent(
            xmpContent: filmXMPContent,
            type: .film,
            index: 0,
            name: "XMP胶卷风格"
        )
        
        if success {
            print("✅ 成功从XMP更新胶卷预设")
        } else {
            print("❌ 从XMP更新预设失败")
        }
    }
    
    /// 示例：批量从XMP文件更新多个预设
    static func batchUpdatePresetsFromXMP() {
        // 定义多个XMP文件和对应的预设配置
        let xmpConfigs = [
            (content: getSampleXMP1(), type: FilterPresetType.vintage, index: 0, name: "复古胶片"),
            (content: getSampleXMP2(), type: FilterPresetType.fashion, index: 0, name: "时尚大片"),
            (content: getSampleXMP3(), type: FilterPresetType.ins, index: 0, name: "INS风格")
        ]
        
        for config in xmpConfigs {
            let success = FilterPresetManager.shared.updatePresetFromXMPContent(
                xmpContent: config.content,
                type: config.type,
                index: config.index,
                name: config.name
            )
            
            print(success ? "✅ 成功更新 \(config.name)" : "❌ 更新 \(config.name) 失败")
        }
    }
    
    /// 示例XMP内容1 - 复古风格
    private static func getSampleXMP1() -> String {
        return """
        <?xml version="1.0" encoding="UTF-8"?>
        <x:xmpmeta xmlns:x="adobe:ns:meta/">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/">
           <crs:Exposure>+0.25</crs:Exposure>
           <crs:Contrast>-10</crs:Contrast>
           <crs:Saturation>-30</crs:Saturation>
           <crs:Temperature>+400</crs:Temperature>
           <crs:Tint>+15</crs:Tint>
           <crs:Clarity>-35</crs:Clarity>
           <crs:VignetteAmount>-50</crs:VignetteAmount>
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """
    }
    
    /// 示例XMP内容2 - 时尚风格
    private static func getSampleXMP2() -> String {
        return """
        <?xml version="1.0" encoding="UTF-8"?>
        <x:xmpmeta xmlns:x="adobe:ns:meta/">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/">
           <crs:Exposure>+0.10</crs:Exposure>
           <crs:Contrast>+35</crs:Contrast>
           <crs:Saturation>+25</crs:Saturation>
           <crs:Temperature>-100</crs:Temperature>
           <crs:Vibrance>+40</crs:Vibrance>
           <crs:Clarity>+30</crs:Clarity>
           <crs:Sharpness>+40</crs:Sharpness>
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """
    }
    
    /// 示例XMP内容3 - INS风格
    private static func getSampleXMP3() -> String {
        return """
        <?xml version="1.0" encoding="UTF-8"?>
        <x:xmpmeta xmlns:x="adobe:ns:meta/">
         <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
          <rdf:Description xmlns:crs="http://ns.adobe.com/camera-raw-settings/1.0/">
           <crs:Exposure>+0.30</crs:Exposure>
           <crs:Contrast>+15</crs:Contrast>
           <crs:Saturation>+8</crs:Saturation>
           <crs:Temperature>+200</crs:Temperature>
           <crs:Vibrance>+25</crs:Vibrance>
           <crs:Clarity>-20</crs:Clarity>
           <crs:VignetteAmount>-25</crs:VignetteAmount>
           <crs:SplitToningShadowHue>220</crs:SplitToningShadowHue>
           <crs:SplitToningShadowSaturation>20</crs:SplitToningShadowSaturation>
           <crs:SplitToningHighlightHue>45</crs:SplitToningHighlightHue>
           <crs:SplitToningHighlightSaturation>35</crs:SplitToningHighlightSaturation>
          </rdf:Description>
         </rdf:RDF>
        </x:xmpmeta>
        """
    }
}

// MARK: - 使用说明

/*
 🎯 XMP预设更新 - 三种使用方法：

 ==========================================
 方法1：自动文件夹处理（推荐）⭐⭐⭐
 ==========================================

 1. 将XMP文件放到项目的 XMPPresets 文件夹中
 2. 按照格式命名：[类型]_[索引]_[名称].xmp
    例如：polaroid_0_我的宝丽来.xmp
 3. 调用自动处理：
    XMPFileManager.autoProcessXMPFiles()

 ==========================================
 方法2：手动指定文件路径
 ==========================================

 let success = FilterPresetManager.shared.updatePresetFromXMP(
     xmpFilePath: "/path/to/your/preset.xmp",
     type: .polaroid,
     index: 0,
     name: "我的自定义预设"
 )

 ==========================================
 方法3：直接提供XMP内容
 ==========================================

 let xmpContent = "您的XMP文件内容..."
 let success = FilterPresetManager.shared.updatePresetFromXMPContent(
     xmpContent: xmpContent,
     type: .film,
     index: 1,
     name: "胶卷风格"
 )

 ==========================================
 支持的XMP参数：
 ==========================================

 ✅ 基础调整：曝光、对比度、高光、阴影、亮度
 ✅ 色彩调整：色温、色调、饱和度、自然饱和度
 ✅ 细节：锐化、清晰度、去雾
 ✅ 效果：渐晕强度、渐晕半径
 ✅ 分离色调：高光/阴影色相和饱和度
 ✅ 色调曲线：参数化阴影、暗部、亮部、高光
 ✅ HSL调整：8个颜色通道的色相、饱和度、明度
 ✅ 颗粒：颗粒量、大小、粗糙度
 ✅ LUT表：自动解析Look预设中的LUT数据

 ==========================================
 文件命名规则：
 ==========================================

 格式：[预设类型]_[索引]_[名称].xmp

 预设类型：
 - polaroid (宝丽来)
 - film (胶卷)
 - vintage (复古)
 - fashion (时尚)
 - ins (INS)

 索引：0-4 (对应预设1-5)

 示例：
 - polaroid_0_经典宝丽来.xmp
 - film_1_Kodak_Gold.xmp
 - vintage_2_复古胶片.xmp
 - fashion_3_时尚大片.xmp
 - ins_4_INS粉调.xmp
 */
