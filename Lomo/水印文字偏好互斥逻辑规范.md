# 水印系统文字与偏好互斥功能需求规范

## 1. 文档目的

本文档详细描述Lomo应用水印系统中文字与偏好选项的互斥逻辑需求，为开发者提供明确的实现指南。

## 2. 背景

Lomo应用允许用户在图片上添加各种样式的水印。为保持UI简洁和用户体验一致性，特定水印类型下，文字与偏好选项需要互斥显示。

## 3. 功能需求

### 3.1 互斥逻辑基本需求

- **FR-01**：指定水印类型下，文字与偏好选项必须互斥（不能同时启用）
- **FR-02**：当用户启用文字时，如偏好已启用，系统应自动关闭偏好选项
- **FR-03**：当用户选择任何偏好选项时，如文字已启用，系统应自动关闭文字选项
- **FR-04**：互斥状态变化时，UI应立即更新以反映新状态

### 3.2 特殊情况处理

- **FR-05**：自定义水印10(custom10)应明确排除互斥逻辑，允许同时显示文字和偏好
- **FR-06**：文字和Logo不互斥，可同时显示

## 4. 技术规范

### 4.1 互斥逻辑实现点

互斥逻辑必须在以下三个关键位置实现：

1. **WatermarkControlView.shouldApplyMutualExclusionForType方法**
   - 功能：控制哪些水印类型应用互斥逻辑
   - 路径：`Lomo/Views/Edit/Components/WatermarkControlView.swift`（约550行）

2. **TextInputOptionView中的文字开关事件处理**
   - 功能：当文字开关状态变化时应用互斥逻辑
   - 路径：`Lomo/Views/Edit/Components/WatermarkControlView.swift`（约1500行和1540行）

3. **WatermarkOptionItem.shouldApplyMutualExclusionLogic方法**
   - 功能：决定水印项目是否应用互斥逻辑
   - 路径：`Lomo/Views/Edit/Components/WatermarkControlView.swift`（约1660行）

### 4.2 当前支持互斥的水印类型

以下水印类型需要支持文字与偏好互斥：
- custom4
- film
- custom7
- custom8
- custom9
- custom5
- custom11
- custom12
- border_2percent

## 5. 实现指南

### 5.1 添加新水印类型支持互斥的步骤

当添加新水印类型需要支持互斥逻辑时，必须修改以下代码：

```swift
// 1. 修改WatermarkControlView.shouldApplyMutualExclusionForType方法
private func shouldApplyMutualExclusionForType(_ type: String) -> Bool {
    if type == "custom10" {
        return false  // 排除custom10
    }
    return type == "custom4" || type == "film" || type == "custom7" || 
           type == "custom8" || type == "custom9" || type == "custom5" || 
           type == "custom11" || type == "custom12" || type == "新水印类型" || 
           type == "border_2percent"
}

// 2. 修改WatermarkOptionItem.shouldApplyMutualExclusionLogic方法
private func shouldApplyMutualExclusionLogic() -> Bool {
    return watermarkType == "custom4" || watermarkType == "film" || 
           watermarkType == "custom7" || watermarkType == "custom8" || 
           watermarkType == "custom9" || watermarkType == "custom5" ||
           watermarkType == "custom11" || watermarkType == "custom12" || 
           watermarkType == "新水印类型" || watermarkType == "border_2percent"
}
```

## 6. 测试要求

### 6.1 验收标准

1. 对每个支持互斥的水印类型，验证以下场景：
   - 当启用文字时，任何已启用的偏好选项应自动关闭
   - 当选择任何偏好选项时，已启用的文字选项应自动关闭
   
2. 确认custom10水印类型允许同时启用文字和偏好选项

3. 确认文字和Logo可以同时显示在任何水印类型中

## 7. 注意事项

- 修改互斥逻辑时，必须同时更新上述三个位置的代码
- 确保UI通知机制正确触发，以便在互斥操作后刷新界面
- 添加新水印类型时，应在设计阶段决定是否需要应用互斥逻辑

## 8. 版本历史

| 版本 | 日期 | 描述 | 作者 |
|------|------|------|------|
| 1.0 | 2023-11-10 | 初始文档 | 开发团队 |
| 1.1 | 2024-05-30 | 添加custom12水印类型 | 开发团队 |

## 9. 附录

### 相关代码文件

- `Lomo/Views/Edit/Components/WatermarkControlView.swift`
- `Lomo/Managers/Edit/WatermarkStyleFactory.swift`
- `Lomo/Utils/Constants/WatermarkConstants.swift` 