# 示例CUBE LUT文件 - 暖色调效果
# 这是一个简化的示例，实际LUT文件会有更多数据点

TITLE "暖色调示例LUT"

# LUT尺寸 (这里使用较小的尺寸作为示例)
LUT_3D_SIZE 9

# 域范围
DOMAIN_MIN 0.0 0.0 0.0
DOMAIN_MAX 1.0 1.0 1.0

# LUT数据 (R G B 格式，每行一个颜色)
# 这个LUT会增加红色和黄色，减少蓝色，创造暖色调效果

0.000000 0.000000 0.000000
0.140625 0.000000 0.000000
0.281250 0.000000 0.000000
0.421875 0.000000 0.000000
0.562500 0.000000 0.000000
0.703125 0.000000 0.000000
0.843750 0.000000 0.000000
0.984375 0.000000 0.000000
1.000000 0.000000 0.000000
0.000000 0.140625 0.000000
0.150000 0.150000 0.000000
0.300000 0.290625 0.000000
0.450000 0.431250 0.000000
0.600000 0.571875 0.000000
0.750000 0.712500 0.000000
0.900000 0.853125 0.000000
1.000000 0.993750 0.000000
1.000000 1.000000 0.000000
0.000000 0.281250 0.000000
0.150000 0.300000 0.000000
0.320000 0.340000 0.000000
0.480000 0.480000 0.000000
0.640000 0.620000 0.000000
0.800000 0.760000 0.000000
0.960000 0.900000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.421875 0.000000
0.150000 0.450000 0.000000
0.320000 0.480000 0.000000
0.500000 0.530000 0.000000
0.680000 0.680000 0.000000
0.860000 0.830000 0.000000
1.000000 0.980000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.562500 0.000000
0.150000 0.600000 0.000000
0.320000 0.640000 0.000000
0.500000 0.680000 0.000000
0.700000 0.740000 0.000000
0.900000 0.900000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.703125 0.000000
0.150000 0.750000 0.000000
0.320000 0.800000 0.000000
0.500000 0.850000 0.000000
0.700000 0.900000 0.000000
0.920000 0.950000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.843750 0.000000
0.150000 0.900000 0.000000
0.320000 0.950000 0.000000
0.500000 1.000000 0.000000
0.700000 1.000000 0.000000
0.920000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.984375 0.000000
0.150000 1.000000 0.000000
0.320000 1.000000 0.000000
0.500000 1.000000 0.000000
0.700000 1.000000 0.000000
0.920000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 1.000000 0.000000
0.150000 1.000000 0.000000
0.320000 1.000000 0.000000
0.500000 1.000000 0.000000
0.700000 1.000000 0.000000
0.920000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
1.000000 1.000000 0.000000
0.000000 0.000000 0.140625
0.150000 0.000000 0.120000
0.300000 0.000000 0.240000
0.450000 0.000000 0.360000
0.600000 0.000000 0.480000
0.750000 0.000000 0.600000
0.900000 0.000000 0.720000
1.000000 0.000000 0.840000
1.000000 0.000000 0.900000
0.000000 0.150000 0.140625
0.160000 0.160000 0.120000
0.320000 0.300000 0.240000
0.480000 0.440000 0.360000
0.640000 0.580000 0.480000
0.800000 0.720000 0.600000
0.960000 0.860000 0.720000
1.000000 1.000000 0.840000
1.000000 1.000000 0.900000

# 继续添加剩余的数据点以完成9x9x9的LUT
# 为了简化，这里省略了大部分数据点
# 实际使用时，您应该使用完整的专业LUT文件

# 注意：这只是一个演示文件
# 真实的LUT文件会包含所有729个数据点（9³）
# 或者更多（如33³ = 35,937个数据点）

# 最后几个数据点示例
0.900000 0.950000 0.800000
1.000000 1.000000 0.850000
1.000000 1.000000 0.900000
