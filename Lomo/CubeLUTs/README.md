# CUBE LUT文件夹

## 🎨 什么是CUBE LUT？

CUBE是3D LUT（Look-Up Table）的标准文件格式，广泛用于：
- 🎬 电影调色和后期制作
- 📸 摄影后期处理
- 🎮 游戏画面渲染
- 📺 视频制作

## 🎯 使用方法

### 1. 获取CUBE文件
- Adobe官方LUT包
- 电影制作公司发布的LUT
- 第三方调色师分享
- 在线LUT资源网站

### 2. 放置文件
将您的.cube文件直接拖拽到这个文件夹中

### 3. 文件命名规则
文件名格式：`[预设类型]_[索引]_[名称].cube`

**支持的预设类型：**
- `polaroid` - 宝丽来
- `film` - 胶卷  
- `vintage` - 复古
- `fashion` - 时尚
- `ins` - INS

**索引范围：** 0-4 (对应预设1-5)

### 4. 文件名示例
```
polaroid_0_电影胶片.cube
film_1_Kodak_5218.cube
vintage_2_复古暖调.cube
fashion_3_时尚冷调.cube
ins_4_INS橙青.cube
```

### 5. 自动处理
在代码中调用：
```swift
// 一键处理所有CUBE文件
CubeLUTManager.autoProcessLUTFiles()

// 或者分步操作
CubeLUTManager.listLUTFiles()        // 列出文件
CubeLUTManager.processAllLUTFiles()  // 处理所有文件
```

## 📋 CUBE文件格式说明

### 基本结构
```
# 注释行
TITLE "LUT名称"
LUT_3D_SIZE 33
DOMAIN_MIN 0.0 0.0 0.0
DOMAIN_MAX 1.0 1.0 1.0

# RGB数据（每行一个颜色）
0.000000 0.000000 0.000000
0.031250 0.000000 0.000000
0.062500 0.000000 0.000000
...
```

### 常见LUT尺寸
- **17³** = 4,913 个数据点（快速，适合实时预览）
- **33³** = 35,937 个数据点（平衡，最常用）
- **65³** = 274,625 个数据点（精确，文件较大）

## 🎨 LUT效果类型

### 电影LUT
- **Alexa LogC to Rec709** - 标准电影色彩空间转换
- **RED Dragon to Rec709** - RED相机色彩匹配
- **Film Emulation** - 胶片模拟效果

### 创意LUT
- **Orange & Teal** - 经典橙青对比
- **Vintage Film** - 复古胶片风格
- **Bleach Bypass** - 漂白效果
- **Cross Process** - 交叉冲印

### 校正LUT
- **Color Temperature** - 色温校正
- **Gamma Correction** - 伽马校正
- **Saturation Boost** - 饱和度增强

## 🔧 技术特性

### 自动分析
系统会自动分析每个LUT的特征：
- 色彩偏移（红、绿、蓝通道）
- 对比度变化
- 饱和度调整
- LUT类型识别

### 智能转换
将3D LUT效果转换为等效的滤镜参数：
- 色温和色调调整
- 饱和度和对比度
- 清晰度和锐化
- 高光阴影控制

### 性能优化
- 支持GPU加速处理
- 自动选择合适的LUT尺寸
- 内存优化和缓存机制

## ⚠️ 注意事项

### 文件要求
- 必须是有效的CUBE格式
- 文件编码必须是UTF-8
- 支持的LUT尺寸：2³ 到 256³

### 性能考虑
- 大尺寸LUT会影响实时预览性能
- 建议使用33³尺寸的LUT
- 避免同时加载过多大尺寸LUT

### 兼容性
- 支持Adobe标准CUBE格式
- 兼容DaVinci Resolve导出的LUT
- 支持Premiere Pro和After Effects LUT

## 💡 使用技巧

### 1. LUT分类管理
按效果类型组织LUT文件：
- 电影级调色LUT
- 创意风格LUT
- 技术校正LUT

### 2. 效果叠加
可以将LUT效果与参数调节结合：
- 先应用LUT基础效果
- 再进行细节参数调整
- 创造独特的视觉风格

### 3. 批量处理
一次性导入多个LUT：
- 准备多个CUBE文件
- 按命名规则重命名
- 一键批量处理

### 4. 效果预览
处理前可以预览LUT信息：
- LUT尺寸和数据量
- 色彩特征分析
- 效果类型识别

## 🌐 LUT资源推荐

### 免费资源
- Adobe官方LUT包
- DaVinci Resolve内置LUT
- 开源电影LUT项目

### 商业资源
- 专业调色师LUT包
- 电影制作公司LUT
- 品牌相机LUT包

### 自制LUT
- 使用DaVinci Resolve创建
- Adobe Premiere Pro导出
- 第三方LUT生成工具
