# 字体系统优化修改记录

## 修改目的

优化字体选择系统，改进字体粗细选择逻辑，使其更加直观和易于维护。主要解决了以下问题：

1. 原有系统使用0.0-1.0的浮点数值映射到不同字体粗细，导致代码复杂且难以维护
2. 不同字体有不同的粗细变体名称，需要在多处维护映射关系
3. 中文、英文和署名字体的粗细处理逻辑不一致

## 修改内容

### 1. 数据模型优化

在`WatermarkSettings.swift`中添加了以下属性：
- `selectedFontWeight`：保存用户选择的中文字体粗细名称
- `selectedSignatureFontWeight`：保存用户选择的署名字体粗细名称
- `selectedEnglishFontWeight`：保存用户选择的英文字体粗细名称（原有属性）

### 2. 字体粗细选择逻辑优化

在`WatermarkControlView.swift`中：
- 修改初始化方法，优先使用保存的字体粗细名称，只有在未设置时才使用默认值
- 修改字体粗细选择按钮处理逻辑，直接保存选择的字体粗细名称
- 为向后兼容，保留了`fontThicknessSliderValue`、`signatureFontThicknessSliderValue`和`englishFontThicknessSliderValue`的更新

### 3. 字体应用逻辑优化

在`WatermarkStyles.swift`中：
- 修改`configureTextForMixedLanguage`方法，使用直接的字体粗细名称来构建字体，不再通过数值判断
- 删除了不再需要的辅助方法：
  - `getFuturaPTWeightFromSlider`
  - `getTypographWeightFromSlider`
  - `getMiamoWeightFromSlider`
  - `getQuanticoWeightFromSlider` 
  - `getSyntaxWeightFromSlider`
  - `getFontWeightForThickness`
- 保留了`mapFuturaPTWeight`方法用于特殊映射

## 统一的字体处理流程

新的字体处理流程更加直观和一致：

1. 用户选择字体类型（中文、英文、署名）
2. 用户选择字体粗细变体
3. 应用时直接使用字体名称+粗细名称构建完整字体名
4. 不再需要复杂的数值映射和条件判断

## 兼容性说明

为保持向后兼容，我们保留了基于滑块值的数值映射逻辑，但在内部实现中优先使用新的基于名称的方式。这确保了：

1. 旧版本保存的设置仍然可以正常工作
2. 新版本采用更加直观的实现方式
3. 未来可以完全移除这些旧的数值映射逻辑

## 未来优化方向

1. 完全移除数值映射逻辑，仅使用基于名称的方式
2. 进一步简化字体选择UI，可能改为字体预览列表
3. 统一中文、英文和署名字体的处理逻辑 