#include <metal_stdlib>
using namespace metal;

// HSB转RGB辅助函数
float3 hsbToRgb(float hue, float saturation, float brightness) {
    float h = fmod(hue * 6.0, 6.0);
    int i = int(h);
    float f = h - i;
    float p = brightness * (1.0 - saturation);
    float q = brightness * (1.0 - saturation * f);
    float t = brightness * (1.0 - saturation * (1.0 - f));
    
    float3 rgb;
    switch(i) {
        case 0: rgb = float3(brightness, t, p); break;
        case 1: rgb = float3(q, brightness, p); break;
        case 2: rgb = float3(p, brightness, t); break;
        case 3: rgb = float3(p, q, brightness); break;
        case 4: rgb = float3(t, p, brightness); break;
        default: rgb = float3(brightness, p, q);
    }
    return rgb;
}

// HSB色轮着色器 - 为所有主要色相添加RGB空间平滑处理
[[ stitchable ]] half4 hsbColorWheel(float2 position, half4 color, float2 size) {
    // 计算中心点
    float2 center = size / 2.0;
    float radius = min(size.x, size.y) / 2.0;
    
    // 计算到中心的距离
    float2 delta = position - center;
    float distance = length(delta);
    
    // 边缘过渡带宽度
    float edgeWidth = 1.0;
    
    // 如果点在圆外很远，直接返回透明色
    if (distance > radius + edgeWidth) {
        return half4(0.0, 0.0, 0.0, 0.0);
    }
    
    // 计算饱和度
    float saturation = min(distance / radius, 1.0);
    
    // 计算色相
    float angle = atan2(delta.y, delta.x);
    float hue = (angle + M_PI_F) / (2.0 * M_PI_F);
    
    // HSB转RGB
    float brightness = 1.0; 
    float3 rgb = hsbToRgb(hue, saturation, brightness);
    
    // --- RGB空间平滑处理 --- 
    // 为主要色相设置混合宽度
    float redBlendWidth = 0.02;     // 红色混合宽度 (约7.2度)
    float yellowBlendWidth = 0.02;  // 黄色混合宽度 (约7.2度)
    // float greenBlendWidth = 0.0;   // 绿色通常较平滑，取消处理
    float cyanBlendWidth = 0.02;   // 青色混合宽度 (约7.2度)
    float blueBlendWidth = 0.025;   // 蓝色混合宽度 (约9度)
    float magentaBlendWidth = 0.02; // 品红色混合宽度 (约7.2度)

    // 定义主要色相的值
    float yellowHue = 60.0 / 360.0;
    float greenHue = 120.0 / 360.0;
    float cyanHue = 180.0 / 360.0;
    float blueHue = 240.0 / 360.0;
    float magentaHue = 300.0 / 360.0;

    // 辅助混合函数
    auto applyBlend = [&](float currentHue, float targetHue, float blendWidth) {
        float dist = abs(currentHue - targetHue);
        // 处理环形边界情况 (例如红色0/1)
        if (targetHue == 0.0) {
             dist = min(currentHue, 1.0 - currentHue);
        } else {
             dist = min(dist, 1.0 - dist); // 考虑环形距离
        }

        if (dist < blendWidth) {
            float blendFactor = smoothstep(0.0, blendWidth, dist);
            float neighborHue1 = fmod(currentHue + blendWidth * 0.5, 1.0);
            float neighborHue2 = fmod(currentHue - blendWidth * 0.5 + 1.0, 1.0);
            float3 rgb1 = hsbToRgb(neighborHue1, saturation, brightness);
            float3 rgb2 = hsbToRgb(neighborHue2, saturation, brightness);
            rgb = mix(rgb, mix(rgb1, rgb2, 0.5), 1.0 - blendFactor);
        }
    };

    // 应用混合
    applyBlend(hue, 0.0, redBlendWidth);          // 红色
    applyBlend(hue, yellowHue, yellowBlendWidth);  // 黄色
    // applyBlend(hue, greenHue, greenBlendWidth);    // 绿色 (取消处理)
    applyBlend(hue, cyanHue, cyanBlendWidth);      // 青色
    applyBlend(hue, blueHue, blueBlendWidth);      // 蓝色
    applyBlend(hue, magentaHue, magentaBlendWidth);// 品红
    
    // --- 结束RGB空间平滑处理 ---
    
    // 边缘平滑处理 - 基于距离计算透明度
    float alpha = 1.0;
    if (distance > radius - edgeWidth && distance <= radius + edgeWidth) {
        // 在边缘过渡带内，计算平滑的透明度
        alpha = 1.0 - smoothstep(radius - edgeWidth, radius + edgeWidth, distance);
    }
    
    return half4(half3(rgb), half(alpha));
} 