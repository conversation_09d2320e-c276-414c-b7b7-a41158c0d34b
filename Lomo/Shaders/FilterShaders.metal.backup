#include <metal_stdlib>
using namespace metal;

// MARK: - 滤镜参数结构体

struct FilterParameters {
    float exposure;
    float contrast;
    float brightness;
    float saturation;
    float vibrance;
    float temperature;
    float tint;
    float highlights;
    float shadows;
    float whites;
    float blacks;
    float clarity;
    float dehaze;
    float gamma;
    float fadeEffect;    // 褪色效果强度 (0.0 到 1.0)
    float monoEffect;    // 黑白效果强度 (0.0 到 1.0)
    float isLinearInput;
    float highlightProtectionIntensity;  // 高光保护强度 (0.0 到 1.0)
    int highlightProtectionMode;         // 高光保护模式 (0=标准, 1=胶片, 2=线性)
    float curveIntensity;                // 曲线强度 (0.0 到 1.0) - 第1步添加

    // HSL选择性调整参数
    float hue;                   // 色相调整 (-180.0 到 +180.0 度)
    float hslSaturation;         // HSL饱和度调整 (-100.0 到 +100.0)
    float hslLuminance;          // HSL明度调整 (-100.0 到 +100.0)
    int selectedHSLColorIndex;   // 选中的颜色范围索引 (0-7)
    float hslColorRangeSoftness; // 颜色范围过渡柔和度 (0.0 到 1.0)

    // 色调分离参数
    float highlightHue;          // 高光色相 (0.0 到 360.0 度)
    float highlightSaturation;   // 高光饱和度 (0.0 到 1.0)
    float shadowHue;             // 阴影色相 (0.0 到 360.0 度)
    float shadowSaturation;      // 阴影饱和度 (0.0 到 1.0)
    float splitToningBalance;    // 色调分离平衡 (-1.0 到 1.0)

    // 细节调整参数
    float sharpness;             // 锐化强度 (-1.0 到 1.0)
    float vignetteIntensity;     // 暗角强度 (-1.0 到 1.0)
    float vignetteRadius;        // 暗角半径 (0.0 到 2.0)
    float noiseReduction;        // 降噪强度 (0.0 到 1.0)
};

// MARK: - 基础参数处理函数

/// 曝光处理
inline float3 applyExposure(float3 color, float exposure) {
    if (abs(exposure) < 0.001) {
        return color;
    }
    return color * exp2(exposure);
}

/// 对比度处理
inline float3 applyContrast(float3 color, float contrast) {
    if (abs(contrast) < 0.001) {
        return color;
    }
    return (color - 0.5) * (1.0 + contrast) + 0.5;
}

/// 亮度处理
inline float3 applyBrightness(float3 color, float brightness) {
    if (abs(brightness) < 0.001) {
        return color;
    }
    return color + brightness;
}

/// 饱和度处理
inline float3 applySaturation(float3 color, float saturation) {
    if (abs(saturation) < 0.001) {
        return color;
    }
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    return mix(float3(luma), color, 1.0 + saturation);
}

/// 自然饱和度处理
inline float3 applyVibrance(float3 color, float vibrance) {
    if (abs(vibrance) < 0.001) {
        return color;
    }
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    float maxChannel = max(max(color.r, color.g), color.b);
    float saturationMask = 1.0 - maxChannel;
    return mix(color, mix(float3(luma), color, 1.0 + vibrance), saturationMask);
}

/// 色温处理
inline float3 applyTemperature(float3 color, float temperature) {
    if (abs(temperature) < 0.001) {
        return color;
    }
    // 简化的色温调整
    float3 tempAdjust = float3(1.0 + temperature * 0.01, 1.0, 1.0 - temperature * 0.01);
    return color * tempAdjust;
}

/// 色调处理
inline float3 applyTint(float3 color, float tint) {
    if (abs(tint) < 0.001) {
        return color;
    }
    // 简化的色调调整
    float3 tintAdjust = float3(1.0, 1.0 + tint * 0.01, 1.0);
    return color * tintAdjust;
}

/// 高光处理
inline float3 applyHighlights(float3 color, float highlights) {
    if (abs(highlights) < 0.001) {
        return color;
    }
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    float highlightMask = smoothstep(0.5, 1.0, luma);
    return color + highlights * highlightMask * 0.01;
}

/// 阴影处理
inline float3 applyShadows(float3 color, float shadows) {
    if (abs(shadows) < 0.001) {
        return color;
    }
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    float shadowMask = smoothstep(0.5, 0.0, luma);
    return color + shadows * shadowMask * 0.01;
}

/// 白色调整
inline float3 applyWhites(float3 color, float whites) {
    if (abs(whites) < 0.001) {
        return color;
    }
    float white_point = 1.0 - whites * 0.3;
    return color / white_point;
}

/// 黑色调整
inline float3 applyBlacks(float3 color, float blacks) {
    if (abs(blacks) < 0.001) {
        return color;
    }
    float black_point = -blacks * 0.1;
    return (color - black_point) / (1.0 - black_point);
}

/// 去雾处理
inline float3 applyDehaze(float3 color, float dehaze) {
    if (abs(dehaze) < 0.001) {
        return color;
    }
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    float dehaze_strength = dehaze * 0.5;
    float contrast_boost = 1.0 + dehaze_strength * (1.0 - luma);
    return color * contrast_boost;
}

// MARK: - 细节调整算法 - 按照色调分离模式实现

/// 锐化处理 - Unsharp Mask算法
inline float3 applySharpening(float3 color, float intensity) {
    if (abs(intensity) < 0.001) {
        return color;
    }

    // 简化的锐化：增强对比度
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    float3 detail = color - float3(luma);
    return color + detail * intensity * 2.0;
}

/// 暗角效果 - 径向渐变算法
inline float3 applyVignette(float3 color, float2 texCoord, float intensity, float radius) {
    if (abs(intensity) < 0.001) {
        return color;
    }

    // 计算到中心的距离
    float2 center = float2(0.5, 0.5);
    float distance = length(texCoord - center);

    // 计算暗角衰减
    float vignette = 1.0 - smoothstep(0.0, radius, distance);

    // 应用暗角效果
    if (intensity > 0.0) {
        // 正值：暗角（边缘变暗）
        return color * mix(1.0, vignette, intensity);
    } else {
        // 负值：反暗角（边缘变亮）
        return color * mix(1.0, 2.0 - vignette, -intensity);
    }
}

/// 降噪处理 - 简化算法
inline float3 applyNoiseReduction(float3 color, float intensity) {
    if (intensity < 0.001) {
        return color;
    }

    // 简化的降噪：轻微平滑
    float luma = dot(color, float3(0.299, 0.587, 0.114));
    return mix(color, float3(luma), intensity * 0.2);
}

// MARK: - 第3步添加：曲线处理函数

/// 曲线LUT采样 - 使用硬件采样器
inline float sampleCurveLUT(float value, texture1d<float> lutTexture, sampler lutSampler) {
    float clampedValue = clamp(value, 0.0, 1.0);
    return lutTexture.sample(lutSampler, clampedValue).r;
}

/// 应用曲线调整到RGB颜色
inline float3 applyCurveToColor(float3 color, texture1d<float> lutTexture, sampler lutSampler, float intensity) {
    if (intensity <= 0.0) {
        return color;
    }

    // 对每个颜色通道应用曲线
    float3 curvedColor;
    curvedColor.r = sampleCurveLUT(color.r, lutTexture, lutSampler);
    curvedColor.g = sampleCurveLUT(color.g, lutTexture, lutSampler);
    curvedColor.b = sampleCurveLUT(color.b, lutTexture, lutSampler);

    // 根据强度混合原色和曲线调整后的颜色
    return mix(color, curvedColor, intensity);
}

/// 保持亮度的色域映射函数
float3 apply_luminance_preserving_gamut_mapping(float3 color, float target_luminance) {
    // 计算当前亮度
    float current_luminance = 0.299 * color.r + 0.587 * color.g + 0.114 * color.b;

    // 如果颜色在有效范围内且亮度接近目标，直接返回
    if (all(color >= 0.0) && all(color <= 1.0) && abs(current_luminance - target_luminance) < 0.001) {
        return color;
    }

    // 首先将颜色限制在有效范围内
    float3 clamped_color = clamp(color, 0.0, 1.0);

    // 重新计算限制后的亮度
    float clamped_luminance = 0.299 * clamped_color.r + 0.587 * clamped_color.g + 0.114 * clamped_color.b;

    // 如果限制后亮度为0，返回灰度值
    if (clamped_luminance < 0.001) {
        return float3(target_luminance);
    }

    // 计算亮度缩放因子
    float luminance_scale = target_luminance / clamped_luminance;

    // 应用亮度缩放，但要确保不超出色域
    float3 scaled_color = clamped_color * luminance_scale;

    // 如果缩放后仍超出色域，使用迭代方法调整
    if (any(scaled_color > 1.0)) {
        // 找到最大的通道值
        float max_channel = max(max(scaled_color.r, scaled_color.g), scaled_color.b);

        // 按比例缩小，保持色相
        scaled_color = scaled_color / max_channel;

        // 重新调整亮度，通过增加所有通道的基础值
        float current_lum = 0.299 * scaled_color.r + 0.587 * scaled_color.g + 0.114 * scaled_color.b;
        float lum_diff = target_luminance - current_lum;

        if (lum_diff > 0.0) {
            // 需要增加亮度，均匀提升所有通道
            float boost = min(lum_diff / 3.0, 1.0 - max(max(scaled_color.r, scaled_color.g), scaled_color.b));
            scaled_color += boost;
        }
    }

    return clamp(scaled_color, 0.0, 1.0);
}

/// 亮度计算
float luminance(float3 color) {
    return dot(color, float3(0.299, 0.587, 0.114));
}

/// RGB转HSL - 基于您提供的专业实现
float3 rgb_to_hsl(float3 rgb) {
    float maxVal = max(max(rgb.r, rgb.g), rgb.b);
    float minVal = min(min(rgb.r, rgb.g), rgb.b);
    float delta = maxVal - minVal;

    // 明度 (Lightness)
    float l = (maxVal + minVal) / 2.0;

    // 饱和度 (Saturation)
    float s = 0.0;
    if (delta != 0.0) {
        s = l > 0.5 ? delta / (2.0 - maxVal - minVal) : delta / (maxVal + minVal);
    }

    // 色相 (Hue)
    float h = 0.0;
    if (delta != 0.0) {
        if (maxVal == rgb.r) {
            h = ((rgb.g - rgb.b) / delta) + (rgb.g < rgb.b ? 6.0 : 0.0);
        } else if (maxVal == rgb.g) {
            h = (rgb.b - rgb.r) / delta + 2.0;
        } else {
            h = (rgb.r - rgb.g) / delta + 4.0;
        }
        h /= 6.0;
    }

    return float3(h * 360.0, s * 100.0, l * 100.0);
}

/// HSL转RGB辅助函数
float hue_to_rgb(float p, float q, float t) {
    if (t < 0.0) t += 1.0;
    if (t > 1.0) t -= 1.0;
    if (t < 1.0/6.0) return p + (q - p) * 6.0 * t;
    if (t < 1.0/2.0) return q;
    if (t < 2.0/3.0) return p + (q - p) * (2.0/3.0 - t) * 6.0;
    return p;
}

/// HSL转RGB - 基于您提供的专业实现
float3 hsl_to_rgb(float3 hsl) {
    float h = hsl.x / 360.0;
    float s = hsl.y / 100.0;
    float l = hsl.z / 100.0;

    if (s == 0.0) {
        return float3(l, l, l);
    }

    float q = l < 0.5 ? l * (1.0 + s) : l + s - l * s;
    float p = 2.0 * l - q;

    float r = hue_to_rgb(p, q, h + 1.0/3.0);
    float g = hue_to_rgb(p, q, h);
    float b = hue_to_rgb(p, q, h - 1.0/3.0);

    return float3(r, g, b);
}

/// RGB转HSV (保留兼容性)
float3 rgb_to_hsv(float3 rgb) {
    float3 hsl = rgb_to_hsl(rgb);
    // 简化：HSV的V近似等于HSL的L，S需要转换
    return float3(hsl.x / 360.0, hsl.y / 100.0, hsl.z / 100.0);
}

/// HSV转RGB (保留兼容性)
float3 hsv_to_rgb(float3 hsv) {
    float3 hsl = float3(hsv.x * 360.0, hsv.y * 100.0, hsv.z * 100.0);
    return hsl_to_rgb(hsl);
}

/// sRGB到XYZ转换 (简化版，用于非线性空间)
float3 srgb_to_xyz(float3 rgb) {
    // 简化的sRGB到XYZ转换矩阵
    float3x3 rgb_to_xyz_matrix = float3x3(
        0.4124564, 0.3575761, 0.1804375,
        0.2126729, 0.7151522, 0.0721750,
        0.0193339, 0.1191920, 0.9503041
    );
    return rgb_to_xyz_matrix * rgb;
}

/// XYZ到sRGB转换 (简化版)
float3 xyz_to_srgb(float3 xyz) {
    float3x3 xyz_to_rgb_matrix = float3x3(
         3.2404542, -1.5371385, -0.4985314,
        -0.9692660,  1.8760108,  0.0415560,
         0.0556434, -0.2040259,  1.0572252
    );
    return xyz_to_rgb_matrix * xyz;
}

/// Lab转换辅助函数 (sRGB版本)
float lab_f_srgb(float t) {
    return (t > 0.008856) ? pow(t, 1.0/3.0) : (7.787 * t + 16.0/116.0);
}

/// Lab逆转换辅助函数 (sRGB版本)
float lab_f_inv_srgb(float t) {
    float t3 = t * t * t;
    return (t3 > 0.008856) ? t3 : (t - 16.0/116.0) / 7.787;
}

/// XYZ到Lab转换 (sRGB版本)
float3 xyz_to_lab_simple(float3 xyz) {
    // D65白点
    const float3 white_point = float3(0.95047, 1.00000, 1.08883);
    xyz = xyz / white_point;

    // 应用Lab转换函数
    float fx = lab_f_srgb(xyz.x);
    float fy = lab_f_srgb(xyz.y);
    float fz = lab_f_srgb(xyz.z);

    float L = 116.0 * fy - 16.0;
    float a = 500.0 * (fx - fy);
    float b = 200.0 * (fy - fz);

    return float3(L, a, b);
}

/// Lab到XYZ转换 (sRGB版本)
float3 lab_to_xyz_simple(float3 lab) {
    const float3 white_point = float3(0.95047, 1.00000, 1.08883);

    float fy = (lab.x + 16.0) / 116.0;
    float fx = lab.y / 500.0 + fy;
    float fz = fy - lab.z / 200.0;

    float3 xyz = float3(lab_f_inv_srgb(fx), lab_f_inv_srgb(fy), lab_f_inv_srgb(fz));
    return xyz * white_point;
}

/// 业界标准曝光算法 - color.rgb *= pow(2.0, params.exposure) + exp2()性能优化
float3 apply_simple_exposure(float3 color, float exposure) {
    if (abs(exposure) < 0.001) {
        return color;
    }

    // 使用业界标准的摄影曝光公式：color.rgb *= pow(2.0, params.exposure)
    // 性能优化：exp2(x) 等价于 pow(2.0, x) 但在GPU上性能更好
    float exposure_factor = exp2(exposure);

    // 直接应用曝光，不做任何复杂处理
    return color * exposure_factor;
}

// MARK: - 高光保护算法

/// 标准模式：Reinhard tone mapping变体
/// 提供平滑的高光压缩，保持自然的色彩过渡
float3 apply_standard_highlight_protection(float3 color, float intensity) {
    if (intensity < 0.001) {
        return color;
    }

    // Reinhard tone mapping变体
    // 公式：color / (1 + color * intensity)
    float3 protected_color = color / (1.0 + color * intensity);

    // 保持原始亮度的比例混合
    float original_luma = luminance(color);
    float protected_luma = luminance(protected_color);

    if (protected_luma > 0.001) {
        protected_color *= original_luma / protected_luma * (1.0 - intensity * 0.3);
    }

    return mix(color, protected_color, intensity);
}

/// 胶片模式：自然高光压缩特性
/// 模拟胶片的非线性响应曲线，提供更自然的高光过渡
float3 apply_film_highlight_protection(float3 color, float intensity) {
    if (intensity < 0.001) {
        return color;
    }

    // 胶片特性曲线 - S型响应
    float3 film_color = color;

    for (int i = 0; i < 3; i++) {
        float x = color[i];

        // 胶片的非线性响应：使用修正的S曲线
        // 在高光区域提供更强的压缩
        if (x > 0.5) {
            // 高光区域：使用对数压缩
            float highlight_factor = (x - 0.5) * 2.0; // 0-1范围
            float compressed = 0.5 + 0.5 * (1.0 - exp(-highlight_factor * (1.0 + intensity * 2.0)));
            film_color[i] = compressed;
        } else {
            // 中低调区域：保持线性
            film_color[i] = x * (1.0 + intensity * 0.1);
        }
    }

    return mix(color, film_color, intensity);
}

/// 线性空间：对数压缩保持线性特性
/// 在线性空间中进行对数压缩，保持色彩的线性特性
float3 apply_linear_highlight_protection(float3 color, float intensity) {
    if (intensity < 0.001) {
        return color;
    }

    // 线性空间对数压缩
    // 使用自然对数进行平滑压缩
    float3 linear_color = color;

    for (int i = 0; i < 3; i++) {
        float x = color[i];

        if (x > 0.001) {
            // 对数压缩：log(1 + x * intensity) / log(1 + intensity)
            float log_factor = 1.0 + intensity * 3.0;
            float compressed = log(1.0 + x * log_factor) / log(1.0 + log_factor);
            linear_color[i] = compressed;
        }
    }

    return mix(color, linear_color, intensity);
}

/// 统一的高光保护接口
/// mode: 0=标准模式, 1=胶片模式, 2=线性空间模式
float3 apply_highlight_protection(float3 color, float intensity, int mode) {
    if (intensity < 0.001) {
        return color;
    }

    switch (mode) {
        case 0:
            return apply_standard_highlight_protection(color, intensity);
        case 1:
            return apply_film_highlight_protection(color, intensity);
        case 2:
            return apply_linear_highlight_protection(color, intensity);
        default:
            return apply_standard_highlight_protection(color, intensity);
    }
}

// 删除复杂的Lab色彩空间转换和高级曝光算法
// 只保留最简单的曝光实现

/// Lab色彩空间色温色调调整 (sRGB版本) - 亮度保持优化版本
float3 apply_lab_temperature_tint_srgb(float3 rgb, float temperature, float tint) {
    // 0. 记录原始感知亮度（使用标准亮度公式）
    float original_luminance = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;

    // 1. sRGB → XYZ → Lab转换
    float3 xyz = srgb_to_xyz(rgb);
    float3 lab = xyz_to_lab_simple(xyz);

    // 2. 保存原始L*，只调整a*和b*
    float original_L = lab.x;

    // 3. a* (绿-红) 和 b* (蓝-黄) 的调整
    const float adjustment_scale = 10.0; // 可根据UI范围调整

    lab.y += tint * adjustment_scale;        // a* 调整 (色调)
    lab.z += temperature * adjustment_scale; // b* 调整 (色温)

    // 4. 恢复原始L*
    lab.x = original_L;

    // 5. Lab → XYZ → sRGB转换
    xyz = lab_to_xyz_simple(lab);
    float3 result = xyz_to_srgb(xyz);

    // 6. 智能色域映射 - 保持亮度的同时处理超出色域的颜色
    result = apply_luminance_preserving_gamut_mapping(result, original_luminance);

    return result;
}



/// 亮度保持的色温色调调整（RGB矩阵版本）
float3 apply_luminance_preserving_temperature_tint(float3 rgb, float temperature, float tint) {
    // 记录原始亮度
    float original_luminance = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;

    float3 result = rgb;

    // 色温调整
    if (temperature != 0.0) {
        float temp = temperature; // -1.0 到 +1.0

        // 改进的色温调整矩阵 - 更平衡的通道调整
        float3x3 tempMatrix;
        if (temp > 0.0) {
            // 暖色调 - 更温和的调整
            tempMatrix = float3x3(
                1.0 + temp * 0.15,  0.0,                0.0,
                0.0,                1.0 + temp * 0.02,  0.0,
                0.0,                0.0,                1.0 - temp * 0.20
            );
        } else {
            // 冷色调 - 更温和的调整
            tempMatrix = float3x3(
                1.0 + temp * 0.20,  0.0,                0.0,
                0.0,                1.0 + temp * 0.02,  0.0,
                0.0,                0.0,                1.0 - temp * 0.15
            );
        }
        result = tempMatrix * result;
    }

    // 色调调整
    if (tint != 0.0) {
        // 更平衡的色调调整
        result.g += tint * 0.08;   // 绿色通道
        result.r -= tint * 0.04;   // 红色通道（相对减少）
        result.b -= tint * 0.02;   // 蓝色通道（轻微减少）
    }

    // 应用亮度保持的色域映射
    return apply_luminance_preserving_gamut_mapping(result, original_luminance);
}

/// 胶片风格的亮度保持色温色调调整
float3 apply_film_temperature_tint(float3 rgb, float temperature, float tint) {
    // 记录原始亮度
    float original_luminance = 0.299 * rgb.r + 0.587 * rgb.g + 0.114 * rgb.b;

    float3 result = rgb;

    // 胶片风格色温调整 - 更柔和的效果
    if (temperature != 0.0) {
        float temp = temperature; // -1.0 到 +1.0

        float3x3 tempMatrix;
        if (temp > 0.0) {
            // 胶片暖色调 - 非常温和的调整
            tempMatrix = float3x3(
                1.0 + temp * 0.12,  0.0,               0.0,
                0.0,                1.0 + temp * 0.01, 0.0,
                0.0,                0.0,               1.0 - temp * 0.15
            );
        } else {
            // 胶片冷色调 - 非常温和的调整
            tempMatrix = float3x3(
                1.0 + temp * 0.15,  0.0,               0.0,
                0.0,                1.0 + temp * 0.01, 0.0,
                0.0,                0.0,               1.0 - temp * 0.12
            );
        }
        result = tempMatrix * result;
    }

    // 胶片风格色调调整 - 更微妙的效果
    if (tint != 0.0) {
        result.g += tint * 0.06;   // 更温和的绿色调整
        result.r -= tint * 0.03;   // 更温和的红色调整
        result.b -= tint * 0.015;  // 更温和的蓝色调整
    }

    // 应用亮度保持的色域映射
    return apply_luminance_preserving_gamut_mapping(result, original_luminance);
}

/// 色温调整 - 将色温偏移转换为RGB乘数 (保留作为备选)
float3 temperatureToRGB(float temperature) {
    // temperature范围: -1.0 到 1.0
    // 负值 = 冷色调 (蓝色增强)
    // 正值 = 暖色调 (红色增强)

    float3 rgb = float3(1.0);

    if (temperature > 0.0) {
        // 暖色调：增强红色，减少蓝色
        rgb.r = 1.0 + temperature * 0.3;
        rgb.b = 1.0 - temperature * 0.2;
    } else {
        // 冷色调：增强蓝色，减少红色
        rgb.r = 1.0 + temperature * 0.2;
        rgb.b = 1.0 - temperature * 0.3;
    }

    return rgb;
}

/// 褪色效果 - 模拟CIPhotoEffectFade (保持亮度版本)
float3 applyFadeEffect(float3 color, float intensity) {
    // 褪色效果：降低对比度，轻微去饱和，添加暖色调，但保持整体亮度
    float3 faded = color;

    // 记录原始亮度
    float originalLuma = luminance(faded);

    // 1. 降低对比度 - 向中灰色靠拢
    faded = mix(faded, float3(0.5), intensity * 0.2); // 减少强度从0.3到0.2

    // 2. 轻微去饱和
    float currentLuma = luminance(faded);
    faded = mix(faded, float3(currentLuma), intensity * 0.3); // 减少强度从0.4到0.3

    // 3. 添加暖色调
    faded.r += intensity * 0.08; // 减少从0.1到0.08
    faded.b -= intensity * 0.04; // 减少从0.05到0.04

    // 4. 恢复原始亮度
    float newLuma = luminance(faded);
    if (newLuma > 0.001) { // 避免除零
        faded *= originalLuma / newLuma;
    }

    return clamp(faded, 0.0, 1.0);
}

/// 黑白效果 - 模拟CIPhotoEffectNoir
float3 applyMonoEffect(float3 color, float intensity) {
    // 黑白效果：转换为灰度，增强对比度
    float3 mono = color;

    // 1. 计算加权灰度值 - 使用更好的权重
    float gray = dot(mono, float3(0.299, 0.587, 0.114));

    // 2. 增强对比度的灰度
    gray = (gray - 0.5) * 1.2 + 0.5;
    gray = clamp(gray, 0.0, 1.0);

    // 3. 根据强度混合原色和黑白
    return mix(color, float3(gray), intensity);
}

// MARK: - 色调分离算法

/// 高光/阴影区域检测 - 基于亮度的智能遮罩（改进版）
float3 calculateToneMasks(float3 color) {
    float luma = luminance(color);

    // 使用更精确的区域分离算法
    // 阴影遮罩：在低亮度区域为1，高亮度区域为0
    float shadowMask = 1.0 - smoothstep(0.0, 0.4, luma);
    shadowMask = pow(shadowMask, 1.5); // 增强对比度

    // 高光遮罩：在高亮度区域为1，低亮度区域为0
    float highlightMask = smoothstep(0.6, 1.0, luma);
    highlightMask = pow(highlightMask, 1.5); // 增强对比度

    // 中间调遮罩：改进算法，更好的中间调检测
    float midtoneMask;
    if (luma >= 0.2 && luma <= 0.8) {
        // 在0.2-0.8范围内计算中间调遮罩
        float normalizedLuma = (luma - 0.2) / 0.6; // 归一化到0-1
        midtoneMask = 1.0 - abs(normalizedLuma - 0.5) * 2.0;
        midtoneMask = smoothstep(0.0, 1.0, midtoneMask);
        midtoneMask = pow(midtoneMask, 0.7); // 柔化过渡
    } else {
        midtoneMask = 0.0;
    }

    // 确保遮罩总和不超过1.0（避免重叠）
    float totalMask = shadowMask + midtoneMask + highlightMask;
    if (totalMask > 1.0) {
        shadowMask /= totalMask;
        midtoneMask /= totalMask;
        highlightMask /= totalMask;
    }

    return float3(shadowMask, midtoneMask, highlightMask);
}

/// 色相偏移函数 - 在HSV空间中进行精确的色相调整
float3 applyHueShift(float3 color, float hueShift) {
    if (abs(hueShift) < 0.001) {
        return color;
    }

    // 转换到HSV空间
    float3 hsv = rgb_to_hsv(color);

    // 应用色相偏移（度数转换为0-1范围）
    float hueOffset = hueShift / 360.0;
    hsv.x = fmod(hsv.x + hueOffset + 1.0, 1.0); // 确保在0-1范围内循环

    // 转换回RGB空间
    return hsv_to_rgb(hsv);
}

/// 专业HSL颜色权重计算 - 基于您提供的实现
float calculateColorWeight(float hue, int colorIndex) {
    // 8种颜色的中心角度和范围
    float colorCenters[8] = {
        0.0,   // 红色
        30.0,  // 橙色
        60.0,  // 黄色
        120.0, // 绿色
        180.0, // 青色
        240.0, // 蓝色
        270.0, // 紫色
        300.0  // 洋红
    };

    float range = 60.0; // 每种颜色覆盖60度范围

    if (colorIndex < 0 || colorIndex >= 8) {
        return 0.0;
    }

    float center = colorCenters[colorIndex];

    // 计算色相距离（考虑色环的循环性）
    float distance = abs(hue - center);
    if (distance > 180.0) {
        distance = 360.0 - distance;
    }

    // 如果在颜色范围内，计算权重
    if (distance <= range / 2.0) {
        // 使用余弦函数创建平滑的权重过渡
        float normalizedDistance = distance / (range / 2.0);
        return cos(normalizedDistance * M_PI_F / 2.0);
    }

    return 0.0;
}

/// 专业HSL选择性调整 - 基于您提供的实现
float3 applySelectiveHSLAdjustment(float3 color, float hueShift, float saturationAdjust, float luminanceAdjust,
                                  int selectedColorIndex, float rangeSoftness) {

    // RGB转HSL
    float3 hsl = rgb_to_hsl(color);

    // 计算颜色权重
    float weight = calculateColorWeight(hsl.x, selectedColorIndex);

    if (weight > 0.0) {
        // 应用HSL调整
        float adjustedH = hsl.x;
        float adjustedS = hsl.y;
        float adjustedL = hsl.z;

        // 应用色相调节
        adjustedH += hueShift * weight;

        // 应用饱和度调节 (saturationAdjust是缩放因子，如1.5表示增加50%)
        adjustedS *= (1.0 + (saturationAdjust - 1.0) * weight);

        // 应用明度调节
        adjustedL += luminanceAdjust * weight;

        // 确保值在有效范围内
        adjustedH = fmod(adjustedH + 360.0, 360.0);
        adjustedS = max(0.0, min(100.0, adjustedS));
        adjustedL = max(0.0, min(100.0, adjustedL));

        // HSL转RGB
        return hsl_to_rgb(float3(adjustedH, adjustedS, adjustedL));
    }

    return color;
}

/// 饱和度调整函数 - 保持亮度的饱和度调整
float3 applySaturationAdjustment(float3 color, float saturationBoost) {
    if (abs(saturationBoost) < 0.001) {
        return color;
    }

    // 计算原始亮度
    float originalLuma = luminance(color);

    // 转换到HSV空间进行饱和度调整
    float3 hsv = rgb_to_hsv(color);

    // 应用饱和度调整
    hsv.y = clamp(hsv.y * (1.0 + saturationBoost), 0.0, 1.0);

    // 转换回RGB
    float3 result = hsv_to_rgb(hsv);

    // 保持原始亮度
    float newLuma = luminance(result);
    if (newLuma > 0.001) {
        result *= originalLuma / newLuma;
    }

    return clamp(result, 0.0, 1.0);
}

/// 色调曲线调整 - 基于亮度的专业级色调偏移
float3 applyToneCurve(float3 color, float shadowTone, float midtoneTone, float highlightTone) {
    if (abs(shadowTone) < 0.001 && abs(midtoneTone) < 0.001 && abs(highlightTone) < 0.001) {
        return color;
    }

    // 计算色调遮罩
    float3 masks = calculateToneMasks(color);
    float shadowMask = masks.x;
    float midtoneMask = masks.y;
    float highlightMask = masks.z;

    // 应用色调调整 - 使用更强的效果
    float3 result = color;

    // 阴影色调调整 - 增强效果强度
    if (abs(shadowTone) > 0.001) {
        // 使用指数曲线增强效果
        float adjustment = shadowTone * shadowMask;
        if (shadowTone > 0.0) {
            // 提亮阴影：使用幂函数
            result = mix(result, pow(result, 1.0 - adjustment * 0.5), shadowMask);
        } else {
            // 压暗阴影：使用幂函数
            result = mix(result, pow(result, 1.0 + abs(adjustment) * 0.5), shadowMask);
        }
    }

    // 中间调色调调整 - 增强效果
    if (abs(midtoneTone) > 0.001) {
        float adjustment = midtoneTone * midtoneMask;
        if (midtoneTone > 0.0) {
            // 提亮中间调
            result = mix(result, result + adjustment * 0.3, midtoneMask);
        } else {
            // 压暗中间调
            result = mix(result, result * (1.0 + adjustment * 0.3), midtoneMask);
        }
    }

    // 高光色调调整 - 增强效果
    if (abs(highlightTone) > 0.001) {
        float adjustment = highlightTone * highlightMask;
        if (highlightTone > 0.0) {
            // 提亮高光：使用S曲线
            result = mix(result, result + adjustment * 0.4, highlightMask);
        } else {
            // 压暗高光：使用反S曲线
            result = mix(result, result * (1.0 + adjustment * 0.4), highlightMask);
        }
    }

    return clamp(result, 0.0, 1.0);
}

/// 专业色调分离函数 - 基于Lightroom的Split Toning算法（调试版本）
float3 applySplitToning(float3 color,
                       float highlightHue, float highlightSaturation,
                       float shadowHue, float shadowSaturation,
                       float balance) {

    // 调试：检查参数是否传递正确
    // 如果所有参数都是0，在图像左上角显示绿色标记
    if (abs(highlightHue) < 0.001 && abs(highlightSaturation) < 0.001 &&
        abs(shadowHue) < 0.001 && abs(shadowSaturation) < 0.001 &&
        abs(balance) < 0.001) {
        // 所有参数为0时，不做任何处理
        return color;
    }

    // 调试：如果有任何非零参数，在图像右上角显示红色标记
    // 这可以帮助我们确认参数是否正确传递

    // 早期退出检查
    const float threshold = 0.001;
    bool hasHighlightToning = (abs(highlightHue) >= threshold || abs(highlightSaturation) >= threshold);
    bool hasShadowToning = (abs(shadowHue) >= threshold || abs(shadowSaturation) >= threshold);

    if (!hasHighlightToning && !hasShadowToning) {
        return color;
    }

    // 计算亮度用于区域分离
    float luma = luminance(color);

    // 根据平衡参数计算动态分界点
    // balance: -1.0 = 降低分界点(扩大阴影范围), 0.0 = 标准分界点, +1.0 = 提高分界点(扩大高光范围)
    float balanceAdjustment = balance * 0.3; // 限制调整范围，避免极端值
    float splitPoint = 0.5 + balanceAdjustment; // 基础分界点0.5 + 平衡调整
    splitPoint = clamp(splitPoint, 0.1, 0.9); // 防止极端分界点

    // 基于动态分界点计算阴影和高光遮罩
    float shadowMask = 1.0 - smoothstep(0.0, splitPoint, luma);
    float highlightMask = smoothstep(splitPoint, 1.0, luma);

    // 增强遮罩对比度，使过渡更自然
    shadowMask = pow(shadowMask, 1.2);
    highlightMask = pow(highlightMask, 1.2);

    // 增强遮罩对比度
    shadowMask = pow(shadowMask, 1.2);
    highlightMask = pow(highlightMask, 1.2);

    float3 result = color;

    // 应用阴影色调分离
    if (hasShadowToning) {
        // 创建阴影色调
        float3 shadowTint = float3(1.0);

        if (abs(shadowSaturation) > threshold) {
            // 使用HSV转RGB创建纯色调
            float3 hsv = float3(shadowHue / 360.0, 1.0, 1.0); // 纯色相，最大饱和度和亮度
            float3 hueColor = hsv_to_rgb(hsv);

            // 根据饱和度强度混合色调
            shadowTint = mix(float3(1.0), hueColor, shadowSaturation);
        }

        // 应用阴影色调到图像 - 考虑平衡权重
        float shadowStrength = shadowMask * 0.6; // 基础强度
        result = mix(result, result * shadowTint, shadowStrength);
    }

    // 应用高光色调分离
    if (hasHighlightToning) {
        // 创建高光色调
        float3 highlightTint = float3(1.0);

        if (abs(highlightSaturation) > threshold) {
            // 使用HSV转RGB创建纯色调
            float3 hsv = float3(highlightHue / 360.0, 1.0, 1.0); // 纯色相，最大饱和度和亮度
            float3 hueColor = hsv_to_rgb(hsv);

            // 根据饱和度强度混合色调
            highlightTint = mix(float3(1.0), hueColor, highlightSaturation);
        }

        // 应用高光色调到图像 - 考虑平衡权重
        float highlightStrength = highlightMask * 0.6; // 基础强度
        result = mix(result, result * highlightTint, highlightStrength);
    }

    return clamp(result, 0.0, 1.0);
}

// 注释：移除了sRGB/线性空间转换函数
// 因为我们已经在Metal纹理加载时明确指定了sRGB色彩空间
// 所有处理都在sRGB空间中进行，避免不必要的色彩空间转换



// MARK: - Kernel函数

/// iPhone系统相机风格滤镜 - 使用Apple标准算法
kernel void lightroom_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                             texture2d<float, access::write> outputTexture [[texture(1)]],
                             constant FilterParameters &params [[buffer(0)]],
                             uint2 gid [[thread_position_in_grid]]) {

    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    float4 color = inputTexture.read(gid);

    // 按照iPhone系统相机的处理顺序和算法

    // 1. 曝光调整 - 业界标准曝光算法 + exp2()性能优化
    if (params.exposure != 0.0) {
        // 使用业界标准曝光算法：color.rgb *= pow(2.0, params.exposure) + exp2()性能优化
        color.rgb = apply_simple_exposure(color.rgb, params.exposure);

        // 应用高光保护（在曝光后立即处理，防止高光溢出）
        if (params.highlightProtectionIntensity > 0.0) {
            color.rgb = apply_highlight_protection(color.rgb, params.highlightProtectionIntensity, params.highlightProtectionMode);
        }
    }

    // 2. 色温色调调整 - 亮度保持版本
    if (params.temperature != 0.0 || params.tint != 0.0) {
        color.rgb = apply_luminance_preserving_temperature_tint(color.rgb, params.temperature, params.tint);
    }

    // 4. 高光阴影调整 - 使用iPhone的智能算法
    if (params.highlights != 0.0 || params.shadows != 0.0) {
        float luma = luminance(color.rgb);

        // iPhone的高光阴影算法 - 更平滑的过渡
        float highlight_mask = smoothstep(0.3, 0.9, luma);
        float shadow_mask = smoothstep(0.7, 0.1, luma);

        // 高光压制
        if (params.highlights != 0.0) {
            float highlight_adjust = 1.0 + params.highlights * highlight_mask * 0.6;
            color.rgb *= highlight_adjust;
        }

        // 阴影提亮
        if (params.shadows != 0.0) {
            float shadow_adjust = 1.0 + params.shadows * shadow_mask * 0.6;
            color.rgb *= shadow_adjust;
        }
    }

    // 4.5. 白色黑色调整 - 色阶调整算法（修正方向）
    if (params.whites != 0.0 || params.blacks != 0.0) {
        // 修正算法方向：正值应该提亮对应区域
        float white_point = 1.0 - params.whites * 0.3; // whites正值时降低白点，让高光更亮
        float black_point = -params.blacks * 0.1; // blacks正值时降低黑点，让阴影更亮

        // 应用色阶调整 - 类似Photoshop的Levels调整
        color.rgb = (color.rgb - black_point) / (white_point - black_point);
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 4.6. 去雾调整 - 增强对比度和饱和度
    if (params.dehaze != 0.0) {
        float luma = luminance(color.rgb);

        // 去雾算法：增强中间调对比度，提升饱和度
        float dehaze_strength = params.dehaze * 0.5; // 控制强度

        // 对比度增强 - 针对中间调
        float contrast_factor = 1.0 + dehaze_strength * (1.0 - abs(luma - 0.5) * 2.0);
        color.rgb = mix(float3(luma), color.rgb, contrast_factor);

        // 饱和度增强 - 去雾时增加色彩鲜艳度
        if (dehaze_strength > 0.0) {
            color.rgb = mix(float3(luma), color.rgb, 1.0 + dehaze_strength * 0.3);
        }

        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 5. 亮度调整 - iPhone使用的是加法模型，不是乘法
    if (params.brightness != 0.0) {
        // iPhone系统相机的亮度调整是线性加法
        color.rgb += params.brightness * 0.5;
    }

    // 6. 对比度调整 - 优化的专业级S曲线算法
    if (params.contrast != 0.0) {
        // 确保在有效范围内
        color.rgb = clamp(color.rgb, 0.0, 1.0);

        // 使用优化的专业级对比度算法
        // 结合了Sigmoid和三次多项式的优点
        for (int i = 0; i < 3; i++) {
            float x = color[i];

            // 优化的强度映射 - 提供更明显的效果
            float strength = params.contrast * 1.2; // 增强效果强度

            // 专业级S曲线算法 - 基于改进的三次多项式
            // 提供更好的细节保留和视觉效果
            if (abs(strength) < 0.001) {
                // 强度接近0时直接跳过，避免不必要的计算
                continue;
            }

            // 使用改进的分段三次多项式S曲线
            float result;
            if (x < 0.5) {
                // 暗部：增强的三次多项式，更好的阴影细节
                float t = x * 2.0; // 映射到0-1
                float cubic = t * t * t;
                float linear = t;
                result = (cubic * strength + linear * (1.0 - strength)) * 0.5;
            } else {
                // 亮部：对称处理，保持平滑过渡
                float t = (1.0 - x) * 2.0; // 映射到0-1
                float cubic = t * t * t;
                float linear = t;
                result = 1.0 - (cubic * strength + linear * (1.0 - strength)) * 0.5;
            }

            color[i] = result;
        }

        // 最终确保结果在有效范围内
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 7. 饱和度调整 - iPhone的饱和度算法
    if (params.saturation != 0.0) {
        float luma = luminance(color.rgb);
        // iPhone使用的饱和度公式
        color.rgb = mix(float3(luma), color.rgb, 1.0 + params.saturation);
    }

    // 8. 自然饱和度 - iPhone的Vibrance算法
    if (params.vibrance != 0.0) {
        float luma = luminance(color.rgb);
        float maxChannel = max(max(color.r, color.g), color.b);
        float saturation = (maxChannel - luma) / (maxChannel + 0.001);

        // iPhone的自然饱和度算法 - 保护已经饱和的颜色
        float protection = 1.0 - saturation;
        float adjustment = params.vibrance * protection;

        color.rgb = mix(float3(luma), color.rgb, 1.0 + adjustment);
    }

    // 8.3. HSL选择性调整 - 专业级颜色范围调整
    color.rgb = applySelectiveHSLAdjustment(color.rgb,
                                           params.hue, params.hslSaturation, params.hslLuminance,
                                           params.selectedHSLColorIndex, params.hslColorRangeSoftness);

    // 8.5. 色调分离 - 专业级高光/阴影色彩调整
    color.rgb = applySplitToning(color.rgb,
                                params.highlightHue, params.highlightSaturation,
                                params.shadowHue, params.shadowSaturation,
                                params.splitToningBalance);

    // 9. 褪色效果 - iPhone风格的褪色处理
    if (params.fadeEffect > 0.0) {
        color.rgb = applyFadeEffect(color.rgb, params.fadeEffect);
    }

    // 10. 黑白效果 - iPhone风格的黑白处理
    if (params.monoEffect > 0.0) {
        color.rgb = applyMonoEffect(color.rgb, params.monoEffect);
    }

    // 11. 细节调整 - 按照色调分离的处理模式

    // 11.1. 锐化处理
    if (params.sharpness != 0.0) {
        color.rgb = applySharpening(color.rgb, params.sharpness);
    }

    // 11.2. 暗角处理
    if (params.vignetteIntensity != 0.0) {
        float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
        color.rgb = applyVignette(color.rgb, texCoord, params.vignetteIntensity, params.vignetteRadius);
    }

    // 11.3. 降噪处理
    if (params.noiseReduction > 0.0) {
        color.rgb = applyNoiseReduction(color.rgb, params.noiseReduction);
    }

    // 最终截断到有效范围
    color.rgb = clamp(color.rgb, 0.0, 1.0);
    outputTexture.write(color, gid);
}

/// VSCO风格滤镜
kernel void vsco_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                       texture2d<float, access::write> outputTexture [[texture(1)]],
                       constant FilterParameters &params [[buffer(0)]],
                       uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // VSCO风格处理 - 胶片特性
    
    // 1. 胶片响应曲线
    float3 film_curve = pow(color.rgb, float3(0.9));

    // 2. 胶片风格色温色调调整 - 亮度保持版本
    if (params.temperature != 0.0 || params.tint != 0.0) {
        film_curve = apply_film_temperature_tint(film_curve, params.temperature, params.tint);
    }

    // 3. 胶片风格色调调整 - 绿-品红平衡，胶片特有的柔和特性
    if (params.tint != 0.0) {
        float tint = params.tint; // -1.0 到 +1.0
        // 胶片风格色调调整 - 比数字滤镜更温和
        film_curve.g += tint * 0.08;  // 减少强度
        film_curve.r -= tint * 0.04;  // 减少强度
        film_curve.b -= tint * 0.02;  // 减少强度
    }

    // 4. 胶片曝光 - 业界标准胶片风格曝光算法 + exp2()性能优化
    if (params.exposure != 0.0) {
        // 使用业界标准曝光算法 + exp2()性能优化，胶片风格使用0.8系数保持柔和特性
        film_curve = apply_simple_exposure(film_curve, params.exposure * 0.8);
    }
    
    // 5. 胶片风格对比度调整 - 专为胶片特性优化
    if (params.contrast != 0.0) {
        film_curve = clamp(film_curve, 0.0, 1.0);

        // 胶片风格对比度 - 模拟真实胶片的响应特性
        // 相比数字对比度，胶片对比度更柔和，保留更多细节
        for (int i = 0; i < 3; i++) {
            float x = film_curve[i];

            // 胶片风格的强度映射 - 比数字滤镜更温和
            float strength = params.contrast * 0.8; // 胶片特有的柔和效果

            // 胶片响应曲线 - 基于真实胶片的非线性特性
            // 使用修正的S曲线，保持胶片的独特质感
            float center = 0.5;
            float deviation = x - center;

            // 胶片特有的渐进式对比度增强
            float film_response;
            if (abs(deviation) < 0.001) {
                film_response = x; // 中性点保持不变
            } else {
                // 使用胶片特有的渐进式曲线
                float curve_factor = 1.0 + strength * (1.0 - abs(deviation * 2.0));
                film_response = center + deviation * curve_factor;
            }

            film_curve[i] = film_response;
        }

        film_curve = clamp(film_curve, 0.0, 1.0);
    }
    
    // 6. 胶片饱和度
    if (params.saturation != 0.0) {
        float3 hsv = rgb_to_hsv(film_curve);
        hsv.y *= (1.0 + params.saturation * 0.8);
        hsv.y = hsv.y / (1.0 + hsv.y * 0.2);
        film_curve = hsv_to_rgb(hsv);
    }

    // 7. 胶片亮度
    if (params.brightness != 0.0) {
        film_curve += params.brightness * 0.8;
    }

    // 8. 胶片自然饱和度
    if (params.vibrance != 0.0) {
        float3 hsv = rgb_to_hsv(film_curve);
        float sat_weight = 1.0 - pow(hsv.y, 0.8);
        hsv.y = hsv.y * (1.0 + params.vibrance * sat_weight * 0.9);
        hsv.y = clamp(hsv.y, 0.0, 1.0);
        film_curve = hsv_to_rgb(hsv);
    }

    // 9. 胶片风格白色黑色调整 - 更柔和的色阶调整（修正方向）
    if (params.whites != 0.0 || params.blacks != 0.0) {
        // 胶片风格的色阶调整 - 比数字滤镜更温和，修正方向
        float white_point = 1.0 - params.whites * 0.2; // 胶片风格：正值降低白点，让高光更亮
        float black_point = -params.blacks * 0.05; // 胶片风格：正值降低黑点，让阴影更亮

        film_curve = (film_curve - black_point) / (white_point - black_point);
        film_curve = clamp(film_curve, 0.0, 1.0);
    }

    // 10. 胶片风格去雾 - 模拟胶片的自然对比度增强
    if (params.dehaze != 0.0) {
        float luma = luminance(film_curve);

        // 胶片风格去雾：更自然的对比度增强
        float dehaze_strength = params.dehaze * 0.3; // 胶片风格：更温和的去雾效果

        // 胶片特有的中间调增强
        float film_contrast = 1.0 + dehaze_strength * (0.5 - abs(luma - 0.5));
        film_curve = mix(float3(luma), film_curve, film_contrast);

        film_curve = clamp(film_curve, 0.0, 1.0);
    }

    // 10.3. 胶片风格HSL选择性调整 - 模拟胶片的颜色特性
    film_curve = applySelectiveHSLAdjustment(film_curve,
                                           params.hue * 0.8, // 胶片风格：稍微减弱色相调整
                                           params.hslSaturation * 0.9, // 胶片风格：稍微减弱饱和度调整
                                           params.hslLuminance * 0.9, // 胶片风格：稍微减弱明度调整
                                           params.selectedHSLColorIndex,
                                           params.hslColorRangeSoftness * 1.2); // 胶片风格：更柔和的过渡

    // 10.5. 胶片风格色调分离 - 模拟胶片的色彩特性
    film_curve = applySplitToning(film_curve,
                                 params.highlightHue, params.highlightSaturation * 0.8, // 胶片风格：稍微减弱强度
                                 params.shadowHue, params.shadowSaturation * 0.8,
                                 params.splitToningBalance * 0.7); // 胶片风格：减弱平衡效果

    film_curve = clamp(film_curve, 0.0, 1.0);
    color.rgb = film_curve;

    // 11. 胶片风格细节调整 - 更温和的处理

    // 11.1. 胶片风格锐化 - 减弱强度保持胶片柔和特性
    if (params.sharpness != 0.0) {
        color.rgb = applySharpening(color.rgb, params.sharpness * 0.7); // 胶片风格：减弱锐化
    }

    // 11.2. 胶片风格暗角 - 更自然的渐变
    if (params.vignetteIntensity != 0.0) {
        float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
        color.rgb = applyVignette(color.rgb, texCoord, params.vignetteIntensity * 0.8, params.vignetteRadius * 1.2); // 胶片风格：更柔和的暗角
    }

    // 11.3. 胶片风格降噪 - 保持胶片颗粒感
    if (params.noiseReduction > 0.0) {
        color.rgb = applyNoiseReduction(color.rgb, params.noiseReduction * 0.6); // 胶片风格：减弱降噪保持颗粒感
    }

    outputTexture.write(color, gid);
}

/// 综合滤镜处理 - 默认使用简单算法
kernel void comprehensive_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                texture2d<float, access::write> outputTexture [[texture(1)]],
                                constant FilterParameters &params [[buffer(0)]],
                                uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 简单的滤镜处理
    
    // 1. 曝光调整 - 统一函数封装
    if (params.exposure != 0.0) {
        color.rgb = applyExposure(color.rgb, params.exposure);
    }

    // 2. 色温调整 - 统一函数封装
    if (params.temperature != 0.0) {
        color.rgb = applyTemperature(color.rgb, params.temperature);
    }

    // 3. 色调调整 - 统一函数封装
    if (params.tint != 0.0) {
        color.rgb = applyTint(color.rgb, params.tint);
    }

    // 4. 亮度调整 - 统一函数封装
    if (params.brightness != 0.0) {
        color.rgb = applyBrightness(color.rgb, params.brightness);
    }

    // 5. 对比度调整 - 统一函数封装
    if (params.contrast != 0.0) {
        color.rgb = applyContrast(color.rgb, params.contrast);
    }

    // 6. 饱和度调整 - 统一函数封装
    if (params.saturation != 0.0) {
        color.rgb = applySaturation(color.rgb, params.saturation);
    }

    // 7. 自然饱和度调整 - 统一函数封装
    if (params.vibrance != 0.0) {
        color.rgb = applyVibrance(color.rgb, params.vibrance);
    }

    // 8. 褪色效果
    if (params.fadeEffect > 0.0) {
        color.rgb = applyFadeEffect(color.rgb, params.fadeEffect);
    }

    // 9. 白色调整 - 统一函数封装
    if (params.whites != 0.0) {
        color.rgb = applyWhites(color.rgb, params.whites);
    }

    // 10. 黑色调整 - 统一函数封装
    if (params.blacks != 0.0) {
        color.rgb = applyBlacks(color.rgb, params.blacks);
    }

    // 11. 去雾调整 - 统一函数封装
    if (params.dehaze != 0.0) {
        color.rgb = applyDehaze(color.rgb, params.dehaze);
    }

    // 10.3. HSL选择性调整 - 专业级颜色范围调整
    color.rgb = applySelectiveHSLAdjustment(color.rgb,
                                           params.hue, params.hslSaturation, params.hslLuminance,
                                           params.selectedHSLColorIndex, params.hslColorRangeSoftness);

    // 10.5. 色调分离 - 专业级高光/阴影色彩调整
    color.rgb = applySplitToning(color.rgb,
                                params.highlightHue, params.highlightSaturation,
                                params.shadowHue, params.shadowSaturation,
                                params.splitToningBalance);

    // 11. 黑白效果
    if (params.monoEffect > 0.0) {
        color.rgb = applyMonoEffect(color.rgb, params.monoEffect);
    }

    // 12. 细节调整 - 标准强度处理

    // 12.1. 锐化处理
    if (params.sharpness != 0.0) {
        color.rgb = applySharpening(color.rgb, params.sharpness);
    }

    // 12.2. 清晰度处理
    if (params.clarity != 0.0) {
        color.rgb = applyClarity(color.rgb, params.clarity);
    }

    // 12.3. 暗角处理
    if (params.vignetteIntensity != 0.0) {
        float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
        color.rgb = applyVignette(color.rgb, texCoord, params.vignetteIntensity, params.vignetteRadius);
    }

    // 12.4. 降噪处理
    if (params.noiseReduction > 0.0) {
        color.rgb = applyNoiseReduction(color.rgb, params.noiseReduction);
    }

    color.rgb = clamp(color.rgb, 0.0, 1.0);
    outputTexture.write(color, gid);
}

/// 第3步添加：支持曲线的综合滤镜处理
kernel void comprehensive_filter_with_curves(texture2d<float, access::read> inputTexture [[texture(0)]],
                                            texture2d<float, access::write> outputTexture [[texture(1)]],
                                            texture1d<float> curveLUTTexture [[texture(2)]],
                                            constant FilterParameters &params [[buffer(0)]],
                                            sampler curveSampler [[sampler(0)]],
                                            uint2 gid [[thread_position_in_grid]]) {

    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    float4 color = inputTexture.read(gid);

    // 第一阶段：基础调整（与原版相同）

    // 1. 曝光调整
    if (params.exposure != 0.0) {
        color.rgb = apply_simple_exposure(color.rgb, params.exposure);
    }

    // 2. 色温色调调整
    if (params.temperature != 0.0 || params.tint != 0.0) {
        color.rgb = apply_lab_temperature_tint_srgb(color.rgb, params.temperature, params.tint);
    }

    // 3. 亮度调整
    if (params.brightness != 0.0) {
        float brightnessMultiplier = 1.0 + params.brightness;
        color.rgb *= brightnessMultiplier;
    }

    // 4. 对比度调整
    if (params.contrast != 0.0) {
        color.rgb = clamp(color.rgb, 0.0, 1.0);
        for (int i = 0; i < 3; i++) {
            float x = color[i];
            float strength = params.contrast * 1.0;
            float center = 0.5;
            float deviation = x - center;
            float contrast_factor = 1.0 + strength;
            float result = center + deviation * contrast_factor;

            if (result > 1.0) {
                result = 1.0 - 0.1 * (result - 1.0) / (1.0 + (result - 1.0));
            } else if (result < 0.0) {
                result = 0.1 * result / (1.0 - result);
            }
            color[i] = result;
        }
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 第二阶段：第3步新增 - 曲线调整
    if (params.curveIntensity > 0.0) {
        color.rgb = applyCurveToColor(color.rgb, curveLUTTexture, curveSampler, params.curveIntensity);
    }

    // 第三阶段：后续调整（与原版相同）

    // 5. 饱和度调整
    if (params.saturation != 0.0) {
        float3 clampedColor = clamp(color.rgb, 0.0, 1.0);
        float luma = luminance(clampedColor);
        color.rgb = mix(float3(luma), color.rgb, 1.0 + params.saturation);
    }

    // 6. 自然饱和度调整
    if (params.vibrance != 0.0) {
        float3 clampedColor = clamp(color.rgb, 0.0, 1.0);
        float3 hsv = rgb_to_hsv(clampedColor);
        float satWeight = 1.0 - hsv.y;
        hsv.y = hsv.y * (1.0 + params.vibrance * satWeight);
        hsv.y = clamp(hsv.y, 0.0, 1.0);
        color.rgb = hsv_to_rgb(hsv);
    }

    // 7. 其他效果
    if (params.fadeEffect > 0.0) {
        color.rgb = applyFadeEffect(color.rgb, params.fadeEffect);
    }

    if (params.whites != 0.0 || params.blacks != 0.0) {
        float white_point = 1.0 - params.whites * 0.3;
        float black_point = -params.blacks * 0.1;
        color.rgb = (color.rgb - black_point) / (white_point - black_point);
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    if (params.dehaze != 0.0) {
        float luma = luminance(color.rgb);
        float dehaze_strength = params.dehaze * 0.5;
        float contrast_factor = 1.0 + dehaze_strength * (1.0 - abs(luma - 0.5) * 2.0);
        color.rgb = mix(float3(luma), color.rgb, contrast_factor);
        if (dehaze_strength > 0.0) {
            color.rgb = mix(float3(luma), color.rgb, 1.0 + dehaze_strength * 0.3);
        }
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 7.5. HSL选择性调整 - 专业级颜色范围调整
    color.rgb = applySelectiveHSLAdjustment(color.rgb,
                                           params.hue, params.hslSaturation, params.hslLuminance,
                                           params.selectedHSLColorIndex, params.hslColorRangeSoftness);

    if (params.monoEffect > 0.0) {
        color.rgb = applyMonoEffect(color.rgb, params.monoEffect);
    }

    // 8. 细节调整 - 曲线后处理

    // 8.1. 锐化处理
    if (params.sharpness != 0.0) {
        color.rgb = applySharpening(color.rgb, params.sharpness);
    }

    // 8.2. 暗角处理
    if (params.vignetteIntensity != 0.0) {
        float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
        color.rgb = applyVignette(color.rgb, texCoord, params.vignetteIntensity, params.vignetteRadius);
    }

    // 8.3. 降噪处理
    if (params.noiseReduction > 0.0) {
        color.rgb = applyNoiseReduction(color.rgb, params.noiseReduction);
    }

    color.rgb = clamp(color.rgb, 0.0, 1.0);
    outputTexture.write(color, gid);
}

/// 多通道曲线处理的综合滤镜 - 支持RGB + 红绿蓝分离通道
kernel void comprehensive_filter_with_multichannel_curves(
    texture2d<float, access::read> inputTexture [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    texture1d<float> rgbLUTTexture [[texture(2)]],
    texture1d<float> redLUTTexture [[texture(3)]],
    texture1d<float> greenLUTTexture [[texture(4)]],
    texture1d<float> blueLUTTexture [[texture(5)]],
    constant FilterParameters &params [[buffer(0)]],
    sampler curveSampler [[sampler(0)]],
    uint2 gid [[thread_position_in_grid]]
) {

    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    float4 color = inputTexture.read(gid);

    // 第一阶段：基础调整（与原版相同）

    // 1. 曝光调整
    if (params.exposure != 0.0) {
        color.rgb = apply_simple_exposure(color.rgb, params.exposure);
    }

    // 2. 色温色调调整
    if (params.temperature != 0.0 || params.tint != 0.0) {
        color.rgb = apply_lab_temperature_tint_srgb(color.rgb, params.temperature, params.tint);
    }

    // 3. 亮度调整
    if (params.brightness != 0.0) {
        float brightnessMultiplier = 1.0 + params.brightness;
        color.rgb *= brightnessMultiplier;
    }

    // 4. 对比度调整
    if (params.contrast != 0.0) {
        color.rgb = clamp(color.rgb, 0.0, 1.0);
        for (int i = 0; i < 3; i++) {
            float x = color[i];
            float strength = params.contrast * 1.0;
            float center = 0.5;
            float deviation = x - center;
            float contrast_factor = 1.0 + strength;
            float result = center + deviation * contrast_factor;

            if (result > 1.0) {
                result = 1.0 - 0.1 * (result - 1.0) / (1.0 + (result - 1.0));
            } else if (result < 0.0) {
                result = 0.1 * result / (1.0 - result);
            }
            color[i] = result;
        }
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    // 第二阶段：多通道曲线调整
    if (params.curveIntensity > 0.0) {
        // 第一步：应用RGB曲线
        float3 rgbAdjusted;
        rgbAdjusted.r = sampleCurveLUT(color.r, rgbLUTTexture, curveSampler);
        rgbAdjusted.g = sampleCurveLUT(color.g, rgbLUTTexture, curveSampler);
        rgbAdjusted.b = sampleCurveLUT(color.b, rgbLUTTexture, curveSampler);

        // 根据强度混合RGB曲线结果
        color.rgb = mix(color.rgb, rgbAdjusted, params.curveIntensity);

        // 第二步：应用分离通道曲线
        float3 channelAdjusted = color.rgb;

        // 红色通道曲线
        float redResult = sampleCurveLUT(color.r, redLUTTexture, curveSampler);
        channelAdjusted.r = mix(color.r, redResult, params.curveIntensity);

        // 绿色通道曲线
        float greenResult = sampleCurveLUT(color.g, greenLUTTexture, curveSampler);
        channelAdjusted.g = mix(color.g, greenResult, params.curveIntensity);

        // 蓝色通道曲线
        float blueResult = sampleCurveLUT(color.b, blueLUTTexture, curveSampler);
        channelAdjusted.b = mix(color.b, blueResult, params.curveIntensity);

        color.rgb = channelAdjusted;
    }

    // 第三阶段：后续调整（与原版相同）

    // 5. 饱和度调整
    if (params.saturation != 0.0) {
        float3 clampedColor = clamp(color.rgb, 0.0, 1.0);
        float luma = luminance(clampedColor);
        color.rgb = mix(float3(luma), color.rgb, 1.0 + params.saturation);
    }

    // 6. 自然饱和度调整
    if (params.vibrance != 0.0) {
        float3 clampedColor = clamp(color.rgb, 0.0, 1.0);
        float3 hsv = rgb_to_hsv(clampedColor);
        float satWeight = 1.0 - hsv.y;
        hsv.y = hsv.y * (1.0 + params.vibrance * satWeight);
        hsv.y = clamp(hsv.y, 0.0, 1.0);
        color.rgb = hsv_to_rgb(hsv);
    }

    // 6.3. HSL选择性调整 - 专业级颜色范围调整（曲线后处理）
    color.rgb = applySelectiveHSLAdjustment(color.rgb,
                                           params.hue, params.hslSaturation, params.hslLuminance,
                                           params.selectedHSLColorIndex, params.hslColorRangeSoftness);

    // 6.5. 色调分离 - 专业级高光/阴影色彩调整（曲线后处理）
    color.rgb = applySplitToning(color.rgb,
                                params.highlightHue, params.highlightSaturation,
                                params.shadowHue, params.shadowSaturation,
                                params.splitToningBalance);

    // 7. 其他效果
    if (params.fadeEffect > 0.0) {
        color.rgb = applyFadeEffect(color.rgb, params.fadeEffect);
    }

    if (params.whites != 0.0 || params.blacks != 0.0) {
        float white_point = 1.0 - params.whites * 0.3;
        float black_point = -params.blacks * 0.1;
        color.rgb = (color.rgb - black_point) / (white_point - black_point);
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    if (params.dehaze != 0.0) {
        float luma = luminance(color.rgb);
        float dehaze_strength = params.dehaze * 0.5;
        float contrast_factor = 1.0 + dehaze_strength * (1.0 - abs(luma - 0.5) * 2.0);
        color.rgb = mix(float3(luma), color.rgb, contrast_factor);
        if (dehaze_strength > 0.0) {
            color.rgb = mix(float3(luma), color.rgb, 1.0 + dehaze_strength * 0.3);
        }
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }

    if (params.monoEffect > 0.0) {
        color.rgb = applyMonoEffect(color.rgb, params.monoEffect);
    }

    // 8. 细节调整 - 多通道曲线后处理

    // 8.1. 锐化处理
    if (params.sharpness != 0.0) {
        color.rgb = applySharpening(color.rgb, params.sharpness);
    }

    // 8.2. 暗角处理
    if (params.vignetteIntensity != 0.0) {
        float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
        color.rgb = applyVignette(color.rgb, texCoord, params.vignetteIntensity, params.vignetteRadius);
    }

    // 8.3. 降噪处理
    if (params.noiseReduction > 0.0) {
        color.rgb = applyNoiseReduction(color.rgb, params.noiseReduction);
    }

    color.rgb = clamp(color.rgb, 0.0, 1.0);
    outputTexture.write(color, gid);
}
