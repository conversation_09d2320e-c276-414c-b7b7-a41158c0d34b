#include <metal_stdlib>
using namespace metal;

/// 特殊效果参数结构体
struct SpecialEffectsParameters {
    float intensity;        // 效果强度 (0.0-1.0)
    float2 position;        // 位置偏移
    float rotation;         // 旋转角度
    float scale;           // 缩放比例
    float contrast;        // 对比度 (用于颗粒)
    float size;           // 尺寸 (用于颗粒和划痕)
    float density;        // 密度
    float padding;        // 内存对齐
};

/// 随机数生成函数
float random(float2 st) {
    return fract(sin(dot(st.xy, float2(12.9898, 78.233))) * 43758.5453123);
}

/// 2D噪声函数
float noise(float2 st) {
    float2 i = floor(st);
    float2 f = fract(st);
    
    float a = random(i);
    float b = random(i + float2(1.0, 0.0));
    float c = random(i + float2(0.0, 1.0));
    float d = random(i + float2(1.0, 1.0));
    
    float2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
}

/// 分形噪声
float fractalNoise(float2 st, int octaves) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for (int i = 0; i < octaves; i++) {
        value += amplitude * noise(st * frequency);
        amplitude *= 0.5;
        frequency *= 2.0;
    }
    
    return value;
}

/// 漏光效果着色器
kernel void apply_light_leak_effect(texture2d<float, access::read> inputTexture [[texture(0)]],
                                   texture2d<float, access::write> outputTexture [[texture(1)]],
                                   texture2d<float, access::sample> leakTexture [[texture(2)]],
                                   constant SpecialEffectsParameters& params [[buffer(0)]],
                                   uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取原始像素
    float4 originalColor = inputTexture.read(gid);
    
    // 计算纹理坐标
    float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
    
    // 应用变换到漏光纹理坐标
    float2 center = float2(0.5, 0.5);
    float2 transformedCoord = texCoord - center;
    
    // 旋转
    float cosR = cos(params.rotation);
    float sinR = sin(params.rotation);
    float2 rotatedCoord = float2(
        transformedCoord.x * cosR - transformedCoord.y * sinR,
        transformedCoord.x * sinR + transformedCoord.y * cosR
    );
    
    // 缩放和位置偏移
    rotatedCoord = rotatedCoord / params.scale + center + params.position;
    
    // 采样漏光纹理
    constexpr sampler s(coord::normalized, address::clamp_to_edge, filter::linear);
    float4 leakColor = leakTexture.sample(s, rotatedCoord);
    
    // Screen混合模式
    float3 screenBlend = 1.0 - (1.0 - originalColor.rgb) * (1.0 - leakColor.rgb);
    
    // 根据强度混合
    float3 finalColor = mix(originalColor.rgb, screenBlend, params.intensity * leakColor.a);
    
    outputTexture.write(float4(finalColor, originalColor.a), gid);
}

/// 胶片颗粒效果着色器
kernel void apply_grain_effect(texture2d<float, access::read> inputTexture [[texture(0)]],
                              texture2d<float, access::write> outputTexture [[texture(1)]],
                              constant SpecialEffectsParameters& params [[buffer(0)]],
                              uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取原始像素
    float4 originalColor = inputTexture.read(gid);
    
    // 计算纹理坐标
    float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
    
    // 根据尺寸参数调整噪声频率
    float2 noiseCoord = texCoord * (1.0 / params.size) * 100.0;
    
    // 生成多层噪声
    float grain = fractalNoise(noiseCoord, 3);
    
    // 调整对比度
    grain = (grain - 0.5) * params.contrast + 0.5;
    grain = clamp(grain, 0.0, 1.0);
    
    // 转换为单色噪声
    float3 grainColor = float3(grain);
    
    // Overlay混合模式
    float3 overlayBlend;
    for (int i = 0; i < 3; i++) {
        if (originalColor[i] < 0.5) {
            overlayBlend[i] = 2.0 * originalColor[i] * grainColor[i];
        } else {
            overlayBlend[i] = 1.0 - 2.0 * (1.0 - originalColor[i]) * (1.0 - grainColor[i]);
        }
    }
    
    // 根据强度混合
    float3 finalColor = mix(originalColor.rgb, overlayBlend, params.intensity);
    
    outputTexture.write(float4(finalColor, originalColor.a), gid);
}

/// 划痕效果着色器
kernel void apply_scratch_effect(texture2d<float, access::read> inputTexture [[texture(0)]],
                                texture2d<float, access::write> outputTexture [[texture(1)]],
                                constant SpecialEffectsParameters& params [[buffer(0)]],
                                uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取原始像素
    float4 originalColor = inputTexture.read(gid);
    
    // 计算纹理坐标
    float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
    
    // 生成垂直划痕
    float scratchNoise = 0.0;
    
    // 多个垂直划痕
    for (int i = 0; i < 5; i++) {
        float x = float(i) * 0.2 + 0.1;
        float scratchWidth = params.size * 0.001;
        float scratchIntensity = params.density;
        
        // 垂直划痕
        float dist = abs(texCoord.x - x);
        if (dist < scratchWidth) {
            float scratchValue = (1.0 - dist / scratchWidth) * scratchIntensity;
            scratchValue *= random(float2(x, floor(texCoord.y * 100.0)));
            scratchNoise = max(scratchNoise, scratchValue);
        }
    }
    
    // 随机水平划痕
    float horizontalScratch = 0.0;
    float yPos = floor(texCoord.y * 200.0) / 200.0;
    if (random(float2(yPos, 0.5)) > 0.98) {
        horizontalScratch = params.density * 0.5;
    }
    
    scratchNoise = max(scratchNoise, horizontalScratch);
    
    // Screen混合模式
    float3 scratchColor = float3(scratchNoise);
    float3 screenBlend = 1.0 - (1.0 - originalColor.rgb) * (1.0 - scratchColor);
    
    // 根据强度混合
    float3 finalColor = mix(originalColor.rgb, screenBlend, params.intensity);
    
    outputTexture.write(float4(finalColor, originalColor.a), gid);
}

/// 高斯模糊着色器 (用于水印背景)
kernel void apply_gaussian_blur(texture2d<float, access::read> inputTexture [[texture(0)]],
                               texture2d<float, access::write> outputTexture [[texture(1)]],
                               constant float& blurRadius [[buffer(0)]],
                               uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float2 texelSize = 1.0 / float2(inputTexture.get_width(), inputTexture.get_height());
    float3 color = float3(0.0);
    float totalWeight = 0.0;
    
    int radius = int(ceil(blurRadius));
    
    // 高斯模糊采样
    for (int x = -radius; x <= radius; x++) {
        for (int y = -radius; y <= radius; y++) {
            float2 offset = float2(x, y) * texelSize;
            uint2 samplePos = uint2(float2(gid) + float2(x, y));
            
            // 边界检查
            if (samplePos.x < inputTexture.get_width() && samplePos.y < inputTexture.get_height()) {
                float distance = length(float2(x, y));
                float weight = exp(-distance * distance / (2.0 * blurRadius * blurRadius));
                
                float4 sampleColor = inputTexture.read(samplePos);
                color += sampleColor.rgb * weight;
                totalWeight += weight;
            }
        }
    }
    
    color /= totalWeight;
    float4 originalColor = inputTexture.read(gid);
    
    outputTexture.write(float4(color, originalColor.a), gid);
}

/// 溶解过渡效果着色器
kernel void apply_dissolve_transition(texture2d<float, access::read> fromTexture [[texture(0)]],
                                     texture2d<float, access::read> toTexture [[texture(1)]],
                                     texture2d<float, access::write> outputTexture [[texture(2)]],
                                     constant float& progress [[buffer(0)]],
                                     uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取两个纹理的像素
    float4 fromColor = fromTexture.read(gid);
    float4 toColor = toTexture.read(gid);
    
    // 计算纹理坐标用于噪声
    float2 texCoord = float2(gid) / float2(outputTexture.get_width(), outputTexture.get_height());
    
    // 生成溶解噪声
    float dissolveNoise = random(texCoord);
    
    // 根据进度和噪声决定显示哪个纹理
    float threshold = progress;
    float4 finalColor = (dissolveNoise < threshold) ? toColor : fromColor;
    
    // 在边界处添加轻微的混合
    float edge = 0.05;
    if (abs(dissolveNoise - threshold) < edge) {
        float blend = (edge - abs(dissolveNoise - threshold)) / edge;
        finalColor = mix(finalColor, mix(fromColor, toColor, progress), blend);
    }
    
    outputTexture.write(finalColor, gid);
}
