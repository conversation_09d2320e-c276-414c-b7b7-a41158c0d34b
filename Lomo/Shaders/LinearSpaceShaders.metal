#include <metal_stdlib>
using namespace metal;

// MARK: - 线性空间滤镜参数结构体

struct LinearFilterParameters {
    float exposure;
    float contrast;
    float brightness;
    float saturation;
    float vibrance;
    float temperature;
    float tint;
    float highlights;
    float shadows;
    float whites;
    float blacks;
    float clarity;
    float dehaze;
    float gamma;
    float isLinearInput;
};

// MARK: - 线性空间处理函数

/// 线性空间的亮度保持色域映射函数
float3 apply_linear_luminance_preserving_gamut_mapping(float3 color, float target_luminance) {
    // 计算当前线性亮度（使用Rec.709系数）
    float current_luminance = 0.2126 * color.r + 0.7152 * color.g + 0.0722 * color.b;

    // 如果颜色在有效范围内且亮度接近目标，直接返回
    if (all(color >= 0.0) && all(color <= 1.0) && abs(current_luminance - target_luminance) < 0.001) {
        return color;
    }

    // 首先将颜色限制在有效范围内
    float3 clamped_color = clamp(color, 0.0, 1.0);

    // 重新计算限制后的亮度
    float clamped_luminance = 0.2126 * clamped_color.r + 0.7152 * clamped_color.g + 0.0722 * clamped_color.b;

    // 如果限制后亮度为0，返回灰度值
    if (clamped_luminance < 0.001) {
        return float3(target_luminance);
    }

    // 计算亮度缩放因子
    float luminance_scale = target_luminance / clamped_luminance;

    // 应用亮度缩放，但要确保不超出色域
    float3 scaled_color = clamped_color * luminance_scale;

    // 如果缩放后仍超出色域，使用迭代方法调整
    if (any(scaled_color > 1.0)) {
        // 找到最大的通道值
        float max_channel = max(max(scaled_color.r, scaled_color.g), scaled_color.b);

        // 按比例缩小，保持色相
        scaled_color = scaled_color / max_channel;

        // 重新调整亮度，通过增加所有通道的基础值
        float current_lum = 0.2126 * scaled_color.r + 0.7152 * scaled_color.g + 0.0722 * scaled_color.b;
        float lum_diff = target_luminance - current_lum;

        if (lum_diff > 0.0) {
            // 需要增加亮度，均匀提升所有通道
            float boost = min(lum_diff / 3.0, 1.0 - max(max(scaled_color.r, scaled_color.g), scaled_color.b));
            scaled_color += boost;
        }
    }

    return clamp(scaled_color, 0.0, 1.0);
}

/// 线性空间亮度计算 - 使用Rec.709标准
float linear_luminance(float3 color) {
    return dot(color, float3(0.2126, 0.7152, 0.0722));
}

/// 线性空间RGB到XYZ转换
float3 linear_rgb_to_xyz(float3 rgb) {
    // sRGB到XYZ转换矩阵
    float3x3 rgb_to_xyz_matrix = float3x3(
        0.4124564, 0.3575761, 0.1804375,
        0.2126729, 0.7151522, 0.0721750,
        0.0193339, 0.1191920, 0.9503041
    );
    return rgb_to_xyz_matrix * rgb;
}

/// XYZ到线性RGB转换
float3 xyz_to_linear_rgb(float3 xyz) {
    // XYZ到sRGB转换矩阵
    float3x3 xyz_to_rgb_matrix = float3x3(
         3.2404542, -1.5371385, -0.4985314,
        -0.9692660,  1.8760108,  0.0415560,
         0.0556434, -0.2040259,  1.0572252
    );
    return xyz_to_rgb_matrix * xyz;
}

/// Lab转换辅助函数
float lab_f(float t) {
    const float delta = 6.0 / 29.0;
    const float delta3 = delta * delta * delta;
    if (t > delta3) {
        return pow(t, 1.0 / 3.0);
    } else {
        return t / (3.0 * delta * delta) + 4.0 / 29.0;
    }
}

/// Lab逆转换辅助函数
float lab_f_inv(float t) {
    const float delta = 6.0 / 29.0;
    if (t > delta) {
        return t * t * t;
    } else {
        return 3.0 * delta * delta * (t - 4.0 / 29.0);
    }
}

/// XYZ到Lab转换 - 使用D65白点
float3 xyz_to_lab(float3 xyz) {
    // D65白点 (标准日光)
    const float3 white_point = float3(0.95047, 1.00000, 1.08883);

    // 归一化到白点
    xyz = xyz / white_point;

    // 应用Lab转换函数
    float fx = lab_f(xyz.x);
    float fy = lab_f(xyz.y);
    float fz = lab_f(xyz.z);

    // 计算Lab值
    float L = 116.0 * fy - 16.0;
    float a = 500.0 * (fx - fy);
    float b = 200.0 * (fy - fz);

    return float3(L, a, b);
}

/// Lab到XYZ转换 - 使用D65白点
float3 lab_to_xyz(float3 lab) {
    // D65白点
    const float3 white_point = float3(0.95047, 1.00000, 1.08883);

    // 计算中间值
    float fy = (lab.x + 16.0) / 116.0;
    float fx = lab.y / 500.0 + fy;
    float fz = fy - lab.z / 200.0;

    // 应用逆转换函数
    float3 xyz = float3(lab_f_inv(fx), lab_f_inv(fy), lab_f_inv(fz));

    // 乘以白点
    return xyz * white_point;
}

/// 线性空间业界标准曝光算法 - color.rgb *= pow(2.0, params.exposure) + exp2()性能优化
float3 apply_linear_simple_exposure(float3 color, float exposure) {
    if (abs(exposure) < 0.001) {
        return color;
    }

    // 使用业界标准的摄影曝光公式：color.rgb *= pow(2.0, params.exposure)
    // 性能优化：exp2(x) 等价于 pow(2.0, x) 但在GPU上性能更好
    float exposure_factor = exp2(exposure);

    // 直接应用曝光，不做任何复杂处理
    return color * exposure_factor;
}

/// Lab色彩空间色温色调调整 - 线性空间亮度保持版本
float3 apply_lab_temperature_tint(float3 rgb, float temperature, float tint) {
    // 0. 记录原始线性亮度
    float original_luminance = 0.2126 * rgb.r + 0.7152 * rgb.g + 0.0722 * rgb.b;

    // 1. RGB → XYZ → Lab转换
    float3 xyz = linear_rgb_to_xyz(rgb);
    float3 lab = xyz_to_lab(xyz);

    // 2. 保存原始L*，只调整a*和b*
    float original_L = lab.x;

    // 3. a* (绿-红) 和 b* (蓝-黄) 的调整
    const float adjustment_scale = 10.0; // 可根据UI范围调整

    lab.y += tint * adjustment_scale;        // a* 调整 (色调)
    lab.z += temperature * adjustment_scale; // b* 调整 (色温)

    // 4. 恢复原始L*
    lab.x = original_L;

    // 5. Lab → XYZ → RGB转换
    xyz = lab_to_xyz(lab);
    float3 result = xyz_to_linear_rgb(xyz);

    // 6. 线性空间的亮度保持色域映射
    return apply_linear_luminance_preserving_gamut_mapping(result, original_luminance);
}



/// 线性空间色温调整 - 使用色温矩阵 (保留作为备选)
float3 apply_temperature_linear(float3 color, float temperature) {
    // 色温调整在线性空间更准确
    float temp_factor = temperature; // -1.0 到 +1.0

    // 基于Planckian轨迹的色温调整
    float3x3 temp_matrix;
    if (temp_factor > 0.0) {
        // 暖色调 - 基于3200K到6500K的变化
        temp_matrix = float3x3(
            1.0 + temp_factor * 0.15,  0.0,                      0.0,
            0.0,                       1.0 + temp_factor * 0.05, 0.0,
            0.0,                       0.0,                      1.0 - temp_factor * 0.25
        );
    } else {
        // 冷色调 - 基于6500K到9300K的变化
        temp_matrix = float3x3(
            1.0 + temp_factor * 0.25,  0.0,                      0.0,
            0.0,                       1.0 + temp_factor * 0.05, 0.0,
            0.0,                       0.0,                      1.0 - temp_factor * 0.15
        );
    }

    return temp_matrix * color;
}

/// 线性空间对比度调整 - 使用伽马曲线
float3 apply_contrast_linear(float3 color, float contrast) {
    // 在线性空间中，对比度调整需要特殊处理
    float contrast_factor = 1.0 + contrast;
    
    // 使用对数空间进行对比度调整，更符合人眼感知
    float3 log_color = log(max(color, float3(0.001))); // 避免log(0)
    float3 adjusted_log = log_color * contrast_factor;
    
    return exp(adjusted_log);
}

/// 线性空间饱和度调整 - 保持亮度不变
float3 apply_saturation_linear(float3 color, float saturation) {
    float luma = linear_luminance(color);
    float sat_factor = 1.0 + saturation;
    
    // 在线性空间中保持亮度不变的饱和度调整
    float3 desaturated = float3(luma);
    return mix(desaturated, color, sat_factor);
}

/// 线性空间自然饱和度调整 - 智能保护
float3 apply_vibrance_linear(float3 color, float vibrance) {
    float luma = linear_luminance(color);
    float max_channel = max(max(color.r, color.g), color.b);
    float min_channel = min(min(color.r, color.g), color.b);
    
    // 计算当前饱和度
    float current_saturation = (max_channel - min_channel) / (max_channel + 0.001);
    
    // 自然饱和度保护 - 已经饱和的颜色受影响较小
    float protection = 1.0 - pow(current_saturation, 2.0);
    float vibrance_factor = 1.0 + vibrance * protection;
    
    float3 desaturated = float3(luma);
    return mix(desaturated, color, vibrance_factor);
}

/// 线性空间高光阴影调整 - 基于亮度遮罩
float3 apply_highlights_shadows_linear(float3 color, float highlights, float shadows) {
    float luma = linear_luminance(color);
    
    // 线性空间的高光阴影遮罩
    float highlight_mask = smoothstep(0.2, 0.8, luma);
    highlight_mask = pow(highlight_mask, 1.5); // 更平滑的过渡
    
    float shadow_mask = smoothstep(0.8, 0.2, luma);
    shadow_mask = pow(shadow_mask, 1.5);
    
    // 高光压制
    if (highlights != 0.0) {
        float highlight_factor = 1.0 + highlights * highlight_mask * 0.8;
        color *= highlight_factor;
    }
    
    // 阴影提亮
    if (shadows != 0.0) {
        float shadow_factor = 1.0 + shadows * shadow_mask * 0.8;
        color *= shadow_factor;
    }
    
    return color;
}

// MARK: - 主要着色器函数

/// 线性空间RAW处理着色器
kernel void linear_raw_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                              texture2d<float, access::write> outputTexture [[texture(1)]],
                              constant LinearFilterParameters &params [[buffer(0)]],
                              uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 确保在线性空间处理
    // 注意：输入纹理应该已经是线性空间的RAW数据
    
    // 1. 曝光调整 - 线性空间业界标准曝光算法 + exp2()性能优化
    if (params.exposure != 0.0) {
        // 使用业界标准线性空间曝光算法：color.rgb *= pow(2.0, params.exposure) + exp2()性能优化
        color.rgb = apply_linear_simple_exposure(color.rgb, params.exposure);
    }
    
    // 2. 色温色调调整 - 使用Lab色彩空间 (专业级)
    if (params.temperature != 0.0 || params.tint != 0.0) {
        color.rgb = apply_lab_temperature_tint(color.rgb, params.temperature, params.tint);
    }
    
    // 4. 高光阴影调整 - 在线性空间更自然
    if (params.highlights != 0.0 || params.shadows != 0.0) {
        color.rgb = apply_highlights_shadows_linear(color.rgb, params.highlights, params.shadows);
    }
    
    // 5. 白色黑色调整 - 线性空间的色阶调整（修正方向）
    if (params.whites != 0.0 || params.blacks != 0.0) {
        float white_point = 1.0 - params.whites * 0.3; // 正值降低白点，让高光更亮
        float black_point = -params.blacks * 0.1; // 正值降低黑点，让阴影更亮

        // 线性色阶调整
        color.rgb = (color.rgb - black_point) / (white_point - black_point);
        color.rgb = clamp(color.rgb, 0.0, 1.0);
    }
    
    // 6. 亮度调整 - 在线性空间使用乘法
    if (params.brightness != 0.0) {
        float brightness_factor = 1.0 + params.brightness;
        color.rgb *= brightness_factor;
    }
    
    // 7. 对比度调整 - 线性空间特殊处理
    if (params.contrast != 0.0) {
        color.rgb = apply_contrast_linear(color.rgb, params.contrast);
    }
    
    // 8. 饱和度调整 - 保持线性空间亮度
    if (params.saturation != 0.0) {
        color.rgb = apply_saturation_linear(color.rgb, params.saturation);
    }
    
    // 9. 自然饱和度调整 - 智能保护
    if (params.vibrance != 0.0) {
        color.rgb = apply_vibrance_linear(color.rgb, params.vibrance);
    }
    
    // 10. 清晰度调整 - 在线性空间进行
    if (params.clarity != 0.0) {
        // 简化的清晰度实现
        float luma = linear_luminance(color.rgb);
        float clarity_factor = 1.0 + params.clarity * 0.3;
        color.rgb = mix(float3(luma), color.rgb, clarity_factor);
    }
    
    // 保持线性空间输出 - 不进行伽马校正
    // 最终的sRGB转换将在后续步骤中进行
    
    outputTexture.write(color, gid);
}

/// 线性空间到sRGB转换着色器
kernel void linear_to_srgb_converter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                     texture2d<float, access::write> outputTexture [[texture(1)]],
                                     uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 linear_color = inputTexture.read(gid);
    
    // 线性到sRGB转换
    float3 srgb_color;
    srgb_color.r = (linear_color.r <= 0.0031308) ? 
                   linear_color.r * 12.92 : 
                   1.055 * pow(linear_color.r, 1.0/2.4) - 0.055;
    srgb_color.g = (linear_color.g <= 0.0031308) ? 
                   linear_color.g * 12.92 : 
                   1.055 * pow(linear_color.g, 1.0/2.4) - 0.055;
    srgb_color.b = (linear_color.b <= 0.0031308) ? 
                   linear_color.b * 12.92 : 
                   1.055 * pow(linear_color.b, 1.0/2.4) - 0.055;
    
    float4 output_color = float4(clamp(srgb_color, 0.0, 1.0), linear_color.a);
    outputTexture.write(output_color, gid);
}

/// sRGB到线性空间转换着色器
kernel void srgb_to_linear_converter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                     texture2d<float, access::write> outputTexture [[texture(1)]],
                                     uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 srgb_color = inputTexture.read(gid);
    
    // sRGB到线性转换
    float3 linear_color;
    linear_color.r = (srgb_color.r <= 0.04045) ? 
                     srgb_color.r / 12.92 : 
                     pow((srgb_color.r + 0.055) / 1.055, 2.4);
    linear_color.g = (srgb_color.g <= 0.04045) ? 
                     srgb_color.g / 12.92 : 
                     pow((srgb_color.g + 0.055) / 1.055, 2.4);
    linear_color.b = (srgb_color.b <= 0.04045) ? 
                     srgb_color.b / 12.92 : 
                     pow((srgb_color.b + 0.055) / 1.055, 2.4);
    
    float4 output_color = float4(linear_color, srgb_color.a);
    outputTexture.write(output_color, gid);
}
