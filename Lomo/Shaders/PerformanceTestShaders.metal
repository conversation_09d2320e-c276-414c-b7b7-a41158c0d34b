#include <metal_stdlib>
using namespace metal;

// 性能测试着色器 - 用于对比exp2()和pow(2.0, x)的性能差异

/// 使用pow(2.0, x)的传统实现
kernel void exposure_test_pow(texture2d<float, access::read> inputTexture [[texture(0)]],
                              texture2d<float, access::write> outputTexture [[texture(1)]],
                              constant float &exposure [[buffer(0)]],
                              uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 传统实现：使用pow(2.0, x)
    if (abs(exposure) > 0.001) {
        float exposure_factor = pow(2.0, exposure);
        color.rgb *= exposure_factor;
    }
    
    outputTexture.write(color, gid);
}

/// 使用exp2()的优化实现
kernel void exposure_test_exp2(texture2d<float, access::read> inputTexture [[texture(0)]],
                               texture2d<float, access::write> outputTexture [[texture(1)]],
                               constant float &exposure [[buffer(0)]],
                               uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 优化实现：使用exp2()
    if (abs(exposure) > 0.001) {
        float exposure_factor = exp2(exposure);
        color.rgb *= exposure_factor;
    }
    
    outputTexture.write(color, gid);
}

/// 批量曝光测试 - pow版本（用于压力测试）
kernel void batch_exposure_test_pow(texture2d<float, access::read> inputTexture [[texture(0)]],
                                    texture2d<float, access::write> outputTexture [[texture(1)]],
                                    constant float &exposure [[buffer(0)]],
                                    uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 模拟复杂处理：多次曝光计算
    for (int i = 0; i < 10; i++) {
        float test_exposure = exposure * (float(i) * 0.1 - 0.5);
        if (abs(test_exposure) > 0.001) {
            float exposure_factor = pow(2.0, test_exposure);
            color.rgb = mix(color.rgb, color.rgb * exposure_factor, 0.1);
        }
    }
    
    outputTexture.write(color, gid);
}

/// 批量曝光测试 - exp2版本（用于压力测试）
kernel void batch_exposure_test_exp2(texture2d<float, access::read> inputTexture [[texture(0)]],
                                     texture2d<float, access::write> outputTexture [[texture(1)]],
                                     constant float &exposure [[buffer(0)]],
                                     uint2 gid [[thread_position_in_grid]]) {
    
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    float4 color = inputTexture.read(gid);
    
    // 模拟复杂处理：多次曝光计算
    for (int i = 0; i < 10; i++) {
        float test_exposure = exposure * (float(i) * 0.1 - 0.5);
        if (abs(test_exposure) > 0.001) {
            float exposure_factor = exp2(test_exposure);
            color.rgb = mix(color.rgb, color.rgb * exposure_factor, 0.1);
        }
    }
    
    outputTexture.write(color, gid);
}
