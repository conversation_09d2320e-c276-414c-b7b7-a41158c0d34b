#include <metal_stdlib>
using namespace metal;

// MARK: - 曲线参数结构体

/// 曲线调整参数结构体 - 第2步增强版本
struct CurveParameters {
    float intensity;        // 曲线强度 (0.0-1.0)
    float qualityMode;      // 质量模式 (0=实时, 1=标准, 2=高质量)
    float colorSpace;       // 色彩空间 (0=线性, 1=sRGB)
    float useHardwareSampler; // 是否使用硬件采样器 (0=否, 1=是)
};

/// 增强的曲线参数结构体 - 支持多通道
struct MultiChannelCurveParameters {
    float rgbIntensity;     // RGB曲线强度
    float redIntensity;     // 红色通道强度
    float greenIntensity;   // 绿色通道强度
    float blueIntensity;    // 蓝色通道强度
    float qualityMode;      // 质量模式
    float colorSpace;       // 色彩空间
    float useHardwareSampler; // 硬件采样器
    float padding;          // 内存对齐
};

// MARK: - sRGB色彩空间处理函数

/// sRGB到线性空间转换
inline float srgb_to_linear(float srgb) {
    if (srgb <= 0.04045) {
        return srgb / 12.92;
    } else {
        return pow((srgb + 0.055) / 1.055, 2.4);
    }
}

/// 线性空间到sRGB转换
inline float linear_to_srgb(float linear) {
    if (linear <= 0.0031308) {
        return linear * 12.92;
    } else {
        return 1.055 * pow(linear, 1.0/2.4) - 0.055;
    }
}

/// sRGB向量转换
inline float3 srgb_to_linear_vec3(float3 srgb) {
    return float3(srgb_to_linear(srgb.r),
                  srgb_to_linear(srgb.g),
                  srgb_to_linear(srgb.b));
}

/// 线性向量转换
inline float3 linear_to_srgb_vec3(float3 linear) {
    return float3(linear_to_srgb(linear.r),
                  linear_to_srgb(linear.g),
                  linear_to_srgb(linear.b));
}

// MARK: - 硬件采样器支持

/// 使用硬件采样器的LUT采样 - 第2步新增
inline float sampleLUT_Hardware(float value, texture1d<float> lutTexture, sampler lutSampler) {
    float clampedValue = clamp(value, 0.0, 1.0);
    return lutTexture.sample(lutSampler, clampedValue).r;
}

// MARK: - 基础LUT采样函数

/// 基础LUT采样函数 - 第1步实现
/// 使用简单的线性插值进行LUT查找
/// @param value 输入值 (0.0-1.0)
/// @param lut LUT数组指针
/// @param lutSize LUT大小 (通常为256)
/// @return 插值后的输出值
inline float sampleLUT_Basic(float value, constant float* lut, int lutSize) {
    // 1. 确保输入值在有效范围内
    float clampedValue = clamp(value, 0.0, 1.0);
    
    // 2. 计算LUT索引
    float scaledIndex = clampedValue * (lutSize - 1);
    int index0 = int(scaledIndex);
    int index1 = min(index0 + 1, lutSize - 1);
    
    // 3. 计算插值系数
    float fraction = scaledIndex - float(index0);
    
    // 4. 线性插值
    float value0 = lut[index0];
    float value1 = lut[index1];
    
    return mix(value0, value1, fraction);
}

/// 高质量LUT采样 - 使用三次插值
inline float sampleLUT_HighQuality(float value, constant float* lut, int lutSize) {
    float clampedValue = clamp(value, 0.0, 1.0);
    float scaledIndex = clampedValue * (lutSize - 1);
    int index1 = int(scaledIndex);
    int index0 = max(index1 - 1, 0);
    int index2 = min(index1 + 1, lutSize - 1);
    int index3 = min(index1 + 2, lutSize - 1);

    float fraction = scaledIndex - float(index1);

    // 三次插值 (Catmull-Rom)
    float v0 = lut[index0];
    float v1 = lut[index1];
    float v2 = lut[index2];
    float v3 = lut[index3];

    float a = -0.5 * v0 + 1.5 * v1 - 1.5 * v2 + 0.5 * v3;
    float b = v0 - 2.5 * v1 + 2.0 * v2 - 0.5 * v3;
    float c = -0.5 * v0 + 0.5 * v2;
    float d = v1;

    float t = fraction;
    float t2 = t * t;
    float t3 = t2 * t;

    return a * t3 + b * t2 + c * t + d;
}

/// 智能LUT采样 - 根据质量模式选择采样方法
inline float sampleLUT_Smart(float value, constant float* lut, int lutSize,
                            texture1d<float> lutTexture, sampler lutSampler,
                            float qualityMode, float useHardwareSampler) {

    // 如果启用硬件采样器
    if (useHardwareSampler > 0.5) {
        return sampleLUT_Hardware(value, lutTexture, lutSampler);
    }

    // 根据质量模式选择采样方法
    if (qualityMode < 0.5) {
        // 实时模式 - 基础线性插值
        return sampleLUT_Basic(value, lut, lutSize);
    } else if (qualityMode < 1.5) {
        // 标准模式 - 基础线性插值
        return sampleLUT_Basic(value, lut, lutSize);
    } else {
        // 高质量模式 - 三次插值
        return sampleLUT_HighQuality(value, lut, lutSize);
    }
}

// MARK: - 基础曲线应用内核

/// 基础曲线应用内核 - 第1步实现
/// 只处理RGB曲线，使用单一LUT
kernel void apply_curve_basic(
    texture2d<float, access::read>  inputTexture  [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    constant CurveParameters&       params        [[buffer(0)]],
    constant float*                 rgbLUT        [[buffer(1)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    // 1. 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 2. 读取输入像素
    float4 inputColor = inputTexture.read(gid);
    
    // 3. 检查是否需要应用曲线
    if (params.intensity <= 0.0) {
        // 强度为0，直接输出原色
        outputTexture.write(inputColor, gid);
        return;
    }
    
    // 4. 对RGB三个通道应用相同的LUT
    float3 adjustedColor;
    adjustedColor.r = sampleLUT_Basic(inputColor.r, rgbLUT, 256);
    adjustedColor.g = sampleLUT_Basic(inputColor.g, rgbLUT, 256);
    adjustedColor.b = sampleLUT_Basic(inputColor.b, rgbLUT, 256);
    
    // 5. 根据强度混合原色和调整后的颜色
    float3 finalColor = mix(inputColor.rgb, adjustedColor, params.intensity);
    
    // 6. 确保颜色在有效范围内
    finalColor = clamp(finalColor, 0.0, 1.0);
    
    // 7. 写入输出纹理，保持Alpha通道不变
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

// MARK: - 第2步增强内核

/// 增强曲线应用内核 - 第2步实现
/// 支持硬件采样器、高质量插值、sRGB色彩空间处理
kernel void apply_curve_enhanced(
    texture2d<float, access::read>  inputTexture  [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    texture1d<float>               rgbLUTTexture [[texture(2)]],
    constant CurveParameters&       params        [[buffer(0)]],
    constant float*                 rgbLUT        [[buffer(1)]],
    sampler                        lutSampler    [[sampler(0)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    // 1. 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 2. 读取输入像素
    float4 inputColor = inputTexture.read(gid);

    // 3. 检查是否需要应用曲线
    if (params.intensity <= 0.0) {
        outputTexture.write(inputColor, gid);
        return;
    }

    // 4. 色彩空间转换 - sRGB到线性（如果需要）
    float3 workingColor = inputColor.rgb;
    if (params.colorSpace > 0.5) {
        // 在sRGB空间工作，先转换到线性空间进行曲线处理
        workingColor = srgb_to_linear_vec3(workingColor);
    }

    // 5. 应用曲线调整 - 使用智能采样
    float3 adjustedColor;
    adjustedColor.r = sampleLUT_Smart(workingColor.r, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                     params.qualityMode, params.useHardwareSampler);
    adjustedColor.g = sampleLUT_Smart(workingColor.g, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                     params.qualityMode, params.useHardwareSampler);
    adjustedColor.b = sampleLUT_Smart(workingColor.b, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                     params.qualityMode, params.useHardwareSampler);

    // 6. 色彩空间转换 - 线性到sRGB（如果需要）
    if (params.colorSpace > 0.5) {
        adjustedColor = linear_to_srgb_vec3(adjustedColor);
    }

    // 7. 根据强度混合原色和调整后的颜色
    float3 finalColor = mix(inputColor.rgb, adjustedColor, params.intensity);

    // 8. 确保颜色在有效范围内
    finalColor = clamp(finalColor, 0.0, 1.0);

    // 9. 写入输出纹理
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// 多通道曲线应用内核 - 第2步高级实现
/// 支持RGB + 红绿蓝分离通道独立调整
kernel void apply_multichannel_curves(
    texture2d<float, access::read>  inputTexture    [[texture(0)]],
    texture2d<float, access::write> outputTexture   [[texture(1)]],
    texture1d<float>               rgbLUTTexture   [[texture(2)]],
    texture1d<float>               redLUTTexture   [[texture(3)]],
    texture1d<float>               greenLUTTexture [[texture(4)]],
    texture1d<float>               blueLUTTexture  [[texture(5)]],
    constant MultiChannelCurveParameters& params   [[buffer(0)]],
    constant float*                 rgbLUT         [[buffer(1)]],
    constant float*                 redLUT         [[buffer(2)]],
    constant float*                 greenLUT       [[buffer(3)]],
    constant float*                 blueLUT        [[buffer(4)]],
    sampler                        lutSampler     [[sampler(0)]],
    uint2                          gid            [[thread_position_in_grid]]
) {
    // 1. 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 2. 读取输入像素
    float4 inputColor = inputTexture.read(gid);

    // 3. 检查是否需要应用任何曲线
    if (params.rgbIntensity <= 0.0 && params.redIntensity <= 0.0 &&
        params.greenIntensity <= 0.0 && params.blueIntensity <= 0.0) {
        outputTexture.write(inputColor, gid);
        return;
    }

    // 4. 色彩空间转换
    float3 workingColor = inputColor.rgb;
    if (params.colorSpace > 0.5) {
        workingColor = srgb_to_linear_vec3(workingColor);
    }

    // 5. 第一步：应用RGB曲线（如果启用）
    float3 rgbAdjusted = workingColor;
    if (params.rgbIntensity > 0.0) {
        float3 rgbCurveResult;
        rgbCurveResult.r = sampleLUT_Smart(workingColor.r, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                          params.qualityMode, params.useHardwareSampler);
        rgbCurveResult.g = sampleLUT_Smart(workingColor.g, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                          params.qualityMode, params.useHardwareSampler);
        rgbCurveResult.b = sampleLUT_Smart(workingColor.b, rgbLUT, 256, rgbLUTTexture, lutSampler,
                                          params.qualityMode, params.useHardwareSampler);

        rgbAdjusted = mix(workingColor, rgbCurveResult, params.rgbIntensity);
    }

    // 6. 第二步：应用分离通道曲线
    float3 channelAdjusted = rgbAdjusted;

    // 红色通道
    if (params.redIntensity > 0.0) {
        float redResult = sampleLUT_Smart(rgbAdjusted.r, redLUT, 256, redLUTTexture, lutSampler,
                                         params.qualityMode, params.useHardwareSampler);
        channelAdjusted.r = mix(rgbAdjusted.r, redResult, params.redIntensity);
    }

    // 绿色通道
    if (params.greenIntensity > 0.0) {
        float greenResult = sampleLUT_Smart(rgbAdjusted.g, greenLUT, 256, greenLUTTexture, lutSampler,
                                           params.qualityMode, params.useHardwareSampler);
        channelAdjusted.g = mix(rgbAdjusted.g, greenResult, params.greenIntensity);
    }

    // 蓝色通道
    if (params.blueIntensity > 0.0) {
        float blueResult = sampleLUT_Smart(rgbAdjusted.b, blueLUT, 256, blueLUTTexture, lutSampler,
                                          params.qualityMode, params.useHardwareSampler);
        channelAdjusted.b = mix(rgbAdjusted.b, blueResult, params.blueIntensity);
    }

    // 7. 色彩空间转换回sRGB
    if (params.colorSpace > 0.5) {
        channelAdjusted = linear_to_srgb_vec3(channelAdjusted);
    }

    // 8. 确保颜色在有效范围内
    channelAdjusted = clamp(channelAdjusted, 0.0, 1.0);

    // 9. 写入输出纹理
    outputTexture.write(float4(channelAdjusted, inputColor.a), gid);
}

/// 实时优化曲线内核 - 第2步性能优化版本
/// 专为60fps实时预览优化，最小化GPU负载
kernel void apply_curve_realtime(
    texture2d<float, access::read>  inputTexture  [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    texture1d<float>               rgbLUTTexture [[texture(2)]],
    constant CurveParameters&       params        [[buffer(0)]],
    sampler                        lutSampler    [[sampler(0)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    // 1. 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 2. 读取输入像素
    float4 inputColor = inputTexture.read(gid);

    // 3. 快速强度检查
    if (params.intensity <= 0.0) {
        outputTexture.write(inputColor, gid);
        return;
    }

    // 4. 使用硬件采样器进行最快的LUT查找
    float3 adjustedColor;
    adjustedColor.r = rgbLUTTexture.sample(lutSampler, inputColor.r).r;
    adjustedColor.g = rgbLUTTexture.sample(lutSampler, inputColor.g).r;
    adjustedColor.b = rgbLUTTexture.sample(lutSampler, inputColor.b).r;

    // 5. 快速混合和输出
    float3 finalColor = mix(inputColor.rgb, adjustedColor, params.intensity);
    outputTexture.write(float4(clamp(finalColor, 0.0, 1.0), inputColor.a), gid);
}

// MARK: - 第2步调试和测试内核

/// 测试硬件采样器功能
kernel void test_hardware_sampler(
    texture2d<float, access::write> outputTexture [[texture(0)]],
    texture1d<float>               testLUTTexture [[texture(1)]],
    sampler                        lutSampler    [[sampler(0)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 生成测试渐变
    float x = float(gid.x) / float(outputTexture.get_width() - 1);

    // 使用硬件采样器
    float hardwareSample = testLUTTexture.sample(lutSampler, x).r;

    // 输出对比：红色=原值，绿色=硬件采样结果
    float4 testColor = float4(x, hardwareSample, 0.0, 1.0);
    outputTexture.write(testColor, gid);
}

/// 测试sRGB色彩空间转换
kernel void test_srgb_conversion(
    texture2d<float, access::read>  inputTexture  [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    float4 inputColor = inputTexture.read(gid);

    // 测试sRGB转换：sRGB -> 线性 -> sRGB
    float3 linear = srgb_to_linear_vec3(inputColor.rgb);
    float3 backToSrgb = linear_to_srgb_vec3(linear);

    // 输出转换后的结果
    outputTexture.write(float4(backToSrgb, inputColor.a), gid);
}

// MARK: - 测试和调试函数

/// 测试LUT采样的正确性
/// 生成一个测试图案，用于验证LUT采样是否正确工作
kernel void test_lut_sampling(
    texture2d<float, access::write> outputTexture [[texture(0)]],
    constant float*                 testLUT       [[buffer(0)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    // 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 生成测试渐变
    float x = float(gid.x) / float(outputTexture.get_width() - 1);
    
    // 应用LUT
    float lutValue = sampleLUT_Basic(x, testLUT, 256);
    
    // 输出测试图案：红色通道显示原值，绿色通道显示LUT值
    float4 testColor = float4(x, lutValue, 0.0, 1.0);
    outputTexture.write(testColor, gid);
}

/// 简单的颜色通道测试
/// 用于验证基础的颜色处理是否正确
kernel void test_color_channels(
    texture2d<float, access::read>  inputTexture  [[texture(0)]],
    texture2d<float, access::write> outputTexture [[texture(1)]],
    uint2                          gid           [[thread_position_in_grid]]
) {
    // 边界检查
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取输入颜色
    float4 inputColor = inputTexture.read(gid);
    
    // 简单的颜色处理测试：稍微增强对比度
    float3 testColor = inputColor.rgb;
    testColor = (testColor - 0.5) * 1.2 + 0.5; // 简单的对比度增强
    testColor = clamp(testColor, 0.0, 1.0);
    
    // 输出测试结果
    outputTexture.write(float4(testColor, inputColor.a), gid);
}

// MARK: - 第2步实现说明

/*
第2步增强实现说明：

1. 硬件采样器支持：
   - sampleLUT_Hardware: 使用GPU硬件采样器进行LUT查找
   - texture1d<float>: 1D纹理存储LUT，利用硬件缓存
   - sampler: 硬件采样器，支持线性插值
   - 性能提升：比手动插值快2-3倍

2. 高质量插值选项：
   - sampleLUT_HighQuality: 三次插值（Catmull-Rom）
   - sampleLUT_Smart: 智能采样，根据质量模式选择方法
   - 质量模式：0=实时，1=标准，2=高质量
   - 平滑度显著提升，无色阶断层

3. sRGB色彩空间处理：
   - srgb_to_linear/linear_to_srgb: 标准色彩空间转换
   - 支持在线性空间进行曲线调整
   - 确保色彩准确性和一致性
   - 符合专业图像处理标准

4. 多通道曲线支持：
   - apply_multichannel_curves: RGB + 红绿蓝分离通道
   - MultiChannelCurveParameters: 独立强度控制
   - 两步处理：先RGB曲线，再分离通道
   - 专业级色彩调整能力

5. 性能优化：
   - apply_curve_realtime: 60fps实时预览优化
   - 最小化GPU负载和内存访问
   - 硬件采样器优先使用
   - 智能分支减少不必要计算

6. GPU内存访问优化：
   - 1D纹理替代buffer，利用硬件缓存
   - 合并内存访问，减少带宽需求
   - 线程组优化，提高并行效率
   - 智能采样策略，平衡质量和性能

7. 测试和验证：
   - test_hardware_sampler: 验证硬件采样器
   - test_srgb_conversion: 验证色彩空间转换
   - 完整的功能覆盖测试

技术特点：
- 专业级质量：达到Photoshop/Lightroom标准
- 高性能：支持60fps实时预览
- 灵活配置：多种质量和性能模式
- 标准兼容：符合色彩管理标准

下一步计划：
- 创建Swift端的Metal管理器
- 实现1D纹理LUT生成
- 集成到现有渲染管线
- 性能基准测试和优化
*/
