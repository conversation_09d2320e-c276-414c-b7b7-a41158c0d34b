#include <metal_stdlib>
using namespace metal;

/// LUT处理参数结构体
struct LUTParameters {
    float intensity;        // LUT强度 (0.0 到 1.0)
    float lutSize;         // LUT尺寸 (通常是64)
    float padding1;        // 内存对齐
    float padding2;        // 内存对齐
};

/// 简化的2D LUT查找函数 (最兼容的实现)
float3 simpleLUT2D(float3 color, texture2d<float, access::sample> lutTexture, float lutSize) {
    // 将RGB值映射到2D LUT坐标
    float blueSlice = color.b * (lutSize - 1.0);
    float blueSliceFloor = floor(blueSlice);
    float blueSliceFrac = blueSlice - blueSliceFloor;

    // 计算2D坐标
    float2 coord1, coord2;
    coord1.x = (color.r * (lutSize - 1.0) + blueSliceFloor * lutSize) / (lutSize * lutSize);
    coord1.y = color.g;

    coord2.x = (color.r * (lutSize - 1.0) + (blueSliceFloor + 1.0) * lutSize) / (lutSize * lutSize);
    coord2.y = color.g;

    // 采样
    constexpr sampler s(coord::normalized, address::clamp_to_edge, filter::linear);
    float3 color1 = lutTexture.sample(s, coord1).rgb;
    float3 color2 = lutTexture.sample(s, coord2).rgb;

    // 插值
    return mix(color1, color2, blueSliceFrac);
}

/// 真正的3D LUT查找函数 - 专业级实现
/// - Parameters:
///   - color: 输入颜色 (0.0-1.0)
///   - lutTexture: 3D LUT纹理
///   - lutSize: LUT尺寸
/// - Returns: 查找后的颜色
float3 lookup3DLUT(float3 color, texture3d<float, access::read> lutTexture, float lutSize) {
    // 确保颜色值在有效范围内
    float3 clampedColor = clamp(color, 0.0, 1.0);

    // 将颜色值映射到LUT纹理坐标
    // 标准3D LUT映射：从 [0,1] 映射到纹理坐标
    float3 lutCoord = clampedColor * (lutSize - 1.0) / lutSize + 0.5 / lutSize;

    // 手动实现3D纹理采样（因为Metal的3D纹理采样可能有兼容性问题）
    // 计算整数坐标
    float3 scaledCoord = clampedColor * (lutSize - 1.0);
    int3 coord0 = int3(scaledCoord);
    int3 coord1 = min(coord0 + 1, int(lutSize - 1));

    // 计算插值权重
    float3 frac = scaledCoord - float3(coord0);

    // 读取8个相邻的LUT值进行三线性插值
    float4 c000 = lutTexture.read(uint3(coord0.x, coord0.y, coord0.z));
    float4 c001 = lutTexture.read(uint3(coord0.x, coord0.y, coord1.z));
    float4 c010 = lutTexture.read(uint3(coord0.x, coord1.y, coord0.z));
    float4 c011 = lutTexture.read(uint3(coord0.x, coord1.y, coord1.z));
    float4 c100 = lutTexture.read(uint3(coord1.x, coord0.y, coord0.z));
    float4 c101 = lutTexture.read(uint3(coord1.x, coord0.y, coord1.z));
    float4 c110 = lutTexture.read(uint3(coord1.x, coord1.y, coord0.z));
    float4 c111 = lutTexture.read(uint3(coord1.x, coord1.y, coord1.z));

    // 三线性插值
    float4 c00 = mix(c000, c001, frac.z);
    float4 c01 = mix(c010, c011, frac.z);
    float4 c10 = mix(c100, c101, frac.z);
    float4 c11 = mix(c110, c111, frac.z);

    float4 c0 = mix(c00, c01, frac.y);
    float4 c1 = mix(c10, c11, frac.y);

    float4 result = mix(c0, c1, frac.x);

    return result.rgb;
}

/// 2D LUT查找函数 (用于扁平化的LUT)
/// - Parameters:
///   - color: 输入颜色 (0.0-1.0)
///   - lutTexture: 2D LUT纹理 (扁平化的3D LUT)
///   - lutSize: LUT尺寸
/// - Returns: 查找后的颜色
float3 lookup2DLUT(float3 color, texture2d<float, access::sample> lutTexture, float lutSize) {
    // 将3D坐标转换为2D坐标
    float blueSlice = color.b * (lutSize - 1.0);
    float blueSliceFloor = floor(blueSlice);
    float blueSliceFrac = blueSlice - blueSliceFloor;
    
    // 计算两个蓝色切片的坐标
    float2 coord1, coord2;
    
    // 第一个切片
    coord1.x = (color.r * (lutSize - 1.0) + blueSliceFloor * lutSize) / (lutSize * lutSize);
    coord1.y = color.g;
    
    // 第二个切片
    coord2.x = (color.r * (lutSize - 1.0) + (blueSliceFloor + 1.0) * lutSize) / (lutSize * lutSize);
    coord2.y = color.g;
    
    // 采样两个切片
    constexpr sampler lutSampler(coord::normalized, 
                                address::clamp_to_edge, 
                                filter::linear);
    
    float3 color1 = lutTexture.sample(lutSampler, coord1).rgb;
    float3 color2 = lutTexture.sample(lutSampler, coord2).rgb;
    
    // 在两个切片之间插值
    return mix(color1, color2, blueSliceFrac);
}

/// 真正的3D LUT滤镜着色器 - 专业级实现
kernel void apply_3d_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                               texture2d<float, access::write> outputTexture [[texture(1)]],
                               texture3d<float, access::read> lutTexture [[texture(2)]],
                               constant LUTParameters& params [[buffer(0)]],
                               uint2 gid [[thread_position_in_grid]]) {
    
    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);
    
    // 应用3D LUT查找
    float3 lutColor = lookup3DLUT(inputColor.rgb, lutTexture, params.lutSize);
    
    // 根据强度混合原色和LUT颜色
    float3 finalColor = mix(inputColor.rgb, lutColor, params.intensity);
    
    // 写入输出
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// 2D LUT滤镜着色器 (用于扁平化的LUT)
kernel void apply_2d_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                               texture2d<float, access::write> outputTexture [[texture(1)]],
                               texture2d<float, access::sample> lutTexture [[texture(2)]],
                               constant LUTParameters& params [[buffer(0)]],
                               uint2 gid [[thread_position_in_grid]]) {
    
    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);
    
    // 应用2D LUT查找
    float3 lutColor = lookup2DLUT(inputColor.rgb, lutTexture, params.lutSize);
    
    // 根据强度混合原色和LUT颜色
    float3 finalColor = mix(inputColor.rgb, lutColor, params.intensity);
    
    // 写入输出
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// CUBE LUT滤镜着色器 (专门处理.cube格式)
kernel void apply_cube_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                 texture2d<float, access::write> outputTexture [[texture(1)]],
                                 texture3d<float, access::read> lutTexture [[texture(2)]],
                                 constant LUTParameters& params [[buffer(0)]],
                                 uint2 gid [[thread_position_in_grid]]) {
    
    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);
    
    // CUBE格式的特殊处理
    float3 scaledColor = clamp(inputColor.rgb, 0.0, 1.0);
    
    // 应用3D LUT查找
    float3 lutColor = lookup3DLUT(scaledColor, lutTexture, params.lutSize);
    
    // 根据强度混合原色和LUT颜色
    float3 finalColor = mix(inputColor.rgb, lutColor, params.intensity);
    
    // 保持原始alpha通道
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// 高质量LUT滤镜着色器 (使用更高精度的插值)
kernel void apply_high_quality_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                         texture2d<float, access::write> outputTexture [[texture(1)]],
                                         texture3d<float, access::read> lutTexture [[texture(2)]],
                                         constant LUTParameters& params [[buffer(0)]],
                                         uint2 gid [[thread_position_in_grid]]) {
    
    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }
    
    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);
    
    // 高质量颜色预处理
    float3 processedColor = inputColor.rgb;
    
    // 伽马校正 (如果需要)
    // processedColor = pow(processedColor, 2.2);
    
    // 应用3D LUT查找
    float3 lutColor = lookup3DLUT(processedColor, lutTexture, params.lutSize);
    
    // 反伽马校正 (如果需要)
    // lutColor = pow(lutColor, 1.0/2.2);
    
    // 使用平滑步函数进行更自然的混合
    float smoothIntensity = smoothstep(0.0, 1.0, params.intensity);
    float3 finalColor = mix(inputColor.rgb, lutColor, smoothIntensity);
    
    // 确保颜色在有效范围内
    finalColor = clamp(finalColor, 0.0, 1.0);
    
    // 写入输出
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// 实时3D LUT预览着色器 (优化性能但保持3D精度)
kernel void apply_realtime_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                     texture2d<float, access::write> outputTexture [[texture(1)]],
                                     texture3d<float, access::read> lutTexture [[texture(2)]],
                                     constant LUTParameters& params [[buffer(0)]],
                                     uint2 gid [[thread_position_in_grid]]) {

    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);

    // 使用3D LUT查找 (真正的3D插值)
    float3 lutColor = lookup3DLUT(inputColor.rgb, lutTexture, params.lutSize);

    // 快速混合
    float3 finalColor = inputColor.rgb + (lutColor - inputColor.rgb) * params.intensity;

    // 写入输出
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}

/// 简化的LUT滤镜着色器 (最兼容版本)
kernel void apply_simple_lut_filter(texture2d<float, access::read> inputTexture [[texture(0)]],
                                   texture2d<float, access::write> outputTexture [[texture(1)]],
                                   texture2d<float, access::sample> lutTexture [[texture(2)]],
                                   constant LUTParameters& params [[buffer(0)]],
                                   uint2 gid [[thread_position_in_grid]]) {

    // 检查边界
    if (gid.x >= outputTexture.get_width() || gid.y >= outputTexture.get_height()) {
        return;
    }

    // 读取输入像素
    float4 inputColor = inputTexture.read(gid);

    // 应用简化的2D LUT查找
    float3 lutColor = simpleLUT2D(inputColor.rgb, lutTexture, params.lutSize);

    // 根据强度混合原色和LUT颜色
    float3 finalColor = mix(inputColor.rgb, lutColor, params.intensity);

    // 写入输出
    outputTexture.write(float4(finalColor, inputColor.a), gid);
}
