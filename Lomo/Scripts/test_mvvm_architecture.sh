#!/bin/bash

# MVVM-S架构测试脚本
echo "🧪 MVVM-S架构完整性测试"
echo "================================"

# 检查所有新创建的文件
echo ""
echo "📁 检查MVVM-S文件结构..."

MVVM_FILES=(
    "Lomo/Services/Edit/WatermarkService.swift"
    "Lomo/ViewModels/Edit/WatermarkViewModel.swift"
    "Lomo/Views/Edit/WatermarkView.swift"
    "Lomo/DependencyInjection/WatermarkDependencyContainer.swift"
)

for file in "${MVVM_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 文件缺失"
        exit 1
    fi
done

# 检查原始文件是否完整
echo ""
echo "🛡️ 检查原始文件完整性..."

ORIGINAL_FILES=(
    "Lomo/Views/Edit/Components/WatermarkControlView.swift"
    "Lomo/Managers/Edit/WatermarkSettingsManager.swift"
    "Lomo/Managers/Edit/WatermarkStyleManager.swift"
    "Lomo/Models/Edit/WatermarkSettings.swift"
)

for file in "${ORIGINAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file - 原始文件保持完整"
    else
        echo "❌ $file - 原始文件缺失"
        exit 1
    fi
done

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查MVVM-S架构层次
echo ""
echo "🏗️ 检查MVVM-S架构层次..."

# 检查Service层
if grep -q "class WatermarkService" "Lomo/Services/Edit/WatermarkService.swift" && \
   grep -q "WatermarkSettingsManager.shared" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ Service层：正确包装现有Manager"
else
    echo "❌ Service层：包装逻辑有问题"
    exit 1
fi

# 检查ViewModel层
if grep -q "class WatermarkViewModel" "Lomo/ViewModels/Edit/WatermarkViewModel.swift" && \
   grep -q "ObservableObject" "Lomo/ViewModels/Edit/WatermarkViewModel.swift" && \
   grep -q "watermarkService: WatermarkServiceProtocol" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ ViewModel层：正确实现依赖注入"
else
    echo "❌ ViewModel层：依赖注入有问题"
    exit 1
fi

# 检查View层
if grep -q "struct WatermarkView" "Lomo/Views/Edit/WatermarkView.swift" && \
   grep -q "@StateObject private var viewModel: WatermarkViewModel" "Lomo/Views/Edit/WatermarkView.swift"; then
    echo "✅ View层：正确使用ViewModel"
else
    echo "❌ View层：ViewModel集成有问题"
    exit 1
fi

# 检查依赖注入容器
if grep -q "class WatermarkDependencyContainer" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift" && \
   grep -q "func makeWatermarkService" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift" && \
   grep -q "func makeWatermarkViewModel" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift"; then
    echo "✅ 依赖注入容器：工厂方法完整"
else
    echo "❌ 依赖注入容器：工厂方法有问题"
    exit 1
fi

# 检查架构分离度
echo ""
echo "🔍 检查架构分离度..."

# 确保View不直接调用Manager
if ! grep -q "WatermarkSettingsManager" "Lomo/Views/Edit/WatermarkView.swift"; then
    echo "✅ View层：没有直接调用Manager（架构分离正确）"
else
    echo "❌ View层：仍然直接调用Manager（架构分离不完整）"
    exit 1
fi

# 确保ViewModel不直接调用Manager（通过Service调用）
# 排除注释中的引用，只检查实际的代码调用（不在注释行中）
MANAGER_CALLS=$(grep -v "^\s*///" "Lomo/ViewModels/Edit/WatermarkViewModel.swift" | grep -c "WatermarkSettingsManager\.shared\." || true)
if [ "$MANAGER_CALLS" -eq 0 ]; then
    echo "✅ ViewModel层：没有直接调用Manager（通过Service调用）"
else
    echo "❌ ViewModel层：仍然直接调用Manager（应该通过Service）"
    exit 1
fi

# 检查现有WatermarkControlView是否未被修改
CONTROL_VIEW_LINES=$(wc -l < "Lomo/Views/Edit/Components/WatermarkControlView.swift")
if [ "$CONTROL_VIEW_LINES" -gt 3000 ]; then
    echo "✅ WatermarkControlView：保持原始状态（$CONTROL_VIEW_LINES 行）"
else
    echo "⚠️ WatermarkControlView：可能被意外修改（$CONTROL_VIEW_LINES 行）"
fi

# 最终验证
echo ""
echo "🎉 第4步验证结果"
echo "================================"
echo "✅ MVVM-S架构文件全部创建成功"
echo "✅ 所有原始文件保持完整"
echo "✅ 项目编译正常"
echo "✅ 架构层次分离正确"
echo "✅ 依赖注入容器工作正常"
echo ""
echo "📋 架构状态："
echo "   Model: WatermarkSettings（SwiftData）"
echo "   View: WatermarkView（新）+ WatermarkControlView（原始）"
echo "   ViewModel: WatermarkViewModel（状态管理）"
echo "   Service: WatermarkService（业务逻辑包装）"
echo ""
echo "🔄 当前状态："
echo "   - 新的MVVM-S架构已经完整搭建"
echo "   - 原始的WatermarkControlView继续正常工作"
echo "   - 两套系统可以并存，互不影响"
echo ""
echo "🔒 安全保证："
echo "   - 删除新创建的4个文件即可完全回滚"
echo "   - 现有功能完全不受影响"
echo "   - 可以安全进行下一步：实际状态迁移"
