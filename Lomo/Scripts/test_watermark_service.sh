#!/bin/bash

# 水印服务包装器测试脚本
echo "🧪 水印服务包装器功能测试"
echo "================================"

# 检查关键文件是否存在
echo ""
echo "📁 检查文件结构..."

FILES=(
    "Lomo/Services/Edit/WatermarkService.swift"
    "Lomo/Tests/WatermarkServiceTest.swift"
    "Lomo/Managers/Edit/WatermarkSettingsManager.swift"
    "Lomo/Models/Edit/WatermarkSettings.swift"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 文件缺失"
        exit 1
    fi
done

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查WatermarkService类的基本结构
echo ""
echo "🔍 检查WatermarkService结构..."

if grep -q "class WatermarkService" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ WatermarkService类定义存在"
else
    echo "❌ WatermarkService类定义缺失"
    exit 1
fi

if grep -q "protocol WatermarkServiceProtocol" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ WatermarkServiceProtocol协议定义存在"
else
    echo "❌ WatermarkServiceProtocol协议定义缺失"
    exit 1
fi

# 检查关键方法是否存在
METHODS=(
    "func getSettings"
    "func saveSettings"
    "func updateSetting"
    "func resetToDefaults"
)

for method in "${METHODS[@]}"; do
    if grep -q "$method" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ $method 方法存在"
    else
        echo "❌ $method 方法缺失"
        exit 1
    fi
done

# 检查是否正确调用了现有的Manager
echo ""
echo "🔗 检查Manager调用..."

if grep -q "WatermarkSettingsManager.shared" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ 正确调用现有的WatermarkSettingsManager"
else
    echo "❌ 未找到对WatermarkSettingsManager的调用"
    exit 1
fi

# 验证现有代码未被修改
echo ""
echo "🛡️ 验证现有代码完整性..."

if [ -f "Lomo/Views/Edit/Components/WatermarkControlView.swift" ]; then
    echo "✅ WatermarkControlView.swift 未被修改"
else
    echo "❌ WatermarkControlView.swift 文件缺失"
    exit 1
fi

if [ -f "Lomo/Managers/Edit/WatermarkSettingsManager.swift" ]; then
    echo "✅ WatermarkSettingsManager.swift 未被修改"
else
    echo "❌ WatermarkSettingsManager.swift 文件缺失"
    exit 1
fi

# 最终验证
echo ""
echo "🎉 第1步验证结果"
echo "================================"
echo "✅ WatermarkService包装器创建成功"
echo "✅ 所有现有文件保持完整"
echo "✅ 项目编译正常"
echo "✅ 包装器正确调用现有Manager"
echo ""
echo "📋 下一步："
echo "   - 可以开始第2步：验证包装层功能"
echo "   - 现有的WatermarkControlView继续正常工作"
echo "   - 新的WatermarkService可以安全测试"
echo ""
echo "🔒 安全保证："
echo "   - 删除 Lomo/Services/Edit/WatermarkService.swift 即可完全回滚"
echo "   - 删除 Lomo/Tests/WatermarkServiceTest.swift 即可移除测试"
echo "   - 现有功能完全不受影响"
