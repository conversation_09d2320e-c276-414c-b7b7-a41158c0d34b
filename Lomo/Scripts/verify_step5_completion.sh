#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 步骤5验证脚本：验证View层依赖注入更新完成情况

echo "🔍 步骤5完成情况验证"
echo "=================================================="

# 1. 检查AdjustView更新
echo ""
echo "1️⃣ 检查AdjustView更新..."

if grep -q "AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift; then
    echo "✅ AdjustView已更新为使用AdjustViewModelRefactored"
else
    echo "❌ AdjustView未更新ViewModel类型"
fi

if grep -q "getCurrentParametersCopy" Lomo/Views/Edit/AdjustView.swift; then
    echo "⚠️ AdjustView仍使用getCurrentParametersCopy()方法"
    echo "   需要替换为currentParameters属性"
else
    echo "✅ AdjustView已移除getCurrentParametersCopy()调用"
fi

# 2. 检查FilterView更新
echo ""
echo "2️⃣ 检查FilterView更新..."

if grep -q "FilterViewModelRefactored" Lomo/Views/Edit/FilterView.swift; then
    echo "✅ FilterView已更新为使用FilterViewModelRefactored"
else
    echo "❌ FilterView未更新ViewModel类型"
fi

# 3. 检查EditView更新
echo ""
echo "3️⃣ 检查EditView更新..."

if grep -q "FilterDependencyContainer.filterViewModel()" Lomo/Views/Edit/EditView.swift; then
    echo "✅ EditView已更新为使用FilterDependencyContainer"
else
    echo "❌ EditView未更新FilterViewModel实例化"
fi

if grep -q "AdjustDependencyContainer.adjustViewModel()" Lomo/Views/Edit/EditView.swift; then
    echo "✅ EditView已更新为使用AdjustDependencyContainer"
else
    echo "❌ EditView未更新AdjustViewModel实例化"
fi

# 4. 编译验证
echo ""
echo "4️⃣ 编译验证..."

if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译通过"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息:"
    swift build 2>&1 | head -10
fi

# 5. 架构合规性检查
echo ""
echo "5️⃣ 架构合规性检查..."

# 检查是否还有旧的ViewModel使用
old_adjust_vm=$(grep -c "AdjustViewModel[^R]" Lomo/Views/Edit/AdjustView.swift || echo "0")
old_filter_vm=$(grep -c "FilterViewModel[^R]" Lomo/Views/Edit/FilterView.swift || echo "0")

if [ "$old_adjust_vm" -eq 0 ]; then
    echo "✅ AdjustView完全使用重构后的ViewModel"
else
    echo "⚠️ AdjustView仍有 $old_adjust_vm 处使用旧的ViewModel"
fi

if [ "$old_filter_vm" -eq 0 ]; then
    echo "✅ FilterView完全使用重构后的ViewModel"
else
    echo "⚠️ FilterView仍有 $old_filter_vm 处使用旧的ViewModel"
fi

# 6. 依赖注入使用检查
echo ""
echo "6️⃣ 依赖注入使用检查..."

if grep -q "DependencyContainer" Lomo/Views/Edit/EditView.swift; then
    echo "✅ EditView正确使用依赖注入容器"
else
    echo "❌ EditView未使用依赖注入容器"
fi

# 7. 统计完成情况
echo ""
echo "📊 步骤5完成情况统计"
echo "=================================================="

completed=0
total=6

# 检查各项完成情况
if grep -q "AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift; then
    ((completed++))
fi

if ! grep -q "getCurrentParametersCopy" Lomo/Views/Edit/AdjustView.swift; then
    ((completed++))
fi

if grep -q "FilterViewModelRefactored" Lomo/Views/Edit/FilterView.swift; then
    ((completed++))
fi

if grep -q "FilterDependencyContainer.filterViewModel()" Lomo/Views/Edit/EditView.swift; then
    ((completed++))
fi

if grep -q "AdjustDependencyContainer.adjustViewModel()" Lomo/Views/Edit/EditView.swift; then
    ((completed++))
fi

if swift build > /dev/null 2>&1; then
    ((completed++))
fi

echo "✅ 完成进度: $completed/$total"
echo "📊 完成率: $((completed * 100 / total))%"

if [ $completed -eq $total ]; then
    echo ""
    echo "🎉 步骤5: 更新View层依赖注入 - 完成！"
    echo "✅ 所有View层已成功更新为使用重构后的ViewModel"
    echo "✅ 依赖注入正确使用"
    echo "✅ 编译通过验证"
    echo "✅ MVVM-S架构重构链条完整"
else
    echo ""
    echo "⚠️ 步骤5: 更新View层依赖注入 - 部分完成"
    echo "需要继续完善剩余项目"
fi

echo ""
echo "🎯 下一步: 步骤6 - 最终验证和优化"
echo "=================================================="