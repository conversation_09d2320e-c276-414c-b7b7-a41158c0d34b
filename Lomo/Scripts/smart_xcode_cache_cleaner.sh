#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 智能 Xcode 缓存清理脚本 - 根据实际需要智能清理

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置参数
DERIVED_DATA_PATH="$HOME/Library/Developer/Xcode/DerivedData"
PROJECT_NAME="Lomo"
MAX_DERIVED_DATA_SIZE_GB=5  # DerivedData 超过 5GB 时清理
MIN_FREE_SPACE_GB=10        # 磁盘剩余空间少于 10GB 时清理
MAX_CACHE_AGE_DAYS=7        # 缓存超过 7 天时清理

echo -e "${CYAN}🧠 智能 Xcode 缓存清理器启动...${NC}"
echo ""

# 函数：获取目录大小（GB）
get_dir_size_gb() {
    local dir="$1"
    if [ -d "$dir" ]; then
        du -sg "$dir" 2>/dev/null | awk '{print $1}'
    else
        echo "0"
    fi
}

# 函数：获取磁盘剩余空间（GB）
get_free_space_gb() {
    df -g . | tail -1 | awk '{print $4}'
}

# 函数：检查文件最后修改时间（天数）
get_file_age_days() {
    local file="$1"
    if [ -f "$file" ] || [ -d "$file" ]; then
        local file_time=$(stat -f %m "$file" 2>/dev/null)
        local current_time=$(date +%s)
        echo $(( (current_time - file_time) / 86400 ))
    else
        echo "999"
    fi
}

# 函数：检查是否存在构建错误指标
check_build_errors() {
    local error_indicators=0
    
    # 检查是否存在损坏的构建数据库
    if find "$DERIVED_DATA_PATH" -name "build.db" -exec file {} \; 2>/dev/null | grep -q "corrupt\|damaged"; then
        echo -e "${RED}🚨 发现损坏的构建数据库${NC}"
        error_indicators=$((error_indicators + 1))
    fi
    
    # 检查是否存在锁定文件（表示异常终止）
    if find "$DERIVED_DATA_PATH" -name "*.lock" 2>/dev/null | grep -q .; then
        echo -e "${YELLOW}⚠️ 发现构建锁定文件（可能异常终止）${NC}"
        error_indicators=$((error_indicators + 1))
    fi
    
    # 检查是否存在过多的临时文件
    local temp_files=$(find "$DERIVED_DATA_PATH" -name "*.tmp" -o -name "*.temp" 2>/dev/null | wc -l)
    if [ "$temp_files" -gt 100 ]; then
        echo -e "${YELLOW}⚠️ 发现过多临时文件 ($temp_files 个)${NC}"
        error_indicators=$((error_indicators + 1))
    fi
    
    return $error_indicators
}

# 函数：智能评估是否需要清理
should_clean_cache() {
    local reasons=()
    local should_clean=false
    
    echo -e "${BLUE}📊 正在分析系统状态...${NC}"
    
    # 1. 检查 DerivedData 大小
    local derived_data_size=$(get_dir_size_gb "$DERIVED_DATA_PATH")
    echo "   DerivedData 大小: ${derived_data_size}GB"
    if [ "$derived_data_size" -gt "$MAX_DERIVED_DATA_SIZE_GB" ]; then
        reasons+=("DerivedData 过大 (${derived_data_size}GB > ${MAX_DERIVED_DATA_SIZE_GB}GB)")
        should_clean=true
    fi
    
    # 2. 检查磁盘剩余空间
    local free_space=$(get_free_space_gb)
    echo "   磁盘剩余空间: ${free_space}GB"
    if [ "$free_space" -lt "$MIN_FREE_SPACE_GB" ]; then
        reasons+=("磁盘空间不足 (${free_space}GB < ${MIN_FREE_SPACE_GB}GB)")
        should_clean=true
    fi
    
    # 3. 检查缓存年龄
    local project_derived_data=$(find "$DERIVED_DATA_PATH" -name "${PROJECT_NAME}-*" -type d | head -1)
    if [ -n "$project_derived_data" ]; then
        local cache_age=$(get_file_age_days "$project_derived_data")
        echo "   项目缓存年龄: ${cache_age}天"
        if [ "$cache_age" -gt "$MAX_CACHE_AGE_DAYS" ]; then
            reasons+=("缓存过期 (${cache_age}天 > ${MAX_CACHE_AGE_DAYS}天)")
            should_clean=true
        fi
    fi
    
    # 4. 检查构建错误指标
    if check_build_errors; then
        reasons+=("检测到构建错误或异常")
        should_clean=true
    fi
    
    # 5. 检查最近是否有构建失败
    if [ -f "/tmp/xcode_build_failed" ]; then
        local fail_age=$(get_file_age_days "/tmp/xcode_build_failed")
        if [ "$fail_age" -lt 1 ]; then
            reasons+=("最近有构建失败记录")
            should_clean=true
        fi
    fi
    
    echo ""
    
    if [ "$should_clean" = true ]; then
        echo -e "${YELLOW}🎯 智能分析结果: 建议清理缓存${NC}"
        echo -e "${YELLOW}📋 清理原因:${NC}"
        for reason in "${reasons[@]}"; do
            echo -e "   ${YELLOW}• $reason${NC}"
        done
        return 0
    else
        echo -e "${GREEN}✅ 智能分析结果: 缓存状态良好，无需清理${NC}"
        return 1
    fi
}

# 函数：执行智能清理
perform_smart_clean() {
    echo ""
    echo -e "${PURPLE}🧹 开始智能清理...${NC}"
    
    # 1. 优先清理项目特定的缓存
    echo -e "${BLUE}1️⃣ 清理项目特定缓存...${NC}"
    local cleaned_size=0
    local project_caches=$(find "$DERIVED_DATA_PATH" -name "${PROJECT_NAME}-*" -type d)
    for cache_dir in $project_caches; do
        if [ -d "$cache_dir" ]; then
            local size=$(get_dir_size_gb "$cache_dir")
            echo "   清理: $(basename "$cache_dir") (${size}GB)"
            rm -rf "$cache_dir"
            cleaned_size=$((cleaned_size + size))
        fi
    done
    
    # 2. 清理过期的其他项目缓存
    echo -e "${BLUE}2️⃣ 清理过期缓存...${NC}"
    find "$DERIVED_DATA_PATH" -maxdepth 1 -type d -mtime +$MAX_CACHE_AGE_DAYS | while read old_cache; do
        if [ "$old_cache" != "$DERIVED_DATA_PATH" ]; then
            local size=$(get_dir_size_gb "$old_cache")
            echo "   清理过期: $(basename "$old_cache") (${size}GB)"
            rm -rf "$old_cache"
            cleaned_size=$((cleaned_size + size))
        fi
    done
    
    # 3. 清理系统缓存（如果磁盘空间紧张）
    local free_space=$(get_free_space_gb)
    if [ "$free_space" -lt "$MIN_FREE_SPACE_GB" ]; then
        echo -e "${BLUE}3️⃣ 清理系统缓存（磁盘空间紧张）...${NC}"
        rm -rf ~/Library/Caches/com.apple.dt.Xcode
        rm -rf ~/Library/Caches/com.apple.dt.XcodeBuild
        rm -rf ~/Library/Caches/com.apple.CoreSimulator.SimulatorTrampoline
        echo "   ✅ 系统缓存清理完成"
    fi
    
    # 4. 清理项目本地构建文件
    echo -e "${BLUE}4️⃣ 清理项目构建文件...${NC}"
    find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
    find . -name ".build" -type d -exec rm -rf {} + 2>/dev/null || true
    echo "   ✅ 项目构建文件清理完成"
    
    echo ""
    echo -e "${GREEN}🎉 智能清理完成！${NC}"
    echo -e "${GREEN}💾 预计释放空间: ${cleaned_size}GB${NC}"
}

# 函数：创建清理记录
create_clean_record() {
    local record_file="$HOME/.xcode_smart_clean_log"
    echo "$(date): 智能清理执行 - $1" >> "$record_file"
}

# 函数：显示使用统计
show_usage_stats() {
    echo ""
    echo -e "${CYAN}📈 使用统计:${NC}"
    
    local record_file="$HOME/.xcode_smart_clean_log"
    if [ -f "$record_file" ]; then
        local total_cleans=$(wc -l < "$record_file")
        local last_clean=$(tail -1 "$record_file" | cut -d: -f1-2)
        echo "   总清理次数: $total_cleans"
        echo "   上次清理: $last_clean"
    else
        echo "   这是首次运行智能清理器"
    fi
    
    echo "   当前 DerivedData 大小: $(get_dir_size_gb "$DERIVED_DATA_PATH")GB"
    echo "   当前磁盘剩余空间: $(get_free_space_gb)GB"
}

# 主逻辑
main() {
    # 检查参数
    case "${1:-auto}" in
        "force")
            echo -e "${YELLOW}🔧 强制清理模式${NC}"
            perform_smart_clean
            create_clean_record "强制清理"
            ;;
        "check")
            echo -e "${BLUE}🔍 仅检查模式${NC}"
            should_clean_cache
            show_usage_stats
            ;;
        "auto"|"")
            # 智能自动模式
            if should_clean_cache; then
                echo ""
                read -p "是否执行智能清理？(y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    perform_smart_clean
                    create_clean_record "智能自动清理"
                else
                    echo -e "${BLUE}ℹ️ 用户取消清理${NC}"
                fi
            fi
            show_usage_stats
            ;;
        "silent")
            # 静默自动清理（用于定时任务）
            if should_clean_cache; then
                perform_smart_clean
                create_clean_record "静默自动清理"
            fi
            ;;
        "help"|"-h"|"--help")
            echo "智能 Xcode 缓存清理器"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  auto    (默认) 智能分析并询问是否清理"
            echo "  force   强制清理所有缓存"
            echo "  check   仅检查状态，不执行清理"
            echo "  silent  静默自动清理（用于定时任务）"
            echo "  help    显示此帮助信息"
            echo ""
            echo "智能清理条件:"
            echo "  • DerivedData 超过 ${MAX_DERIVED_DATA_SIZE_GB}GB"
            echo "  • 磁盘剩余空间少于 ${MIN_FREE_SPACE_GB}GB"
            echo "  • 缓存超过 ${MAX_CACHE_AGE_DAYS} 天"
            echo "  • 检测到构建错误或异常"
            ;;
        *)
            echo -e "${RED}❌ 未知选项: $1${NC}"
            echo "使用 '$0 help' 查看帮助信息"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"