#!/bin/bash

# 最终调试测试脚本
echo "🎯 最终调试测试 - 页面切换水印移除"
echo "================================="

# 检查所有调试点
echo ""
echo "🔍 检查所有调试点..."

FILES_TO_CHECK=(
    "Lomo/Views/Edit/EditView.swift"
    "Lomo/Views/Shared/NavigationTopBar.swift"
    "Lomo/Managers/Edit/WatermarkStyleManager.swift"
)

for file in "${FILES_TO_CHECK[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $(basename "$file") 存在"
    else
        echo "❌ $(basename "$file") 缺失"
    fi
done

# 检查关键调试信息
echo ""
echo "🔍 检查关键调试信息..."

# 1. EditView中的onTabSelected调试
if grep -q "onTabSelected被调用" "Lomo/Views/Edit/EditView.swift" 2>/dev/null; then
    echo "✅ EditView.onTabSelected包含调试信息"
else
    echo "❌ EditView.onTabSelected缺少调试信息"
fi

# 2. NavigationTopBar中的按钮点击调试
if grep -q "NavigationTopBar.*按钮点击" "Lomo/Views/Shared/NavigationTopBar.swift" 2>/dev/null; then
    echo "✅ NavigationTopBar包含按钮点击调试信息"
else
    echo "❌ NavigationTopBar缺少按钮点击调试信息"
fi

# 3. refreshPreviewView的修复
if grep -q "移除水印UI元素" "Lomo/Views/Edit/EditView.swift" 2>/dev/null; then
    echo "✅ refreshPreviewView已修复"
else
    echo "❌ refreshPreviewView未修复"
fi

# 完整的调试流程
echo ""
echo "🎬 完整的调试流程"
echo "================="

echo ""
echo "现在运行应用，按以下步骤测试，并观察控制台日志:"

echo ""
echo "📱 测试步骤:"
echo "  1. 导入一张照片"
echo "  2. 切换到水印页面"
echo "  3. 应用任意水印样式"
echo "  4. 切换到调节页面 (点击调节图标)"

echo ""
echo "🔍 预期的完整日志序列:"
echo "  ┌─ 用户点击调节页面图标"
echo "  │"
echo "  ├─ 🔍 [NavigationTopBar] 编辑模式按钮点击 - 标签: 调节, 值: adjust"
echo "  ├─ 🔍 [NavigationTopBar] 调用onTabSelected(adjust)"
echo "  │"
echo "  ├─ 🔍 [EditView] onTabSelected被调用 - 当前页面: watermark, 新页面: adjust"
echo "  ├─ 🔄 [EditView] 从水印页面切换到adjust，刷新视图移除水印"
echo "  │"
echo "  ├─ 🔍 [EditView] refreshPreviewView() 被调用"
echo "  ├─ 🔄 [EditView] 移除水印UI元素"
echo "  ├─ 🔍 [EditView] refreshPreviewView() 完成"
echo "  │"
echo "  └─ 水印应该立即消失 ✅"

echo ""
echo "❌ 如果没有看到预期日志:"

echo ""
echo "1. 如果没有看到NavigationTopBar的日志:"
echo "   - 检查是否点击了正确的图标"
echo "   - 确认NavigationTopBar使用的是编辑模式"
echo "   - 检查按钮是否被正确渲染"

echo ""
echo "2. 如果看到NavigationTopBar日志但没有EditView日志:"
echo "   - onTabSelected回调可能没有正确传递"
echo "   - 检查EditView中的NavigationTopBar初始化"

echo ""
echo "3. 如果看到所有日志但水印没有消失:"
echo "   - WatermarkManager可能没有正确的预览容器引用"
echo "   - 水印样式的remove()方法可能有问题"
echo "   - 检查WatermarkManagerProvider.shared.watermarkManager是否为nil"

echo ""
echo "🔧 额外调试建议:"

echo ""
echo "如果问题仍然存在，可以添加以下调试代码:"

echo ""
echo "在WatermarkManager.removeCurrentWatermark()中添加:"
echo "  print(\"🔍 [WatermarkManager] removeCurrentWatermark() 被调用\")"
echo "  print(\"   - currentStyle: \\(currentStyle != nil ? \"存在\" : \"nil\")\")"
echo "  print(\"   - previewContainer: \\(previewContainer != nil ? \"存在\" : \"nil\")\")"

echo ""
echo "在水印样式的remove()方法中添加:"
echo "  print(\"🔍 [WatermarkStyle] remove() 被调用\")"

echo ""
echo "🎯 最终目标:"
echo "  ✅ 点击页面切换图标时立即看到完整的日志序列"
echo "  ✅ 水印UI元素立即消失"
echo "  ✅ 页面切换正常工作"
echo "  ✅ 调节和滤镜功能不受影响"

echo ""
echo "🚀 现在运行应用并测试！"
