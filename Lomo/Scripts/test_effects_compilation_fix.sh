#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🔧 特效模块编译错误修复测试"
echo "================================"

# 检查关键文件的编译状态
echo "1️⃣ 检查关键文件编译状态..."

CRITICAL_FILES=(
    "Lomo/DependencyInjection/EffectsDependencyContainer.swift"
    "Lomo/ViewModels/Edit/EditViewModel.swift"
    "Lomo/Services/Edit/EffectsService.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
)

compilation_errors=0
for file in "${CRITICAL_FILES[@]}"; do
    echo "   检查 $(basename $file)..."
    if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
        echo "❌ $(basename $file) 编译失败"
        echo "错误详情:"
        swift -frontend -parse "$file"
        compilation_errors=$((compilation_errors + 1))
    else
        echo "✅ $(basename $file) 编译通过"
    fi
done

if [ $compilation_errors -gt 0 ]; then
    echo ""
    echo "❌ 发现 $compilation_errors 个编译错误"
    exit 1
fi

# 检查EffectsDependencyContainer的@MainActor修复
echo ""
echo "2️⃣ 检查@MainActor修复..."

if grep -q "@MainActor" "Lomo/DependencyInjection/EffectsDependencyContainer.swift"; then
    echo "✅ EffectsDependencyContainer: @MainActor修复已应用"
else
    echo "❌ EffectsDependencyContainer: @MainActor修复未应用"
    exit 1
fi

# 检查EditViewModel的依赖注入修复
echo ""
echo "3️⃣ 检查EditViewModel依赖注入修复..."

if grep -q "effectsService: EffectsServiceProtocol" "Lomo/ViewModels/Edit/EditViewModel.swift"; then
    echo "✅ EditViewModel: 依赖注入已添加"
else
    echo "❌ EditViewModel: 依赖注入未添加"
    exit 1
fi

if ! grep -q "EffectsService.shared" "Lomo/ViewModels/Edit/EditViewModel.swift"; then
    echo "✅ EditViewModel: 单例调用已消除"
else
    echo "❌ EditViewModel: 仍存在单例调用"
    exit 1
fi

# 检查异步调用修复
echo ""
echo "4️⃣ 检查异步调用修复..."

if grep -q "Task {" "Lomo/ViewModels/Edit/EditViewModel.swift" && grep -q "await effectsService" "Lomo/ViewModels/Edit/EditViewModel.swift"; then
    echo "✅ EditViewModel: 异步调用已修复"
else
    echo "❌ EditViewModel: 异步调用未修复"
    exit 1
fi

# 检查方法签名兼容性
echo ""
echo "5️⃣ 检查方法签名兼容性..."

# 检查EffectsService是否有所需的方法
REQUIRED_METHODS=(
    "toggleTimeEnabled"
    "updateTimeStyle"
    "toggleGrainEnabled"
    "updateGrainIntensity"
    "toggleScratchEnabled"
    "updateScratchIntensity"
    "toggleLightLeakEnabled"
    "updateLightLeakIntensity"
)

missing_methods=0
for method in "${REQUIRED_METHODS[@]}"; do
    if grep -q "func $method" "Lomo/Services/Edit/EffectsService.swift"; then
        echo "✅ EffectsService: $method 方法存在"
    else
        echo "❌ EffectsService: $method 方法缺失"
        missing_methods=$((missing_methods + 1))
    fi
done

if [ $missing_methods -gt 0 ]; then
    echo "❌ 发现 $missing_methods 个缺失方法"
    exit 1
fi

# 最终编译测试
echo ""
echo "6️⃣ 最终编译测试..."

echo "   尝试编译所有修复的文件..."
all_files_compile=true
for file in "${CRITICAL_FILES[@]}"; do
    if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
        echo "❌ $(basename $file) 最终编译失败"
        all_files_compile=false
    fi
done

if [ "$all_files_compile" = true ]; then
    echo "✅ 所有文件最终编译通过"
else
    echo "❌ 仍有文件编译失败"
    exit 1
fi

echo ""
echo "🎉 特效模块编译错误修复完成！"
echo "================================"
echo "✅ @MainActor问题已修复"
echo "✅ 单例依赖已消除"
echo "✅ 异步调用已更新"
echo "✅ 方法签名已兼容"
echo "✅ 所有文件编译通过"
echo ""
echo "🚀 特效模块现已完全兼容MVVM-S架构！"