#!/bin/bash

# 修复TextInputOptionView调用脚本
echo "🔄 修复TextInputOptionView调用中的watermarkService参数"
echo "================================"

FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"
BACKUP_FILE="${FILE}.textinput_backup"

# 创建备份
echo "📋 创建备份文件..."
cp "$FILE" "$BACKUP_FILE"
echo "✅ 备份已创建: $BACKUP_FILE"

echo ""
echo "🔄 开始批量修复TextInputOptionView调用..."

# 使用sed批量替换，在watermarkType后面添加watermarkService参数
# 匹配模式：watermarkType: watermarkType,
# 替换为：watermarkType: watermarkType,\n                    watermarkService: watermarkService,

sed -i '' 's/watermarkType: watermarkType,$/watermarkType: watermarkType,\
                        watermarkService: watermarkService,/g' "$FILE"

echo "✅ 已修复所有TextInputOptionView调用"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
    echo ""
    echo "🎉 TextInputOptionView修复完成！"
    echo "================================"
    echo "✅ 所有TextInputOptionView调用已添加watermarkService参数"
    echo "✅ 项目编译正常"
    echo "✅ 备份文件已保存: $BACKUP_FILE"
    echo ""
    echo "📋 如果需要回滚："
    echo "   cp $BACKUP_FILE $FILE"
else
    echo "❌ 项目编译失败，正在回滚..."
    cp "$BACKUP_FILE" "$FILE"
    echo "🔄 已回滚到原始状态"
    echo ""
    echo "编译错误信息："
    swift build
    exit 1
fi
