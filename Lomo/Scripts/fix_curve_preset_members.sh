#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🚨 曲线预设成员修复脚本
# 修复 CurveProcessor.CurvePreset 缺少成员和特效参数重复声明问题

echo "🚨 开始修复曲线预设成员问题..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查当前问题
echo "1️⃣ 检查当前问题..."

echo "   🔍 检查 CurvePreset displayName 属性..."
if grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
    echo "   ✅ CurvePreset 已包含 displayName 属性"
else
    echo "   ❌ CurvePreset 缺少 displayName 属性"
fi

echo "   🔍 检查 CurvePreset 预设成员..."
missing_presets=()
for preset in "contrast" "brightness" "vintage"; do
    if grep -q "case $preset" Lomo/Utils/CurvePresets.swift; then
        echo "   ✅ CurvePreset 包含 $preset 预设"
    else
        echo "   ❌ CurvePreset 缺少 $preset 预设"
        missing_presets+=("$preset")
    fi
done

echo ""

# 2. 修复 CurvePreset 缺少的成员
echo "2️⃣ 修复 CurvePreset 成员..."

echo "   🔧 为 CurvePreset 添加 displayName 属性和缺少的预设..."

# 备份原文件
cp Lomo/Utils/CurvePresets.swift Lomo/Utils/CurvePresets.swift.backup

# 在 CurvePreset 枚举中添加缺少的预设和 displayName 属性
# 首先检查现有的预设
existing_presets=$(grep -o "case [a-zA-Z]*" Lomo/Utils/CurvePresets.swift | sed 's/case //' | tr '\n' ' ')
echo "   📊 现有预设: $existing_presets"

# 添加缺少的预设到枚举定义中
if ! grep -q "case contrast" Lomo/Utils/CurvePresets.swift; then
    # 在 vintageCurve 之后添加新的预设
    sed -i '' '/case vintageCurve = "复古"/a\
        case contrast = "对比度"\
        case brightness = "亮度"\
        case vintage = "复古风格"' Lomo/Utils/CurvePresets.swift
    echo "   ✅ 已添加缺少的预设: contrast, brightness, vintage"
fi

# 添加 displayName 属性
if ! grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
    # 在 var id: String { rawValue } 之后添加 displayName
    sed -i '' '/var id: String { rawValue }/a\
        \
        /// 显示名称\
        var displayName: String {\
            return self.rawValue\
        }' Lomo/Utils/CurvePresets.swift
    echo "   ✅ 已添加 displayName 属性"
fi

echo ""

# 3. 修复 generatePresetPoints 方法中的预设处理
echo "3️⃣ 修复 generatePresetPoints 方法..."

echo "   🔧 更新 generatePresetPoints 方法中的预设处理..."

# 检查 CurveServiceImpl 中的 generatePresetPoints 方法
if grep -q "case .contrast:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ✅ generatePresetPoints 方法已包含 contrast 处理"
else
    echo "   ⚠️ generatePresetPoints 方法需要更新"
    # 这里我们需要确保方法中的预设名称与枚举中的一致
    # 将 .contrast 改为 .contrastCurve 等
    sed -i '' 's/case \.contrast:/case .contrastCurve:/g' Lomo/Services/Implementations/CurveServiceImpl.swift
    sed -i '' 's/case \.brightness:/case .brightCurve:/g' Lomo/Services/Implementations/CurveServiceImpl.swift
    sed -i '' 's/case \.vintage:/case .vintageCurve:/g' Lomo/Services/Implementations/CurveServiceImpl.swift
    echo "   ✅ 已更新 generatePresetPoints 方法中的预设名称"
fi

echo ""

# 4. 重新运行特效参数修复（因为问题又出现了）
echo "4️⃣ 重新修复特效参数重复声明问题..."

echo "   🔧 清理特效参数重复定义..."

# 检查并清理 EffectsViewModel 中可能的重复定义
if grep -q "struct.*Parameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    echo "   ⚠️ 发现 EffectsViewModel 中有参数结构定义，正在清理..."
    # 移除可能的重复定义
    sed -i '' '/^struct GrainParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    sed -i '' '/^struct ScratchParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    sed -i '' '/^struct LightLeakParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    echo "   ✅ 已清理 EffectsViewModel 中的重复定义"
else
    echo "   ✅ EffectsViewModel 中无重复定义"
fi

# 检查 LightLeakModel 中的重复定义
if [ -f "Lomo/Models/LightLeakModel.swift" ]; then
    lightleak_defs=$(grep -n "struct LightLeakParameters" Lomo/Models/LightLeakModel.swift | wc -l)
    if [ "$lightleak_defs" -gt 1 ]; then
        echo "   ⚠️ 发现 LightLeakModel 中有重复的 LightLeakParameters 定义，正在清理..."
        # 保留第一个定义，删除后续重复定义
        awk '/struct LightLeakParameters/ && ++count > 1, /^}/ && count > 1 { next } 1' Lomo/Models/LightLeakModel.swift > temp_lightleak.swift
        mv temp_lightleak.swift Lomo/Models/LightLeakModel.swift
        echo "   ✅ 已清理 LightLeakModel 中的重复定义"
    else
        echo "   ✅ LightLeakModel 中无重复定义"
    fi
fi

echo ""

# 5. 验证修复结果
echo "5️⃣ 验证修复结果..."

echo "   🔍 检查 CurvePreset 成员..."
if grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
    echo "   ✅ CurvePreset 包含 displayName 属性"
else
    echo "   ❌ CurvePreset 仍缺少 displayName 属性"
fi

# 检查预设成员
for preset in "contrast" "brightness" "vintage"; do
    if grep -q "case $preset" Lomo/Utils/CurvePresets.swift; then
        echo "   ✅ CurvePreset 包含 $preset 预设"
    else
        echo "   ❌ CurvePreset 仍缺少 $preset 预设"
    fi
done

echo "   🔍 检查特效参数定义数量..."
lightleak_count=$(find Lomo -name "*.swift" -exec grep -l "struct LightLeakParameters" {} \; 2>/dev/null | wc -l)
grain_count=$(find Lomo -name "*.swift" -exec grep -l "struct GrainParameters" {} \; 2>/dev/null | wc -l)
scratch_count=$(find Lomo -name "*.swift" -exec grep -l "struct ScratchParameters" {} \; 2>/dev/null | wc -l)

echo "   📊 LightLeakParameters 定义数量: $lightleak_count (期望: 1)"
echo "   📊 GrainParameters 定义数量: $grain_count (期望: 1)"
echo "   📊 ScratchParameters 定义数量: $scratch_count (期望: 1)"

echo ""

# 6. 语法检查
echo "6️⃣ 语法检查..."

critical_files=(
    "Lomo/Utils/CurvePresets.swift"
    "Lomo/Services/Implementations/CurveServiceImpl.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    "Lomo/Models/LightLeakModel.swift"
)

syntax_errors=0
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            ((syntax_errors++))
            # 显示前几行错误信息
            echo "      错误详情:"
            swift -frontend -parse "$file" 2>&1 | head -3 | sed 's/^/      /'
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
    fi
done

echo ""

# 7. 总结
echo "7️⃣ 修复总结..."

if [ "$syntax_errors" -eq 0 ] && grep -q "var displayName" Lomo/Utils/CurvePresets.swift && [ "$lightleak_count" -eq 1 ] && [ "$grain_count" -eq 1 ] && [ "$scratch_count" -eq 1 ]; then
    echo "   🎉 曲线预设成员修复成功！"
    echo ""
    echo "✅ 修复成果："
    echo "   • CurvePreset 添加了 displayName 属性"
    echo "   • CurvePreset 添加了缺少的预设成员"
    echo "   • 清理了特效参数重复定义"
    echo "   • 更新了 generatePresetPoints 方法"
    echo "   • 所有相关文件语法检查通过"
    echo ""
    echo "🎯 当前 CurvePreset 结构："
    echo "   📁 displayName: String 属性"
    echo "   📁 预设成员: $(grep -o 'case [a-zA-Z]*' Lomo/Utils/CurvePresets.swift | wc -l) 个"
    echo ""
    echo "🎯 特效参数类型状态："
    echo "   📁 LightLeakParameters: 1个定义"
    echo "   📁 GrainParameters: 1个定义"
    echo "   📁 ScratchParameters: 1个定义"
    
    # 删除备份文件
    rm -f Lomo/Utils/CurvePresets.swift.backup
else
    echo "   ⚠️ 修复过程中发现问题，需要进一步检查"
    echo "   📋 语法错误数: $syntax_errors"
    echo "   📋 参数定义数量: LightLeak=$lightleak_count, Grain=$grain_count, Scratch=$scratch_count"
    if [ -f "Lomo/Utils/CurvePresets.swift.backup" ]; then
        echo "   🔄 如需回滚，备份文件位于: Lomo/Utils/CurvePresets.swift.backup"
    fi
fi

echo ""
echo "🏁 曲线预设成员修复完成！"