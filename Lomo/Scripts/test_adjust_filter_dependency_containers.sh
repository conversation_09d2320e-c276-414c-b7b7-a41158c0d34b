#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 调节和滤镜模块依赖注入容器更新验证脚本
# 验证步骤4: 更新依赖注入容器的完成情况

echo "🔍 调节和滤镜模块依赖注入容器更新验证"
echo "=================================================="

# 1. 检查AdjustDependencyContainer更新
echo ""
echo "1️⃣ 检查AdjustDependencyContainer更新..."

if grep -q "AdjustViewModelRefactored" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "✅ AdjustDependencyContainer已更新为使用AdjustViewModelRefactored"
else
    echo "❌ AdjustDependencyContainer未更新为使用AdjustViewModelRefactored"
fi

if grep -q "AdjustServiceActor" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "✅ AdjustDependencyContainer已更新为使用AdjustServiceActor"
else
    echo "❌ AdjustDependencyContainer未更新为使用AdjustServiceActor"
fi

if grep -q "CurveServiceActor" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "✅ AdjustDependencyContainer已集成CurveServiceActor"
else
    echo "❌ AdjustDependencyContainer未集成CurveServiceActor"
fi

if grep -q "HSLServiceActor" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "✅ AdjustDependencyContainer已集成HSLServiceActor"
else
    echo "❌ AdjustDependencyContainer未集成HSLServiceActor"
fi

# 2. 检查FilterDependencyContainer更新
echo ""
echo "2️⃣ 检查FilterDependencyContainer更新..."

if grep -q "FilterViewModelRefactored" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    echo "✅ FilterDependencyContainer已更新为使用FilterViewModelRefactored"
else
    echo "❌ FilterDependencyContainer未更新为使用FilterViewModelRefactored"
fi

if grep -q "FilterServiceActor" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    echo "✅ FilterDependencyContainer已更新为使用FilterServiceActor"
else
    echo "❌ FilterDependencyContainer未更新为使用FilterServiceActor"
fi

if grep -q "RenderingServiceActor" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    echo "✅ FilterDependencyContainer已集成RenderingServiceActor"
else
    echo "❌ FilterDependencyContainer未集成RenderingServiceActor"
fi

# 3. 检查版权声明
echo ""
echo "3️⃣ 检查版权声明..."

if grep -q "Copyright (c) 2025 LoniceraLab" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "✅ AdjustDependencyContainer包含正确的版权声明"
else
    echo "❌ AdjustDependencyContainer缺少版权声明"
fi

if grep -q "Copyright (c) 2025 LoniceraLab" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    echo "✅ FilterDependencyContainer包含正确的版权声明"
else
    echo "❌ FilterDependencyContainer缺少版权声明"
fi

# 4. 检查依赖注入链
echo ""
echo "4️⃣ 检查依赖注入链..."

# 检查AdjustDependencyContainer的依赖链
adjust_deps=$(grep -c "Service.*:" Lomo/DependencyInjection/AdjustDependencyContainer.swift)
echo "📊 AdjustDependencyContainer管理的服务数量: $adjust_deps"

# 检查FilterDependencyContainer的依赖链
filter_deps=$(grep -c "Service.*:" Lomo/DependencyInjection/FilterDependencyContainer.swift)
echo "📊 FilterDependencyContainer管理的服务数量: $filter_deps"

# 5. 编译验证
echo ""
echo "5️⃣ 编译验证..."

if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译通过"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息:"
    swift build 2>&1 | head -10
fi

# 6. 检查单例消除情况
echo ""
echo "6️⃣ 检查单例消除情况..."

if grep -q "\.shared" Lomo/DependencyInjection/AdjustDependencyContainer.swift | grep -v "SharedService"; then
    echo "⚠️ AdjustDependencyContainer仍有单例使用"
else
    echo "✅ AdjustDependencyContainer已消除业务逻辑单例"
fi

if grep -q "\.shared" Lomo/DependencyInjection/FilterDependencyContainer.swift | grep -v "SharedService"; then
    echo "⚠️ FilterDependencyContainer仍有单例使用"
else
    echo "✅ FilterDependencyContainer已消除业务逻辑单例"
fi

# 7. 总结
echo ""
echo "📋 步骤4完成情况总结"
echo "=================================================="

# 统计完成项目
completed=0
total=8

# 检查各项完成情况
if grep -q "AdjustViewModelRefactored" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    ((completed++))
fi

if grep -q "AdjustServiceActor" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    ((completed++))
fi

if grep -q "FilterViewModelRefactored" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    ((completed++))
fi

if grep -q "FilterServiceActor" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    ((completed++))
fi

if grep -q "Copyright (c) 2025 LoniceraLab" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    ((completed++))
fi

if grep -q "Copyright (c) 2025 LoniceraLab" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    ((completed++))
fi

if swift build > /dev/null 2>&1; then
    ((completed++))
fi

if ! grep -q "\.shared" Lomo/DependencyInjection/AdjustDependencyContainer.swift | grep -v "SharedService" > /dev/null 2>&1; then
    ((completed++))
fi

echo "✅ 完成进度: $completed/$total"
echo "📊 完成率: $((completed * 100 / total))%"

if [ $completed -eq $total ]; then
    echo ""
    echo "🎉 步骤4: 更新依赖注入容器 - 完成！"
    echo "✅ 所有依赖注入容器已成功更新为MVVM-S架构"
    echo "✅ Actor模式服务已正确集成"
    echo "✅ 重构后的ViewModel已正确使用"
    echo "✅ 单例依赖已完全消除"
else
    echo ""
    echo "⚠️ 步骤4: 更新依赖注入容器 - 部分完成"
    echo "需要继续完善剩余项目"
fi

echo ""
echo "🎯 下一步: 步骤5 - 更新View层依赖注入"
echo "=================================================="