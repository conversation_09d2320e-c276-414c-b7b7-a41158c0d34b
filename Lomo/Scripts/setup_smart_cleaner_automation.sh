#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 智能清理器自动化设置脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SMART_CLEANER="$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"
PLIST_NAME="com.lonicera.xcode.smart.cleaner"
PLIST_PATH="$HOME/Library/LaunchAgents/$PLIST_NAME.plist"

echo -e "${CYAN}🤖 智能清理器自动化设置${NC}"
echo ""

# 函数：创建 LaunchAgent plist 文件
create_launch_agent() {
    local interval="$1"
    local interval_seconds=$((interval * 3600)) # 转换为秒
    
    cat > "$PLIST_PATH" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>$PLIST_NAME</string>
    <key>ProgramArguments</key>
    <array>
        <string>$SMART_CLEANER</string>
        <string>silent</string>
    </array>
    <key>StartInterval</key>
    <integer>$interval_seconds</integer>
    <key>RunAtLoad</key>
    <false/>
    <key>StandardOutPath</key>
    <string>$HOME/Library/Logs/xcode_smart_cleaner.log</string>
    <key>StandardErrorPath</key>
    <string>$HOME/Library/Logs/xcode_smart_cleaner_error.log</string>
</dict>
</plist>
EOF
    
    echo -e "${GREEN}✅ 创建 LaunchAgent 配置文件: $PLIST_PATH${NC}"
}

# 函数：设置 Git hooks
setup_git_hooks() {
    local hooks_dir=".git/hooks"
    local pre_commit_hook="$hooks_dir/pre-commit"
    
    if [ ! -d "$hooks_dir" ]; then
        echo -e "${YELLOW}⚠️ 未找到 Git hooks 目录，跳过 Git hooks 设置${NC}"
        return
    fi
    
    # 创建 pre-commit hook
    cat > "$pre_commit_hook" << 'EOF'
#!/bin/bash
# 智能检查是否需要清理 Xcode 缓存

SCRIPT_DIR="$(git rev-parse --show-toplevel)/Lomo/Scripts"
SMART_CLEANER="$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"

if [ -x "$SMART_CLEANER" ]; then
    # 静默检查，如果需要清理则提醒
    if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
        echo "💡 提示: 检测到 Xcode 缓存可能需要清理"
        echo "   运行: ./Lomo/Scripts/smart_xcode_cache_cleaner.sh"
        echo ""
    fi
fi
EOF
    
    chmod +x "$pre_commit_hook"
    echo -e "${GREEN}✅ 设置 Git pre-commit hook${NC}"
}

# 函数：创建 Xcode 构建脚本集成
setup_xcode_integration() {
    local build_script="Lomo/Scripts/xcode_build_with_smart_clean.sh"
    
    cat > "$build_script" << EOF
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# Xcode 构建前智能清理脚本

SCRIPT_DIR="\$(cd "\$(dirname "\${BASH_SOURCE[0]}")" && pwd)"
SMART_CLEANER="\$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"

echo "🔍 构建前智能检查..."

# 检查是否需要清理
if "\$SMART_CLEANER" check | grep -q "建议清理缓存"; then
    echo "🧹 检测到需要清理缓存，自动执行清理..."
    "\$SMART_CLEANER" silent
    echo "✅ 清理完成，继续构建..."
else
    echo "✅ 缓存状态良好，直接构建..."
fi

# 记录构建开始时间（用于失败检测）
echo "\$(date): 构建开始" > /tmp/xcode_build_start

# 这里可以添加实际的构建命令
# xcodebuild -project Lomo.xcodeproj -scheme Lomo build
EOF
    
    chmod +x "$build_script"
    echo -e "${GREEN}✅ 创建 Xcode 构建集成脚本: $build_script${NC}"
}

# 函数：创建 shell 别名
setup_shell_aliases() {
    local shell_rc=""
    
    # 检测使用的 shell
    if [ -n "$ZSH_VERSION" ]; then
        shell_rc="$HOME/.zshrc"
    elif [ -n "$BASH_VERSION" ]; then
        shell_rc="$HOME/.bashrc"
    else
        shell_rc="$HOME/.profile"
    fi
    
    # 添加别名
    local aliases="
# LoniceraLab Xcode 智能清理器别名
alias xclean='$SMART_CLEANER'
alias xcheck='$SMART_CLEANER check'
alias xforce='$SMART_CLEANER force'
"
    
    if ! grep -q "LoniceraLab Xcode 智能清理器" "$shell_rc" 2>/dev/null; then
        echo "$aliases" >> "$shell_rc"
        echo -e "${GREEN}✅ 添加 shell 别名到: $shell_rc${NC}"
        echo -e "${BLUE}   使用方法: xclean, xcheck, xforce${NC}"
    else
        echo -e "${YELLOW}⚠️ Shell 别名已存在${NC}"
    fi
}

# 主菜单
show_menu() {
    echo -e "${BLUE}请选择自动化设置选项:${NC}"
    echo "1) 设置定时自动检查（每 6 小时）"
    echo "2) 设置定时自动检查（每 12 小时）"
    echo "3) 设置定时自动检查（每 24 小时）"
    echo "4) 设置 Git hooks 集成"
    echo "5) 设置 Xcode 构建集成"
    echo "6) 设置 Shell 别名"
    echo "7) 全部设置"
    echo "8) 移除自动化设置"
    echo "9) 查看当前状态"
    echo "0) 退出"
    echo ""
}

# 主逻辑
main() {
    if [ ! -x "$SMART_CLEANER" ]; then
        echo -e "${RED}❌ 未找到智能清理器脚本: $SMART_CLEANER${NC}"
        exit 1
    fi
    
    while true; do
        show_menu
        read -p "请输入选择 (0-9): " choice
        echo ""
        
        case $choice in
            1)
                create_launch_agent 6
                launchctl load "$PLIST_PATH" 2>/dev/null || true
                echo -e "${GREEN}✅ 设置每 6 小时自动检查${NC}"
                ;;
            2)
                create_launch_agent 12
                launchctl load "$PLIST_PATH" 2>/dev/null || true
                echo -e "${GREEN}✅ 设置每 12 小时自动检查${NC}"
                ;;
            3)
                create_launch_agent 24
                launchctl load "$PLIST_PATH" 2>/dev/null || true
                echo -e "${GREEN}✅ 设置每 24 小时自动检查${NC}"
                ;;
            4)
                setup_git_hooks
                ;;
            5)
                setup_xcode_integration
                ;;
            6)
                setup_shell_aliases
                echo -e "${YELLOW}💡 请运行 'source ~/.zshrc' 或重启终端以生效${NC}"
                ;;
            7)
                create_launch_agent 12
                launchctl load "$PLIST_PATH" 2>/dev/null || true
                setup_git_hooks
                setup_xcode_integration
                setup_shell_aliases
                echo -e "${GREEN}🎉 全部自动化设置完成！${NC}"
                ;;
            8)
                launchctl unload "$PLIST_PATH" 2>/dev/null || true
                rm -f "$PLIST_PATH"
                rm -f ".git/hooks/pre-commit"
                echo -e "${GREEN}✅ 移除自动化设置完成${NC}"
                ;;
            9)
                echo -e "${BLUE}📊 当前自动化状态:${NC}"
                if [ -f "$PLIST_PATH" ]; then
                    echo -e "   ${GREEN}✅ LaunchAgent 已设置${NC}"
                    if launchctl list | grep -q "$PLIST_NAME"; then
                        echo -e "   ${GREEN}✅ 定时任务正在运行${NC}"
                    else
                        echo -e "   ${YELLOW}⚠️ 定时任务未运行${NC}"
                    fi
                else
                    echo -e "   ${RED}❌ LaunchAgent 未设置${NC}"
                fi
                
                if [ -f ".git/hooks/pre-commit" ]; then
                    echo -e "   ${GREEN}✅ Git hooks 已设置${NC}"
                else
                    echo -e "   ${RED}❌ Git hooks 未设置${NC}"
                fi
                
                if [ -f "Lomo/Scripts/xcode_build_with_smart_clean.sh" ]; then
                    echo -e "   ${GREEN}✅ Xcode 构建集成已设置${NC}"
                else
                    echo -e "   ${RED}❌ Xcode 构建集成未设置${NC}"
                fi
                ;;
            0)
                echo -e "${CYAN}👋 再见！${NC}"
                break
                ;;
            *)
                echo -e "${RED}❌ 无效选择，请重新输入${NC}"
                ;;
        esac
        echo ""
    done
}

# 执行主函数
main "$@"