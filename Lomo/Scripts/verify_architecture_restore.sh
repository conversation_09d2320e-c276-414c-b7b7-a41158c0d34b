#!/bin/bash

# 架构恢复验证脚本
echo "🔄 架构恢复验证"
echo "==============="

# 检查EditView中的预览逻辑
echo ""
echo "🔍 检查EditView预览逻辑..."

EDIT_VIEW_FILE="Lomo/Views/Edit/EditView.swift"

if [ -f "$EDIT_VIEW_FILE" ]; then
    echo "✅ EditView.swift 文件存在"
    
    # 检查是否使用统一的MockPreviewView
    mock_preview_usage=$(grep -c "MockPreviewView(" "$EDIT_VIEW_FILE" 2>/dev/null || echo "0")
    if [ "$mock_preview_usage" -eq 1 ]; then
        echo "✅ 使用统一的MockPreviewView ($mock_preview_usage 处)"
    else
        echo "⚠️ MockPreviewView使用次数: $mock_preview_usage"
    fi
    
    # 检查是否移除了MetalFilterView的直接使用
    metal_filter_usage=$(grep -c "MetalFilterView(" "$EDIT_VIEW_FILE" 2>/dev/null || echo "0")
    if [ "$metal_filter_usage" -eq 0 ]; then
        echo "✅ 已移除MetalFilterView的直接使用"
    else
        echo "⚠️ 仍有MetalFilterView使用: $metal_filter_usage 处"
    fi
    
    # 检查预览容器传递
    preview_container_usage=$(grep -c "previewContainer: previewProvider.previewContainer" "$EDIT_VIEW_FILE" 2>/dev/null || echo "0")
    if [ "$preview_container_usage" -gt 0 ]; then
        echo "✅ 预览容器正确传递 ($preview_container_usage 处)"
    else
        echo "❌ 预览容器传递缺失"
    fi
    
else
    echo "❌ EditView.swift 文件不存在"
fi

# 检查ImageRenderingService中的预览容器设置
echo ""
echo "🔍 检查ImageRenderingService预览容器设置..."

IMAGE_RENDERING_FILE="Lomo/Services/Implementations/ImageRenderingService.swift"

if [ -f "$IMAGE_RENDERING_FILE" ]; then
    echo "✅ ImageRenderingService.swift 文件存在"
    
    # 检查预览容器设置逻辑
    preview_provider_setup=$(grep -c "provider.setPreviewContainer" "$IMAGE_RENDERING_FILE" 2>/dev/null || echo "0")
    if [ "$preview_provider_setup" -gt 0 ]; then
        echo "✅ 预览容器设置逻辑存在 ($preview_provider_setup 处)"
    else
        echo "❌ 预览容器设置逻辑缺失"
    fi
    
else
    echo "❌ ImageRenderingService.swift 文件不存在"
fi

# 检查WatermarkControlView的调试信息
echo ""
echo "🔍 检查WatermarkControlView调试信息..."

WATERMARK_CONTROL_FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"

if [ -f "$WATERMARK_CONTROL_FILE" ]; then
    echo "✅ WatermarkControlView.swift 文件存在"
    
    # 检查调试信息
    debug_info=$(grep -c "延迟初始化水印" "$WATERMARK_CONTROL_FILE" 2>/dev/null || echo "0")
    if [ "$debug_info" -gt 0 ]; then
        echo "✅ 调试信息已添加 ($debug_info 处)"
    else
        echo "⚠️ 调试信息缺失"
    fi
    
else
    echo "❌ WatermarkControlView.swift 文件不存在"
fi

# 分析架构恢复
echo ""
echo "🎯 架构恢复分析"
echo "==============="

echo ""
echo "1. 🔄 原始架构 (工作正常):"
echo "   - 相机模式: MockPreviewView → 设置预览容器 ✅"
echo "   - 照片模式: MockPreviewView + selectedImage → 设置预览容器 ✅"

echo ""
echo "2. 🚫 重构后架构 (水印失效):"
echo "   - 相机模式: MockPreviewView → 设置预览容器 ✅"
echo "   - 照片模式: MetalFilterView → 没有设置预览容器 ❌"

echo ""
echo "3. ✅ 恢复后架构 (应该正常):"
echo "   - 相机模式: MockPreviewView → 设置预览容器 ✅"
echo "   - 照片模式: MockPreviewView + selectedImage → ImageRenderingService → 设置预览容器 ✅"

echo ""
echo "4. 🔗 预览容器设置流程:"
echo "   - MockPreviewView.makeUIView() → ImageRenderingService.displayImage()"
echo "   - ImageRenderingService → provider.setPreviewContainer(parentView)"
echo "   - WatermarkControlView → 接收previewProvider.previewContainer"

echo ""
echo "5. 🎨 滤镜功能保持:"
echo "   - FilterStateManager.setOriginalImage() 在 EditView.onAppear 中调用"
echo "   - 滤镜处理通过FilterStateManager工作，不依赖MetalFilterView"

# 预期结果
echo ""
echo "📈 预期修复效果"
echo "==============="

echo ""
echo "修复前:"
echo "  📹 相机预览: 水印正常 ✅"
echo "  📸 照片模式: 水印失败 ❌ (previewContainer == nil)"

echo ""
echo "修复后:"
echo "  📹 相机预览: 水印正常 ✅"
echo "  📸 照片模式: 水印正常 ✅ (previewContainer 正确设置)"

echo ""
echo "🧪 测试建议"
echo "==========="

echo ""
echo "1. 运行应用并导入一张照片"
echo "2. 切换到水印页面"
echo "3. 应用任意水印样式"
echo "4. 观察控制台日志，应该看到:"
echo "   - 🔍 [WatermarkControlView] 延迟初始化水印 - 容器尺寸: ..."
echo "   - ✅ WatermarkControlView.init: 应用水印样式，类型: ..."

echo ""
echo "5. 不应该再看到:"
echo "   - ⚠️ WatermarkControlView.init: 预览容器无效或未完全加载"

echo ""
echo "🎉 如果修复成功，照片模式下的水印功能应该完全恢复正常！"
