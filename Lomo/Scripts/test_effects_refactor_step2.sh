#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🎨 特效模块MVVM-S重构 - 阶段2测试"
echo "=================================="

# 检查重构后的核心文件
echo "1️⃣ 检查重构后的核心文件..."

CORE_FILES=(
    "Lomo/Services/Edit/EffectsService.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    "Lomo/DependencyInjection/EffectsDependencyContainer.swift"
)

for file in "${CORE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $(basename $file) 存在"
    else
        echo "❌ $(basename $file) 缺失"
        exit 1
    fi
done

# 检查版权声明
echo ""
echo "2️⃣ 检查版权声明..."

check_copyright() {
    local file="$1"
    local expected="// Copyright (c) 2025 LoniceraLab. All rights reserved."
    
    if [ -f "$file" ]; then
        local first_line=$(head -n 1 "$file")
        if [ "$first_line" = "$expected" ]; then
            echo "✅ $(basename $file): 版权格式正确"
        else
            echo "❌ $(basename $file): 版权格式错误"
            return 1
        fi
    fi
}

for file in "${CORE_FILES[@]}"; do
    check_copyright "$file" || exit 1
done

# 检查EffectsService的Actor实现
echo ""
echo "3️⃣ 检查EffectsService的Actor实现..."

EFFECTS_SERVICE="Lomo/Services/Edit/EffectsService.swift"

if grep -q "actor EffectsService: EffectsServiceProtocol" "$EFFECTS_SERVICE"; then
    echo "✅ EffectsService: 正确实现Actor模式"
else
    echo "❌ EffectsService: 未正确实现Actor模式"
    exit 1
fi

if ! grep -q "static let shared" "$EFFECTS_SERVICE"; then
    echo "✅ EffectsService: 已消除单例模式"
else
    echo "❌ EffectsService: 仍存在单例模式"
    exit 1
fi

if grep -q "init(lightLeakService:" "$EFFECTS_SERVICE" && grep -q "grainService:" "$EFFECTS_SERVICE" && grep -q "scratchService:" "$EFFECTS_SERVICE" && grep -q "storageService:" "$EFFECTS_SERVICE"; then
    echo "✅ EffectsService: 正确实现依赖注入"
else
    echo "❌ EffectsService: 依赖注入实现不正确"
    exit 1
fi

# 检查EffectsViewModel的MVVM实现
echo ""
echo "4️⃣ 检查EffectsViewModel的MVVM实现..."

EFFECTS_VIEWMODEL="Lomo/ViewModels/Edit/EffectsViewModel.swift"

if grep -q "@MainActor" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确使用@MainActor"
else
    echo "❌ EffectsViewModel: 未使用@MainActor"
    exit 1
fi

if grep -q "class EffectsViewModel: ObservableObject" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确继承ObservableObject"
else
    echo "❌ EffectsViewModel: 未正确继承ObservableObject"
    exit 1
fi

if grep -q "private let effectsService: EffectsServiceProtocol" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确使用协议依赖注入"
else
    echo "❌ EffectsViewModel: 依赖注入不正确"
    exit 1
fi

if grep -q "@Published.*state.*ViewState" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确实现状态管理"
else
    echo "❌ EffectsViewModel: 状态管理实现不正确"
    exit 1
fi

# 检查DependencyContainer的真正依赖注入
echo ""
echo "5️⃣ 检查DependencyContainer的依赖注入..."

DEPENDENCY_CONTAINER="Lomo/DependencyInjection/EffectsDependencyContainer.swift"

if ! grep -q "EffectsService.shared" "$DEPENDENCY_CONTAINER"; then
    echo "✅ DependencyContainer: 已消除单例依赖"
else
    echo "❌ DependencyContainer: 仍存在单例依赖"
    exit 1
fi

if grep -q "EffectsService(" "$DEPENDENCY_CONTAINER"; then
    echo "✅ DependencyContainer: 正确创建Service实例"
else
    echo "❌ DependencyContainer: Service实例创建不正确"
    exit 1
fi

if grep -q "lightLeakService: lightLeakService" "$DEPENDENCY_CONTAINER" && grep -q "grainService: grainService" "$DEPENDENCY_CONTAINER" && grep -q "scratchService: scratchService" "$DEPENDENCY_CONTAINER" && grep -q "storageService: storageService" "$DEPENDENCY_CONTAINER"; then
    echo "✅ DependencyContainer: 正确注入所有依赖"
else
    echo "❌ DependencyContainer: 依赖注入不完整"
    exit 1
fi

# 语法检查
echo ""
echo "6️⃣ Swift语法检查..."

for file in "${CORE_FILES[@]}"; do
    echo "   检查 $(basename $file) 语法..."
    if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
        echo "❌ $(basename $file) 语法错误"
        swift -frontend -parse "$file"
        exit 1
    fi
done

echo "✅ 所有文件语法检查通过"

# 检查异步方法实现
echo ""
echo "7️⃣ 检查异步方法实现..."

if grep -q "func.*async" "$EFFECTS_SERVICE"; then
    echo "✅ EffectsService: 正确实现异步方法"
else
    echo "❌ EffectsService: 缺少异步��法"
    exit 1
fi

if grep -q "Task {" "$EFFECTS_VIEWMODEL" && grep -q "await" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确使用异步调用"
else
    echo "❌ EffectsViewModel: 异步调用实现不正确"
    exit 1
fi

# 检查错误处理
echo ""
echo "8️⃣ 检查错误处理..."

if grep -q "throws" "$EFFECTS_SERVICE" && grep -q "catch" "$EFFECTS_SERVICE"; then
    echo "✅ EffectsService: 正确实现错误处理"
else
    echo "❌ EffectsService: 错误处理不完整"
    exit 1
fi

if grep -q "\.error(" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确实现错误状态管理"
else
    echo "❌ EffectsViewModel: 错误状态管理不完整"
    exit 1
fi

# 检查状态管理
echo ""
echo "9️⃣ 检查状态管理..."

if grep -q "@Published.*state.*ViewState" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: 正确实现ViewState模式"
else
    echo "❌ EffectsViewModel: ViewState模式实现不正确"
    exit 1
fi

if grep -q "case idle\|case loading\|case loaded\|case error" "$EFFECTS_VIEWMODEL"; then
    echo "✅ EffectsViewModel: ViewState枚举定义完整"
else
    echo "❌ EffectsViewModel: ViewState枚举定义不完整"
    exit 1
fi

echo ""
echo "🎉 阶段2测试完成！"
echo "=================================="
echo "✅ EffectsService重构为Actor模式"
echo "✅ EffectsViewModel实现MVVM架构"
echo "✅ DependencyContainer实现真正依赖注入"
echo "✅ 消除所有单例依赖"
echo "✅ 实现异步并发安全"
echo "✅ 完善错误处理机制"
echo "✅ 统一状态管理模式"
echo "✅ 版权声明格式正确"
echo "✅ Swift语法检查通过"
echo ""
echo "📝 下一步: 执行阶段3 - 更新View层和最终验证"