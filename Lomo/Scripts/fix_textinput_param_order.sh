#!/bin/bash

# 修复TextInputOptionView参数顺序脚本
echo "🔄 修复TextInputOptionView参数顺序"
echo "================================"

FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"
BACKUP_FILE="${FILE}.param_order_backup"

# 创建备份
echo "📋 创建备份文件..."
cp "$FILE" "$BACKUP_FILE"
echo "✅ 备份已创建: $BACKUP_FILE"

echo ""
echo "🔍 检查当前编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 当前编译成功，无需修复"
    exit 0
else
    echo "❌ 发现编译错误，开始修复参数顺序..."
fi

echo ""
echo "🔄 开始修复参数顺序..."

# 使用sed来修复参数顺序问题
# 查找模式：watermarkService: watermarkService, 在 onUpdateSetting 之前
# 替换为：将 watermarkService 移到 onApplyStyle 之后

# 创建临时文件
TEMP_FILE="${FILE}.temp"
cp "$FILE" "$TEMP_FILE"

# 使用awk来重新排列参数顺序
awk '
BEGIN { 
    in_textinput_call = 0
    call_content = ""
    indent = ""
}

# 检测TextInputOptionView调用开始
/TextInputOptionView\(/ {
    in_textinput_call = 1
    call_content = $0
    # 提取缩进
    match($0, /^[[:space:]]*/)
    indent = substr($0, RSTART, RLENGTH)
    next
}

# 在TextInputOptionView调用中收集内容
in_textinput_call && !/^\s*\)/ {
    call_content = call_content "\n" $0
    next
}

# 检测TextInputOptionView调用结束
in_textinput_call && /^\s*\)/ {
    call_content = call_content "\n" $0
    
    # 检查是否有参数顺序问题
    if (match(call_content, /watermarkService: watermarkService,.*onUpdateSetting:/)) {
        # 需要重新排列参数
        # 提取各个参数
        gsub(/watermarkService: watermarkService,/, "WATERMARK_SERVICE_PLACEHOLDER", call_content)
        gsub(/onApplyStyle: \{[^}]*\},/, "&\nWATERMARK_SERVICE_PLACEHOLDER,", call_content)
        gsub(/WATERMARK_SERVICE_PLACEHOLDER,/, "watermarkService: watermarkService,", call_content)
        gsub(/WATERMARK_SERVICE_PLACEHOLDER/, "", call_content)
    }
    
    print call_content
    in_textinput_call = 0
    call_content = ""
    next
}

# 其他行直接输出
!in_textinput_call { print }
' "$TEMP_FILE" > "$FILE"

# 清理临时文件
rm "$TEMP_FILE"

echo "✅ 参数顺序修复完成"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
    echo ""
    echo "🎉 参数顺序修复完成！"
    echo "================================"
    echo "✅ 所有TextInputOptionView参数顺序正确"
    echo "✅ 项目编译正常"
    echo "✅ 备份文件已保存: $BACKUP_FILE"
    echo ""
    echo "📋 如果需要回滚："
    echo "   cp $BACKUP_FILE $FILE"
else
    echo "❌ 项目编译仍有问题，需要手动检查..."
    echo ""
    echo "编译错误信息："
    swift build 2>&1 | head -10
    echo ""
    echo "🔄 可以回滚到原始状态："
    echo "   cp $BACKUP_FILE $FILE"
fi
