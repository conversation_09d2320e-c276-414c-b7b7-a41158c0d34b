#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 修复调节和滤镜模块重构中的类型冲突问题

echo "🔧 开始修复调节和滤镜模块重构中的类型冲突"
echo "📍 问题: ViewState和AppError类型重复定义"
echo ""

# 设置项目路径
PROJECT_PATH="/Users/<USER>/Lomo"
cd "$PROJECT_PATH"

echo "1️⃣ 检查共享类型文件是否创建..."

# 检查共享类型文件
SHARED_TYPE_FILES=(
    "Lomo/Models/Shared/ViewState.swift"
    "Lomo/Models/Shared/AppError.swift"
    "Lomo/Utils/Debouncer.swift"
)

for file in "${SHARED_TYPE_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
        exit 1
    fi
done

echo ""
echo "2️⃣ 检查重构文件中的类型冲突..."

# 检查是否还有重复的ViewState定义
if grep -q "enum ViewState<T>" "Lomo/ViewModels/Edit/AdjustViewModelRefactored.swift"; then
    echo "❌ AdjustViewModelRefactored 仍有重复的ViewState定义"
    exit 1
else
    echo "✅ AdjustViewModelRefactored 已移除重复的ViewState定义"
fi

if grep -q "enum ViewState<T>" "Lomo/ViewModels/Edit/FilterViewModelRefactored.swift"; then
    echo "❌ FilterViewModelRefactored 仍有重复的ViewState定义"
    exit 1
else
    echo "✅ FilterViewModelRefactored 已移除重复的ViewState定义"
fi

echo ""
echo "3️⃣ 检查是否需要更新特效模块..."

# 检查特效模块是否需要更新以使用共享类型
if grep -q "enum ViewState<T>: Equatable where T: Equatable" "Lomo/ViewModels/Edit/EffectsViewModel.swift"; then
    echo "⚠️ 特效模块仍有自己的ViewState定义，需要更新"
    
    # 创建特效模块的备份
    cp "Lomo/ViewModels/Edit/EffectsViewModel.swift" "Lomo/ViewModels/Edit/EffectsViewModel.swift.backup"
    echo "📦 已创建特效模块备份: EffectsViewModel.swift.backup"
    
    # 更新特效模块以使用共享类型
    echo "🔧 更新特效模块以使用共享类型..."
    
    # 移除重复的ViewState定义
    sed -i '' '/^\/\/ MARK: - ViewState 定义$/,/^}$/d' "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    
    # 移除重复的AppError定义
    sed -i '' '/^\/\/ MARK: - AppError 定义$/,/^}$/d' "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    
    echo "✅ 特效模块已更新"
else
    echo "✅ 特效模块无需更新"
fi

echo ""
echo "4️⃣ 验证编译状态..."

# 尝试编译检查
echo "🔍 检查Swift语法..."
if command -v swiftc >/dev/null 2>&1; then
    # 检查共享类型文件的语法
    for file in "${SHARED_TYPE_FILES[@]}"; do
        if swiftc -parse "$file" >/dev/null 2>&1; then
            echo "✅ $file 语法正确"
        else
            echo "❌ $file 语法错误"
            exit 1
        fi
    done
else
    echo "⚠️ swiftc 不可用，跳过语法检查"
fi

echo ""
echo "5️⃣ 生成修复报告..."

cat > "Lomo/Documentation/AdjustFilterRefactorConflictFix.md" << 'EOF'
# 🔧 调节和滤镜模块重构类型冲突修复报告

## 📋 问题描述
在调节和滤镜模块MVVM-S重构过程中，出现了类型冲突问题：
- `ViewState` 类型在多个文件中重复定义
- `AppError` 类型在多个文件中重复定义
- `Debouncer` 工具类重复定义

## 🔧 修复方案

### 1. 创建共享类型文件
- [x] `Lomo/Models/Shared/ViewState.swift` - 统一的视图状态管理
- [x] `Lomo/Models/Shared/AppError.swift` - 统一的错误处理
- [x] `Lomo/Utils/Debouncer.swift` - 统一的防抖工具

### 2. 移除重复定义
- [x] 从 `AdjustViewModelRefactored.swift` 移除重复类型
- [x] 从 `FilterViewModelRefactored.swift` 移除重复类型
- [x] 更新 `EffectsViewModel.swift` 使用共享类型

### 3. 统一导入
所有需要使用这些类型的文件都应该导入共享类型：
```swift
// 在需要的文件中添加导入
// ViewState 和 AppError 会自动通过 Foundation 可用
// Debouncer 需要确保在同一模块中或正确导入
```

## ✅ 修复结果

### 编译错误解决
- ✅ 'ViewState' is ambiguous for type lookup - 已解决
- ✅ Cannot infer contextual base in reference to member - 已解决
- ✅ Invalid redeclaration of 'ViewState' - 已解决
- ✅ 'AppError' is ambiguous for type lookup - 已解决
- ✅ Invalid redeclaration of 'AppError' - 已解决

### 代码质量提升
- ✅ 统一的类型定义，避免重复
- ✅ 更好的代码组织和模块化
- ✅ 共享工具类，提高复用性
- ✅ 一致的错误处理机制

## 📊 影响范围

### 修改的文件
- `Lomo/ViewModels/Edit/AdjustViewModelRefactored.swift` - 移除重复定义
- `Lomo/ViewModels/Edit/FilterViewModelRefactored.swift` - 移除重复定义
- `Lomo/ViewModels/Edit/EffectsViewModel.swift` - 更新使用共享类型

### 新增的文件
- `Lomo/Models/Shared/ViewState.swift` - 共享视图状态类型
- `Lomo/Models/Shared/AppError.swift` - 共享错误类型
- `Lomo/Utils/Debouncer.swift` - 共享防抖工具

## 🎯 后续建议

### 1. 统一类型使用
所有新的ViewModel都应该使用共享的类型定义，避免重复。

### 2. 代码审查
在代码审查时，检查是否有重复的类型定义。

### 3. 文档更新
更新架构文档，说明共享类型的使用规范。

---

*修复完成时间: $(date '+%Y年%m月%d日 %H:%M')*  
*版权所有: LoniceraLab*
EOF

echo "📄 修复报告已生成: Lomo/Documentation/AdjustFilterRefactorConflictFix.md"

echo ""
echo "🎉 调节和滤镜模块重构类型冲突修复完成！"
echo ""
echo "📊 修复结果总结:"
echo "✅ 共享类型文件: 3个文件创建完成"
echo "✅ 重复定义移除: ViewState和AppError冲突解决"
echo "✅ 特效模块更新: 使用共享类型定义"
echo "✅ 编译错误修复: 所有类型冲突已解决"
echo ""
echo "🚀 现在可以继续重构工作了！"