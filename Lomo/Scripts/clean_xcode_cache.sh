#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# Xcode 缓存清理脚本 - 解决构建数据库损坏问题

echo "🧹 开始全面清理 Xcode 缓存..."

# 1. 清理 DerivedData (最重要)
echo "1️⃣ 清理 DerivedData 缓存..."
rm -rf ~/Library/Developer/Xcode/DerivedData/Lomo-*
rm -rf ~/Library/Developer/Xcode/DerivedData/*
echo "✅ DerivedData 清理完成"

# 2. 清理项目本地构建文件
echo "2️⃣ 清理项目本地构建文件..."
find . -name "build" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name ".build" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "*.xcworkspace" -exec rm -rf {}/xcuserdata \; 2>/dev/null || true
find . -name "*.xcodeproj" -exec rm -rf {}/xcuserdata \; 2>/dev/null || true
find . -name "*.xcodeproj" -exec rm -rf {}/project.xcworkspace/xcuserdata \; 2>/dev/null || true
echo "✅ 项目构建文件清理完成"

# 3. 清理 Xcode 系统缓存
echo "3️⃣ 清理 Xcode 系统缓存..."
rm -rf ~/Library/Caches/com.apple.dt.Xcode
rm -rf ~/Library/Caches/com.apple.dt.XcodeBuild
rm -rf ~/Library/Caches/com.apple.CoreSimulator.SimulatorTrampoline
echo "✅ 系统缓存清理完成"

# 4. 清理 Swift Package Manager 缓存
echo "4️⃣ 清理 Swift Package Manager 缓存..."
swift package clean 2>/dev/null || echo "没有 Swift Package 需要清理"
rm -rf ~/.swiftpm/cache 2>/dev/null || true
echo "✅ Swift Package 缓存清理完成"

# 5. 清理模拟器缓存
echo "5️⃣ 清理模拟器缓存..."
rm -rf ~/Library/Logs/CoreSimulator
rm -rf ~/Library/Developer/CoreSimulator/Caches
echo "✅ 模拟器缓存清理完成"

# 6. 清理 Xcode 日志
echo "6️⃣ 清理 Xcode 日志..."
rm -rf ~/Library/Developer/Xcode/iOS\ DeviceSupport/*/Symbols/System/Library/Caches 2>/dev/null || true
rm -rf ~/Library/Logs/DiagnosticReports/Xcode_* 2>/dev/null || true
echo "✅ Xcode 日志清理完成"

echo ""
echo "🎉 Xcode 缓存清理完成！"
echo ""
echo "📋 建议的后续步骤："
echo "1. 重启 Xcode"
echo "2. 重新打开项目"
echo "3. 等待 Xcode 重新索引项目"
echo "4. 尝试重新构建项目"
echo ""
echo "💡 如果问题仍然存在，可能需要："
echo "- 重启 Mac"
echo "- 检查磁盘空间是否充足"
echo "- 检查项目文件是否有权限问题"