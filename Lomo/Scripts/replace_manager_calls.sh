#!/bin/bash

# 批量替换Manager调用脚本
echo "🔄 批量替换WatermarkControlView中的Manager调用"
echo "================================"

FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"
BACKUP_FILE="${FILE}.backup"

# 创建备份
echo "📋 创建备份文件..."
cp "$FILE" "$BACKUP_FILE"
echo "✅ 备份已创建: $BACKUP_FILE"

# 统计替换前的Manager调用数量
BEFORE_COUNT=$(grep -c "WatermarkSettingsManager\.shared" "$FILE")
echo "📊 替换前Manager调用数量: $BEFORE_COUNT"

echo ""
echo "🔄 开始批量替换..."

# 替换所有的 WatermarkSettingsManager.shared.getSettings() 调用
sed -i '' 's/WatermarkSettingsManager\.shared\.getSettings()/watermarkService.getSettings()/g' "$FILE"
echo "✅ 替换所有 getSettings() 调用"

# 替换所有的 WatermarkSettingsManager.shared.updateSetting 调用
sed -i '' 's/WatermarkSettingsManager\.shared\.updateSetting(/watermarkService.updateSetting(/g' "$FILE"
echo "✅ 替换所有 updateSetting() 调用"

# 替换所有的 WatermarkSettingsManager.shared.saveSettings 调用
sed -i '' 's/WatermarkSettingsManager\.shared\.saveSettings(/watermarkService.saveSettings(/g' "$FILE"
echo "✅ 替换所有 saveSettings() 调用"

# 统计替换后的Manager调用数量
AFTER_COUNT=$(grep -c "WatermarkSettingsManager\.shared" "$FILE")
echo "📊 替换后Manager调用数量: $AFTER_COUNT"

REPLACED_COUNT=$((BEFORE_COUNT - AFTER_COUNT))
echo "🎯 成功替换: $REPLACED_COUNT 个调用"

echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
    echo ""
    echo "🎉 批量替换完成！"
    echo "================================"
    echo "✅ 替换了 $REPLACED_COUNT 个Manager调用"
    echo "✅ 项目编译正常"
    echo "✅ 备份文件已保存: $BACKUP_FILE"
    echo ""
    echo "📋 如果需要回滚："
    echo "   cp $BACKUP_FILE $FILE"
    echo ""
    echo "🔍 剩余的Manager调用（如果有）："
    if [ "$AFTER_COUNT" -gt 0 ]; then
        echo "   还有 $AFTER_COUNT 个调用需要手动处理"
        grep -n "WatermarkSettingsManager\.shared" "$FILE" | head -5
        if [ "$AFTER_COUNT" -gt 5 ]; then
            echo "   ... 还有 $((AFTER_COUNT - 5)) 个调用"
        fi
    else
        echo "   🎉 所有Manager调用都已替换！"
    fi
else
    echo "❌ 项目编译失败，正在回滚..."
    cp "$BACKUP_FILE" "$FILE"
    echo "🔄 已回滚到原始状态"
    echo ""
    echo "编译错误信息："
    swift build
    exit 1
fi
