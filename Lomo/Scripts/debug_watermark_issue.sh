#!/bin/bash

# 水印问题调试脚本
echo "🔍 水印问题调试分析"
echo "===================="

# 检查关键文件是否存在
echo ""
echo "📁 检查关键文件..."

FILES=(
    "Lomo/Managers/Edit/WatermarkStyles.swift"
    "Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift"
    "Lomo/Managers/Edit/MetalSpecialEffectsEngine.swift"
    "Lomo/Shaders/SpecialEffectsShaders.metal"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 文件缺失"
    fi
done

# 检查Metal着色器函数
echo ""
echo "🔍 检查Metal着色器函数..."

if [ -f "Lomo/Shaders/SpecialEffectsShaders.metal" ]; then
    if grep -q "apply_gaussian_blur" "Lomo/Shaders/SpecialEffectsShaders.metal"; then
        echo "✅ apply_gaussian_blur 着色器函数存在"
    else
        echo "❌ apply_gaussian_blur 着色器函数缺失"
    fi
else
    echo "❌ SpecialEffectsShaders.metal 文件不存在"
fi

# 检查WatermarkStyles中的Metal引擎使用
echo ""
echo "🔍 检查WatermarkStyles中的Metal引擎使用..."

if [ -f "Lomo/Managers/Edit/WatermarkStyles.swift" ]; then
    metal_usage=$(grep -c "MetalSpecialEffectsEngine" "Lomo/Managers/Edit/WatermarkStyles.swift")
    if [ "$metal_usage" -gt 0 ]; then
        echo "✅ WatermarkStyles使用Metal引擎 ($metal_usage 处)"
    else
        echo "❌ WatermarkStyles未使用Metal引擎"
    fi
    
    # 检查模糊背景方法
    if grep -q "applyBlurBackground" "Lomo/Managers/Edit/WatermarkStyles.swift"; then
        echo "✅ applyBlurBackground 方法存在"
    else
        echo "❌ applyBlurBackground 方法缺失"
    fi
    
    # 检查视图捕获方法
    if grep -q "captureView" "Lomo/Managers/Edit/WatermarkStyles.swift"; then
        echo "✅ captureView 方法存在"
    else
        echo "❌ captureView 方法缺失"
    fi
else
    echo "❌ WatermarkStyles.swift 文件不存在"
fi

# 检查CustomWatermarkStyle1中的模式检测
echo ""
echo "🔍 检查CustomWatermarkStyle1中的模式检测..."

if [ -f "Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift" ]; then
    if grep -q "isProcessingCameraPreview" "Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift"; then
        echo "✅ 相机预览模式检测存在"
    else
        echo "❌ 相机预览模式检测缺失"
    fi
    
    if grep -q "findActualContentView" "Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift"; then
        echo "✅ 内容视图查找方法存在"
    else
        echo "❌ 内容视图查找方法缺失"
    fi
else
    echo "❌ CustomWatermarkStyle1.swift 文件不存在"
fi

# 检查调试信息是否已添加
echo ""
echo "🔍 检查调试信息..."

debug_patterns=(
    "🔍.*WatermarkStyles.*captureView"
    "🎨.*CustomWatermarkStyle1.*开始应用水印"
    "🔍.*MetalSpecialEffectsEngine.*开始初始化"
    "🔍.*MetalSpecialEffectsEngine.*开始应用高斯模糊"
)

for pattern in "${debug_patterns[@]}"; do
    found=false
    for file in "${FILES[@]}"; do
        if [ -f "$file" ] && grep -q "$pattern" "$file" 2>/dev/null; then
            echo "✅ 调试信息已添加: $pattern"
            found=true
            break
        fi
    done
    if [ "$found" = false ]; then
        echo "⚠️ 调试信息缺失: $pattern"
    fi
done

# 分析可能的问题原因
echo ""
echo "🎯 可能的问题原因分析"
echo "====================="

echo ""
echo "1. 📹 相机预览 vs 📸 照片模式差异:"
echo "   - 相机预览: 实时视图，Metal渲染正常"
echo "   - 照片模式: 静态图像，可能存在视图层次结构差异"

echo ""
echo "2. 🔍 视图捕获问题:"
echo "   - 照片模式下UIImageView可能不在正确的视图层次中"
echo "   - 视图尺寸可能为0或无效"
echo "   - 视图可能没有正确的superview或window"

echo ""
echo "3. ⚡ Metal引擎问题:"
echo "   - Metal设备初始化失败"
echo "   - 着色器编译失败"
echo "   - 纹理创建失败"

echo ""
echo "4. 🖼️ 图像处理问题:"
echo "   - 输入图像格式不兼容"
echo "   - 图像尺寸过大或过小"
echo "   - 内存不足"

echo ""
echo "📋 调试步骤建议"
echo "==============="

echo ""
echo "1. 运行应用并查看控制台日志"
echo "2. 尝试以下操作并观察日志输出:"
echo "   a) 在相机预览模式下应用水印 (应该正常)"
echo "   b) 导入照片后应用水印 (观察失败点)"
echo ""
echo "3. 关注以下关键日志:"
echo "   - 🔍 [CustomWatermarkStyle1] 查找内容视图"
echo "   - 🔍 [WatermarkStyles] captureView 开始"
echo "   - 🔍 [MetalSpecialEffectsEngine] 开始初始化"
echo "   - ❌ 任何错误信息"

echo ""
echo "4. 如果Metal引擎初始化失败，检查:"
echo "   - 设备是否支持Metal"
echo "   - 是否在模拟器中运行 (模拟器Metal支持有限)"
echo "   - 着色器文件是否正确编译"

echo ""
echo "🚀 现在可以运行应用并查看详细的调试日志！"
