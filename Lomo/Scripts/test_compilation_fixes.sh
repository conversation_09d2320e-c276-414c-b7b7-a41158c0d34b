#!/bin/bash

# 编译错误修复验证脚本
# 验证SubscriptionPlan类型冲突和SubscriptionDependencyContainer访问问题是否已修复

echo "🔍 开始验证编译错误修复..."

# 检查SubscriptionPlan类型冲突是否已解决
echo ""
echo "📋 检查SubscriptionPlan类型定义..."

# 检查ViewModel中是否还有重复的SubscriptionPlan定义
if grep -q "enum SubscriptionPlan" Lomo/ViewModels/Subscription/SubscriptionViewModel.swift; then
    echo "❌ SubscriptionViewModel中仍存在重复的SubscriptionPlan定义"
    grep -n "enum SubscriptionPlan" Lomo/ViewModels/Subscription/SubscriptionViewModel.swift
    exit 1
else
    echo "✅ SubscriptionViewModel中已移除重复的SubscriptionPlan定义"
fi

# 检查Model中的SubscriptionPlan定义是否存在
if grep -q "enum SubscriptionPlan" Lomo/Models/Subscription/SubscriptionModel.swift; then
    echo "✅ Model中的SubscriptionPlan定义正常存在"
else
    echo "❌ Model中的SubscriptionPlan定义缺失"
    exit 1
fi

# 检查SubscriptionDependencyContainer访问问题是否已修复
echo ""
echo "🏭 检查SubscriptionDependencyContainer访问..."

# 检查是否还有错误的属性访问方式
if grep -q "SubscriptionDependencyContainer\.shared\.subscriptionViewModel\.showProView" Lomo/Views/Camera/*.swift; then
    echo "❌ Camera视图中仍存在错误的SubscriptionDependencyContainer访问方式"
    grep -n "SubscriptionDependencyContainer\.shared\.subscriptionViewModel\.showProView" Lomo/Views/Camera/*.swift
    exit 1
else
    echo "✅ Camera视图中已修复SubscriptionDependencyContainer访问方式"
fi

# 检查是否使用了正确的方法调用方式
correct_access_count=$(grep -c "SubscriptionDependencyContainer\.subscriptionViewModel()" Lomo/Views/Camera/*.swift | awk -F: '{sum += $2} END {print sum}')
if [ "$correct_access_count" -gt 0 ]; then
    echo "✅ Camera视图中使用了正确的方法调用方式 ($correct_access_count 处)"
else
    echo "❌ Camera视图中未找到正确的方法调用方式"
    exit 1
fi

# 检查修复的文件列表
echo ""
echo "📁 已修复的文件："
echo "- Lomo/ViewModels/Subscription/SubscriptionViewModel.swift"
echo "- Lomo/Views/Camera/ColorSpaceOptionsView.swift"
echo "- Lomo/Views/Camera/ResolutionOptionsView.swift"
echo "- Lomo/Views/Camera/HEVCOptionsView.swift"
echo "- Lomo/Views/Camera/StabilizationOptionsView.swift"
echo "- Lomo/Views/Camera/PhotoFormatOptionsView.swift"
echo "- Lomo/Views/Camera/PhotoModeOptionsView.swift"
echo "- Lomo/Views/Camera/FrameRateOptionsView.swift"

# 检查修复摘要
echo ""
echo "🎯 修复摘要："
echo "1. ✅ 移除了SubscriptionViewModel中重复的SubscriptionPlan定义"
echo "2. ✅ 修复了Camera视图中SubscriptionDependencyContainer的访问方式"
echo "3. ✅ 统一使用Model中的SubscriptionPlan类型"
echo "4. ✅ 使用正确的方法调用语法"

echo ""
echo "🚀 编译错误修复验证完成！"