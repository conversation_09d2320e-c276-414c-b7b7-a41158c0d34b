#!/bin/bash

# 水印Metal渲染修复验证脚本
echo "🧪 验证水印Metal渲染修复..."

# 检查关键修复文件
echo "🔍 检查修复文件..."

WATERMARK_STYLES="Lomo/Managers/Edit/WatermarkStyles.swift"
CUSTOM_STYLE1="Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift"
WATERMARK_TEST="Lomo/Tests/WatermarkMetalRenderingTest.swift"

if [ -f "$WATERMARK_STYLES" ]; then
    echo "✅ WatermarkStyles文件存在"
    
    # 检查关键修复方法
    if grep -q "applyBlurBackgroundDelayed" "$WATERMARK_STYLES"; then
        echo "  ✅ 延迟应用方法已添加"
    else
        echo "  ❌ 延迟应用方法缺失"
    fi
    
    if grep -q "findImageViewInHierarchy" "$WATERMARK_STYLES"; then
        echo "  ✅ UIImageView查找方法已添加"
    else
        echo "  ❌ UIImageView查找方法缺失"
    fi
    
    if grep -q "MetalSpecialEffectsEngine" "$WATERMARK_STYLES"; then
        echo "  ✅ Metal引擎集成完成"
    else
        echo "  ❌ Metal引擎集成缺失"
    fi
    
    # 检查重试机制
    if grep -q "maxRetries" "$WATERMARK_STYLES"; then
        echo "  ✅ Metal引擎重试机制已添加"
    else
        echo "  ❌ Metal引擎重试机制缺失"
    fi
else
    echo "❌ WatermarkStyles文件缺失"
fi

if [ -f "$CUSTOM_STYLE1" ]; then
    echo "✅ CustomWatermarkStyle1文件存在"
    
    # 检查模式检测
    if grep -q "isProcessingCameraPreview" "$CUSTOM_STYLE1"; then
        echo "  ✅ 相机预览模式检测已添加"
    else
        echo "  ❌ 相机预览模式检测缺失"
    fi
    
    if grep -q "applyBlurBackgroundDelayed" "$CUSTOM_STYLE1"; then
        echo "  ✅ 延迟应用调用已添加"
    else
        echo "  ❌ 延迟应用调用缺失"
    fi
else
    echo "❌ CustomWatermarkStyle1文件缺失"
fi

if [ -f "$WATERMARK_TEST" ]; then
    echo "✅ 水印测试文件存在"
    
    # 检查测试方法
    if grep -q "testCameraPreviewWatermark" "$WATERMARK_TEST"; then
        echo "  ✅ 相机预览测试已添加"
    else
        echo "  ❌ 相机预览测试缺失"
    fi
    
    if grep -q "testPhotoModeWatermark" "$WATERMARK_TEST"; then
        echo "  ✅ 照片模式测试已添加"
    else
        echo "  ❌ 照片模式测试缺失"
    fi
    
    if grep -q "testMetalEngineRetry" "$WATERMARK_TEST"; then
        echo "  ✅ Metal引擎重试测试已添加"
    else
        echo "  ❌ Metal引擎重试测试缺失"
    fi
else
    echo "❌ 水印测试文件缺失"
fi

# 检查Core Image残留
echo ""
echo "🔍 检查Core Image残留..."

CORE_IMAGE_COUNT=0

# 检查WatermarkStyles中的Core Image使用
if [ -f "$WATERMARK_STYLES" ]; then
    ci_usage=$(grep -c "CIGaussianBlur\|CIFilter\|CIImage\|CIContext" "$WATERMARK_STYLES" 2>/dev/null || echo "0")
    if [ "$ci_usage" -gt 0 ]; then
        echo "⚠️ $WATERMARK_STYLES 仍有 $ci_usage 处Core Image使用"
        CORE_IMAGE_COUNT=$((CORE_IMAGE_COUNT + ci_usage))
    else
        echo "✅ WatermarkStyles已完全Metal化"
    fi
fi

# 检查Metal引擎使用
if [ -f "$WATERMARK_STYLES" ]; then
    metal_usage=$(grep -c "MetalSpecialEffectsEngine" "$WATERMARK_STYLES" 2>/dev/null || echo "0")
    if [ "$metal_usage" -gt 0 ]; then
        echo "✅ WatermarkStyles使用Metal引擎 ($metal_usage 处)"
    else
        echo "❌ WatermarkStyles未使用Metal引擎"
    fi
fi

# 检查编译状态
echo ""
echo "🔍 检查Swift语法..."

# 简单的语法检查
for file in "$WATERMARK_STYLES" "$CUSTOM_STYLE1" "$WATERMARK_TEST"; do
    if [ -f "$file" ]; then
        # 检查基本语法错误
        if grep -q "func.*{$" "$file" && grep -q "}" "$file"; then
            echo "✅ $file 语法结构正常"
        else
            echo "⚠️ $file 可能存在语法问题"
        fi
    fi
done

# 统计修复内容
echo ""
echo "📊 修复统计..."

TOTAL_FIXES=0

# 统计关键修复
if grep -q "applyBlurBackgroundDelayed" "$WATERMARK_STYLES" 2>/dev/null; then
    TOTAL_FIXES=$((TOTAL_FIXES + 1))
fi

if grep -q "findImageViewInHierarchy" "$WATERMARK_STYLES" 2>/dev/null; then
    TOTAL_FIXES=$((TOTAL_FIXES + 1))
fi

if grep -q "maxRetries" "$WATERMARK_STYLES" 2>/dev/null; then
    TOTAL_FIXES=$((TOTAL_FIXES + 1))
fi

if grep -q "isProcessingCameraPreview" "$CUSTOM_STYLE1" 2>/dev/null; then
    TOTAL_FIXES=$((TOTAL_FIXES + 1))
fi

if [ -f "$WATERMARK_TEST" ]; then
    TOTAL_FIXES=$((TOTAL_FIXES + 1))
fi

echo "关键修复数量: $TOTAL_FIXES/5"
echo "Core Image残留: $CORE_IMAGE_COUNT 处"

# 最终评估
echo ""
echo "🎯 修复评估..."

if [ "$TOTAL_FIXES" -eq 5 ] && [ "$CORE_IMAGE_COUNT" -eq 0 ]; then
    echo "🎉 水印Metal渲染修复完成！"
    echo "✅ 所有关键修复已实施"
    echo "✅ 零Core Image残留"
    echo "✅ 相机预览和照片模式都支持Metal水印"
    echo ""
    echo "🚀 现在可以测试水印功能："
    echo "   📹 相机预览模式: 立即应用Metal水印"
    echo "   📸 照片模式: 延迟应用Metal水印"
else
    echo "⚠️ 水印Metal渲染修复未完全完成"
    echo "关键修复: $TOTAL_FIXES/5"
    echo "Core Image残留: $CORE_IMAGE_COUNT 处"
    echo "请检查上述缺失的组件"
fi

echo ""
echo "🔧 下一步: 在Xcode中编译并测试水印功能"
