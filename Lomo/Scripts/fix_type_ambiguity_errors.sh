#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🚨 类型歧义错误修复脚本
# 修复 CurvePreset 和 RenderingMode 的重复声明问题

echo "🚨 开始修复类型歧义错误..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 修复 CurvePreset 重复声明问题
echo "1️⃣ 修复 CurvePreset 重复声明..."

# 1.1 从协议文件中移除 CurvePreset 定义
echo "   🔧 从 CurveServiceProtocol.swift 中移除 CurvePreset 定义..."
if grep -q "enum CurvePreset" Lomo/Services/Protocols/CurveServiceProtocol.swift; then
    # 创建临时文件，移除 CurvePreset 定义
    awk '
    /^\/\/\/ 曲线预设枚举$/ { skip = 1; next }
    /^enum CurvePreset/ { skip = 1; next }
    skip && /^}$/ { skip = 0; next }
    skip && /^    / { next }
    skip && /^$/ { next }
    !skip { print }
    ' Lomo/Services/Protocols/CurveServiceProtocol.swift > temp_curve_protocol.swift
    mv temp_curve_protocol.swift Lomo/Services/Protocols/CurveServiceProtocol.swift
    echo "   ✅ 已从协议中移除 CurvePreset 定义"
else
    echo "   ℹ️ 协议中未找到 CurvePreset 定义"
fi

# 1.2 从实现文件中移除 CurvePreset 定义
echo "   🔧 从 CurveServiceImpl.swift 中移除 CurvePreset 定义..."
if grep -q "enum CurvePreset" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    # 查找并移除 CurvePreset 定义
    sed -i '' '/^\/\/\/ 曲线预设枚举$/,/^}$/d' Lomo/Services/Implementations/CurveServiceImpl.swift
    echo "   ✅ 已从实现中移除 CurvePreset 定义"
else
    echo "   ℹ️ 实现中未找到 CurvePreset 定义"
fi

# 1.3 更新协议中的 CurvePreset 引用
echo "   🔧 更新协议中的 CurvePreset 引用..."
sed -i '' 's/CurvePreset/CurveProcessor.CurvePreset/g' Lomo/Services/Protocols/CurveServiceProtocol.swift
echo "   ✅ 已更新协议中的 CurvePreset 引用"

# 1.4 更新实现中的 CurvePreset 引用
echo "   🔧 更新实现中的 CurvePreset 引用..."
sed -i '' 's/currentPreset: CurvePreset?/currentPreset: CurveProcessor.CurvePreset?/g' Lomo/Services/Implementations/CurveServiceImpl.swift
sed -i '' 's/preset: CurvePreset/preset: CurveProcessor.CurvePreset/g' Lomo/Services/Implementations/CurveServiceImpl.swift
echo "   ✅ 已更新实现中的 CurvePreset 引用"

echo ""

# 2. 修复 RenderingMode 重复声明问题
echo "2️⃣ 修复 RenderingMode 重复声明..."

# 2.1 从实现文件中移除 RenderingMode 定义
echo "   🔧 从 RenderingServiceImpl.swift 中移除 RenderingMode 定义..."
if grep -q "enum RenderingMode" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    # 查找并移除 RenderingMode 定义
    sed -i '' '/^\/\/\/ 渲染模式枚举$/,/^}$/d' Lomo/Services/Implementations/RenderingServiceImpl.swift
    echo "   ✅ 已从实现中移除 RenderingMode 定义"
else
    echo "   ℹ️ 实现中未找到 RenderingMode 定义"
fi

# 2.2 更新协议中的 RenderingMode 引用（如果需要）
echo "   🔧 更新协议中的 RenderingMode 引用..."
if [ -f "Lomo/Services/Protocols/RenderingServiceProtocol.swift" ]; then
    # 确保使用完整的类型路径
    sed -i '' 's/mode: RenderingMode/mode: RenderingMode/g' Lomo/Services/Protocols/RenderingServiceProtocol.swift
    echo "   ✅ 已检查协议中的 RenderingMode 引用"
fi

# 2.3 更新实现中的 RenderingMode 引用
echo "   🔧 更新实现中的 RenderingMode 引用..."
# 确保实现文件正确引用 Models 中的 RenderingMode
sed -i '' 's/currentMode: RenderingMode/currentMode: RenderingMode/g' Lomo/Services/Implementations/RenderingServiceImpl.swift
echo "   ✅ 已更新实现中的 RenderingMode 引用"

echo ""

# 3. 验证修复结果
echo "3️⃣ 验证修复结果..."

echo "   🔍 检查 CurvePreset 重复定义..."
curve_preset_count=$(find Lomo/Services -name "*.swift" -exec grep -l "enum CurvePreset" {} \; | wc -l)
if [ "$curve_preset_count" -eq 0 ]; then
    echo "   ✅ CurvePreset 重复定义已清理"
else
    echo "   ⚠️ 仍有 $curve_preset_count 个文件包含 CurvePreset 定义"
    find Lomo/Services -name "*.swift" -exec grep -l "enum CurvePreset" {} \;
fi

echo "   🔍 检查 RenderingMode 重复定义..."
rendering_mode_count=$(find Lomo/Services -name "*.swift" -exec grep -l "enum RenderingMode" {} \; | wc -l)
if [ "$rendering_mode_count" -eq 0 ]; then
    echo "   ✅ RenderingMode 重复定义已清理"
else
    echo "   ⚠️ 仍有 $rendering_mode_count 个文件包含 RenderingMode 定义"
    find Lomo/Services -name "*.swift" -exec grep -l "enum RenderingMode" {} \;
fi

echo ""

# 4. 编译验证
echo "4️⃣ 编译验证..."
echo "   🔨 尝试编译项目..."

if xcodebuild -project Lomo.xcodeproj -scheme Lomo -configuration Debug build -quiet 2>/dev/null; then
    echo "   ✅ 编译成功！类型歧义错误已修复"
else
    echo "   ⚠️ 编译仍有问题，需要进一步检查"
    echo "   💡 运行以下命令查看详细错误："
    echo "      xcodebuild -project Lomo.xcodeproj -scheme Lomo build"
fi

echo ""
echo "🎉 类型歧义错误修复完成！"
echo ""
echo "📋 修复总结："
echo "   ✅ 移除了协议和实现中的重复 CurvePreset 定义"
echo "   ✅ 更新了所有 CurvePreset 引用为 CurveProcessor.CurvePreset"
echo "   ✅ 移除了实现中的重复 RenderingMode 定义"
echo "   ✅ 保留了 Models/Edit/RenderingMode.swift 中的原始定义"
echo ""
echo "🎯 现在类型系统应该清晰明确："
echo "   📁 CurvePreset: 使用 CurveProcessor.CurvePreset (来自 Utils/CurvePresets.swift)"
echo "   📁 RenderingMode: 使用 RenderingMode (来自 Models/Edit/RenderingMode.swift)"