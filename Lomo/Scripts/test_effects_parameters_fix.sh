#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🧪 特效参数修复验证脚本
# 验证所有特效参数类型冲突是否已修复

echo "🧪 开始验证特效参数修复..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查类型定义唯一性
echo "1️⃣ 检查类型定义唯一性..."

echo "   🔍 检查各参数类型定义数量..."
lightleak_files=$(find Lomo -name "*.swift" -exec grep -l "struct LightLeakParameters" {} \; 2>/dev/null)
grain_files=$(find Lomo -name "*.swift" -exec grep -l "struct GrainParameters" {} \; 2>/dev/null)
scratch_files=$(find Lomo -name "*.swift" -exec grep -l "struct ScratchParameters" {} \; 2>/dev/null)

lightleak_count=$(echo "$lightleak_files" | wc -l)
grain_count=$(echo "$grain_files" | wc -l)
scratch_count=$(echo "$scratch_files" | wc -l)

echo "   📊 LightLeakParameters 定义数量: $lightleak_count (期望: 1)"
if [ "$lightleak_count" -eq 1 ]; then
    echo "   ✅ LightLeakParameters 定义唯一: $(echo "$lightleak_files" | xargs basename)"
else
    echo "   ❌ LightLeakParameters 定义重复:"
    echo "$lightleak_files"
fi

echo "   📊 GrainParameters 定义数量: $grain_count (期望: 1)"
if [ "$grain_count" -eq 1 ]; then
    echo "   ✅ GrainParameters 定义唯一: $(echo "$grain_files" | xargs basename)"
else
    echo "   ❌ GrainParameters 定义重复:"
    echo "$grain_files"
fi

echo "   📊 ScratchParameters 定义数量: $scratch_count (期望: 1)"
if [ "$scratch_count" -eq 1 ]; then
    echo "   ✅ ScratchParameters 定义唯一: $(echo "$scratch_files" | xargs basename)"
else
    echo "   ❌ ScratchParameters 定义重复:"
    echo "$scratch_files"
fi

echo ""

# 2. 检查 RenderingMode 成员完整性
echo "2️⃣ 检查 RenderingMode 成员完整性..."

required_members=("lightroom" "vsco" "realtime" "highQuality")
missing_members=()

for member in "${required_members[@]}"; do
    if grep -q "case $member" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode 包含 $member 成员"
    else
        echo "   ❌ RenderingMode 缺少 $member 成员"
        missing_members+=("$member")
    fi
done

if [ ${#missing_members[@]} -eq 0 ]; then
    echo "   🎉 RenderingMode 成员完整"
else
    echo "   ⚠️ RenderingMode 缺少成员: ${missing_members[*]}"
fi

echo ""

# 3. 检查类型引用正确性
echo "3️⃣ 检查类型引用正确性..."

echo "   🔍 检查 EffectsViewModel 中的类型引用..."
if grep -q "var grainParameters: GrainParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    echo "   ✅ EffectsViewModel 正确引用 GrainParameters"
else
    echo "   ❌ EffectsViewModel 未正确引用 GrainParameters"
fi

if grep -q "var scratchParameters: ScratchParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    echo "   ✅ EffectsViewModel 正确引用 ScratchParameters"
else
    echo "   ❌ EffectsViewModel 未正确引用 ScratchParameters"
fi

if grep -q "var lightLeakParameters: LightLeakParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    echo "   ✅ EffectsViewModel 正确引用 LightLeakParameters"
else
    echo "   ❌ EffectsViewModel 未正确引用 LightLeakParameters"
fi

echo "   🔍 检查 RenderingServiceImpl 中的 RenderingMode 引用..."
if grep -q "renderingMode: RenderingMode = .realtime" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    echo "   ✅ RenderingServiceImpl 正确使用 .realtime"
else
    echo "   ❌ RenderingServiceImpl 未正确使用 .realtime"
fi

echo ""

# 4. 语法验证
echo "4️⃣ 语法验证..."

critical_files=(
    "Lomo/Models/Edit/RenderingMode.swift"
    "Lomo/Models/LightLeakModel.swift"
    "Lomo/Models/GrainModel.swift"
    "Lomo/Models/ScratchModel.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    "Lomo/Services/Implementations/RenderingServiceImpl.swift"
)

syntax_errors=0
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            ((syntax_errors++))
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
        ((syntax_errors++))
    fi
done

echo ""

# 5. 编译测试（如果可能）
echo "5️⃣ 编译测试..."

echo "   🔨 尝试编译关键文件..."
if command -v swiftc >/dev/null 2>&1; then
    # 尝试编译 RenderingMode
    if swiftc -parse Lomo/Models/Edit/RenderingMode.swift >/dev/null 2>&1; then
        echo "   ✅ RenderingMode.swift 编译通过"
    else
        echo "   ❌ RenderingMode.swift 编译失败"
    fi
else
    echo "   ℹ️ Swift 编译器不可用，跳过编译测试"
fi

echo ""

# 6. 检查原始错误是否解决
echo "6️⃣ 检查原始错误解决情况..."

echo "   🔍 检查是否还有类型歧义..."
# 模拟检查类型歧义的情况
ambiguous_types=()

# 检查是否有多个同名类型定义
if [ "$lightleak_count" -gt 1 ]; then
    ambiguous_types+=("LightLeakParameters")
fi
if [ "$grain_count" -gt 1 ]; then
    ambiguous_types+=("GrainParameters")
fi
if [ "$scratch_count" -gt 1 ]; then
    ambiguous_types+=("ScratchParameters")
fi

if [ ${#ambiguous_types[@]} -eq 0 ]; then
    echo "   ✅ 无类型歧义问题"
else
    echo "   ❌ 仍有类型歧义: ${ambiguous_types[*]}"
fi

echo "   🔍 检查 RenderingMode 成员访问..."
if [ ${#missing_members[@]} -eq 0 ]; then
    echo "   ✅ RenderingMode 成员访问正常"
else
    echo "   ❌ RenderingMode 仍缺少成员: ${missing_members[*]}"
fi

echo ""

# 7. 总结报告
echo "7️⃣ 修复验证总结..."

# 计算成功项目数
success_count=0
total_checks=7

# 检查各项是否成功
if [ "$lightleak_count" -eq 1 ] && [ "$grain_count" -eq 1 ] && [ "$scratch_count" -eq 1 ]; then
    ((success_count++))
fi
if [ ${#missing_members[@]} -eq 0 ]; then
    ((success_count++))
fi
if grep -q "var grainParameters: GrainParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    ((success_count++))
fi
if grep -q "var scratchParameters: ScratchParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    ((success_count++))
fi
if grep -q "var lightLeakParameters: LightLeakParameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    ((success_count++))
fi
if [ "$syntax_errors" -eq 0 ]; then
    ((success_count++))
fi
if [ ${#ambiguous_types[@]} -eq 0 ]; then
    ((success_count++))
fi

echo "   📊 验证结果: $success_count/$total_checks 项检查通过"

if [ "$success_count" -eq "$total_checks" ]; then
    echo "   🎉 特效参数修复验证完全成功！"
    echo ""
    echo "✅ 修复验证成果："
    echo "   • 所有参数类型定义唯一"
    echo "   • RenderingMode 成员完整"
    echo "   • 类型引用正确"
    echo "   • 语法检查通过"
    echo "   • 无类型歧义问题"
    echo ""
    echo "🎯 解决的原始错误："
    echo "   ✅ 'LightLeakParameters' 重复声明 → 已解决"
    echo "   ✅ 'GrainParameters' 类型歧义 → 已解决"
    echo "   ✅ 'ScratchParameters' 类型歧义 → 已解决"
    echo "   ✅ RenderingMode 缺少 'realtime' 成员 → 已解决"
else
    echo "   ⚠️ 部分检查未通过，需要进一步修复"
    echo "   📋 未通过的检查项目数: $((total_checks - success_count))"
fi

echo ""
echo "🏁 特效参数修复验证完成！"