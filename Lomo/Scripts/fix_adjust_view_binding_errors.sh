#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 修复AdjustView中的Binding错误脚本

echo "🔧 开始修复AdjustView中的Binding错误..."

# 定义文件路径
ADJUST_VIEW_FILE="Lomo/Views/Edit/AdjustView.swift"

# 检查文件是否存在
if [ ! -f "$ADJUST_VIEW_FILE" ]; then
    echo "❌ 错误：找不到文件 $ADJUST_VIEW_FILE"
    exit 1
fi

echo "📝 修复getCurrentParametersCopy方法调用..."

# 修复所有getCurrentParametersCopy()调用为currentParameters
sed -i '' 's/adjustViewModel\.getCurrentParametersCopy()/adjustViewModel.currentParameters/g' "$ADJUST_VIEW_FILE"

# 修复所有$adjustViewModel.getCurrentParametersCopy.property调用
sed -i '' 's/\$adjustViewModel\.getCurrentParametersCopy\./adjustViewModel.currentParameters./g' "$ADJUST_VIEW_FILE"

# 修复所有adjustViewModel.getCurrentParametersCopy().property调用
sed -i '' 's/adjustViewModel\.getCurrentParametersCopy()\./adjustViewModel.currentParameters./g' "$ADJUST_VIEW_FILE"

echo "📝 修复Binding类型错误..."

# 修复第82行的特定错误 - 将$adjustViewModel.getCurrentParametersCopy.exposure改为adjustViewModel.currentParameters.exposure
sed -i '' 's/Double(\$adjustViewModel\.getCurrentParametersCopy\.exposure)/Double(adjustViewModel.currentParameters.exposure)/g' "$ADJUST_VIEW_FILE"

echo "📝 验证修复结果..."

# 检查是否还有getCurrentParametersCopy的引用
if grep -q "getCurrentParametersCopy" "$ADJUST_VIEW_FILE"; then
    echo "⚠️ 警告：仍然存在getCurrentParametersCopy的引用"
    grep -n "getCurrentParametersCopy" "$ADJUST_VIEW_FILE"
else
    echo "✅ 所有getCurrentParametersCopy引用已修复"
fi

# 检查语法
echo "🔍 检查Swift语法..."
if command -v swiftc >/dev/null 2>&1; then
    if swiftc -parse "$ADJUST_VIEW_FILE" >/dev/null 2>&1; then
        echo "✅ Swift语法检查通过"
    else
        echo "⚠️ Swift语法检查发现问题，但可能是由于依赖关系"
    fi
else
    echo "ℹ️ 未找到swiftc，跳过语法检查"
fi

echo "🎉 AdjustView Binding错误修复完成！"
echo ""
echo "📋 修复内容："
echo "  - 将所有getCurrentParametersCopy()调用替换为currentParameters"
echo "  - 修复了Binding类型错误"
echo "  - 修复了动态成员访问错误"
echo ""
echo "🔄 请重新编译项目验证修复效果"