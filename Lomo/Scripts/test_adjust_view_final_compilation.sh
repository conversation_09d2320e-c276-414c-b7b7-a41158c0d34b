#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🧪 AdjustView最终编译验证"
echo "======================="

# 1. 综合错误检查
echo "1️⃣ 综合错误检查..."

# 检查所有已知的编译错误模式
error_patterns=(
    "getCurrentHSLParameters()"
    "AdjustViewModel[^R]"
    "\$ad\$"
    "validateAndFixBoundarie[^s]"
    "validateStateConsistency"
    "forceSyncState"
)

echo "   检查已知错误模式..."
total_errors=0

for pattern in "${error_patterns[@]}"; do
    if grep -q "$pattern" Lomo/Views/Edit/AdjustView.swift; then
        echo "   ❌ 发现错误模式: $pattern"
        ((total_errors++))
    fi
done

if [ $total_errors -eq 0 ]; then
    echo "   ✅ 没有发现已知错误模式"
else
    echo "   ❌ 发现 $total_errors 个错误模式"
fi

# 2. 语法完整性检查
echo "2️⃣ 语法完整性检查..."

# 检查基本语法结构
echo "   检查基本语法结构..."
syntax_checks=(
    "struct AdjustView: View"
    "var body: some View"
    "@ObservedObject var adjustViewModel: AdjustViewModelRefactored"
    "init(adjustViewModel: AdjustViewModelRefactored)"
)

syntax_errors=0
for check in "${syntax_checks[@]}"; do
    if ! grep -q "$check" Lomo/Views/Edit/AdjustView.swift; then
        echo "   ❌ 缺少语法结构: $check"
        ((syntax_errors++))
    fi
done

if [ $syntax_errors -eq 0 ]; then
    echo "   ✅ 基本语法结构完整"
else
    echo "   ❌ 发现 $syntax_errors 个语法结构问题"
fi

# 3. 方法完整性检查
echo "3️⃣ 方法完整性检查..."

# 检查辅助方法
helper_methods=(
    "curveControlPoint"
    "createDoubleTapGesture"
    "createDragGesture"
    "handleDragChanged"
    "drawCurvePath"
    "curveColor"
    "provideBoundaryFeedback"
)

missing_methods=0
for method in "${helper_methods[@]}"; do
    if ! grep -q "func $method" Lomo/Views/Edit/AdjustView.swift; then
        echo "   ❌ 缺少方法: $method"
        ((missing_methods++))
    fi
done

if [ $missing_methods -eq 0 ]; then
    echo "   ✅ 所有辅助方法存在"
else
    echo "   ❌ 缺少 $missing_methods 个方法"
fi

# 4. 属性和绑定检查
echo "4️⃣ 属性和绑定检查..."

# 检查关键属性
echo "   检查关键属性..."
if grep -q "private var currentChannelPoints" Lomo/Views/Edit/AdjustView.swift; then
    echo "   ✅ currentChannelPoints 计算属性存在"
else
    echo "   ❌ currentChannelPoints 计算属性缺失"
fi

# 检查HSL绑定
hsl_bindings=$(grep -c "currentHSLParameters" Lomo/Views/Edit/AdjustView.swift)
if [ $hsl_bindings -ge 3 ]; then
    echo "   ✅ HSL参数绑定正确 ($hsl_bindings 处使用)"
else
    echo "   ❌ HSL参数绑定不足 ($hsl_bindings 处使用)"
fi

# 5. 架构合规性检查
echo "5️⃣ 架构合规性检查..."

# 检查MVVM-S架构合规性
echo "   检查MVVM-S架构合规性..."
if grep -q "@ObservedObject.*AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift; then
    echo "   ✅ 使用正确的ViewModel类型"
else
    echo "   ❌ ViewModel类型不正确"
fi

# 检查依赖注入
if grep -q "init.*AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift; then
    echo "   ✅ 依赖注入实现正确"
else
    echo "   ❌ 依赖注入实现有问题"
fi

# 6. 性能优化检查
echo "6️⃣ 性能优化检查..."

# 检查表达式复杂度
echo "   检查表达式复杂度..."
complex_foreach=$(grep -A 10 "ForEach.*currentChannelPoints" Lomo/Views/Edit/AdjustView.swift | grep -c "curveControlPoint")
if [ $complex_foreach -gt 0 ]; then
    echo "   ✅ ForEach表达式已简化为方法调用"
else
    echo "   ❌ ForEach表达式仍然复杂"
fi

# 检查重复访问优化
repeated_access=$(grep -c "adjustViewModel\.curvePoints\[adjustViewModel\.selectedChannel\]" Lomo/Views/Edit/AdjustView.swift)
if [ $repeated_access -le 2 ]; then
    echo "   ✅ 减少了重复属性访问 (剩余: $repeated_access 次)"
else
    echo "   ⚠️ 仍有较多重复属性访问 ($repeated_access 次)"
fi

# 7. 生成最终报告
echo "7️⃣ 生成最终报告..."
echo ""
echo "📋 最终编译验证报告"
echo "=================="
echo "验证时间: $(date)"
echo "验证文件: Lomo/Views/Edit/AdjustView.swift"
echo ""

# 计算总体评分
total_checks=6
passed_checks=0

if [ $total_errors -eq 0 ]; then ((passed_checks++)); fi
if [ $syntax_errors -eq 0 ]; then ((passed_checks++)); fi
if [ $missing_methods -eq 0 ]; then ((passed_checks++)); fi
if [ $hsl_bindings -ge 3 ]; then ((passed_checks++)); fi
if grep -q "@ObservedObject.*AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift; then ((passed_checks++)); fi
if [ $complex_foreach -gt 0 ]; then ((passed_checks++)); fi

score=$((passed_checks * 100 / total_checks))

echo "验证结果:"
echo "  - 错误模式检查: $([ $total_errors -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
echo "  - 语法结构检查: $([ $syntax_errors -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
echo "  - 方法完整性检查: $([ $missing_methods -eq 0 ] && echo "✅ 通过" || echo "❌ 失败")"
echo "  - HSL绑定检查: $([ $hsl_bindings -ge 3 ] && echo "✅ 通过" || echo "❌ 失败")"
echo "  - 架构合规性检查: $(grep -q "@ObservedObject.*AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift && echo "✅ 通过" || echo "❌ 失败")"
echo "  - 性能优化检查: $([ $complex_foreach -gt 0 ] && echo "✅ 通过" || echo "❌ 失败")"
echo ""
echo "总体评分: $score/100"
echo ""

if [ $score -ge 90 ]; then
    echo "🎉 优秀！AdjustView已准备好编译"
elif [ $score -ge 80 ]; then
    echo "👍 良好！AdjustView基本准备好编译"
elif [ $score -ge 70 ]; then
    echo "⚠️ 可接受！AdjustView需要小幅修复"
else
    echo "❌ 需要改进！AdjustView需要进一步修复"
fi

echo ""
echo "建议下一步:"
echo "  1. 运行 Xcode 编译验证"
echo "  2. 进行功能测试"
echo "  3. 检查UI响应性能"
echo ""
echo "🎯 AdjustView最终编译验证完成！"