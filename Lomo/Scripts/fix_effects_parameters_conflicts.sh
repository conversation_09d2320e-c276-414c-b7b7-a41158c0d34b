#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🚨 特效参数类型冲突修复脚本
# 修复 LightLeakParameters、GrainParameters、ScratchParameters 的重复声明和歧义问题
# 修复 RenderingMode 缺少 realtime 成员的问题

echo "🚨 开始修复特效参数类型冲突..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查当前的重复定义情况
echo "1️⃣ 检查重复定义情况..."

echo "   🔍 检查 LightLeakParameters 定义..."
lightleak_count=$(find Lomo -name "*.swift" -exec grep -l "struct LightLeakParameters" {} \; 2>/dev/null | wc -l)
echo "   📊 LightLeakParameters 定义数量: $lightleak_count"

echo "   🔍 检查 GrainParameters 定义..."
grain_count=$(find Lomo -name "*.swift" -exec grep -l "struct GrainParameters" {} \; 2>/dev/null | wc -l)
echo "   📊 GrainParameters 定义数量: $grain_count"

echo "   🔍 检查 ScratchParameters 定义..."
scratch_count=$(find Lomo -name "*.swift" -exec grep -l "struct ScratchParameters" {} \; 2>/dev/null | wc -l)
echo "   📊 ScratchParameters 定义数量: $scratch_count"

echo ""

# 2. 修复 RenderingMode 缺少 realtime 成员的问题
echo "2️⃣ 修复 RenderingMode 枚举..."

echo "   🔧 添加 realtime 和 highQuality 成员到 RenderingMode..."

# 备份原文件
cp Lomo/Models/Edit/RenderingMode.swift Lomo/Models/Edit/RenderingMode.swift.backup

# 更新 RenderingMode 枚举
cat > Lomo/Models/Edit/RenderingMode.swift << 'EOF'
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 渲染模式枚举 - 区分不同的滤镜算法和渲染质量
enum RenderingMode {
    case lightroom     // Lightroom风格算法 - 用于传统调整和非胶片滤镜
    case vsco         // VSCO风格算法 - 用于胶片滤镜
    case realtime     // 实时渲染模式 - 用于预览和交互
    case highQuality  // 高质量渲染模式 - 用于最终输出
    
    var displayName: String {
        switch self {
        case .lightroom:
            return "标准调整模式"
        case .vsco:
            return "胶片调整模式"
        case .realtime:
            return "实时渲染"
        case .highQuality:
            return "高质量渲染"
        }
    }
    
    var shaderFunctionName: String {
        switch self {
        case .lightroom:
            return "lightroom_filter"
        case .vsco:
            return "vsco_filter"
        case .realtime:
            return "realtime_filter"
        case .highQuality:
            return "highquality_filter"
        }
    }
    
    var description: String {
        switch self {
        case .lightroom:
            return "基于Adobe Lightroom算法的专业调色"
        case .vsco:
            return "基于胶片特性的艺术化处理"
        case .realtime:
            return "优化的实时渲染，适合预览和交互"
        case .highQuality:
            return "高质量渲染，适合最终输出"
        }
    }
}

/// 滤镜类型到渲染模式的映射
extension FilterPresetType {
    var renderingMode: RenderingMode {
        switch self {
        case .film:
            return .vsco  // 胶片滤镜使用VSCO算法
        case .polaroid, .vintage, .fashion, .ins:
            return .lightroom  // 其他滤镜使用Lightroom算法
        }
    }
}
EOF

echo "   ✅ RenderingMode 枚举已更新，添加了 realtime 和 highQuality 成员"

echo ""

# 3. 检查并清理可能的重复定义
echo "3️⃣ 检查并清理重复定义..."

# 检查 EffectsViewModel 是否有重复定义
echo "   🔍 检查 EffectsViewModel 中的重复定义..."
if grep -q "struct.*Parameters" Lomo/ViewModels/Edit/EffectsViewModel.swift; then
    echo "   ⚠️ 发现 EffectsViewModel 中有参数结构定义"
    # 移除可能的重复定义
    sed -i '' '/^struct GrainParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    sed -i '' '/^struct ScratchParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    sed -i '' '/^struct LightLeakParameters/,/^}/d' Lomo/ViewModels/Edit/EffectsViewModel.swift
    echo "   ✅ 已清理 EffectsViewModel 中的重复定义"
else
    echo "   ✅ EffectsViewModel 中无重复定义"
fi

# 检查 LightLeakModel 是否有重复定义
echo "   🔍 检查 LightLeakModel 中的重复定义..."
if [ -f "Lomo/Models/LightLeakModel.swift" ]; then
    # 检查是否有重复的 LightLeakParameters 定义
    lightleak_defs=$(grep -n "struct LightLeakParameters" Lomo/Models/LightLeakModel.swift | wc -l)
    if [ "$lightleak_defs" -gt 1 ]; then
        echo "   ⚠️ 发现 LightLeakModel 中有重复的 LightLeakParameters 定义"
        # 保留第一个定义，删除后续重复定义
        awk '/struct LightLeakParameters/ && ++count > 1, /^}/ && count > 1 { next } 1' Lomo/Models/LightLeakModel.swift > temp_lightleak.swift
        mv temp_lightleak.swift Lomo/Models/LightLeakModel.swift
        echo "   ✅ 已清理 LightLeakModel 中的重复定义"
    else
        echo "   ✅ LightLeakModel 中无重复定义"
    fi
fi

echo ""

# 4. 验证修复结果
echo "4️⃣ 验证修复结果..."

echo "   🔍 重新检查定义数量..."
lightleak_count_after=$(find Lomo -name "*.swift" -exec grep -l "struct LightLeakParameters" {} \; 2>/dev/null | wc -l)
grain_count_after=$(find Lomo -name "*.swift" -exec grep -l "struct GrainParameters" {} \; 2>/dev/null | wc -l)
scratch_count_after=$(find Lomo -name "*.swift" -exec grep -l "struct ScratchParameters" {} \; 2>/dev/null | wc -l)

echo "   📊 修复后 LightLeakParameters 定义数量: $lightleak_count_after (期望: 1)"
echo "   📊 修复后 GrainParameters 定义数量: $grain_count_after (期望: 1)"
echo "   📊 修复后 ScratchParameters 定义数量: $scratch_count_after (期望: 1)"

echo "   🔍 检查 RenderingMode 成员..."
if grep -q "case realtime" Lomo/Models/Edit/RenderingMode.swift; then
    echo "   ✅ RenderingMode 包含 realtime 成员"
else
    echo "   ❌ RenderingMode 缺少 realtime 成员"
fi

if grep -q "case highQuality" Lomo/Models/Edit/RenderingMode.swift; then
    echo "   ✅ RenderingMode 包含 highQuality 成员"
else
    echo "   ❌ RenderingMode 缺少 highQuality 成员"
fi

echo ""

# 5. 语法检查
echo "5️⃣ 语法检查..."

files_to_check=(
    "Lomo/Models/Edit/RenderingMode.swift"
    "Lomo/Models/LightLeakModel.swift"
    "Lomo/Models/GrainModel.swift"
    "Lomo/Models/ScratchModel.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    "Lomo/Services/Implementations/RenderingServiceImpl.swift"
)

all_syntax_ok=true
for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            all_syntax_ok=false
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
    fi
done

echo ""

# 6. 总结
echo "6️⃣ 修复总结..."

if [ "$all_syntax_ok" = true ] && [ "$lightleak_count_after" -eq 1 ] && [ "$grain_count_after" -eq 1 ] && [ "$scratch_count_after" -eq 1 ]; then
    echo "   🎉 特效参数类型冲突修复成功！"
    echo ""
    echo "✅ 修复成果："
    echo "   • RenderingMode 添加了 realtime 和 highQuality 成员"
    echo "   • 清理了所有重复的参数结构定义"
    echo "   • 确保每个参数类型只有一个定义"
    echo "   • 所有相关文件语法检查通过"
    echo ""
    echo "🎯 当前参数类型结构："
    echo "   📁 LightLeakParameters: Models/LightLeakModel.swift"
    echo "   📁 GrainParameters: Models/GrainModel.swift"
    echo "   📁 ScratchParameters: Models/ScratchModel.swift"
    echo "   📁 RenderingMode: Models/Edit/RenderingMode.swift (包含 realtime, highQuality)"
    
    # 删除备份文件
    rm -f Lomo/Models/Edit/RenderingMode.swift.backup
else
    echo "   ⚠️ 修复过程中发现问题，需要进一步检查"
    if [ -f "Lomo/Models/Edit/RenderingMode.swift.backup" ]; then
        echo "   🔄 如需回滚，备份文件位于: Lomo/Models/Edit/RenderingMode.swift.backup"
    fi
fi

echo ""
echo "🏁 特效参数类型冲突修复完成！"