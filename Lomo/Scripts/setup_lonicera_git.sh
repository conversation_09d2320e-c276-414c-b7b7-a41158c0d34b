#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# LoniceraLab Git 环境一键设置脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🚀 LoniceraLab Git 环境一键设置${NC}"
echo ""
echo -e "${BLUE}这个脚本将为您设置完整的 LoniceraLab Git 开发环境${NC}"
echo ""

# 检查是否在正确的项目目录
if [ ! -f "Lomo.xcodeproj/project.pbxproj" ]; then
    echo -e "${RED}❌ 请在 Lomo 项目根目录运行此脚本${NC}"
    exit 1
fi

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo -e "${PURPLE}📋 将要设置的内容:${NC}"
echo "• Git Hooks (pre-commit, commit-msg, pre-push)"
echo "• Shell 别名 (garch, gfeat, gfix, gdocs 等)"
echo "• Git 配置优化"
echo "• 智能提交和清理工具集成"
echo ""

read -p "是否继续设置？(Y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Nn]$ ]]; then
    echo -e "${BLUE}ℹ️ 用户取消设置${NC}"
    exit 0
fi

echo ""
echo -e "${CYAN}🔧 开始设置 LoniceraLab Git 环境...${NC}"

# 1. 设置 Git Hooks
echo -e "${BLUE}1️⃣ 设置 Git Hooks...${NC}"
if [ -x "$SCRIPT_DIR/setup_git_hooks.sh" ]; then
    # 自动设置所有 hooks
    HOOKS_DIR=".git/hooks"
    
    # Pre-commit hook
    cat > "$HOOKS_DIR/pre-commit" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab pre-commit hook

echo "🔍 LoniceraLab 提交前检查..."

SCRIPT_DIR="$(git rev-parse --show-toplevel)/Lomo/Scripts"
SMART_COMMIT="$SCRIPT_DIR/smart_git_commit.sh"
SMART_CLEANER="$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"

if [ -x "$SMART_COMMIT" ]; then
    if ! "$SMART_COMMIT"; then
        echo "❌ 提交前检查失败"
        exit 1
    fi
fi

if [ -x "$SMART_CLEANER" ]; then
    if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
        echo "💡 提示: 检测到 Xcode 缓存可能需要清理"
        echo "   运行: ./Lomo/Scripts/smart_xcode_cache_cleaner.sh"
    fi
fi

echo "✅ 提交前检查完成"
EOF
    
    chmod +x "$HOOKS_DIR/pre-commit"
    echo -e "${GREEN}   ✅ 设置 pre-commit hook${NC}"
    
    # Commit-msg hook
    cat > "$HOOKS_DIR/commit-msg" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab commit-msg hook

commit_msg_file="$1"
commit_msg=$(cat "$commit_msg_file")

if ! echo "$commit_msg" | grep -qE "^[🏗️✨🐛📱🎨⚡📚🔧🧹🚨🔒🌐]"; then
    echo "❌ 提交信息缺少 LoniceraLab 标准 emoji"
    echo "💡 建议使用: 🏗️ arch, ✨ feat, 🐛 fix, 📚 docs 等"
    exit 1
fi

echo "✅ 提交信息格式验证通过"
EOF
    
    chmod +x "$HOOKS_DIR/commit-msg"
    echo -e "${GREEN}   ✅ 设置 commit-msg hook${NC}"
else
    echo -e "${YELLOW}   ⚠️ Git Hooks 设置脚本不存在${NC}"
fi

# 2. 设置 Shell 别名
echo -e "${BLUE}2️⃣ 设置 Shell 别名...${NC}"

# 检测使用的 shell
if [ -n "$ZSH_VERSION" ]; then
    SHELL_RC="$HOME/.zshrc"
    SHELL_NAME="zsh"
elif [ -n "$BASH_VERSION" ]; then
    SHELL_RC="$HOME/.bashrc"
    SHELL_NAME="bash"
else
    SHELL_RC="$HOME/.profile"
    SHELL_NAME="shell"
fi

# 添加 LoniceraLab Git 别名
ALIASES="
# ========================================
# LoniceraLab Git 别名 (自动生成)
# ========================================

# 基础 Git 操作
alias gst='git status'
alias gco='git checkout'
alias gcb='git checkout -b'
alias gaa='git add .'
alias gcm='git commit -m'
alias gps='git push'
alias gpl='git pull'
alias glog='git log --oneline --graph --decorate'

# LoniceraLab 专用提交类型
alias garch='git add . && git commit -m \"🏗️ arch: \"'
alias gfeat='git add . && git commit -m \"✨ feat: \"'
alias gfix='git add . && git commit -m \"🐛 fix: \"'
alias gdocs='git add . && git commit -m \"📚 docs: \"'
alias gconfig='git add . && git commit -m \"🔧 config: \"'
alias gui='git add . && git commit -m \"🎨 ui: \"'

# LoniceraLab 智能工具
alias gsmart='$SCRIPT_DIR/smart_git_commit.sh'
alias xclean='$SCRIPT_DIR/smart_xcode_cache_cleaner.sh'
alias xcheck='$SCRIPT_DIR/smart_xcode_cache_cleaner.sh check'

# LoniceraLab 项目专用
alias lomo-status='echo \"📊 LoniceraLab Lomo 项目状态:\" && git status && echo \"\" && echo \"📈 最近提交:\" && git log --oneline -5'
alias lomo-arch='git log --oneline --grep=\"🏗️ arch\" -10'
alias lomo-clean='$SCRIPT_DIR/smart_xcode_cache_cleaner.sh && git status'

# ========================================
"

# 检查是否已经添加过别名
if ! grep -q "LoniceraLab Git 别名" "$SHELL_RC" 2>/dev/null; then
    echo "$ALIASES" >> "$SHELL_RC"
    echo -e "${GREEN}   ✅ 添加 LoniceraLab Git 别名到 $SHELL_RC${NC}"
    echo -e "${YELLOW}   💡 请运行 'source $SHELL_RC' 或重启终端以生效${NC}"
else
    echo -e "${YELLOW}   ⚠️ LoniceraLab Git 别名已存在${NC}"
fi

# 3. 优化 Git 配置
echo -e "${BLUE}3️⃣ 优化 Git 配置...${NC}"

# 设置 Git 配置
git config --local core.autocrlf false
git config --local core.safecrlf true
git config --local pull.rebase false
git config --local init.defaultBranch main

# 设置中文支持
git config --local core.quotepath false
git config --local gui.encoding utf-8
git config --local i18n.commit.encoding utf-8
git config --local i18n.logoutputencoding utf-8

echo -e "${GREEN}   ✅ Git 配置优化完成${NC}"

# 4. 创建快捷脚本
echo -e "${BLUE}4️⃣ 创建快捷脚本...${NC}"

# 创建快速提交脚本
cat > "$SCRIPT_DIR/quick_commit.sh" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab 快速提交脚本

if [ -z "$1" ]; then
    echo "用法: $0 <type> <message>"
    echo "类型: arch, feat, fix, docs, config, ui"
    echo "示例: $0 arch \"Gallery模块MVVM-S重构完成\""
    exit 1
fi

TYPE="$1"
MESSAGE="$2"

case "$TYPE" in
    "arch")
        EMOJI="🏗️"
        ;;
    "feat")
        EMOJI="✨"
        ;;
    "fix")
        EMOJI="🐛"
        ;;
    "docs")
        EMOJI="📚"
        ;;
    "config")
        EMOJI="🔧"
        ;;
    "ui")
        EMOJI="🎨"
        ;;
    *)
        echo "❌ 未知类型: $TYPE"
        exit 1
        ;;
esac

git add .
git commit -m "$EMOJI $TYPE: $MESSAGE"
EOF

chmod +x "$SCRIPT_DIR/quick_commit.sh"
echo -e "${GREEN}   ✅ 创建快速提交脚本${NC}"

# 5. 显示使用指南
echo ""
echo -e "${CYAN}🎉 LoniceraLab Git 环境设置完成！${NC}"
echo ""
echo -e "${PURPLE}📚 使用指南:${NC}"
echo ""
echo -e "${BLUE}🔧 智能工具:${NC}"
echo "  gsmart                    # 智能提交检查"
echo "  xclean                    # 智能缓存清理"
echo "  xcheck                    # 检查缓存状态"
echo ""
echo -e "${BLUE}⚡ 快速提交:${NC}"
echo "  garch \"消息\"              # 🏗️ 架构重构提交"
echo "  gfeat \"消息\"              # ✨ 新功能提交"
echo "  gfix \"消息\"               # 🐛 问题修复提交"
echo "  gdocs \"消息\"              # 📚 文档更新提交"
echo ""
echo -e "${BLUE}📊 项目状态:${NC}"
echo "  lomo-status               # 查看项目状态"
echo "  lomo-arch                 # 查看架构重构历史"
echo "  lomo-clean                # 清理缓存并查看状态"
echo ""
echo -e "${BLUE}🚀 快速脚本:${NC}"
echo "  ./Lomo/Scripts/quick_commit.sh arch \"消息\""
echo "  ./Lomo/Scripts/quick_commit.sh feat \"消息\""
echo ""
echo -e "${YELLOW}💡 提示: 请运行 'source $SHELL_RC' 使别名生效${NC}"
echo ""
echo -e "${GREEN}✅ 现在您可以享受高效的 LoniceraLab Git 工作流程！${NC}"