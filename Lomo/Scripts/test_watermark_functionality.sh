#!/bin/bash

# 水印功能完整性测试脚本
echo "🧪 水印功能完整性测试"
echo "================================"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

echo ""
echo "🔍 检查重构完成情况..."

# 1. 检查WatermarkSettingsManager是否已完全移除
echo ""
echo "📋 检查WatermarkSettingsManager移除情况..."

if [ -f "Lomo/Managers/Edit/WatermarkSettingsManager.swift" ]; then
    echo "❌ WatermarkSettingsManager.swift 文件仍然存在"
    exit 1
else
    echo "✅ WatermarkSettingsManager.swift 文件已成功移除"
fi

# 检查是否还有对WatermarkSettingsManager.shared的实际调用（排除注释）
manager_refs=$(find Lomo -name "*.swift" -not -path "*/.*" -not -name "*.bak" -not -name "*.backup" -exec grep -v "^\s*//\|^\s*/\*\|^\s*\*" {} \; -exec grep -l "WatermarkSettingsManager\.shared" {} \; 2>/dev/null | wc -l)
if [ "$manager_refs" -eq 0 ]; then
    echo "✅ 没有剩余的WatermarkSettingsManager.shared实际调用"
else
    echo "⚠️ 发现 $manager_refs 个文件可能包含Manager引用，检查是否为注释..."
    # 更精确的检查：排除注释行
    actual_refs=0
    for file in $(find Lomo -name "*.swift" -not -path "*/.*" -not -name "*.bak" -not -name "*.backup"); do
        # 检查非注释行中的Manager调用
        non_comment_refs=$(grep -v "^\s*//\|^\s*/\*\|^\s*\*" "$file" | grep -c "WatermarkSettingsManager\.shared" 2>/dev/null || echo "0")
        if [ "$non_comment_refs" -gt 0 ]; then
            echo "❌ $file 包含 $non_comment_refs 个实际Manager调用"
            actual_refs=$((actual_refs + non_comment_refs))
        fi
    done

    if [ "$actual_refs" -eq 0 ]; then
        echo "✅ 所有Manager引用都是注释，无实际调用"
    else
        echo "❌ 发现 $actual_refs 个实际Manager调用"
        exit 1
    fi
fi

# 2. 检查WatermarkService是否正确实现
echo ""
echo "📋 检查WatermarkService实现..."

if [ -f "Lomo/Services/Edit/WatermarkService.swift" ]; then
    echo "✅ WatermarkService.swift 文件存在"
    
    # 检查关键方法是否存在
    if grep -q "func getSettings()" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ getSettings() 方法存在"
    else
        echo "❌ getSettings() 方法缺失"
        exit 1
    fi
    
    if grep -q "func updateSetting" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ updateSetting() 方法存在"
    else
        echo "❌ updateSetting() 方法缺失"
        exit 1
    fi
    
    if grep -q "func saveSettings" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ saveSettings() 方法存在"
    else
        echo "❌ saveSettings() 方法缺失"
        exit 1
    fi
else
    echo "❌ WatermarkService.swift 文件不存在"
    exit 1
fi

# 3. 检查依赖注入容器
echo ""
echo "📋 检查依赖注入容器..."

if [ -f "Lomo/DependencyInjection/WatermarkDependencyContainer.swift" ]; then
    echo "✅ WatermarkDependencyContainer.swift 文件存在"
    
    if grep -q "watermarkService" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift"; then
        echo "✅ watermarkService 属性存在"
    else
        echo "❌ watermarkService 属性缺失"
        exit 1
    fi
else
    echo "❌ WatermarkDependencyContainer.swift 文件不存在"
    exit 1
fi

# 4. 检查WatermarkControlView是否正确使用Service
echo ""
echo "📋 检查WatermarkControlView重构情况..."

if [ -f "Lomo/Views/Edit/Components/WatermarkControlView.swift" ]; then
    echo "✅ WatermarkControlView.swift 文件存在"
    
    # 检查是否使用了WatermarkService
    if grep -q "watermarkService" "Lomo/Views/Edit/Components/WatermarkControlView.swift"; then
        echo "✅ WatermarkControlView 使用 WatermarkService"
    else
        echo "❌ WatermarkControlView 未使用 WatermarkService"
        exit 1
    fi
    
    # 检查是否还有Manager调用
    manager_calls=$(grep -c "WatermarkSettingsManager\.shared" "Lomo/Views/Edit/Components/WatermarkControlView.swift" 2>/dev/null)
    if [ $? -ne 0 ] || [ "$manager_calls" -eq 0 ]; then
        echo "✅ WatermarkControlView 已移除所有Manager调用"
    else
        echo "❌ WatermarkControlView 还有 $manager_calls 个Manager调用"
        exit 1
    fi
else
    echo "❌ WatermarkControlView.swift 文件不存在"
    exit 1
fi

# 5. 检查ViewModel重构情况
echo ""
echo "📋 检查ViewModel重构情况..."

VIEWMODEL_FILES=(
    "Lomo/ViewModels/Edit/EditViewModel.swift"
    "Lomo/ViewModels/Gallery/GalleryViewModel.swift"
)

for file in "${VIEWMODEL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $(basename "$file") 文件存在"
        
        # 检查是否使用了WatermarkService
        if grep -q "watermarkService" "$file"; then
            echo "✅ $(basename "$file") 使用 WatermarkService"
        else
            echo "❌ $(basename "$file") 未使用 WatermarkService"
            exit 1
        fi
        
        # 检查是否还有Manager调用
        manager_calls=$(grep -c "WatermarkSettingsManager\.shared" "$file" 2>/dev/null)
        if [ $? -ne 0 ] || [ "$manager_calls" -eq 0 ]; then
            echo "✅ $(basename "$file") 已移除所有Manager调用"
        else
            echo "❌ $(basename "$file") 还有 $manager_calls 个Manager调用"
            exit 1
        fi
    else
        echo "❌ $(basename "$file") 文件不存在"
        exit 1
    fi
done

# 6. 检查WatermarkStyles重构情况
echo ""
echo "📋 检查WatermarkStyles重构情况..."

# 检查主要的WatermarkStyles文件
if [ -f "Lomo/Managers/Edit/WatermarkStyles.swift" ]; then
    echo "✅ WatermarkStyles.swift 文件存在"
    
    # 检查是否还有Manager调用
    manager_calls=$(grep -c "WatermarkSettingsManager\.shared" "Lomo/Managers/Edit/WatermarkStyles.swift" 2>/dev/null)
    if [ $? -ne 0 ] || [ "$manager_calls" -eq 0 ]; then
        echo "✅ WatermarkStyles.swift 已移除所有Manager调用"
    else
        echo "❌ WatermarkStyles.swift 还有 $manager_calls 个Manager调用"
        exit 1
    fi
else
    echo "❌ WatermarkStyles.swift 文件不存在"
    exit 1
fi

# 检查CustomWatermarkStyle文件
custom_styles_with_manager=$(find Lomo/Managers/Edit/WatermarkStyles -name "CustomWatermarkStyle*.swift" -exec grep -l "WatermarkSettingsManager\.shared" {} \; 2>/dev/null | wc -l)
if [ "$custom_styles_with_manager" -eq 0 ]; then
    echo "✅ 所有CustomWatermarkStyle文件已移除Manager调用"
else
    echo "❌ 还有 $custom_styles_with_manager 个CustomWatermarkStyle文件使用Manager"
    exit 1
fi

echo ""
echo "🎉 水印功能完整性测试通过！"
echo "================================"
echo "✅ WatermarkSettingsManager 已完全移除"
echo "✅ WatermarkService 正确实现"
echo "✅ 依赖注入容器配置正确"
echo "✅ 所有组件已迁移到Service架构"
echo "✅ 项目编译正常"
echo ""
echo "🏆 MVVM-S架构重构成功完成！"
