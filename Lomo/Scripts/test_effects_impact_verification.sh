#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 特效模块重构影响验证脚本
# 检查特效模块重构是否影响了其他模块

echo "🔍 特效模块重构影响验证"
echo "========================================"
echo ""

# 设置项目路径
PROJECT_DIR="/Users/<USER>/Lomo"
cd "$PROJECT_DIR" || exit 1

echo "📍 当前目录: $(pwd)"
echo ""

# 1. 检查可能受影响的关键文件
echo "1️⃣ 检查可能受影响的关键文件..."
echo "----------------------------------------"

affected_files=(
    "Lomo/ViewModels/Camera/CameraViewModel.swift"
    "Lomo/ViewModels/Edit/EditViewModel.swift"
    "Lomo/Views/Edit/EditView.swift"
    "Lomo/Views/Camera/CameraView.swift"
)

compilation_errors=0
for file in "${affected_files[@]}"; do
    echo "检查: $file"
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" > /dev/null 2>&1; then
            echo "✅ 编译通过"
        else
            echo "❌ 编译失败:"
            swift -frontend -parse "$file" 2>&1 | head -3
            compilation_errors=$((compilation_errors + 1))
        fi
    else
        echo "⚠️ 文件不存在"
    fi
    echo ""
done

# 2. 检查EffectsService的单例使用情况
echo "2️⃣ 检查项目中EffectsService单例使用情况..."
echo "----------------------------------------"

echo "搜索 EffectsService.shared 使用..."
singleton_usage=$(grep -r "EffectsService\.shared" Lomo/ --include="*.swift" 2>/dev/null || true)

if [ -z "$singleton_usage" ]; then
    echo "✅ 没有发现 EffectsService.shared 的使用"
else
    echo "⚠️ 发现以下 EffectsService.shared 使用:"
    echo "$singleton_usage"
fi

echo ""

# 3. 检查特效相关的导入和引用
echo "3️⃣ 检查特效相关的导入和引用..."
echo "----------------------------------------"

echo "检查可能的编译错误模式..."
error_patterns=(
    "Type 'EffectsService' has no member 'shared'"
    "Cannot find 'EffectsService' in scope"
    "Use of unresolved identifier 'EffectsService'"
)

found_errors=0
for pattern in "${error_patterns[@]}"; do
    if grep -r "$pattern" Lomo/ --include="*.swift" 2>/dev/null | grep -q .; then
        echo "❌ 发现错误模式: $pattern"
        found_errors=$((found_errors + 1))
    fi
done

if [ $found_errors -eq 0 ]; then
    echo "✅ 没有发现常见的编译错误模式"
fi

echo ""

# 4. 验证特效模块的依赖注入是否正常工作
echo "4️⃣ 验证特效模块依赖注入..."
echo "----------------------------------------"

# 检查EffectsDependencyContainer是否可以正常编译
if swift -frontend -parse "Lomo/DependencyInjection/EffectsDependencyContainer.swift" > /dev/null 2>&1; then
    echo "✅ EffectsDependencyContainer 编译正常"
else
    echo "❌ EffectsDependencyContainer 编译失败"
    compilation_errors=$((compilation_errors + 1))
fi

# 检查EffectsViewModel是否可以正常编译
if swift -frontend -parse "Lomo/ViewModels/Edit/EffectsViewModel.swift" > /dev/null 2>&1; then
    echo "✅ EffectsViewModel 编译正常"
else
    echo "❌ EffectsViewModel 编译失败"
    compilation_errors=$((compilation_errors + 1))
fi

echo ""

# 5. 总结报告
echo "5️⃣ 影响验证总结"
echo "========================================"

if [ $compilation_errors -eq 0 ] && [ $found_errors -eq 0 ]; then
    echo "🎉 特效模块重构影响验证通过！"
    echo ""
    echo "✅ 所有关键文件编译正常"
    echo "✅ 没有发现单例使用问题"
    echo "✅ 没有发现编译错误"
    echo "✅ 依赖注入体系正常工作"
    echo ""
    echo "🏗️ 特效模块重构成功，没有破坏其他模块"
    echo "🚀 可以安全地继续其他工作"
    
else
    echo "⚠️ 发现一些问题需要处理"
    echo ""
    if [ $compilation_errors -gt 0 ]; then
        echo "❌ 编译错误数量: $compilation_errors"
    fi
    if [ $found_errors -gt 0 ]; then
        echo "❌ 错误模式数量: $found_errors"
    fi
    echo ""
    echo "🔧 建议检查和修复上述问题"
fi

echo ""
echo "========================================"
echo "特效模块重构影响验证完成"
echo "========================================"