#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# LoniceraLab 智能 Git 提交脚本 - 自动检查和规范化提交

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🔍 LoniceraLab 智能提交检查器启动...${NC}"
echo ""

# 检查是否在 Git 仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ 当前目录不是 Git 仓库${NC}"
    exit 1
fi

# 检查是否有待提交的更改
if git diff --cached --quiet; then
    echo -e "${YELLOW}⚠️ 没有待提交的更改，请先使用 'git add' 添加文件${NC}"
    exit 1
fi

echo -e "${BLUE}📋 执行 LoniceraLab 提交前检查...${NC}"

# 1. Swift 编译检查
echo -e "${BLUE}1️⃣ 检查 Swift 编译状态...${NC}"
if [ -f "Lomo.xcodeproj/project.pbxproj" ]; then
    # 检查是否有 Swift 语法错误
    swift_files=$(git diff --cached --name-only --diff-filter=AM | grep "\.swift$")
    if [ -n "$swift_files" ]; then
        echo "   检查 Swift 文件语法..."
        for file in $swift_files; do
            if [ -f "$file" ]; then
                # 基本语法检查
                if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
                    echo -e "${RED}   ❌ Swift 语法错误: $file${NC}"
                    echo -e "${RED}   请修复语法错误后再提交${NC}"
                    exit 1
                fi
            fi
        done
        echo -e "${GREEN}   ✅ Swift 语法检查通过${NC}"
    else
        echo "   没有 Swift 文件需要检查"
    fi
else
    echo "   跳过编译检查（非 Xcode 项目）"
fi

# 2. LoniceraLab 版权声明检查
echo -e "${BLUE}2️⃣ 检查 LoniceraLab 版权声明...${NC}"
missing_copyright=()
new_swift_files=$(git diff --cached --name-only --diff-filter=A | grep "\.swift$")

if [ -n "$new_swift_files" ]; then
    for file in $new_swift_files; do
        if [ -f "$file" ]; then
            if ! head -1 "$file" | grep -q "Copyright (c) 2025 LoniceraLab. All rights reserved."; then
                missing_copyright+=("$file")
            fi
        fi
    done
    
    if [ ${#missing_copyright[@]} -gt 0 ]; then
        echo -e "${YELLOW}   ⚠️ 以下新文件缺少 LoniceraLab 版权声明:${NC}"
        for file in "${missing_copyright[@]}"; do
            echo -e "${YELLOW}     - $file${NC}"
        done
        echo -e "${YELLOW}   建议添加版权声明: // Copyright (c) 2025 LoniceraLab. All rights reserved.${NC}"
    else
        echo -e "${GREEN}   ✅ 版权声明检查通过${NC}"
    fi
else
    echo "   没有新的 Swift 文件需要检查版权声明"
fi

# 3. 架构合规性检查
echo -e "${BLUE}3️⃣ 检查 MVVM-S 架构合规性...${NC}"
architecture_warnings=()

# 检查是否有新的单例使用
if git diff --cached | grep -q "\.shared"; then
    architecture_warnings+=("检测到可能的单例使用 (.shared)")
fi

# 检查是否在 View 中包含业务逻辑
view_files=$(git diff --cached --name-only | grep "Views/.*\.swift$")
if [ -n "$view_files" ]; then
    for file in $view_files; do
        if [ -f "$file" ]; then
            # 检查 View 文件中是否有复杂的业务逻辑
            if git diff --cached "$file" | grep -q "func.*{.*if.*else.*}"; then
                architecture_warnings+=("$file 可能包含复杂业务逻辑，建议移至 ViewModel")
            fi
        fi
    done
fi

# 检查 ViewModel 是否正确使用 @MainActor
viewmodel_files=$(git diff --cached --name-only | grep "ViewModels/.*\.swift$")
if [ -n "$viewmodel_files" ]; then
    for file in $viewmodel_files; do
        if [ -f "$file" ]; then
            if git diff --cached "$file" | grep -q "class.*ViewModel" && ! git diff --cached "$file" | grep -q "@MainActor"; then
                architecture_warnings+=("$file ViewModel 可能缺少 @MainActor 注解")
            fi
        fi
    done
fi

if [ ${#architecture_warnings[@]} -gt 0 ]; then
    echo -e "${YELLOW}   ⚠️ 架构合规性警告:${NC}"
    for warning in "${architecture_warnings[@]}"; do
        echo -e "${YELLOW}     - $warning${NC}"
    done
    echo -e "${YELLOW}   请确认是否符合 MVVM-S 架构标准${NC}"
else
    echo -e "${GREEN}   ✅ 架构合规性检查通过${NC}"
fi

# 4. 代码质量检查
echo -e "${BLUE}4️⃣ 检查代码质量...${NC}"
quality_issues=()

# 检查是否有调试代码
if git diff --cached | grep -q "print(\|NSLog(\|debugPrint("; then
    quality_issues+=("检测到调试输出代码 (print/NSLog)")
fi

# 检查是否有 TODO/FIXME 注释
if git diff --cached | grep -q "TODO\|FIXME\|XXX"; then
    quality_issues+=("检测到 TODO/FIXME 注释")
fi

# 检查是否有过长的行
long_lines=$(git diff --cached | grep "^+" | awk 'length > 120 {print NR ": " $0}')
if [ -n "$long_lines" ]; then
    quality_issues+=("检测到超过 120 字符的长行")
fi

if [ ${#quality_issues[@]} -gt 0 ]; then
    echo -e "${YELLOW}   ⚠️ 代码质量提醒:${NC}"
    for issue in "${quality_issues[@]}"; do
        echo -e "${YELLOW}     - $issue${NC}"
    done
else
    echo -e "${GREEN}   ✅ 代码质量检查通过${NC}"
fi

# 5. 提交信息建议
echo -e "${BLUE}5️⃣ 分析提交内容并建议提交信息...${NC}"

# 分析修改的文件类型
modified_files=$(git diff --cached --name-only)
arch_files=$(echo "$modified_files" | grep -E "(ViewModel|Service|DependencyContainer)" | wc -l)
view_files=$(echo "$modified_files" | grep "Views/" | wc -l)
doc_files=$(echo "$modified_files" | grep -E "\.(md|txt)$" | wc -l)
config_files=$(echo "$modified_files" | grep -E "\.(plist|xcconfig|json)$" | wc -l)
script_files=$(echo "$modified_files" | grep -E "Scripts/.*\.sh$" | wc -l)

echo -e "${CYAN}📊 提交内容分析:${NC}"
echo "   修改文件总数: $(echo "$modified_files" | wc -l)"
echo "   架构文件: $arch_files 个"
echo "   视图文件: $view_files 个"
echo "   文档文件: $doc_files 个"
echo "   配置文件: $config_files 个"
echo "   脚本文件: $script_files 个"

echo ""
echo -e "${CYAN}💡 建议的提交信息格式:${NC}"

if [ "$arch_files" -gt 0 ]; then
    echo -e "${GREEN}🏗️ arch: [模块名]MVVM-S重构 - [具体改进内容]${NC}"
    echo ""
    echo "示例:"
    echo "🏗️ arch: Gallery模块依赖注入实施"
    echo ""
    echo "🎯 重构内容:"
    echo "- 创建GalleryServiceProtocol接口"
    echo "- 实施构造函数依赖注入"
    echo "- 消除Manager.shared单例依赖"
    echo ""
    echo "📊 架构评分: XX分 → XX分"
    echo "✅ 编译通过，功能验证完成"
elif [ "$view_files" -gt 0 ] && [ "$arch_files" -eq 0 ]; then
    echo -e "${GREEN}🎨 ui: [界面改进描述]${NC}"
    echo ""
    echo "示例:"
    echo "🎨 ui: 优化相册界面布局和交互体验"
elif [ "$doc_files" -gt 0 ]; then
    echo -e "${GREEN}📚 docs: [文档更新内容]${NC}"
    echo ""
    echo "示例:"
    echo "📚 docs: 完善MVVM-S架构指南和最佳实践"
elif [ "$script_files" -gt 0 ]; then
    echo -e "${GREEN}🔧 config: [脚本或配置改进]${NC}"
    echo ""
    echo "示例:"
    echo "🔧 config: 新增智能Git提交检查脚本"
else
    echo -e "${GREEN}✨ feat: [新功能描述]${NC}"
    echo -e "${GREEN}🐛 fix: [问题修复描述]${NC}"
fi

echo ""
echo -e "${PURPLE}🎯 LoniceraLab 提交信息要点:${NC}"
echo "• 使用 emoji + 中文描述，便于团队理解"
echo "• 包含具体的技术改进内容"
echo "• 记录架构评分变化（如适用）"
echo "• 确认编译和功能验证状态"

# 6. 最终检查总结
echo ""
echo -e "${CYAN}📋 检查总结:${NC}"

total_issues=0
if [ ${#missing_copyright[@]} -gt 0 ]; then
    total_issues=$((total_issues + ${#missing_copyright[@]}))
fi
if [ ${#architecture_warnings[@]} -gt 0 ]; then
    total_issues=$((total_issues + ${#architecture_warnings[@]}))
fi
if [ ${#quality_issues[@]} -gt 0 ]; then
    total_issues=$((total_issues + ${#quality_issues[@]}))
fi

if [ $total_issues -eq 0 ]; then
    echo -e "${GREEN}✅ 所有检查通过，代码质量良好！${NC}"
    echo -e "${GREEN}🚀 可以安全提交到 LoniceraLab 项目${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️ 发现 $total_issues 个需要注意的问题${NC}"
    echo -e "${YELLOW}💡 建议修复后再提交，或确认问题不影响代码质量${NC}"
    
    echo ""
    read -p "是否继续提交？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${GREEN}✅ 用户确认继续提交${NC}"
        exit 0
    else
        echo -e "${BLUE}ℹ️ 用户取消提交，请修复问题后重试${NC}"
        exit 1
    fi
fi