#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🎨 特效模块MVVM-S重构 - 阶段1测试"
echo "=================================="

# 检查协议文件
echo "1️⃣ 检查Service协议文件..."

PROTOCOLS_DIR="Lomo/Services/Protocols"
REQUIRED_PROTOCOLS=(
    "EffectsServiceProtocol.swift"
    "LightLeakServiceProtocol.swift"
    "GrainServiceProtocol.swift"
    "ScratchServiceProtocol.swift"
    "StorageServiceProtocol.swift"
)

for protocol in "${REQUIRED_PROTOCOLS[@]}"; do
    if [ -f "$PROTOCOLS_DIR/$protocol" ]; then
        echo "✅ $protocol 存在"
    else
        echo "❌ $protocol 缺失"
        exit 1
    fi
done

# 检查Service实现文件
echo ""
echo "2️⃣ 检查Service实现文件..."

SERVICE_FILES=(
    "Lomo/Services/LightLeakService.swift"
    "Lomo/Services/GrainService.swift"
    "Lomo/Services/ScratchService.swift"
    "Lomo/Services/StorageService.swift"
)

for service in "${SERVICE_FILES[@]}"; do
    if [ -f "$service" ]; then
        echo "✅ $(basename $service) 存在"
    else
        echo "❌ $(basename $service) 缺失"
        exit 1
    fi
done

# 检查版权声明
echo ""
echo "3️⃣ 检查版权声明..."

check_copyright() {
    local file="$1"
    local expected="// Copyright (c) 2025 LoniceraLab. All rights reserved."
    
    if [ -f "$file" ]; then
        local first_line=$(head -n 1 "$file")
        if [ "$first_line" = "$expected" ]; then
            echo "✅ $(basename $file): 版权格式正确"
        else
            echo "❌ $(basename $file): 版权格式错误"
            echo "   期望: $expected"
            echo "   实际: $first_line"
            return 1
        fi
    fi
}

# 检查所有新创建的文件
for protocol in "${REQUIRED_PROTOCOLS[@]}"; do
    check_copyright "$PROTOCOLS_DIR/$protocol" || exit 1
done

for service in "${SERVICE_FILES[@]}"; do
    check_copyright "$service" || exit 1
done

# 语法检查
echo ""
echo "4️⃣ Swift语法检查..."

# 检查协议文件语法
for protocol in "${REQUIRED_PROTOCOLS[@]}"; do
    echo "   检查 $protocol 语法..."
    if ! swift -frontend -parse "$PROTOCOLS_DIR/$protocol" > /dev/null 2>&1; then
        echo "❌ $protocol 语法错误"
        swift -frontend -parse "$PROTOCOLS_DIR/$protocol"
        exit 1
    fi
done

# 检查Service文件语法
for service in "${SERVICE_FILES[@]}"; do
    echo "   检查 $(basename $service) 语法..."
    if ! swift -frontend -parse "$service" > /dev/null 2>&1; then
        echo "❌ $(basename $service) 语法错误"
        swift -frontend -parse "$service"
        exit 1
    fi
done

echo "✅ 所有文件语法检查通过"

# 检查Actor使用
echo ""
echo "5️⃣ 检查Actor模式使用..."

check_actor_usage() {
    local file="$1"
    local filename=$(basename "$file")
    
    if grep -q "actor.*Service.*:" "$file"; then
        echo "✅ $filename: 正确使用Actor模式"
    else
        echo "❌ $filename: 未使用Actor模式"
        return 1
    fi
}

# 检查Service实现是否使用Actor
for service in "${SERVICE_FILES[@]}"; do
    check_actor_usage "$service" || exit 1
done

# 检查协议继承
echo ""
echo "6️⃣ 检查协议继承..."

check_protocol_inheritance() {
    local file="$1"
    local filename=$(basename "$file")
    
    if grep -q "protocol.*: Actor" "$file"; then
        echo "✅ $filename: 正确继承Actor协议"
    else
        echo "❌ $filename: 未继承Actor协议"
        return 1
    fi
}

# 检查协议是否继承Actor
for protocol in "${REQUIRED_PROTOCOLS[@]}"; do
    if [[ "$protocol" != "StorageServiceProtocol.swift" ]]; then
        check_protocol_inheritance "$PROTOCOLS_DIR/$protocol" || exit 1
    fi
done

# 检查依赖注入
echo ""
echo "7️⃣ 检查依赖注入模式..."

check_dependency_injection() {
    local file="$1"
    local filename=$(basename "$file")
    
    if grep -q "init.*:" "$file" && ! grep -q "static let shared" "$file"; then
        echo "✅ $filename: 使用依赖注入，无单例模式"
    else
        echo "❌ $filename: 仍使用单例模式或缺少依赖注入"
        return 1
    fi
}

# 检查Service实现的依赖注入
for service in "${SERVICE_FILES[@]}"; do
    check_dependency_injection "$service" || exit 1
done

echo ""
echo "🎉 阶段1测试完成！"
echo "=================================="
echo "✅ 所有协议文件创建完成"
echo "✅ 所有Service实现重构完成"
echo "✅ 版权声明格式正确"
echo "✅ Swift语法检查通过"
echo "✅ Actor模式使用正确"
echo "✅ 依赖注入模式实施"
echo "✅ 消除单例依赖"
echo ""
echo "📝 下一步: 执行阶段2 - 重构EffectsService和ViewModel"