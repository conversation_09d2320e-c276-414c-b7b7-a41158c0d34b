#!/bin/bash

# 真正重构第1步测试脚本
echo "🧪 真正重构第1步：直接SwiftData操作验证"
echo "================================"

# 检查WatermarkService是否移除了Manager依赖
echo ""
echo "🔍 检查Manager依赖移除..."

if ! grep -q "WatermarkSettingsManager\.shared" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ WatermarkService：已移除Manager依赖"
else
    echo "❌ WatermarkService：仍然依赖Manager"
    echo "发现的Manager调用："
    grep -n "WatermarkSettingsManager\.shared" "Lomo/Services/Edit/WatermarkService.swift"
    exit 1
fi

# 检查是否直接使用SwiftData
if grep -q "modelContext" "Lomo/Services/Edit/WatermarkService.swift" && \
   grep -q "FetchDescriptor" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ WatermarkService：正确使用SwiftData直接操作"
else
    echo "❌ WatermarkService：未找到SwiftData直接操作"
    exit 1
fi

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查关键方法实现
echo ""
echo "🔍 检查关键方法实现..."

METHODS=(
    "func getSettings"
    "func saveSettings"
    "func updateSetting"
    "func resetToDefaults"
)

for method in "${METHODS[@]}"; do
    if grep -q "$method" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ $method：方法存在"
    else
        echo "❌ $method：方法缺失"
        exit 1
    fi
done

# 检查SwiftData操作
echo ""
echo "🗄️ 检查SwiftData操作..."

SWIFTDATA_OPERATIONS=(
    "FetchDescriptor<WatermarkSettings>"
    "modelContext.fetch"
    "modelContext.insert"
    "modelContext.save"
    "modelContext.delete"
)

for operation in "${SWIFTDATA_OPERATIONS[@]}"; do
    if grep -q "$operation" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ $operation：操作存在"
    else
        echo "❌ $operation：操作缺失"
        exit 1
    fi
done

# 检查原始Manager是否保持完整
echo ""
echo "🛡️ 检查原始Manager完整性..."

if [ -f "Lomo/Managers/Edit/WatermarkSettingsManager.swift" ]; then
    MANAGER_LINES=$(wc -l < "Lomo/Managers/Edit/WatermarkSettingsManager.swift")
    if [ "$MANAGER_LINES" -eq 130 ]; then
        echo "✅ WatermarkSettingsManager：保持原始状态（$MANAGER_LINES 行）"
    else
        echo "⚠️ WatermarkSettingsManager：可能被修改（$MANAGER_LINES 行，期望130行）"
    fi
else
    echo "❌ WatermarkSettingsManager：文件缺失"
    exit 1
fi

# 最终验证
echo ""
echo "🎉 真正重构第1步验证结果"
echo "================================"
echo "✅ WatermarkService完全移除Manager依赖"
echo "✅ 直接使用SwiftData进行数据操作"
echo "✅ 所有关键方法正确实现"
echo "✅ 项目编译正常"
echo "✅ 原始Manager文件保持完整"
echo ""
echo "🔄 当前状态："
echo "   - WatermarkService：真正的SwiftData实现"
echo "   - WatermarkSettingsManager：保持完整（向后兼容）"
echo "   - 两套数据访问方式并存"
echo ""
echo "📋 下一步："
echo "   - 可以开始第2步：迁移WatermarkControlView调用"
echo "   - 逐步将Manager.shared调用替换为Service调用"
echo ""
echo "🎯 重构进展："
echo "   - ✅ 第1步：真正的Service层实现完成"
echo "   - ⏳ 第2步：迁移现有调用"
echo "   - ⏳ 第3步：移除Manager文件"
echo "   - ⏳ 第4步：验证功能完整性"
