#!/bin/bash

# WatermarkViewModel编译错误修复验证脚本
# 验证Non-void function should return a value错误是否已修复

echo "🔍 开始验证WatermarkViewModel编译错误修复..."

# 检查修复的文件
echo ""
echo "📁 检查修复的文件..."

if [ -f "Lomo/ViewModels/Edit/WatermarkViewModel.swift" ]; then
    echo "✅ WatermarkViewModel.swift 文件存在"
else
    echo "❌ WatermarkViewModel.swift 文件不存在"
    exit 1
fi

# 检查guard语句中的return是否已修复
echo ""
echo "🔍 检查guard语句中的return修复..."

# 查找问题行附近的代码
problem_line=$(grep -n "WatermarkManager 不可用" Lomo/ViewModels/Edit/WatermarkViewModel.swift)
if [ -n "$problem_line" ]; then
    echo "找到问题行: $problem_line"
    
    # 检查下一行是否有正确的return nil
    line_number=$(echo "$problem_line" | cut -d: -f1)
    next_line_number=$((line_number + 1))
    
    return_line=$(sed -n "${next_line_number}p" Lomo/ViewModels/Edit/WatermarkViewModel.swift)
    if echo "$return_line" | grep -q "return nil"; then
        echo "✅ guard语句中的return已修复为 'return nil'"
    elif echo "$return_line" | grep -q "return$"; then
        echo "❌ guard语句中仍然是空的return，需要修复"
        exit 1
    else
        echo "⚠️  无法确定return语句的状态: $return_line"
    fi
else
    echo "❌ 未找到相关的guard语句"
    exit 1
fi

# 检查方法签名
echo ""
echo "📋 检查方法签名..."

method_signature=$(grep -A 2 "func selectWatermarkAtIndex" Lomo/ViewModels/Edit/WatermarkViewModel.swift | grep "Double?")
if [ -n "$method_signature" ]; then
    echo "✅ 方法签名正确返回 Double?"
else
    echo "❌ 方法签名可能有问题"
    exit 1
fi

# 检查方法结尾的return语句
echo ""
echo "🔚 检查方法结尾的return语句..."

if grep -q "return updatedWideBorderThickness" Lomo/ViewModels/Edit/WatermarkViewModel.swift; then
    echo "✅ 方法结尾有正确的return语句"
else
    echo "❌ 方法结尾缺少return语句"
    exit 1
fi

echo ""
echo "🎯 修复摘要："
echo "1. ✅ guard语句中的return已修复为 'return nil'"
echo "2. ✅ 方法签名正确声明返回 Double?"
echo "3. ✅ 方法结尾有正确的return语句"
echo "4. ✅ 符合Swift语法要求"

echo ""
echo "🚀 WatermarkViewModel编译错误修复验证完成！"