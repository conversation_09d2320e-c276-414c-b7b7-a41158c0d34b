#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🧪 渲染服务成员修复验证脚本
# 验证 RenderingMode 和 FilterParameters 的缺失成员是否已修复

echo "🧪 开始验证渲染服务成员修复..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查 RenderingMode 成员完整性
echo "1️⃣ 检查 RenderingMode 成员完整性..."

required_members=("lightroom" "vsco" "realtime" "highQuality" "preview")
missing_members=()

for member in "${required_members[@]}"; do
    if grep -q "case $member" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode 包含 $member 成员"
    else
        echo "   ❌ RenderingMode 缺少 $member 成员"
        missing_members+=("$member")
    fi
done

if [ ${#missing_members[@]} -eq 0 ]; then
    echo "   🎉 RenderingMode 成员完整"
else
    echo "   ⚠️ RenderingMode 缺少成员: ${missing_members[*]}"
fi

echo ""

# 2. 检查 FilterParameters 方法完整性
echo "2️⃣ 检查 FilterParameters 方法完整性..."

required_methods=("hasActiveAdjustments" "resetToDefaults")
missing_methods=()

for method in "${required_methods[@]}"; do
    if grep -q "func $method" Lomo/Models/Edit/FilterParameters.swift; then
        echo "   ✅ FilterParameters 包含 $method 方法"
    else
        echo "   ❌ FilterParameters 缺少 $method 方法"
        missing_methods+=("$method")
    fi
done

# 检查计算属性
if grep -q "var activeParametersCount" Lomo/Models/Edit/FilterParameters.swift; then
    echo "   ✅ FilterParameters 包含 activeParametersCount 属性"
else
    echo "   ❌ FilterParameters 缺少 activeParametersCount 属性"
    missing_methods+=("activeParametersCount")
fi

if [ ${#missing_methods[@]} -eq 0 ]; then
    echo "   🎉 FilterParameters 方法完整"
else
    echo "   ⚠️ FilterParameters 缺少方法: ${missing_methods[*]}"
fi

echo ""

# 3. 检查原始错误解决情况
echo "3️⃣ 检查原始错误解决情况..."

echo "   🔍 检查 RenderingServiceImpl 中的 preview 使用..."
preview_usage=$(grep -n "\.preview" Lomo/Services/Implementations/RenderingServiceImpl.swift | wc -l)
if [ "$preview_usage" -gt 0 ]; then
    echo "   📊 发现 $preview_usage 处 .preview 使用"
    if grep -q "case preview" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode.preview 定义存在，使用问题已解决"
    else
        echo "   ❌ RenderingMode.preview 定义缺失，使用问题未解决"
    fi
else
    echo "   ℹ️ 未发现 .preview 使用"
fi

echo "   🔍 检查 RenderingServiceImpl 中的 hasActiveAdjustments 使用..."
hasactive_usage=$(grep -n "hasActiveAdjustments()" Lomo/Services/Implementations/RenderingServiceImpl.swift | wc -l)
if [ "$hasactive_usage" -gt 0 ]; then
    echo "   📊 发现 $hasactive_usage 处 hasActiveAdjustments() 使用"
    if grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then
        echo "   ✅ FilterParameters.hasActiveAdjustments 定义存在，使用问题已解决"
    else
        echo "   ❌ FilterParameters.hasActiveAdjustments 定义缺失，使用问题未解决"
    fi
else
    echo "   ℹ️ 未发现 hasActiveAdjustments() 使用"
fi

echo ""

# 4. 语法验证
echo "4️⃣ 语法验证..."

critical_files=(
    "Lomo/Models/Edit/RenderingMode.swift"
    "Lomo/Models/Edit/FilterParameters.swift"
    "Lomo/Services/Implementations/RenderingServiceImpl.swift"
)

syntax_errors=0
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            ((syntax_errors++))
            # 显示前几行错误信息
            echo "      错误详情:"
            swift -frontend -parse "$file" 2>&1 | head -3 | sed 's/^/      /'
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
        ((syntax_errors++))
    fi
done

echo ""

# 5. 功能验证
echo "5️⃣ 功能验证..."

echo "   🔍 验证 RenderingMode 属性支持..."
properties=("displayName" "shaderFunctionName" "description")
for prop in "${properties[@]}"; do
    if grep -q "var $prop:" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode 支持 $prop 属性"
    else
        echo "   ❌ RenderingMode 缺少 $prop 属性"
    fi
done

echo "   🔍 验证 FilterParameters 方法实现..."
if grep -A 10 "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift | grep -q "return"; then
    echo "   ✅ hasActiveAdjustments 方法有返回值实现"
else
    echo "   ❌ hasActiveAdjustments 方法实现不完整"
fi

echo ""

# 6. 编译测试（如果可能）
echo "6️⃣ 编译测试..."

echo "   🔨 尝试编译关键文件..."
if command -v swiftc >/dev/null 2>&1; then
    # 尝试编译 RenderingMode
    if swiftc -parse Lomo/Models/Edit/RenderingMode.swift >/dev/null 2>&1; then
        echo "   ✅ RenderingMode.swift 编译通过"
    else
        echo "   ❌ RenderingMode.swift 编译失败"
    fi
    
    # 尝试编译 FilterParameters
    if swiftc -parse Lomo/Models/Edit/FilterParameters.swift >/dev/null 2>&1; then
        echo "   ✅ FilterParameters.swift 编译通过"
    else
        echo "   ❌ FilterParameters.swift 编译失败"
    fi
else
    echo "   ℹ️ Swift 编译器不可用，跳过编译测试"
fi

echo ""

# 7. 总结报告
echo "7️⃣ 修复验证总结..."

# 计算成功项目数
success_count=0
total_checks=6

# 检查各项是否成功
if [ ${#missing_members[@]} -eq 0 ]; then ((success_count++)); fi
if [ ${#missing_methods[@]} -eq 0 ]; then ((success_count++)); fi
if [ "$preview_usage" -gt 0 ] && grep -q "case preview" Lomo/Models/Edit/RenderingMode.swift; then ((success_count++)); fi
if [ "$hasactive_usage" -gt 0 ] && grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then ((success_count++)); fi
if [ "$syntax_errors" -eq 0 ]; then ((success_count++)); fi
if grep -A 10 "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift | grep -q "return"; then ((success_count++)); fi

echo "   📊 验证结果: $success_count/$total_checks 项检查通过"

if [ "$success_count" -eq "$total_checks" ]; then
    echo "   🎉 渲染服务成员修复验证完全成功！"
    echo ""
    echo "✅ 修复验证成果："
    echo "   • RenderingMode 包含所有必需成员 (5个)"
    echo "   • FilterParameters 包含所有必需方法"
    echo "   • 原始编译错误已解决"
    echo "   • 语法检查通过"
    echo "   • 功能实现完整"
    echo ""
    echo "🎯 解决的原始错误："
    echo "   ✅ Type 'RenderingMode' has no member 'preview' → 已解决"
    echo "   ✅ Value of type 'FilterParameters' has no member 'hasActiveAdjustments' → 已解决"
    echo ""
    echo "🎯 当前系统状态："
    echo "   📁 RenderingMode: 5个成员 (lightroom, vsco, realtime, highQuality, preview)"
    echo "   📁 FilterParameters: 新增3个方法 (hasActiveAdjustments, resetToDefaults, activeParametersCount)"
else
    echo "   ⚠️ 部分检查未通过，需要进一步修复"
    echo "   📋 未通过的检查项目数: $((total_checks - success_count))"
    
    # 详细报告未通过的项目
    if [ ${#missing_members[@]} -gt 0 ]; then
        echo "   • RenderingMode 缺少成员: ${missing_members[*]}"
    fi
    if [ ${#missing_methods[@]} -gt 0 ]; then
        echo "   • FilterParameters 缺少方法: ${missing_methods[*]}"
    fi
    if [ "$syntax_errors" -gt 0 ]; then
        echo "   • 语法错误数量: $syntax_errors"
    fi
fi

echo ""
echo "🏁 渲染服务成员修复验证完成！"