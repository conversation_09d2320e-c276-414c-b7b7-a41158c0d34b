#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# LoniceraLab Git Hooks 设置脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${CYAN}🔗 LoniceraLab Git Hooks 设置器${NC}"
echo ""

# 检查是否在 Git 仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}❌ 当前目录不是 Git 仓库${NC}"
    exit 1
fi

HOOKS_DIR=".git/hooks"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 确保 hooks 目录存在
if [ ! -d "$HOOKS_DIR" ]; then
    echo -e "${RED}❌ Git hooks 目录不存在: $HOOKS_DIR${NC}"
    exit 1
fi

echo -e "${BLUE}📋 可用的 Git Hooks 设置:${NC}"
echo "1) pre-commit - 提交前检查"
echo "2) commit-msg - 提交信息验证"
echo "3) pre-push - 推送前检查"
echo "4) 全部设置"
echo "5) 移除所有 hooks"
echo "0) 退出"
echo ""

# 函数：创建 pre-commit hook
create_pre_commit_hook() {
    local hook_file="$HOOKS_DIR/pre-commit"
    
    cat > "$hook_file" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab pre-commit hook

echo "🔍 LoniceraLab 提交前检查..."

# 获取脚本目录
SCRIPT_DIR="$(git rev-parse --show-toplevel)/Lomo/Scripts"
SMART_COMMIT="$SCRIPT_DIR/smart_git_commit.sh"
SMART_CLEANER="$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"

# 运行智能提交检查
if [ -x "$SMART_COMMIT" ]; then
    if ! "$SMART_COMMIT"; then
        echo "❌ 提交前检查失败"
        exit 1
    fi
else
    echo "⚠️ 智能提交检查脚本不存在或不可执行"
fi

# 检查 Xcode 缓存状态
if [ -x "$SMART_CLEANER" ]; then
    if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
        echo ""
        echo "💡 提示: 检测到 Xcode 缓存可能需要清理"
        echo "   运行: ./Lomo/Scripts/smart_xcode_cache_cleaner.sh"
        echo ""
    fi
fi

echo "✅ 提交前检查完成"
EOF
    
    chmod +x "$hook_file"
    echo -e "${GREEN}✅ 创建 pre-commit hook: $hook_file${NC}"
}

# 函数：创建 commit-msg hook
create_commit_msg_hook() {
    local hook_file="$HOOKS_DIR/commit-msg"
    
    cat > "$hook_file" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab commit-msg hook

commit_msg_file="$1"
commit_msg=$(cat "$commit_msg_file")

echo "📝 验证 LoniceraLab 提交信息格式..."

# 检查是否包含 emoji
if ! echo "$commit_msg" | grep -qE "^[🏗️✨🐛📱🎨⚡📚🔧🧹🚨🔒🌐]"; then
    echo "❌ 提交信息缺少 LoniceraLab 标准 emoji"
    echo "💡 建议使用: 🏗️ arch, ✨ feat, 🐛 fix, 📚 docs 等"
    echo ""
    echo "当前提交信息:"
    echo "$commit_msg"
    exit 1
fi

# 检查提交信息长度
first_line=$(echo "$commit_msg" | head -n1)
if [ ${#first_line} -gt 100 ]; then
    echo "⚠️ 提交信息首行过长 (${#first_line} > 100 字符)"
    echo "建议简化描述或使用多行格式"
fi

# 检查是否包含中文描述（推荐但不强制）
if ! echo "$commit_msg" | grep -q "[\u4e00-\u9fa5]"; then
    echo "💡 提示: 建议在提交信息中包含中文描述，便于团队理解"
fi

echo "✅ 提交信息格式验证通过"
EOF
    
    chmod +x "$hook_file"
    echo -e "${GREEN}✅ 创建 commit-msg hook: $hook_file${NC}"
}

# 函数：创建 pre-push hook
create_pre_push_hook() {
    local hook_file="$HOOKS_DIR/pre-push"
    
    cat > "$hook_file" << 'EOF'
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# LoniceraLab pre-push hook

echo "🚀 LoniceraLab 推送前检查..."

# 检查当前分支
current_branch=$(git branch --show-current)
echo "当前分支: $current_branch"

# 检查是否在 main 分支直接推送
if [ "$current_branch" = "main" ]; then
    echo "⚠️ 正在向 main 分支推送"
    read -p "确认推送到 main 分支？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 用户取消推送"
        exit 1
    fi
fi

# 检查是否有未提交的更改
if ! git diff-index --quiet HEAD --; then
    echo "❌ 存在未提交的更改，请先提交"
    git status --porcelain
    exit 1
fi

# 检查最近的提交是否符合 LoniceraLab 规范
recent_commits=$(git log --oneline -5)
echo ""
echo "📋 最近的提交:"
echo "$recent_commits"

# 检查是否有符合规范的 emoji 提交
if ! echo "$recent_commits" | grep -qE "[🏗️✨🐛📱🎨⚡📚🔧🧹🚨🔒🌐]"; then
    echo ""
    echo "⚠️ 最近的提交可能不符合 LoniceraLab 提交规范"
    echo "💡 建议使用标准 emoji 和格式"
    
    read -p "是否继续推送？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ 用户取消推送"
        exit 1
    fi
fi

echo "✅ 推送前检查完成"
EOF
    
    chmod +x "$hook_file"
    echo -e "${GREEN}✅ 创建 pre-push hook: $hook_file${NC}"
}

# 函数：移除所有 hooks
remove_all_hooks() {
    local hooks=("pre-commit" "commit-msg" "pre-push")
    
    for hook in "${hooks[@]}"; do
        local hook_file="$HOOKS_DIR/$hook"
        if [ -f "$hook_file" ]; then
            rm "$hook_file"
            echo -e "${GREEN}✅ 移除 $hook hook${NC}"
        fi
    done
}

# 函数：显示当前 hooks 状态
show_hooks_status() {
    echo -e "${BLUE}📊 当前 Git Hooks 状态:${NC}"
    
    local hooks=("pre-commit" "commit-msg" "pre-push")
    for hook in "${hooks[@]}"; do
        local hook_file="$HOOKS_DIR/$hook"
        if [ -f "$hook_file" ] && [ -x "$hook_file" ]; then
            echo -e "   ${GREEN}✅ $hook${NC}"
        else
            echo -e "   ${RED}❌ $hook${NC}"
        fi
    done
}

# 主循环
while true; do
    read -p "请选择操作 (0-5): " choice
    echo ""
    
    case $choice in
        1)
            create_pre_commit_hook
            ;;
        2)
            create_commit_msg_hook
            ;;
        3)
            create_pre_push_hook
            ;;
        4)
            echo -e "${BLUE}🔧 设置所有 Git Hooks...${NC}"
            create_pre_commit_hook
            create_commit_msg_hook
            create_pre_push_hook
            echo -e "${GREEN}🎉 所有 Git Hooks 设置完成！${NC}"
            ;;
        5)
            echo -e "${YELLOW}🗑️ 移除所有 Git Hooks...${NC}"
            remove_all_hooks
            echo -e "${GREEN}✅ 所有 Git Hooks 已移除${NC}"
            ;;
        0)
            echo -e "${CYAN}👋 退出 Git Hooks 设置器${NC}"
            break
            ;;
        *)
            echo -e "${RED}❌ 无效选择，请重新输入${NC}"
            ;;
    esac
    
    echo ""
    show_hooks_status
    echo ""
done