#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🧪 特效模块测试文件修复验证"
echo "================================"

# 检查测试文件编译状态
echo "1️⃣ 检查测试文件编译状态..."

TEST_FILES=(
    "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift"
)

compilation_errors=0
for file in "${TEST_FILES[@]}"; do
    echo "   检查 $(basename $file)..."
    if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
        echo "❌ $(basename $file) 编译失败"
        echo "错误详情:"
        swift -frontend -parse "$file"
        compilation_errors=$((compilation_errors + 1))
    else
        echo "✅ $(basename $file) 编译通过"
    fi
done

if [ $compilation_errors -gt 0 ]; then
    echo ""
    echo "❌ 发现 $compilation_errors 个编译错误"
    exit 1
fi

# 检查单例调用是否已消除
echo ""
echo "2️⃣ 检查单例调用消除..."

SINGLETON_PATTERNS=(
    "LightLeakService.shared"
    "GrainService.shared"
    "ScratchService.shared"
)

singleton_found=0
for pattern in "${SINGLETON_PATTERNS[@]}"; do
    if grep -q "$pattern" "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift"; then
        echo "❌ 仍存在单例调用: $pattern"
        singleton_found=$((singleton_found + 1))
    else
        echo "✅ 已消除单例调用: $pattern"
    fi
done

if [ $singleton_found -gt 0 ]; then
    echo "❌ 发现 $singleton_found 个未消除的单例调用"
    exit 1
fi

# 检查依赖注入容器使用
echo ""
echo "3️⃣ 检查依赖注入容器使用..."

if grep -q "EffectsDependencyContainer.shared" "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift"; then
    echo "✅ 测试文件使用依赖注入容器"
else
    echo "❌ 测试文件未使用依赖注入容器"
    exit 1
fi

# 检查异步调用
echo ""
echo "4️⃣ 检查异步调用..."

if grep -q "Task {" "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift" && grep -q "await" "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift"; then
    echo "✅ 测试文件使用异步调用"
else
    echo "❌ 测试文件未使用异步调用"
    exit 1
fi

# 检查协议使用
echo ""
echo "5️⃣ 检查协议使用..."

PROTOCOL_USAGE=(
    "lightLeakService"
    "grainService"
    "scratchService"
)

protocol_usage_count=0
for usage in "${PROTOCOL_USAGE[@]}"; do
    if grep -q "$usage" "Lomo/Rendering/Tests/MetalSpecialEffectsTest.swift"; then
        echo "✅ 使用协议服务: $usage"
        protocol_usage_count=$((protocol_usage_count + 1))
    else
        echo "❌ 未使用协议服务: $usage"
    fi
done

if [ $protocol_usage_count -eq ${#PROTOCOL_USAGE[@]} ]; then
    echo "✅ 所有协议服务都已使用"
else
    echo "❌ 部分协议服务未使用"
    exit 1
fi

# 最终编译测试
echo ""
echo "6️⃣ 最终编译测试..."

echo "   尝试编译所有测试文件..."
all_tests_compile=true
for file in "${TEST_FILES[@]}"; do
    if ! swift -frontend -parse "$file" > /dev/null 2>&1; then
        echo "❌ $(basename $file) 最终编译失败"
        all_tests_compile=false
    fi
done

if [ "$all_tests_compile" = true ]; then
    echo "✅ 所有测试文件最终编译通过"
else
    echo "❌ 仍有测试文件编译失败"
    exit 1
fi

echo ""
echo "🎉 特效模块测试文件修复完成！"
echo "================================"
echo "✅ 单例调用已消除"
echo "✅ 依赖注入容器已使用"
echo "✅ 异步调用已实现"
echo "✅ 协议服务已使用"
echo "✅ 所有测试文件编译通过"
echo ""
echo "🚀 测试文件现已完全兼容MVVM-S架构！"