#!/bin/bash

# smart_duplication_check.sh - 智能重复检查脚本
# Copyright (c) 2025 LoniceraLab. All rights reserved.

FILENAME="$1"
FUNCTIONALITY="$2"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

if [ -z "$FILENAME" ]; then
    echo -e "${RED}用法: $0 <filename> [functionality]${NC}"
    echo -e "${YELLOW}示例: $0 UIConstants.swift '常量定义'${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 智能检查文件重复: $FILENAME${NC}"
echo -e "${PURPLE}📋 功能类型: ${FUNCTIONALITY:-'未指定'}${NC}"
echo ""

# 1. 检查完全匹配
echo -e "${BLUE}1️⃣ 检查同名文件...${NC}"
if find . -name "$FILENAME" -type f | grep -q .; then
    echo -e "${RED}❌ 发现同名文件:${NC}"
    find . -name "$FILENAME" -type f
    echo ""
    echo -e "${RED}🚨 文件已存在！请使用现有文件，不要重复创建！${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 无同名文件${NC}"
fi

echo ""

# 2. 检查功能相似文件
echo -e "${BLUE}2️⃣ 检查功能相似文件...${NC}"
BASENAME=$(basename "$FILENAME" .swift)

case "$BASENAME" in
    *Constants*)
        echo -e "${YELLOW}🔍 检查常量定义文件...${NC}"
        echo -e "${PURPLE}现有常量文件:${NC}"
        find Lomo/Utils/Constants -name "*.swift" -type f | while read file; do
            echo "  📄 $file"
        done
        echo ""
        echo -e "${YELLOW}💡 重要提示：${NC}"
        echo "• CameraConstants.swift 包含 UIConstants 和 AnimationConstants"
        echo "• WatermarkConstants.swift 包含水印相关常量"
        echo "• 请先检查现有常量文件是否已包含所需常量！"
        echo ""
        echo -e "${BLUE}🔍 快速检查命令：${NC}"
        echo "grep -r \"你需要的常量名\" Lomo/Utils/Constants/"
        ;;
    *Utils*)
        echo -e "${YELLOW}🔍 检查工具类文件...${NC}"
        echo -e "${PURPLE}现有工具类:${NC}"
        find Lomo/Utils -name "*Utils.swift" -type f | while read file; do
            echo "  🛠️  $file"
        done
        echo ""
        echo -e "${YELLOW}💡 重要提示：${NC}"
        echo "• MaskUtils.swift - 遮罩工具类"
        echo "• LayoutUtils.swift - 布局工具类"
        echo "• FontUtils.swift - 字体工具类"
        echo "• 请先检查现有工具类是否已包含所需功能！"
        ;;
    *Service*)
        echo -e "${YELLOW}🔍 检查服务类文件...${NC}"
        echo -e "${PURPLE}现有服务类:${NC}"
        find Lomo/Services -name "*Service.swift" -type f | head -10 | while read file; do
            echo "  ⚙️  $file"
        done
        echo "  ... (更多服务文件)"
        echo ""
        echo -e "${YELLOW}💡 重要提示：${NC}"
        echo "• 请先检查现有服务是否已包含所需功能！"
        echo "• 考虑扩展现有服务而不是创建新服务"
        ;;
    *View*)
        echo -e "${YELLOW}🔍 检查UI组件文件...${NC}"
        echo -e "${PURPLE}通用组件:${NC}"
        find Lomo/Views/Components -name "*.swift" -type f | while read file; do
            echo "  🎨 $file"
        done
        echo -e "${PURPLE}共享视图:${NC}"
        find Lomo/Views/Shared -name "*.swift" -type f | while read file; do
            echo "  🔗 $file"
        done
        echo ""
        echo -e "${YELLOW}💡 重要提示：${NC}"
        echo "• 请先检查现有组件是否可以复用！"
        ;;
    *)
        echo -e "${YELLOW}🔍 检查相似命名文件...${NC}"
        similar_files=$(find . -name "*$BASENAME*" -type f)
        if [ -n "$similar_files" ]; then
            echo -e "${PURPLE}相似文件:${NC}"
            echo "$similar_files" | while read file; do
                echo "  📄 $file"
            done
        else
            echo -e "${GREEN}✅ 无相似命名文件${NC}"
        fi
        ;;
esac

echo ""

# 3. 提供资源索引提醒
echo -e "${BLUE}📚 重要提醒：${NC}"
echo -e "${YELLOW}请先查阅项目资源索引: Lomo/Documentation/ProjectResourcesIndex.md${NC}"
echo "该文档详细列出了所有可复用的资源和使用方法"

echo ""

# 4. 提供检查建议
echo -e "${BLUE}🔍 建议的检查步骤：${NC}"
echo "1. 查阅项目资源索引文档"
echo "2. 使用 grep 搜索相关功能关键词"
echo "3. 检查现有文件的注释和功能说明"
echo "4. 确认真的需要创建新文件后再创建"

echo ""
echo -e "${GREEN}✅ 检查完成${NC}"
echo -e "${YELLOW}⚠️  请确认是否真的需要创建新文件！${NC}"