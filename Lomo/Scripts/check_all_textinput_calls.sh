#!/bin/bash

# 检查所有TextInputOptionView调用脚本
echo "🔍 检查所有TextInputOptionView调用"
echo "================================"

FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

echo ""
echo "🔍 分析所有TextInputOptionView调用..."

# 找到所有TextInputOptionView调用的行号
TEXTINPUT_LINES=$(grep -n "TextInputOptionView(" "$FILE" | cut -d: -f1)

echo "📊 发现 $(echo "$TEXTINPUT_LINES" | wc -l) 个TextInputOptionView调用"

# 检查每个调用的参数结构
for line in $TEXTINPUT_LINES; do
    echo ""
    echo "🔍 检查第 $line 行的调用..."
    
    # 提取这个调用的完整内容（从TextInputOptionView到对应的右括号）
    CALL_CONTENT=$(sed -n "${line},/^[[:space:]]*)/p" "$FILE")
    
    # 检查参数结构
    if echo "$CALL_CONTENT" | grep -q "onUpdateSetting: { newValue in"; then
        echo "  ✅ onUpdateSetting: 正确的闭包"
    elif echo "$CALL_CONTENT" | grep -q "onUpdateSetting: watermarkService"; then
        echo "  ❌ onUpdateSetting: 错误的service对象"
        exit 1
    else
        echo "  ⚠️ onUpdateSetting: 未找到或格式异常"
    fi
    
    if echo "$CALL_CONTENT" | grep -q "onUpdateEnabled: { newValue in"; then
        echo "  ✅ onUpdateEnabled: 正确的闭包"
    else
        echo "  ⚠️ onUpdateEnabled: 未找到或格式异常"
    fi
    
    if echo "$CALL_CONTENT" | grep -q "onApplyStyle: {$"; then
        echo "  ✅ onApplyStyle: 正确的无参数闭包"
    elif echo "$CALL_CONTENT" | grep -q "onApplyStyle: { newValue in"; then
        echo "  ❌ onApplyStyle: 错误的有参数闭包"
        exit 1
    else
        echo "  ⚠️ onApplyStyle: 未找到或格式异常"
    fi
    
    if echo "$CALL_CONTENT" | grep -q "watermarkService: watermarkService"; then
        echo "  ✅ watermarkService: 正确的对象传递"
    else
        echo "  ⚠️ watermarkService: 未找到或格式异常"
    fi
done

# 检查Service调用总数
SERVICE_CALLS=$(grep -c "watermarkService\." "$FILE")
echo ""
echo "📊 Service调用统计："
echo "   - 总调用数: $SERVICE_CALLS"

if [ "$SERVICE_CALLS" -gt 140 ]; then
    echo "   - 状态: ✅ 正常"
else
    echo "   - 状态: ❌ 异常"
    exit 1
fi

# 最终验证
echo ""
echo "🎉 TextInputOptionView调用检查结果"
echo "================================"
echo "✅ 项目编译成功"
echo "✅ 所有TextInputOptionView调用结构正确"
echo "✅ Service调用总数正常（$SERVICE_CALLS 个）"
echo ""
echo "🎯 所有参数类型匹配："
echo "   - onUpdateSetting: (String) -> Void ✅"
echo "   - onUpdateEnabled: (Bool) -> Void ✅"
echo "   - onApplyStyle: () -> Void ✅"
echo "   - watermarkService: WatermarkService ✅"
echo ""
echo "📋 下一步："
echo "   - 真正重构已经完全成功"
echo "   - 所有编译错误都已解决"
echo "   - 可以继续第3步：移除Manager文件"
