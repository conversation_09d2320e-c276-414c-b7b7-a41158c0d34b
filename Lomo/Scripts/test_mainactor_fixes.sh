#!/bin/bash

# MainActor隔离和协议方法修复验证脚本
# 验证MainActor相关编译错误是否已修复

echo "🔍 开始验证MainActor隔离和协议方法修复..."

# 检查SubscriptionDependencyContainer中的MainActor修复
echo ""
echo "🎭 检查MainActor隔离修复..."

# 检查createSubscriptionViewModel方法是否添加了@MainActor
if grep -B 1 "func createSubscriptionViewModel" Lomo/DependencyInjection/SubscriptionDependencyContainer.swift | grep -q "@MainActor"; then
    echo "✅ createSubscriptionViewModel方法已添加@MainActor"
else
    echo "❌ createSubscriptionViewModel方法缺少@MainActor"
    exit 1
fi

# 检查createSubscriptionView方法是否添加了@MainActor
if grep -B 1 "func createSubscriptionView" Lomo/DependencyInjection/SubscriptionDependencyContainer.swift | grep -q "@MainActor"; then
    echo "✅ createSubscriptionView方法已添加@MainActor"
else
    echo "❌ createSubscriptionView方法缺少@MainActor"
    exit 1
fi

# 检查静态便捷方法是否添加了@MainActor
if grep -B 1 "static func subscriptionViewModel" Lomo/DependencyInjection/SubscriptionDependencyContainer.swift | grep -q "@MainActor"; then
    echo "✅ 静态subscriptionViewModel方法已添加@MainActor"
else
    echo "❌ 静态subscriptionViewModel方法缺少@MainActor"
    exit 1
fi

if grep -B 1 "static func subscriptionView" Lomo/DependencyInjection/SubscriptionDependencyContainer.swift | grep -q "@MainActor"; then
    echo "✅ 静态subscriptionView方法已添加@MainActor"
else
    echo "❌ 静态subscriptionView方法缺少@MainActor"
    exit 1
fi

# 检查SettingsViewModel中的协议方法调用修复
echo ""
echo "🔌 检查协议方法调用修复..."

# 检查是否还有checkSubscriptionStatus的调用
if grep -q "checkSubscriptionStatus" Lomo/ViewModels/Settings/SettingsViewModel.swift; then
    echo "❌ SettingsViewModel中仍存在checkSubscriptionStatus调用"
    grep -n "checkSubscriptionStatus" Lomo/ViewModels/Settings/SettingsViewModel.swift
    exit 1
else
    echo "✅ SettingsViewModel中已移除checkSubscriptionStatus调用"
fi

# 检查是否使用了正确的属性访问
if grep -q "subscriptionService\.isProUser" Lomo/ViewModels/Settings/SettingsViewModel.swift; then
    echo "✅ SettingsViewModel中使用了正确的isProUser属性访问"
else
    echo "❌ SettingsViewModel中未找到正确的isProUser属性访问"
    exit 1
fi

# 检查修复的方法数量
echo ""
echo "📊 修复统计..."

mainactor_count=$(grep -c "@MainActor" Lomo/DependencyInjection/SubscriptionDependencyContainer.swift)
echo "- SubscriptionDependencyContainer中@MainActor标记数量: $mainactor_count"

if [ "$mainactor_count" -ge 4 ]; then
    echo "✅ @MainActor标记数量正确"
else
    echo "❌ @MainActor标记数量不足"
    exit 1
fi

echo ""
echo "🎯 修复摘要："
echo "1. ✅ SubscriptionDependencyContainer的所有ViewModel创建方法已添加@MainActor"
echo "2. ✅ 静态便捷方法已添加@MainActor"
echo "3. ✅ SettingsViewModel中移除了不存在的协议方法调用"
echo "4. ✅ 使用现有的isProUser属性替代异步方法调用"
echo "5. ✅ 符合MainActor隔离要求"

echo ""
echo "🚀 MainActor隔离和协议方法修复验证完成！"