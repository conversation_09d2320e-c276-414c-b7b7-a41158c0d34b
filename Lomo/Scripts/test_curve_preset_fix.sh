#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🧪 曲线预设修复验证脚本
# 验证 CurveProcessor.CurvePreset 成员修复是否成功

echo "🧪 开始验证曲线预设修复..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查 CurvePreset 属性完整性
echo "1️⃣ 检查 CurvePreset 属性完整性..."

echo "   🔍 检查 displayName 属性..."
if grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
    echo "   ✅ CurvePreset 包含 displayName 属性"
else
    echo "   ❌ CurvePreset 缺少 displayName 属性"
fi

echo "   🔍 检查 id 属性..."
if grep -q "var id:" Lomo/Utils/CurvePresets.swift; then
    echo "   ✅ CurvePreset 包含 id 属性"
else
    echo "   ❌ CurvePreset 缺少 id 属性"
fi

echo ""

# 2. 检查 CurveServiceImpl 中的预设使用
echo "2️⃣ 检查 CurveServiceImpl 中的预设使用..."

echo "   🔍 检查 generatePresetPoints 方法中的预设..."
problematic_presets=()

# 检查是否还有不存在的预设使用
if grep -q "case \.contrast:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ❌ 仍使用不存在的 .contrast 预设"
    problematic_presets+=("contrast")
fi

if grep -q "case \.brightness:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ❌ 仍使用不存在的 .brightness 预设"
    problematic_presets+=("brightness")
fi

if grep -q "case \.vintage:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ❌ 仍使用不存在的 .vintage 预设"
    problematic_presets+=("vintage")
fi

# 检查是否正确使用了存在的预设
if grep -q "case \.contrastCurve:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ✅ 正确使用 .contrastCurve 预设"
fi

if grep -q "case \.brightCurve:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ✅ 正确使用 .brightCurve 预设"
fi

if grep -q "case \.vintageCurve:" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ✅ 正确使用 .vintageCurve 预设"
fi

if [ ${#problematic_presets[@]} -eq 0 ]; then
    echo "   🎉 所有预设使用正确"
else
    echo "   ⚠️ 仍有问题的预设: ${problematic_presets[*]}"
fi

echo ""

# 3. 检查特效参数类型定义唯一性
echo "3️⃣ 检查特效参数类型定义唯一性..."

lightleak_count=$(find Lomo -name "*.swift" -exec grep -l "struct LightLeakParameters" {} \; 2>/dev/null | wc -l)
grain_count=$(find Lomo -name "*.swift" -exec grep -l "struct GrainParameters" {} \; 2>/dev/null | wc -l)
scratch_count=$(find Lomo -name "*.swift" -exec grep -l "struct ScratchParameters" {} \; 2>/dev/null | wc -l)

echo "   📊 LightLeakParameters 定义数量: $lightleak_count (期望: 1)"
echo "   📊 GrainParameters 定义数量: $grain_count (期望: 1)"
echo "   📊 ScratchParameters 定义数量: $scratch_count (期望: 1)"

if [ "$lightleak_count" -eq 1 ] && [ "$grain_count" -eq 1 ] && [ "$scratch_count" -eq 1 ]; then
    echo "   ✅ 所有特效参数类型定义唯一"
else
    echo "   ❌ 特效参数类型定义不唯一"
fi

echo ""

# 4. 语法验证
echo "4️⃣ 语法验证..."

critical_files=(
    "Lomo/Utils/CurvePresets.swift"
    "Lomo/Services/Implementations/CurveServiceImpl.swift"
    "Lomo/ViewModels/Edit/EffectsViewModel.swift"
    "Lomo/Models/LightLeakModel.swift"
    "Lomo/Models/GrainModel.swift"
    "Lomo/Models/ScratchModel.swift"
)

syntax_errors=0
for file in "${critical_files[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            ((syntax_errors++))
            # 显示前几行错误信息
            echo "      错误详情:"
            swift -frontend -parse "$file" 2>&1 | head -3 | sed 's/^/      /'
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
    fi
done

echo ""

# 5. 编译测试（如果可能）
echo "5️⃣ 编译测试..."

echo "   🔨 尝试编译关键文件..."
if command -v swiftc >/dev/null 2>&1; then
    # 尝试编译 CurvePresets
    if swiftc -parse Lomo/Utils/CurvePresets.swift >/dev/null 2>&1; then
        echo "   ✅ CurvePresets.swift 编译通过"
    else
        echo "   ❌ CurvePresets.swift 编译失败"
    fi
    
    # 尝试编译 CurveServiceImpl
    if swiftc -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then
        echo "   ✅ CurveServiceImpl.swift 编译通过"
    else
        echo "   ❌ CurveServiceImpl.swift 编译失败"
    fi
else
    echo "   ℹ️ Swift 编译器不可用，跳过编译测试"
fi

echo ""

# 6. 检查原始错误解决情况
echo "6️⃣ 检查原始错误解决情况..."

echo "   🔍 检查 displayName 使用..."
displayname_usage=$(grep -c "\.displayName" Lomo/Services/Implementations/CurveServiceImpl.swift 2>/dev/null || echo "0")
if [ "$displayname_usage" -gt 0 ]; then
    echo "   📊 发现 $displayname_usage 处 .displayName 使用"
    if grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
        echo "   ✅ CurvePreset.displayName 使用问题已解决"
    else
        echo "   ❌ CurvePreset.displayName 使用问题未解决"
    fi
else
    echo "   ℹ️ 未发现 .displayName 使用"
fi

echo "   🔍 检查预设成员使用..."
if [ ${#problematic_presets[@]} -eq 0 ]; then
    echo "   ✅ 所有预设成员使用问题已解决"
else
    echo "   ❌ 仍有预设成员使用问题: ${problematic_presets[*]}"
fi

echo ""

# 7. 总结报告
echo "7️⃣ 修复验证总结..."

# 计算成功项目数
success_count=0
total_checks=6

# 检查各项是否成功
if grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then ((success_count++)); fi
if [ ${#problematic_presets[@]} -eq 0 ]; then ((success_count++)); fi
if [ "$lightleak_count" -eq 1 ] && [ "$grain_count" -eq 1 ] && [ "$scratch_count" -eq 1 ]; then ((success_count++)); fi
if [ "$syntax_errors" -eq 0 ]; then ((success_count++)); fi
if [ "$displayname_usage" -gt 0 ] && grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then ((success_count++)); fi
if command -v swiftc >/dev/null 2>&1 && swiftc -parse Lomo/Utils/CurvePresets.swift >/dev/null 2>&1; then ((success_count++)); fi

echo "   📊 验证结果: $success_count/$total_checks 项检查通过"

if [ "$success_count" -eq "$total_checks" ]; then
    echo "   🎉 曲线预设修复验证完全成功！"
    echo ""
    echo "✅ 修复验证成果："
    echo "   • CurvePreset 包含 displayName 属性"
    echo "   • 所有预设使用正确"
    echo "   • 特效参数类型定义唯一"
    echo "   • 语法检查通过"
    echo "   • 编译测试通过"
    echo ""
    echo "🎯 解决的原始错误："
    echo "   ✅ Value of type 'CurveProcessor.CurvePreset' has no member 'displayName' → 已解决"
    echo "   ✅ Type 'CurveProcessor.CurvePreset' has no member 'contrast' → 已解决"
    echo "   ✅ Type 'CurveProcessor.CurvePreset' has no member 'brightness' → 已解决"
    echo "   ✅ Type 'CurveProcessor.CurvePreset' has no member 'vintage' → 已解决"
    echo "   ✅ 特效参数重复声明问题 → 已解决"
    echo ""
    echo "🎯 当前系统状态："
    echo "   📁 CurvePreset: 包含 displayName 属性和完整预设"
    echo "   📁 特效参数: 每个类型只有1个定义"
else
    echo "   ⚠️ 部分检查未通过，需要进一步修复"
    echo "   📋 未通过的检查项目数: $((total_checks - success_count))"
    
    # 详细报告未通过的项目
    if ! grep -q "var displayName" Lomo/Utils/CurvePresets.swift; then
        echo "   • CurvePreset 缺少 displayName 属性"
    fi
    if [ ${#problematic_presets[@]} -gt 0 ]; then
        echo "   • 预设使用问题: ${problematic_presets[*]}"
    fi
    if [ "$syntax_errors" -gt 0 ]; then
        echo "   • 语法错误数量: $syntax_errors"
    fi
fi

echo ""
echo "🏁 曲线预设修复验证完成！"