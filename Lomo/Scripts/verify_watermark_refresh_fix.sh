#!/bin/bash

# 水印页面切换刷新修复验证脚本
echo "🔄 水印页面切换刷新修复验证"
echo "=========================="

# 检查EditView中的页面切换逻辑
echo ""
echo "🔍 检查EditView页面切换逻辑..."

EDIT_VIEW_FILE="Lomo/Views/Edit/EditView.swift"

if [ -f "$EDIT_VIEW_FILE" ]; then
    echo "✅ EditView.swift 文件存在"
    
    # 检查onTabSelected回调
    tab_selected_callback=$(grep -A 5 "onTabSelected:" "$EDIT_VIEW_FILE" | grep -c "从水印页面切换" 2>/dev/null || echo "0")
    if [ "$tab_selected_callback" -gt 0 ]; then
        echo "✅ 页面切换回调已添加 ($tab_selected_callback 处)"
    else
        echo "❌ 页面切换回调缺失"
    fi
    
    # 检查refreshPreviewView方法
    refresh_method=$(grep -c "refreshPreviewView" "$EDIT_VIEW_FILE" 2>/dev/null || echo "0")
    if [ "$refresh_method" -gt 0 ]; then
        echo "✅ refreshPreviewView方法已添加 ($refresh_method 处)"
    else
        echo "❌ refreshPreviewView方法缺失"
    fi
    
    # 检查水印页面检测逻辑
    watermark_detection=$(grep -c "selectedCategory == .watermark" "$EDIT_VIEW_FILE" 2>/dev/null || echo "0")
    if [ "$watermark_detection" -gt 0 ]; then
        echo "✅ 水印页面检测逻辑存在 ($watermark_detection 处)"
    else
        echo "❌ 水印页面检测逻辑缺失"
    fi
    
else
    echo "❌ EditView.swift 文件不存在"
fi

# 分析修复逻辑
echo ""
echo "🎯 修复逻辑分析"
echo "==============="

echo ""
echo "1. 🔍 问题识别:"
echo "   - 应用水印后，水印显示在预览容器上 ✅"
echo "   - 切换到其他页面时，视图没有刷新 ❌"
echo "   - 水印仍然显示在视图上 ❌"
echo "   - 只有调节参数时才会触发视图刷新，移除水印 ⚠️"

echo ""
echo "2. 🔧 解决方案:"
echo "   - 在NavigationTopBar的onTabSelected回调中检测页面切换"
echo "   - 如果从水印页面切换到其他页面，主动刷新视图"
echo "   - 照片模式: 调用filterStateManager.renderCurrentEffect()"
echo "   - 相机模式: 调用watermarkManager.removeCurrentWatermark()"

echo ""
echo "3. 🔄 刷新流程:"
echo "   - 用户在水印页面应用水印 → 水印显示"
echo "   - 用户切换到调节页面 → 触发onTabSelected回调"
echo "   - 检测到从.watermark切换到.adjust → 调用refreshPreviewView()"
echo "   - refreshPreviewView() → 刷新视图 → 水印移除"

echo ""
echo "4. 🎯 预期效果:"
echo "   - 水印页面: 水印正常显示 ✅"
echo "   - 切换页面: 水印立即消失 ✅"
echo "   - 调节功能: 正常工作，无水印干扰 ✅"
echo "   - 滤镜功能: 正常工作，无水印干扰 ✅"

# 检查相关方法
echo ""
echo "🔍 检查相关方法"
echo "==============="

# 检查FilterStateManager的renderCurrentEffect方法
FILTER_STATE_FILE="Lomo/Managers/Edit/FilterStateManager.swift"
if [ -f "$FILTER_STATE_FILE" ]; then
    render_method=$(grep -c "renderCurrentEffect" "$FILTER_STATE_FILE" 2>/dev/null || echo "0")
    if [ "$render_method" -gt 0 ]; then
        echo "✅ FilterStateManager.renderCurrentEffect() 方法存在"
    else
        echo "❌ FilterStateManager.renderCurrentEffect() 方法缺失"
    fi
else
    echo "⚠️ FilterStateManager.swift 文件不存在"
fi

# 检查WatermarkManager的removeCurrentWatermark方法
WATERMARK_MANAGER_FILES=(
    "Lomo/Managers/Edit/WatermarkManager.swift"
    "Lomo/Managers/Edit/WatermarkManagerProvider.swift"
)

for file in "${WATERMARK_MANAGER_FILES[@]}"; do
    if [ -f "$file" ]; then
        remove_method=$(grep -c "removeCurrentWatermark" "$file" 2>/dev/null || echo "0")
        if [ "$remove_method" -gt 0 ]; then
            echo "✅ $(basename "$file") - removeCurrentWatermark() 方法存在"
        else
            echo "⚠️ $(basename "$file") - removeCurrentWatermark() 方法缺失"
        fi
    else
        echo "⚠️ $(basename "$file") 文件不存在"
    fi
done

# 测试建议
echo ""
echo "🧪 测试建议"
echo "==========="

echo ""
echo "1. 📸 测试照片模式:"
echo "   - 导入一张照片"
echo "   - 切换到水印页面，应用任意水印样式"
echo "   - 切换到调节页面 → 观察水印是否立即消失"
echo "   - 切换到滤镜页面 → 观察水印是否立即消失"
echo "   - 调整参数或应用滤镜 → 功能应该正常，无水印干扰"

echo ""
echo "2. 📹 测试相机模式:"
echo "   - 打开相机预览"
echo "   - 切换到水印页面，应用任意水印样式"
echo "   - 切换到调节页面 → 观察水印是否立即消失"
echo "   - 切换到滤镜页面 → 观察水印是否立即消失"

echo ""
echo "3. 🔍 观察关键日志:"
echo "   - 🔄 [EditView] 从水印页面切换到XXX，刷新视图移除水印"
echo "   - 🔄 [EditView] 刷新MetalFilterView以移除水印 (照片模式)"
echo "   - 🔄 [EditView] 刷新MockPreviewView以移除水印 (相机模式)"

echo ""
echo "4. ✅ 预期结果:"
echo "   - 水印应用: 正常显示 ✅"
echo "   - 页面切换: 水印立即消失 ✅"
echo "   - 功能正常: 调节、滤镜功能不受影响 ✅"

echo ""
echo "🎉 如果修复成功，水印将只在水印页面显示，切换页面时自动移除！"
