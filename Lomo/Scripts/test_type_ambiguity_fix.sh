#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🧪 类型歧义修复验证脚本
# 验证 CurvePreset 和 RenderingMode 的类型歧义是否已修复

echo "🧪 开始验证类型歧义修复..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查重复定义清理情况
echo "1️⃣ 检查重复定义清理情况..."

echo "   🔍 检查 CurvePreset 重复定义..."
curve_preset_files=$(find Lomo/Services -name "*.swift" -exec grep -l "enum CurvePreset" {} \; 2>/dev/null)
if [ -z "$curve_preset_files" ]; then
    echo "   ✅ Services 目录中无 CurvePreset 重复定义"
else
    echo "   ❌ 仍有重复定义:"
    echo "$curve_preset_files"
fi

echo "   🔍 检查 RenderingMode 重复定义..."
rendering_mode_files=$(find Lomo/Services -name "*.swift" -exec grep -l "enum RenderingMode" {} \; 2>/dev/null)
if [ -z "$rendering_mode_files" ]; then
    echo "   ✅ Services 目录中无 RenderingMode 重复定义"
else
    echo "   ❌ 仍有重复定义:"
    echo "$rendering_mode_files"
fi

echo ""

# 2. 检查类型引用正确性
echo "2️⃣ 检查类型引用正确性..."

echo "   🔍 检查 CurvePreset 引用..."
# 检查协议中的引用
if grep -q "CurveProcessor.CurvePreset" Lomo/Services/Protocols/CurveServiceProtocol.swift; then
    echo "   ✅ 协议中正确使用 CurveProcessor.CurvePreset"
else
    echo "   ❌ 协议中未找到正确的 CurveProcessor.CurvePreset 引用"
fi

# 检查实现中的引用
if grep -q "CurveProcessor.CurvePreset" Lomo/Services/Implementations/CurveServiceImpl.swift; then
    echo "   ✅ 实现中正确使用 CurveProcessor.CurvePreset"
else
    echo "   ❌ 实现中未找到正确的 CurveProcessor.CurvePreset 引用"
fi

echo "   🔍 检查 RenderingMode 引用..."
# 检查是否有正确的 RenderingMode 引用
if grep -q "RenderingMode" Lomo/Services/Protocols/RenderingServiceProtocol.swift; then
    echo "   ✅ 协议中正确使用 RenderingMode"
else
    echo "   ❌ 协议中未找到 RenderingMode 引用"
fi

echo ""

# 3. 检查原始定义文件完整性
echo "3️⃣ 检查原始定义文件完整性..."

echo "   🔍 检查 CurvePresets.swift..."
if [ -f "Lomo/Utils/CurvePresets.swift" ] && grep -q "enum CurvePreset" Lomo/Utils/CurvePresets.swift; then
    echo "   ✅ CurvePresets.swift 中的原始定义完整"
else
    echo "   ❌ CurvePresets.swift 中的定义有问题"
fi

echo "   🔍 检查 RenderingMode.swift..."
if [ -f "Lomo/Models/Edit/RenderingMode.swift" ] && grep -q "enum RenderingMode" Lomo/Models/Edit/RenderingMode.swift; then
    echo "   ✅ RenderingMode.swift 中的原始定义完整"
else
    echo "   ❌ RenderingMode.swift 中的定义有问题"
fi

echo ""

# 4. 语法检查
echo "4️⃣ 语法检查..."

echo "   🔍 检查协议文件语法..."
if swift -frontend -parse Lomo/Services/Protocols/CurveServiceProtocol.swift >/dev/null 2>&1; then
    echo "   ✅ CurveServiceProtocol.swift 语法正确"
else
    echo "   ❌ CurveServiceProtocol.swift 语法错误"
fi

if swift -frontend -parse Lomo/Services/Protocols/RenderingServiceProtocol.swift >/dev/null 2>&1; then
    echo "   ✅ RenderingServiceProtocol.swift 语法正确"
else
    echo "   ❌ RenderingServiceProtocol.swift 语法错误"
fi

echo "   🔍 检查实现文件语法..."
if swift -frontend -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then
    echo "   ✅ CurveServiceImpl.swift 语法正确"
else
    echo "   ❌ CurveServiceImpl.swift 语法错误"
fi

if swift -frontend -parse Lomo/Services/Implementations/RenderingServiceImpl.swift >/dev/null 2>&1; then
    echo "   ✅ RenderingServiceImpl.swift 语法正确"
else
    echo "   ❌ RenderingServiceImpl.swift 语法错误"
fi

echo ""

# 5. 类型系统验证
echo "5️⃣ 类型系统验证..."

echo "   🔍 验证类型唯一性..."
# 统计项目中的类型定义
curve_preset_count=$(find Lomo -name "*.swift" -exec grep -l "enum CurvePreset" {} \; 2>/dev/null | wc -l)
rendering_mode_count=$(find Lomo -name "*.swift" -exec grep -l "enum RenderingMode" {} \; 2>/dev/null | wc -l)

echo "   📊 CurvePreset 定义数量: $curve_preset_count (期望: 1)"
echo "   📊 RenderingMode 定义数量: $rendering_mode_count (期望: 1)"

if [ "$curve_preset_count" -eq 1 ] && [ "$rendering_mode_count" -eq 1 ]; then
    echo "   ✅ 类型定义唯一性验证通过"
else
    echo "   ❌ 类型定义唯一性验证失败"
fi

echo ""

# 6. 总结
echo "6️⃣ 修复验证总结..."

# 计算成功项目数
success_count=0
total_checks=6

# 检查各项是否成功
if [ -z "$curve_preset_files" ]; then ((success_count++)); fi
if [ -z "$rendering_mode_files" ]; then ((success_count++)); fi
if grep -q "CurveProcessor.CurvePreset" Lomo/Services/Protocols/CurveServiceProtocol.swift; then ((success_count++)); fi
if grep -q "CurveProcessor.CurvePreset" Lomo/Services/Implementations/CurveServiceImpl.swift; then ((success_count++)); fi
if [ "$curve_preset_count" -eq 1 ] && [ "$rendering_mode_count" -eq 1 ]; then ((success_count++)); fi
if swift -frontend -parse Lomo/Services/Protocols/CurveServiceProtocol.swift >/dev/null 2>&1 && swift -frontend -parse Lomo/Services/Protocols/RenderingServiceProtocol.swift >/dev/null 2>&1; then ((success_count++)); fi

echo "   📊 验证结果: $success_count/$total_checks 项检查通过"

if [ "$success_count" -eq "$total_checks" ]; then
    echo "   🎉 类型歧义修复验证完全成功！"
    echo ""
    echo "✅ 修复成果："
    echo "   • CurvePreset 重复定义已清理"
    echo "   • RenderingMode 重复定义已清理"
    echo "   • 所有类型引用都指向正确的定义"
    echo "   • 类型系统清晰明确，无歧义"
    echo ""
    echo "🎯 当前类型系统结构："
    echo "   📁 CurvePreset: CurveProcessor.CurvePreset (Utils/CurvePresets.swift)"
    echo "   📁 RenderingMode: RenderingMode (Models/Edit/RenderingMode.swift)"
else
    echo "   ⚠️ 部分检查未通过，需要进一步修复"
fi

echo ""
echo "🏁 类型歧义修复验证完成！"