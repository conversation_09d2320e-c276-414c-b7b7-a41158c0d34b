#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🔧 RenderingServiceImpl 语法修复脚本
# 修复可能的语法问题

echo "🔧 开始修复 RenderingServiceImpl 语法问题..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查并修复可能的语法问题
echo "1️⃣ 检查 RenderingServiceImpl.swift 语法..."

# 备份原文件
cp Lomo/Services/Implementations/RenderingServiceImpl.swift Lomo/Services/Implementations/RenderingServiceImpl.swift.backup

# 检查是否有异步调用的语法问题
echo "   🔍 检查异步调用语法..."
if grep -q "if !await hasActiveEffects()" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    echo "   ✅ 异步调用语法正确"
else
    echo "   ❌ 异步调用语法有问题"
fi

# 检查方法定义
echo "   🔍 检查方法定义..."
if grep -q "func hasActiveEffects() async -> Bool" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    echo "   ✅ hasActiveEffects 方法定义正确"
else
    echo "   ❌ hasActiveEffects 方法定义有问题"
fi

# 检查大括号匹配
echo "   🔍 检查大括号匹配..."
open_braces=$(grep -o '{' Lomo/Services/Implementations/RenderingServiceImpl.swift | wc -l)
close_braces=$(grep -o '}' Lomo/Services/Implementations/RenderingServiceImpl.swift | wc -l)

echo "   📊 开括号数量: $open_braces"
echo "   📊 闭括号数量: $close_braces"

if [ "$open_braces" -eq "$close_braces" ]; then
    echo "   ✅ 大括号匹配正确"
else
    echo "   ❌ 大括号不匹配"
fi

echo ""

# 2. 尝试简单的语法修复
echo "2️⃣ 尝试语法修复..."

# 确保异步调用语法正确
echo "   🔧 确保异步调用语法正确..."
sed -i '' 's/if !await hasActiveEffects() {/if !(await hasActiveEffects()) {/g' Lomo/Services/Implementations/RenderingServiceImpl.swift

# 检查修复结果
if grep -q "if !(await hasActiveEffects())" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    echo "   ✅ 异步调用语法已修复"
else
    echo "   ℹ️ 异步调用语法无需修复"
fi

echo ""

# 3. 验证修复结果
echo "3️⃣ 验证修复结果..."

echo "   🔨 检查语法..."
if swift -frontend -parse Lomo/Services/Implementations/RenderingServiceImpl.swift >/dev/null 2>&1; then
    echo "   ✅ RenderingServiceImpl.swift 语法正确"
    # 删除备份文件
    rm -f Lomo/Services/Implementations/RenderingServiceImpl.swift.backup
    echo "   🗑️ 已删除备份文件"
else
    echo "   ❌ RenderingServiceImpl.swift 仍有语法错误"
    echo "   🔄 恢复备份文件..."
    mv Lomo/Services/Implementations/RenderingServiceImpl.swift.backup Lomo/Services/Implementations/RenderingServiceImpl.swift
    echo "   📋 详细错误信息:"
    swift -frontend -parse Lomo/Services/Implementations/RenderingServiceImpl.swift 2>&1 | head -5
fi

echo ""
echo "🏁 RenderingServiceImpl 语法修复完成！"