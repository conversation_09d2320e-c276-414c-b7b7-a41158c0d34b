#!/bin/bash

# 完全Metal架构验证脚本
echo "🎯 验证100% Metal架构实现..."

# 检查核心Metal着色器文件
echo "🔍 检查Metal着色器文件..."

FILTER_SHADERS="Lomo/Shaders/FilterShaders.metal"
LUT_SHADERS="Lomo/Shaders/LUTShaders.metal"
SPECIAL_EFFECTS_SHADERS="Lomo/Shaders/SpecialEffectsShaders.metal"

if [ -f "$FILTER_SHADERS" ]; then
    echo "✅ 滤镜着色器存在: $FILTER_SHADERS"
else
    echo "❌ 滤镜着色器缺失: $FILTER_SHADERS"
fi

if [ -f "$LUT_SHADERS" ]; then
    echo "✅ LUT着色器存在: $LUT_SHADERS"
else
    echo "❌ LUT着色器缺失: $LUT_SHADERS"
fi

if [ -f "$SPECIAL_EFFECTS_SHADERS" ]; then
    echo "✅ 特殊效果着色器存在: $SPECIAL_EFFECTS_SHADERS"
else
    echo "❌ 特殊效果着色器缺失: $SPECIAL_EFFECTS_SHADERS"
fi

# 检查Metal处理器
echo ""
echo "🔍 检查Metal处理器..."

METAL_FILTER_RENDERER="Lomo/Managers/Edit/MetalFilterRenderer.swift"
METAL_LUT_PROCESSOR="Lomo/Managers/Edit/MetalLUTProcessor.swift"
METAL_SPECIAL_EFFECTS="Lomo/Managers/Edit/MetalSpecialEffectsEngine.swift"

if [ -f "$METAL_FILTER_RENDERER" ]; then
    echo "✅ Metal滤镜渲染器存在: $METAL_FILTER_RENDERER"
else
    echo "❌ Metal滤镜渲染器缺失: $METAL_FILTER_RENDERER"
fi

if [ -f "$METAL_LUT_PROCESSOR" ]; then
    echo "✅ Metal LUT处理器存在: $METAL_LUT_PROCESSOR"
else
    echo "❌ Metal LUT处理器缺失: $METAL_LUT_PROCESSOR"
fi

if [ -f "$METAL_SPECIAL_EFFECTS" ]; then
    echo "✅ Metal特殊效果引擎存在: $METAL_SPECIAL_EFFECTS"
else
    echo "❌ Metal特殊效果引擎缺失: $METAL_SPECIAL_EFFECTS"
fi

# 检查服务更新
echo ""
echo "🔍 检查服务Metal化..."

LIGHT_LEAK_SERVICE="Lomo/Services/LightLeakService.swift"
GRAIN_SERVICE="Lomo/Services/GrainService.swift"
SCRATCH_SERVICE="Lomo/Services/ScratchService.swift"

if [ -f "$LIGHT_LEAK_SERVICE" ]; then
    if grep -q "MetalSpecialEffectsEngine" "$LIGHT_LEAK_SERVICE"; then
        echo "✅ 漏光服务已Metal化: $LIGHT_LEAK_SERVICE"
    else
        echo "❌ 漏光服务未Metal化: $LIGHT_LEAK_SERVICE"
    fi
else
    echo "❌ 漏光服务文件缺失: $LIGHT_LEAK_SERVICE"
fi

if [ -f "$GRAIN_SERVICE" ]; then
    if grep -q "MetalSpecialEffectsEngine" "$GRAIN_SERVICE"; then
        echo "✅ 颗粒服务已Metal化: $GRAIN_SERVICE"
    else
        echo "❌ 颗粒服务未Metal化: $GRAIN_SERVICE"
    fi
else
    echo "❌ 颗粒服务文件缺失: $GRAIN_SERVICE"
fi

if [ -f "$SCRATCH_SERVICE" ]; then
    if grep -q "MetalSpecialEffectsEngine" "$SCRATCH_SERVICE"; then
        echo "✅ 划痕服务已Metal化: $SCRATCH_SERVICE"
    else
        echo "❌ 划痕服务未Metal化: $SCRATCH_SERVICE"
    fi
else
    echo "❌ 划痕服务文件缺失: $SCRATCH_SERVICE"
fi

# 检查Core Image残留
echo ""
echo "🔍 检查Core Image残留..."

CORE_IMAGE_COUNT=0

# 检查特殊效果服务文件中的Core Image使用
for service_file in "$LIGHT_LEAK_SERVICE" "$GRAIN_SERVICE" "$SCRATCH_SERVICE"; do
    if [ -f "$service_file" ]; then
        ci_usage=$(grep -c "CIFilter\|CIImage\|CIContext" "$service_file" 2>/dev/null || echo "0")
        if [ "$ci_usage" -gt 0 ]; then
            echo "⚠️ $service_file 仍有 $ci_usage 处Core Image使用"
            CORE_IMAGE_COUNT=$((CORE_IMAGE_COUNT + ci_usage))
        fi
    fi
done

# 检查其他关键文件
WATERMARK_STYLES="Lomo/Managers/Edit/WatermarkStyles.swift"
IMAGE_RENDERING_SERVICE="Lomo/Services/Implementations/ImageRenderingService.swift"

if [ -f "$WATERMARK_STYLES" ]; then
    ci_usage=$(grep -c "CIGaussianBlur\|CIFilter\|CIImage\|CIContext" "$WATERMARK_STYLES" 2>/dev/null || echo "0")
    if [ "$ci_usage" -gt 0 ]; then
        echo "⚠️ $WATERMARK_STYLES 仍有 $ci_usage 处Core Image使用"
        CORE_IMAGE_COUNT=$((CORE_IMAGE_COUNT + ci_usage))
    else
        echo "✅ WatermarkStyles已Metal化"
    fi
fi

if [ -f "$IMAGE_RENDERING_SERVICE" ]; then
    ci_usage=$(grep -c "CIDissolveTransition\|CIFilter\|CIImage\|CIContext" "$IMAGE_RENDERING_SERVICE" 2>/dev/null || echo "0")
    if [ "$ci_usage" -gt 0 ]; then
        echo "⚠️ $IMAGE_RENDERING_SERVICE 仍有 $ci_usage 处Core Image使用"
        CORE_IMAGE_COUNT=$((CORE_IMAGE_COUNT + ci_usage))
    else
        echo "✅ ImageRenderingService已Metal化"
    fi
fi

if [ "$CORE_IMAGE_COUNT" -eq 0 ]; then
    echo "✅ 特殊效果服务已完全Metal化"
else
    echo "⚠️ 发现 $CORE_IMAGE_COUNT 处Core Image残留"
fi

# 检查测试文件
echo ""
echo "🔍 检查测试文件..."

METAL_LUT_TEST="Lomo/Tests/MetalLUTTest.swift"
METAL_SPECIAL_EFFECTS_TEST="Lomo/Tests/MetalSpecialEffectsTest.swift"

if [ -f "$METAL_LUT_TEST" ]; then
    echo "✅ Metal LUT测试存在: $METAL_LUT_TEST"
else
    echo "❌ Metal LUT测试缺失: $METAL_LUT_TEST"
fi

if [ -f "$METAL_SPECIAL_EFFECTS_TEST" ]; then
    echo "✅ Metal特殊效果测试存在: $METAL_SPECIAL_EFFECTS_TEST"
else
    echo "❌ Metal特殊效果测试缺失: $METAL_SPECIAL_EFFECTS_TEST"
fi

# 检查着色器函数
echo ""
echo "🔍 检查着色器函数..."

if [ -f "$SPECIAL_EFFECTS_SHADERS" ]; then
    echo "检查特殊效果着色器函数:"
    
    functions=("apply_light_leak_effect" "apply_grain_effect" "apply_scratch_effect" "apply_gaussian_blur" "apply_dissolve_transition")
    
    for func in "${functions[@]}"; do
        if grep -q "$func" "$SPECIAL_EFFECTS_SHADERS"; then
            echo "  ✅ $func"
        else
            echo "  ❌ $func"
        fi
    done
fi

# 统计总结
echo ""
echo "📊 架构统计..."

TOTAL_METAL_FILES=0
TOTAL_SHADER_FILES=0

# 统计Metal处理器文件
for file in "$METAL_FILTER_RENDERER" "$METAL_LUT_PROCESSOR" "$METAL_SPECIAL_EFFECTS"; do
    if [ -f "$file" ]; then
        TOTAL_METAL_FILES=$((TOTAL_METAL_FILES + 1))
    fi
done

# 统计着色器文件
for file in "$FILTER_SHADERS" "$LUT_SHADERS" "$SPECIAL_EFFECTS_SHADERS"; do
    if [ -f "$file" ]; then
        TOTAL_SHADER_FILES=$((TOTAL_SHADER_FILES + 1))
    fi
done

echo "Metal处理器文件: $TOTAL_METAL_FILES/3"
echo "Metal着色器文件: $TOTAL_SHADER_FILES/3"

# 最终评估
echo ""
echo "🎯 最终评估..."

if [ "$TOTAL_METAL_FILES" -eq 3 ] && [ "$TOTAL_SHADER_FILES" -eq 3 ] && [ "$CORE_IMAGE_COUNT" -eq 0 ]; then
    echo "🎉 恭喜！100% Metal架构实现完成！"
    echo "✅ 所有组件都已Metal化"
    echo "✅ 零Core Image依赖"
    echo "✅ 完整的测试覆盖"
    echo ""
    echo "🚀 你现在拥有了专业级的Metal渲染架构！"
else
    echo "⚠️ Metal架构实现未完全完成"
    echo "请检查上述缺失的组件"
fi

echo ""
echo "🔧 下一步: 在Xcode中编译项目以验证Metal功能"
