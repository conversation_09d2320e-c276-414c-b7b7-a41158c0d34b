#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🧪 曲线预设 Switch 穷尽性验证脚本
# 验证 CurveServiceImpl 中的 switch 语句是否穷尽

echo "🧪 开始验证曲线预设 Switch 穷尽性..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查 switch 语句结构
echo "1️⃣ 检查 switch 语句结构..."

echo "   🔍 检查 generatePresetPoints 方法中的 switch..."
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "default:"; then
    echo "   ✅ switch 语句包含 default 分支"
else
    echo "   ❌ switch 语句缺少 default 分支"
fi

# 统计处理的预设数量
handled_presets=$(grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -c "case \.")
echo "   📊 显式处理的预设数量: $handled_presets"

echo ""

# 2. 检查 CurvePreset 枚举成员总数
echo "2️⃣ 检查 CurvePreset 枚举成员总数..."

total_presets=$(grep -c "case [a-zA-Z]* =" Lomo/Utils/CurvePresets.swift)
echo "   📊 CurvePreset 枚举总成员数: $total_presets"

echo ""

# 3. 检查具体处理的预设
echo "3️⃣ 检查具体处理的预设..."

key_presets=("linear" "contrastCurve" "brightCurve" "vintageCurve" "sCurve" "darkCurve" "softCurve" "dramaticCurve")
handled_count=0

for preset in "${key_presets[@]}"; do
    if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "case \.$preset:"; then
        echo "   ✅ 处理了 $preset 预设"
        ((handled_count++))
    else
        echo "   ❌ 未处理 $preset 预设"
    fi
done

echo "   📊 关键预设处理数量: $handled_count/${#key_presets[@]}"

echo ""

# 4. 检查 default 分支实现
echo "4️⃣ 检查 default 分支实现..."

echo "   🔍 检查 default 分支内容..."
if grep -A 10 "default:" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "preset.points"; then
    echo "   ✅ default 分支使用了 preset.points"
else
    echo "   ❌ default 分支未使用 preset.points"
fi

if grep -A 10 "default:" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "线性曲线"; then
    echo "   ✅ default 分支有线性曲线回退"
else
    echo "   ❌ default 分支缺少线性曲线回退"
fi

echo ""

# 5. 语法验证
echo "5️⃣ 语法验证..."

echo "   🔨 检查 CurveServiceImpl 语法..."
if swift -frontend -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then
    echo "   ✅ CurveServiceImpl.swift 语法正确"
else
    echo "   ❌ CurveServiceImpl.swift 语法错误"
    echo "      错误详情:"
    swift -frontend -parse Lomo/Services/Implementations/CurveServiceImpl.swift 2>&1 | head -5 | sed 's/^/      /'
fi

echo ""

# 6. 编译测试
echo "6️⃣ 编译测试..."

echo "   🔨 尝试编译 CurveServiceImpl..."
if command -v swiftc >/dev/null 2>&1; then
    if swiftc -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then
        echo "   ✅ CurveServiceImpl.swift 编译通过"
    else
        echo "   ❌ CurveServiceImpl.swift 编译失败"
        echo "      编译错误:"
        swiftc -parse Lomo/Services/Implementations/CurveServiceImpl.swift 2>&1 | head -3 | sed 's/^/      /'
    fi
else
    echo "   ℹ️ Swift 编译器不可用，跳过编译测试"
fi

echo ""

# 7. 检查原始错误解决情况
echo "7️⃣ 检查原始错误解决情况..."

echo "   🔍 检查 Switch 穷尽性错误..."
# 模拟检查 switch 穷尽性
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "default:"; then
    echo "   ✅ Switch must be exhaustive 错误已解决"
else
    echo "   ❌ Switch must be exhaustive 错误未解决"
fi

echo ""

# 8. 功能完整性检查
echo "8️⃣ 功能完整性检查..."

echo "   🔍 检查预设点生成逻辑..."
# 检查是否有合理的点生成逻辑
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "CGPoint"; then
    echo "   ✅ 预设点生成逻辑存在"
else
    echo "   ❌ 预设点生成逻辑缺失"
fi

# 检查是否有错误处理
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "isEmpty"; then
    echo "   ✅ 包含空值检查"
else
    echo "   ❌ 缺少空值检查"
fi

echo ""

# 9. 总结报告
echo "9️⃣ 修复验证总结..."

# 计算成功项目数
success_count=0
total_checks=7

# 检查各项是否成功
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "default:"; then ((success_count++)); fi
if [ "$handled_count" -ge 6 ]; then ((success_count++)); fi
if grep -A 10 "default:" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "preset.points"; then ((success_count++)); fi
if swift -frontend -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then ((success_count++)); fi
if command -v swiftc >/dev/null 2>&1 && swiftc -parse Lomo/Services/Implementations/CurveServiceImpl.swift >/dev/null 2>&1; then ((success_count++)); fi
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "CGPoint"; then ((success_count++)); fi
if grep -A 50 "private func generatePresetPoints" Lomo/Services/Implementations/CurveServiceImpl.swift | grep -q "isEmpty"; then ((success_count++)); fi

echo "   📊 验证结果: $success_count/$total_checks 项检查通过"

if [ "$success_count" -eq "$total_checks" ]; then
    echo "   🎉 Switch 穷尽性修复验证完全成功！"
    echo ""
    echo "✅ 修复验证成果："
    echo "   • switch 语句包含 default 分支"
    echo "   • 显式处理了 $handled_count 个关键预设"
    echo "   • default 分支有完整的回退逻辑"
    echo "   • 语法检查通过"
    echo "   • 编译测试通过"
    echo "   • 功能逻辑完整"
    echo ""
    echo "🎯 解决的原始错误："
    echo "   ✅ Switch must be exhaustive → 已解决"
    echo ""
    echo "🎯 当前 switch 结构："
    echo "   📁 显式处理: $handled_count 个关键预设"
    echo "   📁 default 分支: 处理其余 $((total_presets - handled_count)) 个预设"
    echo "   📁 回退机制: 使用 preset.points 或线性曲线"
else
    echo "   ⚠️ 部分检查未通过，需要进一步修复"
    echo "   📋 未通过的检查项目数: $((total_checks - success_count))"
fi

echo ""
echo "🏁 Switch 穷尽性修复验证完成！"