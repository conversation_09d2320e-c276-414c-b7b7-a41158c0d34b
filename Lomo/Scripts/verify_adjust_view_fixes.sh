#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🔍 验证AdjustView编译错误修复..."

# 检查AdjustView中的类型使用
echo "1️⃣ 检查AdjustView中的ViewModel类型使用..."
grep -n "AdjustViewModel[^R]" Lomo/Views/Edit/AdjustView.swift || echo "✅ 没有发现旧的AdjustViewModel类型"

# 检查HSL参数的使用
echo "2️⃣ 检查HSL参数的使用..."
grep -n "getCurrentHSLParameters" Lomo/Views/Edit/AdjustView.swift || echo "✅ 没有发现异步方法调用"

# 检查currentHSLParameters的使用
echo "3️⃣ 检查currentHSLParameters属性的使用..."
grep -n "currentHSLParameters" Lomo/Views/Edit/AdjustView.swift

# 检查AdjustViewModelRefactored的使用
echo "4️⃣ 检查AdjustViewModelRefactored的使用..."
grep -n "AdjustViewModelRefactored" Lomo/Views/Edit/AdjustView.swift

echo "✅ AdjustView修复验证完成"