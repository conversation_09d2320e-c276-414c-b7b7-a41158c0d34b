#!/bin/bash

# 属性初始化器修复验证脚本
echo "🧪 属性初始化器修复验证测试"
echo "================================"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查watermarkOptions是否改为计算属性
echo ""
echo "🔍 检查watermarkOptions修复..."

CONTROL_VIEW_FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"

# 检查是否改为计算属性
if grep -q "private var watermarkOptions.*{" "$CONTROL_VIEW_FILE"; then
    echo "✅ watermarkOptions：已改为计算属性"
else
    echo "❌ watermarkOptions：仍然是存储属性"
    exit 1
fi

# 检查是否正确闭合
if grep -q "} // 闭合计算属性" "$CONTROL_VIEW_FILE"; then
    echo "✅ watermarkOptions：计算属性正确闭合"
else
    echo "❌ watermarkOptions：计算属性闭合有问题"
    exit 1
fi

# 检查watermarkService调用是否仍然存在
SERVICE_CALLS_IN_OPTIONS=$(grep -A 200 "private var watermarkOptions" "$CONTROL_VIEW_FILE" | grep -c "watermarkService\.")
if [ "$SERVICE_CALLS_IN_OPTIONS" -ge 2 ]; then
    echo "✅ watermarkOptions：Service调用正常（$SERVICE_CALLS_IN_OPTIONS 个）"
else
    echo "❌ watermarkOptions：Service调用异常（$SERVICE_CALLS_IN_OPTIONS 个）"
    exit 1
fi

# 检查总的Service调用数量是否保持正常
TOTAL_SERVICE_CALLS=$(grep -c "watermarkService\." "$CONTROL_VIEW_FILE")
if [ "$TOTAL_SERVICE_CALLS" -gt 140 ]; then
    echo "✅ WatermarkControlView：总Service调用正常（$TOTAL_SERVICE_CALLS 个）"
else
    echo "❌ WatermarkControlView：总Service调用异常（$TOTAL_SERVICE_CALLS 个）"
    exit 1
fi

# 检查是否还有属性初始化器问题
echo ""
echo "🔍 检查潜在的属性初始化器问题..."

# 搜索可能的问题模式
POTENTIAL_ISSUES=$(grep -n "watermarkService\." "$CONTROL_VIEW_FILE" | grep -E "(private let|private var).*=" | wc -l)
if [ "$POTENTIAL_ISSUES" -eq 0 ]; then
    echo "✅ 没有发现潜在的属性初始化器问题"
else
    echo "⚠️ 发现 $POTENTIAL_ISSUES 个潜在的属性初始化器问题"
    grep -n "watermarkService\." "$CONTROL_VIEW_FILE" | grep -E "(private let|private var).*="
fi

# 检查文件结构完整性
echo ""
echo "🔍 检查文件结构完整性..."

FILE_LINES=$(wc -l < "$CONTROL_VIEW_FILE")
if [ "$FILE_LINES" -gt 3400 ] && [ "$FILE_LINES" -lt 3500 ]; then
    echo "✅ 文件大小正常：$FILE_LINES 行"
else
    echo "⚠️ 文件大小异常：$FILE_LINES 行"
fi

# 最终验证
echo ""
echo "🎉 属性初始化器修复验证结果"
echo "================================"
echo "✅ 项目编译成功"
echo "✅ watermarkOptions改为计算属性"
echo "✅ 计算属性正确闭合"
echo "✅ Service调用正常（$TOTAL_SERVICE_CALLS 个）"
echo "✅ 没有属性初始化器问题"
echo ""
echo "🔧 修复内容："
echo "   - watermarkOptions：从存储属性改为计算属性"
echo "   - 解决了属性初始化器中访问实例成员的问题"
echo "   - 保持了所有Service调用的功能"
echo ""
echo "🎯 技术要点："
echo "   - 属性初始化器在self可用之前运行"
echo "   - 计算属性可以访问实例成员"
echo "   - 保持了代码的功能完整性"
echo ""
echo "📋 下一步："
echo "   - 真正重构已经完全成功"
echo "   - 可以继续第3步：移除Manager文件"
echo "   - 所有编译错误都已解决"
