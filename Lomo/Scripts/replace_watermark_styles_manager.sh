#!/bin/bash

# 批量替换WatermarkStyles中的Manager调用脚本
echo "🔄 批量替换WatermarkStyles中的Manager调用"
echo "================================"

# 获取所有需要替换的文件
FILES=($(find Lomo/Managers/Edit/WatermarkStyles -name "*.swift" -exec grep -l "WatermarkSettingsManager\.shared" {} \;))

echo "📊 发现 ${#FILES[@]} 个文件需要替换"

# 统计替换前的总调用数量
TOTAL_BEFORE=0
for file in "${FILES[@]}"; do
    count=$(grep -c "WatermarkSettingsManager\.shared" "$file")
    TOTAL_BEFORE=$((TOTAL_BEFORE + count))
    echo "📋 $file: $count 个调用"
done

echo "📊 替换前总调用数量: $TOTAL_BEFORE"
echo ""

# 创建备份目录
BACKUP_DIR="Lomo/Scripts/watermark_styles_backup"
mkdir -p "$BACKUP_DIR"

echo "🔄 开始批量替换..."

# 替换每个文件
for file in "${FILES[@]}"; do
    echo "🔄 处理文件: $file"
    
    # 创建备份
    backup_file="$BACKUP_DIR/$(basename "$file").backup"
    cp "$file" "$backup_file"
    
    # 替换 WatermarkSettingsManager.shared.getSettings() 调用
    sed -i '' 's/WatermarkSettingsManager\.shared\.getSettings()/WatermarkDependencyContainer.shared.watermarkService.getSettings()/g' "$file"
    
    # 替换 WatermarkSettingsManager.shared.updateSetting 调用
    sed -i '' 's/WatermarkSettingsManager\.shared\.updateSetting(/WatermarkDependencyContainer.shared.watermarkService.updateSetting(/g' "$file"
    
    # 替换 WatermarkSettingsManager.shared.saveSettings 调用
    sed -i '' 's/WatermarkSettingsManager\.shared\.saveSettings(/WatermarkDependencyContainer.shared.watermarkService.saveSettings(/g' "$file"
    
    echo "✅ 完成: $file"
done

echo ""
echo "📊 验证替换结果..."

# 统计替换后的总调用数量
TOTAL_AFTER=0
for file in "${FILES[@]}"; do
    count=$(grep -c "WatermarkSettingsManager\.shared" "$file" 2>/dev/null || echo "0")
    TOTAL_AFTER=$((TOTAL_AFTER + count))
done

REPLACED_COUNT=$((TOTAL_BEFORE - TOTAL_AFTER))

echo "📊 替换后剩余调用数量: $TOTAL_AFTER"
echo "🎯 成功替换: $REPLACED_COUNT 个调用"

if [ "$TOTAL_AFTER" -eq 0 ]; then
    echo "✅ 所有WatermarkStyles文件的Manager调用已成功替换！"
else
    echo "⚠️ 还有 $TOTAL_AFTER 个Manager调用未替换"
    echo "未替换的文件："
    for file in "${FILES[@]}"; do
        count=$(grep -c "WatermarkSettingsManager\.shared" "$file" 2>/dev/null || echo "0")
        if [ "$count" -gt 0 ]; then
            echo "  - $file: $count 个调用"
        fi
    done
fi

echo ""
echo "📋 备份文件保存在: $BACKUP_DIR"
echo "🔄 WatermarkStyles Manager调用替换完成！"
