#!/bin/bash

# TextInputOptionView参数修复验证脚本
echo "🧪 TextInputOptionView参数修复验证测试"
echo "================================"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查TextInputOptionView调用修复
echo ""
echo "🔍 检查TextInputOptionView调用修复..."

CONTROL_VIEW_FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"

# 检查是否有正确的参数结构
if grep -A 20 "TextInputOptionView(" "$CONTROL_VIEW_FILE" | grep -q "onUpdateSetting: { newValue in"; then
    echo "✅ TextInputOptionView：onUpdateSetting闭包正确"
else
    echo "❌ TextInputOptionView：onUpdateSetting闭包有问题"
    exit 1
fi

if grep -A 20 "TextInputOptionView(" "$CONTROL_VIEW_FILE" | grep -q "onUpdateEnabled: { newValue in"; then
    echo "✅ TextInputOptionView：onUpdateEnabled闭包正确"
else
    echo "❌ TextInputOptionView：onUpdateEnabled闭包有问题"
    exit 1
fi

if grep -A 20 "TextInputOptionView(" "$CONTROL_VIEW_FILE" | grep -q "onApplyStyle: {"; then
    echo "✅ TextInputOptionView：onApplyStyle闭包正确"
else
    echo "❌ TextInputOptionView：onApplyStyle闭包有问题"
    exit 1
fi

if grep -A 20 "TextInputOptionView(" "$CONTROL_VIEW_FILE" | grep -q "watermarkService: watermarkService"; then
    echo "✅ TextInputOptionView：watermarkService参数正确"
else
    echo "❌ TextInputOptionView：watermarkService参数有问题"
    exit 1
fi

# 检查是否没有错误的参数传递
WRONG_PARAMS=$(grep -c "onUpdateSetting: watermarkService" "$CONTROL_VIEW_FILE" 2>/dev/null || true)
if [ -z "$WRONG_PARAMS" ]; then
    WRONG_PARAMS=0
fi

if [ "$WRONG_PARAMS" -eq 0 ]; then
    echo "✅ TextInputOptionView：没有错误的参数传递"
else
    echo "❌ TextInputOptionView：仍有 $WRONG_PARAMS 个错误的参数传递"
    exit 1
fi

# 检查Service调用总数
SERVICE_CALLS=$(grep -c "watermarkService\." "$CONTROL_VIEW_FILE")
if [ "$SERVICE_CALLS" -gt 140 ]; then
    echo "✅ WatermarkControlView：Service调用总数正常（$SERVICE_CALLS 个）"
else
    echo "❌ WatermarkControlView：Service调用总数异常（$SERVICE_CALLS 个）"
    exit 1
fi

# 检查文件结构完整性
FILE_LINES=$(wc -l < "$CONTROL_VIEW_FILE")
if [ "$FILE_LINES" -gt 3400 ] && [ "$FILE_LINES" -lt 3600 ]; then
    echo "✅ 文件大小正常：$FILE_LINES 行"
else
    echo "⚠️ 文件大小异常：$FILE_LINES 行"
fi

# 最终验证
echo ""
echo "🎉 TextInputOptionView修复验证结果"
echo "================================"
echo "✅ 项目编译成功"
echo "✅ onUpdateSetting闭包正确"
echo "✅ onUpdateEnabled闭包正确"
echo "✅ onApplyStyle闭包正确"
echo "✅ watermarkService参数正确"
echo "✅ 没有错误的参数传递"
echo "✅ Service调用正常（$SERVICE_CALLS 个）"
echo ""
echo "🔧 修复内容："
echo "   - onUpdateSetting：从错误的service对象改为正确的闭包"
echo "   - onUpdateEnabled：修复闭包内容，更新enabled状态"
echo "   - onApplyStyle：修复闭包参数，移除不需要的参数"
echo "   - watermarkService：正确传递service对象"
echo ""
echo "🎯 技术要点："
echo "   - Swift闭包参数类型匹配"
echo "   - 依赖注入正确传递"
echo "   - 回调函数正确实现"
echo ""
echo "📋 下一步："
echo "   - 真正重构已经完全成功"
echo "   - 所有编译错误都已解决"
echo "   - 可以继续第3步：移除Manager文件"
