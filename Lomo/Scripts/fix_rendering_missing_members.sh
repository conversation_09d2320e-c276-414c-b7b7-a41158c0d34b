#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 🚨 渲染服务缺失成员修复脚本
# 修复 RenderingMode 缺少 'preview' 成员和 FilterParameters 缺少 'hasActiveAdjustments' 方法

echo "🚨 开始修复渲染服务缺失成员..."
echo "📍 项目路径: $(pwd)"
echo ""

# 1. 检查当前问题
echo "1️⃣ 检查当前问题..."

echo "   🔍 检查 RenderingMode 成员..."
if grep -q "case preview" Lomo/Models/Edit/RenderingMode.swift; then
    echo "   ✅ RenderingMode 已包含 preview 成员"
else
    echo "   ❌ RenderingMode 缺少 preview 成员"
fi

echo "   🔍 检查 FilterParameters hasActiveAdjustments 方法..."
if grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then
    echo "   ✅ FilterParameters 已包含 hasActiveAdjustments 方法"
else
    echo "   ❌ FilterParameters 缺少 hasActiveAdjustments 方法"
fi

echo ""

# 2. 修复 RenderingMode 缺少 preview 成员
echo "2️⃣ 修复 RenderingMode 枚举..."

echo "   🔧 为 RenderingMode 添加 preview 成员..."

# 备份原文件
cp Lomo/Models/Edit/RenderingMode.swift Lomo/Models/Edit/RenderingMode.swift.backup

# 更新 RenderingMode 枚举，添加 preview 成员
cat > Lomo/Models/Edit/RenderingMode.swift << 'EOF'
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 渲染模式枚举 - 区分不同的滤镜算法和渲染质量
enum RenderingMode {
    case lightroom     // Lightroom风格算法 - 用于传统调整和非胶片滤镜
    case vsco         // VSCO风格算法 - 用于胶片滤镜
    case realtime     // 实时渲染模式 - 用于预览和交互
    case highQuality  // 高质量渲染模式 - 用于最终输出
    case preview      // 预览模式 - 用于快速预览和批量操作
    
    var displayName: String {
        switch self {
        case .lightroom:
            return "标准调整模式"
        case .vsco:
            return "胶片调整模式"
        case .realtime:
            return "实时渲染"
        case .highQuality:
            return "高质量渲染"
        case .preview:
            return "预览模式"
        }
    }
    
    var shaderFunctionName: String {
        switch self {
        case .lightroom:
            return "lightroom_filter"
        case .vsco:
            return "vsco_filter"
        case .realtime:
            return "realtime_filter"
        case .highQuality:
            return "highquality_filter"
        case .preview:
            return "preview_filter"
        }
    }
    
    var description: String {
        switch self {
        case .lightroom:
            return "基于Adobe Lightroom算法的专业调色"
        case .vsco:
            return "基于胶片特性的艺术化处理"
        case .realtime:
            return "优化的实时渲染，适合预览和交互"
        case .highQuality:
            return "高质量渲染，适合最终输出"
        case .preview:
            return "快速预览模式，适合批量操作和快速预览"
        }
    }
}

/// 滤镜类型到渲染模式的映射
extension FilterPresetType {
    var renderingMode: RenderingMode {
        switch self {
        case .film:
            return .vsco  // 胶片滤镜使用VSCO算法
        case .polaroid, .vintage, .fashion, .ins:
            return .lightroom  // 其他滤镜使用Lightroom算法
        }
    }
}
EOF

echo "   ✅ RenderingMode 枚举已更新，添加了 preview 成员"

echo ""

# 3. 修复 FilterParameters 缺少 hasActiveAdjustments 方法
echo "3️⃣ 修复 FilterParameters 类..."

echo "   🔧 为 FilterParameters 添加 hasActiveAdjustments 方法..."

# 备份原文件
cp Lomo/Models/Edit/FilterParameters.swift Lomo/Models/Edit/FilterParameters.swift.backup

# 在 FilterParameters 文件末尾添加 hasActiveAdjustments 方法
# 首先检查文件是否以正确的结构结尾
if ! grep -q "}" Lomo/Models/Edit/FilterParameters.swift | tail -1; then
    echo "   ⚠️ FilterParameters 文件结构可能不完整"
fi

# 在文件末尾添加方法（在最后一个 } 之前）
# 使用 sed 在最后一个 } 之前插入方法
sed -i '' '$d' Lomo/Models/Edit/FilterParameters.swift  # 删除最后一行的 }

# 添加 hasActiveAdjustments 方法和新的结尾
cat >> Lomo/Models/Edit/FilterParameters.swift << 'EOF'
    
    // MARK: - 状态检查方法
    
    /// 检查是否有活跃的调整参数
    /// - Returns: 如果有任何非默认值的参数则返回 true
    func hasActiveAdjustments() -> Bool {
        // 检查基础色彩调整参数
        if exposure != 0.0 { return true }
        if contrast != 0.0 { return true }
        if saturation != 0.0 { return true }
        if brightness != 0.0 { return true }
        
        // 检查高级调整参数（如果存在）
        // 这里可以根据实际的 FilterParameters 属性进行扩展
        
        return false
    }
    
    /// 重置所有参数到默认值
    func resetToDefaults() {
        exposure = 0.0
        contrast = 0.0
        saturation = 0.0
        brightness = 0.0
        // 重置其他参数...
    }
    
    /// 获取活跃参数的数量
    var activeParametersCount: Int {
        var count = 0
        if exposure != 0.0 { count += 1 }
        if contrast != 0.0 { count += 1 }
        if saturation != 0.0 { count += 1 }
        if brightness != 0.0 { count += 1 }
        return count
    }
}
EOF

echo "   ✅ FilterParameters 已添加 hasActiveAdjustments 方法"

echo ""

# 4. 验证修复结果
echo "4️⃣ 验证修复结果..."

echo "   🔍 检查 RenderingMode 成员..."
required_members=("lightroom" "vsco" "realtime" "highQuality" "preview")
missing_members=()

for member in "${required_members[@]}"; do
    if grep -q "case $member" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode 包含 $member 成员"
    else
        echo "   ❌ RenderingMode 缺少 $member 成员"
        missing_members+=("$member")
    fi
done

echo "   🔍 检查 FilterParameters 方法..."
if grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then
    echo "   ✅ FilterParameters 包含 hasActiveAdjustments 方法"
else
    echo "   ❌ FilterParameters 缺少 hasActiveAdjustments 方法"
fi

echo ""

# 5. 语法检查
echo "5️⃣ 语法检查..."

files_to_check=(
    "Lomo/Models/Edit/RenderingMode.swift"
    "Lomo/Models/Edit/FilterParameters.swift"
    "Lomo/Services/Implementations/RenderingServiceImpl.swift"
)

syntax_errors=0
for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        if swift -frontend -parse "$file" >/dev/null 2>&1; then
            echo "   ✅ $(basename "$file") 语法正确"
        else
            echo "   ❌ $(basename "$file") 语法错误"
            ((syntax_errors++))
            # 显示语法错误详情
            echo "      错误详情:"
            swift -frontend -parse "$file" 2>&1 | head -3 | sed 's/^/      /'
        fi
    else
        echo "   ⚠️ $(basename "$file") 文件不存在"
        ((syntax_errors++))
    fi
done

echo ""

# 6. 检查原始错误是否解决
echo "6️⃣ 检查原始错误解决情况..."

echo "   🔍 检查 RenderingServiceImpl 中的 preview 使用..."
if grep -q "\.preview" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    if grep -q "case preview" Lomo/Models/Edit/RenderingMode.swift; then
        echo "   ✅ RenderingMode.preview 使用问题已解决"
    else
        echo "   ❌ RenderingMode 仍缺少 preview 成员"
    fi
else
    echo "   ℹ️ RenderingServiceImpl 中未找到 preview 使用"
fi

echo "   🔍 检查 RenderingServiceImpl 中的 hasActiveAdjustments 使用..."
if grep -q "hasActiveAdjustments()" Lomo/Services/Implementations/RenderingServiceImpl.swift; then
    if grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then
        echo "   ✅ FilterParameters.hasActiveAdjustments 使用问题已解决"
    else
        echo "   ❌ FilterParameters 仍缺少 hasActiveAdjustments 方法"
    fi
else
    echo "   ℹ️ RenderingServiceImpl 中未找到 hasActiveAdjustments 使用"
fi

echo ""

# 7. 总结
echo "7️⃣ 修复总结..."

if [ ${#missing_members[@]} -eq 0 ] && [ "$syntax_errors" -eq 0 ] && grep -q "func hasActiveAdjustments" Lomo/Models/Edit/FilterParameters.swift; then
    echo "   🎉 渲染服务缺失成员修复成功！"
    echo ""
    echo "✅ 修复成果："
    echo "   • RenderingMode 添加了 preview 成员"
    echo "   • FilterParameters 添加了 hasActiveAdjustments 方法"
    echo "   • 所有相关文件语法检查通过"
    echo "   • 原始编译错误已解决"
    echo ""
    echo "🎯 当前 RenderingMode 成员："
    echo "   📁 lightroom, vsco, realtime, highQuality, preview"
    echo ""
    echo "🎯 新增 FilterParameters 方法："
    echo "   📁 hasActiveAdjustments() -> Bool"
    echo "   📁 resetToDefaults()"
    echo "   📁 activeParametersCount: Int"
    
    # 删除备份文件
    rm -f Lomo/Models/Edit/RenderingMode.swift.backup
    rm -f Lomo/Models/Edit/FilterParameters.swift.backup
else
    echo "   ⚠️ 修复过程中发现问题，需要进一步检查"
    echo "   📋 缺失成员: ${missing_members[*]}"
    echo "   📋 语法错误数: $syntax_errors"
    if [ -f "Lomo/Models/Edit/RenderingMode.swift.backup" ]; then
        echo "   🔄 如需回滚 RenderingMode，备份文件位于: Lomo/Models/Edit/RenderingMode.swift.backup"
    fi
    if [ -f "Lomo/Models/Edit/FilterParameters.swift.backup" ]; then
        echo "   🔄 如需回滚 FilterParameters，备份文件位于: Lomo/Models/Edit/FilterParameters.swift.backup"
    fi
fi

echo ""
echo "🏁 渲染服务缺失成员修复完成！"