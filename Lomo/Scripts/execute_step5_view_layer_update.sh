#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 步骤5执行脚本：更新View层依赖注入
# 将AdjustView和FilterView更新为使用重构后的ViewModel

echo "🚀 开始执行步骤5: 更新View层依赖注入"
echo "=================================================="

# 检查当前状态
echo ""
echo "1️⃣ 检查当前状态..."

# 检查步骤4是否完成
if ! grep -q "AdjustViewModelRefactored" Lomo/DependencyInjection/AdjustDependencyContainer.swift; then
    echo "❌ 步骤4未完成，请先完成依赖注入容器更新"
    exit 1
fi

if ! grep -q "FilterViewModelRefactored" Lomo/DependencyInjection/FilterDependencyContainer.swift; then
    echo "❌ 步骤4未完成，请先完成依赖注入容器更新"
    exit 1
fi

echo "✅ 步骤4已完成，可以开始步骤5"

# 创建备份
echo ""
echo "2️⃣ 创建备份..."
cp Lomo/Views/Edit/AdjustView.swift Lomo/Views/Edit/AdjustView.swift.backup.step5
cp Lomo/Views/Edit/FilterView.swift Lomo/Views/Edit/FilterView.swift.backup.step5
cp Lomo/Views/Edit/EditView.swift Lomo/Views/Edit/EditView.swift.backup.step5
echo "✅ 备份创建完成"

# 步骤5.1: 更新AdjustView
echo ""
echo "3️⃣ 步骤5.1: 更新AdjustView..."

echo "   📝 更新ViewModel类型声明..."
# 更新类型声明
sed -i '' 's/@ObservedObject var adjustViewModel: AdjustViewModel/@ObservedObject var adjustViewModel: AdjustViewModelRefactored/g' Lomo/Views/Edit/AdjustView.swift
sed -i '' 's/init(adjustViewModel: AdjustViewModel)/init(adjustViewModel: AdjustViewModelRefactored)/g' Lomo/Views/Edit/AdjustView.swift

echo "   📝 替换getCurrentParametersCopy()调用..."
# 替换getCurrentParametersCopy()调用
sed -i '' 's/adjustViewModel\.getCurrentParametersCopy()/adjustViewModel.currentParameters/g' Lomo/Views/Edit/AdjustView.swift

echo "✅ AdjustView更新完成"

# 步骤5.2: 更新FilterView  
echo ""
echo "4️⃣ 步骤5.2: 更新FilterView..."

echo "   📝 更新ViewModel类型声明..."
# 更新类型声明
sed -i '' 's/@ObservedObject var filterViewModel: FilterViewModel/@ObservedObject var filterViewModel: FilterViewModelRefactored/g' Lomo/Views/Edit/FilterView.swift
sed -i '' 's/init(filterViewModel: FilterViewModel)/init(filterViewModel: FilterViewModelRefactored)/g' Lomo/Views/Edit/FilterView.swift

echo "✅ FilterView更新完成"

# 步骤5.3: 更新EditView中的ViewModel实例化
echo ""
echo "5️⃣ 步骤5.3: 更新EditView中的ViewModel实例化..."

# 更新FilterViewModel实例化
sed -i '' 's/@StateObject private var filterViewModelInstance = FilterViewModel()/@StateObject private var filterViewModelInstance = FilterDependencyContainer.filterViewModel()/g' Lomo/Views/Edit/EditView.swift

# 更新AdjustViewModel实例化  
sed -i '' 's/@StateObject private var adjustViewModelInstance = AdjustViewModel()/@StateObject private var adjustViewModelInstance = AdjustDependencyContainer.adjustViewModel()/g' Lomo/Views/Edit/EditView.swift

echo "✅ EditView更新完成"

# 编译验证
echo ""
echo "6️⃣ 编译验证..."

if swift build > /dev/null 2>&1; then
    echo "✅ 编译通过"
else
    echo "❌ 编译失败，正在回滚..."
    
    # 回滚所有更改
    cp Lomo/Views/Edit/AdjustView.swift.backup.step5 Lomo/Views/Edit/AdjustView.swift
    cp Lomo/Views/Edit/FilterView.swift.backup.step5 Lomo/Views/Edit/FilterView.swift  
    cp Lomo/Views/Edit/EditView.swift.backup.step5 Lomo/Views/Edit/EditView.swift
    
    echo "❌ 已回滚到步骤5执行前状态"
    echo "编译错误信息:"
    swift build 2>&1 | head -10
    exit 1
fi

# 功能验证提示
echo ""
echo "7️⃣ 功能验证提示..."
echo "📋 请手动验证以下功能:"
echo "   - 调节功能是否正常工作"
echo "   - 滤镜功能是否正常工作"  
echo "   - 参数调整是否实时生效"
echo "   - UI交互是否响应正常"

# 清理备份文件
echo ""
echo "8️⃣ 清理备份文件..."
rm -f Lomo/Views/Edit/AdjustView.swift.backup.step5
rm -f Lomo/Views/Edit/FilterView.swift.backup.step5
rm -f Lomo/Views/Edit/EditView.swift.backup.step5
echo "✅ 备份文件清理完成"

# 总结
echo ""
echo "🎉 步骤5执行完成！"
echo "=================================================="
echo "✅ AdjustView已更新为使用AdjustViewModelRefactored"
echo "✅ FilterView已更新为使用FilterViewModelRefactored"
echo "✅ EditView已更新为使用依赖注入容器"
echo "✅ 编译验证通过"
echo ""
echo "🎯 下一步: 步骤6 - 最终验证和优化"
echo "   - 架构评分验证"
echo "   - 性能测试"
echo "   - 完整功能测试"
echo "   - 文档更新"