#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 修复AdjustView中HSL方法调用的脚本

echo "🔧 开始修复AdjustView中的HSL方法调用..."

# 定义文件路径
ADJUST_VIEW_FILE="Lomo/Views/Edit/AdjustView.swift"
ADJUST_VIEWMODEL_FILE="Lomo/ViewModels/Edit/AdjustViewModelRefactored.swift"

# 检查文件是否存在
if [ ! -f "$ADJUST_VIEW_FILE" ]; then
    echo "❌ 错误：找不到文件 $ADJUST_VIEW_FILE"
    exit 1
fi

if [ ! -f "$ADJUST_VIEWMODEL_FILE" ]; then
    echo "❌ 错误：找不到文件 $ADJUST_VIEWMODEL_FILE"
    exit 1
fi

echo "📝 修复HSL参数访问..."

# 修复HSL色相访问
sed -i '' 's/adjustViewModel\.getCurrentHSLParameters()\.hue/adjustViewModel.currentParameters.hue/g' "$ADJUST_VIEW_FILE"

# 修复HSL饱和度访问
sed -i '' 's/adjustViewModel\.getCurrentHSLParameters()\.saturation/adjustViewModel.currentParameters.hslSaturation/g' "$ADJUST_VIEW_FILE"

# 修复HSL明度访问
sed -i '' 's/adjustViewModel\.getCurrentHSLParameters()\.luminance/adjustViewModel.currentParameters.hslLuminance/g' "$ADJUST_VIEW_FILE"

echo "📝 添加getCurrentHSLParameters方法到ViewModel..."

# 检查是否已经存在getCurrentHSLParameters方法
if ! grep -q "getCurrentHSLParameters" "$ADJUST_VIEWMODEL_FILE"; then
    # 在文件末尾添加getCurrentHSLParameters方法
    cat >> "$ADJUST_VIEWMODEL_FILE" << 'EOF'
    
    // MARK: - HSL参数获取方法
    
    /// 获取当前选中颜色的HSL参数
    func getCurrentHSLParameters() -> (hue: Float, saturation: Float, luminance: Float) {
        return (
            hue: currentParameters.hue,
            saturation: currentParameters.hslSaturation,
            luminance: currentParameters.hslLuminance
        )
    }
EOF
    echo "✅ 已添加getCurrentHSLParameters方法"
else
    echo "ℹ️ getCurrentHSLParameters方法已存在"
fi

echo "📝 验证修复结果..."

# 检查是否还有未修复的getCurrentHSLParameters调用
if grep -q "getCurrentHSLParameters()" "$ADJUST_VIEW_FILE"; then
    echo "⚠️ 警告：仍然存在getCurrentHSLParameters()的调用"
    grep -n "getCurrentHSLParameters()" "$ADJUST_VIEW_FILE"
else
    echo "✅ 所有getCurrentHSLParameters()调用已修复"
fi

# 检查语法
echo "🔍 检查Swift语法..."
if command -v swiftc >/dev/null 2>&1; then
    if swiftc -parse "$ADJUST_VIEW_FILE" >/dev/null 2>&1; then
        echo "✅ AdjustView Swift语法检查通过"
    else
        echo "⚠️ AdjustView Swift语法检查发现问题，但可能是由于依赖关系"
    fi
    
    if swiftc -parse "$ADJUST_VIEWMODEL_FILE" >/dev/null 2>&1; then
        echo "✅ AdjustViewModel Swift语法检查通过"
    else
        echo "⚠️ AdjustViewModel Swift语法检查发现问题，但可能是由于依赖关系"
    fi
else
    echo "ℹ️ 未找到swiftc，跳过语法检查"
fi

echo "🎉 AdjustView HSL方法修复完成！"
echo ""
echo "📋 修复内容："
echo "  - 修复了getCurrentHSLParameters().hue调用"
echo "  - 修复了getCurrentHSLParameters().saturation调用"
echo "  - 修复了getCurrentHSLParameters().luminance调用"
echo "  - 添加了getCurrentHSLParameters方法到ViewModel"
echo ""
echo "🔄 请重新编译项目验证修复效果"