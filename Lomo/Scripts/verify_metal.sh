#!/bin/bash

# Metal LUT验证脚本
echo "🔍 验证Metal LUT实现..."

# 检查Metal着色器文件
METAL_FILE="Lomo/Shaders/LUTShaders.metal"
if [ -f "$METAL_FILE" ]; then
    echo "✅ Metal着色器文件存在: $METAL_FILE"
    
    # 检查关键函数
    if grep -q "lookup3DLUT" "$METAL_FILE"; then
        echo "✅ lookup3DLUT函数存在"
    else
        echo "❌ lookup3DLUT函数缺失"
    fi
    
    if grep -q "apply_3d_lut_filter" "$METAL_FILE"; then
        echo "✅ apply_3d_lut_filter着色器存在"
    else
        echo "❌ apply_3d_lut_filter着色器缺失"
    fi
    
    # 检查语法错误
    echo "🔍 检查Metal语法..."
    if grep -q "texture3d<float, access::read>" "$METAL_FILE"; then
        echo "✅ 3D纹理类型正确"
    else
        echo "⚠️ 3D纹理类型可能有问题"
    fi
    
else
    echo "❌ Metal着色器文件不存在: $METAL_FILE"
fi

# 检查Swift处理器文件
SWIFT_FILE="Lomo/Managers/Edit/MetalLUTProcessor.swift"
if [ -f "$SWIFT_FILE" ]; then
    echo "✅ Metal LUT处理器存在: $SWIFT_FILE"
    
    if grep -q "class MetalLUTProcessor" "$SWIFT_FILE"; then
        echo "✅ MetalLUTProcessor类存在"
    else
        echo "❌ MetalLUTProcessor类缺失"
    fi
    
    if grep -q "create3DTexture" "$SWIFT_FILE"; then
        echo "✅ 3D纹理创建方法存在"
    else
        echo "❌ 3D纹理创建方法缺失"
    fi
    
else
    echo "❌ Metal LUT处理器不存在: $SWIFT_FILE"
fi

# 检查测试文件
TEST_FILE="Lomo/Tests/MetalLUTTest.swift"
if [ -f "$TEST_FILE" ]; then
    echo "✅ Metal LUT测试文件存在: $TEST_FILE"
else
    echo "❌ Metal LUT测试文件不存在: $TEST_FILE"
fi

# 检查Xcode设置
echo "🔍 检查Xcode设置..."
XCODE_PATH=$(xcode-select -p)
echo "📍 Xcode路径: $XCODE_PATH"

if [[ "$XCODE_PATH" == *"CommandLineTools"* ]]; then
    echo "⚠️ 当前使用Command Line Tools，建议切换到Xcode.app"
    echo "💡 运行: sudo xcode-select -s /Applications/Xcode.app/Contents/Developer"
else
    echo "✅ 使用完整Xcode环境"
fi

# 检查Metal支持
echo "🔍 检查Metal支持..."
if command -v xcrun >/dev/null 2>&1; then
    echo "✅ xcrun可用"
    
    # 尝试编译Metal着色器
    if [ -f "$METAL_FILE" ]; then
        echo "🔨 尝试编译Metal着色器..."
        if xcrun -sdk iphoneos metal -c "$METAL_FILE" -o /tmp/test.air 2>/dev/null; then
            echo "✅ Metal着色器编译成功"
            rm -f /tmp/test.air
        else
            echo "❌ Metal着色器编译失败"
            echo "💡 尝试运行: xcrun -sdk iphoneos metal -c $METAL_FILE"
        fi
    fi
else
    echo "❌ xcrun不可用"
fi

echo "🎯 验证完成"
echo ""
echo "📋 总结:"
echo "- Metal着色器文件: $([ -f "$METAL_FILE" ] && echo "✅" || echo "❌")"
echo "- Swift处理器: $([ -f "$SWIFT_FILE" ] && echo "✅" || echo "❌")"
echo "- 测试文件: $([ -f "$TEST_FILE" ] && echo "✅" || echo "❌")"
echo "- Xcode环境: $([ "$XCODE_PATH" != *"CommandLineTools"* ] && echo "✅" || echo "⚠️")"
echo ""
echo "🚀 下一步: 在Xcode中编译项目以验证Metal LUT功能"
