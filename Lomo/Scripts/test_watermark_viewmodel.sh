#!/bin/bash

# 水印ViewModel测试脚本
echo "🧪 水印ViewModel创建验证"
echo "================================"

# 检查关键文件是否存在
echo ""
echo "📁 检查文件结构..."

FILES=(
    "Lomo/ViewModels/Edit/WatermarkViewModel.swift"
    "Lomo/Services/Edit/WatermarkService.swift"
    "Lomo/Views/Edit/Components/WatermarkControlView.swift"
    "Lomo/Managers/Edit/WatermarkSettingsManager.swift"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 文件缺失"
        exit 1
    fi
done

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

# 检查WatermarkViewModel类的基本结构
echo ""
echo "🔍 检查WatermarkViewModel结构..."

if grep -q "class WatermarkViewModel" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ WatermarkViewModel类定义存在"
else
    echo "❌ WatermarkViewModel类定义缺失"
    exit 1
fi

if grep -q "ObservableObject" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ ObservableObject协议实现存在"
else
    echo "❌ ObservableObject协议实现缺失"
    exit 1
fi

# 检查关键@Published属性是否存在
PUBLISHED_PROPERTIES=(
    "@Published var selectedWatermarkType"
    "@Published var selectedWatermarkCategory"
    "@Published var filteredWatermarkIndices"
    "@Published var currentSettings"
    "@Published var isLoading"
)

for property in "${PUBLISHED_PROPERTIES[@]}"; do
    if grep -q "$property" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
        echo "✅ $property 属性存在"
    else
        echo "❌ $property 属性缺失"
        exit 1
    fi
done

# 检查依赖注入是否正确
echo ""
echo "🔗 检查依赖注入..."

if grep -q "watermarkService: WatermarkServiceProtocol" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ 正确的依赖注入构造函数"
else
    echo "❌ 依赖注入构造函数缺失"
    exit 1
fi

if grep -q "private let watermarkService: WatermarkServiceProtocol" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ 正确的服务属性定义"
else
    echo "❌ 服务属性定义缺失"
    exit 1
fi

# 检查基础方法是否存在
METHODS=(
    "func loadInitialData"
    "func refreshCurrentSettings"
    "func selectCategory"
    "func selectWatermark"
    "func updateWatermarkText"
    "func toggleWatermarkText"
)

for method in "${METHODS[@]}"; do
    if grep -q "$method" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
        echo "✅ $method 方法存在"
    else
        echo "❌ $method 方法缺失"
        exit 1
    fi
done

# 验证现有代码未被修改
echo ""
echo "🛡️ 验证现有代码完整性..."

if [ -f "Lomo/Views/Edit/Components/WatermarkControlView.swift" ]; then
    # 检查WatermarkControlView是否仍然包含原始的@State变量
    if grep -q "@State private var selectedWatermarkType" "Lomo/Views/Edit/Components/WatermarkControlView.swift"; then
        echo "✅ WatermarkControlView.swift 保持原始状态（@State变量未被移除）"
    else
        echo "❌ WatermarkControlView.swift 可能被意外修改"
        exit 1
    fi
else
    echo "❌ WatermarkControlView.swift 文件缺失"
    exit 1
fi

# 检查文件行数，确保没有意外的大规模修改
ORIGINAL_LINES=$(wc -l < "Lomo/Views/Edit/Components/WatermarkControlView.swift")
if [ "$ORIGINAL_LINES" -gt 3000 ]; then
    echo "✅ WatermarkControlView.swift 行数正常 ($ORIGINAL_LINES 行)"
else
    echo "⚠️ WatermarkControlView.swift 行数异常 ($ORIGINAL_LINES 行)，可能被意外修改"
fi

# 最终验证
echo ""
echo "🎉 第3步验证结果"
echo "================================"
echo "✅ WatermarkViewModel创建成功"
echo "✅ 所有@Published属性定义正确"
echo "✅ 依赖注入结构正确"
echo "✅ 基础方法框架完整"
echo "✅ 现有WatermarkControlView完全未被修改"
echo "✅ 项目编译正常"
echo ""
echo "📋 当前状态："
echo "   - WatermarkViewModel仅包含基础属性和空方法"
echo "   - 所有实际逻辑仍在WatermarkControlView中"
echo "   - 可以安全进行下一步：渐进式状态迁移"
echo ""
echo "🔒 安全保证："
echo "   - 删除 Lomo/ViewModels/Edit/WatermarkViewModel.swift 即可完全回滚"
echo "   - WatermarkControlView的所有功能完全正常"
echo "   - 新的ViewModel暂时不被任何地方使用"
