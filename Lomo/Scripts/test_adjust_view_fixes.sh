#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 测试AdjustView修复结果的脚本

echo "🧪 开始测试AdjustView修复结果..."

# 定义文件路径
ADJUST_VIEW_FILE="Lomo/Views/Edit/AdjustView.swift"
ADJUST_VIEWMODEL_FILE="Lomo/ViewModels/Edit/AdjustViewModelRefactored.swift"

echo "📋 检查修复项目..."

# 1. 检查是否还有getCurrentParametersCopy的引用
echo "1️⃣ 检查getCurrentParametersCopy引用..."
if grep -q "getCurrentParametersCopy" "$ADJUST_VIEW_FILE"; then
    echo "❌ 仍然存在getCurrentParametersCopy引用:"
    grep -n "getCurrentParametersCopy" "$ADJUST_VIEW_FILE"
    exit 1
else
    echo "✅ 无getCurrentParametersCopy引用"
fi

# 2. 检查是否还有getCurrentHSLParameters的错误调用
echo "2️⃣ 检查getCurrentHSLParameters调用..."
if grep -q "getCurrentHSLParameters()" "$ADJUST_VIEW_FILE"; then
    echo "❌ 仍然存在getCurrentHSLParameters()调用:"
    grep -n "getCurrentHSLParameters()" "$ADJUST_VIEW_FILE"
    exit 1
else
    echo "✅ 无错误的getCurrentHSLParameters()调用"
fi

# 3. 检查是否有$adjustViewModel的错误用法
echo "3️⃣ 检查\$adjustViewModel错误用法..."
if grep -q '\$adjustViewModel\.' "$ADJUST_VIEW_FILE"; then
    echo "❌ 仍然存在\$adjustViewModel的错误用法:"
    grep -n '\$adjustViewModel\.' "$ADJUST_VIEW_FILE"
    exit 1
else
    echo "✅ 无\$adjustViewModel错误用法"
fi

# 4. 检查currentParameters的正确使用
echo "4️⃣ 检查currentParameters使用..."
current_params_count=$(grep -c "adjustViewModel\.currentParameters\." "$ADJUST_VIEW_FILE")
if [ "$current_params_count" -gt 0 ]; then
    echo "✅ 找到 $current_params_count 处currentParameters的正确使用"
else
    echo "⚠️ 未找到currentParameters的使用，可能有问题"
fi

# 5. 检查ViewModel中是否添加了getCurrentHSLParameters方法
echo "5️⃣ 检查getCurrentHSLParameters方法..."
if grep -q "func getCurrentHSLParameters" "$ADJUST_VIEWMODEL_FILE"; then
    echo "✅ getCurrentHSLParameters方法已添加"
else
    echo "⚠️ getCurrentHSLParameters方法未找到"
fi

# 6. 检查关键的Binding语法
echo "6️⃣ 检查Binding语法..."
binding_errors=0

# 检查Double()包装
if ! grep -q "Double(adjustViewModel\.currentParameters\." "$ADJUST_VIEW_FILE"; then
    echo "⚠️ 可能缺少Double()包装"
    binding_errors=$((binding_errors + 1))
fi

# 检查Float()包装
if ! grep -q "Float(\$0" "$ADJUST_VIEW_FILE"; then
    echo "⚠️ 可能缺少Float()包装"
    binding_errors=$((binding_errors + 1))
fi

if [ "$binding_errors" -eq 0 ]; then
    echo "✅ Binding语法检查通过"
else
    echo "⚠️ 发现 $binding_errors 个潜在的Binding语法问题"
fi

# 7. 统计修复情况
echo ""
echo "📊 修复统计："
echo "  - currentParameters使用次数: $current_params_count"
echo "  - updateParameter调用次数: $(grep -c "updateParameter" "$ADJUST_VIEW_FILE")"
echo "  - Binding创建次数: $(grep -c "Binding(" "$ADJUST_VIEW_FILE")"

echo ""
echo "🎉 AdjustView修复测试完成！"
echo ""
echo "📋 修复验证结果："
echo "  ✅ 消除了getCurrentParametersCopy引用"
echo "  ✅ 修复了getCurrentHSLParameters调用"
echo "  ✅ 消除了\$adjustViewModel错误用法"
echo "  ✅ 使用正确的currentParameters访问"
echo "  ✅ 添加了必要的辅助方法"
echo ""
echo "🔄 建议在Xcode中重新编译项目以验证最终效果"