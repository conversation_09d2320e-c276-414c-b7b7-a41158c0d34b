#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

echo "🧪 步骤3.3：依赖注入更新验证"
echo "================================"

# 1. 检查编译状态
echo "1️⃣ 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
else
    echo "❌ 项目编译失败"
    echo "编译错误信息："
    swift build
    exit 1
fi

echo ""

# 2. 验证WatermarkManagerService创建
echo "2️⃣ 验证WatermarkManagerService..."

if [ -f "Lomo/Services/Edit/WatermarkManagerService.swift" ]; then
    echo "✅ WatermarkManagerService.swift 文件存在"
    
    # 检查协议定义
    if grep -q "protocol WatermarkManagerServiceProtocol" "Lomo/Services/Edit/WatermarkManagerService.swift"; then
        echo "✅ WatermarkManagerServiceProtocol 协议已定义"
    else
        echo "❌ WatermarkManagerServiceProtocol 协议缺失"
        exit 1
    fi
    
    # 检查实现类
    if grep -q "class WatermarkManagerService: WatermarkManagerServiceProtocol" "Lomo/Services/Edit/WatermarkManagerService.swift"; then
        echo "✅ WatermarkManagerService 实现类已定义"
    else
        echo "❌ WatermarkManagerService 实现类缺失"
        exit 1
    fi
else
    echo "❌ WatermarkManagerService.swift 文件不存在"
    exit 1
fi

echo ""

# 3. 验证WatermarkService扩展
echo "3️⃣ 验证WatermarkService扩展..."

if grep -q "func isWatermarkManagerAvailable() -> Bool" "Lomo/Services/Edit/WatermarkService.swift"; then
    echo "✅ WatermarkService 已添加管理器操作方法"
else
    echo "❌ WatermarkService 缺少管理器操作方法"
    exit 1
fi

# 检查方法实现
manager_methods=(
    "isWatermarkManagerAvailable"
    "getWatermarkManager"
    "setupWatermarkManager"
    "applyWatermarkStyleDirect"
    "removeCurrentWatermarkDirect"
)

for method in "${manager_methods[@]}"; do
    if grep -q "func $method" "Lomo/Services/Edit/WatermarkService.swift"; then
        echo "✅ WatermarkService: $method 方法存在"
    else
        echo "❌ WatermarkService: $method 方法缺失"
        exit 1
    fi
done

echo ""

# 4. 验证DI容器更新
echo "4️⃣ 验证依赖注入容器..."

if grep -q "var watermarkManagerService: WatermarkManagerService" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift"; then
    echo "✅ DI容器已添加WatermarkManagerService"
else
    echo "❌ DI容器缺少WatermarkManagerService"
    exit 1
fi

# 检查工厂方法更新
if grep -q "watermarkManagerService: watermarkManagerService" "Lomo/DependencyInjection/WatermarkDependencyContainer.swift"; then
    echo "✅ ViewModel工厂方法已更新"
else
    echo "❌ ViewModel工厂方法未更新"
    exit 1
fi

echo ""

# 5. 验证ViewModel构造函数
echo "5️⃣ 验证ViewModel构造函数..."

if grep -q "watermarkManagerService: WatermarkManagerServiceProtocol?" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
    echo "✅ WatermarkViewModel 构造函数已更新"
else
    echo "❌ WatermarkViewModel 构造函数未更新"
    exit 1
fi

# 检查ViewModel中的新方法
vm_methods=(
    "setupWatermarkManager"
    "isWatermarkManagerAvailable"
    "applyWatermarkStyleDirect"
    "removeCurrentWatermarkDirect"
)

for method in "${vm_methods[@]}"; do
    if grep -q "func $method" "Lomo/ViewModels/Edit/WatermarkViewModel.swift"; then
        echo "✅ WatermarkViewModel: $method 方法存在"
    else
        echo "❌ WatermarkViewModel: $method 方法缺失"
        exit 1
    fi
done

echo ""

# 6. 验证View层依赖注入
echo "6️⃣ 验证View层依赖注入..."

# 检查WatermarkControlView
if grep -q "watermarkManagerService = WatermarkDependencyContainer.shared.watermarkManagerService" "Lomo/Views/Edit/Components/WatermarkControlView.swift"; then
    echo "✅ WatermarkControlView 已注入WatermarkManagerService"
else
    echo "❌ WatermarkControlView 未注入WatermarkManagerService"
    exit 1
fi

# 检查EditView
if grep -q "watermarkManagerService = WatermarkDependencyContainer.shared.watermarkManagerService" "Lomo/Views/Edit/EditView.swift"; then
    echo "✅ EditView 已注入WatermarkManagerService"
else
    echo "❌ EditView 未注入WatermarkManagerService"
    exit 1
fi

echo ""

# 7. 验证兼容性保持
echo "7️⃣ 验证向后兼容性..."

# 检查是否仍保持原有的WatermarkManagerProvider调用（兼容模式）
if grep -q "WatermarkManagerProvider.shared" "Lomo/Views/Edit/Components/WatermarkControlView.swift"; then
    echo "✅ WatermarkControlView 保持向后兼容"
else
    echo "⚠️ WatermarkControlView 可能失去向后兼容性"
fi

if grep -q "WatermarkManagerProvider.shared" "Lomo/Views/Edit/EditView.swift"; then
    echo "✅ EditView 保持向后兼容"
else
    echo "⚠️ EditView 可能失去向后兼容性"
fi

echo ""

# 8. 统计依赖注入覆盖情况
echo "8️⃣ 统计依赖注入覆盖情况..."

# 统计新增的依赖注入代码
di_lines=$(grep -c "watermarkManagerService" Lomo/Views/Edit/Components/WatermarkControlView.swift Lomo/Views/Edit/EditView.swift Lomo/ViewModels/Edit/WatermarkViewModel.swift Lomo/DependencyInjection/WatermarkDependencyContainer.swift 2>/dev/null || echo "0")
echo "📊 依赖注入相关代码行数: $di_lines"

# 统计仍使用单例的地方
singleton_usage=$(grep -c "WatermarkManagerProvider.shared" Lomo/Views/Edit/Components/WatermarkControlView.swift Lomo/Views/Edit/EditView.swift Lomo/ViewModels/Edit/WatermarkViewModel.swift 2>/dev/null || echo "0")
echo "📊 仍使用单例的地方: $singleton_usage 处（兼容模式）"

echo ""

# 9. 最终验证
echo "9️⃣ 最终验证..."

echo "✅ 步骤3.3完成验证："
echo "   - WatermarkManagerService 创建完成"
echo "   - WatermarkService 扩展完成"
echo "   - DI容器更新完成"
echo "   - ViewModel构造函数更新完成"
echo "   - View层依赖注入完成"
echo "   - 向后兼容性保持"
echo "   - 编译状态正常"

echo ""
echo "🎯 准备进入步骤3.4：逐步替换调用"
