#!/bin/bash

# 修复重复参数脚本
echo "🔄 修复重复的watermarkService参数"
echo "================================"

FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"
BACKUP_FILE="${FILE}.duplicate_backup"

# 创建备份
echo "📋 创建备份文件..."
cp "$FILE" "$BACKUP_FILE"
echo "✅ 备份已创建: $BACKUP_FILE"

echo ""
echo "🔍 检查重复参数..."

# 统计重复参数的数量
DUPLICATE_COUNT=$(grep -n "watermarkService: watermarkService," "$FILE" | wc -l)
echo "📊 发现 $DUPLICATE_COUNT 个watermarkService参数"

echo ""
echo "🔄 开始修复重复参数..."

# 创建临时文件来处理重复行
TEMP_FILE="${FILE}.temp"
cp "$FILE" "$TEMP_FILE"

# 使用awk来处理重复的watermarkService参数
# 这个脚本会在每个TextInputOptionView调用中只保留第一个watermarkService参数
awk '
BEGIN { in_textinput_call = 0; seen_watermark_service = 0 }
/TextInputOptionView\(/ { 
    in_textinput_call = 1; 
    seen_watermark_service = 0;
    print; 
    next 
}
/^\s*\)/ && in_textinput_call { 
    in_textinput_call = 0; 
    seen_watermark_service = 0;
    print; 
    next 
}
/watermarkService: watermarkService,/ && in_textinput_call {
    if (seen_watermark_service == 0) {
        seen_watermark_service = 1;
        print;
    }
    # 跳过重复的watermarkService参数
    next;
}
{ print }
' "$TEMP_FILE" > "$FILE"

# 清理临时文件
rm "$TEMP_FILE"

echo "✅ 已修复重复参数"

# 检查修复后的参数数量
AFTER_COUNT=$(grep -n "watermarkService: watermarkService," "$FILE" | wc -l)
echo "📊 修复后参数数量: $AFTER_COUNT"

FIXED_COUNT=$((DUPLICATE_COUNT - AFTER_COUNT))
echo "🎯 修复了 $FIXED_COUNT 个重复参数"

# 检查编译状态
echo ""
echo "🔨 检查编译状态..."
if swift build > /dev/null 2>&1; then
    echo "✅ 项目编译成功"
    echo ""
    echo "🎉 重复参数修复完成！"
    echo "================================"
    echo "✅ 修复了 $FIXED_COUNT 个重复参数"
    echo "✅ 保留了 $AFTER_COUNT 个正确参数"
    echo "✅ 项目编译正常"
    echo "✅ 备份文件已保存: $BACKUP_FILE"
    echo ""
    echo "📋 如果需要回滚："
    echo "   cp $BACKUP_FILE $FILE"
else
    echo "❌ 项目编译失败，正在回滚..."
    cp "$BACKUP_FILE" "$FILE"
    echo "🔄 已回滚到原始状态"
    echo ""
    echo "编译错误信息："
    swift build
    exit 1
fi
