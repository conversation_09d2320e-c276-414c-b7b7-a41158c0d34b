#!/bin/bash

# 检查SubscriptionService.shared引用清理脚本
# 验证是否还有代码文件中存在SubscriptionService.shared的引用

echo "🔍 检查SubscriptionService.shared引用清理情况..."

# 排除文档文件，只检查代码文件
echo "📁 在代码文件中搜索SubscriptionService.shared引用..."

# 搜索Swift代码文件中的引用
swift_files_with_shared=$(find Lomo -name "*.swift" -not -path "*/Documentation/*" -exec grep -l "SubscriptionService\.shared" {} \;)

if [ -z "$swift_files_with_shared" ]; then
    echo "✅ 所有Swift代码文件中已清理SubscriptionService.shared引用"
else
    echo "❌ 以下Swift文件中仍存在SubscriptionService.shared引用:"
    echo "$swift_files_with_shared"
    echo ""
    echo "详细引用位置:"
    find Lomo -name "*.swift" -not -path "*/Documentation/*" -exec grep -Hn "SubscriptionService\.shared" {} \;
    exit 1
fi

# 检查是否有其他类似的单例引用
echo ""
echo "🔍 检查其他可能的业务单例引用..."

other_singletons=$(find Lomo -name "*.swift" -not -path "*/Documentation/*" -exec grep -Hn "\.shared" {} \; | grep -v "DependencyContainer\|UIScreen\|UserDefaults\|NotificationCenter\|FileManager\|URLSession\|Bundle\|ProcessInfo")

if [ -z "$other_singletons" ]; then
    echo "✅ 未发现其他可疑的业务单例引用"
else
    echo "⚠️  发现以下可能的业务单例引用，请检查是否需要重构:"
    echo "$other_singletons"
fi

echo ""
echo "🎯 SubscriptionService.shared引用清理验证完成！"