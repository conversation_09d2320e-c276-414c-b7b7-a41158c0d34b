#!/bin/bash

# Copyright (c) 2025 LoniceraLab. All rights reserved.

# 修复curveColor方法重复声明的问题

echo "🔧 开始修复curveColor方法重复声明..."

# 定义文件路径
ADJUST_VIEW_FILE="Lomo/Views/Edit/AdjustView.swift"

# 检查文件是否存在
if [ ! -f "$ADJUST_VIEW_FILE" ]; then
    echo "❌ 错误：找不到文件 $ADJUST_VIEW_FILE"
    exit 1
fi

echo "📝 备份原文件..."
cp "$ADJUST_VIEW_FILE" "${ADJUST_VIEW_FILE}.backup.redeclaration"

echo "📝 查找所有curveColor方法声明..."

# 查找所有curveColor方法的行号
curve_color_lines=$(grep -n "func curveColor" "$ADJUST_VIEW_FILE" | cut -d: -f1)
echo "发现curveColor方法在以下行: $curve_color_lines"

echo "📝 移除重复的curveColor方法声明..."

# 创建临时文件
TEMP_FILE=$(mktemp)

# 使用awk来移除重复的curveColor方法
awk '
BEGIN { 
    in_curve_color = 0
    curve_color_count = 0
    skip_lines = 0
}

# 检测curveColor方法的开始
/func curveColor/ {
    curve_color_count++
    if (curve_color_count == 1) {
        # 保留第一个curveColor方法
        in_curve_color = 1
        print $0
        next
    } else {
        # 跳过后续的curveColor方法
        in_curve_color = 2  # 标记为要跳过的方法
        skip_lines = 1
        next
    }
}

# 在要跳过的curveColor方法内部
in_curve_color == 2 {
    if (/^[[:space:]]*}[[:space:]]*$/) {
        skip_lines--
        if (skip_lines <= 0) {
            in_curve_color = 0
        }
    } else if (/^[[:space:]]*case.*:/) {
        # 在switch语句内部，不改变skip_lines
    } else if (/{/) {
        skip_lines++
    }
    next
}

# 在保留的curveColor方法内部
in_curve_color == 1 {
    print $0
    if (/^[[:space:]]*}[[:space:]]*$/) {
        in_curve_color = 0
    }
    next
}

# 正常输出其他行
{
    print $0
}
' "$ADJUST_VIEW_FILE" > "$TEMP_FILE"

# 替换原文件
mv "$TEMP_FILE" "$ADJUST_VIEW_FILE"

echo "📝 验证修复结果..."

# 检查剩余的curveColor方法数量
remaining_curve_color=$(grep -c "func curveColor" "$ADJUST_VIEW_FILE")
echo "  - 剩余curveColor方法数量: $remaining_curve_color"

if [ "$remaining_curve_color" -eq 1 ]; then
    echo "✅ curveColor方法重复声明已修复"
elif [ "$remaining_curve_color" -eq 0 ]; then
    echo "❌ 错误：所有curveColor方法都被删除了"
    echo "恢复备份文件..."
    cp "${ADJUST_VIEW_FILE}.backup.redeclaration" "$ADJUST_VIEW_FILE"
    exit 1
else
    echo "⚠️ 仍然存在 $remaining_curve_color 个curveColor方法"
fi

# 检查channelColors属性是否存在
if grep -q "channelColors" "$ADJUST_VIEW_FILE"; then
    echo "✅ channelColors属性存在"
else
    echo "⚠️ channelColors属性不存在，可能需要添加"
fi

# 检查语法
echo "🔍 检查Swift语法..."
if command -v swiftc >/dev/null 2>&1; then
    if swiftc -parse "$ADJUST_VIEW_FILE" >/dev/null 2>&1; then
        echo "✅ Swift语法检查通过"
    else
        echo "⚠️ Swift语法检查发现问题，但可能是由于依赖关系"
    fi
else
    echo "ℹ️ 未找到swiftc，跳过语法检查"
fi

echo "🎉 curveColor方法重复声明修复完成！"
echo ""
echo "📋 修复内容："
echo "  - 移除了重复的curveColor方法声明"
echo "  - 保留了第一个完整的curveColor方法实现"
echo "  - 确保了方法的唯一性"
echo ""
echo "💾 备份文件: ${ADJUST_VIEW_FILE}.backup.redeclaration"
echo "🔄 请重新编译项目验证修复效果"