import UIKit

// MARK: - 自定义水印10
/// 自定义水印10 - 上下左边框宽度一致，右边框宽度较宽
class CustomWatermarkStyle10: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左边框宽度
    private let leftBorderWidth: CGFloat
    
    /// 右边框宽度（较宽）
    private let rightBorderWidth: CGFloat
    
    /// 宽边框宽度
    private let wideBorderWidth: CGFloat
    
    /// 标准边框宽度
    private let normalBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledFrameWrapper: UIView? // 用于存储最外层的已缩放框架视图
    
    // --- 水印元素相关 ---
    private weak var wideItemsContainer: UIView? // 宽边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - normalBorderWidth: 上、下、左边框宽度
    ///   - wideBorderWidth: 宽边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(normalBorderWidth: CGFloat, 
         wideBorderWidth: CGFloat, // From WatermarkConstants.Custom10.rightBorderScreenHeightFactor * screenHeight
         borderColor: UIColor = .white) {
        self.normalBorderWidth = normalBorderWidth
        self.wideBorderWidth = wideBorderWidth
        self.topBorderWidth = normalBorderWidth
        self.bottomBorderWidth = normalBorderWidth
        self.leftBorderWidth = normalBorderWidth
        self.rightBorderWidth = wideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle10: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印10到预览视图
    func apply(to previewContainer: UIView) {
        if scaledFrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle10: apply - 无法找到实际内容视图。")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false
        // 获取宽边框位置设置
        let isRightWideBorderEnabled = watermarkSettings?.isRightWideBorderEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + self.normalBorderWidth + self.wideBorderWidth
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle10: 无法获取视图截图，使用默认背景")
                wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        
        // 根据宽边框位置设置，调整内容视图的位置和创建宽边框容器
        let itemsContainer: UIView
        
        if !isRightWideBorderEnabled {
            // 宽边框在右侧（默认）
            contentView.frame = CGRect(x: self.normalBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
            
            // 创建右侧宽边框元素容器视图
            let containerFrame = CGRect(
                x: self.normalBorderWidth + contentOriginalSize.width,
                y: self.topBorderWidth,
                width: self.wideBorderWidth,
                height: contentOriginalSize.height
            )
            
            itemsContainer = UIView(frame: containerFrame)
        } else {
            // 宽边框在左侧（当"左侧"开关开启时）
            contentView.frame = CGRect(x: self.wideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
            
            // 创建左侧宽边框元素容器视图
            let containerFrame = CGRect(
                x: 0,
                y: self.topBorderWidth,
                width: self.wideBorderWidth,
                height: contentOriginalSize.height
            )
            
            itemsContainer = UIView(frame: containerFrame)
        }
        
        wrapper.addSubview(contentView)
        
        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle10")

        // 配置宽边框容器
        itemsContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了背景
        wrapper.addSubview(itemsContainer)
        self.wideItemsContainer = itemsContainer
        
        // 添加水印元素到宽边框容器
        addWatermarkElementsToContainer(itemsContainer)

        self.scaledFrameWrapper = wrapper
        previewContainer.addSubview(wrapper)

        // --- 将 `wrapper` (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard wrapper.bounds.width > 0, wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle10: wrapper 尺寸为0。")
            wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / wrapper.bounds.width
        let scaleY = containerSize.height / wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle10: 已应用自定义水印10。最终缩放: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到宽边框容器
    private func addWatermarkElementsToContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // Main text (from signature) enabled and not empty
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 统计启用的元素数量
        var currentEnabledElements = 0
        if showLogo { currentEnabledElements += 1 }
        if showText { currentEnabledElements += 1 }
        if showPreference { currentEnabledElements += 1 }
        let enabledElementsCount = currentEnabledElements // Use a new local constant for clarity
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 添加Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // MODIFIED: This creates the main text element, which is now driven by signature settings
        if showText {
            createMainTextAsSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 添加偏好（如果需要）
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用LogoCreator创建Logo
        let factors = (
            single: WatermarkConstants.Custom10.logoSizeSingleElementFactor,
            two: WatermarkConstants.Custom10.logoSizeTwoElementsFactor,
            three: WatermarkConstants.Custom10.logoSizeThreeElementsFactor
        )
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            enabledElementsCount: enabledElementsCount,
            customSizeFactors: factors
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
    }
    
    /// 创建文字标签 (MODIFIED: Now creates main text based on Signature settings)
    private func createMainTextAsSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用TextLabelCreator创建文本标签，使用文字专用的字体大小因子
        let factors: (single: CGFloat, two: CGFloat, three: CGFloat)
        switch enabledElementsCount {
        case 1:
            factors = (single: WatermarkConstants.Custom10.textFontSingleElementFactor, two: 0, three: 0) // placeholder, not used
        case 2:
            factors = (single: 0, two: WatermarkConstants.Custom10.textFontTwoElementsFactor, three: 0) // placeholder, not used
        default: // 3 or more, or if logic changes
            factors = (single: 0, two: 0, three: WatermarkConstants.Custom10.textFontThreeElementsFactor) // placeholder, not used
        }
        
        var fontSize: CGFloat
        if enabledElementsCount == 1 {
            fontSize = UIScreen.main.bounds.height * factors.single
        } else if enabledElementsCount == 2 {
            fontSize = UIScreen.main.bounds.height * factors.two
        } else {
            fontSize = UIScreen.main.bounds.height * factors.three
        }
        
        // 获取缩放比例
        let scaleFactor = CGFloat(settings.preferenceScaleFactor)
        let scaledFontSize = fontSize * scaleFactor

        // 应用署名大小乘数
        let finalFontSize = scaledFontSize * CGFloat(settings.signatureFontSizeMultiplier)
        
        // MODIFIED: Create label using signature text and apply font style as signature
        let label = UILabel()
        label.text = settings.watermarkSignature // Use signature text
        label.textAlignment = .center
        label.numberOfLines = 0 // Allow multiple lines for signature if needed
        
        // Apply font style, marking as signature for correct thickness application
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: finalFontSize, styleIdentifier: "Custom10.MainTextAsSignature", isSignature: true)
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom10.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label // Still store in textLabel, but it's functionally the signature
        print("✅ CustomWatermarkStyle10: 主文本(源自署名) 大小: enabled=\(enabledElementsCount), fontSize=\(fontSize), scaledFontSize=\(scaledFontSize), finalFontSize=\(finalFontSize)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用PreferenceLabelCreator创建标签，使用偏好专用的字体大小因子
        let factors = (
            single: WatermarkConstants.Custom10.preferenceFontSingleElementFactor, 
            two: WatermarkConstants.Custom10.preferenceFontTwoElementsFactor, 
            three: WatermarkConstants.Custom10.preferenceFontThreeElementsFactor
        )
        
        // 获取缩放比例
        let scaleFactor = CGFloat(settings.preferenceScaleFactor)
        
        // 检查偏好选项类型，对于"参数"类型使用特殊的展示方式
        if settings.preferenceOption == "参数" || settings.selectedPreferences.contains("参数") {
            // 创建一个直接继承自UILabel的自定义标签，这样在布局系统中它的行为会与普通标签一致
            let customParamLabel = ParamStyleLabel(frame: .zero)
            
            // 配置自定义参数标签
            customParamLabel.configure(
                with: settings,
                enabledElementsCount: enabledElementsCount,
                customFontSizeFactors: factors
            )
            
            // 设置尺寸
            let maxWidth = container.bounds.width * WatermarkConstants.Custom10.labelMaxWidthContainerWidthFactor
            let size = customParamLabel.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            customParamLabel.frame.size = size
            
            // 添加到容器中
            container.addSubview(customParamLabel)
            self.preferenceLabel = customParamLabel
            
        } else {
            // 其他类型的偏好选项使用常规方式创建
            let standardLabel = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption, 
                with: settings, 
                enabledElementsCount: enabledElementsCount,
                customFontSizeFactors: factors
            )
            
            // 应用缩放到字体大小
            let label = standardLabel
            if let font = standardLabel.font {
                let scaledFontSize = font.pointSize * scaleFactor
                label.font = font.withSize(scaledFontSize)
            }
            
            // 设置尺寸
            let maxWidth = container.bounds.width * WatermarkConstants.Custom10.labelMaxWidthContainerWidthFactor
            let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            label.frame.size = size
            
            container.addSubview(label)
            self.preferenceLabel = label
        }
    }
    
    /// 自定义参数样式标签 - 直接继承自UILabel以确保与布局系统兼容
    private class ParamStyleLabel: UILabel {
        private var paramComponents: [(value: String, unit: String)] = []
        private var actualFont: UIFont?
        private var actualColor: UIColor?
        private var cornerRadius: CGFloat = 3
        private var scaleFactor: CGFloat = 1.0 // 新增缩放比例属性
        private var boxLayers: [CAShapeLayer] = [] // 存储矩形框图层
        private var textLayers: [CATextLayer] = [] // 存储文本图层
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            isUserInteractionEnabled = false
            backgroundColor = .clear
        }
        
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        func configure(with settings: WatermarkSettings, enabledElementsCount: Int, customFontSizeFactors: (single: CGFloat, two: CGFloat, three: CGFloat)?) {
            // 获取参数文本
            let parametersText = PreferenceUtils.getPreferenceText(for: "参数", with: settings)
            print("⚠️ 参数文本: \(parametersText)")
            
            // 解析参数文本
            self.paramComponents = parseParametersText(parametersText)
            print("⚠️ 解析后的参数组件: \(self.paramComponents)")
            
            // 获取缩放比例
            self.scaleFactor = CGFloat(settings.preferenceScaleFactor)
            
            // 计算字体大小，并应用缩放
            let baseFontSize = PreferenceUtils.getFontSize(
                enabledElementsCount: enabledElementsCount,
                customFontSizeFactors: customFontSizeFactors
            )
            let scaledFontSize = baseFontSize * scaleFactor
            
            // 获取字体
            self.actualFont = getFont(with: settings, fontSize: scaledFontSize)
            
            // 获取颜色
            self.actualColor = UIColor(
                red: CGFloat(settings.fontColorRed),
                green: CGFloat(settings.fontColorGreen),
                blue: CGFloat(settings.fontColorBlue),
                alpha: CGFloat(settings.fontColorAlpha)
            )
            
            // 设置圆角大小 - 相对于字体大小而非屏幕高度
            self.cornerRadius = scaledFontSize * 0.25
            
            // 清除之前的图层
            clearLayers()
            
            // 使用CAShapeLayer创建UI
            self.setNeedsLayout()
        }
        
        // 清除之前的所有图层
        private func clearLayers() {
            for layer in boxLayers {
                layer.removeFromSuperlayer()
            }
            boxLayers.removeAll()
            
            for layer in textLayers {
                layer.removeFromSuperlayer()
            }
            textLayers.removeAll()
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            updateLayers()
        }
        
        // 更新所有图层，替代原来的draw方法
        private func updateLayers() {
            // 先清除之前的图层
            clearLayers()
            
            guard let font = actualFont,
                  let color = actualColor,
                  !paramComponents.isEmpty else {
                return
            }
            
            // 计算布局参数 - 更新间距计算以提高可读性，并应用缩放
            let spacing = font.lineHeight * 0.6 * scaleFactor // 参数之间的间距，应用缩放
            let boxHeight = font.lineHeight * 1.5 * scaleFactor // 矩形框高度，应用缩放
            let unitHeight = font.lineHeight * 0.8 * scaleFactor // 单位标签高度，应用缩放
            let paramHeight = boxHeight + unitHeight + cornerRadius // 每个参数包含值+单位的高度
            
            let totalHeight = CGFloat(paramComponents.count) * paramHeight + spacing * CGFloat(max(0, paramComponents.count - 1))
            // 矩形框宽度略小于整体宽度，应用缩放
            let boxWidth = bounds.width * 0.85
            
            // 计算起始Y位置，添加小偏移确保可见性
            var y = (bounds.height - totalHeight) / 2
            
            // 创建每个参数的图层
            for component in paramComponents {
                // 创建矩形框图层
                let boxRect = CGRect(x: bounds.midX - boxWidth/2, y: y, width: boxWidth, height: boxHeight)
                let boxLayer = CAShapeLayer()
                let path = UIBezierPath(roundedRect: boxRect, cornerRadius: cornerRadius).cgPath
                
                // 配置图层
                boxLayer.path = path
                boxLayer.fillColor = UIColor.clear.cgColor // 无填充
                
                // 应用与水印13/14相同的颜色逻辑
                let settings = WatermarkSettingsManager.shared.getSettings()
                let specialColor = WatermarkStyleUtils.getSpecialElementColor(with: settings, borderColor: UIColor.white) // 默认用白色边框计算
                boxLayer.strokeColor = specialColor.cgColor // 使用特殊元素颜色
                
                boxLayer.lineWidth = 1.0
                boxLayer.contentsScale = UIScreen.main.scale // 重要：确保在高分辨率屏幕上清晰显示
                
                // 添加到视图
                layer.addSublayer(boxLayer)
                boxLayers.append(boxLayer)
                
                // 创建参数值文本图层
                let valueTextLayer = CATextLayer()
                valueTextLayer.string = component.value
                valueTextLayer.font = CGFont(font.fontName as CFString)
                valueTextLayer.fontSize = font.pointSize
                valueTextLayer.foregroundColor = color.cgColor
                valueTextLayer.alignmentMode = .center
                valueTextLayer.contentsScale = UIScreen.main.scale // 重要：确保在高分辨率屏幕上清晰显示
                
                // 计算文本尺寸和位置
                let valueFontDescriptor = font.fontDescriptor.withSymbolicTraits(.traitBold) ?? font.fontDescriptor
                let valueFont = UIFont(descriptor: valueFontDescriptor, size: font.pointSize)
                let valueAttributes: [NSAttributedString.Key: Any] = [
                    .font: valueFont
                ]
                let valueSize = component.value.size(withAttributes: valueAttributes)
                
                valueTextLayer.frame = CGRect(
                    x: bounds.midX - valueSize.width/2,
                    y: y + (boxHeight - valueSize.height)/2,
                    width: valueSize.width,
                    height: valueSize.height
                )
                
                // 添加到视图
                layer.addSublayer(valueTextLayer)
                textLayers.append(valueTextLayer)
                
                // 创建单位文本图层
                let unitTextLayer = CATextLayer()
                unitTextLayer.string = component.unit
                unitTextLayer.font = CGFont(font.fontName as CFString)
                unitTextLayer.fontSize = font.pointSize * 0.75 // 单位文本稍小
                
                // 应用与水印13/14相同的颜色逻辑 - 使用特殊元素颜色
                let unitTextColor = WatermarkStyleUtils.getSpecialElementColor(with: settings, borderColor: UIColor.white) // 默认用白色边框计算
                unitTextLayer.foregroundColor = unitTextColor.cgColor
                
                unitTextLayer.alignmentMode = .center
                unitTextLayer.contentsScale = UIScreen.main.scale // 重要：确保在高分辨率屏幕上清晰显示
                
                // 计算单位文本尺寸和位置
                let unitFont = font.withSize(font.pointSize * 0.75)
                let unitAttributes: [NSAttributedString.Key: Any] = [
                    .font: unitFont
                ]
                let unitSize = component.unit.size(withAttributes: unitAttributes)
                
                unitTextLayer.frame = CGRect(
                    x: bounds.midX - unitSize.width/2,
                    y: y + boxHeight + cornerRadius * 0.5,
                    width: unitSize.width,
                    height: unitSize.height
                )
                
                // 添加到视图
                layer.addSublayer(unitTextLayer)
                textLayers.append(unitTextLayer)
                
                // 移动到下一个参数的位置
                y += paramHeight + spacing
            }
        }
        
        override func sizeThatFits(_ size: CGSize) -> CGSize {
            guard let font = actualFont, !paramComponents.isEmpty else {
                return super.sizeThatFits(size)
            }
            
            // 调整参数之间的间距，根据参数数量和字体大小动态计算，并应用缩放
            let spacing = font.lineHeight * 0.6 * scaleFactor // 参数之间的间距，应用缩放
            let boxHeight = font.lineHeight * 1.5 * scaleFactor // 矩形框高度，应用缩放 
            let unitHeight = font.lineHeight * 0.8 * scaleFactor // 单位标签高度，应用缩放
            let paramHeight = boxHeight + unitHeight + cornerRadius // 每个参数包含值+单位的高度
            
            let totalHeight = CGFloat(paramComponents.count) * paramHeight + spacing * CGFloat(max(0, paramComponents.count - 1))
            // 设置宽度与字体大小成比例，应用缩放
            let maxParamWidth = font.pointSize * 5.5 * scaleFactor
            
            return CGSize(width: maxParamWidth, height: totalHeight)
        }
        
        // 获取字体
        private func getFont(with settings: WatermarkSettings, fontSize: CGFloat) -> UIFont {
            let fontThickness = settings.fontThicknessMultiplier
            var fontWeight = UIFont.Weight.regular
            
            // 根据字体粗细设置字重
            if fontThickness < 0.25 {
                fontWeight = .light
            } else if fontThickness < 0.5 {
                fontWeight = .regular
            } else if fontThickness < 0.75 {
                fontWeight = .semibold
            } else {
                fontWeight = .bold
            }
            
            // 根据用户选择的字体名称确定实际字体
            let fontName = settings.selectedFontName
            
            // 处理HarmonyOS_Sans_SC字体
            if fontName == "HarmonyOS_Sans_SC" {
                // 直接根据选择的字重使用对应的字体文件
                let fontWeight = settings.selectedFontWeight
                var harmonyFontName = "HarmonyOS_Sans_SC_Regular" // 默认使用Regular
                
                switch fontWeight {
                    case "Thin": harmonyFontName = "HarmonyOS_Sans_SC_Thin"
                    case "Light": harmonyFontName = "HarmonyOS_Sans_SC_Light"
                    case "Regular": harmonyFontName = "HarmonyOS_Sans_SC_Regular"
                    case "Medium": harmonyFontName = "HarmonyOS_Sans_SC_Medium"
                    case "Bold": harmonyFontName = "HarmonyOS_Sans_SC_Bold"
                    case "Black": harmonyFontName = "HarmonyOS_Sans_SC_Black"
                    default: harmonyFontName = "HarmonyOS_Sans_SC_Regular"
                }
                
                // 尝试应用对应的HarmonyOS字体
                if let harmonyFont = UIFont(name: harmonyFontName, size: fontSize) {
                    // 处理英文和数字部分使用SF字体
                    self.font = harmonyFont
                    WatermarkStyleUtils.configureTextForMixedLanguage(label: self, fontSize: fontSize, fontThickness: fontThickness)
                    return harmonyFont
                }
            } else if fontName == "PingFang-SC" {
                // 直接根据选择的字重使用对应的字体文件
                let fontWeight = settings.selectedFontWeight
                var pingFangFontName = "PingFangSC-Regular" // 默认使用Regular
                
                switch fontWeight {
                    case "Ultralight": pingFangFontName = "PingFangSC-Ultralight"
                    case "Thin": pingFangFontName = "PingFangSC-Thin"
                    case "Light": pingFangFontName = "PingFangSC-Light"
                    case "Regular": pingFangFontName = "PingFangSC-Regular"
                    case "Medium": pingFangFontName = "PingFangSC-Medium"
                    case "Semibold": pingFangFontName = "PingFangSC-Semibold"
                    default: pingFangFontName = "PingFangSC-Regular"
                }
                
                // 尝试应用对应的PingFang-SC字体
                if let pingFangFont = UIFont(name: pingFangFontName, size: fontSize) {
                    // 处理英文和数字部分使用SF字体
                    self.font = pingFangFont
                    WatermarkStyleUtils.configureTextForMixedLanguage(label: self, fontSize: fontSize, fontThickness: fontThickness)
                    return pingFangFont
                }
            } else if fontName == "SourceHanSansSC" {
                // 直接根据选择的字重使用对应的字体文件
                let fontWeight = settings.selectedFontWeight
                var sourceHanFontName = "SourceHanSansSC-Regular" // 默认使用Regular
                
                switch fontWeight {
                    case "ExtraLight": sourceHanFontName = "SourceHanSansSC-ExtraLight"
                    case "Light": sourceHanFontName = "SourceHanSansSC-Light"
                    case "Normal": sourceHanFontName = "SourceHanSansSC-Normal"
                    case "Regular": sourceHanFontName = "SourceHanSansSC-Regular"
                    case "Medium": sourceHanFontName = "SourceHanSansSC-Medium"
                    case "Bold": sourceHanFontName = "SourceHanSansSC-Bold"
                    case "Heavy": sourceHanFontName = "SourceHanSansSC-Heavy"
                    default: sourceHanFontName = "SourceHanSansSC-Regular"
                }
                
                // 尝试应用对应的SourceHanSansSC字体
                if let sourceHanFont = UIFont(name: sourceHanFontName, size: fontSize) {
                    // 处理英文和数字部分使用SF字体
                    self.font = sourceHanFont
                    WatermarkStyleUtils.configureTextForMixedLanguage(label: self, fontSize: fontSize, fontThickness: fontThickness)
                    return sourceHanFont
                }
            } else if fontName == "HONORSansCN" {
                // 使用字体粗细名称确定应使用哪种HONORSansCN变体
                var honorFontName: String
                
                // 获取选择的字体粗细名称
                let selectedWeight = settings.selectedFontWeight
                
                // 根据selectedWeight选择对应的字体
                if selectedWeight.isEmpty {
                    honorFontName = "HONORSansCN-Regular" // 默认使用Regular
                } else if selectedWeight == "SemiBold" {
                    honorFontName = "HONORSansCN-DemiBold" // 修正为正确的文件名
                } else if selectedWeight == "Black" {
                    honorFontName = "HONORSansCN-Heavy" // 修正为正确的文件名
                } else {
                    honorFontName = "HONORSansCN-" + selectedWeight
                }
                
                // 尝试应用对应的HONORSansCN字体
                if let honorFont = UIFont(name: honorFontName, size: fontSize) {
                    // 处理英文和数字部分使用SF字体
                    self.font = honorFont
                    WatermarkStyleUtils.configureTextForMixedLanguage(label: self, fontSize: fontSize, fontThickness: fontThickness)
                    return honorFont
                }
            }
            
            return UIFont.systemFont(ofSize: fontSize, weight: fontWeight)
        }
        
        // 解析参数文本
        private func parseParametersText(_ text: String) -> [(value: String, unit: String)] {
            // 原有的解析方法保持不变
            var components = [(value: String, unit: String)]()
            
            // 默认参数：焦距、光圈、快门速度、ISO
            if text.contains("mm") || text.contains("f/") || text.contains("s") || text.contains("ISO") {
                // 处理常见的相机参数格式
                
                // 尝试提取焦距（格式为"50mm"）
                if let mmRange = text.range(of: "[0-9]+(\\.[0-9]+)?\\s*mm", options: .regularExpression) {
                    let mmString = String(text[mmRange])
                    if let mmIndex = mmString.range(of: "mm", options: .backwards)?.lowerBound {
                        let value = mmString[..<mmIndex].trimmingCharacters(in: .whitespacesAndNewlines)
                        components.append((value: value, unit: "mm"))
                    }
                }
                
                // 光圈 (f/2.8)
                if let apertureRange = text.range(of: "f/[0-9]+(\\.[0-9]+)?", options: .regularExpression) {
                    let apertureString = String(text[apertureRange])
                    if let fIndex = apertureString.firstIndex(of: "/") {
                        let value = String(apertureString[apertureString.index(after: fIndex)...])
                        components.append((value: value, unit: "F"))
                    }
                }
                
                // 快门速度 (1/500s)
                if let shutterRange = text.range(of: "[0-9]+/[0-9]+s", options: .regularExpression) {
                    let shutterString = String(text[shutterRange])
                    if let sIndex = shutterString.lastIndex(of: "s") {
                        let valueStr = String(shutterString[..<sIndex])
                        components.append((value: valueStr, unit: "S"))
                    }
                } else if let shutterRange = text.range(of: "[0-9]+(\\.[0-9]+)?s", options: .regularExpression) {
                    let shutterString = String(text[shutterRange])
                    if let sIndex = shutterString.lastIndex(of: "s") {
                        let valueStr = String(shutterString[..<sIndex])
                        components.append((value: valueStr, unit: "S"))
                    }
                }
                
                // ISO (ISO 100)
                if let isoRange = text.range(of: "ISO\\s*[0-9]+", options: .regularExpression) {
                    let isoString = String(text[isoRange])
                    if let isoValue = isoString.components(separatedBy: CharacterSet.decimalDigits.inverted).filter({ !$0.isEmpty }).first {
                        components.append((value: isoValue, unit: "ISO"))
                    }
                }
            } else {
                // 如果不是标准格式，就以空格分割，尝试解析每个部分
                let parts = text.components(separatedBy: " ").filter { !$0.isEmpty }
                
                for part in parts {
                    if part.contains("f/") {
                        if let fIndex = part.firstIndex(of: "/") {
                            let value = String(part[part.index(after: fIndex)...])
                            components.append((value: value, unit: "F"))
                        }
                    } else if part.hasSuffix("s") && (part.contains("/") || Double(part.dropLast()) != nil) {
                        let value = String(part.dropLast())
                        components.append((value: value, unit: "S"))
                    } else if part.hasSuffix("mm") {
                        let value = String(part.dropLast(2))
                        components.append((value: value, unit: "mm"))
                    } else if part == "ISO" && parts.count > parts.firstIndex(of: part)! + 1 {
                        let nextIndex = parts.firstIndex(of: part)! + 1
                        if nextIndex < parts.count {
                            let value = parts[nextIndex]
                            components.append((value: value, unit: "ISO"))
                        }
                    } else if part.hasPrefix("ISO") {
                        let value = part.dropFirst(3)
                        if !value.isEmpty {
                            components.append((value: String(value), unit: "ISO"))
                        }
                    }
                }
            }
            
            // 如果没有解析出参数，提供默认值
            if components.isEmpty {
                components = [
                    (value: "50", unit: "mm"),
                    (value: "1.8", unit: "F"),
                    (value: "1/125", unit: "S"),
                    (value: "100", unit: "ISO")
                ]
            }
            
            // 确保mm组件(焦距)始终在第一位
            if !components.isEmpty {
                let mmComponents = components.filter { $0.unit == "mm" }
                let otherComponents = components.filter { $0.unit != "mm" }
                
                if !mmComponents.isEmpty {
                    components = mmComponents + otherComponents
                }
            }
            
            return components
        }
    }
    
    /// 布局水印元素
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int) {
        let centerX = container.bounds.width / 2
        let centerY = container.bounds.height / 2
        
        // 获取preferenceScaleFactor用于等比例缩放
        let scaleFactor = CGFloat(watermarkSettings?.preferenceScaleFactor ?? 1.0)
        
        // 根据元素的数量，安排垂直布局
        switch enabledCount {
        case 1:
            // 单个元素：垂直居中
            if let logo = self.logoView {
                logo.center = CGPoint(x: centerX, y: centerY)
            } else if let mainText = self.textLabel {
                mainText.sizeToFit() // 确保标签的大小正确反映其内容
                mainText.center = CGPoint(x: centerX, y: centerY)
            } else if let preference = self.preferenceLabel {
                preference.sizeToFit()
                preference.center = CGPoint(x: centerX, y: centerY)
            }
            
        case 2:
            // 两个元素：垂直排列，间隔开
            // 获取元素高度
            var preferenceHeight: CGFloat = 0
            var textHeight: CGFloat = 0
            var logoHeight: CGFloat = 0
            
            if let preference = self.preferenceLabel {
                preference.sizeToFit()
                preferenceHeight = preference.bounds.height
            }
            
            if let text = self.textLabel {
                text.sizeToFit()
                textHeight = text.bounds.height
            }
            
            if let logo = self.logoView {
                logoHeight = logo.bounds.height
            }
            
            // 使用非线性缩放计算间距
            // 基础间距
            let baseVerticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom10.baseLayout2ElementsVerticalSpacingScreenHeightFactor
            // 非线性缩放后的间距
            let verticalSpacing = WatermarkStyleUtils.calculateLinearSpacing(baseSpacing: baseVerticalSpacing, scaleFactor: scaleFactor)
            
            if let preference = self.preferenceLabel, let text = self.textLabel {
                // 偏好和文字 - 垂直布局
                // 偏好放在上面
                preference.sizeToFit()
                let totalHeight = preferenceHeight + verticalSpacing + textHeight
                let preferenceY = centerY - totalHeight/2 + preferenceHeight/2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                
                // 文字放在下面
                text.sizeToFit()
                let textY = preferenceY + preferenceHeight/2 + verticalSpacing + textHeight/2
                text.center = CGPoint(x: centerX, y: textY)
                print("✅ 水印10两元素垂直布局: 偏好在上(y=\(preferenceY)), 文字在下(y=\(textY))")
            } else if let preference = self.preferenceLabel, let logo = self.logoView {
                // 偏好和Logo - 垂直布局
                // 偏好放在上面
                preference.sizeToFit()
                let totalHeight = preferenceHeight + verticalSpacing + logoHeight
                let preferenceY = centerY - totalHeight/2 + preferenceHeight/2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                
                // Logo放在下面
                let logoY = preferenceY + preferenceHeight/2 + verticalSpacing + logoHeight/2
                logo.center = CGPoint(x: centerX, y: logoY)
                print("✅ 水印10两元素垂直布局: 偏好在上(y=\(preferenceY)), LOGO在下(y=\(logoY))")
            } else if let text = self.textLabel, let logo = self.logoView {
                // 文字和Logo - 垂直布局
                // 修改：文字放在上面，Logo放在下面，使布局一致
                text.sizeToFit()
                // 使用特定的基础间距和线性缩放
                let baseSpecialSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom10.baseLogoTextSpecialSpacingScreenHeightFactor
                let specialSpacing = WatermarkStyleUtils.calculateLinearSpacing(baseSpacing: baseSpecialSpacing, scaleFactor: scaleFactor)
                
                let totalHeight = logoHeight + specialSpacing + textHeight
                
                // 文字放在上面
                let textY = centerY - totalHeight/2 + textHeight/2
                text.center = CGPoint(x: centerX, y: textY)
                
                // Logo放在下面
                let logoY = textY + textHeight/2 + specialSpacing + logoHeight/2
                logo.center = CGPoint(x: centerX, y: logoY)
                print("✅ 水印10两元素垂直布局: 文字在上(y=\(textY)), LOGO在下(y=\(logoY))")
            }
            
        case 3:
            // 三个元素：垂直排列，等间距
            // 获取元素高度
            var preferenceHeight: CGFloat = 0
            var textHeight: CGFloat = 0
            var logoHeight: CGFloat = 0
            
            if let preference = self.preferenceLabel {
                preference.sizeToFit()
                preferenceHeight = preference.bounds.height
            }
            
            if let text = self.textLabel {
                text.sizeToFit()
                textHeight = text.bounds.height
            }
            
            if let logo = self.logoView {
                logoHeight = logo.bounds.height
            }
            
            // 使用非线性缩放计算间距
            let basePreferenceTextSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom10.basePreferenceTextSpacingScreenHeightFactor
            let baseTextLogoSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom10.baseTextLogoSpacingScreenHeightFactor
            
            let preferenceTextSpacing = WatermarkStyleUtils.calculateLinearSpacing(baseSpacing: basePreferenceTextSpacing, scaleFactor: scaleFactor)
            let textLogoSpacing = WatermarkStyleUtils.calculateLinearSpacing(baseSpacing: baseTextLogoSpacing, scaleFactor: scaleFactor)
            
            if let preference = self.preferenceLabel, let text = self.textLabel, let logo = self.logoView {
                let totalHeight = preferenceHeight + preferenceTextSpacing + textHeight + textLogoSpacing + logoHeight
                
                // Logo放在下方
                let logoY = centerY + totalHeight/2 - logoHeight/2
                logo.center = CGPoint(x: centerX, y: logoY)
                
                // 文字放在中间
                let textY = logoY - logoHeight/2 - textLogoSpacing - textHeight/2
                text.center = CGPoint(x: centerX, y: textY)
                
                // 偏好放在上方
                let preferenceY = textY - textHeight/2 - preferenceTextSpacing - preferenceHeight/2
                preference.center = CGPoint(x: centerX, y: preferenceY)
                
                print("✅ 水印10三元素垂直布局: 偏好在上(y=\(preferenceY)), 文字在中(y=\(textY)), Logo在下(y=\(logoY))")
            }
            
        default:
            // 其他情况：处理降级情况
            break
        }
        
        // 打印日志
        var logMessage = "CustomWatermarkStyle10 Layout (enabledCount: \(enabledCount), scale: \(scaleFactor)) - "
        if let logo = self.logoView { logMessage += "Logo: Y=\(logo.frame.midY) " }
        if let mainText = self.textLabel { logMessage += "MainText(Signature): Y=\(mainText.frame.midY) " } // Updated log
        if let preference = self.preferenceLabel { logMessage += "Preference: Y=\(preference.frame.midY)" }
        print(logMessage)
    }
    
    /// 恢复原始内容视图状态（私有辅助方法）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else { return }
        
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
    }
    
    /// 清理存储的状态引用（私有辅助方法）
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledFrameWrapper = nil
        self.wideItemsContainer = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.signatureLabel = nil
        self.watermarkSettings = nil
    }
    
    /// 移除自定义水印10效果
    func remove(from previewContainer: UIView) {
        guard let wrapper = self.scaledFrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            // 如果 wrapper 仍然存在于 previewContainer 中，则尝试移除它
            self.scaledFrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        // 恢复原始状态
        contentView.removeFromSuperview()
        
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        // 移除包装器和清理资源
        wrapper.removeFromSuperview()
        cleanupStoredState()
        
        print("✅ CustomWatermarkStyle10: 已移除自定义水印10。")
    }
}

// MARK: - UIFont扩展
private extension UIFont {
    func withTraits(_ traits: UIFontDescriptor.SymbolicTraits) -> UIFont {
        let descriptor = fontDescriptor.withSymbolicTraits(traits)
        return UIFont(descriptor: descriptor ?? fontDescriptor, size: 0) // size 0表示使用原始大小
    }
} 