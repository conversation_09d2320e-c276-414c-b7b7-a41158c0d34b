import UIKit

// MARK: - 自定义水印22
/// 自定义水印样式22 - 基于自定义水印17（胶片风格）复制的水印
class CustomWatermarkStyle22: WatermarkStyle {
    /// 边框宽度
    private let borderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var custom22WatermarkWrapper: UIView? // 水印包装视图
    
    // --- 水印元素相关 ---
    private weak var watermarkContentContainer: UIView? // 水印内容容器
    private weak var logoView: UIImageView? // Logo图片视图
    // private weak var textLabel: UILabel? // 水印文字标签 - 不再使用
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    
    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, borderColor: UIColor) {
        self.borderWidth = borderWidth // from WatermarkConstants.Custom22.fixedBorderScreenHeightFactor * screenHeight
        self.borderColor = borderColor // from WatermarkConstants.Custom22.borderColor
    }
    
    /// 查找实际的内容视图
    private func findActualContentView(in container: UIView) -> UIView? {
        // 如果容器是UIImageView或CameraPreviewView，则它就是内容视图
        if container is UIImageView || NSStringFromClass(type(of: container)).contains("CameraPreviewView") {
            return container
        }
        
        // 否则，查找第一个UIImageView或CameraPreviewView子视图
        for subview in container.subviews {
            if subview is UIImageView || NSStringFromClass(type(of: subview)).contains("CameraPreviewView") {
                return subview
            }
            
            // 递归查找
            if let contentView = findActualContentView(in: subview) {
                return contentView
            }
        }
        
        // 如果找不到合适的视图，返回容器本身
        return container
    }
    
    // MARK: - WatermarkStyle Protocol
    func apply(to previewContainer: UIView) {
        if custom22WatermarkWrapper != nil {
            remove(from: previewContainer) // 先移除旧的
        }
        
        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle22: apply - 无法找到实际内容视图")
            return
        }
        
        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        
        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }
        
        // --- 创建水印效果 ---
        let contentOriginalSize = contentView.bounds.size
        
        // 创建一个包装视图，大小与内容视图相同（不添加边框）
        let wrapperView = UIView(frame: CGRect(origin: .zero, size: contentOriginalSize))
        wrapperView.clipsToBounds = true
        
        // 保持内容视图的原始尺寸和位置
        contentView.transform = .identity
        contentView.frame = CGRect(x: 0, y: 0, width: contentOriginalSize.width, height: contentOriginalSize.height)
        
        // 添加内容视图到包装视图
        wrapperView.addSubview(contentView)
        
        // 创建水印内容容器
        let watermarkContainer = UIView(frame: wrapperView.bounds)
        watermarkContainer.backgroundColor = UIColor.clear // 透明背景
        watermarkContainer.isUserInteractionEnabled = false // 禁用用户交互
        wrapperView.addSubview(watermarkContainer)
        self.watermarkContentContainer = watermarkContainer
        
        // 添加水印元素到容器
        addWatermarkElements(to: watermarkContainer)
        
        // 保存包装视图引用
        self.custom22WatermarkWrapper = wrapperView
        
        // 添加包装视图到预览容器
        previewContainer.addSubview(wrapperView)
        
        // 计算并应用缩放以适应预览容器
        let containerSize = previewContainer.bounds.size
        guard wrapperView.bounds.width > 0, wrapperView.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle22: wrapperView 尺寸为0")
            wrapperView.removeFromSuperview()
            return
        }
        
        let scaleX = containerSize.width / wrapperView.bounds.width
        let scaleY = containerSize.height / wrapperView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)
        
        wrapperView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapperView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle22: 已应用胶片风格水印，缩放比例: \(finalScaleFactor)")
    }
    
    func remove(from previewContainer: UIView) {
        print("ℹ️ 移除自定义水印22样式")
        
        // 移除水印包装视图
        self.custom22WatermarkWrapper?.removeFromSuperview()
        
        // 恢复内容视图的原始状态
        restoreOriginalContentViewState()
        
        // 清理保存的资源
        cleanupStoredState()
    }
    
    /// 添加水印元素到容器
    private func addWatermarkElements(to container: UIView) {
        // 确保有水印设置
        guard let settings = self.watermarkSettings else {
            print("⚠️ CustomWatermarkStyle22: 无法添加水印元素，缺少设置")
            return
        }
        
        // 确定是否显示各个元素
        let showLogo = !settings.selectedLogo.isEmpty
        let showLogo2 = !settings.selectedLogo2.isEmpty
        // 不再使用文字功能
        // let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        // 自定义水印22不再显示偏好选项
        
        // 获取位置选项
        let isCenter = settings.positionOption == "中"
        
        // 创建水平堆栈视图来包含所有元素
        let contentStackView = UIStackView()
        contentStackView.axis = .horizontal // 更改为水平布局
        contentStackView.distribution = .equalSpacing
        contentStackView.alignment = .center
        
        // 根据显示的元素设置不同的间距
        contentStackView.spacing = UIScreen.main.bounds.width * WatermarkConstants.Custom22.horizontalSpacingScreenWidthFactor // 使用常量
        
        contentStackView.backgroundColor = UIColor.clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加Logo（如果需要）
        if showLogo {
            // 使用LogoCreator创建Logo，传入固定大小
            let logoSize = UIScreen.main.bounds.height * WatermarkConstants.Custom22.logoSizeScreenHeightFactor
            let logoImageView = LogoCreator.createLogo(
                with: settings,
                fixedSize: logoSize
            )
            
            // 计算调整后的Logo尺寸（应用用户设置的大小乘数）
            // 从LogoImageView获取实际大小（已应用了logoSizeMultiplier）或手动计算
            let logoHeight = logoImageView.bounds.height > 0 ? 
                            logoImageView.bounds.height : 
                            (logoSize * CGFloat(settings.logoSizeMultiplier))
            let logoWidth = logoImageView.bounds.width > 0 ?
                           logoImageView.bounds.width :
                           logoHeight
            
            // 设置约束
            logoImageView.translatesAutoresizingMaskIntoConstraints = false
            logoImageView.heightAnchor.constraint(equalToConstant: logoHeight).isActive = true
            logoImageView.widthAnchor.constraint(equalToConstant: logoWidth).isActive = true
            
            contentStackView.addArrangedSubview(logoImageView)
            self.logoView = logoImageView
        }
        
        // 添加Logo2（如果需要）
        if showLogo2 {
            // 使用LogoCreator创建Logo2，传入固定大小和特定logo名称
            let logoSize = UIScreen.main.bounds.height * WatermarkConstants.Custom22.logoSizeScreenHeightFactor
            let logo2ImageView = LogoCreator.createLogo(
                logoName: settings.selectedLogo2,
                with: settings,
                fixedSize: logoSize
            )
            
            // 计算调整后的Logo2尺寸（应用用户设置的大小乘数）
            let logo2Height = logo2ImageView.bounds.height > 0 ? 
                             logo2ImageView.bounds.height : 
                             (logoSize * CGFloat(settings.logoSizeMultiplier))
            let logo2Width = logo2ImageView.bounds.width > 0 ?
                            logo2ImageView.bounds.width :
                            logo2Height
            
            // 设置约束
            logo2ImageView.translatesAutoresizingMaskIntoConstraints = false
            logo2ImageView.heightAnchor.constraint(equalToConstant: logo2Height).isActive = true
            logo2ImageView.widthAnchor.constraint(equalToConstant: logo2Width).isActive = true
            
            contentStackView.addArrangedSubview(logo2ImageView)
        }
        
        // 不再添加文字功能
        /*
        // 创建文字的垂直堆栈视图（如果需要显示）
        if showText {
            let textStackView = UIStackView()
            textStackView.axis = .vertical
            textStackView.distribution = .equalSpacing
            textStackView.alignment = .leading // 靠左对齐
            textStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Custom22.verticalTextStackSpacingScreenHeightFactor
            textStackView.backgroundColor = UIColor.clear
            
            // 添加文字
            // 使用TextLabelCreator创建文本标签
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom22.fontSizeScreenHeightFactor
            let label = TextLabelCreator.createLabel(
                with: settings,
                fixedFontSize: fontSize
            )
            
            label.backgroundColor = UIColor.clear
            
            textStackView.addArrangedSubview(label)
            self.textLabel = label
            
            // 将文字堆栈视图添加到主堆栈视图
            contentStackView.addArrangedSubview(textStackView)
        }
        */
        
        // 根据位置调整堆栈视图的位置
        let centerX = container.bounds.width / 2
        let bottomY = container.bounds.height - UIScreen.main.bounds.height * WatermarkConstants.Custom22.elementsBottomOffsetScreenHeightFactor
        
        // 设置位置约束
        if isCenter {
            // 居中显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                contentStackView.centerYAnchor.constraint(equalTo: container.centerYAnchor)
            ])
        } else {
            // 底部显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                contentStackView.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -UIScreen.main.bounds.height * WatermarkConstants.Custom22.elementsBottomOffsetScreenHeightFactor)
            ])
        }
    }
    
    // MARK: - Helper Methods
    /// 恢复内容视图的原始状态
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 清理存储的状态
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.custom22WatermarkWrapper = nil
        self.watermarkContentContainer = nil
        self.logoView = nil
        // self.textLabel = nil  // 不再使用textLabel
        self.watermarkSettings = nil
    }
} 