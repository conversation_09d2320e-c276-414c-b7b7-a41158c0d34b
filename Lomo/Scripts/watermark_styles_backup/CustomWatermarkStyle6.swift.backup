import UIKit

/// 自定义水印6 - 上下宽边框风格
class CustomWatermarkStyle6: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledFrameWrapper: UIView? // 用于存储最外层的、已缩放的框架视图
    
    // --- 水印元素相关 ---
    private weak var contentContainer: UIView? // 内容区域元素容器
    private weak var topWideBorder: UIView? // 上边框
    private weak var bottomWideBorder: UIView? // 底边框
    private weak var leftBorder: UIView? // 左边框
    private weak var rightBorder: UIView? // 右边框
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private weak var topElementsContainer: UIView? // 上部元素容器
    private weak var bottomElementsContainer: UIView? // 底部元素容器
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    init() {
        // 使用与水印2相同的边框宽度计算方式
        self.sideBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Factory.minBorderWidthScreenHeightFactor
        // 上下宽边框使用固定的宽度，不受边框粗细影响
        self.topBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom6.topBorderScreenHeightFactor
        self.bottomBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom6.bottomBorderScreenHeightFactor
        // 默认使用白色边框
        self.borderColor = .white
    }
    
    /// 接收Factory提供的初始化参数
    init(topBorderWidth: CGFloat,
         bottomBorderWidth: CGFloat,
         sideBorderWidth: CGFloat,
         borderColor: UIColor = .white) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与PolaroidWatermarkStyle中的方法完全相同)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle6: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用水印
    func apply(to previewContainer: UIView) {
        if scaledFrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle6: apply - 无法找到实际内容视图。")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和边框的视图 ---
        let contentOriginalSize = contentView.bounds.size
        
        // 计算包装视图的大小
        let wrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        // 创建包装视图
        let wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if watermarkSettings?.isBlurBorderEnabled ?? false {
            // 模糊边框模式 - 背景透明，不创建边框子视图
            wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: wrapper, with: capturedImage, intensity: watermarkSettings?.blurIntensity ?? 0.7, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle6: 无法获取视图截图，使用默认背景")
                wrapper.backgroundColor = .clear // 保持透明，稍后会创建边框
            }
        } else {
            // 普通边框模式 - 保持透明背景，使用边框子视图
            wrapper.backgroundColor = .clear
        }
        
        // 放置内容视图
        contentView.transform = CGAffineTransform.identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        wrapper.addSubview(contentView)
        
        // 使用扩展方法应用阴影效果
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle6")

        // 创建边框 - 只在非模糊模式下创建彩色边框
        if !(watermarkSettings?.isBlurBorderEnabled ?? false) {
            createBorders(wrapper, settings: self.watermarkSettings)
        }
        
        // 创建内容容器
        let contentContainerFrame = CGRect(
            x: self.sideBorderWidth, 
            y: self.topBorderWidth, 
            width: contentOriginalSize.width, 
            height: contentOriginalSize.height
        )
        let contentContainer = UIView(frame: contentContainerFrame)
        contentContainer.backgroundColor = UIColor.clear
        wrapper.addSubview(contentContainer)
        self.contentContainer = contentContainer
        
        // 创建独立的元素容器（不依赖于边框子视图）
        // --- 上部元素容器 ---
        let topContainerFrame = CGRect(
            x: 0, 
            y: 0, 
            width: wrapperWidth, 
            height: self.topBorderWidth
        )
        let topContainer = UIView(frame: topContainerFrame)
        topContainer.backgroundColor = UIColor.clear
        wrapper.addSubview(topContainer)
        self.topElementsContainer = topContainer
        
        // --- 底部元素容器 ---
        let bottomContainerFrame = CGRect(
            x: 0, 
            y: wrapperHeight - self.bottomBorderWidth, 
            width: wrapperWidth, 
            height: self.bottomBorderWidth
        )
        let bottomContainer = UIView(frame: bottomContainerFrame)
        bottomContainer.backgroundColor = UIColor.clear
        wrapper.addSubview(bottomContainer)
        self.bottomElementsContainer = bottomContainer
        
        // 添加水印元素
        if let settings = self.watermarkSettings {
            addWatermarkElements(to: contentContainer, settings: settings)
        }

        self.scaledFrameWrapper = wrapper
        previewContainer.addSubview(wrapper)

        // --- 缩放包装视图以适应预览容器 ---
        let containerSize = previewContainer.bounds.size
        guard wrapper.bounds.width > 0, wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle6: wrapper 尺寸为0")
            wrapper.removeFromSuperview()
            return
        }

        let scaleX = containerSize.width / wrapper.bounds.width
        let scaleY = containerSize.height / wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle6: 已应用上下宽边框水印，缩放比例: \(finalScaleFactor)")
    }
    
    /// 创建边框
    private func createBorders(_ container: UIView, settings: WatermarkSettings?) {
        guard let settings = settings else { return }
        
        // 使用BorderStyleUtils获取边框颜色
        let borderColor = BorderStyleUtils.getBorderColor(from: settings)
        
        // 使用BorderStyleUtils创建边框
        let borders = BorderStyleUtils.createBorders(
            for: container,
            topWidth: self.topBorderWidth,
            bottomWidth: self.bottomBorderWidth,
            leftWidth: self.sideBorderWidth,
            rightWidth: self.sideBorderWidth,
            color: borderColor
        )
        
        // 保存边框引用
        self.topWideBorder = borders.top
        self.bottomWideBorder = borders.bottom
        self.leftBorder = borders.left
        self.rightBorder = borders.right
    }
    
    /// 添加水印元素
    private func addWatermarkElements(to container: UIView, settings: WatermarkSettings) {
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 署名启用且不为空
        
        // 检查是否有元素需要显示
        if !showLogo && !showText && !showPreference && !showSignature { return }
        
        // 上部处理：放置LOGO和署名，水平排布
        if (showLogo || showSignature) && self.topElementsContainer != nil {
            addElementsToTopContainer(settings: settings, showLogo: showLogo, showSignature: showSignature)
        }
        
        // 下部处理：放置文字和偏好，文字在上，偏好在下
        if (showText || showPreference) && self.bottomElementsContainer != nil {
            addElementsToBottomContainer(settings: settings, showText: showText, showPreference: showPreference)
        }
    }
    
    /// 向上部容器添加LOGO和署名元素
    private func addElementsToTopContainer(settings: WatermarkSettings, showLogo: Bool, showSignature: Bool) {
        guard let topContainer = self.topElementsContainer else { return }
        
        let containerWidth = topContainer.bounds.width
        let containerHeight = topContainer.bounds.height
        
        // 获取宽边框粗细设置
        let wideBorderMultiplier = CGFloat(settings.wideBorderThicknessMultiplier)
        
        // 计算动态Logo尺寸 - Logo大小随边框粗细增长50%
        let baseLogoSize = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseLogoSizeFactor
        let logoGrowth = baseLogoSize * WatermarkConstants.Custom6.logoGrowthFactor * wideBorderMultiplier
        let finalLogoSize = (baseLogoSize + logoGrowth) * CGFloat(settings.logoSizeMultiplier)
        
        // 单个元素的情况：垂直居中
        if showLogo && !showSignature {
            // 只有LOGO - 使用动态计算的尺寸
            let logoImageView = LogoUtils.createLogoImageView(
                with: settings,
                fixedSize: finalLogoSize
            )
            
            // 居中显示
            logoImageView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            
            topContainer.addSubview(logoImageView)
            self.logoView = logoImageView
        }
        else if !showLogo && showSignature {
            // 只有署名 - 使用动态计算的字体大小
            
            // 计算动态字体大小 - 字体大小随边框粗细增长25%
            let baseFontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseFontSizeFactor
            let fontGrowth = baseFontSize * WatermarkConstants.Custom6.fontGrowthFactor * wideBorderMultiplier
            var finalFontSize = baseFontSize + fontGrowth
            
            // 应用署名大小乘数
            finalFontSize *= CGFloat(settings.signatureFontSizeMultiplier)
            
            // 使用TextUtils创建署名标签
            let label = TextUtils.createSignatureLabel(with: settings, fontSize: finalFontSize)
            
            print("✅ CustomWatermarkStyle6: 署名标签 - 基础字体大小=\(baseFontSize), 增长=\(fontGrowth), 最终大小=\(finalFontSize), 大小乘数=\(settings.signatureFontSizeMultiplier)")
            
            // 计算标签尺寸
            let maxWidth = containerWidth * WatermarkConstants.Custom6.labelMaxWidthContainerWidthFactor
            let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            label.frame.size = size
            
            // 居中显示
            label.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            
            topContainer.addSubview(label)
            self.signatureLabel = label
        }
        else if showLogo && showSignature {
            // LOGO和署名同时存在
            
            // 创建LOGO - 使用动态计算的尺寸
            let logoImageView = LogoUtils.createLogoImageView(
                with: settings,
                fixedSize: finalLogoSize
            )
            
            // 创建署名 - 使用动态计算的字体大小
            let baseFontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseFontSizeFactor
            let fontGrowth = baseFontSize * WatermarkConstants.Custom6.fontGrowthFactor * wideBorderMultiplier
            var finalFontSize = baseFontSize + fontGrowth
            
            // 应用署名大小乘数
            finalFontSize *= CGFloat(settings.signatureFontSizeMultiplier)
            
            // 使用TextUtils创建署名标签
            let label = TextUtils.createSignatureLabel(with: settings, fontSize: finalFontSize)
            
            print("✅ CustomWatermarkStyle6: Logo+署名 - 基础字体大小=\(baseFontSize), 增长=\(fontGrowth), 最终大小=\(finalFontSize), 大小乘数=\(settings.signatureFontSizeMultiplier)")
            
            // 计算标签尺寸
            let maxWidth = containerWidth * 0.45
            let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            label.frame.size = size
            
            // 设置水平间距为屏幕宽度的3%
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            
            // 放置Logo和署名，水平排布，间距为3%屏幕宽度
            let totalWidth = logoImageView.frame.width + size.width + horizontalSpacing
            let startX = (containerWidth - totalWidth) / 2
            
            logoImageView.center = CGPoint(x: startX + logoImageView.frame.width/2, y: containerHeight / 2)
            label.center = CGPoint(x: startX + logoImageView.frame.width + horizontalSpacing + size.width/2, y: containerHeight / 2)
            
            topContainer.addSubview(logoImageView)
            topContainer.addSubview(label)
            self.logoView = logoImageView
            self.signatureLabel = label
        }
    }
    
    /// 向下部容器添加文字和偏好元素
    private func addElementsToBottomContainer(settings: WatermarkSettings, showText: Bool, showPreference: Bool) {
        guard let bottomContainer = self.bottomElementsContainer else { return }
        
        let containerWidth = bottomContainer.bounds.width
        let containerHeight = bottomContainer.bounds.height
        
        // 获取宽边框粗细设置
        let wideBorderMultiplier = CGFloat(settings.wideBorderThicknessMultiplier)
        
        // 计算动态字体大小 - 字体大小随边框粗细增长25%
        let baseFontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseFontSizeFactor
        let fontGrowth = baseFontSize * WatermarkConstants.Custom6.fontGrowthFactor * wideBorderMultiplier
        let finalFontSize = baseFontSize + fontGrowth
        
        // 单个元素的情况：垂直居中
        if showText && !showPreference {
            // 只有文字 - 使用动态字体大小
            let label = TextUtils.createTextLabel(
                with: settings, 
                enabledElementsCount: 1,
                fixedFontSize: finalFontSize
            )
            
            // 设置尺寸
            let maxWidth = containerWidth * WatermarkConstants.Custom6.labelMaxWidthContainerWidthFactor
            let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            label.frame.size = size
            
            // 垂直居中
            label.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            
            bottomContainer.addSubview(label)
            self.textLabel = label
        }
        else if !showText && showPreference {
            // 只有偏好 - 使用动态字体大小
            let label = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption, 
                with: settings, 
                enabledElementsCount: 1,
                fixedFontSize: finalFontSize
            )
            
            // 设置尺寸
            let maxWidth = containerWidth * WatermarkConstants.Custom6.labelMaxWidthContainerWidthFactor
            let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            label.frame.size = size
            
            // 垂直居中
            label.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            
            bottomContainer.addSubview(label)
            self.preferenceLabel = label
        }
        else if showText && showPreference {
            // 文字和偏好同时存在 - 使用动态字体大小
            
            // 创建文字标签
            let textLabel = TextUtils.createTextLabel(
                with: settings, 
                enabledElementsCount: 2,
                fixedFontSize: finalFontSize
            )
            
            // 创建偏好标签
            let preferenceLabel = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption, 
                with: settings, 
                enabledElementsCount: 2,
                fixedFontSize: finalFontSize
            )
            
            // 设置尺寸
            let maxWidth = containerWidth * WatermarkConstants.Custom6.labelMaxWidthContainerWidthFactor
            let textSize = textLabel.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            textLabel.frame.size = textSize
            
            let prefSize = preferenceLabel.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
            preferenceLabel.frame.size = prefSize
            
            // 动态计算间距：使用与水印2相同的计算方式
            let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom6.baseBottomBorderHeight
            let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
            let spacingFactor = WatermarkConstants.Custom6.spacing2ElementsBaseSize + 
                borderWidthDifference / UIScreen.main.bounds.height * WatermarkConstants.Custom6.spacing2ElementsGrowthFactor
            let elementSpacing = UIScreen.main.bounds.height * spacingFactor
            
            // 计算两个元素的总高度（包括间距）
            let totalHeight = textLabel.bounds.height + elementSpacing + preferenceLabel.bounds.height
            
            // 计算起始Y位置，使整体居中
            let startY = (containerHeight - totalHeight) / 2
            
            // 设置第一个元素位置（文字在上）
            let textY = startY + (textLabel.bounds.height / 2)
            textLabel.center = CGPoint(x: containerWidth / 2, y: textY)
            
            // 设置第二个元素位置（偏好在下）
            let preferenceY = textY + (textLabel.bounds.height / 2) + elementSpacing + (preferenceLabel.bounds.height / 2)
            preferenceLabel.center = CGPoint(x: containerWidth / 2, y: preferenceY)
            
            bottomContainer.addSubview(textLabel)
            bottomContainer.addSubview(preferenceLabel)
            self.textLabel = textLabel
            self.preferenceLabel = preferenceLabel
            
            print("✅ 水印6两元素垂直居中布局: 文字在上(y=\(textY)), 偏好在下(y=\(preferenceY)), 动态间距=\((spacingFactor*100))%, 总高度=\(totalHeight)")
        }
    }
    
    /// 移除水印
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.contentContainer?.removeFromSuperview()
        self.topElementsContainer?.removeFromSuperview()
        self.bottomElementsContainer?.removeFromSuperview()
        
        guard let wrapper = self.scaledFrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            // 如果 wrapper 仍然存在于 previewContainer 中，则尝试移除它
            self.scaledFrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        // 恢复原始状态
        contentView.removeFromSuperview()
        
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapper.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle6: 已移除上下宽边框水印。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 辅助方法：清理存储的状态变量
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledFrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.preferenceLabel = nil
        self.signatureLabel = nil
        self.contentContainer = nil
        self.topWideBorder = nil
        self.bottomWideBorder = nil
        self.leftBorder = nil
        self.rightBorder = nil
        self.watermarkSettings = nil
        self.topElementsContainer = nil
        self.bottomElementsContainer = nil
    }
} 