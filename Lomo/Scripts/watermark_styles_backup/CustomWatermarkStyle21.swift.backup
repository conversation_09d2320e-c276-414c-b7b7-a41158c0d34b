import UIKit

// MARK: - 自定义水印21
/// 自定义水印样式21 - 基于拍立得风格水印（自定义水印15）
class CustomWatermarkStyle21: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom21FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义21框架视图
    private weak var blurEffectBackgroundView: UIVisualEffectView? // 用于存储模糊效果视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var logo2View: UIImageView? // Logo2图片视图（新增）
    private weak var logo3View: UIImageView? // Logo3图片视图（新增）
    // private weak var textLabel: UILabel? // 水印文字标签 - 不再使用
    // private weak var preferenceLabel: UILabel? // 偏好选项标签 - 不再使用
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat,
         bottomBorderWidth: CGFloat,
         sideBorderWidth: CGFloat,
         borderColor: UIColor = .white) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle21: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    // 使用公共扩展中的捕获视图方法
    
    /// 应用自定义水印21样式到预览视图 - 修改模糊背景实现部分
    func apply(to previewContainer: UIView) {
        if scaledCustom21FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle21: apply - 无法找到实际内容视图。")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom21Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let custom21WrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let custom21WrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let custom21WrapperSize = CGSize(width: custom21WrapperWidth, height: custom21WrapperHeight)

        let custom21Wrapper = UIView(frame: CGRect(origin: .zero, size: custom21WrapperSize))
        custom21Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom21Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom21Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle21: 无法获取视图截图，使用默认背景")
                custom21Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 白色边框模式 - 只设置背景颜色
            custom21Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom21Wrapper.addSubview(contentView)
        
        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom21Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle21")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景
        custom21Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom21FrameWrapper = custom21Wrapper
        previewContainer.addSubview(custom21Wrapper)

        // --- 将 `custom21Wrapper` (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom21Wrapper.bounds.width > 0, custom21Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle21: custom21Wrapper 尺寸为0。")
            custom21Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom21Wrapper.bounds.width
        let scaleY = containerSize.height / custom21Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom21Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom21Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle21: 已应用自定义水印21边框。模糊效果: \(isBlurBorderEnabled ? "已启用" : "未启用")。最终缩放: \(finalScaleFactor)")
    }
    
    // 使用公共扩展中的应用模糊背景方法
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showLogo2 = !settings.selectedLogo2.isEmpty // Logo2不为空字符串表示启用（新增）
        let showLogo3 = !settings.selectedLogo3.isEmpty // Logo3不为空字符串表示启用（新增）
        
        // 统计启用的元素数量（只包括Logo、Logo2和Logo3）
        let enabledElementsCount = [showLogo, showLogo2, showLogo3].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 创建内容视图
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.alignment = .center
        
        // 使用常量替换间距设置
        contentStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Custom19.stackViewSpacingFactor
        
        contentStackView.backgroundColor = .clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 添加Logo2（如果需要）
        if showLogo2 {
            createLogo2View(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 添加Logo3（如果需要）
        if showLogo3 {
            createLogo3View(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount, showLogo: showLogo, showLogo2: showLogo2, showLogo3: showLogo3, showText: false, showPreference: false)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印21使用线性增长 - Logo也使用线性增长（50%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom19.logoSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom19.logoSingleElementGrowthFactor
        } else if enabledElementsCount == 2 {
            baseSize = WatermarkConstants.Custom19.logoTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoTwoElementsGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom19.logoThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoThreeElementsGrowthFactor
        }
        
        // 线性增长公式：logoSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom19.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        let logoSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印21 Logo动态大小: 元素数量=\(enabledElementsCount), logoSize=\((logoSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 创建Logo2视图（新增）
    private func createLogo2View(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印21使用线性增长 - Logo2也使用线性增长（50%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom19.logoSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom19.logoSingleElementGrowthFactor
        } else if enabledElementsCount == 2 {
            baseSize = WatermarkConstants.Custom19.logoTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoTwoElementsGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom19.logoThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoThreeElementsGrowthFactor
        }
        
        // 线性增长公式：logoSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom19.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        let logoSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        
        // 使用LogoCreator但传入Logo2名称
        let logoImageView = LogoCreator.createLogo(
            logoName: settings.selectedLogo2,
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logo2View = logoImageView
        
        print("✅ 水印21 Logo2动态大小: 元素数量=\(enabledElementsCount), logoSize=\((logoSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 创建Logo3视图（新增）
    private func createLogo3View(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印21使用线性增长 - Logo3也使用与Logo和Logo2相同的逻辑
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom19.logoSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom19.logoSingleElementGrowthFactor
        } else if enabledElementsCount == 2 {
            baseSize = WatermarkConstants.Custom19.logoTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoTwoElementsGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom19.logoThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom19.logoThreeElementsGrowthFactor
        }
        
        // 线性增长公式：logoSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom19.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        let logoSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        
        // 使用LogoCreator但传入Logo3名称
        let logoImageView = LogoCreator.createLogo(
            logoName: settings.selectedLogo3,
            with: settings, 
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logo3View = logoImageView
        
        print("✅ 水印21 Logo3动态大小: 元素数量=\(enabledElementsCount), logoSize=\((logoSize/UIScreen.main.bounds.height)*100)%")
    }
    
    /// 安排元素布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int, showLogo: Bool, showLogo2: Bool, showLogo3: Bool, showText: Bool, showPreference: Bool) {
        let containerWidth = container.bounds.width
        let containerHeight = container.bounds.height
        
        // 获取各个元素引用
        let logo = self.logoView
        let logo2 = self.logo2View
        let logo3 = self.logo3View
        
        // --- 各种特殊组合情况处理 ---
        
        // 情况1：三个Logo都显示
        if showLogo && showLogo2 && showLogo3 {
            // 三个Logo水平排列
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            
            // 三个Logo的总宽度
            let totalWidth = (logo?.frame.width ?? 0) + horizontalSpacing + 
                            (logo2?.frame.width ?? 0) + horizontalSpacing + 
                            (logo3?.frame.width ?? 0)
            
            let startX = (containerWidth - totalWidth) / 2
            
            // 放置Logo1
            logo?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) / 2,
                y: containerHeight / 2
            )
            
            // 放置Logo2
            logo2?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) + horizontalSpacing + (logo2?.frame.width ?? 0) / 2,
                y: containerHeight / 2
            )
            
            // 放置Logo3
            logo3?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) + horizontalSpacing + 
                   (logo2?.frame.width ?? 0) + horizontalSpacing + (logo3?.frame.width ?? 0) / 2,
                y: containerHeight / 2
            )
            
            return
        }
        
        // 情况2：只有Logo1和Logo2
        if showLogo && showLogo2 && !showLogo3 {
            // 水平放置Logo和Logo2
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            let totalWidth = (logo?.frame.width ?? 0) + horizontalSpacing + (logo2?.frame.width ?? 0)
            let startX = (containerWidth - totalWidth) / 2
                
            // 计算Logo的位置
                logo?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) / 2,
                y: containerHeight / 2
                )
                
            // 计算Logo2的位置
                logo2?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) + horizontalSpacing + (logo2?.frame.width ?? 0) / 2,
                y: containerHeight / 2
            )
            
            return
        }
        
        // 情况3：只有Logo1和Logo3
        if showLogo && !showLogo2 && showLogo3 {
            // 水平放置Logo和Logo3
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            let totalWidth = (logo?.frame.width ?? 0) + horizontalSpacing + (logo3?.frame.width ?? 0)
            let startX = (containerWidth - totalWidth) / 2
                    
            // 计算Logo的位置
            logo?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) / 2,
                y: containerHeight / 2
                    )
                    
            // 计算Logo3的位置
            logo3?.center = CGPoint(
                x: startX + (logo?.frame.width ?? 0) + horizontalSpacing + (logo3?.frame.width ?? 0) / 2,
                y: containerHeight / 2
            )
            
            return
        }
        
        // 情况4：只有Logo2和Logo3
        if !showLogo && showLogo2 && showLogo3 {
            // 水平放置Logo2和Logo3
            let horizontalSpacing = UIScreen.main.bounds.width * 0.03
            let totalWidth = (logo2?.frame.width ?? 0) + horizontalSpacing + (logo3?.frame.width ?? 0)
            let startX = (containerWidth - totalWidth) / 2
                
            // 计算Logo2的位置
            logo2?.center = CGPoint(
                x: startX + (logo2?.frame.width ?? 0) / 2,
                    y: containerHeight / 2
                )
                
            // 计算Logo3的位置
            logo3?.center = CGPoint(
                x: startX + (logo2?.frame.width ?? 0) + horizontalSpacing + (logo3?.frame.width ?? 0) / 2,
                    y: containerHeight / 2
                )
            
            return
        }
        
        // 情况5：只有一个元素
        if enabledCount == 1 {
            // 只有一个元素：居中显示
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let logo2View = self.logo2View {
                // Logo2垂直居中
                logo2View.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let logo3View = self.logo3View {
                // Logo3垂直居中
                logo3View.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
        }
    }
    
    /// 移除自定义水印21样式效果
    func remove(from previewContainer: UIView) {
        // 先移除可能的模糊效果视图引用
        self.blurEffectBackgroundView = nil
        
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.logo2View?.removeFromSuperview() // 清理Logo2
        self.logo3View?.removeFromSuperview() // 清理Logo3
        // self.textLabel?.removeFromSuperview()
        // self.preferenceLabel?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let custom21Wrapper = self.scaledCustom21FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom21FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        custom21Wrapper.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle21: 已移除自定义水印21边框。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
    }
    
    /// 辅助方法：清理存储状态
    private func cleanupStoredState() {
        originalContentView = nil
        originalFrameInSuperview = nil
        originalTransform = nil
        originalSuperview = nil
        originalIndexInSuperview = nil
        scaledCustom21FrameWrapper = nil
        bottomItemsContainer = nil
        logoView = nil
        logo2View = nil
        logo3View = nil
        // textLabel = nil
        // preferenceLabel = nil
        watermarkSettings = nil
    }
} 