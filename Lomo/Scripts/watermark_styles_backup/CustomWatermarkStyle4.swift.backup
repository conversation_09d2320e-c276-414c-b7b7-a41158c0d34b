import UIKit

// MARK: - 自定义水印4
/// 自定义水印样式4 - 基于拍立得风格定制的水印
class CustomWatermarkStyle4: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom4FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义4框架视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat, 
         bottomBorderWidth: CGFloat, // From WatermarkConstants.Custom4.bottomBorderScreenHeightFactor * screenHeight
         sideBorderWidth: CGFloat, 
         borderColor: UIColor = .white) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle4: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印4到预览视图
    func apply(to previewContainer: UIView) {
        // 安全检查：确保容器有效
        guard previewContainer.bounds.width > 0, previewContainer.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle4: apply - 无效的容器尺寸，跳过应用水印")
            return
        }
        
        if scaledCustom4FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle4: apply - 无法找到实际内容视图。")
            return
        }
        
        // 确保内容视图尺寸有效
        guard contentView.bounds.width > 0, contentView.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle4: apply - 内容视图尺寸无效，跳过应用水印")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom4Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let custom4Wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        custom4Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom4Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom4Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle4: 无法获取视图截图，使用默认背景")
                custom4Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            custom4Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom4Wrapper.addSubview(contentView)

        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom4Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle4")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了白色背景
        custom4Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom4FrameWrapper = custom4Wrapper
        previewContainer.addSubview(custom4Wrapper)

        // --- 将 wrapper (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom4Wrapper.bounds.width > 0, custom4Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle4: wrapper 尺寸为0。")
            custom4Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom4Wrapper.bounds.width
        let scaleY = containerSize.height / custom4Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom4Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom4Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle4: 已应用自定义水印4。最终缩放: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 安全检查：确保容器尺寸有效
        guard container.bounds.width > 0, container.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle4: 无效的容器尺寸，跳过添加水印元素")
            return
        }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showPreference].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        // 创建Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建文字（如果需要）
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建偏好（如果需要）
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 直接安排布局，不使用异步处理
        arrangeElementsLayout(container, enabledCount: enabledElementsCount)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印4使用动态大小 - 根据当前底部边框宽度等比例计算Logo大小
        // 公式：logoSize = bottomBorderWidth * logoSizeBaseBorderRatio
        let logoSize = self.bottomBorderWidth * WatermarkConstants.Custom4.logoSizeBaseBorderRatio
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印4 Logo动态大小: bottomBorderWidth=\(self.bottomBorderWidth), logoSize=\(logoSize)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印4使用线性增长 - 文字只有25%增长，而Logo有50%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom4.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom4.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom4.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom4.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom4.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom4.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印4 文字线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印4使用线性增长 - 偏好标签与文字使用相同的增长模式（25%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        if enabledElementsCount == 1 {
            baseSize = WatermarkConstants.Custom4.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom4.fontSingleElementGrowthFactor
        } else {
            baseSize = WatermarkConstants.Custom4.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom4.fontTwoElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom4.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom4.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
        
        print("✅ 水印4 偏好线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 根据元素数量安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        switch enabledCount {
        case 1:
            // 只有一个元素：居中显示
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let textLabel = self.textLabel {
                // 文字垂直居中
                textLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let preferenceLabel = self.preferenceLabel {
                // 偏好垂直居中
                preferenceLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
            
        case 2:
            // 两个元素：水平布局
            // 使用基于屏幕宽度的内边距常量
            let logoLeftPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom4.logoLeftPaddingScreenWidthFactor
            let textRightPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom4.textRightPaddingScreenWidthFactor
            
            // 检查有哪些元素并采用水平布局
            if let logoView = self.logoView, let preferenceLabel = self.preferenceLabel {
                // LOGO和偏好标签同时存在 - 采用水平布局
                // LOGO放在左边
                let logoX = logoLeftPadding + (logoView.bounds.width / 2)
                logoView.center = CGPoint(x: logoX, y: containerHeight / 2)
                
                // 偏好放在右边
                let preferenceX = containerWidth - textRightPadding - (preferenceLabel.bounds.width / 2)
                preferenceLabel.center = CGPoint(x: preferenceX, y: containerHeight / 2)
                
                print("✅ 水印4两元素水平布局: LOGO在左(x=\(logoX)), 偏好在右(x=\(preferenceX))")
            } else if let logoView = self.logoView, let textLabel = self.textLabel {
                // LOGO和文字同时存在 - 采用水平布局
                // LOGO放在左边
                let logoX = logoLeftPadding + (logoView.bounds.width / 2)
                logoView.center = CGPoint(x: logoX, y: containerHeight / 2)
                
                // 文字放在右边
                let textX = containerWidth - textRightPadding - (textLabel.bounds.width / 2)
                textLabel.center = CGPoint(x: textX, y: containerHeight / 2)
                
                print("✅ 水印4两元素水平布局: LOGO在左(x=\(logoX)), 文字在右(x=\(textX))")
            } else if let textLabel = self.textLabel, let preferenceLabel = self.preferenceLabel {
                // 文字和偏好同时存在 - 采用水平布局
                // 文字放在左边
                let textX = logoLeftPadding + (textLabel.bounds.width / 2)
                textLabel.center = CGPoint(x: textX, y: containerHeight / 2)
                
                // 偏好放在右边
                let preferenceX = containerWidth - textRightPadding - (preferenceLabel.bounds.width / 2)
                preferenceLabel.center = CGPoint(x: preferenceX, y: containerHeight / 2)
                
                print("✅ 水印4两元素水平布局: 文字在左(x=\(textX)), 偏好在右(x=\(preferenceX))")
            }
            
        default:
            // 水印4不支持超过2个元素，但为安全起见添加默认情况
            print("⚠️ 水印4: 不支持\(enabledCount)个元素的布局，元素可能不可见")
            break
        }
    }
    
    /// 移除自定义水印4效果
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let wrapperView = self.scaledCustom4FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom4FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapperView.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle4: 已移除自定义水印4。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 辅助方法：清理存储的状态变量
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledCustom4FrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.preferenceLabel = nil
        self.bottomItemsContainer = nil
        self.watermarkSettings = nil
    }
} 