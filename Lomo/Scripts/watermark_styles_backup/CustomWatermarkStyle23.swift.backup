import UIKit

// MARK: - 自定义水印23
/// 自定义水印23 - 创建田字形(2x2)网格水印，四个相同的内容视图用边框分隔
class CustomWatermarkStyle23: WatermarkStyle {
    /// 边框宽度（用于所有边框和分隔线）
    private let borderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect? // contentView在其原始父视图中的frame
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?

    /// 这个视图包含了 contentView 和它周围的边框，它会被缩放并居中到 previewContainer
    private weak var scaledBorderedContentViewWrapper: UIView?

    /// 用于标记处理的是相机预览还是照片模式
    private var isProcessingCameraPreview: Bool = false
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部内容元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var signatureLabel: UILabel? // 署名标签
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 边框宽度（用于所有边框和分隔线）
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, borderColor: UIColor = .white) {
        self.borderWidth = borderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图
    private func findActualContentView(in container: UIView) -> UIView? {
        var contentView: UIView? = nil
        
        // 优先查找我们为导入图片创建的 imageHostView (tag 123 from MockPreviewView.imageHostViewTag)
        if let imageHost = container.viewWithTag(123) { 
            contentView = imageHost
            isProcessingCameraPreview = false
            print("CustomWatermarkStyle23: 发现照片模式的 imageHostView (tag 123)")
            return contentView
        }

        // 查找相机预览视图
        var cameraPreviewView: UIView? = nil
        
        // 查找 MockCameraPreviewService 创建的中间容器 (tag 100) 和预览视图 (tag 101)
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                cameraPreviewView = cameraPreview
                print("CustomWatermarkStyle23: 发现相机预览视图 (tag 101 in tag 100)")
            }
        }
        // 容器本身是相机预览视图 (tag 101)
        else if container.tag == 101 { 
            cameraPreviewView = container
            print("CustomWatermarkStyle23: 容器本身是相机预览视图 (tag 101)")
        }
        
        // 如果找到了相机预览视图，使用它或创建其副本
        if let cameraPreview = cameraPreviewView {
            isProcessingCameraPreview = true
            
            // 如果发现这是相机预览视图，我们需要特殊处理
            // 由于相机预览可能已经被预先缩放，我们需要确保这里的处理与照片模式一致
            return cameraPreview
        }
        
        // 降级：查找第一个 UIImageView
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            print("CustomWatermarkStyle23: 使用第一个 UIImageView 作为内容视图")
            return imageView
        }

        print("⚠️ CustomWatermarkStyle23: 未找到特定的内容视图，尝试使用第一个子视图")
        // 最后的降级：尝试取第一个子视图
        if let firstSubview = container.subviews.first {
            return firstSubview
        }
        
        print("❌ CustomWatermarkStyle23: 无法确定内容视图，且容器内无子视图")
        return nil // 如果没有任何可识别的内容视图
    }
    
    /// 应用白色边框水印
    func apply(to previewContainer: UIView) {
        if scaledBorderedContentViewWrapper != nil {
            remove(from: previewContainer) // 先移除旧的
        }

        // 获取共享的拼图照片
        let puzzleImages = PuzzleImageProvider.shared.images

        // 获取水印设置
        let settings = WatermarkSettingsManager.shared.getSettings()
        self.watermarkSettings = settings

        // 根据是否存在4张拼图照片，决定使用拼图逻辑还是相机预览逻辑
        if puzzleImages.count == 4 {
            // --- 模式1: 使用4张导入的照片创建拼图 ---
            print("🎨 CustomWatermarkStyle23: Applying puzzle mode with 4 images.")
            
            // 获取第一张照片的宽高比
            let firstImage = puzzleImages[0]
            let firstImageAspectRatio = firstImage.size.width / firstImage.size.height
            print("📐 CustomWatermarkStyle23: 第一张照片宽高比 = \(firstImageAspectRatio)")
            
            // 移除预览容器中已有的图像视图以避免重叠
            if let existingImageHostView = previewContainer.viewWithTag(123) {
                existingImageHostView.isHidden = true
            }
            
            // 获取屏幕尺寸
            let screenWidth = UIScreen.main.bounds.width
            let screenHeight = UIScreen.main.bounds.height
            
            // 边框宽度保持与预览模式一致，使用传入的borderWidth（基于屏幕高度的百分比）
            let actualBorderWidth = self.borderWidth
            
            // 根据第一张照片的宽高比计算内容区域的宽高
            let contentSize: CGSize
            
            // 计算内容的最大尺寸（考虑容器大小和边框宽度）
            let maxContentWidth = previewContainer.bounds.width - (actualBorderWidth * 3)
            let maxContentHeight = screenHeight * 0.5 - (actualBorderWidth * 3)
            
            // 计算内容尺寸，保持第一张照片的宽高比
            let contentWidth: CGFloat
            let contentHeight: CGFloat
            
            // 根据宽高比决定如何计算内容尺寸
            if maxContentWidth / maxContentHeight > firstImageAspectRatio {
                // 高度是限制因素
                contentHeight = maxContentHeight
                contentWidth = contentHeight * firstImageAspectRatio
            } else {
                // 宽度是限制因素
                contentWidth = maxContentWidth
                contentHeight = contentWidth / firstImageAspectRatio
            }
            
            // 根据第一张照片的宽高比决定布局方向
            let isHorizontalLayout = firstImageAspectRatio < 1.0
            
            // 计算单元格宽度和高度（根据布局方向）
            let singleContentWidth: CGFloat
            let singleContentHeight: CGFloat
            
            if isHorizontalLayout {
                // 水平布局（2x2网格，宽大于高）
                singleContentWidth = contentWidth / 2
                singleContentHeight = contentHeight / 2
            } else {
                // 垂直布局（2x2网格，高大于宽）
                singleContentWidth = contentWidth / 2
                singleContentHeight = contentHeight / 2
            }
            
            // 计算整个田字形布局的尺寸，包括边框和分隔线
            let borderedViewWidth = contentWidth + (actualBorderWidth * 3)
            let borderedViewHeight = contentHeight + (actualBorderWidth * 3)
            let borderedViewSize = CGSize(width: borderedViewWidth, height: borderedViewHeight)

            // 创建包含边框和内容的视图
            let borderedView = UIView(frame: CGRect(origin: .zero, size: borderedViewSize))
            borderedView.clipsToBounds = false
            borderedView.backgroundColor = self.borderColor
            
            // 定义四个内容区域的位置
            let cellPositions: [(x: CGFloat, y: CGFloat)]
            
            // 注意：对于2x2网格，无论是横向还是纵向布局，位置计算都是一样的
            // 但我们保留这个条件分支，以便将来可能的差异化处理
            if isHorizontalLayout {
                // 水平布局（宽大于高）
                cellPositions = [
                    (x: actualBorderWidth, y: actualBorderWidth),                                         // 左上 - 照片1
                    (x: actualBorderWidth * 2 + singleContentWidth, y: actualBorderWidth),                // 右上 - 照片2
                    (x: actualBorderWidth, y: actualBorderWidth * 2 + singleContentHeight),               // 左下 - 照片3
                    (x: actualBorderWidth * 2 + singleContentWidth, y: actualBorderWidth * 2 + singleContentHeight) // 右下 - 照片4
                ]
            } else {
                // 垂直布局（高大于宽）
                cellPositions = [
                (x: actualBorderWidth, y: actualBorderWidth),                                         // 左上 - 照片1
                (x: actualBorderWidth * 2 + singleContentWidth, y: actualBorderWidth),                // 右上 - 照片2
                (x: actualBorderWidth, y: actualBorderWidth * 2 + singleContentHeight),               // 左下 - 照片3
                (x: actualBorderWidth * 2 + singleContentWidth, y: actualBorderWidth * 2 + singleContentHeight) // 右下 - 照片4
            ]
            }
            
            // 创建四个内容区域，每个区域显示一张不同的照片
            for (index, position) in cellPositions.enumerated() {
                let cellView = UIImageView(frame: CGRect(
                    x: position.x,
                    y: position.y,
                    width: singleContentWidth,
                    height: singleContentHeight
                ))
                cellView.image = puzzleImages[index]
                cellView.contentMode = .scaleAspectFill
                cellView.clipsToBounds = true
                borderedView.addSubview(cellView)
            }
            
            // 将整个拼图视图缩放并居中
            self.scaledBorderedContentViewWrapper = borderedView
            previewContainer.addSubview(borderedView)
            
            let containerSize = previewContainer.bounds.size
            guard borderedView.bounds.width > 0, borderedView.bounds.height > 0 else { return }
            
            let scaleX = containerSize.width / borderedView.bounds.width
            let scaleY = containerSize.height / borderedView.bounds.height
            let finalScaleFactor = min(scaleX, scaleY)

            borderedView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
            borderedView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
            
            print("✅ CustomWatermarkStyle23: 已应用拼图水印。第一张照片宽高比: \(firstImageAspectRatio), 布局方向: \(isHorizontalLayout ? "水平" : "垂直"), 最终缩放比例: \(finalScaleFactor)")
            
        } else {
            // --- 模式2: 使用相机预览或单张照片创建田字格 (原始逻辑) ---
            print("📹 CustomWatermarkStyle23: Applying live preview mode.")

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle23: apply - 无法找到实际内容视图")
            return
        }

            // 保存原始视图状态，以便恢复
        self.originalContentView = contentView
            self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
                self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
                self.originalIndexInSuperview = 0
        }

            let contentOriginalSize = contentView.bounds.size
            
            // 获取内容视图的宽高比
            let contentAspectRatio = contentOriginalSize.width / contentOriginalSize.height
            print("📐 CustomWatermarkStyle23: 内容视图宽高比 = \(contentAspectRatio)")
            
            // 根据内容视图的宽高比决定布局方向
            let isHorizontalLayout = contentAspectRatio < 1.0
            
            // 计算单个内容区域的尺寸（根据布局方向）
            let singleContentWidth = contentOriginalSize.width / 2
            let singleContentHeight = contentOriginalSize.height / 2
            
            // 计算整个田字形布局的尺寸，包括边框和分隔线
            let borderedViewWidth = (singleContentWidth * 2) + (self.borderWidth * 3)
            let borderedViewHeight = (singleContentHeight * 2) + (self.borderWidth * 3)
        let borderedViewSize = CGSize(width: borderedViewWidth, height: borderedViewHeight)

            // 创建包含边框和内容的视图
        let borderedView = UIView(frame: CGRect(origin: .zero, size: borderedViewSize))
            borderedView.clipsToBounds = false
            borderedView.backgroundColor = self.borderColor
            
            // 捕获原始内容视图的图像
            let capturedImage = WatermarkStyleUtils.captureView(contentView)
            
            // 隐藏原始内容视图，避免与水印重叠
            contentView.isHidden = true
            
            // 定义四个内容区域的位置
            let cellPositions: [(x: CGFloat, y: CGFloat)]
            
            // 注意：对于2x2网格，无论是横向还是纵向布局，位置计算都是一样的
            // 但我们保留这个条件分支，以便将来可能的差异化处理
            if isHorizontalLayout {
                // 水平布局（宽大于高）
                cellPositions = [
                    (x: self.borderWidth, y: self.borderWidth),                                         // 左上
                    (x: self.borderWidth * 2 + singleContentWidth, y: self.borderWidth),                // 右上
                    (x: self.borderWidth, y: self.borderWidth * 2 + singleContentHeight),               // 左下
                    (x: self.borderWidth * 2 + singleContentWidth, y: self.borderWidth * 2 + singleContentHeight) // 右下
                ]
            } else {
                // 垂直布局（高大于宽）
                cellPositions = [
                (x: self.borderWidth, y: self.borderWidth),                                         // 左上
                (x: self.borderWidth * 2 + singleContentWidth, y: self.borderWidth),                // 右上
                (x: self.borderWidth, y: self.borderWidth * 2 + singleContentHeight),               // 左下
                (x: self.borderWidth * 2 + singleContentWidth, y: self.borderWidth * 2 + singleContentHeight) // 右下
            ]
            }
            
            // 创建四个内容区域，每个区域显示相同的内容
            for (index, position) in cellPositions.enumerated() {
                let cellView = UIImageView(frame: CGRect(
                    x: position.x,
                    y: position.y,
                    width: singleContentWidth,
                    height: singleContentHeight
                ))
                
                if let image = capturedImage {
                    cellView.image = image
                    cellView.contentMode = .scaleAspectFill
                    cellView.clipsToBounds = true
            } else {
                    cellView.backgroundColor = .black
                }
                borderedView.addSubview(cellView)
            }

            // 拼图标题水印不添加任何Logo、文字、签名、偏好等元素
            // 只保留边框效果，符合"只有边框颜色和边框粗细两个选项"的设计要求

            self.scaledBorderedContentViewWrapper = borderedView
        previewContainer.addSubview(borderedView)

        let containerSize = previewContainer.bounds.size
            guard borderedView.bounds.width > 0, borderedView.bounds.height > 0 else { return }

        let scaleX = containerSize.width / borderedView.bounds.width
        let scaleY = containerSize.height / borderedView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        borderedView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        borderedView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
            print("✅ CustomWatermarkStyle23: 已应用田字形水印，布局方向: \(isHorizontalLayout ? "水平" : "垂直"), 缩放比例: \(finalScaleFactor)")
        }
    }
    
    /// 移除边框水印效果
    func remove(from previewContainer: UIView) {
        // 清理UI元素
        self.textLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.logoView?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        // 移除主包装视图
        self.scaledBorderedContentViewWrapper?.removeFromSuperview()

        // 如果存在原始视图状态，则恢复它 (仅在相机预览模式下)
        if let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        {
            contentView.removeFromSuperview()
            contentView.transform = .identity
        contentView.frame = originalFrame
            contentView.transform = originalTransform
            contentView.isHidden = false // 确保原始视图可见
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
                superview.addSubview(contentView)
            }
            print("✅ CustomWatermarkStyle23: Restored original content view.")
        }
        
        // 恢复可能被隐藏的图像视图
        if let imageHostView = previewContainer.viewWithTag(123) {
            imageHostView.isHidden = false
        }
        
        // 清理所有存储的状态
        cleanupStoredState()
        print("✅ CustomWatermarkStyle23: Removed watermark and cleaned up state.")
    }
    
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            return
        }
        contentView.removeFromSuperview() //确保从任何当前父视图中移除
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledBorderedContentViewWrapper = nil
        self.textLabel = nil
        self.bottomItemsContainer = nil
        self.watermarkSettings = nil
    }
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 添加对签名和偏好选项的检测
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 签名启用且不为空
        
        // 检查新格式或旧格式的偏好选项
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        
        // 统计启用的元素数量
        let enabledElementsCount = [showLogo, showText, showSignature, showPreference].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        // 获取位置设置
        let position = settings.positionOption // "中" 或 "下"
        let isCenter = position == "中" // 如果是"中"则居中显示，否则在底部显示
        
        // 如果需要显示Logo，创建Logo视图
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示文字，创建文字标签
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示签名，创建签名标签
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 如果需要显示偏好选项，创建偏好标签
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 根据启用的元素数量，安排布局
        arrangeElementsLayout(container, enabledCount: enabledElementsCount, position: position)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的2.5%屏幕高度作为Logo大小
        let logoSize = UIScreen.main.bounds.height * 0.025
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印23 Logo: 大小=\(logoSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印23 文字标签: 字体大小=\(fontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建签名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        // 应用签名大小乘数
        let finalFontSize = fontSize * CGFloat(settings.signatureFontSizeMultiplier)
        
        let label = UILabel()
        label.text = settings.watermarkSignature
        label.textAlignment = .center
        label.numberOfLines = 0 // 允许多行文本
        
        // 应用字体样式，标记为署名类型
        WatermarkStyleUtils.applyFontStyle(to: label, with: settings, fontSize: finalFontSize, styleIdentifier: "CustomWatermarkStyle23.Signature", isSignature: true)
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印23 签名标签: 字体大小=\(finalFontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 使用固定的1%屏幕高度作为字体大小
        let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Border.fontSizeScreenHeightFactor
        // 应用偏好缩放因子
        let finalFontSize = fontSize * CGFloat(settings.preferenceScaleFactor)
        
        // 创建偏好标签
        let preferenceLabel = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption,
            with: settings,
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: finalFontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Border.labelMaxWidthContainerWidthFactor
        let size = preferenceLabel.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        preferenceLabel.frame.size = size
        
        container.addSubview(preferenceLabel)
        self.preferenceLabel = preferenceLabel
        
        print("✅ 水印23 偏好标签: 字体大小=\(finalFontSize), 元素数量=\(enabledElementsCount)")
    }
    
    /// 根据元素数量和位置安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int, position: String) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        let bottomPadding: CGFloat = containerHeight * WatermarkConstants.Border.bottomPaddingContainerHeightFactor
        
        // 参考水印8的垂直间距
        let verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom8.stackViewSpacingFactor
            
        // 收集所有可见元素
        var visibleElements: [(view: UIView, order: Int)] = []
        
        if let logo = self.logoView {
            visibleElements.append((logo, 1)) // Logo优先级最高，排在最上方
        }
        
        if let signature = self.signatureLabel {
            visibleElements.append((signature, 2)) // 署名优先级第二，排在Logo后面
        }
        
        if let text = self.textLabel {
            visibleElements.append((text, 3)) // 文字优先级第三
        }
        
        if let preference = self.preferenceLabel {
            visibleElements.append((preference, 4)) // 偏好选项优先级最低，排在最下方
        }
        
        // 按优先级排序
        visibleElements.sort { $0.order < $1.order }
        
        // 如果没有可见元素，直接返回
        if visibleElements.isEmpty {
            return
        }
        
        // 计算所有元素高度总和（包括间距）
        var totalHeight: CGFloat = 0
        for (index, element) in visibleElements.enumerated() {
            totalHeight += element.view.bounds.height
            if index < visibleElements.count - 1 {
                totalHeight += verticalSpacing
            }
        }
        
        // 计算起始Y位置
        let startY = containerHeight - bottomPadding - totalHeight
        
        // 依次布局每个元素
        var currentY = startY
        for element in visibleElements {
            let view = element.view
            view.center = CGPoint(x: containerWidth / 2, y: currentY + view.bounds.height / 2)
            
            // 文字类型元素需要设置文本对齐方式
            if let label = view as? UILabel {
                label.textAlignment = .center
            }
            
            currentY += view.bounds.height + verticalSpacing
        }
        
        // 输出日志
        let elementTypes = visibleElements.map { element -> String in
            if element.view === self.logoView {
                return "Logo"
            } else if element.view === self.signatureLabel {
                return "署名"
            } else if element.view === self.textLabel {
                return "文字"
            } else if element.view === self.preferenceLabel {
                return "偏好"
            } else {
                return "未知元素"
            }
        }.joined(separator: "、")
        
        print("✅ 水印23 布局: 从上到下依次为\(elementTypes)的布局，共\(visibleElements.count)个元素")
    }
} 