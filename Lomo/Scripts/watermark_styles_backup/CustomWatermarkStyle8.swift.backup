import UIKit

/// 自定义水印8 - 无边框胶片风格，署名替换Logo，支持中下位置
class Custom8WatermarkStyle: WatermarkStyle {
    /// 边框宽度
    private let borderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var custom8WatermarkWrapper: UIView? // 水印包装视图
    
    // --- 水印元素相关 ---
    private weak var watermarkContentContainer: UIView? // 水印内容容器
    private weak var signatureLabel: UILabel? // 署名标签
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用
    
    /// 初始化
    /// - Parameters:
    ///   - borderWidth: 边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(borderWidth: CGFloat, borderColor: UIColor) {
        self.borderWidth = borderWidth // from WatermarkConstants.Custom8.fixedBorderScreenHeightFactor * screenHeight
        self.borderColor = borderColor // from WatermarkConstants.Custom8.borderColor
    }
    
    /// 查找实际的内容视图
    private func findActualContentView(in previewContainer: UIView) -> UIView? {
        // 先尝试查找照片模式下的图片视图 (imageHostView, tag = 123)
        if let imageHostView = previewContainer.viewWithTag(123) {
            print("Custom8WatermarkStyle: 发现照片模式的 imageHostView (tag 123)")
            return imageHostView
        }
        
        // 查找相机预览视图
        if let intermediateContainer = previewContainer.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                print("Custom8WatermarkStyle: 发现相机预览视图 (tag 101 in tag 100)")
                return cameraPreview
            }
        }
        
        if previewContainer.tag == 101 {
            print("Custom8WatermarkStyle: 容器本身是相机预览视图 (tag 101)")
            return previewContainer
        }
        
        // 降级：查找第一个UIImageView
        if let imageView = previewContainer.subviews.first(where: { $0 is UIImageView }) {
            print("Custom8WatermarkStyle: 使用第一个UIImageView作为内容视图")
            return imageView
        }
        
        print("⚠️ Custom8WatermarkStyle: 未找到特定的内容视图，尝试使用第一个子视图")
        return previewContainer.subviews.first
    }
    
    /// 应用自定义水印8到预览视图
    /// - Parameter previewContainer: 预览容器视图
    func apply(to previewContainer: UIView) {
        if custom8WatermarkWrapper != nil {
            remove(from: previewContainer) // 先移除旧的
        }
        
        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ Custom8WatermarkStyle: apply - 无法找到实际内容视图")
            return
        }
        
        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        
        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }
        
        // --- 创建水印效果 ---
        let contentOriginalSize = contentView.bounds.size
        
        // 创建一个包装视图，大小与内容视图相同（不添加边框）
        let wrapperView = UIView(frame: CGRect(origin: .zero, size: contentOriginalSize))
        wrapperView.clipsToBounds = true
        
        // 保持内容视图的原始尺寸和位置
        contentView.transform = .identity
        contentView.frame = CGRect(x: 0, y: 0, width: contentOriginalSize.width, height: contentOriginalSize.height)
        
        // 添加内容视图到包装视图
        wrapperView.addSubview(contentView)
        
        // 创建水印内容容器
        let watermarkContainer = UIView(frame: wrapperView.bounds)
        watermarkContainer.backgroundColor = .clear // 透明背景
        watermarkContainer.isUserInteractionEnabled = false // 禁用用户交互
        wrapperView.addSubview(watermarkContainer)
        self.watermarkContentContainer = watermarkContainer
        
        // 添加水印元素到容器
        addWatermarkElements(to: watermarkContainer)
        
        // 保存包装视图引用
        self.custom8WatermarkWrapper = wrapperView
        
        // 添加包装视图到预览容器
        previewContainer.addSubview(wrapperView)
        
        // 计算并应用缩放以适应预览容器
        let containerSize = previewContainer.bounds.size
        guard wrapperView.bounds.width > 0, wrapperView.bounds.height > 0 else {
            print("❌ Custom8WatermarkStyle: wrapperView 尺寸为0")
            wrapperView.removeFromSuperview()
            return
        }
        
        let scaleX = containerSize.width / wrapperView.bounds.width
        let scaleY = containerSize.height / wrapperView.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)
        
        wrapperView.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        wrapperView.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ Custom8WatermarkStyle: 已应用自定义水印8，缩放比例: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到容器
    private func addWatermarkElements(to container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 确定需要显示的元素
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty
        let showPreference = settings.preferenceOption != "OFF"
        
        // 如果没有任何元素需要显示，直接返回
        if !showSignature && !showText && !showPreference { return }
        
        // 获取位置设置
        let position = settings.positionOption // "中" 或 "下"
        let isCenter = position == "中"
        
        // 获取字体设置
        let fontColor = UIColor(
            red: CGFloat(settings.fontColorRed),
            green: CGFloat(settings.fontColorGreen),
            blue: CGFloat(settings.fontColorBlue),
            alpha: CGFloat(settings.fontColorAlpha)
        )
        
        var fontName = WatermarkConstants.Custom8.defaultTextFontName
        switch settings.selectedFontName {
        case "黑体":
            fontName = WatermarkConstants.Common.defaultPingFangSCSemiboldFont
        case "苹方":
            fontName = WatermarkConstants.Common.defaultPingFangSCRegularFont
        case "Times":
            fontName = WatermarkConstants.Common.defaultTimesNewRomanFont
        case "Courier":
            fontName = WatermarkConstants.Common.defaultCourierNewFont
        case "Makinas-Flat":
            fontName = "Makinas-Flat"
        case "Makinas-Square":
            fontName = "Makinas-Square"
        default:
            fontName = WatermarkConstants.Custom8.defaultTextFontName
        }
        
        // 计算位置
        let containerWidth = container.bounds.width
        let containerHeight = container.bounds.height
        
        // 创建内容视图
        let contentStackView = UIStackView()
        contentStackView.axis = .vertical
        contentStackView.alignment = .center
        contentStackView.spacing = UIScreen.main.bounds.height * WatermarkConstants.Custom8.stackViewSpacingFactor
        contentStackView.backgroundColor = .clear
        contentStackView.translatesAutoresizingMaskIntoConstraints = false
        container.addSubview(contentStackView)
        
        // 添加署名（如果需要）
        if showSignature {
            var fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom8.signatureFontSizeScreenHeightFactor
            
            // 应用署名大小乘数
            fontSize *= CGFloat(settings.signatureFontSizeMultiplier)
            
            let signatureLabel = TextUtils.createSignatureLabel(with: settings, fontSize: fontSize)
            signatureLabel.backgroundColor = .clear
            
            contentStackView.addArrangedSubview(signatureLabel)
            self.signatureLabel = signatureLabel
            
            print("✅ 水印8 署名标签: 字体大小=\(fontSize), 大小乘数=\(settings.signatureFontSizeMultiplier)")
        }
        
        // 添加文字（如果需要）
        if showText {
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom8.fontSizeScreenHeightFactor
            let label = TextLabelCreator.createLabel(
                with: settings,
                fixedFontSize: fontSize
            )
            
            label.backgroundColor = .clear
            
            contentStackView.addArrangedSubview(label)
            self.textLabel = label
        }
        
        // 添加偏好（如果需要）
        if showPreference {
            let fontSize = UIScreen.main.bounds.height * WatermarkConstants.Custom8.fontSizeScreenHeightFactor
            let preferenceLabel = PreferenceLabelCreator.createLabel(
                for: settings.preferenceOption,
                with: settings,
                enabledElementsCount: [showSignature, showText, showPreference].filter { $0 }.count,
                customFontSizeFactors: (
                    single: WatermarkConstants.Custom8.fontSizeScreenHeightFactor,
                    two: WatermarkConstants.Custom8.fontSizeScreenHeightFactor,
                    three: WatermarkConstants.Custom8.fontSizeScreenHeightFactor
                )
            )
            
            preferenceLabel.backgroundColor = .clear
            
            contentStackView.addArrangedSubview(preferenceLabel)
            self.preferenceLabel = preferenceLabel
        }
        
        // 设置位置约束
        if isCenter {
            // 居中显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                contentStackView.centerYAnchor.constraint(equalTo: container.centerYAnchor)
            ])
        } else {
            // 底部显示
            NSLayoutConstraint.activate([
                contentStackView.centerXAnchor.constraint(equalTo: container.centerXAnchor),
                contentStackView.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -UIScreen.main.bounds.height * WatermarkConstants.Custom8.elementsBottomOffsetScreenHeightFactor)
            ])
        }
    }
    
    /// 移除自定义水印8效果
    /// - Parameter previewContainer: 预览容器视图
    func remove(from previewContainer: UIView) {
        guard let wrapperView = self.custom8WatermarkWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.custom8WatermarkWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }
        
        // 1. 从包装视图中移除内容视图
        contentView.removeFromSuperview()
        
        // 2. 恢复内容视图的原始状态
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        // 3. 将内容视图添加回原始父视图
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        // 4. 移除包装视图
        wrapperView.removeFromSuperview()
        
        // 5. 清理存储的状态
        cleanupStoredState()
        print("✅ Custom8WatermarkStyle: 已移除自定义水印8")
    }
    
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.custom8WatermarkWrapper = nil
        self.watermarkContentContainer = nil
        self.signatureLabel = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.watermarkSettings = nil
    }
} 