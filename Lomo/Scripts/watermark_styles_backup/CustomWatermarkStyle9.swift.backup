import UIKit

// MARK: - 自定义水印9
/// 自定义水印样式9 - 基于自定义水印4，新增署名功能支持
class CustomWatermarkStyle9: WatermarkStyle {
    /// 上边框宽度
    private let topBorderWidth: CGFloat
    
    /// 底部边框宽度
    private let bottomBorderWidth: CGFloat
    
    /// 左右边框宽度
    private let sideBorderWidth: CGFloat
    
    /// 边框颜色
    private let borderColor: UIColor
    
    // --- 存储原始状态和中间视图 ---
    private weak var originalContentView: UIView?
    private var originalFrameInSuperview: CGRect?
    private var originalTransform: CGAffineTransform?
    private weak var originalSuperview: UIView?
    private var originalIndexInSuperview: Int?
    private weak var scaledCustom9FrameWrapper: UIView? // 用于存储最外层的、已缩放的自定义9框架视图
    
    // --- 水印元素相关 ---
    private weak var bottomItemsContainer: UIView? // 底部边框中的元素容器
    private weak var logoView: UIImageView? // Logo图片视图
    private weak var textLabel: UILabel? // 水印文字标签
    private weak var preferenceLabel: UILabel? // 偏好选项标签
    private weak var signatureLabel: UILabel? // 署名标签
    private var watermarkSettings: WatermarkSettings? // 保存水印设置的引用

    /// 初始化
    /// - Parameters:
    ///   - topBorderWidth: 上边框宽度
    ///   - bottomBorderWidth: 底部边框宽度
    ///   - sideBorderWidth: 左右边框宽度
    ///   - borderColor: 边框颜色，默认为白色
    init(topBorderWidth: CGFloat, 
         bottomBorderWidth: CGFloat, // From WatermarkConstants.Custom9.bottomBorderScreenHeightFactor * screenHeight
         sideBorderWidth: CGFloat, 
         borderColor: UIColor = .white) {
        self.topBorderWidth = topBorderWidth
        self.bottomBorderWidth = bottomBorderWidth
        self.sideBorderWidth = sideBorderWidth
        self.borderColor = borderColor
    }
    
    /// 查找实际的内容视图 (与BorderWatermarkStyle中的方法类似)
    private func findActualContentView(in container: UIView) -> UIView? {
        if let imageView = container.subviews.first(where: { $0 is UIImageView }) {
            return imageView
        }
        if let intermediateContainer = container.viewWithTag(100) {
            if let cameraPreview = intermediateContainer.viewWithTag(101) {
                return cameraPreview
            }
        }
        if container is UIImageView || container.tag == 101 {
             return container
        }
        print("⚠️ CustomWatermarkStyle9: 未找到实际的内容视图。Container: \(container)")
        return container.subviews.first
    }

    /// 应用自定义水印9到预览视图
    func apply(to previewContainer: UIView) {
        // 安全检查：确保容器有效
        guard previewContainer.bounds.width > 0, previewContainer.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle9: apply - 无效的容器尺寸，跳过应用水印")
            return
        }
        
        if scaledCustom9FrameWrapper != nil {
            remove(from: previewContainer)
        }

        guard let contentView = findActualContentView(in: previewContainer) else {
            print("❌ CustomWatermarkStyle9: apply - 无法找到实际内容视图。")
            return
        }
        
        // 确保内容视图尺寸有效
        guard contentView.bounds.width > 0, contentView.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle9: apply - 内容视图尺寸无效，跳过应用水印")
            return
        }

        // 获取水印设置
        self.watermarkSettings = WatermarkSettingsManager.shared.getSettings()
        // 获取模糊边框设置
        let isBlurBorderEnabled = watermarkSettings?.isBlurBorderEnabled ?? false
        let blurIntensity = watermarkSettings?.blurIntensity ?? 0.7
        // 获取阴影效果设置
        let isShadowEnabled = watermarkSettings?.isShadowEnabled ?? false

        // 1. 保存原始状态
        self.originalContentView = contentView
        self.originalTransform = contentView.transform
        self.originalSuperview = contentView.superview
        if let superview = contentView.superview {
            self.originalFrameInSuperview = contentView.frame
            self.originalIndexInSuperview = superview.subviews.firstIndex(of: contentView)
        } else {
            self.originalFrameInSuperview = contentView.bounds
            self.originalIndexInSuperview = 0
        }

        // --- 创建包含 contentView 和不对称边框的视图 (`custom9Wrapper`) ---
        let contentOriginalSize = contentView.bounds.size
        
        let wrapperWidth = contentOriginalSize.width + (2 * self.sideBorderWidth)
        let wrapperHeight = contentOriginalSize.height + self.topBorderWidth + self.bottomBorderWidth
        let wrapperSize = CGSize(width: wrapperWidth, height: wrapperHeight)

        let custom9Wrapper = UIView(frame: CGRect(origin: .zero, size: wrapperSize))
        custom9Wrapper.clipsToBounds = false // 允许子视图的阴影溢出
        
        // 根据模糊边框设置决定背景 - 两种模式完全互斥
        if isBlurBorderEnabled {
            // 模糊边框模式 - 背景透明
            custom9Wrapper.backgroundColor = .clear
            
            // 使用通用方法捕获内容视图并应用模糊效果
            if let capturedImage = WatermarkStyleUtils.captureView(contentView) {
                WatermarkStyleUtils.applyBlurBackground(to: custom9Wrapper, with: capturedImage, intensity: blurIntensity, settings: watermarkSettings)
            } else {
                // 截图失败时回退到默认背景
                print("⚠️ CustomWatermarkStyle9: 无法获取视图截图，使用默认背景")
                custom9Wrapper.backgroundColor = self.borderColor
            }
        } else {
            // 普通边框模式 - 设置背景颜色
            custom9Wrapper.backgroundColor = self.borderColor
        }
        
        contentView.transform = .identity
        contentView.frame = CGRect(x: self.sideBorderWidth, y: self.topBorderWidth, width: contentOriginalSize.width, height: contentOriginalSize.height)
        custom9Wrapper.addSubview(contentView)

        // 使用扩展方法应用阴影效果
        WatermarkStyleUtils.applyShadowEffect(to: contentView, in: custom9Wrapper, isShadowEnabled: isShadowEnabled, styleIdentifier: "CustomWatermarkStyle9")

        // --- 创建底部元素容器视图 ---
        let bottomItemsContainerFrame = CGRect(
            x: self.sideBorderWidth,
            y: self.topBorderWidth + contentOriginalSize.height,
            width: contentOriginalSize.width,
            height: self.bottomBorderWidth
        )
        
        let bottomContainer = UIView(frame: bottomItemsContainerFrame)
        bottomContainer.backgroundColor = .clear // 透明背景，因为wrapper已经提供了白色背景
        custom9Wrapper.addSubview(bottomContainer)
        self.bottomItemsContainer = bottomContainer
        
        // 添加水印元素到底部容器
        addWatermarkElementsToBottomContainer(bottomContainer)

        self.scaledCustom9FrameWrapper = custom9Wrapper
        previewContainer.addSubview(custom9Wrapper)

        // --- 将 wrapper (作为整体) 缩放并居中到 `previewContainer` ---
        let containerSize = previewContainer.bounds.size
        guard custom9Wrapper.bounds.width > 0, custom9Wrapper.bounds.height > 0 else {
            print("❌ CustomWatermarkStyle9: wrapper 尺寸为0。")
            custom9Wrapper.removeFromSuperview()
            restoreOriginalContentViewState() // 尝试恢复
            return
        }

        let scaleX = containerSize.width / custom9Wrapper.bounds.width
        let scaleY = containerSize.height / custom9Wrapper.bounds.height
        let finalScaleFactor = min(scaleX, scaleY)

        custom9Wrapper.transform = CGAffineTransform(scaleX: finalScaleFactor, y: finalScaleFactor)
        custom9Wrapper.center = CGPoint(x: containerSize.width / 2, y: containerSize.height / 2)
        
        print("✅ CustomWatermarkStyle9: 已应用自定义水印9。最终缩放: \(finalScaleFactor)")
    }
    
    /// 添加水印元素到底部容器
    private func addWatermarkElementsToBottomContainer(_ container: UIView) {
        guard let settings = self.watermarkSettings else { return }
        
        // 安全检查：确保容器尺寸有效
        guard container.bounds.width > 0, container.bounds.height > 0 else {
            print("⚠️ CustomWatermarkStyle9: 无效的容器尺寸，跳过添加水印元素")
            return
        }
        
        // 确定需要显示哪些元素
        let showLogo = !settings.selectedLogo.isEmpty // Logo不为空字符串表示启用
        let showText = settings.isWatermarkTextEnabled && !settings.watermarkText.isEmpty // 文字启用且不为空
        // 检查新格式或旧格式
        let showPreference = !settings.selectedPreferences.isEmpty || settings.preferenceOption != "OFF" // 存在选中的偏好选项或旧格式不为OFF
        let showSignature = settings.isWatermarkSignatureEnabled && !settings.watermarkSignature.isEmpty // 署名启用且不为空
        
        // 统计启用的元素数量（使用通用互斥逻辑，不在这里处理互斥）
        let enabledElementsCount = [showLogo, showText, showPreference, showSignature].filter { $0 }.count
        
        // 如果没有任何元素启用，直接返回
        if enabledElementsCount == 0 { return }
        
        // 创建Logo（如果需要）
        if showLogo {
            createLogoView(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建文字（如果需要）
        if showText {
            createTextLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建偏好（如果需要）
        if showPreference {
            createPreferenceLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 创建署名（如果需要）
        if showSignature {
            createSignatureLabel(container, settings: settings, enabledElementsCount: enabledElementsCount)
        }
        
        // 直接安排布局，不使用异步处理
        arrangeElementsLayout(container, enabledCount: enabledElementsCount)
    }
    
    /// 创建Logo视图
    private func createLogoView(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印9使用动态大小 - 根据当前底部边框宽度等比例计算Logo大小
        // 公式：logoSize = bottomBorderWidth * logoSizeBaseBorderRatio
        let logoSize = self.bottomBorderWidth * WatermarkConstants.Custom9.logoSizeBaseBorderRatio
        
        let logoImageView = LogoCreator.createLogo(
            with: settings,
            fixedSize: logoSize
        )
        
        container.addSubview(logoImageView)
        self.logoView = logoImageView
        
        print("✅ 水印9 Logo动态大小: bottomBorderWidth=\(self.bottomBorderWidth), logoSize=\(logoSize)")
    }
    
    /// 创建文字标签
    private func createTextLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印9使用线性增长 - 文字有25%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        switch enabledElementsCount {
        case 1:
            baseSize = WatermarkConstants.Custom9.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom9.fontSingleElementGrowthFactor
        case 2:
            baseSize = WatermarkConstants.Custom9.fontTwoElementsBaseSize  
            growthFactor = WatermarkConstants.Custom9.fontTwoElementsGrowthFactor
        default: // 3个或更多元素
            baseSize = WatermarkConstants.Custom9.fontThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom9.fontThreeElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom9.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = TextLabelCreator.createLabel(
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom9.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.textLabel = label
        
        print("✅ 水印9 文字线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建偏好标签
    private func createPreferenceLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印9使用线性增长 - 偏好标签与文字使用相同的增长模式（25%增长）
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        switch enabledElementsCount {
        case 1:
            baseSize = WatermarkConstants.Custom9.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom9.fontSingleElementGrowthFactor
        case 2:
            baseSize = WatermarkConstants.Custom9.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom9.fontTwoElementsGrowthFactor
        default: // 3个或更多元素
            baseSize = WatermarkConstants.Custom9.fontThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom9.fontThreeElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom9.baseBorderHeight
        let borderWidthDiff = self.bottomBorderWidth - baseBorderWidth
        let fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDiff / UIScreen.main.bounds.height * growthFactor)
        
        let label = PreferenceLabelCreator.createLabel(
            for: settings.preferenceOption, 
            with: settings, 
            enabledElementsCount: enabledElementsCount,
            fixedFontSize: fontSize // 使用动态计算的字体大小
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom9.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.preferenceLabel = label
        
        print("✅ 水印9 偏好线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDiff/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 创建署名标签
    private func createSignatureLabel(_ container: UIView, settings: WatermarkSettings, enabledElementsCount: Int) {
        // 水印9使用线性增长 - 文字有25%增长
        let baseSize: CGFloat
        let growthFactor: CGFloat
        
        switch enabledElementsCount {
        case 1:
            baseSize = WatermarkConstants.Custom9.fontSingleElementBaseSize
            growthFactor = WatermarkConstants.Custom9.fontSingleElementGrowthFactor
        case 2:
            baseSize = WatermarkConstants.Custom9.fontTwoElementsBaseSize
            growthFactor = WatermarkConstants.Custom9.fontTwoElementsGrowthFactor
        default: // 3 or 4 elements
            baseSize = WatermarkConstants.Custom9.fontThreeElementsBaseSize
            growthFactor = WatermarkConstants.Custom9.fontThreeElementsGrowthFactor
        }
        
        // 线性增长公式：fontSize = baseSize + (currentBorderWidth - baseBorderWidth) * growthFactor
        let baseBorderWidth = UIScreen.main.bounds.height * WatermarkConstants.Custom9.baseBorderHeight
        let borderWidthDifference = self.bottomBorderWidth - baseBorderWidth
        var fontSize = UIScreen.main.bounds.height * (baseSize + borderWidthDifference / UIScreen.main.bounds.height * growthFactor)
        // 应用署名大小乘数
        fontSize *= CGFloat(settings.signatureFontSizeMultiplier)
        
        let label = TextUtils.createSignatureLabel(
            with: settings,
            fontSize: fontSize
        )
        
        // 设置尺寸
        let maxWidth = container.bounds.width * WatermarkConstants.Custom9.labelMaxWidthContainerWidthFactor
        let size = label.sizeThatFits(CGSize(width: maxWidth, height: .greatestFiniteMagnitude))
        label.frame.size = size
        
        container.addSubview(label)
        self.signatureLabel = label
        
        print("✅ 水印9 署名线性增长: baseSize=\(baseSize*100)%, borderDiff=\((borderWidthDifference/UIScreen.main.bounds.height)*100)%, fontSize=\((fontSize/UIScreen.main.bounds.height)*100)%, enabledCount=\(enabledElementsCount)")
    }
    
    /// 根据元素数量安排布局
    private func arrangeElementsLayout(_ container: UIView, enabledCount: Int) {
        let containerHeight = container.bounds.height
        let containerWidth = container.bounds.width
        
        switch enabledCount {
        case 1:
            // 只有一个元素：居中显示
            if let logoView = self.logoView {
                // Logo垂直居中
                logoView.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let textLabel = self.textLabel {
                // 文字垂直居中
                textLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let preferenceLabel = self.preferenceLabel {
                // 偏好垂直居中
                preferenceLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            } else if let signatureLabel = self.signatureLabel {
                // 署名垂直居中
                signatureLabel.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
            }
            
        case 2:
            // 两个元素：水平布局
            // 使用基于屏幕宽度的内边距常量
            let logoLeftPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom9.logoLeftPaddingScreenWidthFactor
            let textRightPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom9.textRightPaddingScreenWidthFactor
            
            // 水印9的两元素布局逻辑：
            // 1. 当logo开启时：logo在左，署名/文字/偏好任一在右
            // 2. 当logo不开启时：署名在左，文字/偏好在右侧
            let leftElement: UIView?
            let rightElement: UIView?
            
            if let logoView = self.logoView {
                // 有Logo：Logo在左侧，其他元素在右侧
                leftElement = logoView
                rightElement = self.signatureLabel ?? self.textLabel ?? self.preferenceLabel
            } else if let signatureLabel = self.signatureLabel {
                // 无Logo但有署名：署名在左侧，文字/偏好在右侧
                leftElement = signatureLabel
                rightElement = self.textLabel ?? self.preferenceLabel
            } else {
                // 无Logo无署名：文字和偏好（应该不会同时存在，但为安全起见）
                leftElement = self.textLabel ?? self.preferenceLabel
                rightElement = nil
            }
            
            if let leftEl = leftElement, let rightEl = rightElement {
                // 左边元素
                let leftX = logoLeftPadding + (leftEl.bounds.width / 2)
                leftEl.center = CGPoint(x: leftX, y: containerHeight / 2)
                
                // 右边元素
                let rightX = containerWidth - textRightPadding - (rightEl.bounds.width / 2)
                rightEl.center = CGPoint(x: rightX, y: containerHeight / 2)
                
                print("✅ 水印9两元素布局: 左侧\(leftEl == self.logoView ? "Logo" : leftEl == self.signatureLabel ? "署名" : "文字/偏好")在(x=\(leftX)), 右侧\(rightEl == self.signatureLabel ? "署名" : rightEl == self.textLabel ? "文字" : "偏好")在(x=\(rightX))")
            } else if let singleEl = leftElement {
                // 只有一个元素时居中显示
                singleEl.center = CGPoint(x: containerWidth / 2, y: containerHeight / 2)
                print("✅ 水印9单元素居中: \(singleEl == self.logoView ? "Logo" : singleEl == self.signatureLabel ? "署名" : singleEl == self.textLabel ? "文字" : "偏好")")
            }
            
        case 3:
            // 三个元素：固定布局 - Logo在左侧，署名和文字/偏好在右侧上下排布
            let logoLeftPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom9.logoLeftPaddingScreenWidthFactor
            let textRightPadding = UIScreen.main.bounds.width * WatermarkConstants.Custom9.textRightPaddingScreenWidthFactor
            let verticalSpacing = UIScreen.main.bounds.height * WatermarkConstants.Custom9.threeElementsVerticalSpacingFactor
            
            if let logoView = self.logoView {
                // Logo在左侧垂直居中
                let logoX = logoLeftPadding + (logoView.bounds.width / 2)
                logoView.center = CGPoint(x: logoX, y: containerHeight / 2)
                
                // 右侧元素：署名和文字/偏好之一
                // 由于互斥逻辑，文字和偏好不会同时存在，所以右侧最多两个元素
                let rightElements = [self.signatureLabel, self.textLabel, self.preferenceLabel].compactMap { $0 }
                
                if rightElements.count >= 2 {
                    let totalElementsHeight = rightElements.reduce(0) { $0 + $1.bounds.height }
                    let totalSpacing = CGFloat(rightElements.count - 1) * verticalSpacing
                    let totalHeight = totalElementsHeight + totalSpacing
                    
                    // 计算起始Y位置，使整组元素垂直居中
                    var currentY = (containerHeight - totalHeight) / 2
                    
                    for element in rightElements {
                        let elementX = containerWidth - textRightPadding - (element.bounds.width / 2)
                        element.center = CGPoint(x: elementX, y: currentY + element.bounds.height / 2)
                        currentY += element.bounds.height + verticalSpacing
                    }
                    
                    print("✅ 水印9三元素布局: Logo在左(x=\(logoX)), 右侧\(rightElements.count)个元素上下排布")
                } else if rightElements.count == 1 {
                    // 只有一个右侧元素时，垂直居中
                    let element = rightElements[0]
                    let elementX = containerWidth - textRightPadding - (element.bounds.width / 2)
                    element.center = CGPoint(x: elementX, y: containerHeight / 2)
                    print("✅ 水印9三元素布局异常: Logo在左，但只有1个右侧元素")
                }
            } else {
                // 没有Logo的三元素情况（理论上不应该发生，但为安全起见）
                print("⚠️ 水印9三元素布局异常: 没有Logo但有3个元素")
                // 使用默认的垂直布局
                let allElements = [self.signatureLabel, self.textLabel, self.preferenceLabel].compactMap { $0 }
                if allElements.count > 0 {
                    let totalElementsHeight = allElements.reduce(0) { $0 + $1.bounds.height }
                    let totalSpacing = CGFloat(allElements.count - 1) * verticalSpacing
                    let totalHeight = totalElementsHeight + totalSpacing
                    
                    var currentY = (containerHeight - totalHeight) / 2
                    let centerX = containerWidth / 2
                    
                    for element in allElements {
                        element.center = CGPoint(x: centerX, y: currentY + element.bounds.height / 2)
                        currentY += element.bounds.height + verticalSpacing
                    }
                }
            }
            
        default:
            // 支持更多元素的情况
            print("⚠️ 水印9: 支持\(enabledCount)个元素，使用默认布局")
            // 可以在这里添加更复杂的布局逻辑
            break
        }
    }
    
    /// 移除自定义水印9效果
    func remove(from previewContainer: UIView) {
        // 清理水印元素
        self.logoView?.removeFromSuperview()
        self.textLabel?.removeFromSuperview()
        self.preferenceLabel?.removeFromSuperview()
        self.signatureLabel?.removeFromSuperview()
        self.bottomItemsContainer?.removeFromSuperview()
        
        guard let wrapperView = self.scaledCustom9FrameWrapper,
              let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview
        else {
            self.scaledCustom9FrameWrapper?.removeFromSuperview()
            cleanupStoredState()
            return
        }

        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        
        wrapperView.removeFromSuperview()
        cleanupStoredState()
        print("✅ CustomWatermarkStyle9: 已移除自定义水印9。")
    }

    /// 辅助方法：恢复内容视图原始状态（用于apply失败时）
    private func restoreOriginalContentViewState() {
        guard let contentView = self.originalContentView,
              let superview = self.originalSuperview,
              let originalFrame = self.originalFrameInSuperview,
              let originalTransform = self.originalTransform,
              let originalIndex = self.originalIndexInSuperview else {
            cleanupStoredState() // 即使部分信息缺失，也尝试清理
            return
        }
        contentView.removeFromSuperview()
        contentView.transform = .identity
        contentView.frame = originalFrame
        contentView.transform = originalTransform
        if superview.subviews.count > originalIndex {
            superview.insertSubview(contentView, at: originalIndex)
        } else {
            superview.addSubview(contentView)
        }
        cleanupStoredState()
    }

    /// 辅助方法：清理存储的状态变量
    private func cleanupStoredState() {
        self.originalContentView = nil
        self.originalFrameInSuperview = nil
        self.originalTransform = nil
        self.originalSuperview = nil
        self.originalIndexInSuperview = nil
        self.scaledCustom9FrameWrapper = nil
        self.logoView = nil
        self.textLabel = nil
        self.preferenceLabel = nil
        self.signatureLabel = nil
        self.bottomItemsContainer = nil
        self.watermarkSettings = nil
    }
} 