#!/bin/bash

# 水印修复验证脚本
echo "🔧 水印修复验证"
echo "==============="

# 检查修复是否已应用
echo ""
echo "🔍 检查修复状态..."

WATERMARK_CONTROL_FILE="Lomo/Views/Edit/Components/WatermarkControlView.swift"

if [ -f "$WATERMARK_CONTROL_FILE" ]; then
    echo "✅ WatermarkControlView.swift 文件存在"
    
    # 检查是否移除了严格的window检查
    window_checks=$(grep -c "container\.window != nil" "$WATERMARK_CONTROL_FILE" 2>/dev/null || echo "0")
    if [ "$window_checks" -eq 0 ]; then
        echo "✅ 已移除严格的window检查"
    else
        echo "⚠️ 仍有 $window_checks 处window检查"
    fi
    
    # 检查是否保留了bounds检查
    bounds_checks=$(grep -c "container\.bounds\.width > 0.*container\.bounds\.height > 0" "$WATERMARK_CONTROL_FILE" 2>/dev/null || echo "0")
    if [ "$bounds_checks" -gt 0 ]; then
        echo "✅ 保留了bounds尺寸检查 ($bounds_checks 处)"
    else
        echo "❌ bounds尺寸检查缺失"
    fi
    
    # 检查是否添加了调试信息
    debug_info=$(grep -c "container\.bounds.*container\.window" "$WATERMARK_CONTROL_FILE" 2>/dev/null || echo "0")
    if [ "$debug_info" -gt 0 ]; then
        echo "✅ 添加了详细调试信息 ($debug_info 处)"
    else
        echo "⚠️ 调试信息缺失"
    fi
    
else
    echo "❌ WatermarkControlView.swift 文件不存在"
fi

# 分析修复内容
echo ""
echo "🎯 修复内容分析"
echo "==============="

echo ""
echo "1. 🔧 移除的严格检查:"
echo "   - container.window != nil (照片模式下可能为nil)"
echo "   - 保留 container.bounds 检查 (确保视图有效尺寸)"

echo ""
echo "2. 📊 添加的调试信息:"
echo "   - watermarkManager 状态"
echo "   - previewContainer 状态"
echo "   - container.bounds 尺寸"
echo "   - container.window 状态"

echo ""
echo "3. 🎯 问题根因:"
echo "   - 相机预览: 视图在活跃窗口中，有window引用 ✅"
echo "   - 照片模式: 新创建的视图，可能暂时没有window引用 ❌"
echo "   - 滤镜系统重构时改变了视图生命周期"

echo ""
echo "4. 🔧 解决方案:"
echo "   - 放宽检查条件，只要求有效的bounds尺寸"
echo "   - 添加详细日志帮助诊断问题"
echo "   - 保持水印功能的核心逻辑不变"

# 检查相关文件状态
echo ""
echo "🔍 相关文件检查"
echo "==============="

RELATED_FILES=(
    "Lomo/Managers/Edit/WatermarkStyles.swift"
    "Lomo/Managers/Edit/WatermarkStyles/CustomWatermarkStyle1.swift"
    "Lomo/Managers/Edit/MetalSpecialEffectsEngine.swift"
    "Lomo/Shaders/SpecialEffectsShaders.metal"
)

for file in "${RELATED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file - 文件缺失"
    fi
done

# 预期结果
echo ""
echo "📈 预期修复效果"
echo "==============="

echo ""
echo "修复前:"
echo "  📹 相机预览: 水印正常 ✅"
echo "  📸 照片模式: 水印失败 ❌ (container.window == nil)"

echo ""
echo "修复后:"
echo "  📹 相机预览: 水印正常 ✅"
echo "  📸 照片模式: 水印正常 ✅ (只检查bounds)"

echo ""
echo "🧪 测试建议"
echo "==========="

echo ""
echo "1. 运行应用并查看控制台日志"
echo "2. 测试相机预览模式:"
echo "   - 打开相机预览"
echo "   - 应用任意水印样式"
echo "   - 应该看到: ✅ [WatermarkControlView] 延迟初始化水印"

echo ""
echo "3. 测试照片模式:"
echo "   - 导入一张照片"
echo "   - 应用任意水印样式"
echo "   - 应该看到水印正常应用，而不是:"
echo "     ⚠️ WatermarkControlView.init: 预览容器无效或未完全加载"

echo ""
echo "4. 关注关键日志:"
echo "   - 🔍 [WatermarkControlView] 延迟初始化水印"
echo "   - ✅ WatermarkControlView.init: 应用水印样式"
echo "   - 任何 ❌ 错误信息"

echo ""
echo "🎉 如果修复成功，照片模式下的水印功能应该恢复正常！"
