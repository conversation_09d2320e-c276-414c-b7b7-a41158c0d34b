#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# EditViewModel异步调用修复验证脚本

echo "🧪 开始验证EditViewModel异步调用修复..."
echo "📍 项目路径: $(pwd)"

# 验证结果统计
TOTAL_CHECKS=6
PASSED_CHECKS=0

echo ""
echo "1️⃣ 检查异步调用是否已修复..."

# 检查是否还有同步调用异步方法的错误
echo "🔍 检查是否还有直接调用setOriginalImage的错误..."
SYNC_CALLS=$(grep -n "self\.filterService\.setOriginalImage(" Lomo/ViewModels/Edit/EditViewModel.swift | grep -v "await" | wc -l)
if [ "$SYNC_CALLS" -eq 0 ]; then
    echo "✅ 已修复所有同步调用异步方法的错误"
    ((PASSED_CHECKS++))
else
    echo "❌ 仍然存在同步调用异步方法的错误: $SYNC_CALLS 处"
    grep -n "self\.filterService\.setOriginalImage(" Lomo/ViewModels/Edit/EditViewModel.swift | grep -v "await"
fi

echo ""
echo "2️⃣ 检查Task包装是否正确..."

# 检查是否正确使用Task包装异步调用
echo "🔍 检查Task包装的使用..."
TASK_COUNT=$(grep -c "Task {" Lomo/ViewModels/Edit/EditViewModel.swift)
if [ "$TASK_COUNT" -ge 3 ]; then
    echo "✅ 正确使用了Task包装异步调用 ($TASK_COUNT 处)"
    ((PASSED_CHECKS++))
else
    echo "❌ Task包装使用不足: $TASK_COUNT 处"
fi

echo ""
echo "3️⃣ 检查错误处理是否完整..."

# 检查是否有正确的错误处理
echo "🔍 检查异步调用的错误处理..."
ERROR_HANDLING_COUNT=$(grep -c "} catch {" Lomo/ViewModels/Edit/EditViewModel.swift)
if [ "$ERROR_HANDLING_COUNT" -ge 3 ]; then
    echo "✅ 包含完整的错误处理机制 ($ERROR_HANDLING_COUNT 处)"
    ((PASSED_CHECKS++))
else
    echo "❌ 错误处理不完整: $ERROR_HANDLING_COUNT 处"
fi

echo ""
echo "4️⃣ 检查await关键字使用..."

# 检查是否正确使用await关键字
echo "🔍 检查await关键字的使用..."
AWAIT_COUNT=$(grep -c "try await.*setOriginalImage" Lomo/ViewModels/Edit/EditViewModel.swift)
if [ "$AWAIT_COUNT" -ge 3 ]; then
    echo "✅ 正确使用了await关键字 ($AWAIT_COUNT 处)"
    ((PASSED_CHECKS++))
else
    echo "❌ await关键字使用不足: $AWAIT_COUNT 处"
fi

echo ""
echo "5️⃣ 检查调试日志完整性..."

# 检查调试日志是否完整
echo "🔍 检查调试日志..."
SUCCESS_LOG_COUNT=$(grep -c "filterService.setOriginalImage() 调用完成" Lomo/ViewModels/Edit/EditViewModel.swift)
ERROR_LOG_COUNT=$(grep -c "filterService.setOriginalImage() 失败" Lomo/ViewModels/Edit/EditViewModel.swift)

if [ "$SUCCESS_LOG_COUNT" -ge 3 ] && [ "$ERROR_LOG_COUNT" -ge 3 ]; then
    echo "✅ 调试日志完整 (成功: $SUCCESS_LOG_COUNT, 错误: $ERROR_LOG_COUNT)"
    ((PASSED_CHECKS++))
else
    echo "❌ 调试日志不完整 (成功: $SUCCESS_LOG_COUNT, 错误: $ERROR_LOG_COUNT)"
fi

echo ""
echo "6️⃣ 语法验证..."

# 检查语法是否正确
echo "🔨 检查EditViewModel语法..."
if swift -frontend -parse Lomo/ViewModels/Edit/EditViewModel.swift 2>/dev/null; then
    echo "✅ EditViewModel.swift 语法正确"
    ((PASSED_CHECKS++))
else
    echo "❌ EditViewModel.swift 语法错误"
fi

echo ""
echo "7️⃣ 修复验证总结..."
echo "📊 验证结果: $PASSED_CHECKS/$TOTAL_CHECKS 项检查通过"

if [ $PASSED_CHECKS -eq $TOTAL_CHECKS ]; then
    echo ""
    echo "🎉 EditViewModel异步调用修复验证完全成功！"
    echo ""
    echo "✅ 修复成果："
    echo "• 消除了所有同步调用异步方法的错误"
    echo "• 正确使用Task包装异步调用"
    echo "• 包含完整的错误处理机制"
    echo "• 正确使用await关键字"
    echo "• 调试日志完整"
    echo "• 语法检查通过"
    echo ""
    echo "🎯 解决的编译错误："
    echo "✅ 'async' call in a function that does not support concurrency → 已解决"
    echo "✅ Call can throw, but it is not marked with 'try' and the error is not handled → 已解决"
    echo ""
    echo "🎯 修复方案："
    echo "📁 修复前: filterService.setOriginalImage(image) (同步调用异步方法)"
    echo "📁 修复后: Task { try await filterService.setOriginalImage(image) } (正确异步调用)"
    echo "📁 效果: 类型安全 + 异步兼容 + 错误处理"
    echo ""
    echo "🏁 EditViewModel异步调用修复验证完成！"
    exit 0
else
    echo ""
    echo "❌ EditViewModel异步调用修复验证未完全通过，需要进一步检查"
    echo "通过: $PASSED_CHECKS/$TOTAL_CHECKS"
    exit 1
fi