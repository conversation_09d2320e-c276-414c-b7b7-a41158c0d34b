import Foundation

/// XMP文件解析器
/// 用于从XMP文件中提取滤镜参数并转换为FilterPreset
class XMPParser {
    
    /// XMP参数映射结构
    struct XMPParameters {
        // 基础调整
        var exposure: Float = 0.0
        var contrast: Float = 0.0
        var highlights: Float = 0.0
        var shadows: Float = 0.0
        var whites: Float = 0.0
        var blacks: Float = 0.0
        var brightness: Float = 0.0
        
        // 色彩调整
        var temperature: Float = 0.0
        var tint: Float = 0.0
        var saturation: Float = 0.0
        var vibrance: Float = 0.0
        
        // 色调曲线
        var parametricShadows: Float = 0.0
        var parametricHighlights: Float = 0.0
        var parametricShadowSplit: Float = 25.0
        var parametricMidtoneSplit: Float = 50.0
        var parametricHighlightSplit: Float = 75.0
        
        // HSL调整
        var hueAdjustmentRed: Float = 0.0
        var hueAdjustmentOrange: Float = 0.0
        var hueAdjustmentYellow: Float = 0.0
        var hueAdjustmentGreen: Float = 0.0
        var hueAdjustmentAqua: Float = 0.0
        var hueAdjustmentBlue: Float = 0.0
        var hueAdjustmentPurple: Float = 0.0
        var hueAdjustmentMagenta: Float = 0.0
        
        var saturationAdjustmentRed: Float = 0.0
        var saturationAdjustmentOrange: Float = 0.0
        var saturationAdjustmentYellow: Float = 0.0
        var saturationAdjustmentGreen: Float = 0.0
        var saturationAdjustmentAqua: Float = 0.0
        var saturationAdjustmentBlue: Float = 0.0
        var saturationAdjustmentPurple: Float = 0.0
        var saturationAdjustmentMagenta: Float = 0.0
        
        var luminanceAdjustmentRed: Float = 0.0
        var luminanceAdjustmentOrange: Float = 0.0
        var luminanceAdjustmentYellow: Float = 0.0
        var luminanceAdjustmentGreen: Float = 0.0
        var luminanceAdjustmentAqua: Float = 0.0
        var luminanceAdjustmentBlue: Float = 0.0
        var luminanceAdjustmentPurple: Float = 0.0
        var luminanceAdjustmentMagenta: Float = 0.0
        
        // 分离色调
        var splitToningShadowHue: Float = 0.0
        var splitToningShadowSaturation: Float = 0.0
        var splitToningHighlightHue: Float = 0.0
        var splitToningHighlightSaturation: Float = 0.0
        var splitToningBalance: Float = 0.0
        
        // 细节
        var sharpness: Float = 0.0
        var clarity: Float = 0.0
        var dehaze: Float = 0.0
        var texture: Float = 0.0
        
        // 效果
        var vignetteAmount: Float = 0.0
        var vignetteRadius: Float = 50.0
        var vignetteMidpoint: Float = 50.0
        var vignetteFeather: Float = 50.0
        var vignetteRoundness: Float = 0.0
        
        // 颗粒
        var grainAmount: Float = 0.0
        var grainSize: Float = 25.0
        var grainRoughness: Float = 50.0
    }
    
    /// 从XMP文件内容解析参数
    /// - Parameter xmpContent: XMP文件的字符串内容
    /// - Returns: 解析出的XMP参数
    static func parseXMP(from xmpContent: String) -> XMPParameters {
        var parameters = XMPParameters()
        
        // 解析基础调整参数
        parameters.exposure = extractFloatValue(from: xmpContent, key: "crs:Exposure") ?? 0.0
        parameters.contrast = extractFloatValue(from: xmpContent, key: "crs:Contrast") ?? 0.0
        parameters.highlights = extractFloatValue(from: xmpContent, key: "crs:Highlights") ?? 0.0
        parameters.shadows = extractFloatValue(from: xmpContent, key: "crs:Shadows") ?? 0.0
        parameters.whites = extractFloatValue(from: xmpContent, key: "crs:Whites") ?? 0.0
        parameters.blacks = extractFloatValue(from: xmpContent, key: "crs:Blacks") ?? 0.0
        parameters.brightness = extractFloatValue(from: xmpContent, key: "crs:Brightness") ?? 0.0
        
        // 解析色彩调整参数
        parameters.temperature = extractFloatValue(from: xmpContent, key: "crs:Temperature") ?? 0.0
        parameters.tint = extractFloatValue(from: xmpContent, key: "crs:Tint") ?? 0.0
        parameters.saturation = extractFloatValue(from: xmpContent, key: "crs:Saturation") ?? 0.0
        parameters.vibrance = extractFloatValue(from: xmpContent, key: "crs:Vibrance") ?? 0.0
        
        // 解析色调曲线参数
        parameters.parametricShadows = extractFloatValue(from: xmpContent, key: "crs:ParametricShadows") ?? 0.0
        parameters.parametricHighlights = extractFloatValue(from: xmpContent, key: "crs:ParametricHighlights") ?? 0.0
        
        // 解析HSL调整参数
        parameters.hueAdjustmentRed = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentRed") ?? 0.0
        parameters.hueAdjustmentOrange = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentOrange") ?? 0.0
        parameters.hueAdjustmentYellow = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentYellow") ?? 0.0
        parameters.hueAdjustmentGreen = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentGreen") ?? 0.0
        parameters.hueAdjustmentAqua = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentAqua") ?? 0.0
        parameters.hueAdjustmentBlue = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentBlue") ?? 0.0
        parameters.hueAdjustmentPurple = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentPurple") ?? 0.0
        parameters.hueAdjustmentMagenta = extractFloatValue(from: xmpContent, key: "crs:HueAdjustmentMagenta") ?? 0.0
        
        parameters.saturationAdjustmentRed = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentRed") ?? 0.0
        parameters.saturationAdjustmentOrange = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentOrange") ?? 0.0
        parameters.saturationAdjustmentYellow = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentYellow") ?? 0.0
        parameters.saturationAdjustmentGreen = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentGreen") ?? 0.0
        parameters.saturationAdjustmentAqua = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentAqua") ?? 0.0
        parameters.saturationAdjustmentBlue = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentBlue") ?? 0.0
        parameters.saturationAdjustmentPurple = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentPurple") ?? 0.0
        parameters.saturationAdjustmentMagenta = extractFloatValue(from: xmpContent, key: "crs:SaturationAdjustmentMagenta") ?? 0.0
        
        parameters.luminanceAdjustmentRed = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentRed") ?? 0.0
        parameters.luminanceAdjustmentOrange = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentOrange") ?? 0.0
        parameters.luminanceAdjustmentYellow = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentYellow") ?? 0.0
        parameters.luminanceAdjustmentGreen = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentGreen") ?? 0.0
        parameters.luminanceAdjustmentAqua = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentAqua") ?? 0.0
        parameters.luminanceAdjustmentBlue = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentBlue") ?? 0.0
        parameters.luminanceAdjustmentPurple = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentPurple") ?? 0.0
        parameters.luminanceAdjustmentMagenta = extractFloatValue(from: xmpContent, key: "crs:LuminanceAdjustmentMagenta") ?? 0.0
        
        // 解析分离色调参数
        parameters.splitToningShadowHue = extractFloatValue(from: xmpContent, key: "crs:SplitToningShadowHue") ?? 0.0
        parameters.splitToningShadowSaturation = extractFloatValue(from: xmpContent, key: "crs:SplitToningShadowSaturation") ?? 0.0
        parameters.splitToningHighlightHue = extractFloatValue(from: xmpContent, key: "crs:SplitToningHighlightHue") ?? 0.0
        parameters.splitToningHighlightSaturation = extractFloatValue(from: xmpContent, key: "crs:SplitToningHighlightSaturation") ?? 0.0
        parameters.splitToningBalance = extractFloatValue(from: xmpContent, key: "crs:SplitToningBalance") ?? 0.0
        
        // 解析细节参数
        parameters.sharpness = extractFloatValue(from: xmpContent, key: "crs:Sharpness") ?? 0.0
        parameters.clarity = extractFloatValue(from: xmpContent, key: "crs:Clarity") ?? 0.0
        parameters.dehaze = extractFloatValue(from: xmpContent, key: "crs:Dehaze") ?? 0.0
        parameters.texture = extractFloatValue(from: xmpContent, key: "crs:Texture") ?? 0.0
        
        // 解析效果参数
        parameters.vignetteAmount = extractFloatValue(from: xmpContent, key: "crs:VignetteAmount") ?? 0.0
        parameters.vignetteRadius = extractFloatValue(from: xmpContent, key: "crs:VignetteRadius") ?? 50.0
        parameters.vignetteMidpoint = extractFloatValue(from: xmpContent, key: "crs:VignetteMidpoint") ?? 50.0
        parameters.vignetteFeather = extractFloatValue(from: xmpContent, key: "crs:VignetteFeather") ?? 50.0
        parameters.vignetteRoundness = extractFloatValue(from: xmpContent, key: "crs:VignetteRoundness") ?? 0.0
        
        // 解析颗粒参数
        parameters.grainAmount = extractFloatValue(from: xmpContent, key: "crs:GrainAmount") ?? 0.0
        parameters.grainSize = extractFloatValue(from: xmpContent, key: "crs:GrainSize") ?? 25.0
        parameters.grainRoughness = extractFloatValue(from: xmpContent, key: "crs:GrainRoughness") ?? 50.0
        
        return parameters
    }
    
    /// 从XMP内容中提取浮点数值
    /// - Parameters:
    ///   - content: XMP文件内容
    ///   - key: 要查找的键名
    /// - Returns: 提取的浮点数值，如果未找到则返回nil
    private static func extractFloatValue(from content: String, key: String) -> Float? {
        // 匹配模式：key="value" 或 key='value'
        let patterns = [
            "\(key)=\"([^\"]+)\"",
            "\(key)='([^']+)'",
            "\(key)>([^<]+)<"
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               let match = regex.firstMatch(in: content, options: [], range: NSRange(content.startIndex..., in: content)),
               let range = Range(match.range(at: 1), in: content) {
                let valueString = String(content[range])
                return Float(valueString)
            }
        }

        return nil
    }

    /// 从XMP内容中提取字符串值
    /// - Parameters:
    ///   - content: XMP文件内容
    ///   - key: 要查找的键名
    /// - Returns: 提取的字符串值，如果未找到则返回nil
    private static func extractStringValue(from content: String, key: String) -> String? {
        let patterns = [
            "\(key)=\"([^\"]+)\"",
            "\(key)='([^']+)'",
            "\(key)>([^<]+)<"
        ]

        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               let match = regex.firstMatch(in: content, options: [], range: NSRange(content.startIndex..., in: content)),
               let range = Range(match.range(at: 1), in: content) {
                return String(content[range])
            }
        }

        return nil
    }

    /// 分析XMP文件的基本信息
    /// - Parameter xmpContent: XMP文件内容
    /// - Returns: XMP文件的基本信息
    static func analyzeXMPInfo(from xmpContent: String) -> [String: String] {
        var info: [String: String] = [:]

        info["PresetType"] = extractStringValue(from: xmpContent, key: "crs:PresetType") ?? "Unknown"
        info["Version"] = extractStringValue(from: xmpContent, key: "crs:Version") ?? "Unknown"
        info["ProcessVersion"] = extractStringValue(from: xmpContent, key: "crs:ProcessVersion") ?? "Unknown"
        info["CameraProfile"] = extractStringValue(from: xmpContent, key: "crs:CameraProfile") ?? "Unknown"
        info["ToneMapStrength"] = extractStringValue(from: xmpContent, key: "crs:ToneMapStrength") ?? "0"
        info["ConvertToGrayscale"] = extractStringValue(from: xmpContent, key: "crs:ConvertToGrayscale") ?? "False"
        info["LookTable"] = extractStringValue(from: xmpContent, key: "crs:LookTable") ?? ""

        return info
    }
    
    /// 将XMP参数转换为FilterPreset
    /// - Parameters:
    ///   - xmpParams: XMP参数
    ///   - name: 预设名称
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: 转换后的FilterPreset
    static func convertToFilterPreset(from xmpParams: XMPParameters, name: String, type: FilterPresetType, index: Int) -> FilterPreset {
        return FilterPreset(
            name: name,
            type: type,
            index: index,
            // 基础参数映射
            exposure: xmpParams.exposure,
            contrast: xmpParams.contrast,
            saturation: xmpParams.saturation,
            brightness: xmpParams.brightness,
            gamma: 1.0, // XMP中没有直接的gamma参数
            temperature: xmpParams.temperature,
            tint: xmpParams.tint,
            highlights: xmpParams.highlights,
            shadows: xmpParams.shadows,
            hue: 0.0, // 需要从HSL调整中计算
            vibrance: xmpParams.vibrance,
            sharpness: xmpParams.sharpness,
            clarity: xmpParams.clarity,
            vignetteIntensity: xmpParams.vignetteAmount,
            vignetteRadius: xmpParams.vignetteRadius / 50.0, // 转换范围从0-100到0-2
            highlightTone: xmpParams.parametricHighlights,
            midtoneTone: 0.0, // 移除参数化暗部/亮部后设为默认值
            shadowTone: xmpParams.parametricShadows,
            highlightHue: xmpParams.splitToningHighlightHue,
            highlightSaturation: xmpParams.splitToningHighlightSaturation,
            shadowHue: xmpParams.splitToningShadowHue,
            shadowSaturation: xmpParams.splitToningShadowSaturation
        )
    }
    
    /// 打印XMP参数详情（用于调试）
    /// - Parameter xmpParams: XMP参数
    static func printXMPParameters(_ xmpParams: XMPParameters) {
        print("=== XMP参数解析结果 ===")
        print("基础调整:")
        print("  曝光: \(xmpParams.exposure)")
        print("  对比度: \(xmpParams.contrast)")
        print("  高光: \(xmpParams.highlights)")
        print("  阴影: \(xmpParams.shadows)")
        print("  亮度: \(xmpParams.brightness)")
        
        print("色彩调整:")
        print("  色温: \(xmpParams.temperature)")
        print("  色调: \(xmpParams.tint)")
        print("  饱和度: \(xmpParams.saturation)")
        print("  自然饱和度: \(xmpParams.vibrance)")
        
        print("细节:")
        print("  锐化: \(xmpParams.sharpness)")
        print("  清晰度: \(xmpParams.clarity)")
        print("  去雾: \(xmpParams.dehaze)")
        
        print("效果:")
        print("  渐晕强度: \(xmpParams.vignetteAmount)")
        print("  渐晕半径: \(xmpParams.vignetteRadius)")
        
        print("分离色调:")
        print("  高光色相: \(xmpParams.splitToningHighlightHue)")
        print("  高光饱和度: \(xmpParams.splitToningHighlightSaturation)")
        print("  阴影色相: \(xmpParams.splitToningShadowHue)")
        print("  阴影饱和度: \(xmpParams.splitToningShadowSaturation)")
        print("=====================")
    }
}
