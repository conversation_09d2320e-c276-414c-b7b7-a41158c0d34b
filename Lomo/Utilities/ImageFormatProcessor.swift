import Foundation
import UIKit
import CoreImage
import ImageIO

/// 图像格式处理器 - 处理不同格式的图像预处理
class ImageFormatProcessor {
    
    // MARK: - 图像格式枚举
    enum ImageFormat {
        case jpeg
        case png
        case heif
        case tiff
        case raw
        case proRAW
        case unknown
        
        var displayName: String {
            switch self {
            case .jpeg: return "JPEG"
            case .png: return "PNG"
            case .heif: return "HEIF"
            case .tiff: return "TIFF"
            case .raw: return "RAW"
            case .proRAW: return "ProRAW"
            case .unknown: return "Unknown"
            }
        }
        
        var isLinearFormat: Bool {
            switch self {
            case .raw, .proRAW: return true
            case .jpeg, .png, .heif: return false
            case .tiff: return false // TIFF可能是线性的，需要进一步检测
            case .unknown: return false
            }
        }
    }
    
    // MARK: - 色彩空间信息
    struct ColorSpaceInfo {
        let isLinear: Bool
        let bitDepth: Int
        let colorSpaceName: String?
        let format: ImageFormat
        
        var description: String {
            return "格式: \(format.displayName), 线性: \(isLinear ? "是" : "否"), 位深度: \(bitDepth)位, 色彩空间: \(colorSpaceName ?? "未知")"
        }
    }
    
    // MARK: - 单例
    static let shared = ImageFormatProcessor()
    private init() {}
    
    // MARK: - 格式检测
    
    /// 从URL检测图像格式
    func detectImageFormat(from url: URL) -> ImageFormat {
        let pathExtension = url.pathExtension.lowercased()
        
        switch pathExtension {
        case "jpg", "jpeg":
            return .jpeg
        case "png":
            return .png
        case "heif", "heic":
            return .heif
        case "tiff", "tif":
            return .tiff
        case "dng":
            return .raw
        case "cr2", "nef", "arw", "orf", "rw2", "pef", "srw", "raf":
            return .raw
        default:
            // 尝试从图像数据检测
            return detectFormatFromImageData(url: url)
        }
    }
    
    /// 从图像数据检测格式
    private func detectFormatFromImageData(url: URL) -> ImageFormat {
        guard let imageSource = CGImageSourceCreateWithURL(url as CFURL, nil),
              let imageType = CGImageSourceGetType(imageSource) else {
            return .unknown
        }
        
        let typeString = imageType as String
        
        if typeString.contains("jpeg") {
            return .jpeg
        } else if typeString.contains("png") {
            return .png
        } else if typeString.contains("heif") || typeString.contains("heic") {
            return .heif
        } else if typeString.contains("tiff") {
            return .tiff
        } else if typeString.contains("raw") || typeString.contains("dng") {
            return .raw
        } else {
            return .unknown
        }
    }
    
    /// 分析图像的色彩空间信息
    func analyzeColorSpace(_ image: UIImage) -> ColorSpaceInfo {
        guard let cgImage = image.cgImage else {
            return ColorSpaceInfo(isLinear: false, bitDepth: 8, colorSpaceName: nil, format: .unknown)
        }
        
        let colorSpace = cgImage.colorSpace
        let bitDepth = cgImage.bitsPerComponent
        let colorSpaceName = colorSpace?.name as String?
        
        // 检测是否为线性色彩空间
        let isLinear = isLinearColorSpace(colorSpace)
        
        // 尝试检测格式
        let format = detectFormatFromCGImage(cgImage)
        
        return ColorSpaceInfo(
            isLinear: isLinear,
            bitDepth: bitDepth,
            colorSpaceName: colorSpaceName,
            format: format
        )
    }
    
    /// 检测是否为线性色彩空间
    private func isLinearColorSpace(_ colorSpace: CGColorSpace?) -> Bool {
        guard let colorSpace = colorSpace,
              let name = colorSpace.name else {
            return false
        }
        
        let nameString = name as String
        
        // 检测线性色彩空间的标识
        return nameString.contains("Linear") ||
               nameString.contains("linear") ||
               nameString == (CGColorSpace.linearSRGB as CFString) as String ||
               nameString == (CGColorSpace.extendedLinearSRGB as CFString) as String
    }
    
    /// 从CGImage检测格式
    private func detectFormatFromCGImage(_ cgImage: CGImage) -> ImageFormat {
        let bitDepth = cgImage.bitsPerComponent
        let colorSpace = cgImage.colorSpace
        
        // 基于位深度和色彩空间推测格式
        if bitDepth >= 12 && isLinearColorSpace(colorSpace) {
            return .raw
        } else if bitDepth == 8 {
            return .jpeg // 大多数8位图像是JPEG或类似格式
        } else {
            return .unknown
        }
    }
    
    // MARK: - 图像预处理
    
    /// 智能预处理图像 - 确保所有格式都能正确处理滤镜
    func preprocessImageForFiltering(_ image: UIImage) -> UIImage {
        print("🎨 [ImageFormatProcessor] 开始智能预处理")
        
        // 分析图像色彩空间
        let colorSpaceInfo = analyzeColorSpace(image)
        print("🎨 [ImageFormatProcessor] 图像信息: \(colorSpaceInfo.description)")
        
        // 检测是否需要线性到sRGB转换
        if colorSpaceInfo.isLinear || ImageColorSpaceAnalyzer.isLinearImage(image) {
            print("🎨 [ImageFormatProcessor] 检测到线性图像，开始转换到sRGB")
            let srgbImage = convertLinearToSRGB(image)
            print("🎨 [ImageFormatProcessor] 线性到sRGB转换完成")
            return srgbImage
        } else {
            print("🎨 [ImageFormatProcessor] 图像已在sRGB空间，无需转换")
            return image
        }
    }
    
    /// 线性图像转换到sRGB
    private func convertLinearToSRGB(_ image: UIImage) -> UIImage {
        guard let ciImage = CIImage(image: image) else {
            print("⚠️ [ImageFormatProcessor] 无法创建CIImage，返回原图")
            return image
        }
        
        // 使用Core Image的线性到sRGB转换滤镜
        let srgbImage = ciImage.applyingFilter("CILinearToSRGBToneCurve")
        
        // 转换回UIImage
        let context = CIContext()
        guard let cgImage = context.createCGImage(srgbImage, from: srgbImage.extent) else {
            print("⚠️ [ImageFormatProcessor] sRGB转换失败，返回原图")
            return image
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    // MARK: - 高级处理
    
    /// 为不同格式优化处理参数
    func getOptimizedProcessingParameters(for format: ImageFormat) -> ProcessingParameters {
        switch format {
        case .jpeg, .png, .heif:
            // sRGB格式 - 标准处理
            return ProcessingParameters(
                useLinearProcessing: false,
                gammaCorrection: 1.0,
                colorSpaceConversion: .none,
                bitDepthOptimization: .standard
            )
            
        case .raw, .proRAW:
            // 线性格式 - 需要特殊处理
            return ProcessingParameters(
                useLinearProcessing: true,
                gammaCorrection: 2.2,
                colorSpaceConversion: .linearToSRGB,
                bitDepthOptimization: .highPrecision
            )
            
        case .tiff:
            // TIFF格式 - 需要动态检测
            return ProcessingParameters(
                useLinearProcessing: false, // 默认false，实际使用时动态检测
                gammaCorrection: 1.0,
                colorSpaceConversion: .auto,
                bitDepthOptimization: .adaptive
            )
            
        case .unknown:
            // 未知格式 - 保守处理
            return ProcessingParameters(
                useLinearProcessing: false,
                gammaCorrection: 1.0,
                colorSpaceConversion: .none,
                bitDepthOptimization: .standard
            )
        }
    }
    
    /// 检测图像是否需要特殊处理
    func requiresSpecialProcessing(_ image: UIImage) -> Bool {
        let colorSpaceInfo = analyzeColorSpace(image)
        
        // 线性图像需要特殊处理
        if colorSpaceInfo.isLinear {
            return true
        }
        
        // 高位深度图像需要特殊处理
        if colorSpaceInfo.bitDepth > 8 {
            return true
        }
        
        // RAW格式需要特殊处理
        if colorSpaceInfo.format.isLinearFormat {
            return true
        }
        
        return false
    }
}

// MARK: - 处理参数结构体

/// 图像处理参数
struct ProcessingParameters {
    let useLinearProcessing: Bool
    let gammaCorrection: Float
    let colorSpaceConversion: ColorSpaceConversion
    let bitDepthOptimization: BitDepthOptimization
    
    enum ColorSpaceConversion {
        case none
        case linearToSRGB
        case srgbToLinear
        case auto
    }
    
    enum BitDepthOptimization {
        case standard    // 8位标准处理
        case highPrecision // 16位高精度处理
        case adaptive    // 自适应处理
    }
}
