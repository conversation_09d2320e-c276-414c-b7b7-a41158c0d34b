import Foundation
import CoreImage

/// CUBE LUT文件管理器
/// 用于自动读取和管理CUBE格式的3D LUT文件
class CubeLUTManager {
    
    /// LUT文件存放目录（相对于项目根目录）
    static let lutDirectory = "CubeLUTs"
    
    /// LUT预设信息
    struct LUTPreset {
        let id: String
        let name: String
        let type: FilterPresetType
        let index: Int
        let filePath: String
        let lutData: CubeLUTParser.LUTData
        let filter: CIFilter
        
        init?(filePath: String, type: FilterPresetType, index: Int, name: String) {
            guard let content = try? String(contentsOfFile: filePath, encoding: .utf8),
                  let lutData = CubeLUTParser.parseCubeLUT(from: content),
                  let filter = CubeLUTParser.createColorCubeFilter(from: lutData) else {
                return nil
            }
            
            self.id = UUID().uuidString
            self.name = name
            self.type = type
            self.index = index
            self.filePath = filePath
            self.lutData = lutData
            self.filter = filter
        }
    }
    
    /// 获取LUT文件目录的完整路径
    static var lutDirectoryPath: String {
        let projectRoot = FileManager.default.currentDirectoryPath
        return "\(projectRoot)/\(lutDirectory)"
    }
    
    /// 创建LUT文件目录（如果不存在）
    static func createLUTDirectory() {
        let fileManager = FileManager.default
        let path = lutDirectoryPath
        
        if !fileManager.fileExists(atPath: path) {
            do {
                try fileManager.createDirectory(atPath: path, withIntermediateDirectories: true, attributes: nil)
                print("✅ 已创建CUBE LUT文件目录: \(path)")
                
                // 创建说明文件
                let readmeContent = """
                # CUBE LUT文件夹
                
                ## 🎯 使用方法
                
                ### 1. 放置CUBE文件
                将您的.cube文件直接拖拽到这个文件夹中
                
                ### 2. 文件命名规则
                文件名格式：`[预设类型]_[索引]_[名称].cube`
                
                **支持的预设类型：**
                - `polaroid` - 宝丽来
                - `film` - 胶卷  
                - `vintage` - 复古
                - `fashion` - 时尚
                - `ins` - INS
                
                **索引范围：** 0-4 (对应预设1-5)
                
                ### 3. 文件名示例
                ```
                polaroid_0_电影胶片.cube
                film_1_Kodak_5218.cube
                vintage_2_复古暖调.cube
                fashion_3_时尚冷调.cube
                ins_4_INS橙青.cube
                ```
                
                ### 4. 自动处理
                在代码中调用：
                ```swift
                // 一键处理所有CUBE文件
                CubeLUTManager.autoProcessLUTFiles()
                
                // 或者分步操作
                CubeLUTManager.listLUTFiles()        // 列出文件
                CubeLUTManager.processAllLUTFiles()  // 处理所有文件
                ```
                
                ## 📋 CUBE文件格式
                
                CUBE文件是3D LUT的标准格式，包含：
                - 标题信息
                - LUT尺寸（通常是17³、33³或65³）
                - 颜色映射数据
                
                ## 🎨 LUT效果类型
                
                - **电影LUT**: 模拟电影胶片色彩
                - **调色LUT**: 专业调色师制作的风格
                - **创意LUT**: 艺术化色彩效果
                - **校正LUT**: 色彩校正和匹配
                
                ## ⚠️ 注意事项
                
                - 文件必须是有效的CUBE格式
                - 支持的LUT尺寸：2³ 到 256³
                - 文件编码必须是UTF-8
                - 大尺寸LUT会影响性能
                
                ## 💡 获取LUT文件
                
                - Adobe官方LUT包
                - 第三方调色师分享
                - 电影制作公司发布
                - 在线LUT资源网站
                """
                
                let readmePath = "\(path)/README.md"
                try readmeContent.write(toFile: readmePath, atomically: true, encoding: .utf8)
                
            } catch {
                print("❌ 创建LUT文件目录失败: \(error)")
            }
        }
    }
    
    /// 获取所有CUBE文件
    /// - Returns: CUBE文件路径数组
    static func getAllLUTFiles() -> [String] {
        let fileManager = FileManager.default
        let path = lutDirectoryPath
        
        guard fileManager.fileExists(atPath: path) else {
            print("⚠️ LUT文件目录不存在: \(path)")
            return []
        }
        
        do {
            let files = try fileManager.contentsOfDirectory(atPath: path)
            let cubeFiles = files.filter { $0.lowercased().hasSuffix(".cube") }
            return cubeFiles.map { "\(path)/\($0)" }
        } catch {
            print("❌ 读取LUT文件目录失败: \(error)")
            return []
        }
    }
    
    /// 解析文件名获取预设信息
    /// - Parameter fileName: 文件名
    /// - Returns: (预设类型, 索引, 名称) 或 nil
    static func parseFileName(_ fileName: String) -> (FilterPresetType, Int, String)? {
        // 移除.cube扩展名
        let nameWithoutExtension = fileName.replacingOccurrences(of: ".cube", with: "")
        
        // 分割文件名：type_index_name
        let components = nameWithoutExtension.components(separatedBy: "_")
        
        guard components.count >= 3 else {
            print("⚠️ 文件名格式不正确: \(fileName)")
            print("   正确格式: [类型]_[索引]_[名称].cube")
            return nil
        }
        
        let typeString = components[0].lowercased()
        let indexString = components[1]
        let name = components[2...].joined(separator: "_")
        
        // 解析预设类型
        let presetType: FilterPresetType
        switch typeString {
        case "polaroid":
            presetType = .polaroid
        case "film":
            presetType = .film
        case "vintage":
            presetType = .vintage
        case "fashion":
            presetType = .fashion
        case "ins":
            presetType = .ins
        default:
            print("❌ 不支持的预设类型: \(typeString)")
            print("   支持的类型: polaroid, film, vintage, fashion, ins")
            return nil
        }
        
        // 解析索引
        guard let index = Int(indexString), index >= 0, index <= 4 else {
            print("❌ 无效的预设索引: \(indexString)")
            print("   索引范围: 0-4")
            return nil
        }
        
        return (presetType, index, name)
    }
    
    /// 处理单个CUBE文件
    /// - Parameter filePath: CUBE文件路径
    /// - Returns: 是否成功处理
    static func processLUTFile(_ filePath: String) -> Bool {
        let fileName = URL(fileURLWithPath: filePath).lastPathComponent
        
        // 解析文件名
        guard let (presetType, index, name) = parseFileName(fileName) else {
            return false
        }
        
        print("🎨 处理CUBE LUT文件: \(fileName)")
        print("   预设类型: \(presetType.displayName)")
        print("   预设索引: \(index)")
        print("   预设名称: \(name)")
        
        // 读取和验证CUBE文件
        guard let content = try? String(contentsOfFile: filePath, encoding: .utf8) else {
            print("❌ 无法读取文件: \(fileName)")
            return false
        }
        
        // 验证CUBE格式
        let validation = CubeLUTParser.validateCubeFormat(content)
        if !validation.isValid {
            print("❌ CUBE文件格式错误:")
            for error in validation.errors {
                print("   - \(error)")
            }
            return false
        }
        
        // 解析LUT数据
        guard let lutData = CubeLUTParser.parseCubeLUT(from: content) else {
            print("❌ 解析LUT数据失败")
            return false
        }
        
        // 打印LUT信息
        CubeLUTParser.printLUTInfo(lutData)
        
        // 分析LUT特征
        let analysis = CubeLUTParser.analyzeLUTCharacteristics(lutData)
        CubeLUTParser.printLUTAnalysis(analysis)
        
        // 创建LUT预设
        guard let lutPreset = LUTPreset(filePath: filePath, type: presetType, index: index, name: name) else {
            print("❌ 创建LUT预设失败")
            return false
        }
        
        // 将LUT转换为等效的滤镜参数
        let filterParams = convertLUTToFilterParameters(lutData, analysis: analysis)
        
        // 更新预设
        let success = updatePresetWithLUT(lutPreset, filterParams: filterParams)
        
        if success {
            print("✅ 成功处理: \(fileName)")
        } else {
            print("❌ 处理失败: \(fileName)")
        }
        
        return success
    }
    
    /// 将LUT特征转换为滤镜参数
    /// - Parameters:
    ///   - lutData: LUT数据
    ///   - analysis: LUT分析结果
    /// - Returns: 等效的滤镜参数
    private static func convertLUTToFilterParameters(_ lutData: CubeLUTParser.LUTData, analysis: [String: Any]) -> FilterParameters {
        let params = FilterParameters()
        
        // 根据LUT分析结果设置参数
        let redShift = analysis["redShift"] as? Float ?? 0
        let greenShift = analysis["greenShift"] as? Float ?? 0
        let blueShift = analysis["blueShift"] as? Float ?? 0
        
        // 色温调整（基于红蓝偏移）
        if redShift > blueShift {
            params.temperature = (redShift - blueShift) * 500 // 暖色调
        } else {
            params.temperature = (redShift - blueShift) * 500 // 冷色调
        }
        
        // 色调调整（基于绿色偏移）
        params.tint = greenShift * 100
        
        // 饱和度调整（基于整体色彩偏移）
        let totalShift = abs(redShift) + abs(greenShift) + abs(blueShift)
        if totalShift > 0.1 {
            params.saturation = min(totalShift * 50, 50) // 增加饱和度
        } else {
            params.saturation = -min((0.1 - totalShift) * 100, 30) // 降低饱和度
        }
        
        // 对比度调整（基于LUT复杂度）
        let complexity = Float(lutData.data.count) / Float(lutData.size * lutData.size * lutData.size * 3)
        params.contrast = min(complexity * 20, 25)
        
        // 其他参数的智能设置
        params.vibrance = params.saturation * 0.7 // 自然饱和度
        params.clarity = totalShift > 0.05 ? 10 : -5 // 清晰度
        
        return params
    }
    
    /// 使用LUT更新预设
    /// - Parameters:
    ///   - lutPreset: LUT预设
    ///   - filterParams: 等效滤镜参数
    /// - Returns: 是否成功更新
    private static func updatePresetWithLUT(_ lutPreset: LUTPreset, filterParams: FilterParameters) -> Bool {
        // 创建新的FilterPreset，包含LUT路径信息
        let newPreset = FilterPreset(
            name: lutPreset.name,
            type: lutPreset.type,
            index: lutPreset.index,
            exposure: filterParams.exposure,
            contrast: filterParams.contrast,
            saturation: filterParams.saturation,
            brightness: filterParams.brightness,
            gamma: filterParams.gamma,
            temperature: filterParams.temperature,
            tint: filterParams.tint,
            highlights: filterParams.highlights,
            shadows: filterParams.shadows,
            hue: filterParams.hue,
            vibrance: filterParams.vibrance,
            sharpness: filterParams.sharpness,
            clarity: filterParams.clarity,
            vignetteIntensity: filterParams.vignetteIntensity,
            vignetteRadius: filterParams.vignetteRadius,
            highlightTone: filterParams.highlightTone,
            midtoneTone: filterParams.midtoneTone,
            shadowTone: filterParams.shadowTone,
            highlightHue: filterParams.highlightHue,
            highlightSaturation: filterParams.highlightSaturation,
            shadowHue: filterParams.shadowHue,
            shadowSaturation: filterParams.shadowSaturation
        )

        // 保存LUT文件路径到预设关联存储
        saveLUTPathForPreset(lutPreset.filePath, type: lutPreset.type, index: lutPreset.index)

        print("🔄 更新预设参数:")
        print("   LUT文件: \(URL(fileURLWithPath: lutPreset.filePath).lastPathComponent)")
        print("   色温: \(filterParams.temperature)")
        print("   色调: \(filterParams.tint)")
        print("   饱和度: \(filterParams.saturation)")
        print("   对比度: \(filterParams.contrast)")
        print("   自然饱和度: \(filterParams.vibrance)")
        print("   清晰度: \(filterParams.clarity)")

        return true
    }

    /// 保存LUT文件路径到预设关联
    /// - Parameters:
    ///   - lutPath: LUT文件路径
    ///   - type: 预设类型
    ///   - index: 预设索引
    static func saveLUTPathForPreset(_ lutPath: String, type: FilterPresetType, index: Int) {
        let key = "LUT_\(type.rawValue)_\(index)"
        UserDefaults.standard.set(lutPath, forKey: key)
        print("💾 已保存LUT路径关联: \(key) -> \(URL(fileURLWithPath: lutPath).lastPathComponent)")
    }

    /// 获取预设关联的LUT文件路径
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    /// - Returns: LUT文件路径，如果没有则返回nil
    static func getLUTPathForPreset(type: FilterPresetType, index: Int) -> String? {
        let key = "LUT_\(type.rawValue)_\(index)"
        return UserDefaults.standard.string(forKey: key)
    }

    /// 清除预设的LUT关联
    /// - Parameters:
    ///   - type: 预设类型
    ///   - index: 预设索引
    static func clearLUTForPreset(type: FilterPresetType, index: Int) {
        let key = "LUT_\(type.rawValue)_\(index)"
        UserDefaults.standard.removeObject(forKey: key)
        print("🗑️ 已清除LUT关联: \(key)")
    }
    
    /// 处理所有LUT文件
    /// - Returns: 处理结果统计
    static func processAllLUTFiles() -> (success: Int, failed: Int) {
        print("🚀 开始处理所有CUBE LUT文件...")
        
        // 确保目录存在
        createLUTDirectory()
        
        // 获取所有LUT文件
        let lutFiles = getAllLUTFiles()
        
        guard !lutFiles.isEmpty else {
            print("📁 LUT文件目录为空")
            print("   请将CUBE文件放到: \(lutDirectoryPath)")
            print("   文件命名格式: [类型]_[索引]_[名称].cube")
            return (0, 0)
        }
        
        print("📋 找到 \(lutFiles.count) 个CUBE文件")
        
        var successCount = 0
        var failedCount = 0
        
        // 处理每个文件
        for filePath in lutFiles {
            if processLUTFile(filePath) {
                successCount += 1
            } else {
                failedCount += 1
            }
            print("") // 空行分隔
        }
        
        // 输出统计结果
        print("📊 处理完成:")
        print("   成功: \(successCount) 个")
        print("   失败: \(failedCount) 个")
        print("   总计: \(lutFiles.count) 个")
        
        if successCount > 0 {
            print("🎉 LUT预设已更新，可以在应用中使用了！")
        }
        
        return (successCount, failedCount)
    }
    
    /// 列出所有LUT文件信息
    static func listLUTFiles() {
        print("📋 CUBE LUT文件列表:")
        print("   目录: \(lutDirectoryPath)")
        
        let lutFiles = getAllLUTFiles()
        
        if lutFiles.isEmpty {
            print("   (空)")
            return
        }
        
        for filePath in lutFiles {
            let fileName = URL(fileURLWithPath: filePath).lastPathComponent
            
            if let (presetType, index, name) = parseFileName(fileName) {
                print("   ✅ \(fileName)")
                print("      类型: \(presetType.displayName), 索引: \(index), 名称: \(name)")
            } else {
                print("   ❌ \(fileName) (格式错误)")
            }
        }
    }
    
    /// 一键处理LUT文件
    static func autoProcessLUTFiles() {
        print("🎯 CUBE LUT文件自动处理器")
        print("========================")
        
        // 列出现有文件
        listLUTFiles()
        print("")
        
        // 处理所有文件
        let result = processAllLUTFiles()
        
        if result.success == 0 && result.failed == 0 {
            print("")
            print("💡 使用提示:")
            print("1. 将CUBE文件放到: \(lutDirectoryPath)")
            print("2. 文件命名格式: [类型]_[索引]_[名称].cube")
            print("3. 再次调用 CubeLUTManager.autoProcessLUTFiles()")
            print("")
            print("📝 示例文件名:")
            print("   polaroid_0_电影胶片.cube")
            print("   film_1_Kodak_5218.cube")
            print("   vintage_2_复古暖调.cube")
        }
    }
}
