import Foundation
import CoreImage

/// CUBE格式LUT解析器
/// ⚠️ 已弃用：此类使用Core Image，已被MetalLUTProcessor替代
/// 用于解析.cube文件并转换为Core Image可用的3D LUT
@available(*, deprecated, message: "使用MetalLUTProcessor替代此Core Image实现")
class CubeLUTParser {
    
    /// LUT数据结构
    struct LUTData {
        let title: String
        let size: Int
        let domainMin: [Float]
        let domainMax: [Float]
        let data: [Float]
        
        init(title: String = "", size: Int = 0, domainMin: [Float] = [0.0, 0.0, 0.0], domainMax: [Float] = [1.0, 1.0, 1.0], data: [Float] = []) {
            self.title = title
            self.size = size
            self.domainMin = domainMin
            self.domainMax = domainMax
            self.data = data
        }
    }
    
    /// 从CUBE文件解析LUT数据
    /// - Parameter cubeContent: CUBE文件内容
    /// - Returns: 解析后的LUT数据，失败返回nil
    static func parseCubeLUT(from cubeContent: String) -> LUTData? {
        let lines = cubeContent.components(separatedBy: .newlines)
        
        var title = ""
        var size = 0
        var domainMin: [Float] = [0.0, 0.0, 0.0]
        var domainMax: [Float] = [1.0, 1.0, 1.0]
        var lutData: [Float] = []
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // 跳过空行和注释
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("#") {
                continue
            }
            
            // 解析标题
            if trimmedLine.hasPrefix("TITLE") {
                let components = trimmedLine.components(separatedBy: "\"")
                if components.count >= 2 {
                    title = components[1]
                }
                continue
            }
            
            // 解析LUT尺寸
            if trimmedLine.hasPrefix("LUT_3D_SIZE") {
                let components = trimmedLine.components(separatedBy: .whitespaces)
                if components.count >= 2, let lutSize = Int(components[1]) {
                    size = lutSize
                }
                continue
            }
            
            // 解析域范围
            if trimmedLine.hasPrefix("DOMAIN_MIN") {
                let components = trimmedLine.components(separatedBy: .whitespaces)
                if components.count >= 4 {
                    domainMin = [
                        Float(components[1]) ?? 0.0,
                        Float(components[2]) ?? 0.0,
                        Float(components[3]) ?? 0.0
                    ]
                }
                continue
            }
            
            if trimmedLine.hasPrefix("DOMAIN_MAX") {
                let components = trimmedLine.components(separatedBy: .whitespaces)
                if components.count >= 4 {
                    domainMax = [
                        Float(components[1]) ?? 1.0,
                        Float(components[2]) ?? 1.0,
                        Float(components[3]) ?? 1.0
                    ]
                }
                continue
            }
            
            // 解析LUT数据
            let components = trimmedLine.components(separatedBy: .whitespaces)
            if components.count >= 3 {
                if let r = Float(components[0]),
                   let g = Float(components[1]),
                   let b = Float(components[2]) {
                    lutData.append(r)
                    lutData.append(g)
                    lutData.append(b)
                }
            }
        }
        
        // 验证数据完整性
        guard size > 0 else {
            print("❌ CUBE文件缺少LUT_3D_SIZE")
            return nil
        }
        
        let expectedDataCount = size * size * size * 3
        guard lutData.count == expectedDataCount else {
            print("❌ LUT数据不完整: 期望\(expectedDataCount)个值，实际\(lutData.count)个值")
            return nil
        }
        
        return LUTData(
            title: title,
            size: size,
            domainMin: domainMin,
            domainMax: domainMax,
            data: lutData
        )
    }
    
    /// 将LUT数据转换为Core Image的CIColorCube滤镜
    /// - Parameter lutData: LUT数据
    /// - Returns: 配置好的CIColorCube滤镜，失败返回nil
    static func createColorCubeFilter(from lutData: LUTData) -> CIFilter? {
        guard let filter = CIFilter(name: "CIColorCube") else {
            print("❌ 无法创建CIColorCube滤镜")
            return nil
        }
        
        // 设置LUT尺寸
        filter.setValue(lutData.size, forKey: "inputCubeDimension")
        
        // 转换数据格式
        let data = Data(bytes: lutData.data, count: lutData.data.count * MemoryLayout<Float>.size)
        filter.setValue(data, forKey: "inputCubeData")
        
        print("✅ 成功创建CIColorCube滤镜")
        print("   标题: \(lutData.title)")
        print("   尺寸: \(lutData.size)³")
        print("   数据点: \(lutData.data.count / 3)")
        
        return filter
    }
    
    /// 应用LUT到图像
    /// - Parameters:
    ///   - image: 输入图像
    ///   - lutData: LUT数据
    /// - Returns: 处理后的图像，失败返回原图
    static func applyLUT(to image: CIImage, using lutData: LUTData) -> CIImage {
        guard let filter = createColorCubeFilter(from: lutData) else {
            return image
        }
        
        filter.setValue(image, forKey: kCIInputImageKey)
        
        return filter.outputImage ?? image
    }
    
    /// 验证CUBE文件格式
    /// - Parameter cubeContent: CUBE文件内容
    /// - Returns: 验证结果和错误信息
    static func validateCubeFormat(_ cubeContent: String) -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        let lines = cubeContent.components(separatedBy: .newlines)
        
        var hasSize = false
        var dataLineCount = 0
        var size = 0
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("#") {
                continue
            }
            
            if trimmedLine.hasPrefix("LUT_3D_SIZE") {
                hasSize = true
                let components = trimmedLine.components(separatedBy: .whitespaces)
                if components.count >= 2, let lutSize = Int(components[1]) {
                    size = lutSize
                    if size < 2 || size > 256 {
                        errors.append("LUT尺寸超出范围 (2-256): \(size)")
                    }
                } else {
                    errors.append("无效的LUT_3D_SIZE格式")
                }
                continue
            }
            
            // 检查数据行格式
            let components = trimmedLine.components(separatedBy: .whitespaces)
            if components.count >= 3 {
                for i in 0..<3 {
                    if Float(components[i]) == nil {
                        errors.append("第\(dataLineCount + 1)行包含无效的数值: \(components[i])")
                        break
                    }
                }
                dataLineCount += 1
            }
        }
        
        if !hasSize {
            errors.append("缺少LUT_3D_SIZE声明")
        }
        
        if size > 0 {
            let expectedLines = size * size * size
            if dataLineCount != expectedLines {
                errors.append("数据行数不匹配: 期望\(expectedLines)行，实际\(dataLineCount)行")
            }
        }
        
        return (errors.isEmpty, errors)
    }
    
    /// 打印LUT信息
    /// - Parameter lutData: LUT数据
    static func printLUTInfo(_ lutData: LUTData) {
        print("=== CUBE LUT信息 ===")
        print("标题: \(lutData.title.isEmpty ? "未命名" : lutData.title)")
        print("尺寸: \(lutData.size)³ (\(lutData.size * lutData.size * lutData.size) 个数据点)")
        print("域范围:")
        print("  最小值: R=\(lutData.domainMin[0]), G=\(lutData.domainMin[1]), B=\(lutData.domainMin[2])")
        print("  最大值: R=\(lutData.domainMax[0]), G=\(lutData.domainMax[1]), B=\(lutData.domainMax[2])")
        print("数据量: \(lutData.data.count) 个浮点数")
        print("==================")
    }
    
    /// 生成示例CUBE文件内容
    /// - Returns: 示例CUBE文件内容
    static func generateSampleCube() -> String {
        return """
        # 示例CUBE LUT文件
        # 这是一个简单的身份LUT（不改变颜色）
        
        TITLE "示例身份LUT"
        
        # LUT尺寸 (常见尺寸: 17, 33, 65)
        LUT_3D_SIZE 17
        
        # 域范围
        DOMAIN_MIN 0.0 0.0 0.0
        DOMAIN_MAX 1.0 1.0 1.0
        
        # LUT数据 (R G B 格式，每行一个颜色)
        # 这里只显示前几行作为示例...
        0.000000 0.000000 0.000000
        0.062500 0.000000 0.000000
        0.125000 0.000000 0.000000
        0.187500 0.000000 0.000000
        0.250000 0.000000 0.000000
        # ... 更多数据行 ...
        """
    }
}

// MARK: - 扩展：LUT效果分析

extension CubeLUTParser {
    
    /// 分析LUT的色彩特征
    /// - Parameter lutData: LUT数据
    /// - Returns: 色彩特征分析结果
    static func analyzeLUTCharacteristics(_ lutData: LUTData) -> [String: Any] {
        var analysis: [String: Any] = [:]
        
        // 基本信息
        analysis["title"] = lutData.title
        analysis["size"] = lutData.size
        analysis["totalPoints"] = lutData.data.count / 3
        
        // 分析色彩偏移
        var redShift: Float = 0
        var greenShift: Float = 0
        var blueShift: Float = 0
        var contrastChange: Float = 0
        
        let pointCount = lutData.size * lutData.size * lutData.size
        
        for i in 0..<pointCount {
            let baseIndex = i * 3
            let inputR = Float(i % lutData.size) / Float(lutData.size - 1)
            let inputG = Float((i / lutData.size) % lutData.size) / Float(lutData.size - 1)
            let inputB = Float(i / (lutData.size * lutData.size)) / Float(lutData.size - 1)
            
            let outputR = lutData.data[baseIndex]
            let outputG = lutData.data[baseIndex + 1]
            let outputB = lutData.data[baseIndex + 2]
            
            redShift += (outputR - inputR)
            greenShift += (outputG - inputG)
            blueShift += (outputB - inputB)
        }
        
        // 计算平均偏移
        redShift /= Float(pointCount)
        greenShift /= Float(pointCount)
        blueShift /= Float(pointCount)
        
        analysis["redShift"] = redShift
        analysis["greenShift"] = greenShift
        analysis["blueShift"] = blueShift
        
        // 判断LUT类型
        var lutType = "未知"
        if abs(redShift) < 0.01 && abs(greenShift) < 0.01 && abs(blueShift) < 0.01 {
            lutType = "身份LUT（无变化）"
        } else if redShift > 0.05 || greenShift > 0.05 {
            lutType = "暖色调LUT"
        } else if blueShift > 0.05 {
            lutType = "冷色调LUT"
        } else if redShift < -0.05 && greenShift < -0.05 {
            lutType = "去饱和LUT"
        }
        
        analysis["lutType"] = lutType
        
        return analysis
    }
    
    /// 打印LUT分析结果
    /// - Parameter analysis: 分析结果
    static func printLUTAnalysis(_ analysis: [String: Any]) {
        print("\n🔍 LUT色彩特征分析:")
        print("   类型: \(analysis["lutType"] as? String ?? "未知")")
        print("   红色偏移: \(String(format: "%.3f", analysis["redShift"] as? Float ?? 0))")
        print("   绿色偏移: \(String(format: "%.3f", analysis["greenShift"] as? Float ?? 0))")
        print("   蓝色偏移: \(String(format: "%.3f", analysis["blueShift"] as? Float ?? 0))")
        print("   数据点数: \(analysis["totalPoints"] as? Int ?? 0)")
    }
}
