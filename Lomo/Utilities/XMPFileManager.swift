import Foundation

/// XMP文件管理器
/// 用于自动读取项目中的XMP文件并更新预设
class XMPFileManager {
    
    /// XMP文件存放目录（相对于项目根目录）
    static let xmpDirectory = "XMPPresets"
    
    /// 获取XMP文件目录的完整路径
    static var xmpDirectoryPath: String {
        let projectRoot = FileManager.default.currentDirectoryPath
        return "\(projectRoot)/\(xmpDirectory)"
    }
    
    /// 创建XMP文件目录（如果不存在）
    static func createXMPDirectory() {
        let fileManager = FileManager.default
        let path = xmpDirectoryPath
        
        if !fileManager.fileExists(atPath: path) {
            do {
                try fileManager.createDirectory(atPath: path, withIntermediateDirectories: true, attributes: nil)
                print("✅ 已创建XMP文件目录: \(path)")
                
                // 创建说明文件
                let readmeContent = """
                # XMP预设文件夹
                
                ## 使用方法：
                1. 将您的XMP文件放到这个文件夹中
                2. 文件命名格式：[预设类型]_[索引]_[名称].xmp
                   例如：polaroid_0_经典宝丽来.xmp
                
                ## 支持的预设类型：
                - polaroid (宝丽来)
                - film (胶卷)
                - vintage (复古)
                - fashion (时尚)
                - ins (INS)
                
                ## 索引范围：
                - 0-4 (对应预设1-5)
                
                ## 示例文件名：
                - polaroid_0_我的宝丽来.xmp
                - film_1_胶卷风格.xmp
                - vintage_2_复古调色.xmp
                - fashion_3_时尚大片.xmp
                - ins_4_INS滤镜.xmp
                
                ## 自动处理：
                调用 XMPFileManager.processAllXMPFiles() 即可自动处理所有XMP文件
                """
                
                let readmePath = "\(path)/README.md"
                try readmeContent.write(toFile: readmePath, atomically: true, encoding: .utf8)
                
            } catch {
                print("❌ 创建XMP文件目录失败: \(error)")
            }
        }
    }
    
    /// 获取所有XMP文件
    /// - Returns: XMP文件路径数组
    static func getAllXMPFiles() -> [String] {
        let fileManager = FileManager.default
        let path = xmpDirectoryPath
        
        guard fileManager.fileExists(atPath: path) else {
            print("⚠️ XMP文件目录不存在: \(path)")
            return []
        }
        
        do {
            let files = try fileManager.contentsOfDirectory(atPath: path)
            let xmpFiles = files.filter { $0.lowercased().hasSuffix(".xmp") }
            return xmpFiles.map { "\(path)/\($0)" }
        } catch {
            print("❌ 读取XMP文件目录失败: \(error)")
            return []
        }
    }
    
    /// 解析文件名获取预设信息
    /// - Parameter fileName: 文件名
    /// - Returns: (预设类型, 索引, 名称) 或 nil
    static func parseFileName(_ fileName: String) -> (FilterPresetType, Int, String)? {
        // 移除.xmp扩展名
        let nameWithoutExtension = fileName.replacingOccurrences(of: ".xmp", with: "")
        
        // 分割文件名：type_index_name
        let components = nameWithoutExtension.components(separatedBy: "_")
        
        guard components.count >= 3 else {
            print("⚠️ 文件名格式不正确: \(fileName)")
            print("   正确格式: [类型]_[索引]_[名称].xmp")
            return nil
        }
        
        let typeString = components[0].lowercased()
        let indexString = components[1]
        let name = components[2...].joined(separator: "_")
        
        // 解析预设类型
        let presetType: FilterPresetType
        switch typeString {
        case "polaroid":
            presetType = .polaroid
        case "film":
            presetType = .film
        case "vintage":
            presetType = .vintage
        case "fashion":
            presetType = .fashion
        case "ins":
            presetType = .ins
        default:
            print("❌ 不支持的预设类型: \(typeString)")
            print("   支持的类型: polaroid, film, vintage, fashion, ins")
            return nil
        }
        
        // 解析索引
        guard let index = Int(indexString), index >= 0, index <= 4 else {
            print("❌ 无效的预设索引: \(indexString)")
            print("   索引范围: 0-4")
            return nil
        }
        
        return (presetType, index, name)
    }
    
    /// 处理单个XMP文件
    /// - Parameter filePath: XMP文件路径
    /// - Returns: 是否成功处理
    static func processXMPFile(_ filePath: String) -> Bool {
        let fileName = URL(fileURLWithPath: filePath).lastPathComponent
        
        // 解析文件名
        guard let (presetType, index, name) = parseFileName(fileName) else {
            return false
        }
        
        print("📄 处理XMP文件: \(fileName)")
        print("   预设类型: \(presetType.displayName)")
        print("   预设索引: \(index)")
        print("   预设名称: \(name)")
        
        // 更新预设
        let success = FilterPresetManager.shared.updatePresetFromXMP(
            xmpFilePath: filePath,
            type: presetType,
            index: index,
            name: name
        )
        
        if success {
            print("✅ 成功处理: \(fileName)")
        } else {
            print("❌ 处理失败: \(fileName)")
        }
        
        return success
    }
    
    /// 处理所有XMP文件
    /// - Returns: 处理结果统计
    static func processAllXMPFiles() -> (success: Int, failed: Int) {
        print("🚀 开始处理所有XMP文件...")
        
        // 确保目录存在
        createXMPDirectory()
        
        // 获取所有XMP文件
        let xmpFiles = getAllXMPFiles()
        
        guard !xmpFiles.isEmpty else {
            print("📁 XMP文件目录为空")
            print("   请将XMP文件放到: \(xmpDirectoryPath)")
            print("   文件命名格式: [类型]_[索引]_[名称].xmp")
            return (0, 0)
        }
        
        print("📋 找到 \(xmpFiles.count) 个XMP文件")
        
        var successCount = 0
        var failedCount = 0
        
        // 处理每个文件
        for filePath in xmpFiles {
            if processXMPFile(filePath) {
                successCount += 1
            } else {
                failedCount += 1
            }
            print("") // 空行分隔
        }
        
        // 输出统计结果
        print("📊 处理完成:")
        print("   成功: \(successCount) 个")
        print("   失败: \(failedCount) 个")
        print("   总计: \(xmpFiles.count) 个")
        
        if successCount > 0 {
            print("🎉 预设已更新，可以在应用中使用了！")
        }
        
        return (successCount, failedCount)
    }
    
    /// 列出所有XMP文件信息
    static func listXMPFiles() {
        print("📋 XMP文件列表:")
        print("   目录: \(xmpDirectoryPath)")
        
        let xmpFiles = getAllXMPFiles()
        
        if xmpFiles.isEmpty {
            print("   (空)")
            return
        }
        
        for filePath in xmpFiles {
            let fileName = URL(fileURLWithPath: filePath).lastPathComponent
            
            if let (presetType, index, name) = parseFileName(fileName) {
                print("   ✅ \(fileName)")
                print("      类型: \(presetType.displayName), 索引: \(index), 名称: \(name)")
            } else {
                print("   ❌ \(fileName) (格式错误)")
            }
        }
    }
    
    /// 清理XMP文件目录
    static func cleanXMPDirectory() {
        let fileManager = FileManager.default
        let path = xmpDirectoryPath
        
        do {
            if fileManager.fileExists(atPath: path) {
                try fileManager.removeItem(atPath: path)
                print("🗑️ 已清理XMP文件目录")
            }
            createXMPDirectory()
        } catch {
            print("❌ 清理XMP文件目录失败: \(error)")
        }
    }
}

// MARK: - 便捷调用方法

extension XMPFileManager {
    
    /// 一键处理XMP文件
    /// 这是最常用的方法，直接调用即可
    static func autoProcessXMPFiles() {
        print("🎯 XMP文件自动处理器")
        print("==================")
        
        // 列出现有文件
        listXMPFiles()
        print("")
        
        // 处理所有文件
        let result = processAllXMPFiles()
        
        if result.success == 0 && result.failed == 0 {
            print("")
            print("💡 使用提示:")
            print("1. 将XMP文件放到: \(xmpDirectoryPath)")
            print("2. 文件命名格式: [类型]_[索引]_[名称].xmp")
            print("3. 再次调用 XMPFileManager.autoProcessXMPFiles()")
            print("")
            print("📝 示例文件名:")
            print("   polaroid_0_我的宝丽来.xmp")
            print("   film_1_胶卷风格.xmp")
            print("   vintage_2_复古调色.xmp")
        }
    }
}
