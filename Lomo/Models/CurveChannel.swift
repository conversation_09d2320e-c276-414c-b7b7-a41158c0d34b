import Foundation
import SwiftUI

/// 曲线通道枚举 - 类型安全的通道管理
enum CurveChannel: Int, CaseIterable, Hashable, Comparable {
    case rgb = 0
    case red = 1
    case green = 2
    case blue = 3
    
    var lutKey: String {
        switch self {
        case .rgb: return "rgb"
        case .red: return "red"
        case .green: return "green"
        case .blue: return "blue"
        }
    }
    
    var displayName: String {
        switch self {
        case .rgb: return "RGB"
        case .red: return "红"
        case .green: return "绿"
        case .blue: return "蓝"
        }
    }

    var color: Color {
        switch self {
        case .rgb: return .white
        case .red: return .red
        case .green: return .green
        case .blue: return .blue
        }
    }

    // MARK: - Comparable
    static func < (lhs: CurveChannel, rhs: CurveChannel) -> Bool {
        return lhs.rawValue < rhs.rawValue
    }
}
