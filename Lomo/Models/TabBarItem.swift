import Foundation
import SwiftUI

/// 底部导航栏项目模型
struct TabBarItem: Identifiable, Equatable, Hashable {
    let id = UUID()
    let title: String
    let iconName: String
    let tag: Int
    let isSpecial: Bool // 是否为特殊按钮（如拍摄按钮）
    
    // 普通按钮的构造方法
    init(title: String, iconName: String, tag: Int) {
        self.title = title
        self.iconName = iconName
        self.tag = tag
        self.isSpecial = false
    }
    
    // 特殊按钮的构造方法
    init(title: String, iconName: String, tag: Int, isSpecial: Bool) {
        self.title = title
        self.iconName = iconName
        self.tag = tag
        self.isSpecial = isSpecial
    }
    
    // 静态定义五个标签页
    static let gallery = TabBarItem(title: "相册", iconName: "photo.fill", tag: 0)
    static let filter = TabBarItem(title: "滤镜", iconName: "camera.filters", tag: 1)
    static let camera = TabBarItem(title: "拍摄", iconName: "camera.fill", tag: 2, isSpecial: true)
    static let watermark = TabBarItem(title: "编辑", iconName: "note", tag: 3)
    static let settings = TabBarItem(title: "设置", iconName: "gearshape", tag: 4)
    
    // 选择模式下的按钮
    static let share = TabBarItem(title: "分享", iconName: "square.and.arrow.up", tag: 5)
    static let delete = TabBarItem(title: "删除", iconName: "trash", tag: 6)
    
    // 所有标签页数组 - 按照从左到右的顺序排列
    static let allTabs = [gallery, filter, camera, watermark, settings]
    
    // 选择模式下的标签页数组 - 包含分享、编辑和删除按钮
    static let selectionModeTabs = [share, watermark, delete]
    
    // 相等性判断
    static func == (lhs: TabBarItem, rhs: TabBarItem) -> Bool {
        return lhs.tag == rhs.tag
    }
    
    // 添加hash方法
    func hash(into hasher: inout Hasher) {
        hasher.combine(tag)
    }
} 