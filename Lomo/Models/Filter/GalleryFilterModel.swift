import Foundation
import SwiftData
import SwiftUI

// MARK: - 相册滤镜相关模型定义

/// 滤镜类型枚举
enum FilterType: String, Codable, CaseIterable {
    case film = "胶片"        // 胶片滤镜
    case polaroid = "宝丽来"  // 宝丽来滤镜
    case nature = "自然"      // 自然风格滤镜
    case fresh = "清新"       // 清新风格滤镜
    case vintage = "复古"     // 复古风格滤镜
    case blackAndWhite = "黑白" // 黑白滤镜
    case custom = "自定义"    // 自定义滤镜
}

/// 滤镜标签枚举
enum FilterLabel: String, Codable {
    case free = "FREE"     // 免费滤镜
    case pro = "PRO"       // 专业版滤镜
}

/// 滤镜数据模型
struct Filter: Identifiable {
    /// 唯一标识符
    let id: String
    
    /// 滤镜名称
    let name: String
    
    /// 滤镜类型
    let type: FilterType
    
    /// 滤镜标签（免费/专业版）
    let label: FilterLabel
    
    /// 滤镜图标名称（用于在UI中显示）
    let iconName: String
    
    /// 预览图片名称
    let previewImageName: String
    
    /// 是否为收藏滤镜
    var isFavorite: Bool = false
    
    /// 初始化方法
    init(id: String = UUID().uuidString, name: String, type: FilterType, label: FilterLabel, iconName: String, previewImageName: String, isFavorite: Bool = false) {
        self.id = id
        self.name = name
        self.type = type
        self.label = label
        self.iconName = iconName
        self.previewImageName = previewImageName
        self.isFavorite = isFavorite
    }
}

/// 滤镜类别枚举
enum FilterCategory {
    case favorites  // 收藏
    case film       // 胶片
    case polaroid   // 宝丽来
    case nature     // 自然
    case fresh      // 清新
    case vintage    // 复古
    case blackAndWhite // 黑白
    case custom     // 自定义
    
    /// 转换为滤镜类型
    var filterType: FilterType? {
        switch self {
        case .film: return .film
        case .polaroid: return .polaroid
        case .nature: return .nature
        case .fresh: return .fresh
        case .vintage: return .vintage
        case .blackAndWhite: return .blackAndWhite
        case .custom: return .custom
        case .favorites: return nil
        }
    }
}