import Foundation
import UIKit

/// 颗粒预设模型
struct GrainPreset: Identifiable, Equatable {
    let id: String
    let name: String
    
    // 颗粒参数
    let size: Double      // 颗粒大小
    let density: Double   // 密度
    let contrast: Double  // 对比度
    
    static func == (lhs: GrainPreset, rhs: GrainPreset) -> Bool {
        return lhs.id == rhs.id
    }
}

/// 颗粒效果参数模型
struct GrainParameters {
    /// 颗粒强度 (0.0-1.0)
    var intensity: Double = 0.0
    
    /// 是否启用
    var isEnabled: Bool = false
    
    /// 选中的预设
    var selectedPreset: GrainPreset?
    
    /// 默认参数
    static let `default` = GrainParameters()
} 