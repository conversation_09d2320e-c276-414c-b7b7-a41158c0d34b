import Foundation
import UIKit

struct LensConfiguration {
    let lens: String
    var isSelected: Bool
    var maxZoomFactor: Float?
    var minZoomFactor: Float?
    var isZoomSupported: Bool
    
    init(
        lens: String,
        isSelected: Bool,
        maxZoomFactor: Float? = nil,
        minZoomFactor: Float? = nil,
        isZoomSupported: Bool = false
    ) {
        self.lens = lens
        self.isSelected = isSelected
        self.maxZoomFactor = maxZoomFactor
        self.minZoomFactor = minZoomFactor
        self.isZoomSupported = isZoomSupported
    }
} 