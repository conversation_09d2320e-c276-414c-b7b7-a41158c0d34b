import Foundation
import SwiftData

/// 相机基础设置的数据模型，用于存储拍摄模式和相机位置
@Model
final class CameraBasicSettings {
    // 唯一标识符
    var id: String = "camera_basic_settings"
    
    // 拍摄模式（false为照片模式，true为视频模式）
    var isVideoMode: Bool = false
    
    // 相机位置（前置/后置）
    var cameraPosition: String = "back"  // "back" 或 "front"
    
    // 更新时间戳
    var updatedAt: Date = Date()
    
    // 视频模式状态栏设置
    var videoEncodingMode: String = "HEVC"
    var videoAspectRatioMode: String = "16:9"
    var videoResolutionMode: String = "4K"
    var videoFrameRateMode: String = "30fps"
    var videoColorSpaceMode: String = "SDR"
    
    // 照片模式状态栏设置
    var photoFormatMode: String = "JPG"
    var photoRatioMode: String = "16:9"
    var photoMode: String = "auto"  // 添加照片模式设置，默认为auto
    
    // 3x3按钮持久化存储
    var isHistogramEnabled: Bool = false
    var isGridEnabled: Bool = false
    // 定时器字段保留但不再用于持久化
    var isTimerEnabled: Bool = false
    var timerMode: String = "off"
    
    // 添加水平仪、峰值对焦和翻转功能的持久化字段
    var isLevelEnabled: Bool = false
    var isPeakingEnabled: Bool = false
    var isFlipped: Bool = false
    var flipMode: String = "off"
    
    // 添加斑马线和防抖功能的持久化字段
    var isZebraEnabled: Bool = false
    var isStabilizationEnabled: Bool = false
    var stabilizationMode: String = "standard"
    
    // 照片HDR状态持久化字段（状态栏功能，非3x3按钮）
    var isHDREnabled: Bool = false
    
    // 默认初始化方法
    init() {}
    
    // 自定义初始化方法，允许设置特定值
    init(
        isVideoMode: Bool = false,
        cameraPosition: String = "back",
        videoEncodingMode: String = "HEVC",
        videoAspectRatioMode: String = "16:9",
        videoResolutionMode: String = "4K",
        videoFrameRateMode: String = "30fps",
        videoColorSpaceMode: String = "SDR",
        photoFormatMode: String = "JPG",
        photoRatioMode: String = "16:9",
        photoMode: String = "auto",
        isHistogramEnabled: Bool = false,
        isGridEnabled: Bool = false,
        isTimerEnabled: Bool = false,
        timerMode: String = "off",
        isLevelEnabled: Bool = false,
        isPeakingEnabled: Bool = false,
        isFlipped: Bool = false,
        flipMode: String = "off",
        isZebraEnabled: Bool = false,
        isStabilizationEnabled: Bool = false,
        stabilizationMode: String = "standard",
        isHDREnabled: Bool = false
    ) {
        self.isVideoMode = isVideoMode
        self.cameraPosition = cameraPosition
        self.videoEncodingMode = videoEncodingMode
        self.videoAspectRatioMode = videoAspectRatioMode
        self.videoResolutionMode = videoResolutionMode
        self.videoFrameRateMode = videoFrameRateMode
        self.videoColorSpaceMode = videoColorSpaceMode
        self.photoFormatMode = photoFormatMode
        self.photoRatioMode = photoRatioMode
        self.photoMode = photoMode
        self.isHistogramEnabled = isHistogramEnabled
        self.isGridEnabled = isGridEnabled
        self.isTimerEnabled = isTimerEnabled
        self.timerMode = timerMode
        self.isLevelEnabled = isLevelEnabled
        self.isPeakingEnabled = isPeakingEnabled
        self.isFlipped = isFlipped
        self.flipMode = flipMode
        self.isZebraEnabled = isZebraEnabled
        self.isStabilizationEnabled = isStabilizationEnabled
        self.stabilizationMode = stabilizationMode
        self.isHDREnabled = isHDREnabled
        self.updatedAt = Date()
    }
    
    // 更新时间戳方法
    func updateTimestamp() {
        updatedAt = Date()
    }
} 