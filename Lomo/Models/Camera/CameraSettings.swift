import Foundation
import AVFoundation

struct CameraSettings {
    let videoParameters: VideoParameters
    let photoParameters: PhotoParameters
    
    init(
        videoParameters: VideoParameters = VideoParameters(
            codec: "HEVC",
            colorSpace: "SDR",
            resolution: "4K",
            frameRate: "60fps",
            aspectRatio: "16:9"
        ),
        photoParameters: PhotoParameters = PhotoParameters(
            format: "JPG",
            mode: "单张",
            flashEnabled: false,
            livePhotoEnabled: false,
            aspectRatio: "4:3"
        )
    ) {
        self.videoParameters = videoParameters
        self.photoParameters = photoParameters
    }
}

struct VideoParameters {
    let codec: String
    let colorSpace: String
    let resolution: String
    let frameRate: String
    let aspectRatio: String
}

struct PhotoParameters {
    let format: String
    let mode: String
    let flashEnabled: Bool
    let livePhotoEnabled: Bool
    let aspectRatio: String
} 