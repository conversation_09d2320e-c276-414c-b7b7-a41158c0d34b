import Foundation
import AVFoundation

// 相机系统类型
enum CameraSystemType: String {
    case single = "single"    // 单摄
    case dual = "dual"      // 双摄
    case triple = "triple"    // 三摄
    case quad = "quad"      // 四摄
}

// 防抖模式
enum StabilizationMode: String, Hashable {
    case off = "off"        // 关闭防抖
    case standard = "standard"   // 标准防抖
    case pro = "pro"        // 专业防抖
    case cinematic = "cinematic"  // 影院级防抖
}

// 相机能力
struct CameraCapability {
    let resolution: CGSize             // 分辨率
    let sensorSize: CGSize            // 传感器尺寸
    let aperture: Float               // 光圈
    let focalLength: Float            // 焦距
    let pixelSize: Float              // 像素尺寸
    let stabilization: [StabilizationMode] // 防抖模式
    let maxFrameRate: Float           // 最大帧率
    let supportedFormats: [AVCaptureDevice.Format] // 支持的格式
}

// 设备特性
struct DeviceFeatures {
    let supportsRAW: Bool             // RAW拍摄
    let supportsProRAW: Bool          // ProRAW支持
    let supportsNightMode: Bool       // 夜间模式
    let supportsPortraitMode: Bool    // 人像模式
    let supportsCinematicMode: Bool   // 电影模式
    let supportsProResVideo: Bool     // ProRes视频
    let maxVideoResolution: CGSize    // 最大视频分辨率
    let maxSlowMotionFPS: Int        // 最大慢动作帧率
    
    // 新增特性
    let supportsHDRVideo: Bool        // HDR视频支持
    let supportsSlowMotion: Bool      // 慢动作视频支持
    let supportsTimeLapse: Bool       // 延时摄影支持
    let supportsOIS: Bool             // 光学防抖支持
    let supportsDolbyVision: Bool     // 杜比视界支持
    let maxHDRFrameRate: Int          // HDR最大帧率
    let maxTimeLapseFrameRate: Int    // 延时最大帧率
    let nightModeMaxISO: Int          // 夜间模式最大ISO
    let portraitEffectOptions: [String] // 可用的人像模式效果
}

// 镜头能力
struct LensCapability {
    let type: AVCaptureDevice.DeviceType
    let maxZoomFactor: CGFloat
    let minFocusDistance: Float
    let fieldOfView: Float
    let supportedFormats: [AVCaptureDevice.Format]
    let stabilizationSupported: Bool
}

// 设备规格
struct DeviceSpecification {
    let model: String                  // 设备型号
    let cameraSystem: CameraSystemType // 相机系统类型
    let mainCamera: CameraCapability   // 主摄能力
    let ultraWide: CameraCapability?   // 超广角能力
    let telephoto: CameraCapability?   // 长焦能力
    let lidarSupported: Bool           // 是否支持激光雷达
    let macroSupported: Bool           // 是否支持微距
    let supportedFeatures: DeviceFeatures // 设备特定功能支持
} 