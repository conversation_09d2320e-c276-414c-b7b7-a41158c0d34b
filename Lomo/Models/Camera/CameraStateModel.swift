import Foundation
import AVFoundation
import UIKit

/// 相机状态模型
struct CameraStateModel {
    // MARK: - 核心状态
    var session: AVCaptureSession = AVCaptureSession()
    var isRecording: Bool = false
    var isVideoMode: Bool = false
    var photoMode: PhotoMode = .auto
    var isAMMode: Bool = false
    
    // MARK: - UI状态
    struct UIState {
        var isParameterExpanded: Bool = false
        var isLeftButtonExpanded: Bool = false
        var isRightButtonExpanded: Bool = false
        var isDialVisible: Bool = false
        var isFilterPressed: Bool = false
        var isEditPressed: Bool = false
        var isLockPressed: Bool = false
        var isLocked: Bool = false
    }
    var ui = UIState()
    
    // MARK: - 相机参数状态
    struct CameraParams {
        var currentZoomFactor: CGFloat = 1.0
        var exposureValue: Double = 0.0
        var focusDistance: Double = 0.5
        var shutterValue: Double = 0.0
        var isoValue: Double = 100.0
        var tintValue: Double = 0.0
        var temperatureValue: Double = 5600.0
        var apertureValue: Double = 1.8
        var deviceMinISO: Double = 50
        var deviceMaxISO: Double = 2000
    }
    var params = CameraParams()
    
    // MARK: - 相机模式设置
    struct CameraModes {
        var flipMode: FlipMode = .off
        var stabilizationMode: StabilizationMode = .standard
        var aspectRatioMode: AspectRatioMode = .ratio169
        var resolutionMode: ResolutionMode = .res1080p
        var photoFormatMode: PhotoFormatMode = .jpg
        var videoEncodingMode: VideoEncodingMode = .h264
        var colorSpaceMode: ColorSpaceMode = .sdr
        var frameRateMode: FrameRateMode = .fps30
        var timerMode: TimerMode = .off
        var photoRatioMode: PhotoRatioMode = .ratio169
        var flashMode: FlashMode = .off
        var livePhotoMode: LivePhotoMode = .off
    }
    var modes = CameraModes()
    
    // MARK: - 设备相关状态
    struct DeviceState {
        var availableLenses: [String] = []
        var selectedLensIndex: Int = 0
        var activeDialMode: DialMode = .exposure
    }
    var device = DeviceState()
    
    // MARK: - 功能可见性状态
    struct VisibilityState {
        var isVideoEncodingOptionsVisible: Bool = false
        var isAspectRatioOptionsVisible: Bool = false
        var isResolutionOptionsVisible: Bool = false
        var isFrameRateOptionsVisible: Bool = false
        var isColorSpaceOptionsVisible: Bool = false
        var isPhotoFormatOptionsVisible: Bool = false
        var isPhotoRatioOptionsVisible: Bool = false
        var isStabilizationOptionsVisible: Bool = false
        var isFlipOptionsVisible: Bool = false
        var isPhotoModeOptionsVisible: Bool = false
        var isTimerOptionsVisible: Bool = false
        var isHEVCOptionsVisible: Bool = false
    }
    var visibility = VisibilityState()
    
    // MARK: - 初始化方法
    init() {}
    
    // MARK: - 向后兼容属性
    // 这些属性是为了保持与现有代码兼容，随着代码迁移可以逐步移除
    var isParameterExpanded: Bool {
        get { ui.isParameterExpanded }
        set { ui.isParameterExpanded = newValue }
    }
    
    var isLeftButtonExpanded: Bool {
        get { ui.isLeftButtonExpanded }
        set { ui.isLeftButtonExpanded = newValue }
    }
    
    var isRightButtonExpanded: Bool {
        get { ui.isRightButtonExpanded }
        set { ui.isRightButtonExpanded = newValue }
    }
    
    var isDialVisible: Bool {
        get { ui.isDialVisible }
        set { ui.isDialVisible = newValue }
    }
    
    var isFilterPressed: Bool {
        get { ui.isFilterPressed }
        set { ui.isFilterPressed = newValue }
    }
    
    var isEditPressed: Bool {
        get { ui.isEditPressed }
        set { ui.isEditPressed = newValue }
    }
    
    var isLockPressed: Bool {
        get { ui.isLockPressed }
        set { ui.isLockPressed = newValue }
    }
    
    var isLocked: Bool {
        get { ui.isLocked }
        set { ui.isLocked = newValue }
    }
    
    // 相机参数兼容属性
    var currentZoomFactor: CGFloat {
        get { params.currentZoomFactor }
        set { params.currentZoomFactor = newValue }
    }
    
    var exposureValue: Double {
        get { params.exposureValue }
        set { params.exposureValue = newValue }
    }
    
    var focusDistance: Double {
        get { params.focusDistance }
        set { params.focusDistance = newValue }
    }
    
    var shutterValue: Double {
        get { params.shutterValue }
        set { params.shutterValue = newValue }
    }
    
    var isoValue: Double {
        get { params.isoValue }
        set { params.isoValue = newValue }
    }
    
    var tintValue: Double {
        get { params.tintValue }
        set { params.tintValue = newValue }
    }
    
    var temperatureValue: Double {
        get { params.temperatureValue }
        set { params.temperatureValue = newValue }
    }
    
    var apertureValue: Double {
        get { params.apertureValue }
        set { params.apertureValue = newValue }
    }
    
    var deviceMinISO: Double {
        get { params.deviceMinISO }
        set { params.deviceMinISO = newValue }
    }
    
    var deviceMaxISO: Double {
        get { params.deviceMaxISO }
        set { params.deviceMaxISO = newValue }
    }
    
    // 设备状态兼容属性
    var availableLenses: [String] {
        get { device.availableLenses }
        set { device.availableLenses = newValue }
    }
    
    var selectedLensIndex: Int {
        get { device.selectedLensIndex }
        set { device.selectedLensIndex = newValue }
    }
    
    var activeDialMode: DialMode {
        get { device.activeDialMode }
        set { device.activeDialMode = newValue }
    }
    
    // 模式设置兼容属性
    var flipMode: FlipMode {
        get { modes.flipMode }
        set { modes.flipMode = newValue }
    }
    
    var stabilizationMode: StabilizationMode {
        get { modes.stabilizationMode }
        set { modes.stabilizationMode = newValue }
    }
    
    var aspectRatioMode: AspectRatioMode {
        get { modes.aspectRatioMode }
        set { modes.aspectRatioMode = newValue }
    }
    
    var resolutionMode: ResolutionMode {
        get { modes.resolutionMode }
        set { modes.resolutionMode = newValue }
    }
    
    var photoFormatMode: PhotoFormatMode {
        get { modes.photoFormatMode }
        set { modes.photoFormatMode = newValue }
    }
    
    var videoEncodingMode: VideoEncodingMode {
        get { modes.videoEncodingMode }
        set { modes.videoEncodingMode = newValue }
    }
    
    var colorSpaceMode: ColorSpaceMode {
        get { modes.colorSpaceMode }
        set { modes.colorSpaceMode = newValue }
    }
    
    var frameRateMode: FrameRateMode {
        get { modes.frameRateMode }
        set { modes.frameRateMode = newValue }
    }
    
    var timerMode: TimerMode {
        get { modes.timerMode }
        set { modes.timerMode = newValue }
    }
    
    var photoRatioMode: PhotoRatioMode {
        get { modes.photoRatioMode }
        set { modes.photoRatioMode = newValue }
    }
    
    var flashMode: FlashMode {
        get { modes.flashMode }
        set { modes.flashMode = newValue }
    }
    
    var livePhotoMode: LivePhotoMode {
        get { modes.livePhotoMode }
        set { modes.livePhotoMode = newValue }
    }
    
    // 可见性状态兼容属性
    var isVideoEncodingOptionsVisible: Bool {
        get { visibility.isVideoEncodingOptionsVisible }
        set { visibility.isVideoEncodingOptionsVisible = newValue }
    }
    
    var isAspectRatioOptionsVisible: Bool {
        get { visibility.isAspectRatioOptionsVisible }
        set { visibility.isAspectRatioOptionsVisible = newValue }
    }
    
    var isResolutionOptionsVisible: Bool {
        get { visibility.isResolutionOptionsVisible }
        set { visibility.isResolutionOptionsVisible = newValue }
    }
    
    var isFrameRateOptionsVisible: Bool {
        get { visibility.isFrameRateOptionsVisible }
        set { visibility.isFrameRateOptionsVisible = newValue }
    }
    
    var isColorSpaceOptionsVisible: Bool {
        get { visibility.isColorSpaceOptionsVisible }
        set { visibility.isColorSpaceOptionsVisible = newValue }
    }
    
    var isPhotoFormatOptionsVisible: Bool {
        get { visibility.isPhotoFormatOptionsVisible }
        set { visibility.isPhotoFormatOptionsVisible = newValue }
    }
    
    var isPhotoRatioOptionsVisible: Bool {
        get { visibility.isPhotoRatioOptionsVisible }
        set { visibility.isPhotoRatioOptionsVisible = newValue }
    }
    
    var isStabilizationOptionsVisible: Bool {
        get { visibility.isStabilizationOptionsVisible }
        set { visibility.isStabilizationOptionsVisible = newValue }
    }
    
    var isFlipOptionsVisible: Bool {
        get { visibility.isFlipOptionsVisible }
        set { visibility.isFlipOptionsVisible = newValue }
    }
    
    var isPhotoModeOptionsVisible: Bool {
        get { visibility.isPhotoModeOptionsVisible }
        set { visibility.isPhotoModeOptionsVisible = newValue }
    }
    
    var isTimerOptionsVisible: Bool {
        get { visibility.isTimerOptionsVisible }
        set { visibility.isTimerOptionsVisible = newValue }
    }
    
    var isHEVCOptionsVisible: Bool {
        get { visibility.isHEVCOptionsVisible }
        set { visibility.isHEVCOptionsVisible = newValue }
    }
}

// 闪光灯模式
extension CameraStateModel {
    enum FlashMode {
        case off
        case on
        case auto
    }
    
    enum LivePhotoMode {
        case off
        case on
    }
} 