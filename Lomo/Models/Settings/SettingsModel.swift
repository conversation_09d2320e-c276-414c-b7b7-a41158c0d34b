import Foundation
import SwiftData

@Model
final class AppSettings {
    // 相机设置
    var isSaveOriginalEnabled: Bool = true
    var copyrightSignature: String = ""
    var isLocationRecordingEnabled: Bool = false
    var isCustomRatioEnabled: Bool = false
    var photoRatio: String = ""
    var videoRatio: String = ""
    var focusPeakingColor: String = "黄色"
    var isVolumeButtonShutterEnabled: Bool = true
    var shutterSound: String = "快门声1"
    var isFocusSoundEnabled: Bool = true
    var isResolutionSettingsEnabled: Bool = false
    var proRAWResolution: String = "48MP"
    var heicResolution: String = "12MP"
    var videoBitrate: String = "自动"
    var audioSource: String = "自动"
    
    // 偏好设置
    var language: String = "跟随系统"
    var deviceOrientation: String = "自动旋转"
    
    // 唯一标识符 - 确保我们始终使用同一个设置实例
    var id: String = "app_settings"
    
    // 创建时间戳 - 便于追踪设置更新时间
    var updatedAt: Date = Date()
    
    // 初始化方法
    init() {}
    
    // 自定义初始化方法，允许设置特定值
    init(
        isSaveOriginalEnabled: Bool = true,
        copyrightSignature: String = "",
        isLocationRecordingEnabled: Bool = false,
        isCustomRatioEnabled: Bool = false,
        photoRatio: String = "",
        videoRatio: String = "",
        focusPeakingColor: String = "黄色",
        isVolumeButtonShutterEnabled: Bool = true,
        shutterSound: String = "快门声1",
        isFocusSoundEnabled: Bool = true,
        isResolutionSettingsEnabled: Bool = false,
        proRAWResolution: String = "48MP",
        heicResolution: String = "12MP",
        videoBitrate: String = "自动",
        audioSource: String = "自动",
        language: String = "跟随系统",
        deviceOrientation: String = "自动旋转"
    ) {
        self.isSaveOriginalEnabled = isSaveOriginalEnabled
        self.copyrightSignature = copyrightSignature
        self.isLocationRecordingEnabled = isLocationRecordingEnabled
        self.isCustomRatioEnabled = isCustomRatioEnabled
        self.photoRatio = photoRatio
        self.videoRatio = videoRatio
        self.focusPeakingColor = focusPeakingColor
        self.isVolumeButtonShutterEnabled = isVolumeButtonShutterEnabled
        self.shutterSound = shutterSound
        self.isFocusSoundEnabled = isFocusSoundEnabled
        self.isResolutionSettingsEnabled = isResolutionSettingsEnabled
        self.proRAWResolution = proRAWResolution
        self.heicResolution = heicResolution
        self.videoBitrate = videoBitrate
        self.audioSource = audioSource
        self.language = language
        self.deviceOrientation = deviceOrientation
    }
    
    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }
}