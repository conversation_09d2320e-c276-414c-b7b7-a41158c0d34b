// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 应用错误类型 - 统一错误处理
/// 用于MVVM-S架构中的错误管理
struct AppError: LocalizedError, Equatable {
    let message: String
    let code: Int?
    let underlyingError: String?
    
    init(message: String, code: Int? = nil, underlyingError: Error? = nil) {
        self.message = message
        self.code = code
        self.underlyingError = underlyingError?.localizedDescription
    }
    
    var errorDescription: String? {
        return message
    }
    
    var failureReason: String? {
        return underlyingError
    }
    
    var recoverySuggestion: String? {
        switch code {
        case 404:
            return "请检查资源是否存在"
        case 500:
            return "请稍后重试"
        default:
            return "请检查网络连接或联系技术支持"
        }
    }
    
    // MARK: - 便利构造方法
    
    static func from(_ error: Error) -> AppError {
        if let appError = error as? AppError {
            return appError
        }
        return AppError(message: error.localizedDescription, underlyingError: error)
    }
    
    static func unknown(_ error: Error) -> AppError {
        return AppError(message: "未知错误", underlyingError: error)
    }
    
    static func serviceUnavailable() -> AppError {
        return AppError(message: "服务不可用", code: 503)
    }
    
    static func invalidParameter(_ message: String) -> AppError {
        return AppError(message: "参数错误: \(message)", code: 400)
    }
    
    static func networkError(_ message: String) -> AppError {
        return AppError(message: "网络错误: \(message)", code: -1)
    }
    
    // MARK: - Equatable
    
    static func == (lhs: AppError, rhs: AppError) -> Bool {
        return lhs.message == rhs.message && 
               lhs.code == rhs.code && 
               lhs.underlyingError == rhs.underlyingError
    }
}