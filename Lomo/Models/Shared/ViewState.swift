// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 视图状态枚举 - 统一状态管理
/// 用于MVVM-S架构中的状态管理
enum ViewState<T> {
    case idle
    case loading
    case loaded(T)
    case error(AppError)
    
    var isLoading: Bool {
        if case .loading = self { return true }
        return false
    }
    
    var error: AppError? {
        if case .error(let error) = self { return error }
        return nil
    }
    
    var data: T? {
        if case .loaded(let data) = self { return data }
        return nil
    }
}

// MARK: - Equatable 支持
extension ViewState: Equatable where T: Equatable {
    static func == (lhs: ViewState<T>, rhs: ViewState<T>) -> Bool {
        switch (lhs, rhs) {
        case (.idle, .idle):
            return true
        case (.loading, .loading):
            return true
        case (.loaded(let lhsData), .loaded(let rhsData)):
            return lhsData == rhsData
        case (.error(let lhsError), .error(let rhsError)):
            return lhsError == rhsError
        default:
            return false
        }
    }
}