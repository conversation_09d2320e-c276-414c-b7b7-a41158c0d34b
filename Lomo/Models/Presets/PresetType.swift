import Foundation

/// 定义预设类型的枚举，使用类型安全的方式表示不同的预设
enum PresetType: String, CaseIterable {
    case polaroid = "polaroid"
    case film = "film"
    case vintage = "vintage"
    case fashion = "fashion"
    case ins = "ins"
    
    /// 获取用于显示的本地化名称
    var displayName: String {
        switch self {
        case .polaroid:
            return "宝丽来"
        case .film:
            return "胶片"
        case .vintage:
            return "复古"
        case .fashion:
            return "时尚"
        case .ins:
            return "INS风"
        }
    }
    
    /// 获取对应的设置键路径（用于设置管理器）
    var settingKeyPath: String {
        switch self {
        case .polaroid:
            return "selectedPolaroidPreset"
        case .film:
            return "selectedFilmPreset"
        case .vintage:
            return "selectedVintagePreset"
        case .fashion:
            return "selectedFashionPreset"
        case .ins:
            return "selectedINSPreset"
        }
    }
} 