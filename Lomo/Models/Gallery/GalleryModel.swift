import Foundation
import SwiftData

// MARK: - 相册相关模型定义

// 相册分类模型 (移到ViewModel或独立文件更佳)
struct AlbumCategory: Identifiable, Hashable { // 添加 Hashable
    let id = UUID()
    let title: String
    let count: Int
    // 可以添加一个属性来存储对应的 PHAssetCollection
    // var collection: PHAssetCollection?
}

// 枚举定义主选项卡 (移到ViewModel或独立文件更佳)
enum MainGalleryTab {
    case myWorks
    case gallery
}

// 水印分类枚举
enum WatermarkCategory {
    case favorites  // 收藏
    case crop       // 裁切
    case filter     // 滤镜
    case adjust     // 调节
    case watermark  // 水印
    case effect     // 特效
    case paper      // 相纸

    // 添加description属性用于调试
    var description: String {
        switch self {
        case .favorites: return "favorites"
        case .crop: return "crop"
        case .filter: return "filter"
        case .adjust: return "adjust"
        case .watermark: return "watermark"
        case .effect: return "effect"
        case .paper: return "paper"
        }
    }
}
