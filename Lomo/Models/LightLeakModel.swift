import Foundation
import UIKit

/// 漏光效果预设
struct LightLeakPreset: Identifiable, Equatable {
    let id: String
    let name: String
    
    /// 获取资源名称
    var resourceName: String {
        return id
    }
    
    /// 模拟获取预设图像(实际实现时会从Assets中加载)
    func getImage() -> UIImage? {
        // 实际开发中,这里返回Assets中的图像
        // return UIImage(named: resourceName)
        return nil
    }
    
    static func == (lhs: LightLeakPreset, rhs: LightLeakPreset) -> Bool {
        return lhs.id == rhs.id
    }
    
    /// 预定义的漏光效果预设列表
    static let allPresets: [LightLeakPreset] = [
        LightLeakPreset(id: "light_leak_1", name: "漏光 1"),
        LightLeakPreset(id: "light_leak_2", name: "漏光 2"),
        LightLeakPreset(id: "light_leak_3", name: "漏光 3"),
        LightLeakPreset(id: "light_leak_4", name: "漏光 4"),
        LightLeakPreset(id: "light_leak_5", name: "漏光 5")
    ]
}

/// 漏光效果参数模型
struct LightLeakParameters {
    /// 漏光强度 (0.0-1.0)
    var intensity: Double = 0.0
    
    /// 漏光位置偏移
    var positionOffset: CGPoint = .zero
    
    /// 旋转角度 (弧度)
    var rotation: Double = 0.0
    
    /// 漏光混合模式
    var blendMode: LightLeakBlendMode = .screen
    
    /// 是否启用
    var isEnabled: Bool = false
    
    /// 选中的预设
    var selectedPreset: LightLeakPreset?
    
    /// 默认参数
    static let `default` = LightLeakParameters()
}

/// 漏光混合模式
enum LightLeakBlendMode: String, CaseIterable, Identifiable {
    case screen      // 滤色
    case overlay     // 叠加
    case softLight   // 柔光
    case colorDodge  // 颜色减淡
    
    var id: String { self.rawValue }
    
    /// Core Image 滤镜名称
    var ciFilterName: String {
        switch self {
        case .screen: return "CIScreenBlendMode"
        case .overlay: return "CIOverlayBlendMode"
        case .softLight: return "CISoftLightBlendMode"
        case .colorDodge: return "CIColorDodgeBlendMode"
        }
    }
    
    /// 获取混合模式的显示名称
    var displayName: String {
        switch self {
        case .screen: return "滤色"
        case .overlay: return "叠加"
        case .softLight: return "柔光"
        case .colorDodge: return "减淡"
        }
    }
} 