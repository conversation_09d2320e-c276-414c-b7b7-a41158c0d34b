import Foundation
import UIKit

/// 划痕预设模型
struct ScratchPreset: Identifiable, Equatable {
    let id: String
    let name: String
    
    // 划痕参数
    let density: Double   // 划痕密度
    let length: Double    // 划痕长度
    let width: Double     // 划痕宽度
    
    static func == (lhs: ScratchPreset, rhs: ScratchPreset) -> Bool {
        return lhs.id == rhs.id
    }
    
    /// 预定义的划痕预设列表
    static let allPresets: [ScratchPreset] = [
        ScratchPreset(id: "light", name: "轻微", 
            density: 0.2, length: 0.5, width: 0.5),
        ScratchPreset(id: "standard", name: "标准", 
            density: 0.4, length: 0.7, width: 0.7),
        ScratchPreset(id: "heavy", name: "严重", 
            density: 0.7, length: 0.9, width: 0.8),
        ScratchPreset(id: "vintage", name: "复古", 
            density: 0.5, length: 0.6, width: 0.6),
        ScratchPreset(id: "horizontal", name: "水平", 
            density: 0.4, length: 1.0, width: 0.7),
        ScratchPreset(id: "mixed", name: "混合", 
            density: 0.6, length: 0.8, width: 0.7)
    ]
}

/// 划痕效果参数模型
struct ScratchParameters {
    /// 划痕强度 (0.0-1.0)
    var intensity: Double = 0.0
    
    /// 是否启用
    var isEnabled: Bool = false
    
    /// 选中的预设
    var selectedPreset: ScratchPreset?
    
    /// 默认参数
    static let `default` = ScratchParameters()
} 