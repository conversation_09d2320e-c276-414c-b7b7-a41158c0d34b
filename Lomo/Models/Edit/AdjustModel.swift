import Foundation
import SwiftData
import SwiftUI

// MARK: - 调节设置的数据模型 (从AdjustSettings.swift完整复制)

/// 调节设置的数据模型
@Model
final class AdjustSettings {
    // 唯一标识符
    var id: String = "adjust_settings"

    // 选中的参数
    var selectedParameter: String = "曝光"

    // 曝光
    var exposure: Double = 0.0

    // 亮度
    var brightness: Double = 0.0

    // 对比度
    var contrast: Double = 0.0

    // 高光
    var highlights: Double = 0.0

    // 阴影
    var shadows: Double = 0.0

    // 色温
    var temperature: Double = 0.0

    // 色调
    var tint: Double = 0.0

    // 饱和度
    var saturation: Double = 0.0

    // 褪色
    var fade: Double = 0.0

    // 锐度
    var sharpness: Double = 0.0

    // 暗角
    var vignette: Double = 0.0

    // 色散
    var chromaticAberration: Double = 0.0

    // 颗粒大小
    var grainSize: Double = 0.0

    // 颗粒饱和度
    var grainSaturation: Double = 0.0

    // HSL调整
    var selectedHSLColorIndex: Int = 0
    var hue: Double = 0.0
    var hslSaturation: Double = 0.0
    var luminance: Double = 0.0

    // 色调调整
    var selectedToneOption: String = "全局"
    var toneBrightness: Double = 0.0
    var toneHue: Double = 0.0

    // 曲线调整
    var curveColorIndex: Int = 0

    // 更新时间
    var updatedAt: Date = Date()

    // 初始化方法
    init() {}

    // 自定义初始化方法
    init(selectedParameter: String = "曝光",
         exposure: Double = 0.0,
         brightness: Double = 0.0,
         contrast: Double = 0.0,
         highlights: Double = 0.0,
         shadows: Double = 0.0,
         temperature: Double = 0.0,
         tint: Double = 0.0,
         saturation: Double = 0.0,
         fade: Double = 0.0,
         sharpness: Double = 0.0,
         vignette: Double = 0.0,
         chromaticAberration: Double = 0.0,
         grainSize: Double = 0.0,
         grainSaturation: Double = 0.0,
         selectedHSLColorIndex: Int = 0,
         hue: Double = 0.0,
         hslSaturation: Double = 0.0,
         luminance: Double = 0.0,
         selectedToneOption: String = "全局",
         toneBrightness: Double = 0.0,
         toneHue: Double = 0.0,
         curveColorIndex: Int = 0) {
        self.selectedParameter = selectedParameter
        self.exposure = exposure
        self.brightness = brightness
        self.contrast = contrast
        self.highlights = highlights
        self.shadows = shadows
        self.temperature = temperature
        self.tint = tint
        self.saturation = saturation
        self.fade = fade
        self.sharpness = sharpness
        self.vignette = vignette
        self.chromaticAberration = chromaticAberration
        self.grainSize = grainSize
        self.grainSaturation = grainSaturation
        self.selectedHSLColorIndex = selectedHSLColorIndex
        self.hue = hue
        self.hslSaturation = hslSaturation
        self.luminance = luminance
        self.selectedToneOption = selectedToneOption
        self.toneBrightness = toneBrightness
        self.toneHue = toneHue
        self.curveColorIndex = curveColorIndex
        self.updatedAt = Date()
    }

    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }
}

// MARK: - 调整功能相关模型 (从AdjustmentModels.swift完整复制)

/// 调整参数
struct AdjustmentParameters {
    // 基本调整
    var exposure: Double = 0.0
    var brightness: Double = 0.0
    var contrast: Double = 0.0
    var highlights: Double = 0.0
    var shadows: Double = 0.0

    // 色彩调整
    var temperature: Double = 0.0
    var tint: Double = 0.0
    var saturation: Double = 0.0
    var fade: Double = 0.0

    // 创意调整
    var sharpness: Double = 0.0
    var vignette: Double = 0.0
    var chromaticAberration: Double = 0.0
    var grainSize: Double = 0.0
    var grainSaturation: Double = 0.0

    // HSL调整
    var selectedHSLColorIndex: Int = 0
    var hue: Double = 0.0
    var hslSaturation: Double = 0.0
    var luminance: Double = 0.0

    // 色调调整
    var selectedToneOption: String = "全局"
    var toneBrightness: Double = 0.0

    // 曲线调整
    var curveColorIndex: Int = 0

    // 当前选中的参数类别
    var selectedParameter: String = "exposure"
}

/// 调整参数类别
enum AdjustmentCategory: String, CaseIterable {
    case basic = "基本"
    case color = "色彩"
    case creative = "创意"
    case hsl = "HSL"
    case tone = "色调"
    case curve = "曲线"
}

/// HSL颜色选项
enum HSLColorOption: Int, CaseIterable {
    case red = 0
    case orange
    case yellow
    case green
    case aqua
    case blue
    case purple
    case magenta

    var displayName: String {
        switch self {
        case .red: return "红色"
        case .orange: return "橙色"
        case .yellow: return "黄色"
        case .green: return "绿色"
        case .aqua: return "青色"
        case .blue: return "蓝色"
        case .purple: return "紫色"
        case .magenta: return "品红"
        }
    }

    var color: Color {
        switch self {
        case .red: return .red
        case .orange: return .orange
        case .yellow: return .yellow
        case .green: return .green
        case .aqua: return .teal
        case .blue: return .blue
        case .purple: return .purple
        case .magenta: return .pink
        }
    }
}

/// 色调选项
enum ToneOption: String, CaseIterable {
    case global = "全局"
    case highlights = "高光"
    case midtones = "中间调"
    case shadows = "阴影"
}
