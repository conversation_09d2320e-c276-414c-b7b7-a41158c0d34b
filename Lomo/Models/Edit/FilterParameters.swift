import Foundation
import SwiftUI

/// 高光保护模式枚举
enum HighlightProtectionMode: Int, CaseIterable {
    case standard = 0   // 标准模式：Reinhard tone mapping变体
    case film = 1       // 胶片模式：自然高光压缩特性
    case linear = 2     // 线性空间：对数压缩保持线性特性

    var displayName: String {
        switch self {
        case .standard:
            return "标准模式"
        case .film:
            return "胶片模式"
        case .linear:
            return "线性空间"
        }
    }

    var description: String {
        switch self {
        case .standard:
            return "Reinhard tone mapping变体"
        case .film:
            return "自然高光压缩特性"
        case .linear:
            return "对数压缩保持线性特性"
        }
    }
}

/// 滤镜参数数据模型
/// 包含所有Core Image滤镜支持的参数
@Observable
class FilterParameters: Equatable {
    
    // MARK: - 基础色彩调整参数
    
    /// 曝光值 (-5.0 到 +5.0 EV)
    var exposure: Float = 0.0
    
    /// 对比度 (-100 到 +100)
    var contrast: Float = 0.0
    
    /// 饱和度 (-100 到 +100)
    var saturation: Float = 0.0
    
    /// 亮度 (-100 到 +100)
    var brightness: Float = 0.0
    
    /// 伽马值 (0.25 到 4.0)
    var gamma: Float = 1.0

    // MARK: - 高光保护参数

    /// 高光保护强度 (0.0 到 1.0)
    var highlightProtectionIntensity: Float = 0.0

    /// 高光保护模式 (0=标准, 1=胶片, 2=线性)
    var highlightProtectionMode: Int = 0

    // MARK: - 色温和色调参数
    
    /// 色温调整 (-100 到 +100，默认0)
    var temperature: Float = 0.0
    
    /// 色调调整 (-100 到 +100)
    var tint: Float = 0.0
    
    // MARK: - 高光阴影参数

    /// 高光调整 (-100 到 +100)
    var highlights: Float = 0.0

    /// 阴影调整 (-100 到 +100)
    var shadows: Float = 0.0

    /// 白色调整 (-100 到 +100) - 对应XMP的Whites2012
    var whites: Float = 0.0

    /// 黑色调整 (-100 到 +100) - 对应XMP的Blacks2012
    var blacks: Float = 0.0

    /// 去雾强度 (-100 到 +100) - 对应XMP的Dehaze
    var dehaze: Float = 0.0
    
    // MARK: - 色相饱和度明度参数

    /// 8个颜色的独立HSL参数存储 (0=红,1=橙,2=黄,3=绿,4=青,5=蓝,6=紫,7=洋红)
    private var hslHueValues: [Float] = Array(repeating: 0.0, count: 8)
    private var hslSaturationValues: [Float] = Array(repeating: 0.0, count: 8)
    private var hslLuminanceValues: [Float] = Array(repeating: 0.0, count: 8)

    /// 选中的HSL颜色范围索引 (0-7: 红、橙、黄、绿、青、蓝、紫、品红)
    var selectedHSLColorIndex: Int = 0

    /// 当前选中颜色的色相调整 (-180 到 +180 度)
    var hue: Float {
        get { hslHueValues[selectedHSLColorIndex] }
        set { hslHueValues[selectedHSLColorIndex] = newValue }
    }

    /// 当前选中颜色的HSL饱和度调整 (-100 到 +100)
    var hslSaturation: Float {
        get { hslSaturationValues[selectedHSLColorIndex] }
        set { hslSaturationValues[selectedHSLColorIndex] = newValue }
    }

    /// 当前选中颜色的HSL明度调整 (-100 到 +100)
    var hslLuminance: Float {
        get { hslLuminanceValues[selectedHSLColorIndex] }
        set { hslLuminanceValues[selectedHSLColorIndex] = newValue }
    }

    /// HSL颜色范围过渡柔和度 (0.0-1.0)
    var hslColorRangeSoftness: Float = 0.3

    /// HSL颜色范围检测精度 (0.5-2.0, 默认1.0)
    var hslColorRangePrecision: Float = 1.0

    /// 自然饱和度 (-100 到 +100)
    var vibrance: Float = 0.0

    /// 褪色效果强度 (0 到 100)
    var fadeEffect: Float = 0.0

    /// 黑白效果强度 (0 到 100)
    var monoEffect: Float = 0.0

    // MARK: - 色彩校准参数 (Lightroom风格的原色校准)

    /// 红原色色相校准 (-100 到 +100)
    var redHue: Float = 0.0

    /// 红原色饱和度校准 (-100 到 +100)
    var redSaturation: Float = 0.0

    /// 绿原色色相校准 (-100 到 +100)
    var greenHue: Float = 0.0

    /// 绿原色饱和度校准 (-100 到 +100)
    var greenSaturation: Float = 0.0

    /// 蓝原色色相校准 (-100 到 +100)
    var blueHue: Float = 0.0

    /// 蓝原色饱和度校准 (-100 到 +100)
    var blueSaturation: Float = 0.0

    /// 当前选中的校准颜色索引 (0=红色, 1=绿色, 2=蓝色)
    var selectedCalibrationColorIndex: Int = 0
    
    // MARK: - 锐化和清晰度参数
    
    /// 锐化程度 (-100 到 +100)
    var sharpness: Float = 0.0
    
    /// 清晰度 (-100 到 +100)
    var clarity: Float = 0.0

    /// 清晰度2 (-100 到 +100)
    var clarity2: Float = 0.0
    
    // MARK: - 渐晕效果参数
    
    /// 渐晕强度 (-100 到 +100)
    var vignetteIntensity: Float = 0.0
    
    /// 渐晕半径 (0.0 到 2.0)
    var vignetteRadius: Float = 1.0

    /// 降噪强度 (0 到 100)
    var noiseReduction: Float = 0.0

    /// 是否为线性图像（需要转换到sRGB）
    var isLinearImage: Bool = false
    
    // MARK: - 色调曲线参数 (简化版)
    
    /// 高光色调曲线 (-100 到 +100)
    var highlightTone: Float = 0.0
    
    /// 中间调色调曲线 (-100 到 +100)
    var midtoneTone: Float = 0.0
    
    /// 阴影色调曲线 (-100 到 +100)
    var shadowTone: Float = 0.0
    
    // MARK: - 分离色调参数

    /// 高光色相 (0 到 360 度)
    var highlightHue: Float = 0.0

    /// 高光饱和度 (0 到 100)
    var highlightSaturation: Float = 0.0

    /// 阴影色相 (0 到 360 度)
    var shadowHue: Float = 0.0

    /// 阴影饱和度 (0 到 100)
    var shadowSaturation: Float = 0.0

    /// 色调分离平衡 (-100 到 +100，0为中性，负值偏向阴影，正值偏向高光)
    var splitToningBalance: Float = 0.0

    // MARK: - 曲线调整参数

    /// RGB曲线查找表 (256个浮点值，0.0-1.0)
    var rgbCurveLUT: [Float] = CurveProcessor.createLinearLUT()

    /// 红色通道曲线查找表
    var redCurveLUT: [Float] = CurveProcessor.createLinearLUT()

    /// 绿色通道曲线查找表
    var greenCurveLUT: [Float] = CurveProcessor.createLinearLUT()

    /// 蓝色通道曲线查找表
    var blueCurveLUT: [Float] = CurveProcessor.createLinearLUT()

    /// 曲线强度 (0.0-1.0)
    var curveIntensity: Float = 1.0

    /// 是否启用RGB曲线
    var rgbCurveEnabled: Bool = false

    /// 是否启用分离通道曲线
    var channelCurvesEnabled: Bool = false

    // MARK: - 初始化方法
    
    init() {}
    
    /// 使用预设参数初始化
    init(preset: FilterPreset) {
        self.exposure = preset.exposure
        self.contrast = preset.contrast
        self.saturation = preset.saturation
        self.brightness = preset.brightness
        self.gamma = preset.gamma
        self.temperature = preset.temperature
        self.tint = preset.tint
        self.highlights = preset.highlights
        self.shadows = preset.shadows
        self.hue = preset.hue
        self.vibrance = preset.vibrance
        self.sharpness = preset.sharpness
        self.clarity = preset.clarity
        self.clarity2 = preset.clarity2
        self.vignetteIntensity = preset.vignetteIntensity
        self.vignetteRadius = preset.vignetteRadius
        self.highlightTone = preset.highlightTone
        self.midtoneTone = preset.midtoneTone
        self.shadowTone = preset.shadowTone
        self.highlightHue = preset.highlightHue
        self.highlightSaturation = preset.highlightSaturation
        self.shadowHue = preset.shadowHue
        self.shadowSaturation = preset.shadowSaturation
    }
    
    // MARK: - 便利方法
    
    /// 重置所有参数到默认值
    func reset() {
        exposure = 0.0
        contrast = 0.0
        saturation = 0.0
        brightness = 0.0
        gamma = 1.0
        highlightProtectionIntensity = 0.0
        highlightProtectionMode = 0
        temperature = 0.0
        tint = 0.0
        highlights = 0.0
        shadows = 0.0
        whites = 0.0
        blacks = 0.0
        dehaze = 0.0
        // 重置所有颜色的HSL参数
        hslHueValues = Array(repeating: 0.0, count: 8)
        hslSaturationValues = Array(repeating: 0.0, count: 8)
        hslLuminanceValues = Array(repeating: 0.0, count: 8)
        selectedHSLColorIndex = 0
        hslColorRangeSoftness = 0.3
        hslColorRangePrecision = 1.0
        vibrance = 0.0
        fadeEffect = 0.0
        monoEffect = 0.0
        // 重置校准参数
        redHue = 0.0
        redSaturation = 0.0
        greenHue = 0.0
        greenSaturation = 0.0
        blueHue = 0.0
        blueSaturation = 0.0
        selectedCalibrationColorIndex = 0
        sharpness = 0.0
        clarity = 0.0
        clarity2 = 0.0
        vignetteIntensity = 0.0
        vignetteRadius = 1.0
        noiseReduction = 0.0
        isLinearImage = false
        highlightTone = 0.0
        midtoneTone = 0.0
        shadowTone = 0.0
        highlightHue = 0.0
        highlightSaturation = 0.0
        shadowHue = 0.0
        shadowSaturation = 0.0

        // 重置曲线参数
        rgbCurveLUT = CurveProcessor.createLinearLUT()
        redCurveLUT = CurveProcessor.createLinearLUT()
        greenCurveLUT = CurveProcessor.createLinearLUT()
        blueCurveLUT = CurveProcessor.createLinearLUT()
        curveIntensity = 1.0
        rgbCurveEnabled = false
        channelCurvesEnabled = false
    }

    /// 重置所有HSL颜色参数到默认值
    func resetAllHSLColors() {
        hslHueValues = Array(repeating: 0.0, count: 8)
        hslSaturationValues = Array(repeating: 0.0, count: 8)
        hslLuminanceValues = Array(repeating: 0.0, count: 8)
        selectedHSLColorIndex = 0
        hslColorRangeSoftness = 0.3
        hslColorRangePrecision = 1.0
        print("🎨 [FilterParameters] 重置所有HSL颜色参数")
    }

    /// 应用预设参数
    func applyPreset(_ preset: FilterPreset, intensity: Float = 1.0) {
        // 应用预设参数，根据强度进行缩放
        self.exposure = preset.exposure * intensity
        self.contrast = preset.contrast * intensity
        self.saturation = preset.saturation * intensity
        self.brightness = preset.brightness * intensity
        self.gamma = 1.0 + (preset.gamma - 1.0) * intensity // 伽马值特殊处理
        self.temperature = preset.temperature * intensity
        self.tint = preset.tint * intensity
        self.highlights = preset.highlights * intensity
        self.shadows = preset.shadows * intensity
        self.hue = preset.hue * intensity
        self.vibrance = preset.vibrance * intensity
        self.sharpness = preset.sharpness * intensity
        self.clarity = preset.clarity * intensity
        self.clarity2 = preset.clarity2 * intensity
        self.vignetteIntensity = preset.vignetteIntensity * intensity
        self.vignetteRadius = 1.0 + (preset.vignetteRadius - 1.0) * intensity // 半径特殊处理
        self.highlightTone = preset.highlightTone * intensity
        self.midtoneTone = preset.midtoneTone * intensity
        self.shadowTone = preset.shadowTone * intensity
        self.highlightHue = preset.highlightHue * intensity
        self.highlightSaturation = preset.highlightSaturation * intensity
        self.shadowHue = preset.shadowHue * intensity
        self.shadowSaturation = preset.shadowSaturation * intensity
    }
    
    /// 复制当前参数
    func copy() -> FilterParameters {
        let newParams = FilterParameters()
        newParams.exposure = self.exposure
        newParams.contrast = self.contrast
        newParams.saturation = self.saturation
        newParams.brightness = self.brightness
        newParams.gamma = self.gamma
        newParams.highlightProtectionIntensity = self.highlightProtectionIntensity
        newParams.highlightProtectionMode = self.highlightProtectionMode
        newParams.temperature = self.temperature
        newParams.tint = self.tint
        newParams.highlights = self.highlights
        newParams.shadows = self.shadows
        newParams.whites = self.whites
        newParams.blacks = self.blacks
        newParams.dehaze = self.dehaze
        newParams.hue = self.hue
        newParams.hslSaturation = self.hslSaturation
        newParams.hslLuminance = self.hslLuminance
        newParams.vibrance = self.vibrance
        newParams.fadeEffect = self.fadeEffect
        newParams.monoEffect = self.monoEffect
        // 校准参数复制
        newParams.redHue = self.redHue
        newParams.redSaturation = self.redSaturation
        newParams.greenHue = self.greenHue
        newParams.greenSaturation = self.greenSaturation
        newParams.blueHue = self.blueHue
        newParams.blueSaturation = self.blueSaturation
        newParams.selectedCalibrationColorIndex = self.selectedCalibrationColorIndex
        newParams.sharpness = self.sharpness
        newParams.clarity = self.clarity
        newParams.clarity2 = self.clarity2
        newParams.vignetteIntensity = self.vignetteIntensity
        newParams.vignetteRadius = self.vignetteRadius
        newParams.noiseReduction = self.noiseReduction
        newParams.isLinearImage = self.isLinearImage
        newParams.highlightTone = self.highlightTone
        newParams.midtoneTone = self.midtoneTone
        newParams.shadowTone = self.shadowTone
        newParams.highlightHue = self.highlightHue
        newParams.highlightSaturation = self.highlightSaturation
        newParams.shadowHue = self.shadowHue
        newParams.shadowSaturation = self.shadowSaturation
        return newParams
    }
}

// MARK: - 参数范围验证扩展

extension FilterParameters {
    
    /// 验证并限制参数在有效范围内
    func validateAndClampParameters() {
        exposure = max(-5.0, min(5.0, exposure))
        contrast = max(-100.0, min(100.0, contrast))
        saturation = max(-100.0, min(100.0, saturation))
        brightness = max(-100.0, min(100.0, brightness))
        gamma = max(0.25, min(4.0, gamma))
        highlightProtectionIntensity = max(0.0, min(1.0, highlightProtectionIntensity))
        highlightProtectionMode = max(0, min(2, highlightProtectionMode))
        temperature = max(-100.0, min(100.0, temperature))
        tint = max(-100.0, min(100.0, tint))
        highlights = max(-100.0, min(100.0, highlights))
        shadows = max(-100.0, min(100.0, shadows))
        whites = max(-100.0, min(100.0, whites))
        blacks = max(-100.0, min(100.0, blacks))
        dehaze = max(-100.0, min(100.0, dehaze))
        hue = max(-180.0, min(180.0, hue))
        hslSaturation = max(-100.0, min(100.0, hslSaturation))
        hslLuminance = max(-100.0, min(100.0, hslLuminance))
        vibrance = max(-100.0, min(100.0, vibrance))
        fadeEffect = max(0.0, min(100.0, fadeEffect))
        monoEffect = max(0.0, min(100.0, monoEffect))
        redSaturation = max(-100.0, min(100.0, redSaturation))
        greenSaturation = max(-100.0, min(100.0, greenSaturation))
        blueSaturation = max(-100.0, min(100.0, blueSaturation))
        sharpness = max(-100.0, min(100.0, sharpness))
        clarity = max(-100.0, min(100.0, clarity))
        clarity2 = max(-100.0, min(100.0, clarity2))
        vignetteIntensity = max(-100.0, min(100.0, vignetteIntensity))
        vignetteRadius = max(0.0, min(2.0, vignetteRadius))
        noiseReduction = max(0.0, min(100.0, noiseReduction))
        highlightTone = max(-100.0, min(100.0, highlightTone))
        midtoneTone = max(-100.0, min(100.0, midtoneTone))
        shadowTone = max(-100.0, min(100.0, shadowTone))

        // 色调分离参数验证 - 增强边界检查
        highlightHue = validateHue(highlightHue)
        highlightSaturation = max(0.0, min(100.0, highlightSaturation))
        shadowHue = validateHue(shadowHue)
        shadowSaturation = max(0.0, min(100.0, shadowSaturation))
        splitToningBalance = max(-100.0, min(100.0, splitToningBalance))
    }

    // MARK: - 参数验证辅助方法

    /// 验证色相值 - 确保在0-360度范围内，支持循环
    private func validateHue(_ hue: Float) -> Float {
        if hue.isNaN || hue.isInfinite {
            return 0.0
        }

        // 将色相值规范化到0-360度范围
        var normalizedHue = fmod(hue, 360.0)
        if normalizedHue < 0 {
            normalizedHue += 360.0
        }
        return normalizedHue
    }

    /// 验证饱和度值 - 确保在0-100范围内
    private func validateSaturation(_ saturation: Float) -> Float {
        if saturation.isNaN || saturation.isInfinite {
            return 0.0
        }
        return max(0.0, min(100.0, saturation))
    }

    /// 验证色调曲线值 - 确保在-100到100范围内
    private func validateTone(_ tone: Float) -> Float {
        if tone.isNaN || tone.isInfinite {
            return 0.0
        }
        return max(-100.0, min(100.0, tone))
    }

    /// 检查色调分离参数的有效性
    func validateSplitToningParameters() -> Bool {
        let isHighlightHueValid = highlightHue >= 0.0 && highlightHue <= 360.0
        let isHighlightSatValid = highlightSaturation >= 0.0 && highlightSaturation <= 100.0
        let isShadowHueValid = shadowHue >= 0.0 && shadowHue <= 360.0
        let isShadowSatValid = shadowSaturation >= 0.0 && shadowSaturation <= 100.0
        let isBalanceValid = splitToningBalance >= -100.0 && splitToningBalance <= 100.0

        return isHighlightHueValid && isHighlightSatValid && isShadowHueValid &&
               isShadowSatValid && isBalanceValid
    }

    /// 比较两个参数对象是否相等（用于避免重复处理）
    func isEqual(to other: FilterParameters) -> Bool {
        return self == other
    }
    
    // MARK: - Equatable 协议实现
    
    static func == (lhs: FilterParameters, rhs: FilterParameters) -> Bool {
        return lhs.exposure == rhs.exposure &&
               lhs.contrast == rhs.contrast &&
               lhs.saturation == rhs.saturation &&
               lhs.brightness == rhs.brightness &&
               lhs.gamma == rhs.gamma &&
               lhs.highlightProtectionIntensity == rhs.highlightProtectionIntensity &&
               lhs.highlightProtectionMode == rhs.highlightProtectionMode &&
               lhs.temperature == rhs.temperature &&
               lhs.tint == rhs.tint &&
               lhs.highlights == rhs.highlights &&
               lhs.shadows == rhs.shadows &&
               lhs.whites == rhs.whites &&
               lhs.blacks == rhs.blacks &&
               lhs.dehaze == rhs.dehaze &&
               lhs.hue == rhs.hue &&
               lhs.hslSaturation == rhs.hslSaturation &&
               lhs.hslLuminance == rhs.hslLuminance &&
               lhs.vibrance == rhs.vibrance &&
               lhs.fadeEffect == rhs.fadeEffect &&
               lhs.monoEffect == rhs.monoEffect &&
               lhs.redSaturation == rhs.redSaturation &&
               lhs.greenSaturation == rhs.greenSaturation &&
               lhs.blueSaturation == rhs.blueSaturation &&
               lhs.sharpness == rhs.sharpness &&
               lhs.clarity == rhs.clarity &&
               lhs.clarity2 == rhs.clarity2 &&
               lhs.vignetteIntensity == rhs.vignetteIntensity &&
               lhs.vignetteRadius == rhs.vignetteRadius &&
               lhs.noiseReduction == rhs.noiseReduction &&
               lhs.isLinearImage == rhs.isLinearImage &&
               lhs.highlightTone == rhs.highlightTone &&
               lhs.midtoneTone == rhs.midtoneTone &&
               lhs.shadowTone == rhs.shadowTone &&
               lhs.highlightHue == rhs.highlightHue &&
               lhs.highlightSaturation == rhs.highlightSaturation &&
               lhs.shadowHue == rhs.shadowHue &&
               lhs.shadowSaturation == rhs.shadowSaturation &&
               lhs.splitToningBalance == rhs.splitToningBalance &&
               lhs.rgbCurveLUT == rhs.rgbCurveLUT &&
               lhs.redCurveLUT == rhs.redCurveLUT &&
               lhs.greenCurveLUT == rhs.greenCurveLUT &&
               lhs.blueCurveLUT == rhs.blueCurveLUT &&
               lhs.curveIntensity == rhs.curveIntensity &&
               lhs.rgbCurveEnabled == rhs.rgbCurveEnabled &&
               lhs.channelCurvesEnabled == rhs.channelCurvesEnabled
    }
    
    // MARK: - 状态检查方法
    
    /// 检查是否有活跃的调整参数
    /// - Returns: 如果有任何非默认值的参数则返回 true
    func hasActiveAdjustments() -> Bool {
        // 检查基础色彩调整参数
        if exposure != 0.0 { return true }
        if contrast != 0.0 { return true }
        if saturation != 0.0 { return true }
        if brightness != 0.0 { return true }
        
        // 检查高级调整参数（如果存在）
        // 这里可以根据实际的 FilterParameters 属性进行扩展
        
        return false
    }
    
    /// 重置所有参数到默认值
    func resetToDefaults() {
        exposure = 0.0
        contrast = 0.0
        saturation = 0.0
        brightness = 0.0
        // 重置其他参数...
    }
    
    /// 获取活跃参数的数量
    var activeParametersCount: Int {
        var count = 0
        if exposure != 0.0 { count += 1 }
        if contrast != 0.0 { count += 1 }
        if saturation != 0.0 { count += 1 }
        if brightness != 0.0 { count += 1 }
        return count
    }
}
