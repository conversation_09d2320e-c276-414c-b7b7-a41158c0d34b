import Foundation
import SwiftData

/// 水印设置的数据模型
@Model
final class WatermarkSettings {
    // 唯一标识符
    var id: String = "watermark_settings"
    
    // 选中的水印Logo
    var selectedLogo: String = ""
    
    // 选中的水印Logo2（用于自定义水印19）
    var selectedLogo2: String = ""
    
    // 选中的水印Logo3（用于自定义水印21）
    var selectedLogo3: String = ""
    
    // 水印文字
    var watermarkText: String = ""
    
    // 是否启用水印文字
    var isWatermarkTextEnabled: Bool = false
    
    // 水印描述 - 为自定义水印13特殊添加
    var watermarkDescription: String = ""
    
    // 是否启用水印描述 - 为自定义水印13特殊添加
    var isWatermarkDescriptionEnabled: Bool = false
    
    // 水印署名
    var watermarkSignature: String = ""
    
    // 是否启用水印署名
    var isWatermarkSignatureEnabled: Bool = false
    
    // 水印偏好选项 (OFF, 参数, 经纬度, 日期)
    var preferenceOption: String = "OFF"
    
    // 新增：多选偏好选项数组 (存储所有选中的选项)
    var selectedPreferences: [String] = []
    
    // 新增：记录选择顺序 (保存偏好选项的选中顺序)
    var preferenceSelectionOrder: [String] = []
    
    // 水印偏好自定义文本值
    var preferenceCustomText: String = ""
    var preferenceParametersText: String = ""
    var preferenceLocationText: String = ""
    var preferenceDateText: String = ""
    var preferencePlaceText: String = ""
    
    // 水印位置选项 (中, 下)
    var positionOption: String = "中"
    
    // 更新时间
    var updatedAt: Date = Date()
    
    // 新增边框颜色属性
    var borderColorRed: Double
    var borderColorGreen: Double
    var borderColorBlue: Double
    var borderColorAlpha: Double
    
    // 新增：边框粗细乘数 (0.0 到 1.0)
    var borderThicknessMultiplier: Double
    
    // 新增：上下边框粗细乘数 (0.0 到 1.0)，专门用于水印1
    var topBottomBorderThicknessMultiplier: Double
    
    // 新增：左右边框粗细乘数 (0.0 到 1.0)，专门用于水印18
    var leftRightBorderThicknessMultiplier: Double
    
    // 新增：当前激活的水印样式类型 ("none", "border_2percent", "polaroid", etc.)
    var activeWatermarkStyleType: String
    
    // 新增字体颜色属性
    var fontColorRed: Double
    var fontColorGreen: Double
    var fontColorBlue: Double
    var fontColorAlpha: Double
    
    // 新增：模糊边框属性
    var isBlurBorderEnabled: Bool
    var blurIntensity: Double // 保留 Double 类型以便向下兼容
    var blurStyle: String // 新增：用于存储样式名称
    
    // 新增：阴影效果属性
    var isShadowEnabled: Bool
    
    // 新增字体粗细属性 (0.0 到 1.0)
    var fontThicknessMultiplier: Double
    
    // 新增字体选项
    var selectedFontName: String
    
    // 新增：选中的中文字体粗细名称（如"Regular"、"Bold"等）
    var selectedFontWeight: String
    
    // 新增：选中的署名字体粗细名称
    var selectedSignatureFontWeight: String
    
    // 新增 Logo 颜色属性
    var logoColorRed: Double
    var logoColorGreen: Double
    var logoColorBlue: Double
    var logoColorAlpha: Double
    
    // 新增：边框激活边 (top, right, bottom, left)
    var activeBorderSides: [String]
    
    // 新增：水印边框是否启用
    var isWatermarkRimEnabled: Bool
    
    // 新增：水印字体粗细
    var watermarkFontWeight: Double
    
    // 新增：署名字体粗细 (0.0 到 1.0)
    var signatureFontThicknessMultiplier: Double
    
    // 新增：宽边框粗细 (0.0 到 1.0，会根据水印类型映射到不同范围)
    var wideBorderThicknessMultiplier: Double
    
    // 新增：水印6上下边框等宽模式开关
    var isEqualWidthBorderEnabled: Bool
    
    // 新增：偏好选项分行显示开关
    var isMultilinePreferenceEnabled: Bool
    
    // 新增：自定义水印5右侧宽边框开关
    var isRightWideBorderEnabled: Bool
    
    // 新增：偏好选项缩放比例 (0.75-1.25，默认1.0)
    var preferenceScaleFactor: Double
    
    // 新增：署名大小乘数 (0.5 到 2.0，默认1.0)
    var signatureFontSizeMultiplier: Double
    
    // 新增：文字大小乘数 (0.5 到 2.0，默认1.0)
    var textFontSizeMultiplier: Double
    
    // 新增：Logo大小乘数 (0.5 到 1.5，默认1.0)
    var logoSizeMultiplier: Double
    
    // 新增：英文字体粗细 (0.0 到 1.0)
    var englishFontThicknessMultiplier: Double
    
    // 新增：英文字体选项
    var selectedEnglishFontName: String
    
    // 新增：选中的英文字体粗细名称（如"Bold"、"Light"等）
    var selectedEnglishFontWeight: String
    
    // 初始化方法
    init() {
        // 为所有属性提供默认值，包括新增的边框颜色
        self.selectedLogo = ""
        self.selectedLogo2 = ""
        self.selectedLogo3 = ""
        self.watermarkText = ""
        self.isWatermarkTextEnabled = false
        self.watermarkDescription = ""
        self.isWatermarkDescriptionEnabled = false
        self.watermarkSignature = ""
        self.isWatermarkSignatureEnabled = false
        self.preferenceOption = "OFF"
        self.selectedPreferences = [] // 初始化为空数组
        self.preferenceSelectionOrder = [] // 初始化为空数组
        self.preferenceCustomText = ""
        self.preferenceParametersText = "50mm f/1.8 1/125s ISO100"
        self.preferenceLocationText = "39.9042° N, 116.4074° E"
        self.preferenceDateText = ""  // 日期会动态生成
        self.preferencePlaceText = "北京市海淀区"
        self.positionOption = "中"
        self.borderColorRed = 1.0 // 默认为白色
        self.borderColorGreen = 1.0
        self.borderColorBlue = 1.0
        self.borderColorAlpha = 1.0
        self.borderThicknessMultiplier = 0.2 // 默认20%的粗细，相对于最大预设值
        self.topBottomBorderThicknessMultiplier = 0.2 // 默认20%的上下边框粗细
        self.leftRightBorderThicknessMultiplier = 0.2 // 默认20%的左右边框粗细
        self.activeWatermarkStyleType = "none" // 默认无水印样式
        self.activeBorderSides = ["top", "right", "bottom", "left"] // 默认四边都激活
        self.isWatermarkRimEnabled = false
        self.watermarkFontWeight = 0.5 // 默认中等粗细
        self.isBlurBorderEnabled = false
        self.blurIntensity = 0.5 // 默认50%强度
        self.blurStyle = "systemMaterial" // 默认使用标准系统材质
        self.isShadowEnabled = false // 默认禁用阴影效果
        self.fontColorRed = 0.0 // 默认为黑色
        self.fontColorGreen = 0.0
        self.fontColorBlue = 0.0
        self.fontColorAlpha = 1.0
        self.fontThicknessMultiplier = 0.5 // 默认50%的字体粗细
        self.selectedFontName = "系统" // 默认使用系统字体
        self.logoColorRed = 0.0 // 默认为黑色
        self.logoColorGreen = 0.0
        self.logoColorBlue = 0.0
        self.logoColorAlpha = 1.0
        self.updatedAt = Date()
        self.signatureFontThicknessMultiplier = 0.5 // 默认50%的署名字体粗细
        self.wideBorderThicknessMultiplier = 0.0 // 默认0%滑块，对应各水印的最小值
        self.isEqualWidthBorderEnabled = false // 默认关闭等宽模式
        self.isMultilinePreferenceEnabled = false // 默认关闭分行显示
        self.isRightWideBorderEnabled = false // 默认宽边框在左侧
        self.preferenceScaleFactor = 1.0 // 默认1.0倍缩放比例，对应屏幕高度的2%
        self.signatureFontSizeMultiplier = 1.0 // 默认100%的署名大小
        self.textFontSizeMultiplier = 1.0 // 默认100%的文字大小
        self.logoSizeMultiplier = 1.0 // 默认1.0倍Logo大小
        self.englishFontThicknessMultiplier = 0.5 // 默认50%的英文字体粗细
        self.selectedEnglishFontName = "" // 默认英文字体为空，使用系统字体
        self.selectedEnglishFontWeight = "" // 默认选中的英文字体粗细名称为空
        self.selectedFontWeight = "" // 新增选中的中文字体粗细名称
        self.selectedSignatureFontWeight = "" // 新增选中的署名字体粗细名称
    }
    
    // 自定义初始化方法
    init(selectedLogo: String = "",
         selectedLogo2: String = "",
         selectedLogo3: String = "",
         watermarkText: String = "",
         isWatermarkTextEnabled: Bool = false,
        watermarkDescription: String = "",
        isWatermarkDescriptionEnabled: Bool = false,
         watermarkSignature: String = "",
         isWatermarkSignatureEnabled: Bool = false,
         preferenceOption: String = "OFF",
         selectedPreferences: [String] = [], // 添加新参数
         preferenceSelectionOrder: [String] = [], // 添加新参数
         positionOption: String = "中",
         // 初始化新增的边框颜色属性
         borderColorRed: Double = 1.0, // 默认为白色
         borderColorGreen: Double = 1.0,
         borderColorBlue: Double = 1.0,
         borderColorAlpha: Double = 1.0,
         borderThicknessMultiplier: Double = 0.2, // 新增参数并设默认值
         topBottomBorderThicknessMultiplier: Double = 0.2, // 新增上下边框参数
         leftRightBorderThicknessMultiplier: Double = 0.2, // 新增左右边框参数
         activeWatermarkStyleType: String = "none", // 新增参数
         fontColorRed: Double = 0.0, // 默认为黑色
         fontColorGreen: Double = 0.0,
         fontColorBlue: Double = 0.0,
         fontColorAlpha: Double = 1.0,
         isBlurBorderEnabled: Bool = false, // 新增模糊边框参数
         blurIntensity: Double = 0.7, // 新增模糊强度参数
         blurStyle: String = "regular", // 新增模糊样式参数
         isShadowEnabled: Bool = false, // 新增阴影效果参数
         fontThicknessMultiplier: Double = 0.5, // 默认50%的字体粗细
         selectedFontName: String = "系统", // 默认使用系统字体
         logoColorRed: Double = 0.0, // 默认为黑色
         logoColorGreen: Double = 0.0,
         logoColorBlue: Double = 0.0,
         logoColorAlpha: Double = 1.0,
         activeBorderSides: [String] = ["top", "right", "bottom", "left"], // 新增边框激活边参数
         isWatermarkRimEnabled: Bool = false, // 新增水印边框是否启用参数
         watermarkFontWeight: Double = 0.5, // 新增水印字体粗细参数
         signatureFontThicknessMultiplier: Double = 0.5, // 新增署名字体粗细参数
         wideBorderThicknessMultiplier: Double = 0.0, // 新增宽边框粗细参数，默认为0（对应各水印的最小值）
         isEqualWidthBorderEnabled: Bool = false, // 新增水印6等宽边框参数
         isMultilinePreferenceEnabled: Bool = false, // 新增偏好选项分行显示参数
         isRightWideBorderEnabled: Bool = false, // 新增自定义水印5右侧宽边框参数
         preferenceScaleFactor: Double = 1.0,
         signatureFontSizeMultiplier: Double = 1.0,
         textFontSizeMultiplier: Double = 1.0,
         logoSizeMultiplier: Double = 1.0,
         englishFontThicknessMultiplier: Double = 0.5,
         selectedEnglishFontName: String = "",
         selectedEnglishFontWeight: String = "",
         selectedFontWeight: String = "",
         selectedSignatureFontWeight: String = "") { // 默认英文字体为空，使用系统字体
        self.selectedLogo = selectedLogo
        self.selectedLogo2 = selectedLogo2
        self.selectedLogo3 = selectedLogo3
        self.watermarkText = watermarkText
        self.isWatermarkTextEnabled = isWatermarkTextEnabled
        self.watermarkDescription = watermarkDescription
        self.isWatermarkDescriptionEnabled = isWatermarkDescriptionEnabled
        self.watermarkSignature = watermarkSignature
        self.isWatermarkSignatureEnabled = isWatermarkSignatureEnabled
        self.preferenceOption = preferenceOption
        self.selectedPreferences = selectedPreferences
        self.preferenceSelectionOrder = preferenceSelectionOrder
        self.positionOption = positionOption
        self.borderColorRed = borderColorRed
        self.borderColorGreen = borderColorGreen
        self.borderColorBlue = borderColorBlue
        self.borderColorAlpha = borderColorAlpha
        self.borderThicknessMultiplier = borderThicknessMultiplier // 初始化新增属性
        self.topBottomBorderThicknessMultiplier = topBottomBorderThicknessMultiplier // 初始化上下边框属性
        self.leftRightBorderThicknessMultiplier = leftRightBorderThicknessMultiplier // 初始化左右边框属性
        self.activeWatermarkStyleType = activeWatermarkStyleType // 初始化新增属性
        self.fontColorRed = fontColorRed
        self.fontColorGreen = fontColorGreen
        self.fontColorBlue = fontColorBlue
        self.fontColorAlpha = fontColorAlpha
        self.isBlurBorderEnabled = isBlurBorderEnabled // 初始化模糊边框属性
        self.blurIntensity = blurIntensity // 初始化模糊强度属性
        self.blurStyle = blurStyle // 初始化模糊样式属性
        self.isShadowEnabled = isShadowEnabled // 初始化阴影效果属性
        self.fontThicknessMultiplier = fontThicknessMultiplier // 初始化新增属性
        self.selectedFontName = selectedFontName // 初始化新增属性
        self.logoColorRed = logoColorRed
        self.logoColorGreen = logoColorGreen
        self.logoColorBlue = logoColorBlue
        self.logoColorAlpha = logoColorAlpha
        self.activeBorderSides = activeBorderSides // 初始化边框激活边属性
        self.isWatermarkRimEnabled = isWatermarkRimEnabled // 初始化水印边框是否启用属性
        self.watermarkFontWeight = watermarkFontWeight // 初始化水印字体粗细属性
        self.signatureFontThicknessMultiplier = signatureFontThicknessMultiplier // 初始化署名字体粗细属性
        self.wideBorderThicknessMultiplier = wideBorderThicknessMultiplier // 初始化宽边框粗细属性
        self.isEqualWidthBorderEnabled = isEqualWidthBorderEnabled // 初始化等宽边框属性
        self.isMultilinePreferenceEnabled = isMultilinePreferenceEnabled // 初始化偏好分行显示属性
        self.isRightWideBorderEnabled = isRightWideBorderEnabled // 初始化右侧宽边框属性
        self.preferenceScaleFactor = preferenceScaleFactor // 初始化偏好选项缩放比例属性
        self.signatureFontSizeMultiplier = signatureFontSizeMultiplier // 初始化署名大小乘数
        self.textFontSizeMultiplier = textFontSizeMultiplier // 初始化文字大小乘数
        self.logoSizeMultiplier = logoSizeMultiplier // 初始化Logo大小乘数
        self.englishFontThicknessMultiplier = englishFontThicknessMultiplier // 初始化英文字体粗细属性
        self.selectedEnglishFontName = selectedEnglishFontName // 初始化英文字体选项
        self.selectedEnglishFontWeight = selectedEnglishFontWeight // 初始化选中的英文字体粗细名称
        self.selectedFontWeight = selectedFontWeight // 初始化选中的中文字体粗细名称
        self.selectedSignatureFontWeight = selectedSignatureFontWeight // 初始化选中的署名字体粗细名称
        self.updatedAt = Date()
    }
    
    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }
    
    // 从另一个设置实例复制数据（用于克隆）
    func copyFrom(_ other: WatermarkSettings) {
        self.id = other.id
        self.selectedLogo = other.selectedLogo
        self.selectedLogo2 = other.selectedLogo2
        self.selectedLogo3 = other.selectedLogo3
        self.watermarkText = other.watermarkText
        self.isWatermarkTextEnabled = other.isWatermarkTextEnabled
        self.watermarkDescription = other.watermarkDescription
        self.isWatermarkDescriptionEnabled = other.isWatermarkDescriptionEnabled
        self.watermarkSignature = other.watermarkSignature
        self.isWatermarkSignatureEnabled = other.isWatermarkSignatureEnabled
        self.preferenceOption = other.preferenceOption
        self.selectedPreferences = other.selectedPreferences
        self.preferenceSelectionOrder = other.preferenceSelectionOrder
        self.preferenceCustomText = other.preferenceCustomText
        self.preferenceParametersText = other.preferenceParametersText
        self.preferenceLocationText = other.preferenceLocationText
        self.preferenceDateText = other.preferenceDateText
        self.preferencePlaceText = other.preferencePlaceText
        self.positionOption = other.positionOption
        self.borderColorRed = other.borderColorRed
        self.borderColorGreen = other.borderColorGreen
        self.borderColorBlue = other.borderColorBlue
        self.borderColorAlpha = other.borderColorAlpha
        self.borderThicknessMultiplier = other.borderThicknessMultiplier
        self.topBottomBorderThicknessMultiplier = other.topBottomBorderThicknessMultiplier
        self.leftRightBorderThicknessMultiplier = other.leftRightBorderThicknessMultiplier
        self.activeWatermarkStyleType = other.activeWatermarkStyleType
        self.fontColorRed = other.fontColorRed
        self.fontColorGreen = other.fontColorGreen
        self.fontColorBlue = other.fontColorBlue
        self.fontColorAlpha = other.fontColorAlpha
        self.isBlurBorderEnabled = other.isBlurBorderEnabled
        self.blurIntensity = other.blurIntensity
        self.blurStyle = other.blurStyle
        self.isShadowEnabled = other.isShadowEnabled
        self.fontThicknessMultiplier = other.fontThicknessMultiplier
        self.selectedFontName = other.selectedFontName
        self.logoColorRed = other.logoColorRed
        self.logoColorGreen = other.logoColorGreen
        self.logoColorBlue = other.logoColorBlue
        self.logoColorAlpha = other.logoColorAlpha
        self.activeBorderSides = other.activeBorderSides
        self.isWatermarkRimEnabled = other.isWatermarkRimEnabled
        self.watermarkFontWeight = other.watermarkFontWeight
        self.signatureFontThicknessMultiplier = other.signatureFontThicknessMultiplier
        self.wideBorderThicknessMultiplier = other.wideBorderThicknessMultiplier
        self.isEqualWidthBorderEnabled = other.isEqualWidthBorderEnabled
        self.isMultilinePreferenceEnabled = other.isMultilinePreferenceEnabled
        self.isRightWideBorderEnabled = other.isRightWideBorderEnabled
        self.preferenceScaleFactor = other.preferenceScaleFactor
        self.signatureFontSizeMultiplier = other.signatureFontSizeMultiplier
        self.textFontSizeMultiplier = other.textFontSizeMultiplier
        self.logoSizeMultiplier = other.logoSizeMultiplier
        self.englishFontThicknessMultiplier = other.englishFontThicknessMultiplier
        self.selectedEnglishFontName = other.selectedEnglishFontName
        self.selectedEnglishFontWeight = other.selectedEnglishFontWeight
        self.selectedFontWeight = other.selectedFontWeight
        self.selectedSignatureFontWeight = other.selectedSignatureFontWeight
        self.updatedAt = other.updatedAt
    }
    
    // 构造函数重载，允许指定所有参数
    init(id: String = "watermark_settings",
         selectedLogo: String = "",
         selectedLogo2: String = "",
         selectedLogo3: String = "",
         watermarkText: String = "",
         isWatermarkTextEnabled: Bool = false,
         watermarkDescription: String = "",
         isWatermarkDescriptionEnabled: Bool = false,
         watermarkSignature: String = "",
         isWatermarkSignatureEnabled: Bool = false,
         preferenceOption: String = "OFF",
         selectedPreferences: [String] = [],
         preferenceSelectionOrder: [String] = [],
         positionOption: String = "中",
         borderColorRed: Double = 1.0,
         borderColorGreen: Double = 1.0,
         borderColorBlue: Double = 1.0,
         borderColorAlpha: Double = 1.0,
         borderThicknessMultiplier: Double = 0.2,
         topBottomBorderThicknessMultiplier: Double = 0.2,
         leftRightBorderThicknessMultiplier: Double = 0.2,
         activeWatermarkStyleType: String = "none",
         fontColorRed: Double = 0.0,
         fontColorGreen: Double = 0.0,
         fontColorBlue: Double = 0.0,
         fontColorAlpha: Double = 1.0,
         isBlurBorderEnabled: Bool = false,
         blurIntensity: Double = 0.7,
         blurStyle: String = "regular",
         isShadowEnabled: Bool = false,
         fontThicknessMultiplier: Double = 0.5,
         selectedFontName: String = "系统",
         logoColorRed: Double = 0.0,
         logoColorGreen: Double = 0.0,
         logoColorBlue: Double = 0.0,
         logoColorAlpha: Double = 1.0,
         activeBorderSides: [String] = ["top", "right", "bottom", "left"],
         isWatermarkRimEnabled: Bool = false,
         watermarkFontWeight: Double = 0.5,
         signatureFontThicknessMultiplier: Double = 0.5,
         wideBorderThicknessMultiplier: Double = 0.0,
         isEqualWidthBorderEnabled: Bool = false,
         isMultilinePreferenceEnabled: Bool = false,
         isRightWideBorderEnabled: Bool = false,
         preferenceScaleFactor: Double = 1.0,
         signatureFontSizeMultiplier: Double = 1.0,
         textFontSizeMultiplier: Double = 1.0,
         logoSizeMultiplier: Double = 1.0,
         englishFontThicknessMultiplier: Double = 0.5,
         selectedEnglishFontName: String = "",
         selectedEnglishFontWeight: String = "",
         selectedFontWeight: String = "",
         selectedSignatureFontWeight: String = "") {
        self.id = id
        self.selectedLogo = selectedLogo
        self.selectedLogo2 = selectedLogo2
        self.selectedLogo3 = selectedLogo3
        self.watermarkText = watermarkText
        self.isWatermarkTextEnabled = isWatermarkTextEnabled
        self.watermarkDescription = watermarkDescription
        self.isWatermarkDescriptionEnabled = isWatermarkDescriptionEnabled
        self.watermarkSignature = watermarkSignature
        self.isWatermarkSignatureEnabled = isWatermarkSignatureEnabled
        self.preferenceOption = preferenceOption
        self.selectedPreferences = selectedPreferences
        self.preferenceSelectionOrder = preferenceSelectionOrder
        self.positionOption = positionOption
        self.borderColorRed = borderColorRed
        self.borderColorGreen = borderColorGreen
        self.borderColorBlue = borderColorBlue
        self.borderColorAlpha = borderColorAlpha
        self.borderThicknessMultiplier = borderThicknessMultiplier
        self.topBottomBorderThicknessMultiplier = topBottomBorderThicknessMultiplier
        self.leftRightBorderThicknessMultiplier = leftRightBorderThicknessMultiplier
        self.activeWatermarkStyleType = activeWatermarkStyleType
        self.fontColorRed = fontColorRed
        self.fontColorGreen = fontColorGreen
        self.fontColorBlue = fontColorBlue
        self.fontColorAlpha = fontColorAlpha
        self.isBlurBorderEnabled = isBlurBorderEnabled
        self.blurIntensity = blurIntensity
        self.blurStyle = blurStyle
        self.isShadowEnabled = isShadowEnabled
        self.fontThicknessMultiplier = fontThicknessMultiplier
        self.selectedFontName = selectedFontName
        self.logoColorRed = logoColorRed
        self.logoColorGreen = logoColorGreen
        self.logoColorBlue = logoColorBlue
        self.logoColorAlpha = logoColorAlpha
        self.activeBorderSides = activeBorderSides
        self.isWatermarkRimEnabled = isWatermarkRimEnabled
        self.watermarkFontWeight = watermarkFontWeight
        self.signatureFontThicknessMultiplier = signatureFontThicknessMultiplier
        self.wideBorderThicknessMultiplier = wideBorderThicknessMultiplier
        self.isEqualWidthBorderEnabled = isEqualWidthBorderEnabled
        self.isMultilinePreferenceEnabled = isMultilinePreferenceEnabled
        self.isRightWideBorderEnabled = isRightWideBorderEnabled
        self.preferenceScaleFactor = preferenceScaleFactor
        self.signatureFontSizeMultiplier = signatureFontSizeMultiplier
        self.textFontSizeMultiplier = textFontSizeMultiplier
        self.logoSizeMultiplier = logoSizeMultiplier
        self.englishFontThicknessMultiplier = englishFontThicknessMultiplier
        self.selectedEnglishFontName = selectedEnglishFontName
        self.selectedEnglishFontWeight = selectedEnglishFontWeight
        self.selectedFontWeight = selectedFontWeight
        self.selectedSignatureFontWeight = selectedSignatureFontWeight
        self.updatedAt = Date()
    }
} 