import Foundation
import SwiftData

/// 相纸设置的数据模型
@Model
final class PaperModel {
    // 唯一标识符
    var id: String = "paper_settings"

    // 宝丽来预设
    var selectedPolaroidPreset: Int = 0

    // 胶片预设
    var selectedFilmPreset: Int = 0

    // 复古预设
    var selectedVintagePreset: Int = 0

    // 时尚预设
    var selectedFashionPreset: Int = 0

    // INS风预设
    var selectedINSPreset: Int = 0

    // 活跃的相纸类型和预设索引（控制全局选中状态）
    var activeFilterType: String = ""
    var activePresetIndex: Int = -1

    // 最近使用的预设
    var recentPresets: [String] = []

    // 收藏的预设
    var favoritePresets: [String] = []

    // 更新时间
    var updatedAt: Date = Date()

    // 初始化方法
    init() {}

    // 自定义初始化方法
    init(selectedPolaroidPreset: Int = 0,
         selectedFilmPreset: Int = 0,
         selectedVintagePreset: Int = 0,
         selectedFashionPreset: Int = 0,
         selectedINSPreset: Int = 0,
         activeFilterType: String = "",
         activePresetIndex: Int = -1,
         recentPresets: [String] = [],
         favoritePresets: [String] = []) {
        self.selectedPolaroidPreset = selectedPolaroidPreset
        self.selectedFilmPreset = selectedFilmPreset
        self.selectedVintagePreset = selectedVintagePreset
        self.selectedFashionPreset = selectedFashionPreset
        self.selectedINSPreset = selectedINSPreset
        self.activeFilterType = activeFilterType
        self.activePresetIndex = activePresetIndex
        self.recentPresets = recentPresets
        self.favoritePresets = favoritePresets
        self.updatedAt = Date()
    }

    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }

    // 添加到最近使用的预设
    func addToRecentPresets(_ preset: String) {
        // 如果已经存在，先移除
        if let index = recentPresets.firstIndex(of: preset) {
            recentPresets.remove(at: index)
        }

        // 添加到最前面
        recentPresets.insert(preset, at: 0)

        // 限制最近使用的数量为10个
        if recentPresets.count > 10 {
            recentPresets = Array(recentPresets.prefix(10))
        }

        updateTimestamp()
    }

    // 切换预设收藏状态
    func toggleFavorite(_ preset: String) {
        if favoritePresets.contains(preset) {
            favoritePresets.removeAll { $0 == preset }
        } else {
            favoritePresets.append(preset)
        }

        updateTimestamp()
    }
}
