import Foundation
import SwiftData

/// 滤镜设置的数据模型 (从FilterSettings.swift完整复制)
@Model
final class FilterSettings {
    // 唯一标识符
    var id: String = "filter_settings"

    // 当前选中的滤镜
    var selectedFilter: String = "原图"

    // 滤镜强度
    var filterIntensity: Double = 1.0

    // 最近使用的滤镜
    var recentFilters: [String] = []

    // 收藏的滤镜
    var favoriteFilters: [String] = []

    // 预设选择
    var selectedPolaroidPreset: Int = 0
    var selectedFilmPreset: Int = 0
    var selectedVintagePreset: Int = 0
    var selectedFashionPreset: Int = 0
    var selectedINSPreset: Int = 0

    // 活跃的滤镜类型和预设索引（控制全局选中状态）
    var activeFilterType: String = ""
    var activePresetIndex: Int = -1

    // 更新时间
    var updatedAt: Date = Date()

    // 初始化方法
    init() {}

    // 自定义初始化方法
    init(selectedFilter: String = "原图",
         filterIntensity: Double = 1.0,
         recentFilters: [String] = [],
         favoriteFilters: [String] = [],
         selectedPolaroidPreset: Int = 0,
         selectedFilmPreset: Int = 0,
         selectedVintagePreset: Int = 0,
         selectedFashionPreset: Int = 0,
         selectedINSPreset: Int = 0,
         activeFilterType: String = "",
         activePresetIndex: Int = -1) {
        self.selectedFilter = selectedFilter
        self.filterIntensity = filterIntensity
        self.recentFilters = recentFilters
        self.favoriteFilters = favoriteFilters
        self.selectedPolaroidPreset = selectedPolaroidPreset
        self.selectedFilmPreset = selectedFilmPreset
        self.selectedVintagePreset = selectedVintagePreset
        self.selectedFashionPreset = selectedFashionPreset
        self.selectedINSPreset = selectedINSPreset
        self.activeFilterType = activeFilterType
        self.activePresetIndex = activePresetIndex
        self.updatedAt = Date()
    }

    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }

    // 添加到最近使用的滤镜
    func addToRecentFilters(_ filter: String) {
        // 如果已经存在，先移除
        if let index = recentFilters.firstIndex(of: filter) {
            recentFilters.remove(at: index)
        }

        // 添加到最前面
        recentFilters.insert(filter, at: 0)

        // 限制最近使用的数量为10个
        if recentFilters.count > 10 {
            recentFilters = Array(recentFilters.prefix(10))
        }

        updateTimestamp()
    }

    // 切换滤镜收藏状态
    func toggleFavorite(_ filter: String) {
        if favoriteFilters.contains(filter) {
            favoriteFilters.removeAll { $0 == filter }
        } else {
            favoriteFilters.append(filter)
        }

        updateTimestamp()
    }
}

// MARK: - 滤镜预设相关模型 (从FilterPreset.swift完整复制)

/// 滤镜预设类型枚举
enum FilterPresetType: String, CaseIterable, Codable {
    case polaroid = "宝丽来"
    case film = "胶卷"
    case vintage = "复古"
    case fashion = "时尚"
    case ins = "INS"

    var displayName: String {
        return self.rawValue
    }
}

/// 滤镜预设数据模型
struct FilterPreset: Identifiable, Codable {
    let id: String
    let name: String
    let type: FilterPresetType
    let index: Int // 预设索引 (0-4)

    // MARK: - 滤镜参数
    let exposure: Float
    let contrast: Float
    let saturation: Float
    let brightness: Float
    let gamma: Float
    let temperature: Float
    let tint: Float
    let highlights: Float
    let shadows: Float
    let hue: Float
    let vibrance: Float
    let sharpness: Float
    let clarity: Float
    let clarity2: Float
    let vignetteIntensity: Float
    let vignetteRadius: Float
    let highlightTone: Float
    let midtoneTone: Float
    let shadowTone: Float
    let highlightHue: Float
    let highlightSaturation: Float
    let shadowHue: Float
    let shadowSaturation: Float

    init(
        id: String = UUID().uuidString,
        name: String,
        type: FilterPresetType,
        index: Int,
        exposure: Float = 0.0,
        contrast: Float = 0.0,
        saturation: Float = 0.0,
        brightness: Float = 0.0,
        gamma: Float = 1.0,
        temperature: Float = 0.0,
        tint: Float = 0.0,
        highlights: Float = 0.0,
        shadows: Float = 0.0,
        hue: Float = 0.0,
        vibrance: Float = 0.0,
        sharpness: Float = 0.0,
        clarity: Float = 0.0,
        clarity2: Float = 0.0,
        vignetteIntensity: Float = 0.0,
        vignetteRadius: Float = 1.0,
        highlightTone: Float = 0.0,
        midtoneTone: Float = 0.0,
        shadowTone: Float = 0.0,
        highlightHue: Float = 0.0,
        highlightSaturation: Float = 0.0,
        shadowHue: Float = 0.0,
        shadowSaturation: Float = 0.0
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.index = index
        self.exposure = exposure
        self.contrast = contrast
        self.saturation = saturation
        self.brightness = brightness
        self.gamma = gamma
        self.temperature = temperature
        self.tint = tint
        self.highlights = highlights
        self.shadows = shadows
        self.hue = hue
        self.vibrance = vibrance
        self.sharpness = sharpness
        self.clarity = clarity
        self.clarity2 = clarity2
        self.vignetteIntensity = vignetteIntensity
        self.vignetteRadius = vignetteRadius
        self.highlightTone = highlightTone
        self.midtoneTone = midtoneTone
        self.shadowTone = shadowTone
        self.highlightHue = highlightHue
        self.highlightSaturation = highlightSaturation
        self.shadowHue = shadowHue
        self.shadowSaturation = shadowSaturation
    }
}

// MARK: - 预设配置管理器

class FilterPresetManager {
    static let shared = FilterPresetManager()

    private init() {}

    /// 获取所有预设
    func getAllPresets() -> [FilterPreset] {
        return [
            // 宝丽来预设
            getPolaroidPresets(),
            // 胶卷预设
            getFilmPresets(),
            // 复古预设
            getVintagePresets(),
            // 时尚预设
            getFashionPresets(),
            // INS预设
            getINSPresets()
        ].flatMap { $0 }
    }

    /// 根据类型获取预设
    func getPresets(for type: FilterPresetType) -> [FilterPreset] {
        switch type {
        case .polaroid:
            return getPolaroidPresets()
        case .film:
            return getFilmPresets()
        case .vintage:
            return getVintagePresets()
        case .fashion:
            return getFashionPresets()
        case .ins:
            return getINSPresets()
        }
    }

    /// 根据类型和索引获取特定预设
    func getPreset(type: FilterPresetType, index: Int) -> FilterPreset? {
        let presets = getPresets(for: type)
        return presets.first { $0.index == index }
    }

    /// 从XMP文件更新预设
    /// - Parameters:
    ///   - xmpFilePath: XMP文件路径
    ///   - type: 预设类型
    ///   - index: 预设索引
    ///   - name: 预设名称（可选，如果不提供则使用默认名称）
    /// - Returns: 是否成功更新
    func updatePresetFromXMP(xmpFilePath: String, type: FilterPresetType, index: Int, name: String? = nil) -> Bool {
        guard let xmpContent = try? String(contentsOfFile: xmpFilePath, encoding: .utf8) else {
            print("❌ 无法读取XMP文件: \(xmpFilePath)")
            return false
        }

        return updatePresetFromXMPContent(xmpContent: xmpContent, type: type, index: index, name: name)
    }

    /// 从XMP内容更新预设
    /// - Parameters:
    ///   - xmpContent: XMP文件内容
    ///   - type: 预设类型
    ///   - index: 预设索引
    ///   - name: 预设名称（可选）
    /// - Returns: 是否成功更新
    func updatePresetFromXMPContent(xmpContent: String, type: FilterPresetType, index: Int, name: String? = nil) -> Bool {
        // 解析XMP参数
        let xmpParams = XMPParser.parseXMP(from: xmpContent)

        // 打印解析结果（调试用）
        print("📄 正在解析XMP参数...")
        XMPParser.printXMPParameters(xmpParams)

        // 生成预设名称
        let presetName = name ?? "XMP预设\(index + 1)"

        // 转换为FilterPreset
        let newPreset = XMPParser.convertToFilterPreset(
            from: xmpParams,
            name: presetName,
            type: type,
            index: index
        )

        // 更新预设（这里需要修改具体的预设数组）
        updatePresetInMemory(newPreset, type: type, index: index)

        print("✅ 成功从XMP更新预设: \(presetName)")
        return true
    }

    /// 在内存中更新预设
    /// - Parameters:
    ///   - preset: 新的预设
    ///   - type: 预设类型
    ///   - index: 预设索引
    private func updatePresetInMemory(_ preset: FilterPreset, type: FilterPresetType, index: Int) {
        // 注意：由于当前的预设是通过方法动态生成的，
        // 这里我们需要一个持久化的存储机制
        // 暂时先打印更新信息，实际应用中可以保存到UserDefaults或文件
        print("🔄 更新预设到内存:")
        print("  类型: \(type.displayName)")
        print("  索引: \(index)")
        print("  名称: \(preset.name)")
        print("  曝光: \(preset.exposure)")
        print("  对比度: \(preset.contrast)")
        print("  饱和度: \(preset.saturation)")
        print("  色温: \(preset.temperature)")
        print("  高光: \(preset.highlights)")
        print("  阴影: \(preset.shadows)")

        // TODO: 实现实际的预设更新逻辑
        // 可以考虑：
        // 1. 保存到UserDefaults
        // 2. 保存到本地JSON文件
        // 3. 使用Core Data
        // 4. 修改预设生成方法以支持动态预设
    }
}

// MARK: - 具体预设定义

extension FilterPresetManager {

    /// 宝丽来预设 - 全部重置为默认值
    private func getPolaroidPresets() -> [FilterPreset] {
        return [
            FilterPreset(name: "经典宝丽来", type: .polaroid, index: 0),
            FilterPreset(name: "暖调宝丽来", type: .polaroid, index: 1),
            FilterPreset(name: "柔和宝丽来", type: .polaroid, index: 2),
            FilterPreset(name: "复古宝丽来", type: .polaroid, index: 3),
            FilterPreset(name: "梦幻宝丽来", type: .polaroid, index: 4)
        ]
    }

    /// 胶卷预设 - 全部重置为默认值
    private func getFilmPresets() -> [FilterPreset] {
        return [
            FilterPreset(name: "Kodak Gold", type: .film, index: 0),
            FilterPreset(name: "Fuji Pro", type: .film, index: 1),
            FilterPreset(name: "Ilford HP5", type: .film, index: 2),
            FilterPreset(name: "Portra 400", type: .film, index: 3),
            FilterPreset(name: "Tri-X 400", type: .film, index: 4)
        ]
    }

    /// 复古预设 - 全部重置为默认值
    private func getVintagePresets() -> [FilterPreset] {
        return [
            FilterPreset(name: "经典复古", type: .vintage, index: 0),
            FilterPreset(name: "怀旧复古", type: .vintage, index: 1),
            FilterPreset(name: "褪色复古", type: .vintage, index: 2),
            FilterPreset(name: "暖调复古", type: .vintage, index: 3),
            FilterPreset(name: "胶片复古", type: .vintage, index: 4)
        ]
    }

    /// 时尚预设 - 全部重置为默认值
    private func getFashionPresets() -> [FilterPreset] {
        return [
            FilterPreset(name: "时尚大片", type: .fashion, index: 0),
            FilterPreset(name: "冷调时尚", type: .fashion, index: 1),
            FilterPreset(name: "暖调时尚", type: .fashion, index: 2),
            FilterPreset(name: "高对比时尚", type: .fashion, index: 3),
            FilterPreset(name: "柔和时尚", type: .fashion, index: 4)
        ]
    }

    /// INS预设 - 全部重置为默认值
    private func getINSPresets() -> [FilterPreset] {
        return [
            FilterPreset(name: "INS经典", type: .ins, index: 0),
            FilterPreset(name: "INS粉调", type: .ins, index: 1),
            FilterPreset(name: "INS蓝调", type: .ins, index: 2),
            FilterPreset(name: "INS橙调", type: .ins, index: 3),
            FilterPreset(name: "INS绿调", type: .ins, index: 4)
        ]
    }
}