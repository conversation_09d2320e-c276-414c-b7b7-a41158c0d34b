import Foundation
import SwiftUI

// MARK: - 特效功能相关模型

/// 特效参数
struct EffectParameters {
    // 特效开关状态
    var isTimeEnabled: Bool = false
    var isGrainEnabled: Bool = false
    var isScratchEnabled: Bool = false
    var isLeakEnabled: Bool = false
    
    // 特效参数
    var selectedTimeStyle: String = ""
    var grainIntensity: Double = 0.0
    var scratchIntensity: Double = 0.0
    var leakIntensity: Double = 0.0
    var leakRandomValue: Int = Int.random(in: 1...100)
    var randomSeed: Int = Int.random(in: 0...1000)
}

/// 胶卷类型
struct FilmStock {
    let id: String
    let name: String
    let previewImage: String
    let category: FilmCategory
    var isFavorite: Bool = false
}

/// 胶卷类别
enum FilmCategory: String, CaseIterable {
    case polaroid = "宝丽来"
    case film = "胶片"
    case vintage = "复古"
    case fashion = "时尚"
    case ins = "INS风"
}

/// 胶片预设选择状态
struct FilmPresetSelections {
    var selectedPolaroidPreset: Int = 0
    var selectedFilmPreset: Int = 0
    var selectedVintagePreset: Int = 0
    var selectedFashionPreset: Int = 0
    var selectedINSPreset: Int = 0
} 