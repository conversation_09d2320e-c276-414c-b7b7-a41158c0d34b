// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftData

/// 构图设置的数据模型
@Model
final class CropModel {
    // 唯一标识符
    var id: String = "crop_settings"

    // 裁剪刻度偏移量
    var cropScaleDragOffset: Double = 0.0

    // 上次拖动结束时的偏移量
    var lastDragOffset: Double = 0.0

    // 旋转角度
    var rotationAngle: Double = 0.0

    // 选中的宽高比
    var selectedRatio: String = "original"

    // 更新时间
    var updatedAt: Date = Date()

    // 初始化方法
    init() {}

    // 自定义初始化方法
    init(cropScaleDragOffset: Double = 0.0,
         lastDragOffset: Double = 0.0,
         rotationAngle: Double = 0.0,
         selectedRatio: String = "original") {
        self.cropScaleDragOffset = cropScaleDragOffset
        self.lastDragOffset = lastDragOffset
        self.rotationAngle = rotationAngle
        self.selectedRatio = selectedRatio
        self.updatedAt = Date()
    }

    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }
}
