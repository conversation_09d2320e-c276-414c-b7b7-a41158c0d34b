import Foundation
import SwiftUI

// MARK: - 编辑功能相关模型

/// 水印/文本偏好选项
enum PreferenceOption: String, CaseIterable {
    case off = "关闭"
    case date = "日期"
    case location = "位置"
    case custom = "自定义"
    case dateLocation = "日期&位置"
}

/// 水印/文本位置选项
enum PositionOption: String, CaseIterable {
    case topLeft = "左上"
    case topCenter = "上中"
    case topRight = "右上"
    case center = "中心"
    case bottomLeft = "左下"
    case bottomCenter = "下中"
    case bottomRight = "右下"
}

// 注意: WatermarkCategory已在WatermarkView.swift中定义
// 注意: MainGalleryTab已在GalleryView.swift中定义
// 注意: AlbumCategory和Album已在GalleryView.swift中定义

/// 编辑参数
struct EditParameters {
    // 构图相关
    var cropScaleDragOffset: CGFloat = 0.0
    var lastDragOffset: CGFloat = 0.0
    var rotationAngle: Double = 0.0
    var selectedRatio: String = "original"
    
    // 水印相关
    var textInput: String = ""
    var isTextEnabled: Bool = false
    var selectedPreference: PreferenceOption = .off
    var selectedPosition: PositionOption = .center
} 