import Foundation
import SwiftData

/// 特效设置的数据模型
@Model
final class EffectsModel {
    // 唯一标识符
    var id: String = "effect_settings"

    // 时间戳效果设置
    var isTimeEnabled: Bool = false
    var selectedTimeStyle: String = ""
    var selectedTimeColor: String = "orange" // 默认橙色
    var selectedTimePosition: String = "bottomLeft" // 新增：时间戳位置，默认左下角

    // 颗粒效果设置
    var isGrainEnabled: Bool = false
    var grainIntensity: Double = 0.0
    var selectedGrainPreset: String = ""

    // 划痕效果设置
    var isScratchEnabled: Bool = false
    var scratchIntensity: Double = 0.0
    var selectedScratchPreset: String = ""

    // 漏光效果设置
    var isLeakEnabled: Bool = false
    var leakIntensity: Double = 0.0
    var leakRandomValue: Int = 1
    var selectedLeakPreset: String = ""

    // 更新时间
    var updatedAt: Date = Date()

    // 初始化方法
    init() {}

    // 自定义初始化方法
    init(isTimeEnabled: Bool = false,
         selectedTimeStyle: String = "",
         selectedTimeColor: String = "orange",
         selectedTimePosition: String = "bottomLeft",
         isGrainEnabled: Bool = false,
         grainIntensity: Double = 0.0,
         selectedGrainPreset: String = "",
         isScratchEnabled: Bool = false,
         scratchIntensity: Double = 0.0,
         selectedScratchPreset: String = "",
         isLeakEnabled: Bool = false,
         leakIntensity: Double = 0.0,
         leakRandomValue: Int = 1,
         selectedLeakPreset: String = "") {
        self.isTimeEnabled = isTimeEnabled
        self.selectedTimeStyle = selectedTimeStyle
        self.selectedTimeColor = selectedTimeColor
        self.selectedTimePosition = selectedTimePosition
        self.isGrainEnabled = isGrainEnabled
        self.grainIntensity = grainIntensity
        self.selectedGrainPreset = selectedGrainPreset
        self.isScratchEnabled = isScratchEnabled
        self.scratchIntensity = scratchIntensity
        self.selectedScratchPreset = selectedScratchPreset
        self.isLeakEnabled = isLeakEnabled
        self.leakIntensity = leakIntensity
        self.leakRandomValue = leakRandomValue
        self.selectedLeakPreset = selectedLeakPreset
        self.updatedAt = Date()
    }

    // 更新时间戳
    func updateTimestamp() {
        updatedAt = Date()
    }

    // 随机生成新的漏光效果
    func randomizeLeakEffect() {
        leakRandomValue = Int.random(in: 1...100)
        updateTimestamp()
    }
}
