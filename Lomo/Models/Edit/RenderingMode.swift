// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

/// 渲染模式枚举 - 区分不同的滤镜算法和渲染质量
enum RenderingMode {
    case lightroom     // Lightroom风格算法 - 用于传统调整和非胶片滤镜
    case vsco         // VSCO风格算法 - 用于胶片滤镜
    case realtime     // 实时渲染模式 - 用于预览和交互
    case highQuality  // 高质量渲染模式 - 用于最终输出
    case preview      // 预览模式 - 用于快速预览和批量操作
    
    var displayName: String {
        switch self {
        case .lightroom:
            return "标准调整模式"
        case .vsco:
            return "胶片调整模式"
        case .realtime:
            return "实时渲染"
        case .highQuality:
            return "高质量渲染"
        case .preview:
            return "预览模式"
        }
    }
    
    var shaderFunctionName: String {
        switch self {
        case .lightroom:
            return "lightroom_filter"
        case .vsco:
            return "vsco_filter"
        case .realtime:
            return "realtime_filter"
        case .highQuality:
            return "highquality_filter"
        case .preview:
            return "preview_filter"
        }
    }
    
    var description: String {
        switch self {
        case .lightroom:
            return "基于Adobe Lightroom算法的专业调色"
        case .vsco:
            return "基于胶片特性的艺术化处理"
        case .realtime:
            return "优化的实时渲染，适合预览和交互"
        case .highQuality:
            return "高质量渲染，适合最终输出"
        case .preview:
            return "快速预览模式，适合批量操作和快速预览"
        }
    }
}

/// 滤镜类型到渲染模式的映射
extension FilterPresetType {
    var renderingMode: RenderingMode {
        switch self {
        case .film:
            return .vsco  // 胶片滤镜使用VSCO算法
        case .polaroid, .vintage, .fashion, .ins:
            return .lightroom  // 其他滤镜使用Lightroom算法
        }
    }
}
