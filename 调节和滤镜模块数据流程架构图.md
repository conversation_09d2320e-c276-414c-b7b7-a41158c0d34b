# 🔄 调节模块和滤镜应用模块数据流程架构图

## 📋 图表说明
- **图表类型**: 数据流程图 + 操作流程图 + 架构关系图
- **分析对象**: 调节模块(Adjust) + 滤镜应用模块(Filter)
- **目的**: 可视化展示模块间的数据流动和操作流程
- **版权方**: LoniceraLab

---

## 🏗️ 整体架构关系图

```
┌─────────────────────────────────────────────────────────────────┐
│                    Lomo 编辑系统架构图                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │   AdjustView    │    │   FilterView    │                    │
│  │   (UI层)        │    │   (UI层)        │                    │
│  │                 │    │                 │                    │
│  │ • 参数选择界面   │    │ • 预设选择界面   │                    │
│  │ • 滑块控件      │    │ • 滤镜分类展示   │                    │
│  │ • 曲线编辑器    │    │ • 强度调整      │                    │
│  │ • HSL颜色选择   │    │ • 状态指示      │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                            │
│           ▼                       ▼                            │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │ AdjustViewModel │    │ FilterViewModel │                    │
│  │  (ViewModel层)  │    │  (ViewModel层)  │                    │
│  │                 │    │                 │                    │
│  │ • @Published    │    │ • @Published    │                    │
│  │ • 状态管理      │    │ • 状态管理      │                    │
│  │ • 业务编排      │    │ • 业务编排      │                    │
│  │ • UI逻辑       │    │ • UI逻辑       │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                            │
│           ▼                       ▼                            │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │  AdjustService  │◄──►│  FilterService  │                    │
│  │   (Service层)   │    │   (Service层)   │                    │
│  │                 │    │                 │                    │
│  │ • 参数处理      │    │ • 预设管理      │                    │
│  │ • 曲线计算      │    │ • LUT处理       │                    │
│  │ • HSL处理       │    │ • 图像处理      │                    │
│  │ • 数据持久化    │    │ • 状态持久化    │                    │
│  └─────────────────┘    └─────────────────┘                    │
│           │                       │                            │
│           └───────────┬───────────┘                            │
│                       ▼                                        │
│              ┌─────────────────┐                               │
│              │ FilterParameters│                               │
│              │   (共享模型)    │                               │
│              │                 │                               │
│              │ • 统一参数模型   │                               │
│              │ • 所有调整参数   │                               │
│              │ • 滤镜参数      │                               │
│              │ • 状态标识      │                               │
│              └─────────────────┘                               │
│                       │                                        │
│                       ▼                                        │
│              ┌─────────────────┐                               │
│              │  Metal渲染器    │                               │
│              │   (渲染层)      │                               │
│              │                 │                               │
│              │ • 实时渲染      │                               │
│              │ • LUT处理       │                               │
│              │ • 图像输出      │                               │
│              │ • 性能优化      │                               │
│              └─────────────────┘                               │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 📊 调节模块数据流程图

```
┌─────────────────────────────────────────────────────────────────┐
│                    调节模块数据流程图                            │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  用户操作                                                       │
│     │                                                           │
│     ▼                                                           │
│  ┌─────────────────┐                                           │
│  │   参数选择      │                                           │
│  │                 │                                           │
│  │ • 曝光 (exposure)│                                          │
│  │ • 色彩 (color)   │                                          │
│  │ • 曲线 (curve)   │                                          │
│  │ • 色调分离(tone) │                                          │
│  │ • HSL           │                                           │
│  │ • 细节 (detail)  │                                          │
│  │ • 校准(calibration)│                                        │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │   参数调整      │                                           │
│  │                 │                                           │
│  │ • 滑块拖动      │                                           │
│  │ • 曲线编辑      │                                           │
│  │ • 颜色选择      │                                           │
│  │ • 数值输入      │                                           │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐    ┌─────────────────┐                   │
│  │ AdjustViewModel │───►│  状态更新       │                   │
│  │                 │    │                 │                   │
│  │ • updateParameter│    │ • @Published    │                   │
│  │ • updateHSL*    │    │ • objectWillChange│                 │
│  │ • updateCurve*  │    │ • UI自动刷新    │                   │
│  │ • updateTone*   │    │                 │                   │
│  └─────────────────┘    └─────────────────┘                   │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │  AdjustService  │                                           │
│  │                 │                                           │
│  │ • 参数验证      │                                           │
│  │ • 数据处理      │                                           │
│  │ • 状态同步      │                                           │
│  │ • 持久化存储    │                                           │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │ FilterService   │                                           │
│  │                 │                                           │
│  │ • updateParameter│                                          │
│  │ • 参数应用      │                                           │
│  │ • 渲染器更新    │                                           │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │  Metal渲染器    │                                           │
│  │                 │                                           │
│  │ • 实时预览      │                                           │
│  │ • 参数应用      │                                           │
│  │ • 图像输出      │                                           │
│  └─────────────────┘                                           │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🎨 滤镜应用模块数据流程图

```
┌─────────────────────────────────────────────────────────────────┐
│                  滤镜应用模块数据流程图                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  用户操作                                                       │
│     │                                                           │
│     ▼                                                           │
│  ┌─────────────────┐                                           │
│  │   滤镜选择      │                                           │
│  │                 │                                           │
│  │ • 宝丽来 (5个)   │                                          │
│  │ • 胶卷 (5个)     │                                          │
│  │ • 复古 (5个)     │                                          │
│  │ • 时尚 (5个)     │                                          │
│  │ • INS (5个)      │                                          │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │   预设应用      │                                           │
│  │                 │                                           │
│  │ • 点击预设按钮   │                                          │
│  │ • 强度调整      │                                           │
│  │ • 取消选择      │                                           │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐    ┌─────────────────┐                   │
│  │ FilterViewModel │───►│  状态更新       │                   │
│  │                 │    │                 │                   │
│  │ • applyPreset   │    │ • selectedPreset│                   │
│  │ • clearPreset   │    │ • hasActiveFilter│                  │
│  │ • updateIntensity│    │ • @Published    │                   │
│  └─────────────────┘    └─────────────────┘                   │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐                                           │
│  │  FilterService  │                                           │
│  │                 │                                           │
│  │ • 预设参数获取   │                                          │
│  │ • LUT文件加载    │                                          │
│  │ • 参数应用      │                                           │
│  │ • 状态管理      │                                           │
│  └─────────────────┘                                           │
│           │                                                     │
│           ▼                                                     │
│  ┌─────────────────┐    ┌─────────────────┐                   │
│  │FilterPresetManager│   │ MetalLUTProcessor│                  │
│  │                 │    │                 │                   │
│  │ • 预设数据管理   │    │ • LUT文件处理    │                   │
│  │ • 参数配置      │    │ • Metal渲染     │                   │
│  └─────────────────┘    └─────────────────┘                   │
│           │                       │                            │
│           └───────────┬───────────┘                            │
│                       ▼                                        │
│              ┌─────────────────┐                               │
│              │  Metal渲染器    │                               │
│              │                 │                               │
│              │ • 滤镜效果应用   │                               │
│              │ • LUT变换       │                               │
│              │ • 实时预览      │                               │
│              │ • 最终输出      │                               │
│              └─────────────────┘                               │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔄 模块间交互流程图

```
┌─────────────────────────────────────────────────────────────────┐
│                    模块间交互流程图                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐                ┌─────────────────┐         │
│  │   调节模块      │                │  滤镜应用模块    │         │
│  │                 │                │                 │         │
│  │ AdjustService   │◄──────────────►│ FilterService   │         │
│  │                 │                │                 │         │
│  │ • 参数更新      │   共享参数模型   │ • 预设应用      │         │
│  │ • 曲线处理      │                │ • LUT处理       │         │
│  │ • HSL处理       │                │ • 状态管理      │         │
│  └─────────────────┘                └─────────────────┘         │
│           │                                   │                 │
│           └─────────────┬─────────────────────┘                 │
│                         ▼                                       │
│                ┌─────────────────┐                              │
│                │ FilterParameters│                              │
│                │   (共享模型)    │                              │
│                │                 │                              │
│                │ • exposure      │                              │
│                │ • contrast      │                              │
│                │ • saturation    │                              │
│                │ • temperature   │                              │
│                │ • curvePoints   │                              │
│                │ • hslParameters │                              │
│                │ • lutPath       │                              │
│                │ • ...更多参数   │                              │
│                └─────────────────┘                              │
│                         │                                       │
│                         ▼                                       │
│                ┌─────────────────┐                              │
│                │   渲染管线      │                              │
│                │                 │                              │
│                │ ┌─────────────┐ │                              │
│                │ │标准渲染器   │ │ ← sRGB图像                   │
│                │ │MetalFilter  │ │                              │
│                │ │Renderer     │ │                              │
│                │ └─────────────┘ │                              │
│                │                 │                              │
│                │ ┌─────────────┐ │                              │
│                │ │高精度渲染器 │ │ ← RAW/线性图像               │
│                │ │HighPrecision│ │                              │
│                │ │MetalRenderer│ │                              │
│                │ └─────────────┘ │                              │
│                │                 │                              │
│                │ ┌─────────────┐ │                              │
│                │ │LUT处理器    │ │ ← LUT文件处理                │
│                │ │MetalLUT     │ │                              │
│                │ │Processor    │ │                              │
│                │ └─────────────┘ │                              │
│                └─────────────────┘                              │
│                         │                                       │
│                         ▼                                       │
│                ┌─────────────────┐                              │
│                │   图像输出      │                              │
│                │                 │                              │
│                │ • 实时预览      │                              │
│                │ • 当前显示      │                              │
│                │ • 最终输出      │                              │
│                └─────────────────┘                              │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## ⚙️ 具体操作流程图

### 调节参数修改流程
```
用户拖动滑块
      ↓
AdjustView.Binding.set
      ↓
AdjustViewModel.updateParameter()
      ↓
参数验证和范围检查
      ↓
AdjustService.updateParameter()
      ↓
FilterService.updateParameter()
      ↓
FilterParameters.参数更新
      ↓
Metal渲染器.updateParameters()
      ↓
实时预览更新
      ↓
用户看到效果反馈
```

### 滤镜预设应用流程
```
用户点击预设按钮
      ↓
FilterView.Button.action
      ↓
FilterViewModel.applyPreset()
      ↓
FilterService.applyPreset()
      ↓
FilterPresetManager.getPreset()
      ↓
FilterParameters.applyPreset()
      ↓
检查LUT文件关联
      ↓
CubeLUTManager.getLUTPath()
      ↓
MetalLUTProcessor.loadLUT()
      ↓
Metal渲染器应用滤镜+LUT
      ↓
实时预览更新
      ↓
用户看到滤镜效果
```

### 曲线编辑流程
```
用户拖拽曲线控制点
      ↓
CurveEditorView.DragGesture
      ↓
AdjustViewModel.updatePointPosition()
      ↓
边界约束和验证
      ↓
AdjustService.updateCurvePoints()
      ↓
CurveProcessor.generateLUT()
      ↓
FilterService.应用曲线LUT
      ↓
Metal渲染器.applyCurveLUT()
      ↓
实时预览更新
      ↓
用户看到曲线调整效果
```

---

## 🎯 数据流关键节点

### 1. 参数统一节点 - FilterParameters
```swift
// 所有调整参数的统一模型
struct FilterParameters {
    // 基础调整参数 (来自调节模块)
    var exposure: Float = 0.0
    var contrast: Float = 0.0
    var saturation: Float = 0.0
    
    // 滤镜参数 (来自滤镜模块)
    var selectedPresetType: FilterPresetType?
    var presetIntensity: Float = 1.0
    
    // LUT参数 (来自滤镜模块)
    var currentLUTPath: String?
    var lutIntensity: Float = 1.0
    
    // 曲线参数 (来自调节模块)
    var curvePoints: [CurveChannel: [CGPoint]] = [:]
    var curveIntensity: Float = 1.0
    
    // HSL参数 (来自调节模块)
    var hue: Float = 0.0
    var hslSaturation: Float = 0.0
    var hslLuminance: Float = 0.0
}
```

### 2. 渲染统一节点 - Metal渲染器
```swift
// 统一的渲染入口
class MetalFilterRenderer {
    func updateParameters(_ parameters: FilterParameters)
    func applyLUT(lutPath: String?, intensity: Float)
    func setRenderingMode(_ mode: RenderingMode)
    var currentOutputImage: UIImage? { get }
}
```

### 3. 状态同步节点 - Service层
```swift
// 状态同步的关键节点
class FilterService {
    @Published var currentParameters = FilterParameters()
    @Published var hasActiveFilter: Bool = false
    
    func updateParameter<T>(_ keyPath: WritableKeyPath<FilterParameters, T>, value: T)
    func applyPreset(type: FilterPresetType, index: Int)
}
```

---

## 📊 性能关键路径

### 实时预览路径 (高频操作)
```
参数修改 → FilterParameters → Metal渲染器 → GPU处理 → 预览更新
   ↑                                                      ↓
防抖优化 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← 60fps限制
```

### LUT处理路径 (中频操作)
```
预设选择 → LUT文件加载 → Metal LUT处理器 → GPU LUT变换 → 效果应用
   ↑                                                        ↓
文件缓存 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← 异步处理
```

### 数据持久化路径 (低频操作)
```
参数修改 → 防抖延迟 → 数据验证 → SwiftData保存 → 磁盘写入
   ↑                                              ↓
批量保存 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←←← 后台队列
```

---

## 🔍 架构问题可视化

### 当前架构问题
```
❌ 单例依赖问题
AdjustViewModel → AdjustService.shared
FilterViewModel → FilterService.shared
                      ↓
               全局状态污染

❌ 职责混乱问题  
AdjustService (1221行+)
├── 数据持久化
├── 曲线管理
├── HSL管理  
├── 色调分离管理
├── 参数验证
├── 状态同步
└── 性能监控

❌ 状态分散问题
AdjustViewModel (@Published)
       ↕
AdjustService (@Published)
       ↕
FilterService (@Published)
```

### 目标架构设计
```
✅ 依赖注入架构
AdjustViewModel → AdjustServiceProtocol (Actor)
FilterViewModel → FilterServiceProtocol (Actor)
                      ↓
               清晰的依赖关系

✅ 职责分离架构
AdjustService (Actor) → 专注参数处理
CurveService (Actor) → 专注曲线计算
HSLService (Actor) → 专注HSL处理
FilterService (Actor) → 专注滤镜应用

✅ 统一状态管理
ViewModel层 (@Published) ← 唯一状态源
       ↓
Service层 (Actor) ← 业务逻辑处理
       ↓
Model层 ← 数据持久化
```

---

## 📋 总结

这个数据流程架构图清晰地展示了：

1. **模块关系**: 调节模块和滤镜应用模块通过FilterParameters紧密协作
2. **数据流向**: 从用户操作到最终渲染的完整数据流程
3. **关键节点**: FilterParameters、Metal渲染器、Service层是关键的数据汇聚点
4. **性能路径**: 实时预览、LUT处理、数据持久化的不同性能要求
5. **架构问题**: 当前单例依赖、职责混乱、状态分散的具体表现
6. **重构目标**: 依赖注入、职责分离、统一状态管理的清晰架构

这为后续的MVVM-S重构提供了详细的技术蓝图和实施指导。

---

*图表生成时间: 2025年1月*  
*版权所有: LoniceraLab*