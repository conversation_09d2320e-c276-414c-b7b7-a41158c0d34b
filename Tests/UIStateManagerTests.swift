import XCTest
@testable import Lomo

final class UIStateManagerTests: XCTestCase {
    
    var uiStateManager: UIStateManager!
    var mockAnimationViewModel: CameraAnimationViewModel!
    
    override func setUp() {
        super.setUp()
        let state = CameraState()
        mockAnimationViewModel = CameraAnimationViewModel(state: state)
        uiStateManager = UIStateManager(animationViewModel: mockAnimationViewModel)
    }
    
    override func tearDown() {
        uiStateManager = nil
        mockAnimationViewModel = nil
        super.tearDown()
    }
    
    func testInitialState() {
        // 验证初始状态
        XCTAssertFalse(uiStateManager.isRotating)
        XCTAssertEqual(uiStateManager.rotationAngle, 0)
        XCTAssertFalse(uiStateManager.isFilterPressed)
        XCTAssertFalse(uiStateManager.isEditPressed)
        XCTAssertFalse(uiStateManager.isLockPressed)
    }
    
    func testUpdateButtonPressState() {
        // 测试更新按钮状态
        uiStateManager.updateButtonPressState(button: .filter, isPressed: true)
        XCTAssertTrue(uiStateManager.isFilterPressed)
        XCTAssertFalse(uiStateManager.isEditPressed)
        XCTAssertFalse(uiStateManager.isLockPressed)
        
        uiStateManager.updateButtonPressState(button: .edit, isPressed: true)
        XCTAssertTrue(uiStateManager.isFilterPressed)
        XCTAssertTrue(uiStateManager.isEditPressed)
        XCTAssertFalse(uiStateManager.isLockPressed)
        
        uiStateManager.updateButtonPressState(button: .lock, isPressed: true)
        XCTAssertTrue(uiStateManager.isFilterPressed)
        XCTAssertTrue(uiStateManager.isEditPressed)
        XCTAssertTrue(uiStateManager.isLockPressed)
        
        // 测试将状态设为false
        uiStateManager.updateButtonPressState(button: .filter, isPressed: false)
        XCTAssertFalse(uiStateManager.isFilterPressed)
        XCTAssertTrue(uiStateManager.isEditPressed)
        XCTAssertTrue(uiStateManager.isLockPressed)
    }
    
    func testAnimateCameraSwitch() {
        // 测试相机切换动画
        let expectation = XCTestExpectation(description: "相机切换动画完成")
        
        // 验证初始状态
        XCTAssertFalse(uiStateManager.isRotating)
        XCTAssertEqual(uiStateManager.rotationAngle, 0)
        
        // 执行动画
        uiStateManager.animateCameraSwitch {
            expectation.fulfill()
        }
        
        // 验证动画开始状态
        XCTAssertTrue(uiStateManager.isRotating)
        XCTAssertEqual(uiStateManager.rotationAngle, 180)
        
        // 等待动画完成
        wait(for: [expectation], timeout: 1.0)
        
        // 验证动画完成后的状态
        XCTAssertFalse(uiStateManager.isRotating)
        XCTAssertEqual(uiStateManager.rotationAngle, 180)
    }
    
    func testExecuteWithAnimation() {
        // 测试执行带动画的操作
        var testValue = 0
        
        uiStateManager.executeWithAnimation {
            testValue = 100
        }
        
        // 验证操作已执行
        XCTAssertEqual(testValue, 100)
    }
    
    func testGetAnimations() {
        // 测试获取动画对象
        let standardAnimation = uiStateManager.getSpringAnimation()
        let quickAnimation = uiStateManager.getQuickSpringAnimation()
        
        // 验证动画对象不为nil
        XCTAssertNotNil(standardAnimation)
        XCTAssertNotNil(quickAnimation)
    }
} 