# HarmonyOS Sans SC 字体配置说明

我已经在项目中添加了对华为鸿蒙字体(HarmonyOS Sans SC)的支持，该字体具有六种不同的粗细变体：

- HarmonyOS_Sans_SC_Thin
- HarmonyOS_Sans_SC_Light
- HarmonyOS_Sans_SC_Regular
- HarmonyOS_Sans_SC_Medium
- HarmonyOS_Sans_SC_Bold
- HarmonyOS_Sans_SC_Black

## 代码已完成的修改

1. 已修改`WatermarkControlView.swift`，在字体选项中添加了"HarmonyOS_Sans_SC"选项
2. 根据字体粗细动态调整显示的粗细选项，为HarmonyOS_Sans_SC显示六种粗细选项
3. 修改`WatermarkStyles.swift`中的`applyFontStyle`方法，支持HarmonyOS_Sans_SC字体及其六种粗细
4. 修改`CustomWatermarkStyle10.swift`中的`getFont`方法，支持HarmonyOS_Sans_SC字体

## 如何在项目中配置字体

为了让字体在应用中正常工作，您需要完成以下步骤：

### 1. 确保字体文件已添加到项目中

项目目录结构显示字体文件已存在于以下位置：
```
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Medium.dataset/HarmonyOS_Sans_SC_Medium.ttf
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Black.dataset/HarmonyOS_Sans_SC_Black.ttf
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Bold.dataset/HarmonyOS_Sans_SC_Bold.ttf
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Thin.dataset/HarmonyOS_Sans_SC_Thin.ttf
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Regular.dataset/HarmonyOS_Sans_SC_Regular.ttf
./Lomo/Assets.xcassets/HarmonyOS_Sans_SC_Light.dataset/HarmonyOS_Sans_SC_Light.ttf
```

### 2. 修改Info.plist文件

在Xcode中，打开项目的Info.plist文件，添加以下配置：

1. 添加`Fonts provided by application`键（如果尚不存在）
   - 键名：`UIAppFonts`
   - 类型：Array

2. 在该数组中添加以下字体文件名：
   - `HarmonyOS_Sans_SC_Thin.ttf`
   - `HarmonyOS_Sans_SC_Light.ttf`
   - `HarmonyOS_Sans_SC_Regular.ttf`
   - `HarmonyOS_Sans_SC_Medium.ttf`
   - `HarmonyOS_Sans_SC_Bold.ttf`
   - `HarmonyOS_Sans_SC_Black.ttf`

### 3. 将字体文件移至正确位置

Asset Catalog (.xcassets)中的字体无法被iOS正常加载。请确保将TTF文件添加到项目的资源目录中：

1. 在Xcode中，右键点击项目导航器中的"Lomo"组
2. 选择"Add Files to 'Lomo'..."
3. 选择所有HarmonyOS_Sans_SC字体文件
4. 勾选"Copy items if needed"选项
5. 点击"Add"按钮

### 4. 检查字体是否正确加载

为确保字体已正确加载，可以在应用启动时添加以下代码，打印所有可用字体名称：

```swift
// 在AppDelegate.swift或适当的启动代码中添加
for family in UIFont.familyNames.sorted() {
    print("Family: \(family)")
    for name in UIFont.fontNames(forFamilyName: family).sorted() {
        print("   Font: \(name)")
    }
}
```

## 注意事项

- 字体文件的路径必须与Info.plist中指定的名称完全匹配
- 如果使用自定义字体时出现问题，请检查控制台输出中的警告消息
- 某些设备或iOS版本可能需要重启应用才能正确加载新字体 