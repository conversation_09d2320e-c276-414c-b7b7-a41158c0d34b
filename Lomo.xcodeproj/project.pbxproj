// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		EB1C1D3F2D603FA3006C8FE4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EB1C1D252D603FA2006C8FE4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EB1C1D2C2D603FA2006C8FE4;
			remoteInfo = Lomo;
		};
		EB1C1D492D603FA3006C8FE4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = EB1C1D252D603FA2006C8FE4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = EB1C1D2C2D603FA2006C8FE4;
			remoteInfo = Lomo;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		EB1C1D2D2D603FA2006C8FE4 /* Lomo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Lomo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		EB1C1D3E2D603FA3006C8FE4 /* LomoTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LomoTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		EB1C1D482D603FA3006C8FE4 /* LomoUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = LomoUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		EB1C1D2F2D603FA2006C8FE4 /* Lomo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = Lomo;
			sourceTree = "<group>";
		};
		EB1C1D412D603FA3006C8FE4 /* LomoTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LomoTests;
			sourceTree = "<group>";
		};
		EB1C1D4B2D603FA3006C8FE4 /* LomoUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = LomoUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		EB1C1D2A2D603FA2006C8FE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D3B2D603FA3006C8FE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D452D603FA3006C8FE4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		EB1C1D242D603FA2006C8FE4 = {
			isa = PBXGroup;
			children = (
				EB1C1D2F2D603FA2006C8FE4 /* Lomo */,
				EB1C1D412D603FA3006C8FE4 /* LomoTests */,
				EB1C1D4B2D603FA3006C8FE4 /* LomoUITests */,
				EB1C1D2E2D603FA2006C8FE4 /* Products */,
			);
			sourceTree = "<group>";
		};
		EB1C1D2E2D603FA2006C8FE4 /* Products */ = {
			isa = PBXGroup;
			children = (
				EB1C1D2D2D603FA2006C8FE4 /* Lomo.app */,
				EB1C1D3E2D603FA3006C8FE4 /* LomoTests.xctest */,
				EB1C1D482D603FA3006C8FE4 /* LomoUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		EB1C1D2C2D603FA2006C8FE4 /* Lomo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EB1C1D522D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "Lomo" */;
			buildPhases = (
				EB1C1D292D603FA2006C8FE4 /* Sources */,
				EB1C1D2A2D603FA2006C8FE4 /* Frameworks */,
				EB1C1D2B2D603FA2006C8FE4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				EB1C1D2F2D603FA2006C8FE4 /* Lomo */,
			);
			name = Lomo;
			packageProductDependencies = (
			);
			productName = Lomo;
			productReference = EB1C1D2D2D603FA2006C8FE4 /* Lomo.app */;
			productType = "com.apple.product-type.application";
		};
		EB1C1D3D2D603FA3006C8FE4 /* LomoTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EB1C1D552D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "LomoTests" */;
			buildPhases = (
				EB1C1D3A2D603FA3006C8FE4 /* Sources */,
				EB1C1D3B2D603FA3006C8FE4 /* Frameworks */,
				EB1C1D3C2D603FA3006C8FE4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EB1C1D402D603FA3006C8FE4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EB1C1D412D603FA3006C8FE4 /* LomoTests */,
			);
			name = LomoTests;
			packageProductDependencies = (
			);
			productName = LomoTests;
			productReference = EB1C1D3E2D603FA3006C8FE4 /* LomoTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		EB1C1D472D603FA3006C8FE4 /* LomoUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = EB1C1D582D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "LomoUITests" */;
			buildPhases = (
				EB1C1D442D603FA3006C8FE4 /* Sources */,
				EB1C1D452D603FA3006C8FE4 /* Frameworks */,
				EB1C1D462D603FA3006C8FE4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				EB1C1D4A2D603FA3006C8FE4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				EB1C1D4B2D603FA3006C8FE4 /* LomoUITests */,
			);
			name = LomoUITests;
			packageProductDependencies = (
			);
			productName = LomoUITests;
			productReference = EB1C1D482D603FA3006C8FE4 /* LomoUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		EB1C1D252D603FA2006C8FE4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					EB1C1D2C2D603FA2006C8FE4 = {
						CreatedOnToolsVersion = 16.2;
					};
					EB1C1D3D2D603FA3006C8FE4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = EB1C1D2C2D603FA2006C8FE4;
					};
					EB1C1D472D603FA3006C8FE4 = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = EB1C1D2C2D603FA2006C8FE4;
					};
				};
			};
			buildConfigurationList = EB1C1D282D603FA2006C8FE4 /* Build configuration list for PBXProject "Lomo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = EB1C1D242D603FA2006C8FE4;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = EB1C1D2E2D603FA2006C8FE4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				EB1C1D2C2D603FA2006C8FE4 /* Lomo */,
				EB1C1D3D2D603FA3006C8FE4 /* LomoTests */,
				EB1C1D472D603FA3006C8FE4 /* LomoUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		EB1C1D2B2D603FA2006C8FE4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D3C2D603FA3006C8FE4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D462D603FA3006C8FE4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		EB1C1D292D603FA2006C8FE4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D3A2D603FA3006C8FE4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		EB1C1D442D603FA3006C8FE4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		EB1C1D402D603FA3006C8FE4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EB1C1D2C2D603FA2006C8FE4 /* Lomo */;
			targetProxy = EB1C1D3F2D603FA3006C8FE4 /* PBXContainerItemProxy */;
		};
		EB1C1D4A2D603FA3006C8FE4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = EB1C1D2C2D603FA2006C8FE4 /* Lomo */;
			targetProxy = EB1C1D492D603FA3006C8FE4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		EB1C1D502D603FA3006C8FE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 8RXMULF2A2;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		EB1C1D512D603FA3006C8FE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 8RXMULF2A2;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		EB1C1D532D603FA3006C8FE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Lomo/Lomo.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Lomo/Preview Content\"";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问相机来进行拍照";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册来保存照片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册来保存和查看照片";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.Lomo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		EB1C1D542D603FA3006C8FE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Lomo/Lomo.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "\"Lomo/Preview Content\"";
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSCameraUsageDescription = "需要访问相机来进行拍照";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "需要访问相册来保存照片";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问相册来保存和查看照片";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.Lomo;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		EB1C1D562D603FA3006C8FE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.LomoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lomo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lomo";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		EB1C1D572D603FA3006C8FE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.LomoTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Lomo.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Lomo";
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
		EB1C1D592D603FA3006C8FE4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.LomoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Lomo;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Debug;
		};
		EB1C1D5A2D603FA3006C8FE4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEAD_CODE_STRIPPING = YES;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MACOSX_DEPLOYMENT_TARGET = 15.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.LoniceraLab.LomoUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = Lomo;
				XROS_DEPLOYMENT_TARGET = 2.2;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		EB1C1D282D603FA2006C8FE4 /* Build configuration list for PBXProject "Lomo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EB1C1D502D603FA3006C8FE4 /* Debug */,
				EB1C1D512D603FA3006C8FE4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EB1C1D522D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "Lomo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EB1C1D532D603FA3006C8FE4 /* Debug */,
				EB1C1D542D603FA3006C8FE4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EB1C1D552D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "LomoTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EB1C1D562D603FA3006C8FE4 /* Debug */,
				EB1C1D572D603FA3006C8FE4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		EB1C1D582D603FA3006C8FE4 /* Build configuration list for PBXNativeTarget "LomoUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				EB1C1D592D603FA3006C8FE4 /* Debug */,
				EB1C1D5A2D603FA3006C8FE4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = EB1C1D252D603FA2006C8FE4 /* Project object */;
}
