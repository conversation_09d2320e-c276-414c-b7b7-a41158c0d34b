# 焦距选择器手势实现说明

## 需求背景

在相机应用中，焦距选择器需要同时支持两种不同的用户交互：

1. **点击操作**：切换到特定焦距的镜头（如0.5x、1x、3x等）
2. **长按操作**：显示变焦刻度盘，允许用户进行连续、精确的变焦调整

这两种操作需要在同一个组件上共存，且不能相互干扰。

## 实现挑战

SwiftUI中，同时处理点击和长按手势存在一些固有的挑战：

1. 手势识别的优先级问题
2. 手势之间的潜在冲突
3. 确保正确的用户体验（避免长按时也触发点击操作）

## 实现方案

### 组件结构

`LensButton`组件是焦距选择器的核心，它接收两个关键回调函数：

- `action`：处理点击事件，通常调用`viewModel.selectLensAtIndex(index)`
- `longPressAction`：处理长按事件，通常调用`viewModel.showZoomDial()`

### 代码实现

```swift
struct LensButton: View {
    let configuration: LensConfiguration
    let action: () -> Void
    let longPressAction: () -> Void
    let screenHeight: CGFloat
    let currentZoomFactor: CGFloat
    @ObservedObject var viewModel: CameraViewModel
    let deviceOrientation: UIDeviceOrientation
    private let screenMetrics = ScreenMetricsService.shared
    
    // ... 其他属性和初始化方法 ...
    
    var body: some View {
        return Button(action: {
            // 按钮点击直接调用action
            action()
        }) {
            HStack(alignment: .lastTextBaseline, spacing: UIConstants.zoomDisplaySpacing) {
                Text(formattedValue)
                    .font(.system(size: screenHeight * UIConstants.zoomDisplayFontSize, weight: UIConstants.zoomDisplayWeight))
                
                // ... UI内容 ...
            }
            // ... UI修饰符 ...
        }
        // 长按手势使用更高优先级的gesture处理
        .highPriorityGesture(
            LongPressGesture(minimumDuration: 0.25)
                .onEnded { _ in
                    longPressAction()
                }
        )
        .lensButtonStyle(screenHeight: screenHeight, baseSize: UIConstants.lensButtonBaseSize)
        // ... 其他修饰符 ...
    }
}
```

### 关键技术点

1. **使用Button的action处理点击事件**：
   - 将Button的`action`参数直接设置为需要执行的点击操作
   - 这确保了用户点击时能正确切换镜头焦距

2. **使用高优先级手势处理长按事件**：
   - 通过`.highPriorityGesture`修饰符添加长按手势
   - 设置`LongPressGesture`的`minimumDuration`为0.25秒，确保与普通点击区分
   - 使用`onEnded`回调确保手势完成时才触发长按操作

3. **避免同时使用多个`.simultaneousGesture`**：
   - 之前尝试使用两个`.simultaneousGesture`修饰符分别处理点击和长按
   - 但在SwiftUI中，后一个`.simultaneousGesture`会覆盖前一个，导致点击操作失效

## 错误尝试与修正

### 错误方案1：两个simultaneousGesture

```swift
Button(action: {}) {
    // ... 按钮内容 ...
}
.simultaneousGesture(
    TapGesture().onEnded { _ in
        action()
    }
)
.simultaneousGesture(
    LongPressGesture(minimumDuration: 0.25)
        .sequenced(before: DragGesture(minimumDistance: 0))
        .onChanged { _ in
            longPressAction()
        }
)
```

**问题**：后一个`.simultaneousGesture`会覆盖前一个，导致只有长按被识别。

### 错误方案2：使用gesture替代simultaneousGesture

```swift
Button(action: {}) {
    // ... 按钮内容 ...
}
.gesture(
    LongPressGesture(minimumDuration: 0.5)
        .onEnded { _ in
            longPressAction()
        }
)
```

**问题**：`.gesture`会完全接管手势处理，导致Button的点击事件被屏蔽。

### 正确方案：Button.action + highPriorityGesture

```swift
Button(action: {
    // 按钮点击直接调用action
    action()
}) {
    // ... 按钮内容 ...
}
.highPriorityGesture(
    LongPressGesture(minimumDuration: 0.25)
        .onEnded { _ in
            longPressAction()
        }
)
```

**优点**：
- 清晰区分点击和长按行为
- 不会互相干扰
- 符合用户预期的交互体验

## 与其他刻度盘的区别

值得注意的是，光圈、快门等其他刻度盘按钮采用了不同的实现方式：

```swift
Button(action: {
    viewModel.showApertureDialWithTimer()
}) {
    // ... 按钮内容 ...
}
```

这是因为这些按钮只需要一种交互行为（点击显示刻度盘），不需要区分点击和长按，实现更为简单。

## 总结

焦距选择器的手势实现采用了Button.action + highPriorityGesture的组合方式，成功解决了需要在同一组件上处理两种不同手势的挑战。这种实现方式确保了良好的用户体验，使用户能够通过点击快速切换镜头，也能通过长按进行精细的变焦调整。 