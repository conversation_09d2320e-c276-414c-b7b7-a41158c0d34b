import CoreImage
import Foundation

// 获取所有Core Image滤镜的脚本
class CoreImageFiltersList {
    
    static func getAllFilters() {
        print("=== Core Image 完整滤镜列表 ===\n")
        
        // 获取所有分类
        let categories = [
            kCICategoryDistortionEffect,
            kCICategoryGeometryAdjustment,
            kCICategoryCompositeOperation,
            kCICategoryHalftoneEffect,
            kCICategoryColorAdjustment,
            kCICategoryColorEffect,
            kCICategoryTransition,
            kCICategoryTileEffect,
            kCICategoryGenerator,
            kCICategoryReduction,
            kCICategoryGradient,
            kCICategoryStylize,
            kCICategorySharpen,
            kCICategoryBlur,
            kCICategoryVideo,
            kCICategoryStillImage,
            kCICategoryInterlaced,
            kCICategoryNonSquarePixels,
            kCICategoryHighDynamicRange,
            kCICategoryBuiltIn
        ]
        
        var allFilters: Set<String> = []
        
        for category in categories {
            let filtersInCategory = CIFilter.filterNames(inCategory: category)
            print("📂 \(category)")
            print("   滤镜数量: \(filtersInCategory.count)")
            
            for filterName in filtersInCategory.sorted() {
                allFilters.insert(filterName)
                print("   • \(filterName)")
            }
            print("")
        }
        
        print("🎯 总计滤镜数量: \(allFilters.count)")
        print("\n=== 按字母顺序排列的完整列表 ===")
        
        for (index, filterName) in allFilters.sorted().enumerated() {
            print("\(index + 1). \(filterName)")
        }
    }
    
    static func getColorAdjustmentFilters() {
        print("\n=== 色彩调整相关滤镜 ===")
        let colorFilters = CIFilter.filterNames(inCategory: kCICategoryColorAdjustment)
        
        for filterName in colorFilters.sorted() {
            if let filter = CIFilter(name: filterName) {
                print("\n🎨 \(filterName)")
                print("   输入参数:")
                for key in filter.inputKeys {
                    print("     • \(key)")
                }
            }
        }
    }
    
    static func getImageProcessingFilters() {
        print("\n=== 图像处理相关滤镜 ===")
        
        let relevantCategories = [
            kCICategoryColorAdjustment,
            kCICategorySharpen,
            kCICategoryBlur,
            kCICategoryStylize
        ]
        
        for category in relevantCategories {
            let filters = CIFilter.filterNames(inCategory: category)
            print("\n📂 \(category) (\(filters.count)个滤镜)")
            
            for filterName in filters.sorted() {
                print("   • \(filterName)")
            }
        }
    }
}

// 运行脚本
CoreImageFiltersList.getAllFilters()
CoreImageFiltersList.getColorAdjustmentFilters()
CoreImageFiltersList.getImageProcessingFilters()
