# 📜 LoniceraLab 版权格式验证指南

<!-- Copyright (c) 2025 LoniceraLab. All rights reserved. -->

## 🎯 标准版权格式（必须严格遵循）

### 唯一正确格式
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.
```

## 🔍 格式验证清单

### 1. 字符级别检查
- [ ] 第1-2字符: `//` (双斜杠)
- [ ] 第3字符: ` ` (单个空格)
- [ ] 第4-12字符: `Copyright` (C大写，其余小写)
- [ ] 第13字符: ` ` (单个空格)
- [ ] 第14-16字符: `(c)` (小括号包围小写c)
- [ ] 第17字符: ` ` (单个空格)
- [ ] 第18-21字符: `2025` (年份)
- [ ] 第22字符: ` ` (单个空格)
- [ ] 第23-33字符: `LoniceraLab` (L和L大写，无空格)
- [ ] 第34字符: `.` (句号)
- [ ] 第35字符: ` ` (单个空格)
- [ ] 第36-38字符: `All` (A大写，ll小写)
- [ ] 第39字符: ` ` (单个空格)
- [ ] 第40-45字符: `rights` (全小写)
- [ ] 第46字符: ` ` (单个空格)
- [ ] 第47-55字符: `reserved` (全小写)
- [ ] 第56字符: `.` (句号)

### 2. 总长度检查
- [ ] 总字符数: 56个字符
- [ ] 无多余空格或换行符

## ❌ 常见错误格式

### 错误1: 多行注释
```swift
/*
 * Copyright (c) 2025 LoniceraLab. All rights reserved.
 */
```
**问题**: 使用了多行注释，应该使用单行双斜杠注释

### 错误2: 缺少 (c)
```swift
// Copyright 2025 LoniceraLab. All rights reserved.
```
**问题**: 缺少 `(c)` 标记

### 错误3: 公司名格式错误
```swift
// Copyright (c) 2025 Lonicera Lab. All rights reserved.
```
**问题**: 公司名应该是 `LoniceraLab`，不是 `Lonicera Lab`

### 错误4: 大小写错误
```swift
// copyright (c) 2025 loniceralab. all rights reserved.
```
**问题**: 多处大小写错误

### 错误5: 缺少句号
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved
```
**问题**: 结尾缺少句号

### 错误6: 年份错误
```swift
// Copyright (c) 2024 LoniceraLab. All rights reserved.
```
**问题**: 年份应该是 2025

### 错误7: 多余空格
```swift
//  Copyright  (c)  2025  LoniceraLab.  All  rights  reserved.
```
**问题**: 有多余的空格

## ✅ 正确使用示例

### Swift View 文件
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI

struct HomeView: View {
    var body: some View {
        Text("LoniceraLab iOS App")
    }
}
```

### Swift ViewModel 文件
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation
import SwiftUI

@MainActor
class HomeViewModel: ObservableObject {
    @Published var title = "LoniceraLab"
}
```

### Swift Service 文件
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

actor NetworkService {
    func fetchData() async throws -> Data {
        // 实现代码
        return Data()
    }
}
```

### Swift Model 文件
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import Foundation

struct User: Codable {
    let id: String
    let name: String
}
```

## 🔧 自动验证脚本

### 验证单个文件
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 检查文件是否包含正确的版权声明

file="$1"
expected="// Copyright (c) 2025 LoniceraLab. All rights reserved."

if [ -f "$file" ]; then
    first_line=$(head -n 1 "$file")
    if [ "$first_line" = "$expected" ]; then
        echo "✅ $file: 版权格式正确"
    else
        echo "❌ $file: 版权格式错误"
        echo "期望: $expected"
        echo "实际: $first_line"
    fi
else
    echo "❌ 文件不存在: $file"
fi
```

### 批量验证项目文件
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 批量检查所有 Swift 文件的版权声明

find . -name "*.swift" -type f | while read file; do
    ./check_copyright.sh "$file"
done
```

### 自动添加版权声明脚本
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 自动为 Swift 文件添加版权声明

copyright_line="// Copyright (c) 2025 LoniceraLab. All rights reserved."

for file in $(find . -name "*.swift" -type f); do
    first_line=$(head -n 1 "$file")
    if [ "$first_line" != "$copyright_line" ]; then
        echo "添加版权声明到: $file"
        # 创建临时文件
        temp_file=$(mktemp)
        echo "$copyright_line" > "$temp_file"
        echo "" >> "$temp_file"
        cat "$file" >> "$temp_file"
        mv "$temp_file" "$file"
    fi
done
```

### 版权格式修复脚本
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 修复错误的版权格式

correct_copyright="// Copyright (c) 2025 LoniceraLab. All rights reserved."

for file in $(find . -name "*.swift" -type f); do
    first_line=$(head -n 1 "$file")
    
    # 检查是否包含版权信息但格式错误
    if [[ "$first_line" == *"Copyright"* ]] && [[ "$first_line" != "$correct_copyright" ]]; then
        echo "修复版权格式: $file"
        # 替换第一行
        tail -n +2 "$file" > temp_file
        echo "$correct_copyright" > "$file"
        echo "" >> "$file"
        cat temp_file >> "$file"
        rm temp_file
    fi
done
```

## 📝 版权添加模板

### 新建 Swift 文件模板
```swift
// Copyright (c) 2025 LoniceraLab. All rights reserved.

import SwiftUI
import Foundation

// 在这里添加你的代码
```

### Xcode 文件模板设置
1. 打开 Xcode
2. 进入 Preferences > Text Editing > Code Completion
3. 设置文件头模板为：
```
// Copyright (c) 2025 LoniceraLab. All rights reserved.
```

### VS Code 代码片段设置
```json
{
    "LoniceraLab Copyright": {
        "prefix": "copyright",
        "body": [
            "// Copyright (c) 2025 LoniceraLab. All rights reserved.",
            "",
            "$0"
        ],
        "description": "LoniceraLab 版权声明"
    }
}
```

## 🚨 强制执行规则

### 代码审查检查点
1. **创建新文件时**: 必须包含正确的版权声明
2. **修改现有文件时**: 确保版权声明格式正确
3. **合并代码时**: 检查所有文件的版权声明
4. **发布前检查**: 运行批量验证脚本

### 违规处理
- **轻微格式错误**: 立即修正，重新提交
- **缺少版权声明**: 添加正确格式，重新审查
- **重复违规**: 需要额外的格式培训

### Git Hook 集成
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# pre-commit hook 检查版权声明

echo "检查版权声明..."

copyright_line="// Copyright (c) 2025 LoniceraLab. All rights reserved."
error_found=false

# 检查所有暂存的 Swift 文件
for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.swift$'); do
    if [ -f "$file" ]; then
        first_line=$(head -n 1 "$file")
        if [ "$first_line" != "$copyright_line" ]; then
            echo "❌ $file: 缺少或格式错误的版权声明"
            error_found=true
        fi
    fi
done

if [ "$error_found" = true ]; then
    echo "请修复版权声明后重新提交"
    exit 1
fi

echo "✅ 版权声明检查通过"
exit 0
```

## 📊 质量保证

### 版权合规评分
- **100分**: 所有文件都有正确的版权声明
- **90-99分**: 少数文件有轻微格式问题
- **80-89分**: 部分文件缺少版权声明
- **<80分**: 大量文件不符合标准，需要整改

### 目标标准
- **LoniceraLab 项目要求**: 100分合规率
- **检查频率**: 每次代码提交
- **修复时限**: 发现问题后24小时内修复

### 合规性报告生成
```bash
#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 生成版权合规性报告

echo "LoniceraLab 版权合规性报告"
echo "生成时间: $(date)"
echo "=========================="

total_files=0
compliant_files=0
non_compliant_files=0

copyright_line="// Copyright (c) 2025 LoniceraLab. All rights reserved."

for file in $(find . -name "*.swift" -type f); do
    total_files=$((total_files + 1))
    first_line=$(head -n 1 "$file")
    
    if [ "$first_line" = "$copyright_line" ]; then
        compliant_files=$((compliant_files + 1))
    else
        non_compliant_files=$((non_compliant_files + 1))
        echo "❌ $file"
    fi
done

compliance_rate=$((compliant_files * 100 / total_files))

echo "=========================="
echo "总文件数: $total_files"
echo "合规文件: $compliant_files"
echo "不合规文件: $non_compliant_files"
echo "合规率: $compliance_rate%"

if [ $compliance_rate -eq 100 ]; then
    echo "🎉 恭喜！达到 100% 合规率"
elif [ $compliance_rate -ge 90 ]; then
    echo "⚠️  接近目标，需要修复 $non_compliant_files 个文件"
else
    echo "🚨 需要立即整改，合规率过低"
fi
```

## 🛠️ 开发工具集成

### Xcode Build Phase 脚本
```bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.
# 在 Xcode Build Phases 中添加此脚本

echo "检查版权声明合规性..."

copyright_line="// Copyright (c) 2025 LoniceraLab. All rights reserved."
error_count=0

for file in $(find "${SRCROOT}" -name "*.swift" -type f); do
    first_line=$(head -n 1 "$file")
    if [ "$first_line" != "$copyright_line" ]; then
        echo "warning: $file 缺少正确的版权声明"
        error_count=$((error_count + 1))
    fi
done

if [ $error_count -gt 0 ]; then
    echo "发现 $error_count 个文件缺少正确的版权声明"
fi
```

### SwiftLint 规则配置
```yaml
# .swiftlint.yml
# Copyright (c) 2025 LoniceraLab. All rights reserved.

custom_rules:
  copyright_header:
    name: "版权声明"
    regex: '^(?!// Copyright \(c\) 2025 LoniceraLab\. All rights reserved\.)'
    message: "文件必须以 LoniceraLab 版权声明开头"
    severity: error
```

## 📈 持续改进

### 版权声明演进历史
- **v1.0**: 基础版权格式定义
- **v1.1**: 添加字符级验证
- **v1.2**: 集成自动化工具
- **v1.3**: 完善错误处理和修复

### 未来改进计划
- [ ] 集成 AI 自动检测和修复
- [ ] 支持多种文件格式的版权声明
- [ ] 开发 Xcode 插件自动添加版权
- [ ] 建立版权合规性仪表板

## 🔗 相关资源

### 法律参考
- [软件版权保护法律指南](https://example.com/copyright-law)
- [开源许可证对比](https://example.com/license-comparison)

### 工具推荐
- **版权检查工具**: copyright-checker
- **自动化脚本**: copyright-automation
- **IDE 插件**: copyright-header-plugin

---

**记住**: 版权声明是 LoniceraLab 品牌保护的重要组成部分，必须严格执行，不允许任何偏差。每一个 Swift 文件都是 LoniceraLab 知识产权的体现，正确的版权声明是我们专业性和法律合规性的标志。