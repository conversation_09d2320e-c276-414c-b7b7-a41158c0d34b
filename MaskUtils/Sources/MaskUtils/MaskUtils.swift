import SwiftUI

/// 遮罩工具类，提供创建各种遮罩效果的方法
public struct MaskUtils {
    
    /// 遮罩配置结构体
    public struct MaskConfiguration {
        public let width: CGFloat  // 遮罩底部宽度（屏幕宽度的百分比）
        public let height: CGFloat  // 遮罩高度（屏幕高度的百分比）
        public let offset: CGFloat  // 遮罩顶部偏移（屏幕高度的百分比）
        public let curveIntensity: CGFloat  // 曲线强度（0-1之间，控制弧线的弯曲程度）
        
        public init(width: CGFloat, height: CGFloat, offset: CGFloat, curveIntensity: CGFloat) {
            self.width = width
            self.height = height
            self.offset = offset
            self.curveIntensity = curveIntensity
        }
        
        /// 标准配置
        public static let standard = MaskConfiguration(
            width: 0.05,    // 5%屏幕宽度
            height: 0.03,   // 3%屏幕高度
            offset: 0.04,   // 4%屏幕高度
            curveIntensity: 0.75    // 75%弯曲强度
        )
        
        /// 刻度尺配置（无偏移）
        public static let scaleRuler = MaskConfiguration(
            width: 0.05,    // 5%屏幕宽度
            height: 0.03,   // 3%屏幕高度
            offset: 0.0,    // 无偏移
            curveIntensity: 0.75    // 75%弯曲强度
        )
    }
    
    /// 为圆形刻度盘创建遮罩路径
    /// - Parameters:
    ///   - dialWidth: 刻度盘宽度
    ///   - dialHeight: 刻度盘高度
    ///   - config: 遮罩配置
    /// - Returns: 遮罩路径
    public static func createDialMaskPath(dialWidth: CGFloat, dialHeight: CGFloat, config: MaskConfiguration) -> Path {
        Path { path in
            let width: CGFloat = UIScreen.main.bounds.width * config.width
            let height: CGFloat = UIScreen.main.bounds.height * config.height
            let offset: CGFloat = UIScreen.main.bounds.height * config.offset

            // 计算顶点位置
            let radian = 90 * Double.pi / 180
            let x = dialWidth/2 + cos(radian) * (dialHeight - offset)
            let y = dialHeight - sin(radian) * (dialHeight - offset)

            let topPoint = CGPoint(x: x, y: y)
            let leftPoint = CGPoint(x: x - width/2, y: y - height)
            let rightPoint = CGPoint(x: x + width/2, y: y - height)

            // 计算控制点 - 根据曲线强度调整
            let controlY = y - height * (1 - config.curveIntensity)
            let controlXOffset = width/8
            let leftControlPoint = CGPoint(x: x - controlXOffset, y: controlY)
            let rightControlPoint = CGPoint(x: x + controlXOffset, y: controlY)

            path.move(to: topPoint)
            path.addQuadCurve(to: leftPoint, control: leftControlPoint)
            path.addLine(to: rightPoint)
            path.addQuadCurve(to: topPoint, control: rightControlPoint)
            path.closeSubpath()
        }
    }
    
    /// 为直线刻度尺创建遮罩路径
    /// - Parameters:
    ///   - centerX: 中心X坐标
    ///   - config: 遮罩配置
    /// - Returns: 遮罩路径
    public static func createScaleRulerMaskPath(centerX: CGFloat, config: MaskConfiguration) -> Path {
        Path { path in
            let width: CGFloat = UIScreen.main.bounds.width * config.width
            let height: CGFloat = UIScreen.main.bounds.height * config.height
            let offset: CGFloat = UIScreen.main.bounds.height * config.offset
            
            let topPoint = CGPoint(x: centerX, y: offset + height)
            let leftPoint = CGPoint(x: centerX - width/2, y: offset)
            let rightPoint = CGPoint(x: centerX + width/2, y: offset)
            
            // 计算控制点 - 根据曲线强度调整
            let controlY = offset + height * config.curveIntensity
            let controlXOffset = width/8
            let leftControlPoint = CGPoint(x: centerX - controlXOffset, y: controlY)
            let rightControlPoint = CGPoint(x: centerX + controlXOffset, y: controlY)
            
            path.move(to: topPoint)
            path.addQuadCurve(to: leftPoint, control: leftControlPoint)
            path.addLine(to: rightPoint)
            path.addQuadCurve(to: topPoint, control: rightControlPoint)
            path.closeSubpath()
        }
    }
} 