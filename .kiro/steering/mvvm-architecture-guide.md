# 🏗️ Lomo 项目增强版 MVVM-S 架构指南

## 📋 架构概述

基于深入的架构分析和行业最佳实践，制定以下完善的 MVVM-S (Service) 架构指南。本指南平衡了架构严格性与实际可操作性，为团队提供清晰、可执行的架构标准。

## 🎯 架构哲学

**核心理念**: 构建可扩展、高性能的现代 Swift 应用架构，通过渐进式改进和模块化设计实现架构目标。

**设计原则**:
- **单一职责**: 每层只负责自己的核心职责
- **依赖倒置**: 高层模块不依赖低层模块，都依赖抽象
- **开闭原则**: 对扩展开放，对修改封闭
- **渐进改善**: 允许技术债务的分阶段偿还
- **性能优先**: 架构决策考虑性能影响

## 🏛️ 核心架构原则

### 1. 五层架构分离

```
┌─────────────────┐
│   Coordinator   │ ← 导航逻辑，模块协调
├─────────────────┤
│      View       │ ← 纯UI展示，用户交互
├─────────────────┤
│   ViewModel     │ ← 状态管理，业务编排
├─────────────────┤
│    Service      │ ← 数据操作，外部接口
├─────────────────┤
│     Model       │ ← 数据结构，业务实体
└─────────────────┘
```

### 2. 依赖注入策略（渐进式实施）

#### 阶段1: 基础依赖注入
```swift
// 优先级1: 消除关键业务单例
protocol FilterServiceProtocol {
    func updateFilter(_ params: FilterParameters) async throws
}

class FilterViewModel: ObservableObject {
    private let filterService: FilterServiceProtocol
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
    }
}
```

#### 阶段2: 环境注入（推荐）
```swift
// SwiftUI 原生方式
extension EnvironmentValues {
    var filterService: FilterServiceProtocol {
        get { self[FilterServiceKey.self] }
        set { self[FilterServiceKey.self] = newValue }
    }
}

struct FilterView: View {
    @Environment(\.filterService) private var filterService
    @StateObject private var viewModel: FilterViewModel
    
    init() {
        self._viewModel = StateObject(wrappedValue: FilterViewModel())
    }
    
    var body: some View {
        // UI 实现
    }
    .onAppear {
        viewModel.configure(with: filterService)
    }
}
```

#### 阶段3: 模块化容器
```swift
// 模块化的依赖提供者
protocol FilterDependencyProviding {
    var filterService: FilterServiceProtocol { get }
    var storageService: StorageProtocol { get }
}

protocol SubscriptionDependencyProviding {
    var subscriptionService: SubscriptionServiceProtocol { get }
    var analyticsService: AnalyticsProtocol { get }
}

// 组合式依赖管理
class AppDependencyContainer {
    lazy var filterDependencies: FilterDependencyProviding = FilterDependencyProvider()
    lazy var subscriptionDependencies: SubscriptionDependencyProviding = SubscriptionDependencyProvider()
}
```

### 3. 状态管理进阶模式

#### 基础状态模式
```swift
enum ViewState<T> {
    case idle
    case loading
    case loaded(T)
    case error(AppError)
    
    var isLoading: Bool {
        if case .loading = self { return true }
        return false
    }
    
    var error: AppError? {
        if case .error(let error) = self { return error }
        return nil
    }
}

@MainActor
class FilterViewModel: ObservableObject {
    @Published var state: ViewState<FilterParameters> = .idle
    @Published var filterParameters = FilterParameters()
    
    private let filterService: FilterServiceProtocol
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
    }
}
```

#### 复杂状态管理（Reducer模式）
```swift
// 对于复杂交互的模块
struct FilterAction {
    enum ActionType {
        case updateExposure(Float)
        case updateContrast(Float)
        case resetAll
        case saveSettings
        case loadSettings
    }
}

struct FilterState {
    var parameters: FilterParameters
    var uiState: ViewState<Void>
    var validationErrors: [ValidationError]
}

@MainActor
class FilterViewModel: ObservableObject {
    @Published private(set) var state = FilterState()
    
    func dispatch(_ action: FilterAction) {
        Task {
            await handle(action)
        }
    }
    
    private func handle(_ action: FilterAction) async {
        // 状态更新逻辑
    }
}
```

## 📊 架构质量评分系统（分级标准）

| 等级 | 分数范围 | 标准 | 适用场景 |
|------|----------|------|----------|
| **优秀** | 90-100分 | 完全符合所有架构原则 | 核心功能模块 |
| **良好** | 75-89分 | 符合主要原则，有小幅偏差 | 一般功能模块 |
| **可接受** | 60-74分 | 基本符合，存在技术债务计划 | 遗留代码重构中 |
| **需改进** | <60分 | 不符合核心原则 | 必须立即重构 |

### 评分细则

| 评分项目 | 权重 | 优秀(9-10分) | 良好(7-8分) | 可接受(6-7分) | 需改进(<6分) |
|---------|------|------------|-----------|-------------|------------|
| **状态管理** | 25% | 集中式状态，@Published | 大部分集中，少量分散 | 混合状态管理 | 状态分散混乱 |
| **依赖注入** | 25% | 完全依赖注入 | 主要依赖注入，少量单例 | 部分单例使用 | 大量单例依赖 |
| **层次分离** | 20% | 严格分层，无越界 | 分层清晰，偶有越界 | 基本分层 | 层次混乱 |
| **错误处理** | 15% | 统一错误处理机制 | 基本错误处理 | 部分错误处理 | 缺少错误处理 |
| **性能优化** | 10% | 考虑性能影响 | 基本性能考虑 | 少量性能考虑 | 无性能考虑 |
| **架构清晰度** | 5% | 架构层次清晰 | 基本清晰 | 部分清晰 | 架构混乱 |

## 🏗️ 标准架构模板

### Model 层增强
```swift
// ✅ 基础数据模型
struct FilterParameters: Codable, Equatable {
    var exposure: Float = 0.0
    var contrast: Float = 0.0
    var brightness: Float = 0.0
    
    // 验证逻辑
    func validate() -> [ValidationError] {
        var errors: [ValidationError] = []
        if exposure < -2.0 || exposure > 2.0 {
            errors.append(.invalidExposureRange)
        }
        return errors
    }
}

// ✅ 业务实体
struct User: Identifiable, Codable {
    let id: String
    let name: String
    let isProUser: Bool
    let subscriptionExpiry: Date?
    
    var hasActiveSubscription: Bool {
        guard let expiry = subscriptionExpiry else { return false }
        return expiry > Date()
    }
}

// ✅ 错误类型定义
enum AppError: LocalizedError, Equatable {
    case networkError(underlying: String)
    case storageError(underlying: String)
    case validationError([ValidationError])
    case subscriptionRequired
    
    var errorDescription: String? {
        switch self {
        case .networkError(let message):
            return "网络错误: \(message)"
        case .storageError(let message):
            return "存储错误: \(message)"
        case .validationError(let errors):
            return "验证失败: \(errors.map(\.localizedDescription).joined(separator: ", "))"
        case .subscriptionRequired:
            return "此功能需要 Pro 订阅"
        }
    }
}
```

### Service 层增强
```swift
// ✅ 服务协议设计
protocol FilterServiceProtocol: Actor {
    func saveSettings(_ settings: FilterParameters) async throws
    func loadSettings() async throws -> FilterParameters
    func resetToDefaults() async throws
    func validateSettings(_ settings: FilterParameters) async -> [ValidationError]
}

// ✅ 并发安全的Service实现
actor FilterService: FilterServiceProtocol {
    private let storage: StorageProtocol
    private let validator: FilterValidatorProtocol
    
    init(storage: StorageProtocol, validator: FilterValidatorProtocol) {
        self.storage = storage
        self.validator = validator
    }
    
    func saveSettings(_ settings: FilterParameters) async throws {
        let errors = await validator.validate(settings)
        guard errors.isEmpty else {
            throw AppError.validationError(errors)
        }
        try await storage.save(settings, key: "filter_settings")
        await notifySettingsChanged(settings)
    }
    
    func loadSettings() async throws -> FilterParameters {
        do {
            return try await storage.load(FilterParameters.self, key: "filter_settings")
        } catch {
            // 返回默认设置而不是抛出错误
            return FilterParameters()
        }
    }
    
    private func notifySettingsChanged(_ settings: FilterParameters) async {
        // 通知其他相关服务
    }
}

// ✅ 存储抽象
protocol StorageProtocol {
    func save<T: Codable>(_ object: T, key: String) async throws
    func load<T: Codable>(_ type: T.Type, key: String) async throws -> T
    func remove(key: String) async throws
}

// ✅ 具体存储实现
class UserDefaultsStorage: StorageProtocol {
    private let userDefaults = UserDefaults.standard
    private let queue = DispatchQueue(label: "storage.queue", qos: .utility)
    
    func save<T: Codable>(_ object: T, key: String) async throws {
        try await withCheckedThrowingContinuation { continuation in
            queue.async {
                do {
                    let data = try JSONEncoder().encode(object)
                    self.userDefaults.set(data, forKey: key)
                    continuation.resume()
                } catch {
                    continuation.resume(throwing: AppError.storageError(error.localizedDescription))
                }
            }
        }
    }
    
    func load<T: Codable>(_ type: T.Type, key: String) async throws -> T {
        try await withCheckedThrowingContinuation { continuation in
            queue.async {
                guard let data = self.userDefaults.data(forKey: key) else {
                    continuation.resume(throwing: AppError.storageError("数据不存在"))
                    return
                }
                do {
                    let object = try JSONDecoder().decode(type, from: data)
                    continuation.resume(returning: object)
                } catch {
                    continuation.resume(throwing: AppError.storageError(error.localizedDescription))
                }
            }
        }
    }
}
```

### ViewModel 层增强
```swift
// ✅ 高性能ViewModel实现
@MainActor
class FilterViewModel: ObservableObject {
    // MARK: - Published Properties (最小化使用)
    @Published private(set) var state: ViewState<FilterParameters> = .idle
    @Published var filterParameters = FilterParameters() {
        didSet {
            // 防抖保存
            saveDebouncer.schedule()
        }
    }
    
    // MARK: - 依赖
    private let filterService: FilterServiceProtocol
    private let debouncer = Debouncer(delay: 0.5)
    private lazy var saveDebouncer = Debouncer(delay: 1.0) { [weak self] in
        Task { await self?.saveSettings() }
    }
    
    // MARK: - 私有状态
    private var cancellables = Set<AnyCancellable>()
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
        setupBindings()
        loadSettings()
    }
    
    // MARK: - 公共方法
    func updateExposure(_ value: Float) {
        var newParams = filterParameters
        newParams.exposure = value
        filterParameters = newParams
    }
    
    func updateContrast(_ value: Float) {
        var newParams = filterParameters
        newParams.contrast = value
        filterParameters = newParams
    }
    
    func resetAllParameters() {
        Task {
            state = .loading
            do {
                try await filterService.resetToDefaults()
                await loadSettings()
            } catch {
                state = .error(AppError.from(error))
            }
        }
    }
    
    // MARK: - 私有方法
    private func setupBindings() {
        // 性能优化：只在必要时更新UI
        $filterParameters
            .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main)
            .removeDuplicates()
            .sink { [weak self] _ in
                // 触发相关UI更新
            }
            .store(in: &cancellables)
    }
    
    private func loadSettings() {
        Task {
            state = .loading
            do {
                let settings = try await filterService.loadSettings()
                filterParameters = settings
                state = .loaded(settings)
            } catch {
                state = .error(AppError.from(error))
            }
        }
    }
    
    private func saveSettings() async {
        do {
            try await filterService.saveSettings(filterParameters)
        } catch {
            state = .error(AppError.from(error))
        }
    }
}

// ✅ 防抖工具
class Debouncer {
    private let delay: TimeInterval
    private let action: () -> Void
    private var workItem: DispatchWorkItem?
    
    init(delay: TimeInterval, action: @escaping () -> Void) {
        self.delay = delay
        self.action = action
    }
    
    func schedule() {
        workItem?.cancel()
        workItem = DispatchWorkItem { [weak self] in
            self?.action()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem!)
    }
}
```

### View 层增强
```swift
// ✅ 性能优化的View实现
struct FilterView: View {
    @StateObject private var viewModel: FilterViewModel
    @Environment(\.dismiss) private var dismiss
    
    init(filterService: FilterServiceProtocol) {
        self._viewModel = StateObject(wrappedValue: 
            FilterViewModel(filterService: filterService))
    }
    
    var body: some View {
        NavigationView {
            contentView
                .navigationTitle("滤镜调整")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("重置") {
                            viewModel.resetAllParameters()
                        }
                        .disabled(viewModel.state.isLoading)
                    }
                }
        }
        .alert("错误", isPresented: .constant(viewModel.state.error != nil)) {
            Button("确定") {
                // 清除错误状态
            }
        } message: {
            if let error = viewModel.state.error {
                Text(error.localizedDescription)
            }
        }
    }
    
    @ViewBuilder
    private var contentView: some View {
        switch viewModel.state {
        case .idle, .loading:
            loadingView
        case .loaded:
            mainContentView
        case .error:
            errorView
        }
    }
    
    private var loadingView: some View {
        VStack {
            ProgressView("加载中...")
            Text("正在加载滤镜设置")
                .foregroundColor(.secondary)
                .font(.caption)
        }
    }
    
    private var mainContentView: some View {
        ScrollView {
            VStack(spacing: 24) {
                // 曝光调整
                FilterSliderView(
                    title: "曝光",
                    value: $viewModel.filterParameters.exposure,
                    range: -2.0...2.0,
                    onValueChanged: viewModel.updateExposure
                )
                
                // 对比度调整
                FilterSliderView(
                    title: "对比度",
                    value: $viewModel.filterParameters.contrast,
                    range: -1.0...1.0,
                    onValueChanged: viewModel.updateContrast
                )
                
                // 预览区域
                FilterPreviewView(parameters: viewModel.filterParameters)
            }
            .padding()
        }
    }
    
    private var errorView: some View {
        VStack {
            Image(systemName: "exclamationmark.triangle")
                .font(.largeTitle)
                .foregroundColor(.red)
            Text("加载失败")
                .font(.headline)
            Button("重试") {
                viewModel.loadSettings()
            }
            .buttonStyle(.borderedProminent)
        }
    }
}

// ✅ 可复用的滤镜滑块组件
struct FilterSliderView: View {
    let title: String
    @Binding var value: Float
    let range: ClosedRange<Float>
    let onValueChanged: (Float) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.headline)
                Spacer()
                Text(String(format: "%.1f", value))
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(.secondary)
            }
            
            Slider(value: $value, in: range) { editing in
                if !editing {
                    onValueChanged(value)
                }
            }
        }
        .padding(.vertical, 4)
    }
}
```

### Coordinator 层（导航管理）
```swift
// ✅ 导航协调器
protocol FilterCoordinating: AnyObject {
    func showFilterEditor()
    func showPresetSelection()
    func dismissCurrentView()
}

@MainActor
class FilterCoordinator: ObservableObject, FilterCoordinating {
    @Published var presentedView: FilterPresentedView?
    
    private let dependencyProvider: FilterDependencyProviding
    
    init(dependencyProvider: FilterDependencyProviding) {
        self.dependencyProvider = dependencyProvider
    }
    
    func showFilterEditor() {
        let viewModel = FilterViewModel(filterService: dependencyProvider.filterService)
        presentedView = .editor(viewModel)
    }
    
    func showPresetSelection() {
        presentedView = .presetSelection
    }
    
    func dismissCurrentView() {
        presentedView = nil
    }
}

enum FilterPresentedView: Identifiable {
    case editor(FilterViewModel)
    case presetSelection
    
    var id: String {
        switch self {
        case .editor: return "editor"
        case .presetSelection: return "presetSelection"
        }
    }
}
```

## 🚫 测试策略说明（重要）

### 📋 当前阶段政策：不编写测试代码

**明确声明**：当前项目处于架构重构阶段，暂时不需要编写任何测试相关代码。

#### ❌ 禁止的测试相关工作
- 不编写单元测试（XCTestCase）
- 不创建 Mock 对象
- 不编写测试用例
- 不讨论测试策略

#### ✅ 当前质量保证方式
- **编译验证**：确保代码编译通过，0警告0错误
- **手动功能验证**：通过实际操作验证功能正常
- **架构评分**：使用架构质量评分系统
- **代码审查**：人工审查架构合规性和代码质量

#### 🎯 专注重点
- MVVM-S 架构实现
- 依赖注入体系建立
- 状态管理规范化
- 代码结构优化

**重要**：任何测试相关的建议都是不必要的，请专注于架构重构工作。

## 🚀 性能优化指南

### 1. @Published 使用优化
```swift
// ❌ 避免频繁更新
@Published var sliderValue: Float = 0.0 {
    didSet {
        updateFilter() // 每次滑动都会触发
    }
}

// ✅ 使用防抖
@Published var sliderValue: Float = 0.0
private let updateDebouncer = Debouncer(delay: 0.3) { [weak self] in
    self?.updateFilter()
}

func onSliderChanged() {
    updateDebouncer.schedule()
}
```

### 2. 状态更新优化
```swift
// ❌ 避免不必要的UI更新
@Published var complexObject: ComplexObject = ComplexObject()

// ✅ 使用 Equatable 避免重复更新
struct FilterParameters: Equatable {
    // 实现 Equatable
}

@Published var filterParameters = FilterParameters() // 只在实际改变时更新
```

### 3. 内存管理
```swift
class FilterViewModel: ObservableObject {
    private var cancellables = Set<AnyCancellable>()
    
    deinit {
        // 确保清理资源
        cancellables.removeAll()
        print("🗑️ FilterViewModel deinitialized")
    }
}
```

## 📈 架构演进路线图

### 阶段1: 基础重构 (1-2个月)
- [ ] 识别并重构最关键的单例依赖
- [ ] 实施基础依赖注入模式
- [ ] 建立标准错误处理机制
- [ ] 核心模块达到85分以上

### 阶段2: 架构优化 (2-3个月)
- [ ] 引入环境注入模式
- [ ] 实施并发安全改进
- [ ] 优化状态管理模式
- [ ] 完善架构文档和规范

### 阶段3: 高级特性 (3-4个月)
- [ ] 实施Coordinator模式
- [ ] 引入复杂状态管理（如需要）
- [ ] 性能优化和监控
- [ ] 架构文档完善

## 🔧 实用工具和扩展

### 架构检查工具
```swift
// ✅ 运行时架构验证
#if DEBUG
struct ArchitectureValidator {
    static func validateDependencies(for viewModel: Any) {
        let mirror = Mirror(reflecting: viewModel)
        for child in mirror.children {
            if let property = child.value as? AnyObject,
               String(describing: type(of: property)).contains("Manager"),
               String(describing: property).contains("shared") {
                fatalError("⚠️ 发现单例依赖: \(child.label ?? "unknown")")
            }
        }
    }
}
#endif
```

### 依赖注入助手
```swift
// ✅ 简化依赖注入的宏（如果可用）
@DependencyInjected var filterService: FilterServiceProtocol

// ✅ 手动依赖注入助手
protocol Injectable {
    associatedtype Dependencies
    init(dependencies: Dependencies)
}

extension Injectable {
    static func create(with dependencies: Dependencies) -> Self {
        return Self(dependencies: dependencies)
    }
}
```

## 🛡️ 架构守护规则

### 代码审查检查清单

#### 必须项 (违反立即拒绝)
- [ ] 无新增业务逻辑单例
- [ ] ViewModel不包含UI操作代码
- [ ] Service层有对应协议定义
- [ ] 异步操作有错误处理

#### 推荐项 (优化建议)
- [ ] 使用@Published时考虑性能影响
- [ ] 复杂交互考虑Coordinator模式
- [ ] 架构层次清晰分离
- [ ] 遵循命名约定

### 违规处理流程
1. **自动检查**: CI/CD 中运行架构检查脚本
2. **人工审查**: 代码审查时检查架构合规性
3. **渐进改善**: 对于历史代码，制定改善计划
4. **培训支持**: 提供架构培训和最佳实践分享

## 📚 学习资源和参考

### 推荐阅读
- [Swift Concurrency](https://docs.swift.org/swift-book/LanguageGuide/Concurrency.html)
- [SwiftUI Data Flow](https://developer.apple.com/documentation/swiftui/managing-model-data-in-your-app)
- [Dependency Injection in Swift](https://www.swiftbysundell.com/articles/dependency-injection-using-factories-in-swift/)

### 架构决策记录 (ADR)

#### ADR-001: 选择 MVVM-S 架构
**决策**: 采用 MVVM-S 架构而不是 VIPER 或 TCA

**原因**:
- 与 SwiftUI 天然契合
- 学习曲线相对平缓
- 支持渐进式重构
- 平衡了复杂性和功能性

**影响**:
- 需要严格的分层约束
- 需要依赖注入基础设施
- 更容易维护和扩展

#### ADR-002: 渐进式依赖注入
**决策**: 采用渐进式依赖注入而不是一次性全部重构

**原因**:
- 降低重构风险
- 允许团队学习适应
- 保持业务连续性

**影响**:
- 需要制定清晰的重构计划
- 可能存在临时的架构不一致
- 需要持续的架构监控

---

## 🎯 总结

这份增强版 MVVM-S 架构指南提供了：

1. **渐进式架构改进**: 平衡理想与现实的架构演进路径
2. **分级质量标准**: 灵活的评分系统适应不同模块需求
3. **完整实施指南**: 从基础到高级的详细实现模板
4. **性能优化策略**: 确保架构改进不影响应用性能
5. **工具和文档支持**: 完整的开发工具和架构文档

**执行原则**: 
- 核心模块追求90-100分的优秀标准
- 一般模块达到75-89分的良好标准
- 遗留代码制定60-74分的改善计划
- 新增代码严格按照架构指南执行

通过这种平衡的方法，我们可以在保持开发效率的同时，逐步提升整体架构质量。