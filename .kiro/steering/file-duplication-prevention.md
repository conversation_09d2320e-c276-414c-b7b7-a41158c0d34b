# 🚫 文件重复防护指南

## 📋 核心原则

**在创建任何新文件之前，必须先检查项目中是否已存在同名或功能相同的文件**

## 🚨 重复造轮子案例警示

### 案例：Crop模块重构中的UIConstants重复创建

**错误行为**：
- 需要UIConstants和AnimationConstants时，直接创建新文件
- 没有先检查项目中是否已有相同功能的文件
- 结果：重复创建了已存在的常量定义

**正确做法**：
- 先搜索现有的常量文件：`find . -name "*Constants*" -type f`
- 发现`CameraConstants.swift`已包含所需的UIConstants和AnimationConstants
- 直接使用现有常量，避免重复定义

**教训**：重复造轮子不仅浪费时间，还违反了DRY原则，增加了维护成本

## 🔍 强制检查流程

### 1. 创建文件前的必要检查

```bash
# 检查文件是否已存在
find . -name "FileName.swift" -type f

# 检查相似功能的文件（按功能分类搜索）
find . -name "*Constants*" -type f    # 常量定义
find . -name "*Utils*" -type f        # 工具类
find . -name "*Service*" -type f      # 服务类
find . -name "*Manager*" -type f      # 管理器类
find . -name "*Navigation*" -type f   # 导航相关
find . -name "*Animation*" -type f    # 动画相关
find . -name "*UI*" -type f           # UI相关
```

### 2. 功能重复检查（重点！）

**在创建任何功能性文件前，必须先检查现有文件的功能覆盖范围**

```bash
# 检查常量定义文件
grep -r "static let.*Color\|static let.*CGFloat\|static let.*Animation" Lomo/Utils/Constants/

# 检查UI工具文件  
grep -r "struct.*Utils\|class.*Utils" Lomo/Utils/

# 检查服务接口文件
grep -r "protocol.*Service\|protocol.*Protocol" Lomo/Services/Protocols/
```

### 3. 现有文件功能映射表（必读！）

**在创建新文件前，必须先查阅此表确认功能是否已存在**

| 功能需求 | 现有文件位置 | 包含内容 | 使用方式 |
|---------|-------------|----------|----------|
| **UI常量** | `Lomo/Utils/Constants/CameraConstants.swift` | UIConstants枚举：字体大小、颜色、尺寸、间距等 | `UIConstants.dialMainTickLength` |
| **动画常量** | `Lomo/Utils/Constants/CameraConstants.swift` | AnimationConstants枚举：弹簧动画、缓动动画等 | `AnimationConstants.standardSpring` |
| **水印常量** | `Lomo/Utils/Constants/WatermarkConstants.swift` | 水印样式、尺寸、颜色等常量 | `WatermarkConstants.Polaroid.logoSizeScreenHeightFactor` |
| **遮罩工具** | `Lomo/Utils/MaskUtils.swift` | 创建各种遮罩路径的工具方法 | `MaskUtils.createScaleRulerMaskPath()` |
| **布局工具** | `Lomo/Utils/LayoutUtils.swift` | 布局计算、屏幕适配等工具 | `LayoutUtils.calculateSize()` |
| **字体工具** | `Lomo/Utils/FontUtils.swift` | 字体处理、文本测量等工具 | `FontUtils.measureText()` |
| **导航栏** | `Lomo/Views/Shared/NavigationTopBar.swift` | 通用顶部导航栏组件 | 直接使用组件 |
| **底部标签** | `Lomo/Views/Shared/BottomTabBar.swift` | 底部标签栏组件 | 直接使用组件 |
| **通用按钮** | `Lomo/Views/Components/OptionButton.swift` | 可复用的选项按钮组件 | 直接使用组件 |
| **自定义滑块** | `Lomo/Views/Components/CustomSlider.swift` | 自定义滑块组件 | 直接使用组件 |

### 4. 功能检查命令速查表

| 需要创建的功能 | 检查命令 | 预期发现 |
|---------------|----------|----------|
| UI常量类 | `grep -r "enum UIConstants\|struct UIConstants" Lomo/Utils/Constants/` | CameraConstants.swift中的UIConstants |
| 动画常量类 | `grep -r "enum AnimationConstants\|struct AnimationConstants" Lomo/Utils/Constants/` | CameraConstants.swift中的AnimationConstants |
| 工具类 | `find Lomo/Utils -name "*Utils.swift" -type f` | MaskUtils, LayoutUtils, FontUtils等 |
| 通用组件 | `find Lomo/Views/Components -name "*.swift" -type f` | OptionButton, CustomSlider等 |
| 共享视图 | `find Lomo/Views/Shared -name "*.swift" -type f` | NavigationTopBar, BottomTabBar等 |

### 3. 项目结构检查

```bash
# 检查Views目录结构
find Lomo/Views -type f -name "*.swift" | sort

# 检查Components目录
find Lomo/Views/Components -type f -name "*.swift" | sort

# 检查Configuration目录
find Lomo/Configuration -type f -name "*.swift" | sort
```

## ⚠️ 常见重复文件警告

### NavigationTopBar 冲突示例
```
错误信息: Multiple commands produce 'NavigationTopBar.stringsdata'
原因: 项目中可能已存在 NavigationBar.swift 或 TopBar.swift
解决: 检查现有导航组件，复用或合并功能
```

### UIConstants 冲突示例
```
错误信息: Multiple commands produce 'UIConstants.o'
原因: 项目中可能已存在 AppConstants.swift 或 Config.swift
解决: 检查现有配置文件，合并常量定义
```

## 🔧 预防措施

### 1. 创建文件前的标准流程

```bash
# 步骤1: 检查文件是否存在
echo "检查文件: $FILENAME"
if [ -f "$FILEPATH" ]; then
    echo "❌ 文件已存在: $FILEPATH"
    exit 1
fi

# 步骤2: 检查相似功能文件
echo "检查相似功能文件..."
find . -name "*$(basename $FILENAME .swift)*" -type f

# 步骤3: 确认无冲突后创建
echo "✅ 无冲突，可以创建文件"
```

### 2. 文件命名规范

- **唯一性**: 确保文件名在项目中唯一
- **描述性**: 文件名应清楚描述其功能
- **模块前缀**: 使用模块前缀避免冲突 (如: GalleryFilterView)
- **功能后缀**: 使用标准后缀 (View, ViewModel, Service, Protocol)

### 3. 目录结构规范

```
Lomo/
├── Views/
│   ├── Components/          # 通用UI组件
│   ├── Filter/             # 滤镜模块视图
│   ├── Gallery/            # 相册模块视图
│   └── Shared/             # 共享视图
├── Configuration/          # 配置文件
├── Models/                 # 数据模型
└── Services/              # 服务层
```

## 🚨 紧急修复流程

### 当出现重复文件错误时：

1. **立即停止开发**
2. **识别冲突文件**
   ```bash
   # 查找所有同名文件
   find . -name "NavigationTopBar*" -type f
   ```
3. **分析功能重复**
   - 比较文件内容
   - 确定保留哪个文件
   - 合并必要功能
4. **删除重复文件**
   ```bash
   # 删除重复文件
   rm path/to/duplicate/file.swift
   ```
5. **更新引用**
   - 更新import语句
   - 更新文件引用
6. **验证编译**
   ```bash
   # 验证编译通过
   xcodebuild -project Lomo.xcodeproj -scheme Lomo build
   ```

## 📋 检查清单

在创建任何新文件前，必须完成以下检查：

- [ ] 使用 `find` 命令检查同名文件
- [ ] 检查相似功能的现有文件
- [ ] 确认文件名符合命名规范
- [ ] 确认目录位置正确
- [ ] 验证无编译冲突
- [ ] 添加正确的版权声明

## 📚 项目资源索引

**在创建任何新文件前，请先查阅项目资源索引：**
`Lomo/Documentation/ProjectResourcesIndex.md`

该文档详细列出了所有可复用的资源，包括：
- 常量定义文件及其包含内容
- 工具类文件及其功能
- 通用UI组件及其使用场景
- 快速查找命令和使用示例

## 🎯 最佳实践

### 1. 查找优于创建
- **第一步**：查阅项目资源索引
- **第二步**：使用搜索命令确认
- **第三步**：如确实不存在，再创建新文件

### 2. 注释驱动开发
- 在文件头部添加详细的功能说明注释
- 包含使用示例和适用场景
- 添加"避免重复创建"的提醒

### 3. 索引维护
- 创建新资源时，同步更新项目资源索引
- 发现重复资源时，立即合并并更新索引
- 定期审查和清理重复资源

## 🔗 相关工具

### 智能重复检查脚本
```bash
#!/bin/bash
# smart_duplication_check.sh - 智能重复检查脚本

FILENAME="$1"
FUNCTIONALITY="$2"

if [ -z "$FILENAME" ]; then
    echo "用法: $0 <filename> [functionality]"
    echo "示例: $0 UIConstants.swift '常量定义'"
    exit 1
fi

echo "🔍 智能检查文件重复: $FILENAME"
echo "📋 功能类型: ${FUNCTIONALITY:-'未指定'}"

# 1. 检查完全匹配
echo "1️⃣ 检查同名文件..."
if find . -name "$FILENAME" -type f | grep -q .; then
    echo "❌ 发现同名文件:"
    find . -name "$FILENAME" -type f
    exit 1
fi

# 2. 检查功能相似文件
echo "2️⃣ 检查功能相似文件..."
BASENAME=$(basename "$FILENAME" .swift)

case "$BASENAME" in
    *Constants*)
        echo "🔍 检查常量定义文件..."
        find Lomo/Utils/Constants -name "*.swift" -type f
        echo "💡 提示：检查现有常量文件是否已包含所需常量"
        ;;
    *Utils*)
        echo "🔍 检查工具类文件..."
        find Lomo/Utils -name "*Utils.swift" -type f
        echo "💡 提示：检查现有工具类是否已包含所需功能"
        ;;
    *Service*)
        echo "🔍 检查服务类文件..."
        find Lomo/Services -name "*Service.swift" -type f
        echo "💡 提示：检查现有服务是否已包含所需功能"
        ;;
    *)
        echo "🔍 检查相似命名文件..."
        find . -name "*$BASENAME*" -type f
        ;;
esac

# 3. 提供资源索引提醒
echo ""
echo "📚 重要提醒："
echo "请先查阅项目资源索引: Lomo/Documentation/ProjectResourcesIndex.md"
echo "该文档详细列出了所有可复用的资源和使用方法"

echo ""
echo "✅ 检查完成，请确认是否真的需要创建新文件"
```

---

**重要提醒**: 这个规则是强制性的，违反将导致编译错误和项目不稳定。每次创建文件前都必须执行检查！