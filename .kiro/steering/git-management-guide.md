# 📚 LoniceraLab 项目 Git 管理指南

## 🎯 核心管理原则（最高优先级）

### 📍 本地项目路径为准原则
**绝对原则**: `/Users/<USER>/Lomo` 是项目的唯一标准和权威来源

**执行策略**:
- ✅ **本地项目路径为准**: `/Users/<USER>/Lomo` 是标准，所有决策以本地为准
- ✅ **其他都要适配本地**: GitHub、远程仓库、云端等都要配合本地项目
- ❌ **绝不修改本地适配其他**: 绝不为了适配远程仓库而改动本地项目结构

**实施要求**:
- 任何Git操作都必须保护本地项目的完整性
- 远程仓库状态冲突时，以本地为准进行解决
- 分支合并、代码同步等操作都要确保本地项目不受影响
- 所有自动化脚本和工具都要围绕本地项目路径设计

---

## 📋 项目信息
- **创作团队**: LoniceraLab 工作室
- **项目名称**: Lomo (iOS 相机应用)
- **开发语言**: Swift
- **架构模式**: MVVM-S
- **开发阶段**: 框架搭建期 + 架构重构期
- **项目工作目录**: `/Users/<USER>/Lomo` （权威标准路径）
- **Git 仓库数据**: `/Volumes/LH 16T/GitRepos/Lomo.git`
- **GitHub 仓库**: `https://github.com/LH151211/Lomo.git`

---

## 🗂️ 仓库配置信息

### 本地开发环境
- **项目工作目录**: `/Users/<USER>/Lomo` （日常开发工作的地方）
- **Git 仓库数据**: `/Volumes/LH 16T/GitRepos/Lomo.git` （Git 历史数据存储）
- **配置方式**: 使用 gitdir 分离工作目录和仓库数据
- **存储优势**: 工作文件在快速内置硬盘，Git 数据在大容量外置硬盘

### 远程仓库配置
- **GitHub 仓库**: `https://github.com/LH151211/Lomo.git`
- **仓库所有者**: LH151211
- **访问权限**: 私有仓库（推荐）
- **主分支**: `main`

### Git 配置说明
```bash
# 项目工作目录（开发位置）
cd /Users/<USER>/Lomo

# Git 仓库数据位置（通过 .git 文件指向）
cat .git
# 输出: gitdir: /Volumes/LH 16T/GitRepos/Lomo.git

# 正常使用 Git 命令（在工作目录执行）
git status
git add .
git commit -m "提交信息"
git push origin main
```

---

## 🛡️ 本地项目保护指南

### 🚨 禁止的危险操作
**绝对禁止**以下可能破坏本地项目的操作：

1. **强制合并远程分支**: `git pull --force`, `git merge --allow-unrelated-histories`
2. **重置本地分支**: `git reset --hard origin/main`
3. **删除本地分支**: 除非明确确认不需要
4. **修改本地项目结构**: 为了适配远程仓库而改动本地文件组织
5. **自动解决冲突**: 必须手动检查每个冲突

### ✅ 安全的Git操作流程
**推荐的安全操作顺序**：

```bash
# 1. 检查当前状态（安全）
git status
git branch -v

# 2. 备份当前工作（安全）
git add .
git commit -m "📦 backup: 操作前状态备份"

# 3. 推送到远程（安全）
git push origin [current-branch]

# 4. 如需同步远程，先检查差异（安全）
git fetch origin
git log --oneline HEAD..origin/main  # 查看远程新提交
git diff HEAD origin/main             # 查看具体差异

# 5. 只有在完全确认安全时才进行合并操作
```

### 🔒 本地项目完整性验证
**每次Git操作后必须验证**：

```bash
# 验证项目路径正确
pwd  # 应该显示: /Users/<USER>/Lomo

# 验证关键文件存在
ls -la Lomo/LomoApp.swift
ls -la Lomo/Views/
ls -la Lomo/ViewModels/

# 验证项目可以编译
xcodebuild -project Lomo.xcodeproj -scheme Lomo build
```

---

## 🎯 LoniceraLab 工作室 Git 管理哲学

### 核心原则
- **架构优先**: 每次提交都要考虑对 MVVM-S 架构的影响
- **功能完整**: 确保每次提交后 Lomo 应用都能正常编译和运行
- **中文友好**: 提交信息支持中文，便于 LoniceraLab 团队理解
- **质量保证**: 每次提交都要通过编译检查，符合 LoniceraLab 工作室代码标准

---

## 📝 LoniceraLab 提交信息规范

### 1. 提交信息格式（支持中文）
```
<emoji> <type>: <description>

<body>

<footer>
```

### 2. LoniceraLab 工作室专用 Emoji 和类型
| Emoji | Type | 中文说明 | 使用场景 |
|-------|------|----------|----------|
| 🏗️ | **arch** | 架构重构 | Lomo 项目 MVVM-S 架构改进、依赖注入实施 |
| ✨ | **feat** | 新功能 | Lomo 应用新功能开发 |
| 🐛 | **fix** | 修复问题 | Bug 修复、编译错误解决 |
| 📱 | **ios** | iOS 特性 | iOS 平台特定功能、SwiftUI 组件 |
| 🎨 | **ui** | 界面优化 | Lomo 应用 UI/UX 改进、视觉效果调整 |
| ⚡ | **perf** | 性能优化 | Lomo 应用性能提升、内存优化 |
| 📚 | **docs** | 文档更新 | 项目文档、代码注释、README |
| 🔧 | **config** | 配置修改 | Xcode 配置、构建脚本、依赖管理 |
| 🧹 | **clean** | 代码清理 | 代码格式化、无用代码删除 |
| 🚨 | **fix-critical** | 紧急修复 | 严重 Bug、崩溃问题修复 |
| 🔒 | **security** | 安全相关 | 安全漏洞修复、权限管理 |
| 🌐 | **i18n** | 国际化 | 中文本地化、多语言支持 |

### 3. LoniceraLab 工作室提交信息示例

#### 架构重构类
```bash
git commit -m "🏗️ arch: Lomo Gallery模块MVVM-S重构完成

🎯 重构内容:
- 创建GalleryViewModel和GalleryService
- 实施依赖注入模式，消除单例依赖
- 完善错误处理和状态管理

📊 架构评分: 75分 → 90分
✅ 编译通过，功能验证完成"
```

#### iOS 功能开发类
```bash
git commit -m "📱 ios: Lomo 新增相机滤镜实时预览功能

🎯 功能特性:
- 基于 Metal 的实时滤镜渲染
- 支持 10+ 种滤镜效果
- 优化 iOS 设备性能表现

🔧 技术实现:
- 使用 SwiftUI + Combine 架构
- Metal Performance Shaders 加速
- 内存使用优化，支持低端设备"
```

#### 问题修复类
```bash
git commit -m "🐛 fix: 修复 Xcode 构建数据库损坏问题

🚨 问题描述:
- disk I/O error: 构建数据库损坏
- TDDistiller instance 重复使用错误
- assetcatalog_dependencies_thinned 文件缺失

🛠️ 解决方案:
- 清理所有 DerivedData 缓存
- 创建智能缓存清理脚本
- 建立自动化清理机制

✅ 验证结果: 构建恢复正常，创建预防机制"
```

#### 文档更新类
```bash
git commit -m "📚 docs: 完善 Lomo 项目架构文档和开发指南

📋 更新内容:
- 新增 MVVM-S 架构详细指南
- 完善 LoniceraLab 工作室代码标准
- 创建模块重构操作手册

🎯 目标: 为 LoniceraLab 团队提供清晰的开发规范和架构指导"
```

---

## 🔄 Lomo 项目工作流程

### 1. 日常开发流程

#### 开始新功能开发
```bash
# 1. 同步主分支
git checkout main
git pull origin main

# 2. 创建功能分支（使用中文描述）
git checkout -b feature/相机滤镜功能
# 或使用英文
git checkout -b feature/camera-filter

# 3. 开发过程中频繁提交
git add .
git commit -m "🏗️ arch: 创建滤镜模块基础架构"
```

#### ⚠️ 架构重构流程 (重要更新)
```bash
# 🚨 重要：在开始重构前先提交当前状态
# 1. 提交重构前的稳定状态
git add .
git commit -m "📦 save: 开始[模块名]重构前的稳定状态保存"

# 2. 创建重构分支（可选，也可在当前分支继续）
git checkout -b refactor/gallery-mvvm-s

# 3. 分步骤提交重构（每个小步骤都提交）
git commit -m "🏗️ arch: 第一步 - 创建GalleryViewModel"
git commit -m "🏗️ arch: 第二步 - 实施依赖注入"  
git commit -m "🏗️ arch: 第三步 - 完善错误处理"

# 4. 重构完成后推送
git push origin refactor/gallery-mvvm-s
```

#### 🎯 重构前提交的重要性
**为什么必须在重构前提交？**
- **风险控制**: 重构过程中可能出现意外问题，需要回滚点
- **进度保护**: 避免因为重构失败导致之前的工作丢失
- **清晰历史**: 明确区分重构前后的代码状态
- **团队协作**: 让团队成员了解重构的起始点
- **问题定位**: 如果重构后出现问题，可以快速对比重构前状态

### 2. 🏗️ 模块重构专用工作流程

#### 标准重构流程 (强制执行)
```bash
# 步骤1: 重构前状态保存 (必须!)
echo "🔍 检查当前工作状态..."
git status

echo "📦 保存重构前状态..."
git add .
git commit -m "📦 save: [模块名]重构前状态保存 - $(date '+%Y-%m-%d %H:%M')"

# 步骤2: 创建重构计划文档
echo "📋 创建重构计划..."
# 创建模块分析文档
# 制定重构步骤计划

# 步骤3: 分步骤重构并提交
echo "🏗️ 开始分步骤重构..."
# 每完成一个小步骤就提交
git add .
git commit -m "🏗️ arch: [模块名] - 步骤1: 创建ViewModel"

git add .  
git commit -m "🏗️ arch: [模块名] - 步骤2: 创建Service协议"

git add .
git commit -m "🏗️ arch: [模块名] - 步骤3: 实施依赖注入"

# 步骤4: 重构完成总结提交
git add .
git commit -m "🎉 arch: [模块名]MVVM-S重构完成 - 架构评分提升至XX分"
```

#### 重构提交类型规范
| 提交类型 | 使用场景 | 示例 |
|---------|----------|------|
| `📦 save:` | 重构前状态保存 | `📦 save: 相纸模块重构前状态保存` |
| `🏗️ arch:` | 架构重构步骤 | `🏗️ arch: 相纸模块 - 创建协议抽象` |
| `🔧 fix:` | 重构中的错误修复 | `🔧 fix: 修复MainActor编译错误` |
| `📚 docs:` | 重构文档更新 | `📚 docs: 完善相纸模块重构文档` |
| `🎉 complete:` | 重构完成 | `🎉 complete: 相纸模块MVVM-S重构完成` |

### 3. 提交频率建议

#### Lomo 项目特定建议
- **架构重构**: 每完成一个重构步骤就提交（如创建ViewModel、Service等）
- **iOS 功能开发**: 每实现一个功能点就提交（如UI组件、业务逻辑等）
- **问题修复**: 每修复一个独立问题就提交
- **文档更新**: 文档修改可以单独提交
- **配置修改**: Xcode 配置、构建脚本修改单独提交

#### 避免的提交模式
❌ 一次提交包含多个模块的重构  
❌ 功能开发和架构重构混在一起提交  
❌ 提交信息使用纯英文（LoniceraLab 团队中文友好原则）  
❌ 提交未经编译验证的代码  

---

## 🌿 Lomo 项目分支管理策略

### 1. 分支命名规范
```bash
# 主要分支
main                    # 主分支，稳定版本
develop                 # 开发分支，集成最新功能

# 功能分支
feature/模块名-功能描述    # 新功能开发
feature/camera-filter   # 相机滤镜功能
feature/gallery-ui      # 相册界面功能

# 重构分支
refactor/模块名-mvvm-s   # 架构重构
refactor/gallery-mvvm-s # Gallery模块MVVM-S重构
refactor/crop-mvvm-s    # Crop模块MVVM-S重构

# 修复分支
fix/问题描述            # 问题修复
fix/xcode-build-error  # Xcode构建错误修复
fix/memory-leak        # 内存泄漏修复

# 发布分支
release/版本号          # 发布准备
release/v1.0.0         # 1.0.0版本发布

# 热修复分支
hotfix/紧急问题描述     # 生产环境紧急修复
hotfix/crash-on-launch # 启动崩溃紧急修复
```

### 2. 分支操作流程
```bash
# 创建并切换到新分支
git checkout -b feature/新功能

# 开发完成后推送
git push origin feature/新功能

# 合并到开发分支
git checkout develop
git merge feature/新功能

# 删除已合并的分支
git branch -d feature/新功能
git push origin --delete feature/新功能
```

---

## 🔍 LoniceraLab 代码审查标准

### 1. 提交前自检清单
- [ ] **编译检查**: 确保 Xcode 编译通过，0警告0错误
- [ ] **架构合规**: 符合 MVVM-S 架构标准
- [ ] **版权声明**: 所有新文件包含 LoniceraLab 版权声明
- [ ] **代码规范**: 遵循 LoniceraLab Swift 代码标准
- [ ] **功能验证**: 手动验证功能正常工作
- [ ] **中文支持**: 确保中文环境下正常工作
- [ ] **提交信息**: 使用规范的提交信息格式

### 2. 代码审查要点
```bash
# 架构层面
- 是否符合 MVVM-S 分层原则
- 是否正确使用依赖注入
- 是否避免了业务逻辑单例

# iOS 开发层面
- 是否遵循 SwiftUI 最佳实践
- 是否考虑了 iOS 设备性能
- 是否支持不同屏幕尺寸

# LoniceraLab 标准
- 版权声明是否正确
- 注释是否使用中文
- 是否使用了配置中心
```

---

## 🛠️ LoniceraLab Git 工具和脚本

### 1. 项目专用 Git 脚本

#### 智能提交脚本
```bash
#!/bin/bash
# /Users/<USER>/Lomo/Scripts/smart_git_commit.sh
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# LoniceraLab 工作室 Lomo 项目智能 Git 提交脚本

echo "🔍 LoniceraLab Lomo 项目智能提交检查..."
echo "📍 项目工作目录: /Users/<USER>/Lomo"
echo "💾 Git 仓库数据: /Volumes/LH 16T/GitRepos/Lomo.git"
echo "🌐 远程仓库: https://github.com/LH151211/Lomo.git"
echo ""

# 1. 编译检查
echo "1️⃣ 检查 Swift 编译..."
if ! swift build 2>/dev/null; then
    echo "❌ 编译失败，请修复后再提交"
    exit 1
fi

# 2. 版权声明检查
echo "2️⃣ 检查 LoniceraLab 版权声明..."
missing_copyright=$(find . -name "*.swift" -exec grep -L "Copyright (c) 2025 LoniceraLab" {} \;)
if [ -n "$missing_copyright" ]; then
    echo "⚠️ 以下文件缺少版权声明:"
    echo "$missing_copyright"
fi

# 3. 架构合规检查
echo "3️⃣ 检查 MVVM-S 架构合规性..."
# 检查是否有新的单例使用
if git diff --cached | grep -q "\.shared"; then
    echo "⚠️ 检测到可能的单例使用，请确认是否符合架构标准"
fi

echo "✅ 检查完成，可以提交到 GitHub"
```

#### Git Hooks 设置
```bash
#!/bin/bash
# /Volumes/LH 16T/GitRepos/Lomo.git/hooks/pre-commit
# LoniceraLab 工作室 Lomo 项目提交前检查

echo "🔗 LoniceraLab Git Hook 执行中..."
echo "📍 项目工作目录: /Users/<USER>/Lomo"
echo "💾 Git 仓库数据: /Volumes/LH 16T/GitRepos/Lomo.git"

# 运行智能提交检查
SCRIPT_DIR="$(git rev-parse --show-toplevel)/Lomo/Scripts"
SMART_COMMIT="$SCRIPT_DIR/smart_git_commit.sh"
SMART_CLEANER="$SCRIPT_DIR/smart_xcode_cache_cleaner.sh"

if [ -x "$SMART_COMMIT" ]; then
    if ! "$SMART_COMMIT"; then
        echo "❌ LoniceraLab 提交检查失败"
        exit 1
    fi
fi

# 运行智能缓存检查
if [ -x "$SMART_CLEANER" ]; then
    if "$SMART_CLEANER" check | grep -q "建议清理缓存"; then
        echo "💡 提示: 检测到 Xcode 缓存可能需要清理"
        echo "   运行: $SMART_CLEANER"
    fi
fi

echo "✅ LoniceraLab Git Hook 检查完成"
```

### 2. Git 别名配置
```bash
# 添加到 ~/.gitconfig 或 ~/.zshrc

# ========================================
# LoniceraLab 工作室 Lomo 项目 Git 别名
# 项目工作目录: /Users/<USER>/Lomo
# Git 仓库数据: /Volumes/LH 16T/GitRepos/Lomo.git
# GitHub: https://github.com/LH151211/Lomo.git
# ========================================

# 基础 Git 操作
alias gst='git status'
alias gco='git checkout'
alias gcb='git checkout -b'
alias gaa='git add .'
alias gcm='git commit -m'
alias gps='git push origin main'
alias gpl='git pull origin main'

# LoniceraLab 架构重构专用
alias garch='git add . && git commit -m "🏗️ arch: "'
alias gfeat='git add . && git commit -m "✨ feat: "'
alias gfix='git add . && git commit -m "🐛 fix: "'
alias gdocs='git add . && git commit -m "📚 docs: "'

# Lomo 项目专用操作
alias lomo-cd='cd "/Users/<USER>/Lomo"'
alias lomo-status='echo "📊 Lomo 项目状态:" && git status'
alias lomo-sync='git add . && git commit -m "🔄 sync: 同步更新" && git push origin main'

# 智能工具集成
alias gsmart='"/Users/<USER>/Lomo/Scripts/smart_git_commit.sh"'
alias xclean='"/Users/<USER>/Lomo/Scripts/smart_xcode_cache_cleaner.sh"'
```

---

## 📊 LoniceraLab 项目 Git 统计和监控

### 1. 提交质量监控
```bash
# 检查最近的提交质量
git log --oneline -10 --grep="🏗️\|✨\|🐛\|📚"

# 统计各类型提交数量
git log --oneline --grep="🏗️ arch" | wc -l  # 架构重构提交数
git log --oneline --grep="✨ feat" | wc -l   # 功能开发提交数
git log --oneline --grep="🐛 fix" | wc -l    # 问题修复提交数
```

### 2. 架构演进追踪
```bash
# 追踪架构重构进度
git log --oneline --grep="🏗️ arch" --since="1 month ago"

# 查看模块重构历史
git log --oneline --grep="Gallery.*MVVM-S"
git log --oneline --grep="Crop.*MVVM-S"
```

---

## 🎯 LoniceraLab 最佳实践总结

### 1. 日常开发建议
- **提交频率**: 每完成一个逻辑单元就提交（30分钟-2小时）
- **分支策略**: 使用功能分支开发，完成后合并到 develop
- **提交信息**: 使用 emoji + 中文描述，便于团队理解
- **代码审查**: 重点关注架构合规性和 iOS 开发规范

### 2. 架构重构建议
- **分步提交**: 每个重构步骤单独提交，便于回滚和追踪
- **文档同步**: 重构完成后及时更新架构文档
- **测试验证**: 每次重构后进行功能验证
- **评分记录**: 记录重构前后的架构评分

### 3. 团队协作建议
- **同步频率**: 每天开始工作前拉取最新代码
- **冲突解决**: 优先保证架构一致性
- **知识分享**: 通过提交信息分享技术决策
- **持续改进**: 定期回顾和优化 Git 工作流程

---

## � 项目路源径管理

### 标准化路径配置
```bash
# 设置项目路径环境变量
export LOMO_PROJECT_PATH="/Users/<USER>/Lomo"
export LOMO_GIT_DIR="/Volumes/LH 16T/GitRepos/Lomo.git"
export LOMO_SCRIPTS_PATH="$LOMO_PROJECT_PATH/Lomo/Scripts"

# 快速导航函数
lomo() {
    cd "$LOMO_PROJECT_PATH"
    echo "📍 已切换到 Lomo 项目工作目录"
    echo "💾 Git 仓库数据: $LOMO_GIT_DIR"
    echo "🌐 GitHub: https://github.com/LH151211/Lomo.git"
    git status
}

# 快速脚本执行
lomo-smart-commit() {
    cd "$LOMO_PROJECT_PATH" && "$LOMO_SCRIPTS_PATH/smart_git_commit.sh"
}

lomo-clean-cache() {
    cd "$LOMO_PROJECT_PATH" && "$LOMO_SCRIPTS_PATH/smart_xcode_cache_cleaner.sh"
}
```

### 路径验证脚本
```bash
#!/bin/bash
# 验证 Lomo 项目路径配置

EXPECTED_WORK_PATH="/Users/<USER>/Lomo"
EXPECTED_GIT_PATH="/Volumes/LH 16T/GitRepos/Lomo.git"
CURRENT_PATH=$(pwd)

if [ "$CURRENT_PATH" = "$EXPECTED_WORK_PATH" ]; then
    echo "✅ 当前在正确的 Lomo 项目工作目录"
    
    # 检查 Git 配置
    if [ -f ".git" ] && grep -q "$EXPECTED_GIT_PATH" ".git"; then
        echo "✅ Git 仓库数据正确指向: $EXPECTED_GIT_PATH"
    else
        echo "⚠️ Git 配置可能有问题"
    fi
    
    echo "🌐 GitHub: https://github.com/LH151211/Lomo.git"
else
    echo "⚠️ 当前路径: $CURRENT_PATH"
    echo "📍 期望工作路径: $EXPECTED_WORK_PATH"
    echo "💡 使用 'lomo' 命令快速切换到项目目录"
fi
```

## 🔗 相关资源

- [Git 官方文档](https://git-scm.com/doc)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [LoniceraLab 代码开发标准](.kiro/steering/lonicera-coding-standards.md)
- [MVVM-S 架构指南](.kiro/steering/mvvm-architecture-guide.md)
- [Lomo 项目 GitHub 仓库](https://github.com/LH151211/Lomo.git)

---

---

## 🚨 紧急恢复指南

### 如果本地项目被意外修改

**立即执行以下步骤**：

```bash
# 1. 停止所有操作
git status  # 检查当前状态

# 2. 如果有未提交的更改，先备份
cp -r /Users/<USER>/Lomo /Users/<USER>/Lomo_emergency_backup

# 3. 查看最近的安全提交
git log --oneline -10

# 4. 恢复到最近的安全状态
git checkout [safe-commit-hash]
# 或者
git reset --hard [safe-commit-hash]

# 5. 验证项目完整性
ls -la Lomo/
xcodebuild -project Lomo.xcodeproj -scheme Lomo build
```

### 本地项目权威性声明

**重要提醒**: 
- `/Users/<USER>/Lomo` 是项目的唯一权威来源
- 任何与本地项目冲突的远程状态都应该被忽略或覆盖
- 本地项目的完整性和稳定性是最高优先级
- 所有Git操作都必须服务于保护和增强本地项目

### 联系支持

如果遇到无法解决的Git问题：
1. 立即停止所有Git操作
2. 备份当前本地项目状态
3. 记录问题发生的具体步骤
4. 寻求专业Git支持，但始终坚持本地项目优先原则

---

**记住**: 良好的 Git 管理是 LoniceraLab 工作室项目成功的基础，但**保护本地项目完整性**是最高优先级！🛡️