# 🚨 AI架构重构严格约束指南

## 🎯 核心使命声明

**你的唯一任务是：将现有代码从一种架构模式转换为另一种架构模式，除此之外不得做任何修改。**

**简单理解**：就像搬家一样，只是把东西从一个房间搬到另一个房间，东西本身一点都不能变。

---

## 🔒 零容忍禁令（违反即停止）

### ❌ UI相关 - 绝对禁止触碰
```swift
// 这些值绝对不能改动
Color.blue → 必须保持 Color.blue
.font(.title) → 必须保持 .font(.title)  
.padding(16) → 必须保持 .padding(16)
.cornerRadius(8) → 必须保持 .cornerRadius(8)
.frame(width: 100) → 必须保持 .frame(width: 100)

// 动画参数绝对不能改动
withAnimation(.easeInOut(duration: 0.3)) → 完全保持不变
```

### ❌ 业务逻辑 - 绝对禁止修改
```swift
// 计算逻辑绝对不能改
let result = (value * 0.8 + offset) / 2.0
// 重构后必须保持完全相同的计算

// 条件判断绝对不能改
if exposure > 2.0 || exposure < -2.0 {
    // 条件必须保持完全相同
}

// 数据验证绝对不能改
guard password.count >= 8 else { return false }
// 验证规则必须保持完全相同
```

### ❌ 功能行为 - 绝对禁止改变
```swift
// 方法签名绝对不能改（对外接口）
func saveImage(_ image: UIImage) → 保持不变
func calculateExposure(value: Float) -> Float → 保持不变

// 返回值类型和内容绝对不能改
func getFilteredImage() -> UIImage? → 必须返回相同类型和内容
```

---

## ✅ 唯一允许的操作：代码搬运

### 1. 创建新的架构容器
```swift
// ✅ 允许：创建新的ViewModel
class FilterViewModel: ObservableObject {
    // 创建空壳，准备接收搬运的代码
}

// ✅ 允许：创建新的Service
class FilterService {
    // 创建空壳，准备接收搬运的代码
}
```

### 2. 原样搬运代码
```swift
// 原始代码（在View中）
private func calculateExposure(value: Float) -> Float {
    let adjusted = value * 0.8 + 0.2
    return max(-2.0, min(2.0, adjusted))
}

// ✅ 正确搬运（搬到ViewModel中）
class FilterViewModel: ObservableObject {
    // 完全相同的代码，一个字符都不改
    private func calculateExposure(value: Float) -> Float {
        let adjusted = value * 0.8 + 0.2
        return max(-2.0, min(2.0, adjusted))
    }
}
```

### 3. 更新调用关系
```swift
// 原始调用
let result = calculateExposure(value: sliderValue)

// ✅ 正确更新调用关系
let result = viewModel.calculateExposure(value: sliderValue)
// 只改变调用路径，不改变调用参数和返回值处理
```

### 4. 添加依赖注入
```swift
// ✅ 允许：添加依赖注入构造函数
init(filterService: FilterServiceProtocol) {
    self.filterService = filterService
}

// ✅ 允许：添加协议定义
protocol FilterServiceProtocol {
    func saveSettings(_ settings: FilterParameters) async throws
}
```

---

## 📋 重构操作检查表

### 重构前检查（强制执行）
- [ ] **明确当前功能**：完整测试并记录现有功能表现
- [ ] **识别UI常量**：记录所有颜色、字体、尺寸、动画参数
- [ ] **记录业务逻辑**：记录所有计算公式、验证规则、数据处理流程
- [ ] **确定搬运目标**：明确哪些代码要搬到哪个新类中

### 重构中检查（每一步都要检查）
- [ ] **代码原样搬运**：复制粘贴，不做任何修改
- [ ] **调用关系更新**：只修改调用路径，不修改调用参数
- [ ] **编译检查**：每次搬运后立即编译检查
- [ ] **功能验证**：每次搬运后立即验证功能

### 重构后验证（全面验证）
- [ ] **像素级UI对比**：UI截图与重构前完全一致
- [ ] **功能行为对比**：每个功能的表现与重构前完全一致
- [ ] **数据一致性**：数据存储和读取结果完全一致
- [ ] **性能表现**：响应速度与重构前基本一致

---

## 🚫 常见错误示例及纠正

### 错误1：改进代码逻辑
```swift
// ❌ 错误：AI想要"改进"代码
// 原始代码
var result = 0.0
for i in 0..<values.count {
    result += values[i] * weights[i]
}

// AI想改成这样（禁止！）
let result = zip(values, weights).map(*).reduce(0, +)

// ✅ 正确：原样搬运
var result = 0.0
for i in 0..<values.count {
    result += values[i] * weights[i]
}
```

### 错误2：优化UI代码
```swift
// ❌ 错误：AI想要"优化"UI
// 原始代码
VStack(spacing: 8) {
    Text("标题")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(Color(red: 0.2, green: 0.3, blue: 0.8))
    // ...
}

// AI想改成这样（禁止！）
VStack(spacing: 8) {
    Text("标题")
        .font(.headline)  // 改了字体
        .foregroundColor(.blue)  // 改了颜色
    // ...
}

// ✅ 正确：完全保持原样
VStack(spacing: 8) {
    Text("标题")
        .font(.system(size: 16, weight: .medium))
        .foregroundColor(Color(red: 0.2, green: 0.3, blue: 0.8))
    // ...
}
```

### 错误3：修改业务逻辑
```swift
// ❌ 错误：AI想要"修复"业务逻辑
// 原始代码（可能看起来不够严谨）
if user.age > 18 {
    allowAccess = true
}

// AI想改成这样（禁止！）
if user.age >= 18 {  // 改了条件
    allowAccess = true
}

// ✅ 正确：保持原有逻辑，即使它看起来不完美
if user.age > 18 {
    allowAccess = true
}
```

---

## 📐 精确重构模板

### 模板1：View到ViewModel的搬运
```swift
// 步骤1：创建ViewModel壳子
class FilterViewModel: ObservableObject {
    // 空白，等待接收代码
}

// 步骤2：原样搬运状态属性
// 从View中找到：@State private var exposure: Float = 0.0
// 搬运到ViewModel：@Published var exposure: Float = 0.0

// 步骤3：原样搬运方法
// 从View中原样复制所有业务方法

// 步骤4：更新View中的调用
// 原来：exposure
// 现在：viewModel.exposure
```

### 模板2：创建Service层
```swift
// 步骤1：定义协议（基于现有功能）
protocol FilterServiceProtocol {
    // 基于现有方法签名定义，不添加新功能
}

// 步骤2：创建Service实现
class FilterService: FilterServiceProtocol {
    // 原样搬运相关的数据操作代码
}

// 步骤3：注入到ViewModel
class FilterViewModel: ObservableObject {
    private let filterService: FilterServiceProtocol
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
    }
}
```

---

## 🔍 实时监控规则

### 编写代码时的自检问题
在写每一行代码前，问自己：

1. **"我正在改变任何视觉效果吗？"** → 如果是，立即停止
2. **"我正在改变任何计算逻辑吗？"** → 如果是，立即停止  
3. **"我正在添加新功能吗？"** → 如果是，立即停止
4. **"我只是在搬运现有代码吗？"** → 如果不是，立即停止

### 危险信号识别
遇到以下想法时，立即停止：
- "这里可以优化一下..."
- "这个颜色不太好看，改成..."
- "这个逻辑有问题，应该..."
- "这样写更简洁..."
- "顺便修复一下这个bug..."

### 安全信号确认
只有以下想法是安全的：
- "把这段代码原样搬到ViewModel中"
- "给这个方法添加依赖注入参数"
- "创建一个协议接口"
- "更新调用路径"

---

## ⚡ 紧急刹车机制

### 立即停止的情况
如果发现自己在做以下任何事情，立即停止并重新开始：

1. **修改任何数值**：颜色值、尺寸、动画时长等
2. **改变条件判断**：if条件、循环条件等
3. **修改计算公式**：任何数学运算
4. **添加新的功能点**：新的按钮、新的字段等
5. **删除现有功能**：任何working的代码
6. **重写实现方式**：即使功能相同也不行

### 恢复流程
1. **停止当前操作**
2. **回到重构前状态**
3. **重新阅读约束规则**
4. **制定纯搬运计划**
5. **严格按照模板执行**

---

## 📚 成功重构示例

### 示例：Filter模块重构

#### 重构前（View中的代码）
```swift
struct FilterView: View {
    @State private var exposure: Float = 0.0
    @State private var contrast: Float = 0.0
    
    private func updateFilter() {
        // 原有的更新逻辑
        let adjustedExposure = exposure * 0.8 + 0.1
        FilterManager.shared.setExposure(adjustedExposure)
    }
    
    var body: some View {
        VStack(spacing: 16) {
            Slider(value: $exposure, in: -2...2)
                .accentColor(Color(red: 0.2, green: 0.6, blue: 1.0))
            Button("应用") {
                updateFilter()
            }
            .foregroundColor(.white)
            .background(Color.blue)
        }
    }
}
```

#### 重构后（MVVM架构）
```swift
// ViewModel（搬运后的代码）
class FilterViewModel: ObservableObject {
    @Published var exposure: Float = 0.0  // 原样搬运
    @Published var contrast: Float = 0.0  // 原样搬运
    
    private let filterService: FilterServiceProtocol
    
    init(filterService: FilterServiceProtocol) {
        self.filterService = filterService
    }
    
    func updateFilter() {
        // 完全相同的更新逻辑，一个字符都不改
        let adjustedExposure = exposure * 0.8 + 0.1
        filterService.setExposure(adjustedExposure)  // 只改变调用路径
    }
}

// Service（搬运FilterManager的代码）
protocol FilterServiceProtocol {
    func setExposure(_ value: Float)
}

class FilterService: FilterServiceProtocol {
    func setExposure(_ value: Float) {
        // 原样搬运FilterManager.shared中的逻辑
    }
}

// View（UI保持完全不变）
struct FilterView: View {
    @StateObject private var viewModel: FilterViewModel
    
    init(filterService: FilterServiceProtocol) {
        self._viewModel = StateObject(wrappedValue: FilterViewModel(filterService: filterService))
    }
    
    var body: some View {
        VStack(spacing: 16) {  // 完全相同的spacing
            Slider(value: $viewModel.exposure, in: -2...2)  // 只改变绑定路径
                .accentColor(Color(red: 0.2, green: 0.6, blue: 1.0))  // 完全相同的颜色
            Button("应用") {  // 完全相同的文字
                viewModel.updateFilter()  // 只改变调用路径
            }
            .foregroundColor(.white)  // 完全相同的颜色
            .background(Color.blue)   // 完全相同的背景
        }
    }
}
```

**重构要点**：
- UI完全没有变化：spacing、颜色、字体都保持原样
- 业务逻辑完全没有变化：计算公式、参数范围都保持原样  
- 只是代码位置发生了变化：从View搬到了ViewModel和Service

---

## 🎯 最终检验标准

### 终极测试：盲测验证
重构完成后，找一个不知道你重构了什么的用户来测试应用：

1. **他们能发现任何UI变化吗？** → 必须回答"否"
2. **他们能发现任何功能变化吗？** → 必须回答"否"  
3. **应用的表现和之前一样吗？** → 必须回答"是"

如果任何一个答案不符合预期，重构失败，需要重新开始。

### 技术验证
- **代码编译**: 0个警告，0个错误
- **单元测试**: 如果原来有测试，必须100%通过
- **UI测试**: 如果原来有UI测试，必须100%通过
- **内存使用**: 与重构前基本一致
- **启动时间**: 与重构前基本一致

---

## 📄 总结

**重构 = 纯粹的代码搬运工作**

- ✅ **允许**：创建新的类、协议、方法
- ✅ **允许**：移动现有代码到新位置
- ✅ **允许**：更新调用关系和依赖注入
- ❌ **禁止**：修改任何现有的实现细节
- ❌ **禁止**：改变任何用户可感知的行为
- ❌ **禁止**：添加任何新功能或优化

**记住**：你是一个搬运工，不是设计师，不是产品经理，不是优化专家。你的工作就是把代码从A搬到B，保持100%原样。

**如果有疑问**：宁可不改，也不要自作主张。