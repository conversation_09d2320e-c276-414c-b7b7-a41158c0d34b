# 重置按钮实现模式文档

## 概述

在Lomo应用中，重置按钮需要同时处理三个层面的重置：UI重置、数据重置和效果重置。本文档总结了曲线、色调分离、HSL三个功能模块的重置按钮实现模式，为后续功能开发提供参考。

## 重置按钮的三个层面

### 1. UI重置
- **目标**：将UI控件（滑块、色环控制点等）恢复到默认位置
- **实现**：通过SwiftUI的`@Published`属性或状态绑定自动更新

### 2. 数据重置
- **目标**：将FilterParameters中的相关参数重置为默认值
- **实现**：直接修改参数值或调用专门的重置方法

### 3. 效果重置
- **目标**：Metal渲染器重新渲染图像，移除视觉效果
- **实现**：通过FilterStateManager的参数更新机制触发渲染器更新

## 三个模块的实现模式

### 1. 曲线 (CurveManager)

#### 重置方法
```swift
func resetCurrentChannel() {
    // UI重置：重置控制点位置
    DispatchQueue.main.async { [weak self] in
        withAnimation(.easeInOut(duration: 0.3)) {
            self?.curvePoints = self?.getDefaultPoints() ?? []
        }
    }
    
    // 数据重置：重置曲线LUT
    filterStateManager?.batchUpdateParameters {
        // 重置当前通道的曲线数据
        switch currentChannel {
        case .rgb:
            filterStateManager?.currentParameters.rgbCurveLUT = CurveProcessor.createLinearLUT()
        case .red:
            filterStateManager?.currentParameters.redCurveLUT = CurveProcessor.createLinearLUT()
        // ... 其他通道
        }
    }
}
```

#### 关键特点
- 使用`withAnimation`提供平滑的UI过渡
- 通过`batchUpdateParameters`确保Metal渲染器更新
- 分通道独立重置

### 2. 色调分离 (SplitToningManager)

#### 重置方法
```swift
func resetAllTones() {
    guard let filterStateManager = filterStateManager else { return }

    print("🔄 [SplitToningManager] 重置所有色调分离参数")

    // UI重置：重置色环控制点位置
    DispatchQueue.main.async { [weak self] in
        withAnimation(.easeInOut(duration: 0.3)) {
            self?.toneHueOffset = .zero
        }
    }

    // 数据重置：批量更新所有参数
    filterStateManager.batchUpdateParameters {
        filterStateManager.currentParameters.highlightHue = 0.0
        filterStateManager.currentParameters.highlightSaturation = 0.0
        filterStateManager.currentParameters.shadowHue = 0.0
        filterStateManager.currentParameters.shadowSaturation = 0.0
        filterStateManager.currentParameters.splitToningBalance = 0.0
    }

    print("🔄 [SplitToningManager] 所有参数重置完成")
}
```

#### 关键特点
- UI和数据重置分离，避免SwiftUI更新冲突
- 使用`batchUpdateParameters`批量更新多个参数
- 包含详细的调试日志

### 3. HSL选择性调整 (HSLManager)

#### 重置方法
```swift
// 重置当前颜色
func resetCurrentColor() {
    guard let filterStateManager = filterStateManager else { return }
    
    print("🔄 [HSLManager] 重置当前颜色: \(colorNames[selectedColorIndex])")
    
    // 数据重置：批量更新当前颜色的HSL参数
    filterStateManager.batchUpdateParameters {
        filterStateManager.updateParameter(\.hue, value: 0.0)
        filterStateManager.updateParameter(\.hslSaturation, value: 0.0)
        filterStateManager.updateParameter(\.hslLuminance, value: 0.0)
    }
    
    print("✅ [HSLManager] \(colorNames[selectedColorIndex])参数已重置")
}

// 重置所有颜色
func resetAllColors() {
    guard let filterStateManager = filterStateManager else { return }

    print("🔄 [HSLManager] 重置所有颜色参数")

    // 数据重置：重置所有8个颜色的HSL参数
    filterStateManager.batchUpdateParameters {
        filterStateManager.currentParameters.resetAllHSLColors()
    }

    print("✅ [HSLManager] 所有颜色参数已重置")
}
```

#### 关键特点
- 支持单个颜色和所有颜色的重置
- UI重置通过`@Published`属性自动处理
- 使用专门的`resetAllHSLColors()`方法重置复杂的多颜色数据结构

## 核心机制：batchUpdateParameters

### 作用
`FilterStateManager.batchUpdateParameters`是确保效果重置的关键机制：

```swift
func batchUpdateParameters(_ updates: () -> Void) {
    updates()                                    // 执行参数更新
    hasActiveFilter = true                       // 标记为有活跃滤镜
    updateRenderersWithCurrentParameters()       // 更新Metal渲染器
}
```

### 调用链
```
batchUpdateParameters
    ↓
updateRenderersWithCurrentParameters
    ↓
metalRenderer.updateParameters(currentParameters)
    ↓
renderCurrentEffect()
    ↓
Metal着色器重新渲染图像
```

## 最佳实践

### 1. 统一的重置模式
```swift
func resetFeature() {
    guard let filterStateManager = filterStateManager else { return }
    
    // 1. UI重置（如果需要）
    DispatchQueue.main.async { [weak self] in
        withAnimation(.easeInOut(duration: 0.3)) {
            // 重置UI控件状态
        }
    }
    
    // 2. 数据重置 + 效果重置
    filterStateManager.batchUpdateParameters {
        // 重置参数值
    }
}
```

### 2. 避免的错误模式
```swift
// ❌ 错误：直接修改参数，不触发渲染器更新
func wrongReset() {
    filterStateManager.currentParameters.someParameter = 0.0
    filterStateManager.objectWillChange.send()  // 这样不够
}

// ❌ 错误：在UI更新中修改@Published属性
DispatchQueue.main.async {
    self.somePublishedProperty = newValue  // 可能导致SwiftUI警告
    filterStateManager.updateParameter(...)  // 在UI更新中调用
}
```

### 3. 推荐的模式
```swift
// ✅ 正确：使用batchUpdateParameters确保完整的更新链
func correctReset() {
    filterStateManager.batchUpdateParameters {
        // 所有参数重置
    }
}
```

## 调试技巧

### 1. 添加日志
```swift
print("🔄 [FeatureManager] 开始重置")
// 重置逻辑
print("✅ [FeatureManager] 重置完成")
```

### 2. 验证重置效果
```swift
// 重置后验证参数值
let hasEffect = checkIfParametersHaveEffect()
print("🎨 [FeatureManager] 重置后是否还有效果: \(hasEffect)")
```

### 3. 检查Metal渲染器状态
```swift
// 在MetalFilterRenderer中添加HSL参数检查
let hasHSLEffect = metalParams.hue != 0.0 || metalParams.hslSaturation != 0.0 || metalParams.hslLuminance != 0.0
print("🎨 [Metal渲染器] - 是否有HSL效果: \(hasHSLEffect)")
```

## 总结

重置按钮的正确实现需要：

1. **UI层面**：通过SwiftUI的响应式机制自动更新UI控件
2. **数据层面**：重置FilterParameters中的相关参数
3. **效果层面**：通过`batchUpdateParameters`触发Metal渲染器更新

关键是使用`FilterStateManager.batchUpdateParameters`方法，它确保了从参数更新到Metal渲染器重新渲染的完整数据流，避免了UI重置但效果不重置的问题。

## 应用到其他功能

当为其他功能（如锐化、清晰度、渐晕等）实现重置按钮时，请遵循以下模式：

1. 创建对应的Manager类（如果还没有）
2. 实现重置方法，使用`batchUpdateParameters`
3. 在UI层调用Manager的重置方法
4. 添加适当的调试日志
5. 测试确保UI、数据、效果三个层面都正确重置

这个模式已经在曲线、色调分离、HSL三个功能中验证有效，可以安全地应用到其他功能模块。
