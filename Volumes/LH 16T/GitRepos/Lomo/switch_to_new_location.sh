#!/bin/bash
# Copyright (c) 2025 LoniceraLab. All rights reserved.

# Lomo 项目路径切换脚本

echo "🚀 LoniceraLab Lomo 项目路径切换完成！"
echo ""
echo "📊 项目状态报告:"
echo "✅ 原路径: /Users/<USER>/Lomo"
echo "✅ 新路径: /Volumes/LH 16T/GitRepos/Lomo"
echo "✅ GitHub 仓库: https://github.com/LH151211/Lomo.git"
echo "✅ 最新提交已同步到 GitHub"
echo ""

echo "📋 下一步操作建议:"
echo "1. 在您的开发工具中更新项目路径到: /Volumes/LH 16T/GitRepos/Lomo"
echo "2. 更新任何脚本或配置中的路径引用"
echo "3. 可以安全删除原路径 /Users/<USER>/Lomo（建议先备份）"
echo ""

echo "🔧 快速验证命令:"
echo "git -C '/Volumes/LH 16T/GitRepos/Lomo' status"
echo "git -C '/Volumes/LH 16T/GitRepos/Lomo' log --oneline -5"
echo ""

echo "💡 添加到 shell 配置的别名:"
echo "alias lomo-cd='cd \"/Volumes/LH 16T/GitRepos/Lomo\"'"
echo "alias lomo-status='git -C \"/Volumes/LH 16T/GitRepos/Lomo\" status'"
echo ""

# 验证项目完整性
echo "🔍 验证项目完整性..."
PROJECT_PATH="/Volumes/LH 16T/GitRepos/Lomo"

if [ -f "$PROJECT_PATH/Lomo.xcodeproj/project.pbxproj" ]; then
    echo "✅ Xcode 项目文件存在"
else
    echo "❌ Xcode 项目文件缺失"
fi

if [ -d "$PROJECT_PATH/Lomo/Scripts" ]; then
    echo "✅ Scripts 目录存在"
    echo "   脚本数量: $(find "$PROJECT_PATH/Lomo/Scripts" -name "*.sh" | wc -l)"
else
    echo "❌ Scripts 目录缺失"
fi

if [ -d "$PROJECT_PATH/.kiro/steering" ]; then
    echo "✅ Kiro steering 配置存在"
    echo "   配置文件数量: $(find "$PROJECT_PATH/.kiro/steering" -name "*.md" | wc -l)"
else
    echo "❌ Kiro steering 配置缺失"
fi

echo ""
echo "🎉 项目迁移完成！现在可以在新路径继续开发 Lomo 项目。"