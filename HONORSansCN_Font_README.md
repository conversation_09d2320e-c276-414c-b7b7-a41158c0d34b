# HONORSansCN 字体集成说明

## 概述

HONORSansCN是一款由HONOR（荣耀）推出的专属字体，提供了9种不同的字重变体，为Lomo应用提供了更加丰富的水印文字展示选项。

本文档介绍如何在Lomo应用中配置和使用HONORSansCN字体。

## 字重变体

HONORSansCN提供以下9种字重变体：

1. Thin (极细)
2. ExtraLight (特细)
3. Light (细体)
4. Regular (常规)
5. Medium (中等)
6. SemiBold (半粗)
7. Bold (粗体)
8. ExtraBold (特粗)
9. Black (黑体)

## 配置步骤

### 1. 添加字体文件

1. 下载HONORSansCN字体文件（九种字重）
2. 将字体文件添加到项目中:
   - 在Xcode中，选择 `Project Navigator`
   - 右键点击 `Resources` 文件夹（或其他适合存放资源的文件夹）
   - 选择 `Add Files to "Lomo"...`
   - 选择下载的字体文件，确保选中 `Copy items if needed`
   - 点击 `Add`

### 2. 在Info.plist中注册字体

在项目的Info.plist文件中添加以下内容：

```xml
<key>UIAppFonts</key>
<array>
    <string>HONORSansCN-Thin.otf</string>
    <string>HONORSansCN-ExtraLight.otf</string>
    <string>HONORSansCN-Light.otf</string>
    <string>HONORSansCN-Regular.otf</string>
    <string>HONORSansCN-Medium.otf</string>
    <string>HONORSansCN-SemiBold.otf</string>
    <string>HONORSansCN-Bold.otf</string>
    <string>HONORSansCN-ExtraBold.otf</string>
    <string>HONORSansCN-Black.otf</string>
</array>
```

注：字体文件扩展名可能为.ttf或.otf，请根据实际文件类型调整。

### 3. 验证字体安装

在开发环境中，可以使用以下代码验证字体是否正确安装：

```swift
// 打印所有可用的字体名称
for familyName in UIFont.familyNames {
    print("Font Family: \(familyName)")
    for fontName in UIFont.fontNames(forFamilyName: familyName) {
        print("- \(fontName)")
    }
}
```

如果字体正确安装，应该会在输出中看到"HONORSansCN-Regular"等字体名称。

## 使用说明

HONORSansCN字体已在应用中集成，可通过以下方式使用：

1. 打开水印编辑界面
2. 在字体选择列表中选择"HONORSansCN"
3. 在字体粗细选项中可以选择9种不同的粗细变体

## 特点和优势

1. **丰富的字重选择**：9种字重变体提供了从极细到黑体的全面选择
2. **现代设计风格**：符合HONOR品牌调性的现代简约设计
3. **优秀的中文显示**：针对简体中文优化，提供清晰易读的文字显示
4. **辨识度高**：独特的字体设计，使水印更具辨识度

## 注意事项

1. 确保所有9种字体文件都已正确添加到项目中
2. 如果字体未显示，检查Info.plist配置和字体文件路径
3. 在不同的iOS版本上测试字体显示效果

## 兼容性

HONORSansCN字体适用于iOS 13.0及以上版本。 