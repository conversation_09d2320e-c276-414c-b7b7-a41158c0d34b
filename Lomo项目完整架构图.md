# 🏗️ Lomo项目完整架构图

## 📊 项目统计
- **总Swift文件数**: 241个
- **主要目录**: 15个
- **架构模式**: MVVM-S + 依赖注入

## 🗂️ 完整文件结构图

```
Lomo/
├── 📱 LomoApp.swift                                    # 应用入口点
│
├── 🏗️ DependencyInjection/ (10个文件)                  # 依赖注入容器
│   ├── AdjustDependencyContainer.swift                 # 调整模块依赖
│   ├── CropDependencyContainer.swift                   # 裁剪模块依赖
│   ├── EffectsDependencyContainer.swift                # 特效模块依赖
│   ├── FilterDependencyContainer.swift                 # 滤镜模块依赖
│   ├── GalleryDependencyContainer.swift                # 相册模块依赖
│   ├── GalleryFilterDependencyContainer.swift          # 相册滤镜模块依赖
│   ├── PaperDependencyContainer.swift                  # 纸张模块依赖
│   ├── SettingsDependencyContainer.swift               # 设置模块依赖
│   ├── SubscriptionDependencyContainer.swift           # 订阅模块依赖
│   └── WatermarkDependencyContainer.swift              # 水印模块依赖
│
├── 📋 Models/ (25个文件)                               # 数据模型层
│   ├── Camera/ (6个文件)                               # 相机相关模型
│   │   ├── CameraBasicSettings.swift                  # 相机基础设置
│   │   ├── CameraSettings.swift                       # 相机设置
│   │   ├── CameraStateModel.swift                     # 相机状态模型
│   │   ├── DeviceSpecification.swift                  # 设备规格
│   │   ├── DialMode.swift                             # 拨盘模式
│   │   └── LensConfiguration.swift                    # 镜头配置
│   ├── Edit/ (8个文件)                                # 编辑相关模型
│   │   ├── AdjustModel.swift                          # 调整模型
│   │   ├── CropModel.swift                            # 裁剪模型
│   │   ├── EditModels.swift                           # 编辑模型集合
│   │   ├── EffectModels.swift                         # 特效模型
│   │   ├── EffectsModel.swift                         # 特效模型
│   │   ├── FilterModel.swift                          # 滤镜模型
│   │   ├── FilterParameters.swift                     # 滤镜参数
│   │   ├── PaperModel.swift                           # 纸张模型
│   │   ├── RenderingMode.swift                        # 渲染模式
│   │   └── WatermarkSettings.swift                    # 水印设置
│   ├── Filter/ (2个文件)                              # 滤镜相关模型
│   │   ├── FilterCategory.swift                       # 滤镜分类
│   │   └── GalleryFilterModel.swift                   # 相册滤镜模型
│   ├── Gallery/ (1个文件)                             # 相册相关模型
│   │   └── GalleryModel.swift                         # 相册模型
│   ├── Presets/ (1个文件)                             # 预设相关模型
│   │   └── PresetType.swift                           # 预设类型
│   ├── Settings/ (1个文件)                            # 设置相关模型
│   │   └── SettingsModel.swift                        # 设置模型
│   ├── Subscription/ (1个文件)                        # 订阅相关模型
│   │   └── SubscriptionModel.swift                    # 订阅模型
│   ├── CurveChannel.swift                             # 曲线通道
│   ├── GrainModel.swift                               # 颗粒模型
│   ├── LightLeakModel.swift                           # 漏光模型
│   ├── ScratchModel.swift                             # 划痕模型
│   └── TabBarItem.swift                               # 标签栏项目
│
├── 🎭 ViewModels/ (21个文件)                           # 视图模型层
│   ├── Camera/ (8个文件)                              # 相机视图模型
│   │   ├── CameraAnimationViewModel.swift             # 相机动画视图模型
│   │   ├── CameraControlViewModel.swift               # 相机控制视图模型
│   │   ├── CameraSettingsViewModel.swift              # 相机设置视图模型
│   │   ├── CameraState.swift                          # 相机状态
│   │   ├── CameraViewModel.swift                      # 相机主视图模型
│   │   ├── CameraViewModel+Callbacks.swift            # 相机视图模型回调扩展
│   │   ├── CameraViewModel+Effects.swift              # 相机视图模型特效扩展
│   │   └── UIStateManager.swift                       # UI状态管理器
│   ├── Edit/ (7个文件)                                # 编辑视图模型
│   │   ├── AdjustViewModel.swift                      # 调整视图模型
│   │   ├── CropViewModel.swift                        # 裁剪视图模型
│   │   ├── EditViewModel.swift                        # 编辑主视图模型
│   │   ├── EffectsViewModel.swift                     # 特效视图模型
│   │   ├── FilterViewModel.swift                      # 滤镜视图模型
│   │   ├── PaperViewModel.swift                       # 纸张视图模型
│   │   └── WatermarkViewModel.swift                   # 水印视图模型
│   ├── Gallery/ (1个文件)                             # 相册视图模型
│   │   └── GalleryViewModel.swift                     # 相册视图模型
│   ├── Settings/ (1个文件)                            # 设置视图模型
│   │   └── SettingsViewModel.swift                    # 设置视图模型
│   ├── Subscription/ (1个文件)                        # 订阅视图模型
│   │   └── SubscriptionViewModel.swift                # 订阅视图模型
│   ├── GalleryFilterViewModel.swift                   # 相册滤镜视图模型
│   └── SharedTabViewModel.swift                       # 共享标签视图模型
│
├── 🎨 Views/ (78个文件)                               # 视图层
│   ├── Camera/ (35个文件)                             # 相机视图
│   │   ├── Dial/ (22个文件)                           # 拨盘相关视图
│   │   │   ├── Configurations/ (21个文件)             # 拨盘配置
│   │   │   │   ├── ApertureDialConfiguration.swift   # 光圈拨盘配置
│   │   │   │   ├── ApertureDialUtils.swift           # 光圈拨盘工具
│   │   │   │   ├── ExposureDialConfiguration.swift   # 曝光拨盘配置
│   │   │   │   ├── ExposureDialUtils.swift           # 曝光拨盘工具
│   │   │   │   ├── FocusDialConfiguration.swift      # 对焦拨盘配置
│   │   │   │   ├── FocusDialUtils.swift              # 对焦拨盘工具
│   │   │   │   ├── ISODialConfiguration.swift        # ISO拨盘配置
│   │   │   │   ├── ISODialUtils.swift                # ISO拨盘工具
│   │   │   │   ├── ShutterDialConfiguration.swift    # 快门拨盘配置
│   │   │   │   ├── ShutterDialUtils.swift            # 快门拨盘工具
│   │   │   │   ├── TemperatureDialConfiguration.swift # 色温拨盘配置
│   │   │   │   ├── TemperatureDialUtils.swift        # 色温拨盘工具
│   │   │   │   ├── TintDialConfiguration.swift       # 色调拨盘配置
│   │   │   │   ├── TintDialUtils.swift               # 色调拨盘工具
│   │   │   │   ├── ZoomDialConfiguration.swift       # 变焦拨盘配置
│   │   │   │   └── ZoomUtils.swift                   # 变焦工具
│   │   │   └── DialView.swift                        # 拨盘主视图
│   │   ├── AspectRatioOptionsView.swift              # 宽高比选项视图
│   │   ├── CameraView.swift                          # 相机主视图
│   │   ├── CameraView+temp.swift                     # 相机视图临时扩展
│   │   ├── ColorSpaceOptionsView.swift               # 色彩空间选项视图
│   │   ├── FlipOptionsView.swift                     # 翻转选项视图
│   │   ├── FrameRateOptionsView.swift                # 帧率选项视图
│   │   ├── HEVCOptionsView.swift                     # HEVC选项视图
│   │   ├── HistogramView.swift                       # 直方图视图
│   │   ├── PhotoFormatOptionsView.swift              # 照片格式选项视图
│   │   ├── PhotoModeOptionsView.swift                # 照片模式选项视图
│   │   ├── PhotoRatioOptionsView.swift               # 照片比例选项视图
│   │   ├── ResolutionOptionsView.swift               # 分辨率选项视图
│   │   ├── StabilizationOptionsView.swift            # 防抖选项视图
│   │   ├── TimerOptionsView.swift                    # 定时器选项视图
│   │   └── TimestampView.swift                       # 时间戳视图
│   ├── Components/ (4个文件)                          # 通用组件
│   │   ├── AddButton.swift                           # 添加按钮
│   │   ├── CustomSlider.swift                        # 自定义滑块
│   │   ├── OptionButton.swift                        # 选项按钮
│   │   └── PreviewScrollView.swift                   # 预览滚动视图
│   ├── Edit/ (12个文件)                              # 编辑视图
│   │   ├── Components/ (8个文件)                     # 编辑组件
│   │   │   ├── CropView.swift                        # 裁剪视图
│   │   │   ├── EffectsView.swift                     # 特效视图
│   │   │   ├── PaperView.swift                       # 纸张视图
│   │   │   ├── WatermarkCategoryBarView.swift        # 水印分类栏视图
│   │   │   ├── WatermarkControlView.swift            # 水印控制视图
│   │   │   ├── WatermarkOptionsPanelView.swift       # 水印选项面板视图
│   │   │   └── WatermarkStyleGridView.swift          # 水印样式网格视图
│   │   ├── AdjustView.swift                          # 调整视图
│   │   ├── EditView.swift                            # 编辑主视图
│   │   ├── FilterView.swift                          # 滤镜视图
│   │   └── WatermarkView.swift                       # 水印视图
│   ├── Filter/ (1个文件)                             # 滤镜视图
│   │   └── GalleryFilterView.swift                   # 相册滤镜视图
│   ├── Gallery/ (1个文件)                            # 相册视图
│   │   └── GalleryView.swift                         # 相册主视图
│   ├── Main/ (2个文件)                               # 主视图
│   │   ├── AppContainerView.swift                    # 应用容器视图
│   │   └── ContentView.swift                         # 内容视图
│   ├── Settings/ (3个文件)                           # 设置视图
│   │   ├── PhotoRatioSettingsView.swift              # 照片比例设置视图
│   │   ├── SettingsView.swift                        # 设置主视图
│   │   └── VideoRatioSettingsView.swift              # 视频比例设置视图
│   ├── Shared/ (5个文件)                             # 共享视图
│   │   ├── BottomTabBar.swift                        # 底部标签栏
│   │   ├── GalleryViewFixedImport.swift              # 相册视图修复导入
│   │   ├── NavigationTopBar.swift                    # 顶部导航栏
│   │   ├── SharedTabView.swift                       # 共享标签视图
│   │   └── SubscriptionView.swift                    # 订阅视图
│   └── Subscription/ (1个文件)                       # 订阅视图
│       └── SubscriptionView.swift                    # 订阅主视图
│
├── ⚙️ Services/ (54个文件)                            # 服务层
│   ├── Edit/ (7个文件)                               # 编辑服务
│   │   ├── AdjustService.swift                       # 调整服务
│   │   ├── CropService.swift                         # 裁剪服务
│   │   ├── EffectsService.swift                      # 特效服务
│   │   ├── FilterService.swift                       # 滤镜服务
│   │   ├── PaperService.swift                        # 纸张服务
│   │   ├── SharedService.swift                       # 共享服务
│   │   └── WatermarkService.swift                    # 水印服务
│   ├── Filter/ (1个文件)                             # 滤镜服务
│   │   └── GalleryFilterService.swift                # 相册滤镜服务
│   ├── Gallery/ (1个文件)                            # 相册服务
│   │   └── GalleryService.swift                      # 相册服务
│   ├── Implementations/ (25个文件)                   # 服务实现
│   │   ├── Dial/ (3个文件)                           # 拨盘服务
│   │   │   ├── DialService.swift                     # 拨盘服务
│   │   │   ├── DialServiceManager.swift              # 拨盘服务管理器
│   │   │   └── DialServiceProtocol.swift             # 拨盘服务协议
│   │   ├── AudioService.swift                        # 音频服务
│   │   ├── CameraAnimationService.swift              # 相机动画服务
│   │   ├── CameraError.swift                         # 相机错误
│   │   ├── CameraLensManager.swift                   # 相机镜头管理器
│   │   ├── CameraService.swift                       # 相机服务
│   │   ├── CameraType.swift                          # 相机类型
│   │   ├── DeviceCapabilityManager.swift             # 设备能力管理器
│   │   ├── DeviceConfigurationManager.swift          # 设备配置管理器
│   │   ├── ExposureService.swift                     # 曝光服务
│   │   ├── ImageAnalysisService.swift                # 图像分析服务
│   │   ├── iOSImageService.swift                     # iOS图像服务
│   │   ├── NotificationService.swift                 # 通知服务
│   │   ├── PresetService.swift                       # 预设服务
│   │   ├── RecordingService.swift                    # 录制服务
│   │   ├── ScreenMetricsService.swift                # 屏幕指标服务
│   │   ├── TimerService.swift                        # 定时器服务
│   │   ├── UIControlService.swift                    # UI控制服务
│   │   ├── UIKitBridgeService.swift                  # UIKit桥接服务
│   │   ├── ViewControllerService.swift               # 视图控制器服务
│   │   └── ZoomService.swift                         # 变焦服务
│   ├── Protocols/ (15个文件)                         # 服务协议
│   │   ├── AudioServiceProtocol.swift                # 音频服务协议
│   │   ├── CameraPreviewServiceProtocol.swift        # 相机预览服务协议
│   │   ├── CameraServiceProtocol.swift               # 相机服务协议
│   │   ├── DialConfiguration.swift                   # 拨盘配置协议
│   │   ├── ExposureServiceProtocol.swift             # 曝光服务协议
│   │   ├── FilterServiceProtocol.swift               # 滤镜服务协议
│   │   ├── ImageServiceProtocol.swift                # 图像服务协议
│   │   ├── NotificationServiceProtocol.swift         # 通知服务协议
│   │   ├── PresetServiceProtocol.swift               # 预设服务协议
│   │   ├── RecordingServiceProtocol.swift            # 录制服务协议
│   │   ├── ScreenMetricsServiceProtocol.swift        # 屏幕指标服务协议
│   │   ├── SubscriptionServiceProtocol.swift         # 订阅服务协议
│   │   ├── UIControlServiceProtocol.swift            # UI控制服务协议
│   │   ├── UIKitBridgeServiceProtocol.swift          # UIKit桥接服务协议
│   │   ├── ViewControllerServiceProtocol.swift       # 视图控制器服务协议
│   │   └── ZoomServiceProtocol.swift                 # 变焦服务协议
│   ├── Settings/ (2个文件)                           # 设置服务
│   │   ├── SettingsService.swift                     # 设置服务
│   │   └── SettingsServiceProtocol.swift             # 设置服务协议
│   ├── Subscription/ (2个文件)                       # 订阅服务
│   │   ├── SubscriptionConfig.swift                  # 订阅配置
│   │   └── SubscriptionService.swift                 # 订阅服务
│   ├── AnimationService.swift                        # 动画服务
│   ├── CameraSettingsStorageService.swift            # 相机设置存储服务
│   ├── FormattingService.swift                       # 格式化服务
│   ├── GrainService.swift                            # 颗粒服务
│   ├── HapticService.swift                           # 触觉反馈服务
│   ├── LightLeakService.swift                        # 漏光服务
│   ├── PerformanceService.swift                      # 性能服务
│   ├── PuzzleImageProvider.swift                     # 拼图图像提供者
│   ├── ScratchService.swift                          # 划痕服务
│   ├── ServiceFactory.swift                          # 服务工厂
│   ├── SessionManager.swift                          # 会话管理器
│   ├── UIStyleService.swift                          # UI样式服务
│   └── UserDefaultsService.swift                     # 用户默认设置服务
│
├── 🎮 Managers/ (29个文件)                            # 管理器层
│   ├── Camera/ (1个文件)                             # 相机管理器
│   │   └── CameraBasicManager.swift                  # 相机基础管理器
│   └── Edit/ (28个文件)                              # 编辑管理器
│       ├── WatermarkStyles/ (25个文件)               # 水印样式
│       │   ├── CustomWatermarkStyle1.swift          # 自定义水印样式1
│       │   ├── CustomWatermarkStyle2.swift          # 自定义水印样式2
│       │   ├── ... (CustomWatermarkStyle3-25.swift) # 自定义水印样式3-25
│       ├── WatermarkStyleManager.swift               # 水印样式管理器
│       └── WatermarkStyles.swift                     # 水印样式集合
│
├── 🖥️ Rendering/ (8个文件)                           # 渲染层
│   ├── Edit/ (5个文件)                               # 编辑渲染
│   │   ├── HighPrecisionMetalRenderer.swift          # 高精度Metal渲染器
│   │   ├── MetalFilterEngine.swift                   # Metal滤镜引擎
│   │   ├── MetalFilterRenderer.swift                 # Metal滤镜渲染器
│   │   ├── MetalFilterView.swift                     # Metal滤镜视图
│   │   └── MetalLUTProcessor.swift                   # Metal LUT处理器
│   ├── Shared/ (4个文件)                             # 共享渲染
│   │   ├── CameraPreviewService.swift                # 相机预览服务
│   │   ├── ImageRenderingService.swift               # 图像渲染服务
│   │   ├── MetalSpecialEffectsEngine.swift           # Metal特效引擎
│   │   └── MockCameraPreviewService.swift            # 模拟相机预览服务
│   └── Tests/ (2个文件)                              # 渲染测试
│       ├── MetalLUTTest.swift                        # Metal LUT测试
│       └── MetalSpecialEffectsTest.swift             # Metal特效测试
│
├── 🔧 Utils/ (25个文件)                              # 工具类
│   ├── Camera/ (1个文件)                             # 相机工具
│   │   └── CameraEventHandler.swift                 # 相机事件处理器
│   ├── Constants/ (2个文件)                          # 常量定义
│   │   ├── CameraConstants.swift                     # 相机常量
│   │   └── WatermarkConstants.swift                  # 水印常量
│   ├── Enums/ (1个文件)                              # 枚举定义
│   │   └── ParameterType.swift                       # 参数类型
│   ├── Extensions/ (1个文件)                         # 扩展
│   │   └── AVFoundation+Extensions.swift             # AVFoundation扩展
│   ├── AnimationManager.swift                        # 动画管理器
│   ├── AnimationUtils.swift                          # 动画工具
│   ├── ButtonControlLogic.swift                      # 按钮控制逻辑
│   ├── CompileTestRunner.swift                       # 编译测试运行器
│   ├── CurveBoundaryManager.swift                    # 曲线边界管理器
│   ├── CurveControlPointManager.swift                # 曲线控制点管理器
│   ├── CurvePresets.swift                            # 曲线预设
│   ├── CurveProcessor.swift                          # 曲线处理器
│   ├── CurveValidationTests.swift                    # 曲线验证测试
│   ├── EnhancedCurveValidator.swift                  # 增强曲线验证器
│   ├── EnhancedMetalCurveRenderer.swift              # 增强Metal曲线渲染器
│   ├── FontLineThicknessUtils.swift                  # 字体线条粗细工具
│   ├── FontUtils.swift                               # 字体工具
│   ├── ImageColorSpaceAnalyzer.swift                 # 图像色彩空间分析器
│   ├── LayoutUtils.swift                             # 布局工具
│   ├── MaskUtils.swift                               # 遮罩工具
│   ├── MetalCurveValidator.swift                     # Metal曲线验证器
│   ├── MockImageGenerator.swift                      # 模拟图像生成器
│   ├── OrientationUtils.swift                        # 方向工具
│   ├── RunCurveValidation.swift                      # 运行曲线验证
│   ├── SimpleValidationRunner.swift                  # 简单验证运行器
│   ├── TestUtils.swift                               # 测试工具
│   └── TimerManager.swift                            # 定时器管理器
│
├── 🛠️ Utilities/ (5个文件)                           # 实用工具
│   ├── CubeLUTManager.swift                          # Cube LUT管理器
│   ├── CubeLUTParser.swift                           # Cube LUT解析器
│   ├── ImageFormatProcessor.swift                    # 图像格式处理器
│   ├── XMPFileManager.swift                          # XMP文件管理器
│   └── XMPParser.swift                               # XMP解析器
│
├── 🎨 Shaders/ (1个文件)                             # 着色器
│   └── ShaderLibrary.swift                          # 着色器库
│
├── 📝 Examples/ (2个文件)                            # 示例代码
│   ├── ProcessUserXMP.swift                         # 处理用户XMP
│   └── XMPPresetExample.swift                       # XMP预设示例
│
└── 🧪 Tests/ (1个文件)                               # 测试文件
    └── LabColorSpaceTest.swift                       # Lab色彩空间测试
```

## 🔗 模块依赖关系图

### 核心依赖流向
```
LomoApp.swift
    ↓
AppContainerView.swift
    ↓
ContentView.swift
    ↓
SharedTabView.swift
    ├── CameraView.swift ← CameraViewModel ← CameraService
    ├── GalleryView.swift ← GalleryViewModel ← GalleryService
    ├── GalleryFilterView.swift ← GalleryFilterViewModel ← GalleryFilterService (滤镜展示)
    ├── EditView.swift ← EditViewModel ← FilterService/AdjustService/etc. (滤镜应用)
    └── SettingsView.swift ← SettingsViewModel ← SettingsService
```

### 🎨 滤镜系统架构详解

**设计理念**: 滤镜功能采用**展示层**和**应用层**分离的架构

```
滤镜系统整体架构:

📱 GalleryFilter模块 (滤镜展示层)
├── 职责: 为用户展示可用滤镜，提供选择界面
├── 功能: 滤镜预览、分类浏览、收藏管理、选择交互
├── 文件: GalleryFilterView.swift, GalleryFilterViewModel.swift, GalleryFilterService.swift
└── 不负责: 实际滤镜处理和图像渲染

✏️ Edit模块中的Filter (滤镜应用层)
├── 职责: 实际应用滤镜到图片，提供参数调整
├── 功能: 滤镜渲染、参数调整、实时预览、图像处理
├── 文件: FilterView.swift, FilterViewModel.swift, FilterService.swift
└── 不负责: 滤镜的展示和选择界面

🔄 共享的滤镜核心系统
├── FilterParameters.swift        # 滤镜参数定义
├── 滤镜预设数据                  # 预设配置
├── MetalFilterEngine.swift      # Metal渲染引擎
├── MetalLUTProcessor.swift      # LUT处理器
├── 滤镜算法库                    # 核心算法
└── 滤镜资源文件                  # LUT文件等

用户使用流程:
用户浏览相册 → GalleryFilter选择滤镜 → Edit模块应用滤镜 → 调整参数 → 保存图片
      ↓                    ↓                    ↓
   展示滤镜列表          传递选择结果        实际处理图像
      ↓                    ↓                    ↓
   共享底层滤镜数据 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

### 依赖注入容器关系
```
各模块DependencyContainer
    ↓
提供对应的Service实例
    ↓
注入到对应的ViewModel
    ↓
ViewModel被View使用
```

## 📊 关键文件使用分析

### 🔥 高频使用文件 (被多个模块引用)
1. **SharedService.swift** - 被Edit模块所有组件使用
2. **NavigationTopBar.swift** - 被多个View使用
3. **FilterParameters.swift** - 被滤镜相关组件使用
4. **CameraViewModel.swift** - 相机功能核心
5. **EditViewModel.swift** - 编辑功能核心

### 🎯 模块入口文件
1. **Camera模块**: `CameraView.swift` + `CameraViewModel.swift`
2. **Gallery模块**: `GalleryView.swift` + `GalleryViewModel.swift`
3. **GalleryFilter模块(滤镜展示)**: `GalleryFilterView.swift` + `GalleryFilterViewModel.swift`
4. **Edit模块(包含滤镜应用)**: `EditView.swift` + `EditViewModel.swift`
5. **Settings模块**: `SettingsView.swift` + `SettingsViewModel.swift`
6. **Subscription模块**: `SubscriptionView.swift` + `SubscriptionViewModel.swift`

### 🔧 工具和服务文件
1. **Metal渲染**: `MetalFilterRenderer.swift`, `MetalFilterEngine.swift`
2. **图像处理**: `ImageFormatProcessor.swift`, `CubeLUTManager.swift`
3. **UI工具**: `LayoutUtils.swift`, `FontUtils.swift`
4. **动画**: `AnimationManager.swift`, `AnimationService.swift`

## 🚨 架构问题识别

### 1. 文件组织问题
- **水印样式文件过多**: 25个CustomWatermarkStyle文件，应该合并
- **拨盘配置分散**: 21个拨盘相关文件，结构复杂
- **常量定义不统一**: 只有Camera和Watermark常量，缺少UI常量

### 2. 依赖关系问题
- **循环依赖风险**: 某些Service之间可能存在循环引用
- **单例使用**: 部分代码仍使用.shared模式
- **依赖注入不完整**: 部分组件未使用依赖注入

### 3. 架构设计特点
- **滤镜系统分层设计**: GalleryFilter(展示层) + Edit Filter(应用层) 共享底层滤镜系统
- **工具类分散**: Utils和Utilities目录功能重叠，需要整理
- **服务实现重复**: 某些服务功能相似，可以优化

## 🎯 优化建议

### 1. 文件结构优化
- 合并水印样式文件为配置驱动
- 简化拨盘配置结构
- 创建统一的常量管理

### 2. 架构优化
- 完善依赖注入体系
- 消除循环依赖
- 统一服务接口设计

### 3. 代码优化
- 合并重复功能模块
- 统一工具类组织
- 建立清晰的模块边界

---

**总结**: Lomo项目包含241个Swift文件，采用MVVM-S架构，但存在文件组织混乱、依赖关系复杂、代码重复等问题。需要系统性的重构来提升代码质量和可维护性。