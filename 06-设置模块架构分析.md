# 📊 设置模块 MVVM-S 架构分析

<!-- Copyright (c) 2025 LoniceraLab. All rights reserved. -->

## 🎯 架构概述

设置模块已成功实现完整的 MVVM-S 架构，符合 LoniceraLab 的高效、精简、中文友好的开发标准。

## 📈 架构数据流程图

```mermaid
graph TB
    %% 用户交互层
    User[👤 用户操作] --> SettingsView[📱 SettingsView]
    
    %% View层
    SettingsView --> |用户点击/输入| SettingsViewModel[🧠 SettingsViewModel]
    SettingsViewModel --> |@Published状态更新| SettingsView
    
    %% ViewModel层内部状态
    subgraph "ViewModel状态管理"
        SettingsViewModel --> |22个@Published属性| ViewState[📊 视图状态]
        ViewState --> |相机设置| CameraSettings[📷 相机设置状态]
        ViewState --> |偏好设置| PreferenceSettings[⚙️ 偏好设置状态]
        ViewState --> |应用信息| AppInfo[ℹ️ 应用信息状态]
        ViewState --> |用户状态| UserStatus[👤 用户状态]
        ViewState --> |UI状态| UIState[🎨 UI状态]
    end
    
    %% Service层
    SettingsViewModel --> |依赖注入| SettingsService[🔧 SettingsService]
    SettingsService --> |SwiftData操作| SwiftDataContext[💾 ModelContext]
    
    %% Model层
    SwiftDataContext --> |CRUD操作| AppSettings[📋 AppSettings Model]
    AppSettings --> |数据持久化| SwiftDataStorage[(🗄️ SwiftData存储)]
    
    %% 依赖注入容器
    SettingsDependencyContainer[🏭 SettingsDependencyContainer] --> |创建实例| SettingsService
    SettingsDependencyContainer --> |工厂方法| SettingsViewModel
    SettingsDependencyContainer --> |共享容器| SharedService[🔄 SharedService]
    
    %% 跨模块依赖
    SettingsViewModel --> |订阅服务依赖| SubscriptionService[💰 SubscriptionService]
    SubscriptionDependencyContainer[🏭 SubscriptionDependencyContainer] --> |提供服务| SubscriptionService
    
    %% 通知系统
    SubscriptionService --> |Pro状态变化| NotificationCenter[📢 NotificationCenter]
    NotificationCenter --> |状态同步| SettingsViewModel
    
    %% 数据流向标注
    classDef userLayer fill:#e1f5fe
    classDef viewLayer fill:#f3e5f5
    classDef viewModelLayer fill:#e8f5e8
    classDef serviceLayer fill:#fff3e0
    classDef modelLayer fill:#fce4ec
    classDef storageLayer fill:#f1f8e9
    classDef containerLayer fill:#e0f2f1
    
    class User userLayer
    class SettingsView viewLayer
    class SettingsViewModel,ViewState,CameraSettings,PreferenceSettings,AppInfo,UserStatus,UIState viewModelLayer
    class SettingsService,SubscriptionService,SharedService serviceLayer
    class AppSettings modelLayer
    class SwiftDataContext,SwiftDataStorage storageLayer
    class SettingsDependencyContainer,SubscriptionDependencyContainer containerLayer
```

## 🏗️ 架构层次说明

### 1. 用户交互层 (User Layer)
- **用户操作**: 点击、输入、滑动等交互行为
- **颜色标识**: 浅蓝色 (#e1f5fe)

### 2. View 层 (View Layer)
- **SettingsView**: 设置界面的主视图
- **职责**: 纯 UI 展示，响应用户交互
- **颜色标识**: 浅紫色 (#f3e5f5)

### 3. ViewModel 层 (ViewModel Layer)
- **SettingsViewModel**: 设置功能的视图模型
- **状态管理**: 22个 @Published 属性
- **分类状态**: 相机设置、偏好设置、应用信息、用户状态、UI状态
- **颜色标识**: 浅绿色 (#e8f5e8)

### 4. Service 层 (Service Layer)
- **SettingsService**: 设置数据服务
- **SubscriptionService**: 订阅服务
- **SharedService**: 共享服务
- **职责**: 数据操作、业务逻辑处理
- **颜色标识**: 浅橙色 (#fff3e0)

### 5. Model 层 (Model Layer)
- **AppSettings**: SwiftData 模型
- **职责**: 数据结构定义
- **颜色标识**: 浅粉色 (#fce4ec)

### 6. 存储层 (Storage Layer)
- **SwiftDataContext**: 数据上下文
- **SwiftDataStorage**: 持久化存储
- **颜色标识**: 浅绿色 (#f1f8e9)

### 7. 容器层 (Container Layer)
- **SettingsDependencyContainer**: 设置模块依赖注入容器
- **SubscriptionDependencyContainer**: 订阅模块依赖注入容器
- **职责**: 依赖管理、实例创建
- **颜色标识**: 浅青色 (#e0f2f1)

## 📊 架构评分

| 评分项目 | 权重 | 得分 | 说明 |
|---------|------|------|------|
| **状态管理** | 25% | 25/25 | 集中式状态管理，使用 @Published |
| **依赖注入** | 25% | 23/25 | 主要依赖注入，SharedService 仍为单例 |
| **层次分离** | 20% | 20/20 | 严格分层，无越界 |
| **错误处理** | 15% | 13/15 | 基本错误处理，可进一步完善 |
| **性能优化** | 10% | 9/10 | 基本性能考虑，如懒加载 |
| **可测试性** | 5% | 5/5 | 完全可测试 |
| **总分** | 100% | **95/100** | **优秀** |

## 🔄 数据流程详解

### 数据加载流程
1. 用户打开设置页面
2. SettingsView 初始化，接收注入的 ViewModel
3. ViewModel 调用 `loadSavedSettings()`
4. SettingsService 从 SwiftData 加载设置
5. ViewModel 更新 @Published 属性
6. View 自动响应状态变化并更新 UI

### 数据保存流程
1. 用户修改设置
2. View 调用 ViewModel 的方法
3. ViewModel 调用 `updateSetting()` 方法
4. SettingsService 更新 SwiftData 中的数据
5. 数据持久化到磁盘

### 跨模块通信
1. 订阅状态变化通过 NotificationCenter 传递
2. ViewModel 订阅 "ProUserStatusChanged" 通知
3. 收到通知后更新 `isProUser` 状态

## ✅ 版权合规性

所有设置模块文件已添加 LoniceraLab 标准版权声明：
- ✅ `SettingsModel.swift`
- ✅ `SettingsService.swift`
- ✅ `SettingsViewModel.swift`
- ✅ `SettingsView.swift`
- ✅ `SettingsDependencyContainer.swift`

## 🎯 结论

设置模块是 LoniceraLab 项目中 MVVM-S 架构的优秀实现，达到了 95/100 的优秀评分。架构清晰、依赖注入完善、状态管理集中，完全符合项目的高效、精简、中文友好的开发标准。
