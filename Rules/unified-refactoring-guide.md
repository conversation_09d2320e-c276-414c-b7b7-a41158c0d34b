# 🔗 Lomo项目统一重构执行指南

## 📋 指南体系说明

本项目重构由两个核心指南协同工作：

- **📖 [增强版MVVM-S架构指南]**：定义"做什么" - 架构标准、模式、最佳实践
- **🚨 [AI重构严格约束指南]**：定义"怎么做" - 执行约束、操作规范、质量控制

## 🎯 统一执行流程

### 阶段1：重构规划（使用架构指南）

```
输入：现有模块代码
参考：增强版MVVM-S架构指南
输出：重构计划文档
```

#### 1.1 架构现状评估
- 使用架构指南的评分系统评估当前模块
- 识别需要重构的具体问题（单例依赖、状态管理等）
- 确定目标架构模式（ViewModel、Service、依赖注入等）

#### 1.2 制定重构计划
```markdown
## 模块重构计划：[ModuleName]

### 当前架构评分：XX/100
### 目标架构评分：100/100

### 重构步骤：
1. 创建 [ModuleName]ViewModel
2. 创建 [ModuleName]Service 和协议
3. 实施依赖注入
4. 更新 [ModuleName]View

### 代码搬运清单：
- [ ] 状态属性：从 @State 搬运到 @Published
- [ ] 业务方法：从 View 搬运到 ViewModel
- [ ] 数据操作：从 Manager.shared 搬运到 Service
```

### 阶段2：严格执行（使用约束指南）

```
输入：重构计划文档
参考：AI重构严格约束指南
输出：重构后的代码
```

#### 2.1 执行前检查
- [ ] 阅读完整的约束指南
- [ ] 明确当前功能表现（截图、手动验证记录）
- [ ] 确认所有UI常量和业务逻辑参数
- [ ] 建立git备份点

#### 2.2 逐步执行（每步都要验证）
```swift
// 步骤1：创建架构壳子（参考架构指南模板）
// 步骤2：原样搬运代码（严格按照约束指南）
// 步骤3：更新调用关系（仅修改调用路径）
// 步骤4：验证功能完整性（约束指南验证标准）
```

### 阶段3：质量验证（两个指南联合验证）

```
输入：重构后代码
参考：两个指南的验证标准
输出：质量验证报告
```

## 🔄 统一检查清单

### 架构合规性检查（来自架构指南）
- [ ] **状态管理**：所有状态在ViewModel中，使用@Published
- [ ] **依赖注入**：无业务逻辑单例，构造函数注入
- [ ] **层次分离**：View-ViewModel-Service-Model清晰分层
- [ ] **错误处理**：统一的异步错误处理模式
- [ ] **协议设计**：Service层有对应协议定义

### 功能完整性检查（来自约束指南）
- [ ] **UI一致性**：所有颜色、字体、布局、动画参数完全不变
- [ ] **业务逻辑一致性**：所有计算、验证、处理逻辑完全不变
- [ ] **用户体验一致性**：所有交互流程和反馈完全不变
- [ ] **性能一致性**：响应速度和内存使用基本一致
- [ ] **数据一致性**：存储格式和读写逻辑完全不变

### 代码质量检查（统一标准）
- [ ] **编译检查**：0警告，0错误
- [ ] **架构评分**：达到目标分数（通常100分）
- [ ] **功能验证**：所有现有功能手动验证100%通过
- [ ] **命名规范**：遵循项目命名约定
- [ ] **代码组织**：MARK注释清晰，方法顺序合理

## 📚 统一术语表

### 核心概念统一定义

| 术语 | 定义 | 来源指南 |
|------|------|----------|
| **架构重构** | 改变代码组织结构，不改变功能行为 | 两个指南 |
| **代码搬运** | 将代码从一处移动到另一处，保持完全不变 | 约束指南 |
| **依赖注入** | 通过构造函数传入依赖，消除单例使用 | 架构指南 |
| **业务逻辑单例** | 包含业务逻辑的单例模式（禁止） | 架构指南 |
| **状态管理** | 使用@Published在ViewModel中集中管理状态 | 架构指南 |
| **层次分离** | View-ViewModel-Service-Model各司其职 | 架构指南 |
| **功能完整性** | 重构前后功能表现100%一致 | 约束指南 |
| **UI一致性** | 重构前后视觉效果100%一致 | 约束指南 |

### 禁止行为统一清单

| 禁止行为 | 说明 | 检查方式 |
|----------|------|----------|
| **修改UI样式** | 任何颜色、字体、布局、动画参数 | 像素级对比 |
| **修改业务逻辑** | 任何计算、验证、处理逻辑 | 手动功能验证对比 |
| **添加新功能** | 任何新的功能特性 | 功能清单对比 |
| **性能优化** | 除非明确要求 | 性能指标对比 |
| **使用业务单例** | Manager.shared等模式 | 代码静态分析 |

## 🛠️ 统一工具和模板

### 重构执行模板

```swift
// ==========================================
// 模块重构模板：严格按照此模板执行
// ==========================================

// 步骤1：创建ViewModel（架构指南模板）
@MainActor
class [ModuleName]ViewModel: ObservableObject {
    // MARK: - 依赖注入（必需）
    private let service: [ModuleName]ServiceProtocol
    
    // MARK: - 状态管理（@Published）
    @Published var state: ViewState<[DataType]> = .idle
    // 从原View中原样搬运的状态属性...
    
    // MARK: - 初始化（强制依赖注入）
    init(service: [ModuleName]ServiceProtocol) {
        self.service = service
    }
    
    // MARK: - 业务方法（从原View中原样搬运）
    // 保持方法体完全不变，只修改调用路径
}

// 步骤2：创建Service（架构指南模板）
protocol [ModuleName]ServiceProtocol {
    // 基于原有功能定义接口，不添加新功能
}

class [ModuleName]Service: [ModuleName]ServiceProtocol {
    // 从原Manager中原样搬运的方法...
    // 保持实现逻辑完全不变
}

// 步骤3：更新View（约束指南要求）
struct [ModuleName]View: View {
    @StateObject private var viewModel: [ModuleName]ViewModel
    
    init(service: [ModuleName]ServiceProtocol) {
        self._viewModel = StateObject(wrappedValue: 
            [ModuleName]ViewModel(service: service))
    }
    
    var body: some View {
        // UI代码保持完全不变
        // 只修改状态绑定路径：从self到viewModel
        // 只修改方法调用路径：从self到viewModel
    }
}
```

### 验证检查模板

```swift
// ==========================================
// 重构验证检查清单
// ==========================================

// ✅ 编译检查
// - 构建成功，无警告无错误

// ✅ 功能对比检查（每个功能点都要验证）
// - 按钮点击响应 ✓
// - 滑块拖动效果 ✓  
// - 数据保存加载 ✓
// - 错误处理显示 ✓
// - 页面跳转逻辑 ✓

// ✅ UI对比检查（每个视觉元素都要验证）
// - 颜色完全一致 ✓
// - 字体大小一致 ✓
// - 布局间距一致 ✓
// - 动画效果一致 ✓
// - 交互反馈一致 ✓

// ✅ 架构评分检查
// - 当前评分：___/100
// - 目标评分：100/100
// - 是否达标：___
```

## 🚨 统一应急处理流程

### 发现问题时的处理步骤

1. **立即停止**：不再进行任何修改
2. **问题定位**：确定是架构问题还是功能破坏问题
3. **回滚代码**：恢复到最近的可工作状态
4. **重新规划**：
   - 如果是架构问题 → 重读架构指南，调整重构计划
   - 如果是功能破坏 → 重读约束指南，重新执行
5. **重新执行**：严格按照指南重新开始

### 常见问题及解决方案

| 问题类型 | 现象 | 解决方案 | 参考指南 |
|----------|------|----------|----------|
| **编译错误** | 构建失败 | 检查调用路径更新是否正确 | 约束指南 |
| **功能异常** | 行为改变 | 检查是否修改了业务逻辑 | 约束指南 |
| **UI变化** | 视觉效果改变 | 检查是否修改了UI参数 | 约束指南 |
| **架构不合规** | 评分不达标 | 检查是否按架构模板实现 | 架构指南 |
| **性能下降** | 响应变慢 | 检查是否引入性能问题 | 两个指南 |

## 📈 统一进度追踪

### 模块重构进度表

```markdown
| 模块名称 | 当前评分 | 目标评分 | 重构状态 | 完成时间 | 验证状态 |
|----------|----------|----------|----------|----------|----------|
| Filter   | 75/100   | 100/100  | 进行中   | 待定     | 待验证   |
| Camera   | 60/100   | 100/100  | 待开始   | 待定     | 待验证   |
| Edit     | 45/100   | 100/100  | 待开始   | 待定     | 待验证   |
```

### 质量指标追踪

```markdown
## 项目整体架构质量
- 平均架构评分：___/100
- 重构完成模块：___/___
- 功能完整性：100%（强制要求）
- UI一致性：100%（强制要求）
```

## 🎯 总结

这个统一指南确保了：

1. **执行有序**：明确的阶段划分和执行流程
2. **标准统一**：术语、检查项、模板都保持一致  
3. **质量保证**：双重验证机制确保架构和功能都达标
4. **问题预防**：预设了常见问题的解决方案
5. **进度可控**：清晰的追踪和评估机制

**使用原则**：先读架构指南理解目标，再读约束指南掌握方法，最后用这个统一指南执行重构。

---

## 🚫 测试模块政策（统一声明）

### 📋 重要声明：当前阶段不编写测试代码

本统一重构指南明确规定：**当前项目阶段专注于架构重构，暂时不需要编写任何测试相关代码。**

#### ❌ 严格禁止的测试工作
- **不编写单元测试**：包括 XCTestCase、@testable import 等
- **不创建 Mock 对象**：包括 MockService、MockManager 等
- **不编写测试用例**：包括 func test*() 方法
- **不设置测试环境**：包括测试配置、测试数据等
- **不讨论测试策略**：包括测试覆盖率、测试框架等

#### ✅ 当前阶段质量保证
1. **编译验证**：确保 0 警告 0 错误
2. **手动功能验证**：通过实际操作验证功能
3. **架构评分**：使用架构质量评分系统
4. **代码审查**：人工审查架构合规性
5. **文档记录**：详细记录重构过程

#### 🎯 重构执行重点
- **MVVM-S 架构实现**：完整的架构层次分离
- **依赖注入体系**：消除业务逻辑单例模式
- **状态管理规范**：@Published 和 ObservableObject 正确使用
- **代码结构优化**：文件组织、命名规范、注释格式
- **SwiftUI 最佳实践**：现代 iOS 开发模式应用

#### 📝 执行指导
在重构过程中，如果遇到任何测试相关的建议或要求，请明确回复：
> "根据项目当前阶段的统一重构指南，我们专注于架构重构工作，暂时不编写测试代码。质量通过编译检查、手动验证和架构评分来保证。"

**重要提醒**：任何偏离此政策的测试相关工作都是不必要的，请严格按照重构指南执行。