# SourceHanSansSC 字体集成说明

## 概述

Source Han Sans SC (思源黑体)是一款开源的泛中日韩字体，提供了7种不同的字重变体，为Lomo应用提供了更多样化的水印文字展示选项。

本文档介绍如何在Lomo应用中配置和使用SourceHanSansSC字体。

## 字重变体

SourceHanSansSC提供以下7种字重变体：

1. ExtraLight (极细)
2. Light (细体)
3. Normal (标准)
4. Regular (常规)
5. Medium (中等)
6. Bold (粗体)
7. Heavy (极粗)

## 配置步骤

### 1. 添加字体文件

1. 下载SourceHanSansSC字体文件（七种字重）
2. 将字体文件添加到项目中:
   - 在Xcode中，选择 `Project Navigator`
   - 右键点击 `Resources` 文件夹（或其他适合存放资源的文件夹）
   - 选择 `Add Files to "Lomo"...`
   - 选择下载的字体文件，确保选中 `Copy items if needed`
   - 点击 `Add`

### 2. 在Info.plist中注册字体

在项目的Info.plist文件中添加以下内容：

```xml
<key>UIAppFonts</key>
<array>
    <string>SourceHanSansSC-ExtraLight.otf</string>
    <string>SourceHanSansSC-Light.otf</string>
    <string>SourceHanSansSC-Normal.otf</string>
    <string>SourceHanSansSC-Regular.otf</string>
    <string>SourceHanSansSC-Medium.otf</string>
    <string>SourceHanSansSC-Bold.otf</string>
    <string>SourceHanSansSC-Heavy.otf</string>
</array>
```

### 3. 验证字体是否正确加载

在应用启动时，可以添加以下代码验证字体是否已正确加载：

```swift
for family in UIFont.familyNames.sorted() {
    print("Family: \(family)")
    for name in UIFont.fontNames(forFamilyName: family).sorted() {
        print("   Font: \(name)")
    }
}
```

## 字体使用

Lomo应用已完成了SourceHanSansSC字体的集成，用户可以通过水印编辑界面选择并使用：

1. 在水印编辑界面选择任意水印样式
2. 点击"字体"选项
3. 在字体选择器中选择"SourceHanSansSC"
4. 在字体粗细选择器中选择需要的字重变体

## 注意事项

1. 字体文件较大，会增加应用的安装包大小
2. 若要减小安装包体积，可以考虑只包含部分字重变体（如Regular、Medium和Bold）
3. 确保字体文件名称与代码中引用的名称完全一致，包括大小写

## 授权和许可

Source Han Sans是Adobe与Google合作开发的开源字体，使用SIL开源字体许可证（OFL）。在商业应用中使用时，请确保遵循相关的授权规定。

## 故障排除

如果字体无法正确显示或应用，请检查：

1. 字体文件是否已正确添加到项目中
2. Info.plist中的字体名称是否与实际字体文件名一致
3. 确认所需的字体变体是否已正确加载（使用上述验证代码） 